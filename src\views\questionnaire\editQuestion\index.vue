<!-- 编辑题库 -->
<template>
  <container-card>
    <div class="main">
      <smDraggable ref="smDraggableRef" @back="back" @submitQuestion="submitQuestion"></smDraggable>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import smDraggable from '@/components/smDraggable/index.vue';
import { saveQuestion, getQuestionDetail } from '@/api/question';

// Define component name
defineOptions({
  name: 'EditQuestion'
});

// Props
const props = defineProps<{
  title: string;
  remark: string;
  id: string;
}>();

// Refs
const router = useRouter();
const smDraggableRef = ref();

/**
 * 获取问卷详情
 */
const getData = async () => {
  if (props.id !== 'undefined') {
    try {
      const res = await getQuestionDetail(props.id);
      if (res.code === 200) {
        smDraggableRef.value?.initFileLise(res.data.detailModelList);
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      ElMessage.error('获取问卷详情失败');
    }
  }
};

/**
 * 返回
 */
const back = () => {
  router.push('/questionnaire');
};

/**
 * 提交问卷
 * @param list 列表
 */
const submitQuestion = (list: any[]) => {
  if (list.length === 0) {
    ElMessage.error('请添加至少一个问题');
    return;
  }
  const detailModelList = JSON.parse(JSON.stringify(list));
  detailModelList.forEach((v: any) => {
    if (v.problemSelect) {
      v.problemSelect = arrayToObject(v.problemSelect);
    } else {
      v.problemSelect = {};
    }
  });

  const params = {
    title: props.title,
    remark: props.remark,
    detailModelList: detailModelList
  } as { title: string; remark: string; detailModelList: any[]; id?: string };

  if (props.id !== 'undefined') {
    params.id = props.id;
  }

  saveQuestion(params)
    .then((res) => {
      if (res.code === 200) {
        router.push('/questionnaire');
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((error) => {
      ElMessage.error('保存问卷失败');
    });
};

/**
 * 数组转对象
 * @param arr 数组
 * @returns 对象
 */
const arrayToObject = (arr: any[]): Record<string, any> => {
  const obj: Record<string, any> = {};
  for (let i = 0; i < arr.length; i++) {
    obj[i] = arr[i];
  }
  return obj;
};

/**
 * 挂载
 */
onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
</style>
