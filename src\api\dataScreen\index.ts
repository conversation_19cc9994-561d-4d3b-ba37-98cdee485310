import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ScreenData, ScreenQuery, FormulaParams } from '@/api/dataScreen/types';

/**
 * 获取大屏数据列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getScreenList(params: ScreenQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/design/pageList',
    method: 'get',
    params: params
  });
}

/**
 * 保存数据大屏
 * @param params 大屏数据
 * @returns {AxiosPromise}
 */
export function saveScreen(params: ScreenData): AxiosPromise<any> {
  return request({
    url: '/qjt/design/saveOrUpdate',
    method: 'post',
    data: params
  });
}

/**
 * 根据id获取大屏详情
 * @param id 大屏ID
 * @param mode 模式
 * @param viewCode 视图代码
 * @returns {AxiosPromise}
 */
export function getScreenById(id: string | number, mode: string, viewCode?: string): AxiosPromise<any> {
  return request({
    url: '/qjt/design/getById/' + id + '/' + mode + '/' + viewCode,
    method: 'get'
  });
}

/**
 * 根据表达式获取结果
 * @param params 表达式参数
 * @returns {AxiosPromise}
 */
export function getDataForFormula(params: FormulaParams): AxiosPromise<any> {
  return request({
    url: `/qjt/formula/calculate`,
    method: 'post',
    params: params
  });
}

/**
 * 删除数据大屏
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delScreen(params: ScreenQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/design/delete`,
    method: 'delete',
    params: params
  });
}
