<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" class="gisMap-main" id="layerMapRef">
    <div id="viewDiv" ref="gisMapRef" class="map"></div>
    <div class="right-handle-div" id="right-handle-div">
      <div class="min-handle-item" style="margin-bottom: 12px" @click="showTL = true" v-if="route.query.iscq">
        <svg-icon icon-class="tuli" />
      </div>
      <div class="min-handle-item" @click="zoomMap">
        <svg-icon icon-class="zoom" />
      </div>
      <!-- 图层 -->
      <div class="min-handle-item handle-marign" @click="showChangeTC = !showChangeTC" v-show="userStore.vipType == 3">
        <svg-icon icon-class="tuceng" />
      </div>
      <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
        <svg-icon icon-class="map" />
      </div>
      <!-- 聚合控制按钮 -->
      <div
        class="min-handle-item handle-marign"
        id="cluster-button"
        @click="toggleClusterPanel"
        :class="{ 'handle-marign-active': showClusterPanel }"
      >
        <svg-icon icon-class="cluster" />
      </div>
      <div class="tuceng-box" v-show="showChangeTC">
        <div style="margin-left: 8px">
          <span>总数:{{ treeCount }}</span>
          <span style="padding-left: 8px">勾选:{{ defaultCheckList.length }}</span>
        </div>
        <el-tree
          ref="treeRef"
          :data="layerList"
          show-checkbox
          node-key="id"
          @node-click="handleNodeClick"
          @check-change="handleNodeChange"
          :default-checked-keys="defaultCheckList"
          :props="defaultProps"
          :check-strictly="true"
          :default-expand-all="true"
        >
        </el-tree>
      </div>
    </div>
    <div class="map-change" v-show="showChangeMap">
      <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">天地图地图矢量</div>
      </div>
      <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">天地图影像</div>
      </div>
    </div>
    <div class="map-copyRight">
      <img src="https://api.tianditu.gov.cn/v4.0/image/logo.png" alt="" class="copy-ico" />
      GS（2024）0568号 - 甲测资字1100471
    </div>
    <div class="attr-box" v-show="showAttr" :style="{ 'top': top, 'left': left }">
      <div class="attr-div">
        <div class="attr-close" @click="showAttr = false"><i class="el-icon-circle-close"></i></div>
        <div class="attr-row title">属性</div>
        <div class="attr-row" v-for="(item, index) in properties" :key="index" :class="{ 'two': index % 2 != 0 }">
          <div class="left">{{ item.label }}</div>
          <div class="right">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <!-- 查看数据大屏 -->
    <div class="check-screen" @click="jumpScreen" v-show="userStore.vipType == 3">数据大屏</div>
    <!-- 左上方操作框 -->
    <div class="handle-top" :class="{ 'handle-vertical': handleTopType === 'vertical' }" ref="handleTop">
      <el-tooltip class="item" effect="dark" content="展开操作栏" placement="top">
        <svg-icon icon-class="pick_on" title="展开操作栏" class="pick-up" v-show="!isHandleOn" @click="isHandleOn = true" />
      </el-tooltip>
      <template v-if="isHandleOn">
        <div class="handle-icons" id="handle-icons">
          <el-dropdown @command="handleCommandUpdate">
            <div class="handle-item">
              管理<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="addProject">新建</el-dropdown-item>
                <el-dropdown-item command="handleRefreshExpress">刷新表达式</el-dropdown-item>
                <el-dropdown-item command="handleFlowTask">流程任务解锁</el-dropdown-item>
                <el-dropdown-item command="handleBitchUpdateField">批量修改字段</el-dropdown-item>
                <el-dropdown-item @click="showManager">批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div :class="handleTopType === 'across' ? 'hr' : 'vertical'"></div>
        <el-dropdown @command="handleCommand">
          <div class="handle-item">
            导出<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="beforeHandleCommand(item)" v-for="(item, index) in exportBtn" :key="index" :disabled="!item.id">{{
                item.exportName
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div :class="handleTopType === 'across' ? 'hr' : 'vertical'"></div>
        <el-dropdown @command="handleCommandUpdate">
          <div class="handle-item">
            更新<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="UpdateShp">shp更新</el-dropdown-item>
              <el-dropdown-item command="updateExcel">excel更新</el-dropdown-item>
              <el-dropdown-item command="updateTP">批量更新图片</el-dropdown-item>
              <el-dropdown-item command="updateZDFC">更新文件夹图片</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div :class="handleTopType === 'across' ? 'hr' : 'vertical'"></div>
        <el-dropdown @command="handleCommandUpdate">
          <div class="handle-item">
            标注<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="handleBZ">标注</el-dropdown-item>
              <el-dropdown-item command="handleBZRest">取消标注</el-dropdown-item>
              <el-dropdown-item command="handleChangeJZD">{{ !isShowJZD ? '显示界址点' : '隐藏界址点' }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div :class="handleTopType === 'across' ? 'hr' : 'vertical'"></div>
        <el-tooltip class="item" effect="dark" content="收起操作栏" placement="bottom">
          <svg-icon icon-class="pick_off" class="pick-up" @click="isHandleOn = false" />
        </el-tooltip>
      </template>
    </div>
    <div class="handle-graphic" v-show="props.nowCheckedZD || currentRuleId" :style="{ 'top': handleTopType === 'across' ? '60px' : '200px' }">
      <!-- <div class="handle-graphic-left">
        <div>
          <el-switch
            v-model="showHideValue"
            :active-value="true"
            :inactive-value="false"
            @change="handleVisibilityChange"
            active-text="显示"
            inactive-text="隐藏"
          />
        </div>
      </div> -->
      <div class="handle-graphic-right">
        <div style="display: flex; align-items: center; gap: 10px">
          <p>颜色：</p>
          <el-color-picker show-alpha v-model="graphicColor" @change="handleColorChange" />
        </div>
      </div>
    </div>
    <!-- 拆迁进度展示数据 -->
    <div class="cqplan-div" :style="{ top: cqplantop + 'px', left: cqplanleft + 'px' }" v-show="showCQPlan">
      <div class="cqplan-title">{{ parcelNameZ }}</div>
      <div class="hr"></div>
      <div class="cqplan-content">
        <!-- 已拆除 -->
        <div class="item cqOk">已拆除{{ msgMsg.cqOkNum }}</div>
        <!-- 未拆除 -->
        <div class="item cqON" style="border-left: none">未拆除{{ msgMsg.cqNoNum }}</div>
      </div>
    </div>
    <!-- 图例 拆迁户专用 -->
    <div class="right-map-color" v-show="showTL && route.query.iscq">
      <div class="title">
        <div>图例</div>
        <div class="right-close" @click="closeTL">×</div>
      </div>
      <div class="hr"></div>
      <div class="tuli-content">
        <div class="item">
          已完成调查
          <div class="color-box color1"></div>
        </div>
        <div class="item">
          已完成测绘
          <div class="color-box color2"></div>
        </div>
        <div class="item">
          已完成确权
          <div class="color-box color3"></div>
        </div>
        <div class="item">
          已完成评估
          <div class="color-box color4"></div>
        </div>
        <div class="item">
          已完成签约
          <div class="color-box color5"></div>
        </div>
        <div class="item">
          已完成腾空
          <div class="color-box color6"></div>
        </div>
        <div class="item">
          已完成拆除
          <div class="color-box color7"></div>
        </div>
      </div>
    </div>

    <!-- 聚合配置面板 -->
    <div class="cluster-config-panel" id="cluster-config-panel" v-show="showClusterPanel">
      <div class="title">
        <div>设置</div>
        <div class="right-close" @click="toggleClusterPanel">×</div>
      </div>
      <div class="hr"></div>
      <div class="cluster-content">
        <div class="config-section">
          <div class="section-title">🔷 要素聚合</div>
          <div class="config-item">
            <label>聚合开关:</label>
            <el-switch v-model="clusterConfig.enabled" @change="handleClusteringChange" />
          </div>
          <div class="config-item" v-show="clusterConfig.enabled">
            <label>视图缩放等级:</label>
            <el-input-number v-model="clusterConfig.zoomLevel" :min="1" :max="20" @change="handleClusteringChange" size="small" />
          </div>
          <div class="config-item" v-show="clusterConfig.enabled">
            <label>聚合半径:</label>
            <el-slider v-model="clusterConfig.distance" :min="1" :max="100000" @change="handleClusteringChange" />
            <span class="unit-label">{{ clusterConfig.distance }}m</span>
          </div>
          <div class="config-item" v-show="clusterConfig.enabled">
            <label>最小数量:</label>
            <el-input-number v-model="clusterConfig.minSize" :min="1" :max="1000" @change="handleClusteringChange" size="small" />
          </div>
          <div class="section-title">🔷 显示节点（默认显示所有节点）</div>
          <!-- <div>
            <el-button type="primary" text @click="handleRestMap">执行</el-button>
          </div> -->
          <el-tree
            ref="ruleTreeRef"
            :data="props.ruleTree"
            show-checkbox
            node-key="id"
            :default-checked-keys="defaultCheckRules"
            :props="ruleTreeProps"
            :check-strictly="true"
            :default-expand-all="true"
            @check="handleTreeCheck"
          >
            <template #default="{ node }">
              <span style="color: #666">{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </div>
    </div>
    <!-- 导入/更新SHP -->
    <update-shp :uploadProjectDialog="shpUploadDialog" :moduleId="projectStore.proModuleId" @closeDialog="handleCloseShpDialog"></update-shp>
    <!-- 标注弹窗 -->
    <labelDialogComponent
      :labelDialog="labelDialog"
      :moduleId="projectStore.proModuleId"
      @handleCloseLabel="handleCloseLabel"
      @submitField="submitField"
    ></labelDialogComponent>
    <!-- 用excel导入更新 -->
    <updateExcel :uploadExcelDialog="uploadExcelDialog" @closeExcelUpload="closeExcelUpload" :moduleId="projectStore.proModuleId"></updateExcel>
    <!-- 特殊用excel导入更新 适用杭州的那张村提供的彩色复杂表格 -->
    <updateSpe :updateSpeDialog="updateSpeDialog" :moduleId="projectStore.proModuleId" @closeSpeUpload="closeSpeUpload"></updateSpe>
    <!-- shp更新界址点、界址线弹窗 -->
    <updateShpChild :updateShpChildDialog="updateShpChildDialog" :moduleId="projectStore.proModuleId" @closeShpChild="closeShpChild"></updateShpChild>
    <!-- 批量更新图片 -->
    <template v-if="updateTPFileDialog">
      <updateTPFile :updateTPFileDialog="updateTPFileDialog" :moduleId="projectStore.proModuleId" @closeUpdateTP="closeUpdateTP"></updateTPFile>
    </template>
    <!-- 批量更新宗地和房产图 -->
    <updateTPZDFC :updateZDFCDialog="updateZDFCDialog" :moduleId="projectStore.proModuleId" @closeUpdateZDFC="closeUpdateZDFC"></updateTPZDFC>
    <!-- 刷新表达式 -->
    <refreshExpress
      :refreshExpressDialog="refreshExpressDialog"
      @closeRefreshExpress="closeRefreshExpress"
      :moduleIdPop="projectStore.proModuleId"
      :ruleTree="ruleTree"
    ></refreshExpress>
    <!-- 流程任务数据解锁管理 -->
    <flowTaskManager :flowTaskDailog="flowTaskDailog" @closeFlowTask="closeFlowTask" :moduleId="projectStore.proModuleId"></flowTaskManager>
    <!-- 批量更新字段 -->
    <bitchEditField
      :bitchUpdateFieldDialog="bitchUpdateFieldDialog"
      :ruleTree="ruleTree"
      :moduleId="projectStore.proModuleId"
      @handleCloseBitchUpdateFieldDialog="handleCloseBitchUpdateFieldDialog"
    ></bitchEditField>
    <!-- 移植5.0的勘界 -->
    <oldKJDialog
      v-show="kjDialog"
      :companyData="companyData"
      :kjDialog="kjDialog"
      @closeKJDialog="closeKJDialog"
      @sumbitDownloadKJ="sumbitDownloadKJ"
    ></oldKJDialog>
    <!-- 公共导出 -->
    <el-dialog
      :title="nowDownMsg.exportName"
      v-model="publicDownDialoig"
      width="1200px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <el-form :model="downLoadMsg" :rules="downLoadMsgRule" label-position="top" ref="downLoadRef" class="demo-ruleForm">
        <div class="left-content">
          <div class="form-row">
            <el-form-item label="选择坐标系统" :prop="nowDownMsg.coordinate.must == 1 ? 'onWkid' : ''" class="form-item">
              <el-cascader
                v-model="downLoadMsg.onWkid"
                :options="wkidList"
                :show-all-levels="false"
                style="width: 100%"
                :placeholder="nowDownMsg.coordinate.placeholder"
              ></el-cascader>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="面积计算方式" class="form-item">
              <el-radio-group v-model="downLoadMsg.areaType">
                <el-radio :value="1">投影坐标系</el-radio>
                <el-radio :value="2">大地坐标系</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="导出shp编码" class="form-item">
              <template #label>
                <span>导出shp编码</span>
                <span class="tip-class">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="一般情况下：UTF-8适配于arcmap 10.7版本,GBK适配于arcmap 10.2及以下版本"
                    placement="top-start"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-radio-group v-model="downLoadMsg.code">
                <el-radio value="UTF-8">UTF-8</el-radio>
                <el-radio value="GBK">GBK</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item class="form-item">
              <template #label>
                <span>是否分批导出</span>
                <span class="tip-class">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="分批导出默认是10条一批次，如果没有超过10条不会分批。（分批导出会生成批次号，方便排查问题）"
                    placement="top-start"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <div class="flex items-center">
                <div>
                  <el-radio-group v-model="isBatch">
                    <el-radio :value="1">是</el-radio>
                    <el-radio :value="0">否</el-radio>
                  </el-radio-group>
                </div>
                <div style="width: 200px">
                  <el-select v-show="isBatch == 1" v-model="batchNum" placeholder="请选择每个批次数量" style="margin-left: 20px">
                    <el-option v-for="item in 10" :key="item" :label="`${item}条`" :value="item"> </el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="包围盒类型" v-if="showBoxType" class="form-item">
              <div class="tip-class">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="包围盒默认是按照矩形，为了适配以前老数据（已入库）用户可选择按正方形计算包围盒，会影响J1位置判断"
                  placement="top-start"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </div>
              <el-radio-group v-model="downLoadMsg.boxType">
                <el-radio :label="1">矩形</el-radio>
                <el-radio :label="2">正方形</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="公司信息" class="form-item">
              <el-select v-model="downLoadMsg.companyName" placeholder="请选择" style="width: 100%" @change="changeCompany">
                <el-option v-for="item in companyList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="right-content">
          <el-form-item label="数据列表" :prop="nowDownMsg.dataList.must == 1 ? 'zdList' : ''" class="data-list-item">
            <div class="dialog-search" style="display: flex; align-items: center; justify-content: space-between">
              <el-button type="primary" size="small" @click="searchBtn">筛选数据</el-button>
              <div class="selected-count" v-if="multipleSelection.length > 0">
                <span>已选择 {{ multipleSelection.length }} 条数据</span>
                <el-button type="text" @click="clearSelection">清空选择</el-button>
              </div>
            </div>
            <el-table-v2
              :columns="columns"
              :data="downLoadMsg.zdList"
              :width="500"
              :height="500"
              :row-height="40"
              :fixed="true"
              @row-click="handleRowClick"
              :row-class="getRowClass"
              class="dialog-content"
            />
            <div class="page">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="dialogSearch.pageNum"
                :page-sizes="[20, 50, 100, 500, 1000, 2000, 5000]"
                :page-size="dialogSearch.pageSize"
                layout="total, sizes, pager"
                :total="localTotal"
              >
              </el-pagination>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer" style="display: flex; justify-content: space-between; align-items: center; width: 100%">
          <div>
            <el-checkbox v-model="isNoDel" v-if="userStore.user['userName'] === '18285070490'">不删除服务器数据</el-checkbox>
            <el-checkbox v-model="downLoadMsg.ignore">忽略表达式错误内容</el-checkbox>
          </div>
          <div>
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="submitDown">确 定</el-button>
          </div>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="downLoadMsg.zdList"
      :isManager="managerDialog"
      :ifTree="ifTree"
      :isKJ="isKJ"
      :ruleTree="ruleTree"
      :ruleIds="ruleIds"
    ></searchData>
    <!-- 进度条弹窗 -->
    <el-dialog
      title="数据下载中"
      v-model="progressDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleCloseProgress"
      :width="showBatch ? '500px' : '300px'"
    >
      <!-- 不是批量导出的时候 -->
      <div class="down-dialog" v-if="!showBatch">
        <el-progress :stroke-width="16" type="circle" :percentage="progress" :status="downStatus as any"></el-progress>
        <div style="margin-top: 10px">{{ downMsg }}</div>
      </div>
      <!-- 批量导出的时候 -->
      <div class="down-content" v-else>
        <div class="flex-row" style="font-weight: bold">总进度：</div>
        <div class="flex-row">
          <el-progress :text-inside="true" :stroke-width="26" :percentage="batchProgress"></el-progress>
          <div style="margin-top: 10px">{{ batchMsg }}</div>
        </div>
        <div class="flex-row" style="font-weight: bold">当前导出进度：</div>
        <div class="flex-row">
          <el-progress :text-inside="true" :stroke-width="26" :percentage="oneceProgress"></el-progress>
          <div style="margin-top: 10px">{{ downMsg }}</div>
        </div>
      </div>
    </el-dialog>
    <!-- 筛选弹窗 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      @editCondition="editCondition"
      :moduleIdPop="moduleId"
      :isShowSpe="isShowSpe"
    ></dataSearch>
    <el-dialog
      title="请选择下载方式"
      v-model="downloadTypeDialog"
      width="800px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleCloseDownType"
    >
      <div class="dialog-box">
        <div class="dialog-item" @click="changeDownloadType(1)" :class="{ 'dialog-item-active': downloadType == 1 }">
          <img src="../../../assets/images/xunlei.png" alt="" class="left" />
          <div class="right">
            <span class="top">迅雷下载</span>
            <span class="bottom">高速稳定下载</span>
          </div>
          <div class="end">
            <el-checkbox v-model="xlDown" class="custom-size"></el-checkbox>
          </div>
        </div>
        <div class="dialog-item" @click="changeDownloadType(2)" :class="{ 'dialog-item-active': downloadType == 2 }">
          <img src="../../../assets/images/normal.png" alt="" class="left" />
          <div class="right">
            <span class="top">普通下载</span>
            <span class="bottom">浏览器下载</span>
          </div>
          <div class="end">
            <el-checkbox v-model="ptDown" class="custom-size"></el-checkbox>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDownType">取 消</el-button>
          <el-button type="primary" @click="submitDaownType">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <el-tour v-model="open" @close="handleTourClose">
    <el-tour-step target="#handle-icons" title="操作工具栏" description="我们对工具栏进行了迁移，现在你可以在这进行操作！" />
    <el-tour-step
      target="#cluster-button"
      title="新增图层显示功能"
      description="我们新增了图层显示功能！现在地图默认加载聚合后的效果，如果你想查看原始图形，可以点击该按钮操作聚合控制面板来进行聚合操作。"
    />
  </el-tour>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onBeforeUnmount, h } from 'vue';
import { getToken } from '@/utils/auth';
import { loadModules } from 'esri-loader';
import { ElMessage, ElMessageBox, ElNotification, ElLoading, ElCheckbox } from 'element-plus';
import { ArrowDown } from '@element-plus/icons-vue';
import { Upload, Document, Picture, Folder, Edit, Remove, View, Refresh, Unlock, EditPen } from '@element-plus/icons-vue';
// import autoImage from '@/components/autoImage/index.vue';
import authImg from '@/components/authImg/index.vue';
import searchData from './searchData/index.vue';
import dataSearch from '@/components/dataSearch/index.vue';
import { getSearchTask } from '@/api/task';
import {
  getPlaceDetail,
  selectModuleById as selectModuleByIdApi,
  exportSettingList,
  getExportDetail,
  downLoadPublic,
  downLoadPublicCheck,
  getPlaceList,
  selectParcelClusterList,
  selectGeometry
} from '@/api/modal';
import { findAsyncMsg as findAsyncMsgApi, findAsyncFileBrowser, exportPcNew, selectIfOrder } from '@/api/project';
import { listUser } from '@/api/system/user';
import { checkPermi } from '@/utils/permission';
import { getCompany as getCompanyApi } from '@/api/control';
// 弹窗组件导入
import updateShp from '@/components/updateSHP/index.vue';
import labelDialogComponent from '@/views/autoProject/components/labelDialog.vue';
import updateExcel from '@/components/updateExcel/index.vue';
import updateSpe from '@/components/updateSpe/index.vue';
import updateShpChild from '@/components/updateShpChild/index.vue';
import updateTPFile from '@/components/updateTPFile/index.vue';
import updateTPZDFC from '@/components/updateTPZDFC/index.vue';
import refreshExpress from '@/components/refreshExpress/index.vue';
import flowTaskManager from '@/components/flowTaskManager/index.vue';
import bitchEditField from '@/views/autoProject/components/bitchEditField.vue';
import { hexToRgba } from '@/utils/validate';
import { getTCList, getShpTree as getShpTreeApi } from '@/api/issueManager';
import axios from 'axios';
import { getScreenList, saveScreen } from '@/api/dataScreen';
import { useProjectStore } from '@/store/modules/project';
import taskDefaultBigData from '@/data/taskDefaultBigData.json';
import { throttle } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { useRoute } from 'vue-router';
import oldKJDialog from '@/components/oldKJDialog/index.vue';
import type { TableV2RowScope } from 'element-plus';
import { isArray } from '@/utils/validate';
import { useTdtInstanceComponent } from './hook/useTdtInstanceComponent';
import degImg from '@/assets/images/deg.png';

const route = useRoute();
const userStore = useUserStore();
const TOUR_VIEWED_KEY = 'datamap_tour_viewed';
const open = ref(!localStorage.getItem(TOUR_VIEWED_KEY));

// 处理导览关闭事件
const handleTourClose = () => {
  localStorage.setItem(TOUR_VIEWED_KEY, 'true');
};

const router = useRouter();
const shaixuanDialog = ref(false); // 筛选弹窗
const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

let fontLayer = null; //文字layer和点
let graphicsLayerAll = null; //所有宗地layer
let childGraphics: any = null; //查询详情 子要素图形layer
let degGraphics = null; //方位角layer
let labelLayer: any = null; //标注层
let jzdzbLayer = null; //界址点坐标 高亮用
let tiledLayer = null; //影像
let tiledLayerAnno = null; //影像标记
let normalLayer = null; //矢量底图
let normalAnno = null; //矢量标记
let labelFeatureLayer = null;
const tolerance = 3; //偏差值
const sketch = null;
const handleExtentChange = throttle(() => {
  // 处理地图范围变化的逻辑
}, 200); // 每 200 毫秒处理一次
const isShowSpe = ref(false); // 切换要素节点和图层节点 当有图层节点的时候才切换
// 接收的参数部分
interface QueryParams {
  pageNum: number;
  pageSize: number;
  parcelName: string;
  moduleId: number | string;
  areaCode: string;
}
interface Props {
  parcelList: any[];
  total: number;
  queryParams: QueryParams;
  nowCheckedZD: number;
  pageCount: number;
  title: string;
  ruleTree: any[];
  mainHeight: number;
  isShowJZD: boolean;
  firstNodes: any[];
  isKJ: boolean;
  toolType: number;
  isAZFI: boolean;
  ruleIds: any[];
  defaultWMS: string;
  changeParcelList: boolean;
  currentRuleId: string; // 新增props
  kjExportUrl: string;
}
const props = withDefaults(defineProps<Props>(), {
  parcelList: () => [],
  total: 0,
  queryParams: () => ({
    pageNum: 1,
    pageSize: 20,
    parcelName: '',
    moduleId: undefined,
    areaCode: ''
  }),
  nowCheckedZD: 0,
  pageCount: 0,
  title: '',
  ruleTree: () => [],
  mainHeight: 0,
  isShowJZD: true,
  firstNodes: () => [],
  isKJ: false,
  toolType: 0,
  isAZFI: false,
  ruleIds: () => [],
  changeNowCheckedZD: 0,
  currentRuleId: '', // 新增props
  kjExportUrl: '' //勘界自主配置的导出url
});

const projectStore = useProjectStore();
const queryParamsCopy = computed(() => props.queryParams);
//  ---定义emit---
const emit = defineEmits<{
  (e: 'checkedOneParcelId', parcelId: any): void;
  (e: 'getLinyeData'): void;
  (e: 'changeNowCheckedZD', id: any): void;
  (e: 'noInitCenter'): void;
  (e: 'getNodeStyle'): void;
  (e: 'drawLabel', fieldName: string, linkId: number): void;
  (e: 'removerLabelLayer'): void;
  (e: 'getLinyeData'): void;
  (e: 'update:nowCheckedZD', id: any): void;
  (e: 'openInfo'): void;
  (e: 'toggleGraphicsVisibility'): void;
  (e: 'getParmas', params: any): void;
}>();

// --- 定义变量部分 ---
const currentSelectZd: any = ref({}); //选中的宗地
const baseUrlImg = ref(import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/');
let map = null; //地图容器
let view = null; //mapview
const allZD: any = ref([]); //选中项目所有宗地
const fit = ref('cover'); //地图缩放方式
const oldHighlight: any = ref(null); //之前高亮的图形
const fullscreenLoading = ref(false);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const token = ref(getToken());
const showChangeMap = ref(false);
const checkedMap = ref('image'); //显示的底图 默认影像
const oldSpeHightGrc = ref(null); //上一个高亮的方位角要素
const showChangeTC = ref(false); //显示选择图层
const layerList = ref<Array<any>>([]); //图层列表
const nowRuleIds = ref<Array<any>>([]); //选中的规则id
// 类型定义
interface Property {
  label: string;
  value: string;
}

interface ExportButton {
  exportName: string;
  id: number;
}

const checkList = ref([]); //选中的图层
const base = ref(import.meta.env.VITE_APP_LAYER_BASE);
const properties = ref<Property[]>([]); //点击的要素数据
const showAttr = ref(false); //是否展示要素属性
const top = ref('0px');
const left = ref('0px');
const isHandleOn = ref(true); //顶部操作展开
const showCQPlan = ref(false); //是否显示拆迁进度
const msgMsg: any = reactive({}); //拆迁进度显示的信息
const nowPoint = ref(null); //拆迁进度当前高亮的图形的中间点
const cqplanleft = ref(0); // 拆迁进度显示弹窗左侧距离
const cqplantop = ref(0); //拆迁进度显示弹窗上侧距离
const showTL = ref(false); //是否显示图例
const parcelNameZ = ref(''); //栋名字 拆迁进度需要
const reloadFirst = ref(true); //是否是重新绘制第一次
const defaultProps = reactive({
  children: 'children',
  label: 'label',
  id: 'id'
}); // 属性结构绑定的节点
const ruleTreeProps = reactive({
  children: 'list',
  label: 'typeName',
  id: 'id'
});
// 当前树结构的层级展示
const treeCount = ref(0); // 当前树结构的层级展示
const defaultCheckList = ref([]); // 默认勾选当前树节点
const mapDescVisible = ref(false); // 设置图层描述
const mapDescContent = ref(''); // 图层描述的内容
const treeRef = ref(null);
const gisMapRef = ref(null);
const msgList = ref([]); // 拆迁进度信息
const nowLayerList = ref<any[]>([]); // 当前选中的图层
const allNodeStyle = ref([]); //规则树的节点平铺样式
// 从 zdList.vue 移动过来的变量
const exportBtn = ref<ExportButton[]>([]); // 导出按钮列表
const managerDialog = ref(false); // 管理弹窗
const publicDownDialoig = ref(false); // 公共下载弹窗
const handleTop = ref(null); // 顶部操作栏

// 添加本地总数
const localTotal = ref(props.total);

// 监听 props.total 的变化
watch(
  () => props.total,
  (newVal) => {
    localTotal.value = newVal;
  }
);

// 监听弹窗打开，加载数据
watch(
  () => publicDownDialoig.value,
  (val) => {
    if (val) {
      // 获取数据列表
      getPlaceList({
        moduleId: projectStore.proModuleId,
        pageNum: 1,
        pageSize: 20
      }).then((res) => {
        if (res.code == 200) {
          downLoadMsg.value.zdList = res.data.list;
          localTotal.value = res.data.total; // Update local total
          // 恢复选中状态
          nextTick(() => {
            if (multipleSelection.value.length > 0) {
              multipleSelection.value.forEach((row) => {
                multipleTableRef.value?.toggleRowSelection(row, true);
              });
            }
          });
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  }
);
const downStatus = ref('');
let nowDownMsg: any = reactive({
  //选中的导出设置内容
  coordinate: {
    placeholder: '',
    must: 1,
    defaultValue: undefined
  },
  dataList: {
    placeholder: ''
  },
  exportName: '',
  detail: {
    fileName: ''
  },
  areaType: undefined
});
const downLoadMsg: any = ref({
  //导出设置
  zdList: [],
  companyName: ''
});
//导出设置
const downLoadMsgRule = ref({}); // 导出设置规则
const searchDialog = ref(false); // 搜索弹窗

// 表格相关变量
const multipleTableRef = ref();
const multipleSelection = ref<any[]>([]); // 表格选中的数据

// 格式化日期
const formatDateType = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

// 处理表格全选
const handleSelectionAll = (selection: any[]) => {
  multipleSelection.value = selection;
  // 只更新选中的数据名称，不影响原始数据列表
  downLoadMsg.value.zdListNames = selection.map((item) => item.parcelName);
  // 保持原始数据列表不变，只更新选中状态
  nextTick(() => {
    const currentData = downLoadMsg.value.zdList;
    if (selection.length === 0) {
      // 取消全选时，清除所有选中状态但保持数据显示
      currentData.forEach((row) => {
        multipleTableRef.value?.toggleRowSelection(row, false);
      });
    } else {
      // 全选时，设置所有行为选中状态
      currentData.forEach((row) => {
        multipleTableRef.value?.toggleRowSelection(row, true);
      });
    }
  });
};

// 处理表格行选择
const handleSelectTable = (selection: any[], row: any) => {
  multipleSelection.value = selection;
  // 只更新选中的数据名称，不影响原始数据列表
  downLoadMsg.value.zdListNames = selection.map((item) => item.parcelName);
  // 确保行选择状态一致
  nextTick(() => {
    const isSelected = selection.some((item) => item.id === row.id);
    multipleTableRef.value?.toggleRowSelection(row, isSelected);
  });
};

// 处理行点击
const handleRowClick = (row: any, column: any, event: any) => {
  if (column.type === 'selection') return;
  const isSelected = multipleSelection.value.some((item) => item.id === row.id);
  multipleTableRef.value?.toggleRowSelection(row, !isSelected);
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  dialogSearch.value.pageSize = val;
  getData(dialogSearch.value);
};

const handleCurrentChange = (val: number) => {
  dialogSearch.value.pageNum = val;
  getData(dialogSearch.value);
};
// 获取数据
const getData = (params: any) => {
  fullscreenLoading.value = true;
  getPlaceList({
    ...params,
    moduleId: projectStore.proModuleId
  }).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      downLoadMsg.value.zdList = res.data.list;
      localTotal.value = res.data.total; // Update local total
      // 恢复选中状态
      nextTick(() => {
        multipleSelection.value.forEach((row) => {
          multipleTableRef.value?.toggleRowSelection(row, true);
        });
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 筛选按钮点击事件
const searchBtn = () => {
  shaixuanDialog.value = true;
};
let shpGdbTree = reactive({
  //导出的树结构迭代 整理出来shp和gdb列表
  shp: [],
  gdb: [],
  pic: [],
  word: [],
  excel: [],
  fileMap: []
});
const progress = ref(0); // 进度
const progressDialog = ref(false); // 进度弹窗
const downLoadFileName = ref(''); // 下载文件名
const moduleId = ref(''); // 模块ID
const companyList = ref<Array<{ label: string; value: string }>>([]); // 公司列表
const uploadExcelDialog = ref(false); // 上传Excel弹窗
const updateSpeDialog = ref(false); // 更新特殊弹窗
const updateShpChildDialog = ref(false); // 更新shp子弹窗
const updateTPFileDialog = ref(false); // 更新TP文件弹窗
const updateZDFCDialog = ref(false); // 更新ZDFC弹窗
const refreshExpressDialog = ref(false); // 刷新表达式弹窗
const catNodeDialog = ref(false); // 分类节点弹窗
const catMsg = reactive({
  nodeType: 1,
  ruleIds: []
}); // 分类信息
const catMsgRules = ref({}); // 分类信息规则
const flowTaskDailog = ref(false); // 流程任务弹窗
const downloadTypeDialog = ref(false); // 下载类型弹窗
const downLoadId = ref(''); // 下载ID
const ifTree = ref(false); // 是否树形
const downloadType = ref<number>(1); // 下载类型 1:普通下载 2:迅雷下载
const fileSize = ref(null); // 文件大小
const progressEnd = ref(null); // 进度情况
const isNoDel = ref(false); // 是否不删除
const treeSettingDialog = ref(false); // 树设置弹窗
const showGraphs = ref([]); // 显示图形
const isBatch = ref<number>(0); // 是否批量导出 0:否 1:是
const showBatch = ref(false); // 显示批量
const batchProgress = ref(0); // 批量导出进度
const batchMsg = ref({}); // 批量信息
const oneceProgress = ref(0); // 单次导出进度
const nowBatch = ref(0); // 当前批量
const batchNum = ref(50); // 批量数量
const bitchUpdateFieldDialog = ref(false); // 批量更新字段弹窗
const downLoadRef = ref(null); // 下载引用
const catMsgRef = ref(null); // 分类信息引用
const companyData = ref([]); // 公司数据
const showBoxType = ref(false); // 显示盒子类型
const isShowJZD = ref(false); // 是否显示界址点
const isDefault = ref(false); // 是否默认
const currentPercent = ref(0); // 当前百分比
const isContinue = ref(false); // 是否继续下载
let loadingProgessInstance: any = null; // 加载进度实例
const labelDialog = ref(false); // 标注弹窗
const shpUploadDialog = ref(false); // 导入或者更新shp
const kjDialog = ref(false); // 勘界弹窗
const isChecked = ref(false);
import { geojsonToWKT } from '@terraformer/wkt';
interface GraphicsLayer {
  graphics: {
    items: any[];
    toArray(): any[];
    length: number;
  };
  visible: boolean;
  removeAll(): void;
  add(graphic: any): void;
}

interface Map {
  remove(layer: any): void;
}
// 聚合相关变量
const isClusteringEnabled = ref(false); // 修改为false,默认不启用聚合
const showClusterPanel = ref(false); // 控制聚合面板显示
const clusterConfig = reactive({
  enabled: true, // 统一的聚合开关
  distance: 100, // 统一的聚合距离
  minSize: 1, // 统一的最小聚合数量
  zoomLevel: 16 // 视图缩放等级
});
let clusterLayer: any = null; // 聚合图层
let originalGraphicsData: any[] = []; // 原始图形数据

const wkidList = reactive([
  {
    value: 'CGCS_2000',
    label: 'CGCS_2000',
    children: [
      {
        value: 'cgcs2000_3',
        label: '三度带',
        children: []
      },
      {
        value: 'cgcs2000_6',
        label: '六度带',
        children: []
      }
    ]
  }
]);
const downMsg = ref(''); // 导出进度信息
const dialogSearch: any = ref({
  //弹窗筛选
  areaCode: queryParamsCopy.value.areaCode, // 行政区划
  createDate: '', //创建时间
  updateDate: '', //最后修改时间
  createUserId: '', //采集人
  updateUserId: '', //最后修改人
  taskId: '', //任务id
  allocation: '', //查询是否分配任务 如果选择了任务设置为true
  pageNum: queryParamsCopy.value.pageNum,
  pageSize: queryParamsCopy.value.pageSize,
  parcelName: queryParamsCopy.value.parcelName,
  moduleId: queryParamsCopy.value.moduleId,
  conditionFields: [], //字段查询
  ruleIds: [],
  parcelCode: null,
  ifCheck: false,
  downLoadId: undefined,
  createTimeStart: '',
  createTimeEnd: '',
  updateTimeStart: '',
  updateTimeEnd: '',
  createUserName: '',
  optUserName: '',
  optUserId: '',
  express: false //是否查询表达式异常的数据
});
const taskList = ref([]); // 任务列表
const defaultCheckRules = ref([]); // 默认勾选的规则
// 定义 ruleTreeRef
const ruleTreeRef = ref(null);
const lastGeoWkt = ref(null); // 上一次的wkt
const nowHighlightId = ref(null); // 当前选中的高度
const nowViewCenter = ref(null); // 当前地图中心点
const showHideValue = ref(true); // 修改默认值为true
const graphicColor = ref('');
const handleTopType = ref('across'); // 处理顶部操作栏类型 竖 vertical 横 across

// --- 定义方法部分 ---

/**
 * 选中某条数据 地图初始化显示子要素 先要把子要素数据全部清除
 * @param data 选中的数据
 */
const initData = (data: any) => {
  currentSelectZd.value = data;
  highlightZD();
  //选中某个宗地 父要素
  //1、先清除childGraphics 2、再绘制所有子要素
  childGraphics.removeAll();
  // 清除所有方位角
  clearDeg();
  // // 迭代绘制子要素
  // drawChildEle(data.list);
};

/**
 * 当前选中的数据字段内容
 */
const handleNodeClick = (data) => {
  const index = data.mapName.indexOf(':');
  const title = data.mapName.substring(index + 1, data.mapName.length);
  // 得到title后 找到相应的图层 居中
  const layer = map.findLayerById(title);
  view.center = [layer.fullExtent.center.longitude, layer.fullExtent.center.latitude];
};

/**
 * 选择图层
 * @param data 选择的图层
 * @param flg 当前选中图层的状态

 */
const handleNodeChange = (data, flg) => {
  // 存在的时候 加载wmslayer
  if (flg) {
    const index = data.mapName.indexOf(':');
    const title = data.mapName.substring(index + 1, data.mapName.length);
    const url = `${base.value}${data.mapName.substring(0, index)}/${title}/wms`;
    initWMS(url, title);
  } else {
    //取消选中的时候要把对应的wmslayer移除
    const index = data.mapName.indexOf(':');
    const title = data.mapName.substring(index + 1, data.mapName.length);
    removeWMS(title);
  }
};

/**
 * 移除wmsLayer
 * @param title 图层名称
 */
const removeWMS = (title: any) => {
  // 查找所有匹配的图层
  const layers = map.layers.items.filter((layer) => layer.id === title || layer.title === title);

  // 移除找到的所有匹配图层
  layers.forEach((layer) => {
    try {
      map.remove(layer);
      layer.destroy(); // 确保图层被完全销毁
    } catch (error) {
      console.error('移除图层失败:', error);
    }
  });

  // 强制刷新视图
  view.when(() => {
    view.goTo(view.extent);
  });
};

/**
 * 查看数据图层结果树
 * @param data 图层数据
 */
const handleMapDetial = (data) => {
  mapDescVisible.value = true;
  if (data.descripition && data.descripition !== '' && data.descripition !== null) {
    mapDescContent.value = data.descripition;
  } else {
    mapDescContent.value = '暂无图层描述';
  }
};

/**
 * 测试展示数据
 */
const getShpTree = (flg) => {
  // 图层树结构
  getShpTreeApi().then((res) => {
    if (res.code == 200) {
      layerList.value = res.data;
      treeCount.value = countTotalNodes(res.data);
      // 默认勾选第一个图层
      nowLayerList.value = [];
      if (layerList.value.length > 0) {
        defaultCheckList.value.push(layerList.value[0].id);
        nowLayerList.value.push(layerList.value[0].mapName);
        changeLayer([layerList.value[0].mapName]);
      }
      layerList.value.length > 0 && changeLayer([layerList.value[0]?.mapName]);

      // 如果是图层要素 需要把要素绑定的图层默认选中
      if (props.defaultWMS) {
        for (let index = 0; index < layerList.value.length; index++) {
          if (layerList.value[index].layerName == props.defaultWMS) {
            layerList.value[index].checked = true;
            if (!checkList.value.includes(props.defaultWMS)) {
              checkList.value.push(layerList.value[index].layerName);
            }
            break;
          }
        }
      }
      if (!flg) {
        showChangeTC.value = !showChangeTC.value;
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取当前的展示数据
 * @param nodes 当前层级的节点
 * @returns 当前层级的节点数
 */
const countTotalNodes = (nodes) => {
  let count = nodes.length; // 当前层级的节点数
  nodes.forEach((node) => {
    if (node.children && node.children.length > 0) {
      count += countTotalNodes(node.children); // 递归计算子节点数量并累加
    }
  });
  return count;
};

/**
 * 点击时高亮当前的宗地
 * @param item 当前宗地
 */
const getCurrentParcel = (item) => {
  currentSelectZd.value = item;
  highlightZD();
};

/**
 * 初始化林业
 * @param val 当前宗地
 * @param isInitCenter 是否初始化中心
 */
const initLinye = (val, isInitCenter) => {
  if (fontLayer) {
    map.remove(fontLayer);
    fontLayer = null; // 清空引用避免内存泄漏
  }
  if (childGraphics) {
    childGraphics.removeAll();
  }
  allZD.value = val;
  // //加载所有宗地
  // drawZD(isInitCenter);
  if (val.length === 0 || !val[0].geometryId) {
    return;
  }
  //改为请求第一个图形，然后居中第一个图形再请求该图形视窗范围
  const params = {
    id: val[0].geometryId
  };
  selectGeometry(params).then((res) => {
    if (res.code == 200) {
      //如果第一个图形存在 则加载第一个图形并居中 然后再请求视窗范围
      drawFirstGeometry(res.data, val[0].ruleId, val[0].graphicalType, val[0].id);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 将驼峰命名转换为短横线命名
 * @param str 驼峰命名
 * @returns 短横线命名
 */
const camelToKebab = (str) => {
  return str?.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();
};
/**
 * 绘制第一个宗地
 * @param data 绘制的第一个图形数据
 * @param ruleId 规则id
 * @param graphicalType 图形类型 1:点 2:线 3:面
 * @param dataId 数据id
 */
const drawFirstGeometry = (data: any, ruleId: number, graphicalType: number, dataId: number) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    graphicsLayerAll.removeAll();
    let oldIndex = -1;
    if (props.nowCheckedZD) {
      // 当前要素列表是否存在之前选中的id
      oldIndex = allZD.value.findIndex((item: any) => item.id == props.nowCheckedZD);
    }

    // 存储原始图形数据用于聚合
    const currentGraphics: any[] = [];
    if (data.geomArcgis) {
      let bgColor = null;
      const polylineColor = getOneStyle(ruleId, 'polylineColor') || '';
      if (polylineColor.includes('rgba')) {
        bgColor = polylineColor;
      } else {
        bgColor = hexToRgba(polylineColor, '0.2');
        if (getOneStyle(ruleId, 'polygonFillColor')) {
          bgColor = hexToRgba(getOneStyle(ruleId, 'polygonFillColor'));
        }
      }
      const polygon = jsonUtils.fromJSON(JSON.parse(data.geomArcgis));

      if (graphicalType == 1) {
        //点
        //创建文本符号
        let textSymbol = new TextSymbol();

        textSymbol = {
          type: 'simple-marker', // autocasts as SimpleLineSymbol()
          style: camelToKebab(getOneStyle(ruleId, 'pointType')) || 'circle',
          color: getOneStyle(ruleId, 'pointColor'),
          width: getOneStyle(ruleId, 'pointSize') || 2
        };
        const graphic = new Graphic({
          geometry: polygon,
          symbol: textSymbol,
          id: dataId
        });
        //定位第一个
        view.center = [polygon.longitude, polygon.latitude];
        view.zoom = 18;
        // graphicsLayerAll.add(graphic);
        currentGraphics.push(graphic);
      } else if (graphicalType == 2) {
        //线
        const lineSymbol = {
          type: 'simple-line', // 自动转换为新的 SimpleLineSymbol()
          color: getOneStyle(ruleId, 'polylineColor'), // RGB颜色值作为数组
          width: getOneStyle(ruleId, 'polylineWidth') || 1,
          style: getOneStyle(ruleId, 'polylineType')
        };
        const polygonGraphic = new Graphic({
          geometry: polygon,
          symbol: lineSymbol,
          id: dataId
        });
        //定位第一个
        view.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
        view.zoom = 18;
        // graphicsLayerAll.add(polygonGraphic);
        currentGraphics.push(polygonGraphic);
      } else if (graphicalType == 3) {
        //面
        const simpleFillSymbol = {
          type: 'simple-fill',
          style: camelToKebab(getOneStyle(ruleId, 'polygonType')) || 'solid',
          outline: {
            color: getOneStyle(ruleId, 'polylineColor'),
            width: getOneStyle(ruleId, 'polylineWidth') || 1,
            style: getOneStyle(ruleId, 'polylineType')
          }
        };
        const polygonGraphic = new Graphic({
          geometry: polygon,
          symbol: simpleFillSymbol,
          id: dataId
        });
        view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
        view.zoom = 18;
        // graphicsLayerAll.add(polygonGraphic);
        currentGraphics.push(polygonGraphic);
      }
    }

    // 保存当前图形数据用于聚合
    originalGraphicsData = currentGraphics;

    // 如果有图形数据，应用聚合
    if (currentGraphics.length > 0) {
      // 强制应用聚合
      if (!clusterLayer) {
        handleClusteringChange();
      }
    }
    // 赋值需要高亮的数据id
    nowHighlightId.value = dataId;
  });
};
/**
 * 初始化地图
 */
const init = () => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine'
    ],
    config
  ).then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine]) => {
    const { tiledLayerIns, tiledLayerAnnoIns, normalLayerIns, normalAnnoIns } = useTdtInstanceComponent(WebTileLayer, SpatialReference);
    tiledLayer = tiledLayerIns;
    tiledLayerAnno = tiledLayerAnnoIns;
    normalLayer = normalLayerIns;
    normalAnno = normalAnnoIns;

    map = new Map({
      // topo-vector
      // basemap: "satellite",
      basemap: {
        baseLayers: [tiledLayer, tiledLayerAnno, normalLayer, normalAnno]
      },
      // ground: "world-elevation",
      logo: false,
      spatialReference: {
        wkid: 102100
      },
      renderer: {
        type: 'webgl'
      }
    });
    view = new MapView({
      //这个是让地图显示在id为map_contentView的标签上，是id叫他不是其他的
      container: gisMapRef.value,
      map: map,
      center: [116.39126, 39.90763],
      zoom: 6,
      ui: {
        // components: ["zoom", "compass"]
        components: []
      },
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    //设置最大缩放等级
    view.constraints = {
      minZoom: 2,
      maxZoom: 24
    };

    const compassWidget = new Compass({ view: view });
    view.ui.add(compassWidget, 'bottom-right');
    const zoom = new Zoom({
      view: view
    });
    view.ui.add(zoom, 'bottom-right');
    view.ui.remove('attribution');
    const scaleBar = new ScaleBar({
      view: view,
      unit: 'metric',
      style: 'line'
    });
    // Add widget to the bottom left corner of the view
    view.ui.add(scaleBar, {
      position: 'bottom-right'
    });
    const that = this;
    //添加文字标注layer到map
    // fontLayer = new GraphicsLayer({
    //   id: '234',
    //   visible: false
    // });
    // map.add(fontLayer, 9999);
    //添加文字标注layer到map
    labelLayer = new GraphicsLayer({
      id: 'labelLayer',
      minScale: 1000000
    });
    map.add(labelLayer, 9999);
    //添加宗地图形layer到map
    graphicsLayerAll = new GraphicsLayer({
      id: '123',
      minScale: 10000000
    });
    map.add(graphicsLayerAll);
    //子要素layer
    childGraphics = new GraphicsLayer({
      id: 'childEle',
      minScale: 11000000
    });
    map.add(childGraphics);
    degGraphics = new GraphicsLayer({
      id: 'degEle'
    });
    map.add(degGraphics);
    jzdzbLayer = new GraphicsLayer({
      id: 'jzd'
    });
    map.add(jzdzbLayer);
    // 这里需要区分一下 如果是地址有parcelId的就单独请求这一条数据
    if (route.query.parcelId) {
      emit('checkedOneParcelId', route.query.parcelId);
    } else {
      //地图初始化完成后再调用父组件获取所有林业信息  重要
      emit('getLinyeData'); //true 表示第一次请求
      getShpTree(true);
    }
    //先初始化
    nowRuleIds.value = [];
    // 初始化勾选所有规则
    defaultCheckRules.value = [];
    setDefaultCheckRules(props.ruleTree);
    traverseTree(props.ruleTree);
    // 处理节流防抖
    view.watch('extent', handleExtentChange);
    view.on('click', function (event) {
      //打印点击的图形
      view.hitTest(event).then((res) => {
        if (res.results.length != 0) {
          // 移除 && !clusterConfig.enabled 条件
          const graphic = res.results[0].graphic;
          const id = graphic.id;
          emit('changeNowCheckedZD', id);
          // 清除之前的高亮
          if (oldHighlight.value) {
            oldHighlight.value.remove();
          }
          // 设置新的高亮
          view.whenLayerView(graphic.layer).then(function (layerView) {
            oldHighlight.value = layerView.highlight(graphic);
          });
          // 将视图中心移动到点击的图形
          if (graphic.geometry) {
            view.goTo({
              target: graphic.geometry,
              zoom: view.zoom // 保持当前缩放级别
            });
          }
        } else {
          executeIdentify(event);
        }
      });
    });
    view.watch('extent', function (evt) {
      if (showCQPlan.value) {
        //代表有拆迁进度信息
        updateCQPlan();
      }
    });
    // 监听地图静止状态（移动/缩放结束）
    view.watch('stationary', handleMapMoveEnd);
  });
};
// 监听 currentPercent
watch(
  currentPercent,
  (val: any) => {
    const percent = val.toFixed(2);
    let planNum = 0;
    const size = (fileSize.value / 1024 / 1024).toFixed(2);
    if (progressEnd.value && progressEnd.value.loaded) {
      planNum = Number(((progressEnd.value.loaded / fileSize.value) * 100).toFixed(2));
    }
  },
  { deep: true }
);

/**
 * 处理地图静止状态（移动/缩放结束）
 * @param event 是否移动/缩放结束
 */
const handleMapMoveEnd = (event: any) => {
  if (event) {
    const level = view.zoom;
    view.when(() => {
      const geoWkt = extentToWKB(view.extent);
      //记录最近一次视窗图形
      lastGeoWkt.value = geoWkt;
      // 根据缩放级别动态调整聚合
      clusterConfig.distance = calculateClusterDistance(level);
      if (clusterConfig.enabled) {
        handleClusteringChange();
      }
      if (level >= 11) {
        const mainIds = [];
        // mainIds 需要特殊处理下图层要素问题
        if (props.parcelList.length > 0) {
          // mainIds = props.parcelList.map((item) => item.id);
          props.parcelList.forEach((v) => {
            if (v.container === 0) {
              mainIds.push(v.id);
            } else {
              //图层要素需要循环要素具体内容
              v.children.forEach((k: any) => {
                mainIds.push(k.id);
              });
            }
          });
        }
        const checkedRules = ruleTreeRef.value.getCheckedNodes();
        const ruleIds = [];
        checkedRules.forEach((item) => {
          ruleIds.push(item.id);
        });
        // if (ruleIds.length == 0) {
        //   ElMessage.error('请至少勾选一个节点');
        //   return;
        // }
        const params = {
          ruleIds: ruleIds,
          wkb: geoWkt,
          pageNum: 1,
          pageSize: 10000,
          mainIds: mainIds,
          linkIds: []
        };
        // const linkIds = [];
        // projectStore.nodeStyles.forEach((item) => {
        //   if (item.style.pointColorDynamic) {
        //     linkIds.push(item.style.pointColorDynamic.linkId);
        //   }
        //   if (item.style.polygonFillColorDynamic) {
        //     linkIds.push(item.style.polygonFillColorDynamic.linkId);
        //   }
        //   if (item.style.polylineColorDynamic) {
        //     linkIds.push(item.style.polylineColorDynamic.linkId);
        //   }
        // });
        // if (linkIds.length > 0) {
        //   params.linkIds = linkIds;
        // }
        selectParcelClusterList(params).then((res) => {
          if (res.code == 200) {
            if (res.data.list) {
              drawChildNodeToSub(res.data.list);
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      }
    });
  }
};
/**
 * 返回树结构的所有ids
 * @param nodes 树结构
 */
const traverseTree = (nodes: any[]) => {
  nodes.forEach((node) => {
    if (node.id) {
      nowRuleIds.value.push(node.id);
    }
    if (node.list?.length) {
      traverseTree(node.list);
    }
  });
};

/**
 * 转换Extent为WKB多边形
 * @param extent 范围
 * @returns 转换后的WKB多边形
 */
const extentToWKB = (extent: any) => {
  const xmin = extent.xmin;
  const ymin = extent.ymin;
  const xmax = extent.xmax;
  const ymax = extent.ymax;
  // 生成多边形坐标环（闭合矩形）
  const coordinates = [
    [xmin, ymin],
    [xmax, ymin],
    [xmax, ymax],
    [xmin, ymax],
    [xmin, ymin] // 闭合多边形
  ];
  const geometry = {
    type: 'Polygon',
    coordinates: [coordinates]
  };
  return `SRID=3857;${geojsonToWKT(geometry)}`;
};

/**
 * 更新拆迁进度浮窗
 */
const updateCQPlan = () => {
  const screenPoint = view.toScreen(nowPoint.value);
  cqplanleft.value = screenPoint.x - 60;
  cqplantop.value = screenPoint.y - 40;
};

/**
 * 清除拆迁进度浮窗
 */
const clearCQLabel = () => {
  showCQPlan.value = false;
  msgList.value = [];
};
/**
 * 取消高亮 如果没有图形的时候
 */
const initLableCQ = () => {
  if (oldHighlight.value) {
    oldHighlight.value.remove();
  }
  // 如果有拆迁房标注 需要同时去掉
  showCQPlan.value = false;
  msgList.value = [];
};

/**
 * 绘制宗地
 * @param isInitCenter 是否初始化中心
 */
const drawZD = (isInitCenter: any) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    graphicsLayerAll.removeAll();
    let oldIndex = -1;
    if (props.nowCheckedZD) {
      // 当前要素列表是否存在之前选中的id
      oldIndex = allZD.value.findIndex((item: any) => item.id == props.nowCheckedZD);
    }

    // 存储原始图形数据用于聚合
    const currentGraphics: any[] = [];
    allZD.value.forEach((v: any, idx: any) => {
      if (v.geomArcgis) {
        let bgColor = null;
        const polylineColor = getOneStyle(v.ruleId, 'polylineColor') || '';
        if (polylineColor.includes('rgba')) {
          bgColor = polylineColor;
        } else {
          bgColor = hexToRgba(polylineColor, '0.2');
          if (getOneStyle(v.ruleId, 'polygonFillColor')) {
            bgColor = hexToRgba(getOneStyle(v.ruleId, 'polygonFillColor'));
          }
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));

        if (v.graphicalType == 1) {
          //点
          //创建文本符号
          let textSymbol = new TextSymbol();

          textSymbol = {
            type: 'simple-marker', // autocasts as SimpleLineSymbol()
            color: getOneStyle(v.ruleId, 'pointColor'),
            width: getOneStyle(v.ruleId, 'pointSize') || 2
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.longitude, polygon.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(graphic);
          currentGraphics.push(graphic);
        } else if (v.graphicalType == 2) {
          //线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: getOneStyle(v.ruleId, 'polylineColor'), // RGB color values as an array
            width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
            style: getOneStyle(v.ruleId, 'polylineType')
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(polygonGraphic);
          currentGraphics.push(polygonGraphic);
        } else if (v.graphicalType == 3) {
          //面
          const simpleFillSymbol = {
            type: 'simple-fill',
            style: camelToKebab(getOneStyle(ruleId, 'polygonType')),
            outline: {
              color: getOneStyle(v.ruleId, 'polylineColor'),
              width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
              style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          if (props.nowCheckedZD && oldIndex != -1) {
            //选中了数据，定位到数据的位置 并且在当前绘制的宗地存在选中的宗地id
            if (v.id == props.nowCheckedZD) {
              view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
              view.zoom = 18;
            }
          } else {
            //代表没有选中数据 默认定位第一个
            if (idx == 0 && isInitCenter) {
              view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
              view.zoom = 18;
              emit('noInitCenter');
            }
          }
          graphicsLayerAll.add(polygonGraphic);
          currentGraphics.push(polygonGraphic);
        }
      }
    });

    // 保存当前图形数据用于聚合
    originalGraphicsData = currentGraphics;

    // 如果有图形数据，应用聚合
    if (currentGraphics.length > 0) {
      // 强制应用聚合
      if (!clusterLayer) {
        handleClusteringChange();
      }
    }
  });
};

/**
 * 高亮当前选中宗地
 */
const highlightZD = () => {
  const ruleId = props.currentRuleId || currentSelectZd.value?.ruleId;
  // 获取并打印当前高亮图层的样式
  if (ruleId) {
    const fillColor = getOneStyle(ruleId, 'polygonFillColor');
    graphicColor.value = fillColor;
  }
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol',
      'esri/geometry/Extent'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol, Extent]) => {
    //先清除之前的文字标注
    if (fontLayer) {
      map.remove(fontLayer);
      fontLayer = null; // 清空引用避免内存泄漏
    }
    nowHighlightId.value = props.nowCheckedZD;
    //找到所有宗地图形的layer
    const layer = map.findLayerById('123');
    layer.graphics.items.forEach((v) => {
      if (v.id == props.nowCheckedZD) {
        // getzhijieList(v.geometry);
        // 获取图形的范围
        const geometry = v.geometry;
        if (geometry && geometry.extent) {
          // 计算padding（扩展范围的10%）
          const xPadding = (geometry.extent.xmax - geometry.extent.xmin) * 0.1;
          const yPadding = (geometry.extent.ymax - geometry.extent.ymin) * 0.1;
          // 创建新的扩展范围
          const expandedExtent = new Extent({
            xmin: geometry.extent.xmin - xPadding,
            ymin: geometry.extent.ymin - yPadding,
            xmax: geometry.extent.xmax + xPadding,
            ymax: geometry.extent.ymax + yPadding,
            spatialReference: geometry.spatialReference
          });
          // 使用 goTo 方法调整视图
          view.goTo(
            {
              target: expandedExtent
            },
            {
              duration: 1000, // 动画持续时间（毫秒）
              easing: 'ease-out' // 动画效果
            }
          );
        }
        view.whenLayerView(v.layer).then(function (layerView) {
          oldHighlight.value = layerView.highlight(v);
        });
        if (oldHighlight.value) {
          oldHighlight.value.remove(); //取消上一个高亮宗地
        }
      }
    });
  });
};

/**
 * 高亮图形的界址点坐标显示 自己用
 */
const hightJZD = (geometry) => {
  jzdzbLayer.removeAll();
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol',
      'esri/geometry/geometryEngine',
      'esri/geometry/Polygon'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol, geometryEngine, Polygon]) => {
    geometry.rings.forEach((v) => {
      v.forEach((k) => {
        //创建文本符号
        let textSymbol = new TextSymbol();
        //文本的内容的实在
        textSymbol = {
          type: 'text',
          text: `X:${k[0]},Y:${k[1]}`,
          color: [255, 0, 0],
          haloSize: '1px',
          xoffset: 0,
          yoffset: 0
        };
        const point = new Point(k[0], k[1], new SpatialReference({ wkid: 102100 }));
        const graphic = new Graphic({ geometry: point, symbol: textSymbol });
        jzdzbLayer.add(graphic);
      });
    });
  });
};

/**
 * 组合界址点位置 按照J1在最左上方为准
 */
const getzhijieList = (geometry: any) => {
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol',
      'esri/geometry/geometryEngine',
      'esri/geometry/Polygon',
      'esri/layers/FeatureLayer',
      'esri/layers/support/LabelClass'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol, geometryEngine, Polygon, FeatureLayer, LabelClass]) => {
    if (geometry.rings) {
      let beginNum = 1; //起始为J1 用于处理孤岛
      geometry.rings.forEach((v, vdx) => {
        const polygon = new Polygon({
          type: 'polygon',
          rings: v,
          spatialReference: geometry.spatialReference
        });
        const ymax = polygon.extent.ymax;
        const xmin = polygon.extent.xmin;
        polygon.rings[0].pop();
        const area = geometryEngine.geodesicArea(polygon, 'square-meters');
        // 如果得到面积是负数为反着画，如果是正数是正着画
        const distanceList = [];
        polygon.rings[0].forEach((v) => {
          const distance = Math.sqrt(Math.pow(Math.abs(xmin - v[0]), 2) + Math.pow(Math.abs(ymax - v[1]), 2));
          distanceList.push(distance);
        });
        const min = Math.min(...distanceList);
        const index = distanceList.indexOf(min);
        let endList = []; //最终的数组
        if (index == 0) {
          //即第一个点就是J1，不用分割数组
          if (area > 0) {
            //顺时针
            endList = polygon.rings[0];
          } else {
            //逆时针
            endList.push(polygon.rings[0][0]);
            const tem = JSON.parse(JSON.stringify(polygon.rings[0])).reverse();
            tem.forEach((v, idx) => {
              if (idx != tem.length - 1) {
                endList.push(v);
              }
            });
          }
        } else if (index == distanceList.length - 1) {
          //最后一个点是J1，也不用分割，直接倒序
          if (area > 0) {
            //顺时针
            endList.push(polygon.rings[0][index]);
            polygon.rings[0].forEach((v, idx) => {
              if (idx != index) {
                endList.push(v);
              }
            });
          } else {
            //逆时针
            endList = JSON.parse(JSON.stringify(polygon.rings[0])).reverse();
          }
        } else {
          //需要分割数组
          if (area > 0) {
            //顺时针
            const left = [];
            const right = [];
            polygon.rings[0].forEach((v, idx) => {
              if (idx < index) {
                //左侧
                left.push(v);
              } else {
                right.push(v);
              }
            });
            endList = right.concat(left);
          } else {
            //逆时针
            let left = [];
            let right = [];
            polygon.rings[0].forEach((v, idx) => {
              if (idx <= index) {
                //左侧
                left.push(v);
              } else {
                right.push(v);
              }
            });
            left = left.reverse();
            right = right.reverse();
            endList = left.concat(right);
          }
        }
        const ite_features = [];
        //新版 解决压盖问题
        endList.forEach((k, kdx) => {
          const point = new Point(k[0], k[1], new SpatialReference({ wkid: 102100 }));
          ite_features.push({
            geomArcgis: point,
            id: kdx,
            labelText: beginNum
          });
          beginNum++;
        });
        // 移出原有的界址点标注
        // fontLayer.removeAll();
        // 创建要素集合
        const features = ite_features.map((q) => ({
          geometry: q.geomArcgis,
          attributes: {
            objectId: q.id,
            labelText: q.labelText
          }
        }));

        // 创建标注图层
        if (!fontLayer) {
          fontLayer = new FeatureLayer({
            source: features,
            fields: [
              { name: 'objectId', type: 'oid' },
              { name: 'labelText', type: 'string' }
            ],
            objectIdField: 'objectId',
            labelingInfo: [
              new LabelClass({
                labelExpressionInfo: { expression: '$feature.labelText' },
                symbol: new TextSymbol({
                  color: [255, 0, 0],
                  font: { size: 14, weight: 'bold' },
                  haloSize: '1px',
                  yoffset: 5,
                  xoffset: 5
                }),
                // 配置冲突解决策略
                deconflictionStrategy: 'dynamic', // 动态冲突检测
                labelPlacement: 'center-center',
                minScale: 10000, // 最小显示比例尺
                priority: 100 // 标注优先级
              })
            ],
            visible: false,
            minScale: 10000 // 最小显示比例尺
          });
          map.add(fontLayer);
        } else {
          // 更新要素
          fontLayer.applyEdits({ addFeatures: features });
        }
      });
    }
  });
};

/**
 * F11效果
 */
const zoomMap = () => {
  document.documentElement['webkitRequestFullscreen']();
};

/**
 * 切换底图
 * @param type 1:地图 2:影像图
 */
const changeMap = (type) => {
  if (type == 1) {
    //地图
    checkedMap.value = 'normal';
    tiledLayer.visible = false;
    tiledLayerAnno.visible = false;
    normalLayer.visible = true;
    normalAnno.visible = true;
  } else if (type == 2) {
    //影像图
    checkedMap.value = 'image';
    tiledLayer.visible = true;
    tiledLayerAnno.visible = true;
    normalLayer.visible = false;
    normalAnno.visible = false;
  }
};
/**
 * 创建聚合图层
 */
const createClusterLayer = (graphics = originalGraphicsData) => {
  loadModules(
    [
      'esri/layers/FeatureLayer',
      'esri/layers/support/Field',
      'esri/renderers/ClassBreaksRenderer',
      'esri/symbols/SimpleMarkerSymbol',
      'esri/symbols/SimpleLineSymbol',
      'esri/symbols/SimpleFillSymbol',
      'esri/symbols/TextSymbol',
      'esri/layers/support/LabelClass'
    ],
    config
  ).then(([FeatureLayer, Field, ClassBreaksRenderer, SimpleMarkerSymbol, SimpleLineSymbol, SimpleFillSymbol, TextSymbol, LabelClass]) => {
    // 处理不同类型的要素聚合
    const clusteredFeatures = performClustering(graphics);
    // 创建要素集合
    const features = clusteredFeatures.map((cluster) => ({
      geometry: cluster.geometry,
      attributes: {
        ObjectID: cluster.id,
        cluster_count: cluster.count,
        cluster_type: cluster.type,
        cluster_id: cluster.id
      }
    }));

    // 定义字段
    const fields = [
      new Field({
        name: 'ObjectID',
        alias: 'ObjectID',
        type: 'oid'
      }),
      new Field({
        name: 'cluster_count',
        alias: '聚合数量',
        type: 'integer'
      }),
      new Field({
        name: 'cluster_type',
        alias: '聚合类型',
        type: 'string'
      }),
      new Field({
        name: 'cluster_id',
        alias: '聚合ID',
        type: 'string'
      })
    ];

    // 创建聚合图层
    clusterLayer = new FeatureLayer({
      source: features,
      fields: fields,
      objectIdField: 'ObjectID',
      renderer: createClusterRenderer(SimpleMarkerSymbol, SimpleLineSymbol, SimpleFillSymbol),
      labelingInfo: [createClusterLabels(TextSymbol, LabelClass)],
      popupTemplate: {
        title: '聚合信息',
        content: [
          {
            type: 'fields',
            fieldInfos: [
              {
                fieldName: 'cluster_count',
                label: '聚合数量'
              },
              {
                fieldName: 'cluster_type',
                label: '聚合类型'
              }
            ]
          }
        ]
      }
    });
    map.add(clusterLayer);
  });
};
/**
 * 执行聚合算法
 */
const performClustering = (graphics: any[]) => {
  const clusters: any[] = [];
  const processed = new Set();
  graphics.forEach((graphic, index) => {
    if (processed.has(graphic)) return;
    const cluster = {
      id: `cluster_${clusters.length}`,
      geometry: graphic.geometry,
      count: 1,
      type: getGeometryType(graphic.geometry),
      graphics: [graphic]
    };
    // 查找邻近要素
    for (let i = index + 1; i < graphics.length; i++) {
      const otherGraphic = graphics[i];
      if (processed.has(otherGraphic)) continue;

      if (shouldCluster(graphic, otherGraphic)) {
        cluster.graphics.push(otherGraphic);
        cluster.count++;
        processed.add(otherGraphic);
      }
    }
    // 只有当聚合数量大于最小值时才创建聚合
    if (cluster.count >= clusterConfig.minSize) {
      // 计算聚合中心点
      cluster.geometry = calculateClusterCenter(cluster.graphics);
      clusters.push(cluster);
    } else {
      // 单个要素不聚合，保持原样
      clusters.push({
        id: `single_${clusters.length}`,
        geometry: graphic.geometry,
        count: 1,
        type: getGeometryType(graphic.geometry),
        graphics: [graphic]
      });
    }
    processed.add(graphic);
  });

  return clusters;
};

/**
 * 判断两个要素是否应该聚合
 */
const shouldCluster = (graphic1: any, graphic2: any) => {
  const distance = calculateDistance(graphic1.geometry, graphic2.geometry);
  return distance <= clusterConfig.distance;
};

/**
 * 获取几何类型
 */

type GeometryType = 'point' | 'line' | 'polygon';

const getGeometryType = (geometry: any): GeometryType => {
  if (geometry.type === 'point') return 'point';
  if (geometry.type === 'polyline') return 'line';
  if (geometry.type === 'polygon') return 'polygon';
  return 'point'; // 默认返回point类型
};
/**
 * 计算两个几何体之间的距离
 */
const calculateDistance = (geom1: any, geom2: any) => {
  const center1 = getGeometryCenter(geom1);
  const center2 = getGeometryCenter(geom2);
  return Math.sqrt(Math.pow(center1.x - center2.x, 2) + Math.pow(center1.y - center2.y, 2));
};

/**
 * 获取几何体中心点
 */
const getGeometryCenter = (geometry: any) => {
  if (geometry.type === 'point') {
    return { x: geometry.x, y: geometry.y };
  } else if (geometry.centroid) {
    return { x: geometry.centroid.x, y: geometry.centroid.y };
  } else if (geometry.extent) {
    return { x: geometry.extent.center.x, y: geometry.extent.center.y };
  }
  return { x: 0, y: 0 };
};

/**
 * 计算聚合中心点
 */
const calculateClusterCenter = (graphics: any[]) => {
  let totalX = 0;
  let totalY = 0;

  graphics.forEach((graphic) => {
    const center = getGeometryCenter(graphic.geometry);
    totalX += center.x;
    totalY += center.y;
  });

  return {
    type: 'point',
    x: totalX / graphics.length,
    y: totalY / graphics.length,
    spatialReference: graphics[0].geometry.spatialReference
  };
};

/**
 * 创建聚合渲染器
 */
const createClusterRenderer = (SimpleMarkerSymbol: any, SimpleLineSymbol: any, SimpleFillSymbol: any) => {
  return {
    type: 'class-breaks',
    field: 'cluster_count',
    classBreakInfos: [
      {
        minValue: 5,
        maxValue: 5,
        symbol: new SimpleMarkerSymbol({
          style: 'circle',
          color: [51, 51, 204, 0.9],
          size: '50px',
          outline: {
            color: 'white',
            width: 3
          }
        })
      },
      {
        minValue: 1,
        maxValue: 10,
        symbol: new SimpleMarkerSymbol({
          style: 'circle',
          color: [51, 204, 51, 0.9],
          size: '50px',
          outline: {
            color: 'white',
            width: 2
          }
        })
      },
      {
        minValue: 11,
        maxValue: 50,
        symbol: new SimpleMarkerSymbol({
          style: 'circle',
          color: [204, 204, 51, 0.9],
          size: '70px',
          outline: {
            color: 'white',
            width: 2
          }
        })
      },
      {
        minValue: 51,
        maxValue: Infinity,
        symbol: new SimpleMarkerSymbol({
          style: 'circle',
          color: [204, 51, 51, 0.9],
          size: '80px',
          outline: {
            color: 'white',
            width: 3
          }
        })
      }
    ]
  };
};

/**
 * 创建聚合标签
 */
const createClusterLabels = (TextSymbol: any, LabelClass: any) => {
  return new LabelClass({
    labelExpressionInfo: {
      expression: '$feature.cluster_count'
    },
    symbol: new TextSymbol({
      color: 'white',
      font: {
        family: 'Arial',
        size: 14,
        weight: 'bold'
      },
      haloColor: 'rgba(0, 0, 0, 0.5)',
      haloSize: 1
    }),
    labelPlacement: 'center-center'
  });
};

/**
 * 迭代绘制所有有图形的子要素
 */
const drawChildEle = (list) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const currentChildGraphics = [];
    originalGraphicsData = [];
    // 清除现有的子要素图形
    childGraphics.removeAll();
    // 如果存在聚合图层，先移除
    if (clusterLayer) {
      map.remove(clusterLayer);
      clusterLayer = null;
    }
    list.forEach((v, idx) => {
      if (v.geomArcgis) {
        let bgColor = hexToRgba(getOneStyle(v.ruleId, 'polylineColor'), '0.2');
        if (getOneStyle(v.ruleId, 'polygonFillColor')) {
          bgColor = hexToRgba(getOneStyle(v.ruleId, 'polygonFillColor'));
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        let color = getOneStyle(v.ruleId, 'polylineColor');
        if (polygon.type == 'polygon') {
          //面
          // 面 并且是第五级levelNum=5 拆迁户专用颜色区分
          if (route.query.iscq && v.levelNum == 5) {
            // 判断拆迁进度
            for (let i = 0; i < v.fieldInstanceModels.length; i++) {
              if (v.fieldInstanceModels[i].groupName == '征迁进度') {
                switch (v.fieldInstanceModels[i].attribution.ZQJD) {
                  case '已完成调查':
                    bgColor = hexToRgba('#0199FF', '0.8');
                    color = '#0199FF';
                    break;
                  case '已完成确权':
                    bgColor = hexToRgba('#9B66FD', '0.8');
                    color = '#9B66FD';
                    break;
                  case '已完成签约':
                    bgColor = hexToRgba('#00FF80', '0.8');
                    color = '#00FF80';
                    break;
                  case '已完成拆除':
                    bgColor = hexToRgba('#FF8D02', '0.8');
                    color = '#FF8D02';
                    break;
                  case '已完成测绘':
                    bgColor = hexToRgba('#0002CC', '0.8');
                    color = '#0002CC';
                    break;
                  case '已完成评估':
                    bgColor = hexToRgba('#FEFF02', '0.8');
                    color = '#FEFF02';
                    break;
                  case '已完成腾空':
                    bgColor = hexToRgba('#FF01FD', '0.8');
                    color = '#FF01FD';
                    break;
                  default:
                    break;
                }
                break;
              }
            }
          }
          const simpleFillSymbol = {
            type: 'simple-fill',
            // color: [240,230,140, 0.8],  // Orange, opacity 80%
            style: camelToKebab(getOneStyle(ruleId, 'polygonType')) || 'solid',
            color: bgColor,
            outline: {
              // color: [255, 255, 255],
              color: color,
              width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
              style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          childGraphics.add(polygonGraphic);
          currentChildGraphics.push(polygonGraphic);
        } else if (polygon.type == 'polyline') {
          // 线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: getOneStyle(v.ruleId, 'polylineColor'), // RGB color values as an array
            width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
            style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          childGraphics.add(polygonGraphic);
          currentChildGraphics.push(polygonGraphic);
        } else if (polygon.type == 'point') {
          //点
          let pointColor = '#ff0000';
          if (getOneStyle(v.ruleId, 'pointColor')) {
            pointColor = getOneStyle(v.ruleId, 'pointColor');
          }
          //创建文本符号
          let textSymbol = new TextSymbol();
          //文本的内容的实在
          textSymbol = {
            type: 'text',
            text: v.parcelName,
            color: hexToRgba(pointColor),
            haloSize: '1px',
            xoffset: 0,
            yoffset: 0
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          childGraphics.add(graphic);
          currentChildGraphics.push(graphic);
        }
      }
      if (v.list.length != 0) {
        drawChildEle(v.list);
      }
    });
    // 如果有子要素数据，更新原始数据并重新应用聚合
    if (currentChildGraphics.length > 0) {
      if (childGraphics && childGraphics.graphics.length > 0) {
        originalGraphicsData = [...originalGraphicsData, ...childGraphics.graphics.toArray()];
        // 强制应用聚合
        if (!clusterLayer) {
          handleClusteringChange();
        }
      }
    }
  });
};
/**
 * 高亮某个图形
 * @param {*} id 图形id
 */
const endChangHighLight = (id: number) => {
  let heightGraphics = null;
  //代表是第一级 宗地
  for (let index = 0; index < graphicsLayerAll.graphics.items.length; index++) {
    if (graphicsLayerAll.graphics.items[index].id == id) {
      heightGraphics = graphicsLayerAll.graphics.items[index];
      break;
    }
  }
  if (heightGraphics) {
    getzhijieList(heightGraphics.geometry);
    // //如果地图缩放等级小于18强行设置为18
    // if (view.zoom < 18) {
    //   view.zoom = 18;
    // }
    if (heightGraphics.geometry.type == 'polygon') {
      if (heightGraphics.geometry.centroid) {
        //给中心点为当前选中宗地
        const nowCenter = [heightGraphics.geometry.centroid.longitude, heightGraphics.geometry.centroid.latitude];
        if (JSON.stringify(nowCenter) !== JSON.stringify(nowViewCenter.value)) {
          view.center = nowCenter;
          nowViewCenter.value = nowCenter;
        }
      }
    } else if (heightGraphics.geometry.type == 'polyline') {
      if (heightGraphics.geometry.extent.center) {
        const nowCenter = [heightGraphics.geometry.extent.center.longitude, heightGraphics.geometry.extent.center.latitude];
        if (JSON.stringify(nowCenter) !== JSON.stringify(nowViewCenter.value)) {
          view.center = nowCenter;
          nowViewCenter.value = nowCenter;
        }
      }
    } else if (heightGraphics.geometry.type == 'point') {
      if (heightGraphics.geometry) {
        const nowCenter = [heightGraphics.geometry.longitude, heightGraphics.geometry.latitude];
        if (JSON.stringify(nowCenter) !== JSON.stringify(nowViewCenter.value)) {
          view.center = nowCenter;
          nowViewCenter.value = nowCenter;
        }
      }
    }
  }

  // if (oldHighlight.value) {
  //   oldHighlight.value.remove(); //取消上一个高亮宗地
  // }
  if (heightGraphics) {
    view.whenLayerView(heightGraphics.layer).then(function (layerView) {
      oldHighlight.value = layerView.highlight(heightGraphics);
    });
  }
};

// 绘制方位角
const initDeg = (val, deg, src) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const point = {
      type: 'point',
      longitude: val[0],
      latitude: val[1]
    };
    const fillSymbol = {
      type: 'picture-marker', //"picture-fill",
      url: degImg, //"https://static.arcgis.com/images/Symbols/Shapes/BlackStarLargeB.png",
      // url: 'https://static.arcgis.com/images/Symbols/Shapes/BlackStarLargeB.png',
      width: '20px',
      height: '53px',
      outline: {
        style: 'solid'
      },
      angle: deg
    };

    const pointGraphic = new Graphic({
      geometry: point,
      symbol: fillSymbol,
      id: src
    });
    degGraphics.add(pointGraphic);
  });
};

/**
 * 清除图片方位角
 */
const clearDeg = () => {
  degGraphics.removeAll();
};

/**
 * 方位角 放大有方位角图片时候 高亮地图对应的要素 通过地址
 * @param {*} url 地址
 */
const heightSpeGrc = (url) => {
  for (let index = 0; index < degGraphics.graphics.items.length; index++) {
    if (degGraphics.graphics.items[index].id == url) {
      view.whenLayerView(degGraphics.graphics.items[index].layer).then(function (layerView) {
        oldSpeHightGrc.value = layerView.highlight(degGraphics.graphics.items[index]);
        view.center = [degGraphics.graphics.items[index].geometry.longitude, degGraphics.graphics.items[index].geometry.latitude];
        if (view.zoom < 18) {
          view.zoom = 18;
        }
      });
      if (oldSpeHightGrc.value) {
        oldSpeHightGrc.value.remove(); //取消上一个高亮宗地
      }
      break;
    }
  }
};

/**
 * 清除高亮的特殊方位角
 */
const clearSpeHightGrc = () => {
  if (oldSpeHightGrc.value) {
    oldSpeHightGrc.value.remove(); //取消上一个高亮宗地
  }
};
/**
 * 显示图层
 * @param {*} flg flg == true 的时候是默认请求图层列表 给图层要素赋值选中的 不需要显示图层信息 避免当是图层要素的时候没有点击图层就直接点击地图查看属性
 */
const showTc = (flg) => {
  if (layerList.value.length == 0) {
    //没有图层的时候请求图层
    getTCList().then((res) => {
      if (res.code == 200) {
        res.data.forEach((v) => {
          const index = v.mapName.indexOf(':');
          const title = v.mapName.substring(index + 1, v.length);
          const ite = {
            layerName: v,
            title: title
          };
          layerList.value.push(ite);
        });

        // 如果是图层要素 需要把要素绑定的图层默认选中
        if (props.defaultWMS) {
          for (let index = 0; index < layerList.value.length; index++) {
            if (layerList.value[index].layerName == props.defaultWMS) {
              layerList.value[index].checked = true;
              if (!checkList.value.includes(props.defaultWMS)) {
                checkList.value.push(layerList.value[index].layerName);
              }
              break;
            }
          }
        }
        if (!flg) {
          showChangeTC.value = !showChangeTC.value;
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    if (!flg) {
      showChangeTC.value = !showChangeTC.value;
    }
  }
};

const changeLayer = (val) => {
  map.layers.items.forEach((v) => {
    if (v.type == 'wms') {
      v.visible = false;
      nextTick(() => {
        map.remove(v);
      });
    }
  });
  val.forEach((v) => {
    const index = v.indexOf(':');
    const title = v.substring(index + 1, v.length);
    const url = `${base.value}${v.substring(0, index)}/${title}/wms`;
    initWMS(url, title);
  });
};

//加载wms图层
const initWMS = (url, title) => {
  loadModules(['esri/layers/WMSLayer'], config).then(([WMSLayer]) => {
    const oneLayer = new WMSLayer({
      url: url,
      visible: true,
      id: title,
      // WMSLayer 自定义参数，由于后台geoserver增加了权限
      customParameters: {
        token: getToken()
      }
    });
    map.add(oneLayer);
    // oneLayer.on('layerview-create', function (event) {
    //   // The LayerView for the layer that emitted this event
    //   view.center = [oneLayer.fullExtent.center.longitude, oneLayer.fullExtent.center.latitude];
    //   view.zoom = 15;
    // });
  });
};

const executeIdentify = async (event: any) => {
  try {
    const [projection] = await loadModules(['esri/geometry/projection'], config);

    const element = document.getElementById('layerMapRef');
    if (!element) return;

    const offsetWidth = element.offsetWidth;
    const offsetHeight = element.offsetHeight;

    // 设置属性框位置
    top.value = event.y + 'px';
    left.value = event.x + 'px';

    // 确保属性框不超出地图范围
    if (event.x + 300 >= offsetWidth) {
      // 超出宽度 移到左边
      left.value = event.x - 300 + 'px';
    }
    if (event.y + 400 >= offsetHeight) {
      // 超出高度 移到上面
      top.value = event.y - 400 + 'px';
    }

    await projection.load();

    let hasFeatures = false; // 标记是否找到要素

    // 遍历所有选中的图层
    for (let index = 0; index < nowLayerList.value.length; index++) {
      const BBOX = `${event.mapPoint.x},${event.mapPoint.y},${event.mapPoint.x + tolerance},${event.mapPoint.y + tolerance}`;
      const layerName = nowLayerList.value[index];
      const num = layerName.indexOf(':');
      const title = layerName.substring(num + 1, layerName.length);
      //
      const url = `${base.value}${layerName.substring(
        0,
        num
      )}/${title}/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=image%2Fpng&TRANSPARENT=true&QUERY_LAYERS=${layerName.substring(
        0,
        num
      )}%3A${title}&STYLES&LAYERS=${layerName.substring(
        0,
        num
      )}%3A${title}&exceptions=application%2Fvnd.ogc.se_inimage&INFO_FORMAT=application%2Fjson&FEATURE_COUNT=50&X=50&Y=50&SRS=EPSG%3A3857&WIDTH=101&HEIGHT=101&BBOX=${BBOX}&token=${getToken()}`;
      const res = await axios.get(url);
      if (res.data.features && res.data.features.length > 0) {
        hasFeatures = true;
        const featProps = res.data.features[0].properties;
        properties.value = [];

        // 将属性转换为数组格式
        Object.keys(featProps).forEach((key) => {
          properties.value.push({
            label: key,
            value: featProps[key]
          });
        });

        showAttr.value = true;
        break; // 找到要素后退出循环
      }
    }
    // 如果没有找到任何要素，关闭属性框
    if (!hasFeatures) {
      showAttr.value = false;
    }
  } catch (error) {
    showAttr.value = false; // 发生错误时也关闭属性框
  }
};

// 根据url获取属性
const getFeatures = (url) => {
  axios.get(url).then((res) => {
    if (res.data.features) {
      if (res.data.features.length != 0) {
        const propertiesTemp = res.data.features[0].properties;
        properties.value = [];
        const list = Object.keys(properties);
        list.forEach((v) => {
          properties.value.push({
            label: v,
            value: propertiesTemp[v]
          });
        });
        showAttr.value = true;
      }
    }
  });
};
/**
 * 居中某个图层
 * @param {*} item 图层
 */
const centerTc = (item) => {
  map.layers.items.forEach((v) => {
    if (v.type == 'wms' && v.id == item.title) {
      view.center = [v.fullExtent.center.longitude, v.fullExtent.center.latitude];
    }
  });
};

/**
 * 查看数据大屏
 */
const jumpScreen = async () => {
  const moduleId = projectStore.proModuleId;
  const searchMsg = {
    pageNo: 1,
    pageSize: 10000,
    moduleId: moduleId
  };
  if (moduleId) {
    getScreenList(searchMsg).then(async (res) => {
      if (res.code == 200) {
        if (res.data.records.length == 0) {
          //没有数据大屏的时候直接调用添加一个默认数据大屏
          const bigDataJson = JSON.parse(JSON.stringify(taskDefaultBigData));
          bigDataJson.moduleId = moduleId;
          const code = await selectModuleById();
          bigDataJson.components.forEach((v) => {
            if (v.cptOption.cptDataForm && v.cptOption.cptDataForm.dataSource == 2) {
              //是表达式的时候需要修改绑定的code
              v.cptOption.cptDataForm.code = code;
            }
            if (v.cptKey == 'cpt-select') {
              //任务组件单独处理
              v.cptOption.cptDataForm.moduleId = moduleId;
            }
          });
          bigDataJson.components = JSON.stringify(bigDataJson.components);
          bigDataJson.id = ''; //id置空
          bigDataJson.isDefault = 1; //默认模板
          saveScreen(bigDataJson).then((res) => {
            if (res.code == 200) {
              window.open(`/preview/${moduleId}/${res.data.id}`);
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
        if (res.data.records.length != 0) {
          const id = res.data.records[0].id;
          window.open(`/preview/${moduleId}/${id}`);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 图层要素的时候在列表选中某条数据 地图显示属性并高亮绘制
const drawWMSItem = (item) => {
  // 绘制前，需要把layer的图形全部移除
  graphicsLayerAll.removeAll();
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/geometry/Polygon'], config).then(([jsonUtils, Graphic, Polygon]) => {
    const bgColor = hexToRgba('#FF0000', '0.2');
    const polygon = new Polygon({
      rings: item.geometry.coordinates[0],
      spatialReference: { wkid: 102100 }
    });
    const simpleFillSymbol = {
      type: 'simple-fill',
      // color: [240,230,140, 0.8],  // Orange, opacity 80%
      style: camelToKebab(getOneStyle(ruleId, 'polygonType')) || 'solid',
      color: bgColor,
      outline: {
        // color: [255, 255, 255],
        color: '#FF0000',
        width: 1
      }
    };
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: simpleFillSymbol
    });
    graphicsLayerAll.add(polygonGraphic);
    view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
    const heightGraphics = graphicsLayerAll.graphics.items[0];
    if (oldHighlight.value) {
      oldHighlight.value.remove(); //取消上一个高亮图形
    }
    view.whenLayerView(heightGraphics.layer).then(function (layerView) {
      oldHighlight.value = layerView.highlight(heightGraphics);
    });
    // 显示当前选中图形的属性
    const properties = item.properties;
    properties.value = [];
    const list = Object.keys(properties);
    list.forEach((v) => {
      properties.value.push({
        label: v,
        value: properties[v]
      });
    });
    top.value = window.innerHeight / 2 - 40 + 'px';
    left.value = window.innerWidth / 2 - 100 + 'px';
    showAttr.value = true;
  });
};

const cesTC = () => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const geomArcgis =
      '{"rings":[[[[11745849.69162435,3185500.408744505],[11745849.575494783,3185500.388103385],[11745850.420166042,3185505.0027461196],[11745850.232074318,3185500.643178677],[11745849.69162435,3185500.408744505]]],[[[11745868.283269025,3185513.353540648],[11745868.246289019,3185512.3946984815],[11745852.10739604,3185514.220490393],[11745853.48643758,3185521.7545177117],[11745869.033422332,3185518.137901425],[11745868.283269025,3185513.353540648]]]],"spatialReference":{"latestWkid":3857,"wkid":102100}}';
    const polygon = jsonUtils.fromJSON(JSON.parse(geomArcgis));
    const simpleFillSymbol = {
      type: 'simple-fill',
      color: [240, 230, 140, 0.8], // Orange, opacity 80%
      // color:bgColor,
      outline: {
        color: [255, 255, 255],
        // color: v.styleAttribution.polylineColor,
        width: 1,
        style: 'solid'
      }
    };
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: simpleFillSymbol
    });
    view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
    view.zoom = 18;
    graphicsLayerAll.add(polygonGraphic);
  });
};

// 绘制标注
const drawLabel = (list) => {
  // 先移除之前的 数据
  // labelLayer.removeAll();
  loadModules(
    [
      'esri/layers/FeatureLayer',
      'esri/layers/support/LabelClass',
      'esri/geometry/support/jsonUtils',
      'esri/Graphic',
      'esri/symbols/TextSymbol',
      'esri/geometry/Point',
      'esri/geometry/SpatialReference'
    ],
    config
  ).then(([FeatureLayer, LabelClass, jsonUtils, Graphic, TextSymbol, Point, SpatialReference]) => {
    // 先清除之前的标注 如果有的话
    removerLabelLayer();
    // 创建要素集合 只需要图形的中点，不需要整个图形
    const item_features = [];
    list.forEach((v) => {
      const geomArcgis = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
      if (geomArcgis.type == 'polygon') {
        const point = new Point({
          x: geomArcgis.centroid.x,
          y: geomArcgis.centroid.y,
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });
        item_features.push({
          geometry: point,
          attributes: {
            objectId: v.id,
            labelText: v.linkIdValue
          }
        });
      } else if (geomArcgis.type == 'point') {
        const point = new Point({
          x: geomArcgis.x,
          y: geomArcgis.y,
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });
        item_features.push({
          geometry: point,
          attributes: {
            objectId: v.id,
            labelText: v.linkIdValue
          }
        });
      } else if (geomArcgis.type == 'polyline') {
        const point = new Point({
          x: geomArcgis.extent.center.x,
          y: geomArcgis.extent.center.y,
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });
        item_features.push({
          geometry: point,
          attributes: {
            objectId: v.id,
            labelText: v.linkIdValue
          }
        });
      }
    });
    // 创建标注图层
    if (!labelFeatureLayer) {
      labelFeatureLayer = new FeatureLayer({
        source: item_features,
        fields: [
          { name: 'objectId', type: 'oid' },
          { name: 'labelText', type: 'string' }
        ],
        objectIdField: 'objectId',
        labelingInfo: [
          new LabelClass({
            labelExpressionInfo: { expression: '$feature.labelText' },
            symbol: new TextSymbol({
              color: [255, 0, 0],
              font: { size: 14, weight: 'bold' },
              haloSize: '1px'
            }),
            // 配置冲突解决策略
            deconflictionStrategy: 'dynamic', // 动态冲突检测
            labelPlacement: 'center-center',
            minScale: 10000, // 最小显示比例尺
            priority: 100 // 标注优先级
          })
        ],
        minScale: 10000 // 最小显示比例尺
      });
      map.add(labelFeatureLayer);
    } else {
      // 更新要素
      labelFeatureLayer.applyEdits({ addFeatures: item_features });
    }

    // list.forEach((v, idx) => {
    //   if (v.geomArcgis) {
    //     const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
    //     //创建文本符号
    //     let textSymbol = new TextSymbol();
    //     //文本的内容的实在
    //     textSymbol = {
    //       type: 'text',
    //       text: v.linkIdValue,
    //       color: '#ffa700',
    //       haloSize: '1px',
    //       xoffset: 0,
    //       yoffset: 0,
    //       font: {
    //         size: 14, // 这里设置字体大小，单位通常是像素
    //         weight: 'bold'
    //       }
    //     };
    //     let point = null;
    //     if (polygon.type == 'polygon') {
    //       point = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
    //     } else if (polygon.type == 'point') {
    //       //点
    //       point = polygon;
    //     } else if (polygon.type == 'polyline') {
    //       //线
    //       point = new Point(polygon.extent.center.x, polygon.extent.center.y, new SpatialReference({ wkid: 102100 }));
    //     }
    //     const graphic = new Graphic({
    //       geometry: point,
    //       symbol: textSymbol
    //     });
    //     labelLayer.add(graphic);
    //   }
    // });
  });
};

/**
 * 当前高亮图形绘制拆迁高亮 一定是子要素图形
 * @param {*} msg
 * @param {*} id
 * @param {*} name
 * @param {*} isMainNode isMainNode 代表是根 根需要去graphicsLayer里面找
 */
const drawCQLabel = (msg, id, name, isMainNode) => {
  // 先移除之前的 数据
  loadModules(
    ['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol', 'esri/geometry/Point', 'esri/geometry/SpatialReference'],
    config
  ).then(([jsonUtils, Graphic, TextSymbol, Point, SpatialReference]) => {
    if (isMainNode) {
      for (let index = 0; index < graphicsLayerAll.graphics.items.length; index++) {
        if (graphicsLayerAll.graphics.items[index].id == id) {
          const polygon = graphicsLayerAll.graphics.items[index].geometry;
          nowPoint.value = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
          showCQPlan.value = true;
          msgMsg.value = msg;
          parcelNameZ.value = name;
          updateCQPlan();
          break;
        }
      }
    } else {
      for (let index = 0; index < childGraphics.graphics.items.length; index++) {
        if (childGraphics.graphics.items[index].id == id) {
          const polygon = childGraphics.graphics.items[index].geometry;
          nowPoint.value = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
          showCQPlan.value = true;
          msgMsg.value = msg;
          parcelNameZ.value = name;
          updateCQPlan();
          break;
        }
      }
    }
  });
};

const changeJZDVisbale = (flg) => {
  if (flg) {
    if (fontLayer) {
      //显示界址点标注
      fontLayer.visible = true;
    }
  } else {
    if (fontLayer) {
      fontLayer.visible = false;
    }
  }
};

// 获取模块信息
const selectModuleById = () => {
  return new Promise((resolve, reject) => {
    const moduleId = projectStore.proModuleId;
    selectModuleByIdApi({ id: moduleId }).then((res: any) => {
      if (res.code == 200) {
        resolve(res.data.code);
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

/**
 * 移除标注
 */
const removerLabelLayer = () => {
  // 移除标注图层
  if (labelFeatureLayer) {
    map.remove(labelFeatureLayer);
    labelFeatureLayer = null; // 清空引用避免内存泄漏
  }
};

/**
 * 新增的时候还是打开新的窗口做
 */
const addProject = () => {
  router.push(`/addProject/index/${projectStore.proModuleId}`);
};

/**
 * 返回新的command对象
 */
const beforeHandleCommand = (item) => {
  //index我这里是遍历的角标，即你需要传递的额外参数
  return item;
};

/**
 * 选中某个导出按钮
 */
const handleCommand = (data) => {
  if (data.id == -1) {
    //勘界专用 5.0移植过来的
    const flg = checkPermi(['qjt:kanjie:export']);
    if (!flg) {
      ElMessageBox.alert('您没有权限导出勘界，请联系管理员！！！', '提示', {
        confirmButtonText: '确定',
        callback: (action) => {}
      });
      return;
    }
    kjDialog.value = true;
  } else if (data.id == -101) {
    //默认导出
    initDownMsg();
    nowDownMsg.exportName = '默认导出';
    downLoadMsg.value.onWkid = ['CGCS_2000', 'cgcs2000_3', 35];
    downLoadMsg.value.areaType = 1;
    isDefault.value = true;
  } else {
    isDefault.value = false;
    getExportDetail(data.id).then((res) => {
      if (res.code == 200) {
        nowDownMsg = { ...res.data };
        // 先初始化树
        shpGdbTree = {
          shp: [],
          gdb: [],
          pic: [],
          word: [],
          excel: [],
          fileMap: []
        }; //导出的树结构迭代 整理出来shp和gdb列表
        downLoadFileName.value = nowDownMsg.detail.fileName;
        getShpGdb([nowDownMsg.detail]);
        initDownMsg();
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};
/**
 * 关闭勘界弹窗
 */
const closeKJDialog = () => {
  kjDialog.value = false;
};
const sumbitDownloadKJ = (parmas, flg) => {
  const moduleId = projectStore.proModuleId;
  parmas.moduleId = moduleId;
  if (flg) {
    parmas.del = 0;
  }
  exportPcNew(parmas, props.kjExportUrl).then((res) => {
    if (res.code == 200) {
      progress.value = res.data.progress;
      progressDialog.value = true;
      downStatus.value = '';
      publicDownDialoig.value = false;
      isContinue.value = true;
      downLoadFileName.value = `${parmas.xmmc}共${parmas.num}户`;
      findAsyncMsg(res.data.id);
      kjDialog.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 处理操作命令更新
 */
const handleCommandUpdate = async (command: any) => {
  if (command == 'UpdateShp') {
    handleUpdateShp();
  } else if (command == 'handleBZ') {
    // 标注
    labelDialog.value = true;
  } else if (command == 'handleBZRest') {
    // 取消标注
    localStorage.removeItem('labelMsg');
    // emit('removerLabelLayer'); // 这个需要根据实际情况处理
    removerLabelLayer();
  } else if (command == 'updateExcel') {
    const code = await getAllowUpdate();
    if (code < 0) {
      ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
        confirmButtonText: '确定',
        callback: (action: any) => {}
      });
      return;
    }
    uploadExcelDialog.value = true;
  } else if (command == 'updateTP') {
    updateTPFileDialog.value = true;
  } else if (command == 'updateZDFC') {
    updateZDFCDialog.value = true;
  } else if (command == 'handleRefreshExpress') {
    const code = await getAllowUpdate();
    if (code < 0) {
      ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
        confirmButtonText: '确定',
        callback: (action: any) => {}
      });
      return;
    }
    refreshExpressDialog.value = true;
  } else if (command == 'handleFlowTask') {
    flowTaskDailog.value = true;
  } else if (command == 'handleBitchUpdateField') {
    const code = await getAllowUpdate();
    if (code < 0) {
      ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
        confirmButtonText: '确定',
        callback: (action: any) => {}
      });
      return;
    }
    bitchUpdateFieldDialog.value = true;
  } else if (command == 'handleChangeJZD') {
    isShowJZD.value = !isShowJZD.value;
    if (isShowJZD.value) {
      changeJZDVisbale(true);
    } else {
      changeJZDVisbale(false);
    }
  }
};

/**
 * 检查是否允许更新
 * @returns code -1 表达式未排序 0 没有表达式 1 有表达式且已排序
 */
const getAllowUpdate = async () => {
  return new Promise((resolve, reject) => {
    const params = {
      moduleId: projectStore.proModuleId
    };
    selectIfOrder(params).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        resolve(-1);
      }
    });
  });
};

/**
 * 初始化下载信息
 */
// 初始化导出设置内容
const initDownMsg = () => {
  const downLoadMsgTemp = {
    zdList: [], //选中的宗地
    zdListNames: '', //选择的宗地中文名
    companyName: companyList.value[0].value,
    areaType: nowDownMsg.areaType,
    code: 'UTF-8',
    onWkid: undefined,
    ignore: false
  }; //导出设置
  if (showBoxType.value) {
    downLoadMsgTemp.boxType = 1;
  }
  const downLoadMsgRuleTemp: any = {
    zdList: [{ required: true, message: '请选择数据', trigger: 'change' }]
  }; //导出设置校验
  if (nowDownMsg.coordinate.disable == 0) {
    //坐标系
    if (nowDownMsg.coordinate.defaultValue) {
      downLoadMsgTemp.onWkid = nowDownMsg.coordinate.defaultValue;
    } else {
      downLoadMsgTemp.onWkid = '';
    }
    if (nowDownMsg.coordinate.must == 1) {
      //必填
      const rules = [{ required: true, message: '请选择坐标系', trigger: 'change' }];
      downLoadMsgRuleTemp.onWkid = rules;
    }
  }

  downLoadMsg.value = downLoadMsgTemp;
  downLoadMsgRule.value = downLoadMsgRuleTemp;
  isChecked.value = false;
  downloadType.value = null;
  isNoDel.value = false;
  publicDownDialoig.value = true;
};

/**
 * 获取导出设置
 */
const getExportSetting = () => {
  // 1. 判断公司权限
  const SPECIAL_COMPANY_IDS = [
    '9bd5578788ae4f8f9577b56ab8baf088', // 神马勘测
    '970e14222c2340dba1877be4ed81df51' // 神马测试环境
  ];

  if (SPECIAL_COMPANY_IDS.includes(userStore.user?.companyId)) {
    showBoxType.value = true;
  }
  const params: any = {
    moduleId: projectStore.proModuleId
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  exportSettingList(params).then((res) => {
    if (res.code == 200) {
      if (res.data.length != 0) {
        exportBtn.value = res.data;
        exportBtn.value.unshift({ exportName: '系统默认导出', id: -101 });
      } else {
        exportBtn.value.push({ exportName: '系统默认导出', id: -101 });
      }
      if (props.toolType === 1) {
        //特殊判断勘界类型
        if (res.data.length == 0) {
          exportBtn.value = [];
          exportBtn.value.unshift({ exportName: '系统默认导出', id: -101 });
        }
        exportBtn.value.push({ exportName: '勘界导出', id: -1 });
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取公司列表
 */
const getCompany = () => {
  getCompanyApi().then((res) => {
    if (res.code == 200) {
      companyData.value = res.data;
      companyList.value = [];
      companyList.value.push({
        label: `${res.data.companyName}(主公司)`,
        value: res.data.companyName
      });
      res.data.sysCompanyChildList.forEach((v) => {
        companyList.value.push({
          label: `${v.companyName}(关联公司)`,
          value: v.companyName
        });
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 关闭excel更新
 */
const closeExcelUpload = () => {
  uploadExcelDialog.value = false;
};

/**
 * 关闭固定excel 更新弹窗
 */
const closeSpeUpload = () => {
  updateSpeDialog.value = false;
};

/**
 * 关闭shp更新子要素弹窗
 */
const closeShpChild = () => {
  updateShpChildDialog.value = false;
};

/**
 * 关闭批量更新图片
 */
const closeUpdateTP = () => {
  updateTPFileDialog.value = false;
};

/**
 * 关闭批量更新宗地和房产图
 */
const closeUpdateZDFC = () => {
  updateZDFCDialog.value = false;
};

/**
 * 关闭刷新表达式
 */
const closeRefreshExpress = () => {
  refreshExpressDialog.value = false;
};

/**
 * 关闭流程任务数据解锁
 */
const closeFlowTask = () => {
  flowTaskDailog.value = false;
};

/**
 * 关闭批量更新字段
 */
const handleCloseBitchUpdateFieldDialog = () => {
  bitchUpdateFieldDialog.value = false;
};

/**
 * 导入或者更新shp 打开弹窗
 */
const handleUpdateShp = async () => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  shpUploadDialog.value = true;
};

/**
 * 关闭shp更新弹窗
 */
const handleCloseShpDialog = () => {
  shpUploadDialog.value = false;
};

/**
 * 关闭标注弹窗
 */
const handleCloseLabel = () => {
  labelDialog.value = false;
};
/**
 * 关闭公共导出弹窗
 */
const handleClose = () => {
  downLoadMsg.value = {
    zdList: []
  };
  publicDownDialoig.value = false;
  clearSelection();
};
/**
 * 提交标注字段
 */
const submitField = (fieldName: string, linkId: string) => {
  labelDialog.value = false;
  emit('drawLabel', fieldName, linkId); // 这个需要根据实际情况处理
};

/**
 * 真正导出
 */
const submitDown = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.error('请选择数据');
    return;
  }
  downLoadRef.value.validate((valid) => {
    if (valid) {
      const wkId = 3857;
      const parmas: any = {
        exportId: nowDownMsg.id,
        moduleId: nowDownMsg.moduleId,
        wkId: wkId,
        areaType: downLoadMsg.value.areaType,
        code: downLoadMsg.value.code,
        boxType: downLoadMsg.value.boxType,
        ignore: downLoadMsg.value.ignore
      };
      if (nowDownMsg.coordinate.disable == 1) {
        //坐标系统不显示的时候需要主动去取默认值
        if (nowDownMsg.coordinate.defaultValue[2] < 25) {
          //六度带
          parmas.wkId = 4478 + nowDownMsg.coordinate.defaultValue[2];
        } else if (nowDownMsg.coordinate.defaultValue[2] >= 25) {
          //三度带
          parmas.wkId = 4513 + nowDownMsg.coordinate.defaultValue[2] - 25;
        }
      }
      if (downLoadMsg.value.onWkid) {
        if (downLoadMsg.value.onWkid[2] < 25) {
          //六度带
          parmas.wkId = 4478 + downLoadMsg.value.onWkid[2];
        } else if (downLoadMsg.value.onWkid[2] >= 25) {
          //三度带
          parmas.wkId = 4513 + downLoadMsg.value.onWkid[2] - 25;
        }
      }
      const zdId = [];
      selectedRows.value.forEach((v) => {
        zdId.push(v.id);
      });
      if (!isDefault.value) {
        //默认导出不需要进入
        const gdbMap = [];
        const shpMap = [];
        const picMap = [];
        const wordMap = [];
        const excelMap = [];
        const fileMap = [];
        shpGdbTree.shp.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          shpMap.push(obj);
        });
        shpGdbTree.gdb.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          gdbMap.push(obj);
        });
        shpGdbTree.pic.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          picMap.push(obj);
        });
        shpGdbTree.word.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          wordMap.push(obj);
        });
        shpGdbTree.excel.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          excelMap.push(obj);
        });
        shpGdbTree.fileMap.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          fileMap.push(obj);
        });
        parmas.gdbMap = gdbMap;
        parmas.shpMap = shpMap;
        parmas.picMap = picMap;
        parmas.wordMap = wordMap;
        parmas.excelMap = excelMap;
        parmas.fileMap = fileMap;
        parmas.companyName = downLoadMsg.value.companyName;
      }
      parmas.zdId = zdId;
      downLoadMsg.value = {
        zdList: [], //选中的宗地
        zdListNames: '', //选择的宗地中文名
        onWkid: ''
      };
      if (isChecked.value) {
        if (isDefault.value) {
          //默认导出
          const moduleId = projectStore.proModuleId;
          const newParmas = {
            zdId: zdId,
            defaultExport: true,
            moduleId: moduleId,
            wkId: parmas.wkId
          };
          const loading = ElLoading.service({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          downLoadPublicCheck(newParmas).then((res) => {
            loading.close();
            if (res.code == 200) {
              ElNotification({
                title: '错误提示',
                message: res.data,
                duration: 0
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else {
          const loading = ElLoading.service({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          downLoadPublicCheck(parmas).then((res) => {
            loading.close();
            if (res.code == 200) {
              ElNotification({
                title: '错误提示',
                message: res.data,
                duration: 0
              });
              clearSelection();
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
      } else {
        if (isDefault.value) {
          //默认导出
          const moduleId = projectStore.proModuleId;
          const newParmas: any = {
            zdId: zdId,
            defaultExport: true,
            moduleId: moduleId,
            wkId: parmas.wkId,
            code: parmas.code
          };
          if (isNoDel.value) {
            newParmas.del = 0;
          }
          if (isBatch.value && newParmas.zdId.length > batchNum.value) {
            //是需要分批导出 并且数量大于分批量
            // 先把导出的弹窗关了
            // 然后计算出来一共多少批次 然后开始每一批次进度
            publicDownDialoig.value = false;
            showBatch.value = true;
            batchProgress.value = 0; //批量导出进度
            batchMsg.value = ''; //批量导出信息
            oneceProgress.value = 0; //单次导出进度
            progressDialog.value = true;
            batchDown(newParmas);
          } else {
            const loading = ElLoading.service({
              lock: true,
              text: '加载中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            downLoadPublic(newParmas).then((res) => {
              loading.close();
              if (res.code == 200) {
                progress.value = res.data.progress;
                showBatch.value = false; //是否显示批量
                progressDialog.value = true;
                downStatus.value = '';
                publicDownDialoig.value = false;
                isContinue.value = true;
                findAsyncMsg(res.data.id);
                clearSelection();
              } else {
                ElMessage.error(res.msg);
              }
            });
          }
        } else {
          if (isNoDel.value) {
            parmas.del = 0;
          }
          if (isBatch.value && parmas.zdId.length > batchNum.value) {
            //是需要分批导出 并且数量大于分批量
            // 先把导出的弹窗关了
            // 然后计算出来一共多少批次 然后开始每一批次进度
            publicDownDialoig.value = false;
            showBatch.value = true;
            batchProgress.value = 0; //批量导出进度
            batchMsg.value = ''; //批量导出信息
            oneceProgress.value = 0; //单次导出进度
            progressDialog.value = true;
            batchDown(parmas);
          } else {
            const loading = ElLoading.service({
              lock: true,
              text: '加载中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            downLoadPublic(parmas).then((res) => {
              loading.close();
              if (res.code == 200) {
                progress.value = res.data.progress;
                showBatch.value = false; //是否显示批量
                progressDialog.value = true;
                downStatus.value = '';
                publicDownDialoig.value = false;
                isContinue.value = true;
                findAsyncMsg(res.data.id);
                clearSelection();
              } else {
                ElMessage.error(res.msg);
              }
            });
          }
        }
      }
    } else {
      return false;
    }
  });
};
/**
 * 管理数据
 */
const showManager = () => {
  managerDialog.value = true;
  ifTree.value = true;
  searchDialog.value = true;
};

/**
 * 查询异步消息
 */
const findAsyncMsg = (id, fileName?: string) => {
  if (isContinue.value) {
    const parmas = {
      id: id
    };
    findAsyncMsgApi(parmas).then((res) => {
      if (res.code == 200) {
        if (!res.data) {
          ElMessageBox.alert(res.msg, {
            confirmButtonText: '确定',
            callback: (action: any) => {
              progressDialog.value = false;
            }
          });
          return;
        }
        downMsg.value = res.data.remark;
        if (parseFloat(res.data.progress) != 1) {
          progress.value = Number((res.data.progress * 100).toFixed(2));
          if (res.data.status == -1) {
            ElMessage.error(res.data.result);
            downStatus.value = 'exception';
            progressDialog.value = false;
          } else {
            setTimeout(() => {
              findAsyncMsg(id, fileName);
            }, 1000);
          }
        } else if (parseFloat(res.data.progress) == 1) {
          downStatus.value = 'success';
          if (res.data.path) {
            downLoadFileName.value = res.data.path;
          }
          progress.value = Number((res.data.progress * 100).toFixed(2));
          downLoadId.value = id;
          fileSize.value = res.data.size;
          generalDown(fileName);
          setTimeout(() => {
            progressDialog.value = false;
          }, 1000);
        }
      } else {
        ElMessage.error(res.msg);
        progressDialog.value = false;
      }
    });
  }
};

/**
 * 获取拆迁进度颜色
 * @param {*} item
 * @returns
 */
const getCQPlanClass = (item) => {
  let obj = {};
  switch (item.type) {
    case 1:
      obj = { 'color1': true };
      break;
    case 2:
      obj = { 'color2': true };
      break;
    case 3:
      obj = { 'color3': true };
      break;
    case 4:
      obj = { 'color4': true };
      break;
    case 5:
      obj = { 'color5': true };
      break;
    case 6:
      obj = { 'color6': true };
      break;
    case 7:
      obj = { 'color7': true };
      break;
    default:
      break;
  }
  return obj;
};

const closeTL = () => {
  showTL.value = false;
};

/**
 * 重新绘制  根据需要绘制的内容重新绘制图形
 * @param {*} parcelList
 * @param {*} showGraphs
 */
const reloadGraphs = (parcelList, showGraphs) => {
  loadModules(['esri/layers/GraphicsLayer'], config).then(([GraphicsLayer]) => {
    // 未知原因 无法直接调用removeAll()清除图形
    map.remove(graphicsLayerAll);
    //添加宗地图形layer到map
    graphicsLayerAll = new GraphicsLayer({
      id: '123'
    });
    map.add(graphicsLayerAll);
    if (showGraphs.length != 0) {
      reloadGraphsItem(parcelList, showGraphs);
    }
  });
};
/**
 * 重新绘制  根据需要绘制的内容重新绘制图形
 * @param {*} parcelList
 * @param {*} showGraphs
 */
const reloadGraphsItem = (parcelList, showGraphs) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    parcelList.forEach((v, idx) => {
      if (v.geomArcgis && showGraphs.includes(v.ruleId)) {
        let bgColor = null;
        if (getOneStyle(v.ruleId, 'polylineColor').includes('rgba')) {
          bgColor = getOneStyle(v.ruleId, 'polylineColor');
        } else {
          bgColor = hexToRgba(getOneStyle(v.ruleId, 'polylineColor'), '0.2');
          if (getOneStyle(v.ruleId, 'polygonFillColor')) {
            bgColor = hexToRgba(getOneStyle(v.ruleId, 'polygonFillColor'));
          }
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        if (v.graphicalType == 1) {
          //点
          //创建文本符号
          let textSymbol = new TextSymbol();

          textSymbol = {
            type: 'simple-marker', // autocasts as SimpleLineSymbol()
            style: camelToKebab(getOneStyle(v.ruleId, 'pointType')) || 'circle',
            color: getOneStyle(v.ruleId, 'pointColor'),
            width: getOneStyle(v.ruleId, 'pointSize') || 2
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.longitude, polygon.latitude];
            // view.zoom = 18;
          }
          graphicsLayerAll.add(graphic);
        } else if (v.graphicalType == 2) {
          //线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: getOneStyle(v.ruleId, 'polylineColor'), // RGB color values as an array
            width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
            style: getOneStyle(v.ruleId, 'polylineType')
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
            // view.zoom = 18;
          }
          graphicsLayerAll.add(polygonGraphic);
        } else if (v.graphicalType == 3) {
          //面
          const simpleFillSymbol = {
            type: 'simple-fill',
            // color: [240,230,140, 0.8],  // Orange, opacity 80%
            // color:bgColor,
            style: camelToKebab(getOneStyle(v.ruleId, 'polygonType')) || 'solid',
            outline: {
              // color: [255, 255, 255],
              color: getOneStyle(v.ruleId, 'polylineColor'),
              width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
              style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          if (reloadFirst.value) {
            view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
            // view.zoom = 18;
            reloadFirst.value = false;
          }

          graphicsLayerAll.add(polygonGraphic);
        }
      }
      if (v.list.length != 0) {
        reloadGraphsItem(v.list, showGraphs);
      }
    });
  });
};

/**
 * 根据id和key 获取pina里面平铺的样式列表得到对应的样式
 * @returns 样式信息
 */
const getOneStyle = (id: number, key: string) => {
  let style = null;
  for (let index = 0; index < projectStore.nodeStyles.length; index++) {
    if (projectStore.nodeStyles[index].id === id) {
      style = projectStore.nodeStyles[index].style[key];
      break;
    }
  }
  return style;
};

/**
 * 绘制子要素，分段请求后绘制
 * @param {*} list 子要素列表
 */
const drawChildNodeToSub = (list: any) => {
  // 清空原始图形数据数组
  originalGraphicsData = [];
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const currentChildGraphics = [];
    // 清除现有的子要素图形
    childGraphics.removeAll();
    graphicsLayerAll.removeAll();
    // 如果存在聚合图层，先移除
    if (clusterLayer) {
      map.remove(clusterLayer);
      clusterLayer = null;
    }
    list.forEach((v, idx) => {
      if (v.geomArcgis) {
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        if (polygon.type == 'polygon') {
          let bgColor = hexToRgba(getOneStyle(v.ruleId, 'polylineColor'), '0.2');
          if (v.polylineColorExpress) {
            bgColor = v.polylineColorExpress;
          } else if (!v.polylineColorExpress && getOneStyle(v.ruleId, 'polygonFillColor')) {
            bgColor = hexToRgba(getOneStyle(v.ruleId, 'polygonFillColor'));
          }
          //面
          // 面 并且是第五级levelNum=5 拆迁户专用颜色区分
          if (route.query.iscq && v.levelNum == 5) {
            // 判断拆迁进度
            for (let i = 0; i < v.fieldInstanceModels.length; i++) {
              if (v.fieldInstanceModels[i].groupName == '征迁进度') {
                switch (v.fieldInstanceModels[i].attribution.ZQJD) {
                  case '已完成调查':
                    bgColor = hexToRgba('#0199FF', '0.8');
                    color = '#0199FF';
                    break;
                  case '已完成确权':
                    bgColor = hexToRgba('#9B66FD', '0.8');
                    color = '#9B66FD';
                    break;
                  case '已完成签约':
                    bgColor = hexToRgba('#00FF80', '0.8');
                    color = '#00FF80';
                    break;
                  case '已完成拆除':
                    bgColor = hexToRgba('#FF8D02', '0.8');
                    color = '#FF8D02';
                    break;
                  case '已完成测绘':
                    bgColor = hexToRgba('#0002CC', '0.8');
                    color = '#0002CC';
                    break;
                  case '已完成评估':
                    bgColor = hexToRgba('#FEFF02', '0.8');
                    color = '#FEFF02';
                    break;
                  case '已完成腾空':
                    bgColor = hexToRgba('#FF01FD', '0.8');
                    color = '#FF01FD';
                    break;
                  default:
                    break;
                }
                break;
              }
            }
          }
          let outLineColor = getOneStyle(v.ruleId, 'polylineColor');
          if (v.polylineColorExpress) {
            outLineColor = v.polylineColorExpress;
          }
          const simpleFillSymbol = {
            type: 'simple-fill',
            style: camelToKebab(getOneStyle(v.ruleId, 'polygonType')) || 'solid',
            color: bgColor,
            outline: {
              color: outLineColor,
              width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
              style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          currentChildGraphics.push(polygonGraphic);
        } else if (polygon.type == 'polyline') {
          // 线
          const lineSymbol = {
            type: 'simple-line',
            color: v.polylineColorExpress ? v.polylineColorExpress : getOneStyle(v.ruleId, 'polylineColor'),
            width: getOneStyle(v.ruleId, 'polylineWidth') || 1,
            style: getOneStyle(v.ruleId, 'polylineType') || 'solid'
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          currentChildGraphics.push(polygonGraphic);
        } else if (polygon.type == 'point') {
          //点
          let pointColor = '#ff0000';
          //如果有表达式动态样式的值 取该值
          if (v.pointColorExpress) {
            pointColor = v.pointColorExpress;
          } else if (!v.pointColorExpress && getOneStyle(v.ruleId, 'pointColor')) {
            pointColor = getOneStyle(v.ruleId, 'pointColor');
          }
          //创建文本符号
          let textSymbol = new TextSymbol();
          //文本的内容的实在
          textSymbol = {
            type: 'simple-marker',
            style: camelToKebab(getOneStyle(v.ruleId, 'pointType')) || 'circle',
            color: hexToRgba(pointColor),
            width: getOneStyle(v.ruleId, 'pointSize') || 2
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          currentChildGraphics.push(graphic);
        }
      }
    });

    // 一次性添加所有图形
    childGraphics.addMany(currentChildGraphics);
    map.add(childGraphics);
    // 更新原始图形数据并应用聚合
    if (currentChildGraphics.length > 0) {
      originalGraphicsData = [...originalGraphicsData, ...currentChildGraphics];
      handleClusteringChange();
    }
    //绘制完成了之后需要判断是否有高亮内容
    if (nowHighlightId.value && !clusterConfig.enabled) {
      endChangHighLight(nowHighlightId.value);
    }
  });
};

/**
 * 选择数据
 */
const chooseData = () => {
  managerDialog.value = false;
  ifTree.value = false;
  searchDialog.value = true;
};
/**
 * 得到筛选要素数据
 */
const getChooseData = (list) => {
  downLoadMsg.value.zdList = list;
  const names = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  downLoadMsg.value.zdListNames = names.join(',');
  searchDialog.value = false;
};
/**
 * 修改公司名称
 * @param val
 */
const changeCompany = (val) => {
  downLoadMsg.value.companyName = val;
};
/**
 * 把导出里面的文件结构迭代 整理出来代表gdb和shp的树id
 * @param list
 */
const getShpGdb = (list) => {
  list.forEach((v) => {
    if (v.fileType == 3) {
      // shp
      shpGdbTree.shp.push(v.id);
    } else if (v.fileType == 4) {
      // gdb
      shpGdbTree.gdb.push(v.id);
    } else if (v.fileType == 5) {
      // 图片
      shpGdbTree.pic.push(v.id);
    } else if (v.fileType == 1) {
      // word
      shpGdbTree.word.push(v.id);
    } else if (v.fileType == 2) {
      //excel
      shpGdbTree.excel.push(v.id);
    } else if (v.fileType == 7) {
      // 附件
      shpGdbTree.fileMap.push(v.id);
    }
    if (v.list.length != 0) {
      getShpGdb(v.list);
    }
  });
};
/**
 * 关闭筛选数据弹窗
 */
const closeSearchDialog = () => {
  // 需要重新请求下列表 因为可能管理删除了某些数据
  if (managerDialog.value) {
    // 管理才进行重新查询
    emit('getLinyeData', true);
  }
  downLoadMsg.value.zdList = [];
  downLoadMsg.value.zdListNames = '';
  searchDialog.value = false;
};
/**
 * 关闭进度条
 */
const handleCloseProgress = () => {
  progressDialog.value = false;
  isContinue.value = false;
};
// 批量导出
const batchDown = (ite_params) => {
  const zdId = ite_params.zdId;
  const batchSize = batchNum.value;
  let currentIndex = 0;
  const processBatch = async () => {
    const currentBatch = zdId.slice(currentIndex, currentIndex + batchSize);
    if (currentBatch.length === 0) {
      // 所有批次都处理完毕
      showBatch.value = false;
      progressDialog.value = false;
      return;
    }

    // 更新当前批次的参数
    const batchParams = { ...ite_params, zdId: currentBatch };

    // 发送导出请求
    try {
      const res = await downLoadPublic(batchParams);
      if (res.code == 200) {
        oneceProgress.value = res.data.progress;
        downStatus.value = '';
        batchMsg.value = `正在导出第 ${Math.floor(currentIndex / batchSize) + 1} 批次（当前批次数量${currentBatch.length}）`;
        nowBatch.value = Math.floor(currentIndex / batchSize) + 1;
        isContinue.value = true;
        await waitForBatchCompletion(res.data.id, `_第${nowBatch.value}批次`);
        // 当前批次完成，更新批量导出进度
        batchProgress.value = Number((((currentIndex + batchSize) / zdId.length) * 100).toFixed(2));
        if (batchProgress.value > 100) {
          batchProgress.value = 100;
        }
        // 处理下一批次
        currentIndex += batchSize;
        await processBatch();
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      ElMessage.error('导出过程中出现错误');
      isContinue.value = false;
      progressDialog.value = false;
    }
  };

  // 开始处理第一批
  processBatch();
};
/**
 * 等待批量导出完成
 * @param id 导出id
 * @param fileName 文件名
 */
const waitForBatchCompletion = (id, fileName) => {
  return new Promise((resolve) => {
    const checkProgress = async () => {
      if (isContinue.value) {
        const parmas = { id };
        const res = await findAsyncMsgApi(parmas);
        if (res.code == 200) {
          if (!res.data) {
            ElMessageBox.alert('数据异常，请重试！！！', {
              confirmButtonText: '确定',
              callback: (action) => {
                progressDialog.value = false;
              }
            });
            return;
          }
          downMsg.value = res.data.remark;
          if (parseFloat(res.data.progress) != 1) {
            oneceProgress.value = Number((res.data.progress * 100).toFixed(2));
            if (res.data.status == -1) {
              ElMessage.error(res.data.result);
              downStatus.value = 'exception';
              progressDialog.value = false;
            } else {
              setTimeout(checkProgress, 1000);
            }
          } else if (parseFloat(res.data.progress) == 1) {
            downStatus.value = 'success';
            if (res.data.path) {
              downLoadFileName.value = res.data.path;
            }
            oneceProgress.value = Number((res.data.progress * 100).toFixed(2));
            downLoadId.value = id;
            fileSize.value = res.data.size;
            generalDown(`${downLoadFileName.value}${fileName}`);
            setTimeout(() => {
              resolve(res);
            }, 1000);
          }
        } else {
          ElMessage.error(res.msg);
        }
      }
    };
    checkProgress();
  });
};
/**
 * 修改筛选条件
 * @param type
 * @param idx
 */
const editCondition = (type, idx) => {
  //type 1新增 2删除 idx 删除的下标
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

//通用下载
const generalDown = (spfileName?: string) => {
  const parmas = {
    id: downLoadId.value,
    fileName: downLoadFileName.value
  };
  findAsyncFileBrowser(parmas, showProgess).then((res) => {
    downloadTypeDialog.value = false;
    if (res.data.type == 'application/json') {
      loadingProgessInstance.close();
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data) => {
        bugMsg = JSON.parse(data.currentTarget['result']).msg;
        ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      let fileName = downLoadFileName.value;
      if (spfileName) {
        //如果是批量导出
        fileName = spfileName;
      }
      const blob = new Blob([res.data], { type: 'application/zip' });
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = fileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
      setTimeout(() => {
        loadingProgessInstance.close();
      }, 3000);
    }
  });
};
/**
 * 获取当前下载进度
 * @param progress
 */
const showProgess = (progress) => {
  if (!showBatch.value) {
    //如果不是批量导出才需要下载进度
    progressEnd.value = progress;
    currentPercent.value = progress.loaded / 1024 / 1024;
    const percent = currentPercent.value.toFixed(2);
    const planNum = ((progress.loaded / fileSize.value) * 100).toFixed(2);
    loadingProgessInstance = ElLoading.service({
      lock: true,
      text: `数据下载中${percent}MB，进度${planNum}%`,
      spinner: 'Loading',
      background: 'rgba(255, 255, 255, 0.9)'
    });
  }
};
/**
 * 提交下载方式
 */
const submitDaownType = () => {
  if (!downloadType.value) {
    ElMessage.error('请选择下载方式！！！');
    return;
  }
  if (downloadType.value == 1) {
    //迅雷下载
    xunleiDownLoad();
  } else if (downloadType.value == 2) {
    //普通下载
    generalDown();
  }
};
// 调用迅雷下载  暂时不用，以后可能用
const xunleiDownLoad = () => {
  // 创建单个任务
  const url = `${import.meta.env.VITE_APP_BASE_API}/tool/async/findAsyncFileXL?id=${downLoadId.value}&fileName=${downLoadFileName.value}.zip`;
  // thunderLink.newTask({
  //   downloadDir: '', // 指定当前任务的下载目录名称，迅雷会在用户剩余空间最大的磁盘根目录中创建这个目录。【若不填此项，会下载到用户默认下载目录】
  //   tasks: [
  //     {
  //       name: '', // 指定下载文件名（含扩展名）。【若不填此项，将根据下载 URL 自动获取文件名】
  //       url: url // 指定下载地址【必填项】
  //     }
  //   ]
  // });
  downloadTypeDialog.value = false;
};
/**
 * 关闭下载方式
 */
const handleCloseDownType = () => {
  downloadTypeDialog.value = false;
};
/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};
/**
 * 提交筛选条件
 * @param isMapping
 */
const submitSearch = (isMapping?: number) => {
  //isMapping 1为导出未匹配到的数据
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  if (dialogSearch.value.parcelCode === '') {
    dialogSearch.value.parcelCode = null;
  }
  if (dialogSearch.value.parcelCode && !isArray(dialogSearch.value.parcelCode)) {
    dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
  }
  if (dialogSearch.value.taskId) {
    //选择了任务id 就要改变这个值
    dialogSearch.value.allocation = true;
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.value.allocation = '';
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  if (dialogSearch.value.updateDate && dialogSearch.value.updateDate.length != 0) {
    dialogSearch.value.updateTimeStart = dialogSearch.value.updateDate[0];
    dialogSearch.value.updateTimeEnd = dialogSearch.value.updateDate[1];
  } else if (!dialogSearch.value.updateDate) {
    dialogSearch.value.updateTimeStart = '';
    dialogSearch.value.updateTimeEnd = '';
  }

  // 获取数据列表
  getPlaceList({
    ...dialogSearch.value,
    moduleId: projectStore.proModuleId,
    pageNum: 1,
    pageSize: dialogSearch.value.pageSize
  }).then((res) => {
    if (res.code == 200) {
      downLoadMsg.value.zdList = res.data.list;
      localTotal.value = res.data.total; // 更新localTotal值
      // 恢复选中状态
      nextTick(() => {
        if (multipleSelection.value.length > 0) {
          multipleSelection.value.forEach((row) => {
            multipleTableRef.value?.toggleRowSelection(row, true);
          });
        }
      });
    } else {
      ElMessage.error(res.msg);
    }
  });

  shaixuanDialog.value = false;
};
/**
 * 重置筛选条件
 */
const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 20,
    parcelName: '',
    moduleId: queryParamsCopy.value.moduleId,
    conditionFields: [],
    ruleIds: [],
    parcelCode: null,
    express: false //是否查询表达式异常的数据
  }; //弹窗筛选
  emit('getParmas', dialogSearch.value);
  shaixuanDialog.value = false;
};

// 清除选中的用户 1采集人 2最后更新人
const clearUser = (type) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};
/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: projectStore.proModuleId // 直接使用projectStore.proModuleId
  };
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
// --- 生命周期 ---
onMounted(() => {
  // 初始化导出设置
  getExportSetting();
  getCompany();
  getSearchTaskList(); // 添加这行，在组件挂载时就获取任务列表
  isContinue.value = true;
  const list_3 = [];
  for (let i = 25; i < 46; i++) {
    list_3.push({ value: i, label: `${i}` });
  }
  const list_6 = [];
  for (let i = 13; i < 24; i++) {
    list_6.push({ value: i, label: `${i}` });
  }
  wkidList[0].children[0].children = list_3;
  wkidList[0].children[1].children = list_6;
  if (view) {
    view.watch('zoom', (newZoom) => {
      // 更新聚合距离
      clusterConfig.distance = calculateClusterDistance(newZoom);

      // 如果当前缩放级别大于等于设定的聚合级别，且之前是聚合状态，则需要取消聚合
      // 如果当前缩放级别小于设定的聚合级别，且之前是非聚合状态，则需要进行聚合
      if ((newZoom >= clusterConfig.zoomLevel && clusterLayer) || (newZoom < clusterConfig.zoomLevel && !clusterLayer && clusterConfig.enabled)) {
        handleClusteringChange();
      }
    });
  }
});

onBeforeUnmount(() => {
  if (view) {
    // destroy the map view
    view.container = null;
  }
});

// 添加下载类型相关变量
const xlDown = ref<boolean>(false); // 迅雷下载选择
const ptDown = ref<boolean>(true); // 普通下载选择

// 添加下载类型切换方法
const changeDownloadType = (type: number) => {
  downloadType.value = type;
  if (type === 1) {
    xlDown.value = false;
    ptDown.value = true;
  } else {
    xlDown.value = true;
    ptDown.value = false;
  }
};

/**
 * 切换聚合面板显示
 */
const toggleClusterPanel = () => {
  showClusterPanel.value = !showClusterPanel.value;
};

/**
 * 处理聚合开关变化
 */
const handleClusteringChange = () => {
  // 获取当前视图的缩放等级
  const currentZoom = view ? view.zoom : 0;
  // 如果聚合开关关闭或当前缩放等级大于设定的聚合等级，不进行聚合
  if (!clusterConfig.enabled || currentZoom >= clusterConfig.zoomLevel) {
    // 移除聚合图层
    if (clusterLayer) {
      if (map && typeof map.remove === 'function') {
        map.remove(clusterLayer);
      }
      clusterLayer = null;
    }
    // 显示原始图层
    if (graphicsLayerAll && typeof graphicsLayerAll.removeAll === 'function') {
      graphicsLayerAll.visible = true;
      // 确保原始图层中的图形都被添加回来
      if (originalGraphicsData.length > 0) {
        graphicsLayerAll.removeAll();
        originalGraphicsData.forEach((graphic) => {
          if (graphic && typeof graphicsLayerAll.add === 'function') {
            graphicsLayerAll.add(graphic);
          }
        });
      }
    }

    // 显示子要素图层
    if (childGraphics) {
      childGraphics.visible = true;
    }
    //查询是否有需要高亮的数据
    if (nowHighlightId.value) {
      endChangHighLight(nowHighlightId.value);
    }
    return;
  }

  // 如果有原始数据或子要素数据，且需要进行聚合
  if (originalGraphicsData.length > 0 || (childGraphics && childGraphics.graphics.items && childGraphics.graphics.items.length > 0)) {
    // 先移除已存在的聚合图层
    if (clusterLayer) {
      if (map && typeof map.remove === 'function') {
        map.remove(clusterLayer);
      }
      clusterLayer = null;
    }
    // 合并主要素和子要素的图形数据，并进行去重
    let allGraphics = [...originalGraphicsData];
    if (childGraphics && childGraphics.graphics.items) {
      // 使用Map根据id去重
      const uniqueGraphicsMap = new Map();
      [...allGraphics, ...childGraphics.graphics.items].forEach((graphic) => {
        if (graphic.id && !uniqueGraphicsMap.has(graphic.id)) {
          uniqueGraphicsMap.set(graphic.id, graphic);
        }
      });
      allGraphics = Array.from(uniqueGraphicsMap.values());
      // 隐藏子要素图层
      childGraphics.visible = false;
    }
    // 创建新的聚合图层
    createClusterLayer(allGraphics);
    // 隐藏原始图层
    if (graphicsLayerAll) {
      graphicsLayerAll.visible = false;
    }
  }
};

/**
 * 初始化勾选所有树内容
 * @param tree 树数据
 */
const setDefaultCheckRules = (tree: any) => {
  // 遍历每个规则
  tree.forEach((rule: any) => {
    defaultCheckRules.value.push(rule.id); // 将规则的ID添加到默认勾选规则数组中
    if (rule.list && rule.list.length > 0) {
      setDefaultCheckRules(rule.list); // 递归处理子规则
    }
  });
};

/**
 * 重新请求地图范围
 */
const handleRestMap = () => {
  if (lastGeoWkt.value) {
    let mainIds = [];
    if (props.parcelList.length > 0) {
      mainIds = props.parcelList.map((item) => item.id);
    }
    const checkedRules = ruleTreeRef.value.getCheckedNodes();
    const ruleIds = [];
    checkedRules.forEach((item) => {
      ruleIds.push(item.id);
    });
    if (ruleIds.length == 0) {
      ElMessage.error('请至少勾选一个节点');
      return;
    }
    const params = {
      ruleIds: ruleIds,
      wkb: lastGeoWkt.value,
      pageNum: 1,
      pageSize: 10000,
      mainIds: mainIds
    };
    selectParcelClusterList(params).then((res) => {
      if (res.code == 200) {
        if (res.data.list) {
          drawChildNodeToSub(res.data.list);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

/**
 * 计算聚合距离
 * @param zoomLevel 缩放级别
 * @returns 聚合距离
 */
const calculateClusterDistance = (zoomLevel: number): number => {
  // 根据缩放级别动态计算聚合距离
  // 缩放级别越小，聚合距离越大
  const baseDistance = 100; // 基础聚合距离
  const factor = Math.pow(2, 15 - zoomLevel); // 15是一个参考级别
  return Math.min(Math.max(baseDistance * factor, 50), 100000); // 限制在50到100000米之间
};

const handleVisibilityChange = (value: boolean) => {
  // 获取当前高亮的图形ID
  const highlightId = nowHighlightId.value;
  if (!highlightId) return;
  // 在主图层中查找高亮图形
  let targetGraphic = null;
  if (graphicsLayerAll?.graphics?.items) {
    targetGraphic = graphicsLayerAll.graphics.items.find((graphic) => graphic.id === highlightId);
    if (targetGraphic) {
      targetGraphic.visible = value;
    }
  }
  // 在子要素图层中查找相关图形
  if (childGraphics?.graphics?.items) {
    const childItems = childGraphics.graphics.items;
    childItems.forEach((graphic: any) => {
      if (graphic.id === highlightId) {
        graphic.visible = value;
      }
    });
  }
};
const handleColorChange = (value: string | null) => {
  //更改当前高亮图形的颜色
  const ruleId = props.currentRuleId || currentSelectZd.value?.ruleId;
  if (!ruleId || !value) return;
  // 只更新当前选中图层的样式
  const currentNodeStyle = projectStore.nodeStyles.find((style) => style.id === ruleId);
  if (!currentNodeStyle) {
    return;
  }
  // 将RGBA颜色转换为十六进制
  let colorValue = value;
  if (value.startsWith('rgba')) {
    const matches = value.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
    if (matches) {
      const [, r, g, b, a] = matches;
      const alpha = Math.round(parseFloat(a) * 255)
        .toString(16)
        .padStart(2, '0');
      colorValue = `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}${alpha}`;
    }
  }
  // 只更新当前选中图层的颜色
  const updatedStyles = projectStore.nodeStyles.map((style) => {
    if (style.id === ruleId) {
      return {
        ...style,
        style: {
          ...style.style,
          polylineColor: colorValue,
          polygonFillColor: colorValue,
          pointColor: colorValue
        }
      };
    }
    return style;
  });
  // 更新 store
  projectStore.setNodeStyles(updatedStyles);
  // 重新渲染高亮
  highlightZD();
};

// 监听currentRuleId的变化
watch(
  () => props.currentRuleId,
  (newRuleId) => {
    if (newRuleId) {
      // 调用highlightZD来更新graphicColor
      highlightZD();
    }
  }
);

const handleTreeCheck = (data: any, checked: { checkedKeys: string[] }) => {
  const checkedNodes = ruleTreeRef.value.getCheckedNodes();
  const checkedKeys = checkedNodes.map((node) => node.id);
  defaultCheckRules.value = checkedKeys;
  // 更新图层显示状态
  if (props.ruleTree) {
    props.ruleTree.forEach((rule) => {
      const isVisible = checkedKeys.includes(rule.id);
      const layer = map.findLayerById(rule.id);
      if (layer) {
        layer.visible = isVisible;
      }
    });

    // 强制刷新视图
    if (view) {
      const currentCenter = view.center.clone();
      const currentZoom = view.zoom;
      view.goTo(
        {
          center: currentCenter,
          zoom: currentZoom
        },
        {
          duration: 0 // 立即更新，不使用动画
        }
      );
    }
  }
};

// 移除默认选中逻辑
watch(
  () => props.ruleTree,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      defaultCheckRules.value = []; // 不再默认选中任何节点
    }
  },
  { immediate: true }
);

// 表格列定义
const columns = ref([
  {
    key: 'selection',
    dataKey: 'selection',
    width: 45,
    align: 'center',
    headerCellRenderer: () => {
      return h(ElCheckbox, {
        modelValue: isAllSelected.value,
        onChange: handleSelectAll
      });
    },
    cellRenderer: ({ rowData }: any) => {
      return h(ElCheckbox, {
        modelValue: selectedRows.value.some((item) => item.id === rowData.id),
        onChange: (val: boolean) => handleSelect(val, rowData)
      });
    }
  },
  {
    key: 'index',
    title: '序号',
    width: 60,
    cellRenderer: ({ rowIndex }: TableV2RowScope<RowData>) => rowIndex + 1
  },
  {
    key: 'parcelName',
    title: '数据名称',
    width: 200,
    dataKey: 'parcelName'
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 200,
    cellRenderer: ({ rowData }: TableV2RowScope<RowData>) => formatDateType(rowData.createTime)
  }
]);

// 选中行数据
const selectedRows = ref<any[]>([]);

// 行选择处理
const handleSelect = (checked: boolean, row: any) => {
  if (checked) {
    selectedRows.value.push(row);
  } else {
    selectedRows.value = selectedRows.value.filter((item) => item.id !== row.id);
  }
  handleSelectTable(selectedRows.value, row);
};

// 获取行样式
const getRowClass = ({ rowData }: TableV2RowScope<RowData>) => {
  return selectedRows.value.some((item) => item.id === rowData.id) ? 'selected-row' : '';
};

// 全选状态
const isAllSelected = computed(() => {
  if (downLoadMsg.value.zdList.length === 0) return false;
  // 使用Map优化查找性能
  const selectedIds = new Set(selectedRows.value.map((row) => row.id));
  return downLoadMsg.value.zdList.every((row) => selectedIds.has(row.id));
});

// 处理全选
const handleSelectAll = (val: boolean) => {
  if (val) {
    // 全选当前页 - 使用Map优化性能
    const existingIds = new Map(selectedRows.value.map((row) => [row.id, row]));
    downLoadMsg.value.zdList.forEach((row) => {
      if (!existingIds.has(row.id)) {
        selectedRows.value.push(row);
      }
    });
  } else {
    // 取消当前页的选择 - 使用Set优化性能
    const currentPageIds = new Set(downLoadMsg.value.zdList.map((row) => row.id));
    selectedRows.value = selectedRows.value.filter((row) => !currentPageIds.has(row.id));
  }
  handleSelectionAll(selectedRows.value);
};

// 在 script 部分添加 clearSelection 方法
// 添加在 handleRowClick 方法后面
const clearSelection = () => {
  selectedRows.value = [];
  downLoadMsg.value.zdListNames = [];
  handleSelectionAll([]); // 通知父组件选择已清空
};

/**
 * 改变地图头部操作样式
 * @param type 竖vertical 横across
 */
const changeHandleTopType = (type: string) => {
  handleTopType.value = type;
};

defineExpose({
  init,
  initLinye,
  endChangHighLight,
  clearDeg,
  drawLabel,
  changeJZDVisbale,
  clearCQLabel,
  initLableCQ,
  reloadGraphs,
  initDeg,
  removerLabelLayer,
  initData,
  drawChildNodeToSub,
  drawFirstGeometry,
  changeHandleTopType,
  heightSpeGrc
});
</script>

<style scoped lang="scss">
.right-map-color {
  position: absolute;
  right: 60px;
  bottom: 50px;
  width: 260px;
  border: #409eff solid 1px;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  .title {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 10px;
    .right-close {
      height: 16px;
      width: 16px;
      border: #87ceeb solid 1px;
      color: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .hr {
    border-top: #87ceeb solid 1px;
    width: 100%;
  }
  .tuli-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 10px;
    .item {
      width: calc(50% - 0px);
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .color-box {
        width: 40px;
        height: 20px;
        margin-left: 2px;
      }
      .color1 {
        background: #0199ff;
      }
      .color2 {
        background: #0002cc;
      }
      .color3 {
        background: #9b66fd;
      }
      .color4 {
        background: #feff02;
      }
      .color5 {
        background: #00ff80;
      }
      .color6 {
        background: #ff01fd;
      }
      .color7 {
        background: #ff8d02;
      }
    }
  }
}
.cqplan-div {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  .cqplan-title {
    padding: 5px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    background: linear-gradient(107deg, rgba(3, 59, 146, 0.5) -8%, rgba(0, 34, 87, 0.5) 99%);
    border: 1px solid #7ab8ff;
    backdrop-filter: blur(10px);
    border-radius: 5px;
  }
  .hr {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 12px solid rgba(0, 34, 87, 0.6); /* 可以修改颜色和大小 */
    // background: rgba(0, 34, 87, .6);
  }
  .cqplan-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 14px;
      padding: 5px;
      border: #fff solid 1px;
    }
    .cqOk {
      background: #058828;
    }
    .cqON {
      background: #dc1a35;
    }
  }
}
.handle-vertical {
  flex-direction: column !important;
}
.handle-top {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 8px;
  background: rgb(0, 0, 0, 0.5);
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .pick-up {
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: #fff;
  }
  .pick-up:hover {
    color: #1890ff;
  }
  .handle-item {
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .handle-item:hover {
    color: #1890ff;
  }
  .hr {
    height: 16px;
    border-right: #fff solid 1px;
    margin: 0px 10px;
  }
  .vertical {
    width: 42px;
    border-bottom: #fff solid 1px;
    margin: 10px 0px;
  }
}
.handle-graphic {
  position: absolute;
  top: 60px;
  left: 10px;
  padding: 5px;
  border-radius: 4px;
  background: rgb(0, 0, 0, 0.5);
  color: #fff;
  z-index: 2000;
  .handle-graphic-left {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
  }

  .handle-graphic-right {
    display: flex;
    align-items: center;
  }
}
:deep(.esri-view .esri-view-surface:focus::after) {
  outline: none !important;
}
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom .esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
:deep(.esri-zoom .esri-widget--button:hover) {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 10px;
}
:deep(.esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}

.gisMap-main {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 0;
  .check-screen {
    position: absolute;
    right: 16px;
    top: 25px;
    padding: 5px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    color: #fff;
    font-size: 12px;
  }
  .check-screen1 {
    position: absolute;
    right: 16px;
    top: 45px;
    padding: 5px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    color: #fff;
    font-size: 12px;
  }
  .map {
    width: 100%;
    height: 100%;
  }

  .rght-handle-div {
    position: absolute;
    width: 20px;
    height: 36px;
    background: #f1efef;
    border-radius: 4px 0px 0px 4px;
    right: 0px;
    top: 45%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #999;
    z-index: 99;

    .zankai {
      width: 16px;
      height: 16px;
    }
  }

  .rght-handle-div :hover {
    color: #1b9af7;
  }

  .project-detail {
    position: absolute;
    width: 500px;
    height: calc(100% - 30px);
    background: #ffffff;
    right: 0px;
    top: 0px;
    padding: 15px;
    // opacity: .8;
    .head-div {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .tab-div {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      overflow-x: auto;

      .tab-row {
        height: 30px;
        min-width: 110px;
        font-size: 14px;
        color: #999999;
        cursor: pointer;
        background-color: #f6f7f8;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tab-active {
        background: #e7f5ff;
        color: #333333;
      }

      .error-tab {
        border: #ff5555 solid 1px;
      }
    }

    .detail-content {
      height: calc(100% - 123px);
      overflow: auto;

      .flex-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .label {
            color: #333333;
            font-size: 14px;
            margin-bottom: 15px;
          }
        }

        .margin {
          margin-left: 20px;
        }
      }

      .flex-box {
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px;
        margin-bottom: 20px;

        .flex-row {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;

          .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .label {
              color: #333333;
              font-size: 14px;
              margin-bottom: 15px;
            }

            .img-box {
              width: 100%;
              height: 140px;
            }
          }

          .margin {
            margin-left: 20px;
          }
        }

        .right-handle {
          display: flex;
          flex-basis: row;
          justify-content: flex-end;

          .span {
            color: #999999;
            cursor: pointer;
          }
        }
      }

      .grid-container {
        display: grid;
        grid-gap: 20px 20px;
        grid-template-columns: repeat(2, 45%);
        width: 100%;

        .grid-item {
          height: 300px;
        }
      }
    }
  }

  .more-right {
    right: 500px;
  }

  .dialog-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    .label {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .content {
    }
  }

  .zdfclabel {
    display: inline-block;
    width: 20%;
  }

  .zdfccontent {
    display: inline-block;
    width: 70%;
  }
  .uploadimgspan {
    display: inline-block;
    font-size: 3pt;
    font-style: italic;
  }

  .dialog-box-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .dialog-row-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // justify-content: space-between;
    margin-bottom: 20px;

    .checkbox-item {
      width: 20%;
      margin-bottom: 5px;
    }
  }

  .dialog-label {
    margin-bottom: 10px;
  }

  .dialog-row-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .red-span {
    color: #ff5555;
    margin-bottom: 10px;
  }

  .zhipai {
    position: absolute;
    top: 70px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }

  .gengxin {
    position: absolute;
    top: 110px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }
  .right-handle-div {
    position: absolute;
    right: 16px;
    bottom: 170px;
    .tuceng-box {
      position: absolute;
      max-height: 200px;
      background: rgba(0, 0, 0, 0.5);
      right: 45px;
      border-radius: 8px;
      overflow: auto;
      padding: 5px;
      top: 0px;
      min-width: 200px;
      color: #fff;
      .check-item {
        margin-bottom: 5px;
        color: #fff;
      }
    }
    /*滚动条样式*/
    .tuceng-box::-webkit-scrollbar {
      width: 4px;
    }
    .tuceng-box::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(255, 255, 255, 0.5);
    }
    .tuceng-box::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #ffffff;
      position: relative;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .map-change {
    position: absolute;
    bottom: 180px;
    right: 60px;
    display: flex;
    flex-direction: row;
    .map-item {
      width: 96px;
      height: 72px;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: flex-end;
      .map-footer {
        width: 100%;
        height: 22px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
        border-radius: 0px 0px 8px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #fff;
      }
      .map-footer-active {
        color: #1b9af7;
      }
    }
    .map-left {
      background-image: url('../../../assets/images/normal-map.png');
      background-size: cover;
    }
    .map-right {
      background-image: url('../../../assets/images/image-map.png');
      background-size: cover;
      margin-left: 8px;
    }
    .map-active {
      border: #1b9af7 solid 1px;
    }
  }
  .map-copyRight {
    position: absolute;
    bottom: 10px;
    left: 16px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    .copy-ico {
      width: 53px;
      height: 22px;
      margin-right: 10px;
    }
  }
}
.attr-box {
  position: absolute;
  width: 300px;
  height: 400px;
  z-index: 1;
  .attr-div {
    width: 100%;
    height: 100%;
    background: rgb(0, 0, 0, 0.5);
    border-radius: 8px;
    position: relative;
    overflow: auto;
    .attr-close {
      position: absolute;
      top: 8px;
      right: 5px;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
    }
    .attr-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #fff;
      padding: 0px 10px;
      flex-wrap: wrap;
      font-size: 12px;
      .left {
        width: 100px;
      }
      .right {
        flex: 1;
      }
    }
    .two {
      background: rgba(0, 0, 0, 0.5);
    }
    .title {
      font-weight: bold;
      display: flex;
      justify-content: center;
      background: rgba(0, 0, 0, 0.5);
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
    }
  }
  /*滚动条样式*/
  .attr-div::-webkit-scrollbar {
    width: 4px;
  }
  .attr-div::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .attr-div::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}

:deep(.el-tree) {
  background-color: transparent;
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background-color: rgba(248, 248, 248, 0.2);
  }
}

.handle-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 0 8px;

  .icon-item {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    :deep(.el-icon) {
      font-size: 16px;
      color: #ffffff;
      transition: all 0.3s;
    }

    &:hover {
      background-color: #e6e8eb;
      transform: scale(1.1);

      :deep(.el-icon) {
        color: #000000;
      }
    }
  }
}

/* 聚合配置面板样式 */
.cluster-config-panel {
  position: absolute;
  top: 80px;
  right: 20px;
  width: 320px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 1000;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-weight: bold;
    font-size: 16px;
    color: #333;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 12px 12px 0 0;

    .right-close {
      cursor: pointer;
      font-size: 20px;
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;

      &:hover {
        color: white;
        transform: scale(1.1);
      }
    }
  }

  .cluster-content {
    padding: 20px;
    max-height: 450px;
    overflow-y: auto;

    .config-section {
      margin-bottom: 24px;
      padding: 16px;
      background: rgba(248, 250, 252, 0.8);
      border-radius: 8px;
      border: 1px solid rgba(226, 232, 240, 0.8);

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;
        font-size: 15px;
      }

      .config-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        label {
          font-size: 14px;
          color: #666;
          min-width: 60px;
          font-weight: 500;
        }

        .el-switch {
          margin-left: auto;
        }

        .el-slider {
          flex: 1;
          margin-left: 12px;
          margin-right: 8px;
        }

        .el-input-number {
          width: 80px;
        }

        .unit-label {
          font-size: 12px;
          color: #999;
          margin-left: 4px;
          min-width: 20px;
        }
      }
    }
  }
}

.dialog-content {
  :deep(.el-table-v2__row.selected-row) {
    background-color: var(--el-table-row-hover-bg-color);
  }
}

.dialog-search {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .selected-count {
    font-size: 14px;
    color: #606266;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
  }
}

.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 110px;
  height: 110px;
}
.upload-demo .el-upload-list--picture-card img {
  width: 110px;
  height: 110px;
}
.uoloadSty .el-upload--picture-card {
  width: 110px;
  height: 110px;
  line-height: 110px;
}

.disUoloadSty .el-upload--picture-card {
  display: none;
  /* 上传按钮隐藏 */
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.down-content {
  display: flex;
  flex-direction: column;
  .flex-row {
    margin-bottom: 10px;
  }
}
:deep(.el-empty__description) {
  margin-top: -20px;
}
.demo-ruleForm {
  display: flex;

  .left-content {
    border: 1px solid rgb(177, 177, 177);
    border-radius: 8px;
    padding: 10px;
    background: rgba(177, 177, 177, 0.08);
    flex: 1;
  }
  .right-content {
    flex: 1;
    margin-left: 20px;
    .page {
      right: 0;
    }
  }
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    .form-item {
      flex: 1;
      min-width: 0;
    }
  }

  .data-list-item {
    margin-bottom: 20px;
  }
}
</style>
