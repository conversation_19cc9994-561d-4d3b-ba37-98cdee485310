<!-- 超期过渡费弹窗 -->
<template>
  <div class="addCQGDF-main">
    <el-dialog title="超期过渡费配置" v-model="cqgdfDialogCopy" width="860" :before-close="handleClose">
      <div class="flex-row">
        起始日期：
        <el-date-picker
          v-model="rangDate"
          type="monthrange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始月份"
          value-format="timestamp"
          end-placeholder="结束月份"
        >
        </el-date-picker>
      </div>
      <el-table :data="tableData" style="width: 100%" :height="height" border>
        <el-table-column v-for="(item, index) in cqgdfField.attribution.children" :key="index" :label="item.fieldCn">
          <template #default="scope">
            <template v-if="item.fieldCn == '月份选择'">
              {{ formatDateTypeMonth(scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldCn == '过渡补偿价格'">
              {{ scope.row[item.fieldName] }}
            </template>
            <template v-else>
              <!-- 输入框 -->
              <template v-if="item.valueMethod == 'input'">
                <el-input v-model="scope.row[item.fieldName]" :placeholder="item.inputHint"></el-input>
              </template>
              <!-- 单选框 -->
              <template v-if="item.valueMethod == 'radio'">
                <el-radio-group v-model="scope.row[item.fieldName]">
                  <el-radio :label="ite.label" v-for="(ite, idx) in item.attribution.options" :key="idx"></el-radio>
                </el-radio-group>
              </template>
              <!-- 下拉选择 -->
              <template v-if="item.valueMethod == 'select'">
                <el-select v-model="scope.row[item.fieldName]" :placeholder="item.inputHint">
                  <el-option v-for="(ite, idx) in item.attribution.options" :key="idx" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { formatDateTypeMonth } from '@/utils/filters';
// --- props ---
interface Props {
  // 打开弹框
  cqgdfDialog: boolean;
  cqgdfField?: any;
  nowChecked?: any;
  cqgdfValue?: any;
}

const props = withDefaults(defineProps<Props>(), {
  cqgdfDialog: false
});

const cqgdfDialogCopy = computed(() => props.cqgdfDialog);

// --- 定义变量 ---
const height = ref(window.innerHeight - 420);
const rangDate = ref('');
const tableData = ref([]);

watch(
  cqgdfDialogCopy,
  (val) => {
    if (val) {
      initDialog();
    }
  },
  { deep: true }
);

function initDialog() {
  let FWYSRQ = null;
  let GDMJ = 0;
  let GDQX = 0;
  props.nowChecked.fieldList.forEach((v) => {
    if (v.label == '房屋验收日期') {
      //房屋验收日期
      FWYSRQ = v.value;
    } else if (v.label == '过渡面积') {
      GDMJ = v.value;
    } else if (v.label == '过渡期限') {
      GDQX = v.value;
    }
  });
  const monthsList = getMonthsInRange(FWYSRQ, GDQX);
  tableData.value = [];
  monthsList.forEach((v, vdx) => {
    let ite = {};
    if (props.cqgdfValue && vdx <= props.cqgdfValue.length - 1) {
      //直接按顺序来回显赋值
      ite = props.cqgdfValue[vdx];
    } else {
      props.cqgdfField.attribution.children.forEach((k) => {
        if (k.fieldCn == '月份选择') {
          ite[k.fieldName] = v;
        } else if (k.fieldCn == '过渡补偿价格') {
          let costValue = 0; //过渡补偿费用
          let xs = 1.1 ** (vdx + 1) * 10; //系数 (1.1^3*10).toFixed(2) 是截取后两位
          if (vdx >= 16) {
            //系数 50为最大
            xs = 50;
          } else {
            //小于17次需要截取小数点两位
            xs = +truncateToTwoDecimalPlaces(xs);
          }
          if ([10, 11, 12, 13, 14].includes(vdx)) {
            xs = xs - 0.01;
          }
          costValue = +roundToTwoDecimalPlaces(Number(GDMJ) * xs);
          ite[k.fieldName] = costValue;
        } else {
          ite[k.fieldName] = '';
        }
      });
    }
    tableData.value.push(ite);
  });
}
//  ---定义emit---
const emit = defineEmits<{
  (e: 'closeCQGDF'): void;
  (e: 'submitCQGDF', tableData: any): void;
}>();

// --- 定义方法 ---
const handleClose = () => {
  emit('closeCQGDF');
};
const submit = () => {
  emit('submitCQGDF', tableData.value);
};

const getMonthsInRange = (startTimestamp, ignoreYears, currentDate = new Date()) => {
  // 将开始日期的时间戳转换为 Date 对象
  const startDate = new Date(startTimestamp);

  // 计算出忽略两年后的日期（这里使用毫秒数表示两年：2 * 365 * 24 * 60 * 60 * 1000，注意闰年）
  // 为了简化，这里我们假设每年都是365天，实际中应考虑闰年
  const millisecondsInYear = 365 * 24 * 60 * 60 * 1000;
  const ignoreDate = new Date(startDate.getTime() + ignoreYears * millisecondsInYear);

  // 获取当前日期（默认为调用函数时的日期）
  // const currentDate = new Date(); // 如果不需要默认参数，可以取消注释这行代码

  // 初始化一个数组来存储月份
  const monthsInRange = [];

  // 设置起始月份为忽略日期后的第一个月（忽略日期的月份+1，如果超出11则年份+1，月份重置为0）
  const currentMonth = new Date(ignoreDate);
  currentMonth.setMonth(ignoreDate.getMonth() + 1);
  if (currentMonth.getMonth() === ignoreDate.getMonth()) {
    // 如果月份没变化，说明是12月之后，年份需要+1
    currentMonth.setFullYear(currentMonth.getFullYear() + 1);
    currentMonth.setMonth(0); // 重置月份为1月（0索引）
  }

  // 遍历直到当前日期或下个月超出当前日期
  while (currentMonth <= currentDate) {
    // 只存储年份和月份，创建一个新的 Date 对象避免引用问题
    monthsInRange.push(new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).getTime());

    // 移动到下一个月
    currentMonth.setMonth(currentMonth.getMonth() + 1);
    if (currentMonth.getMonth() === 0 && currentMonth.getFullYear() !== ignoreDate.getFullYear()) {
      // 如果月份变成0（即1月之前），并且年份有变化（说明不是年初的回绕），则处理闰年情况（但在这个循环里其实不需要，因为我们已经按月递增了）
      // 注意：这里的处理主要是为了说明年份和月份的变化，实际上在按月递增时，JS 会自动处理闰年
    }
  }

  // 返回月份列表（每个元素都是一个表示该月第一天的 Date 对象）
  return monthsInRange;
};

const getTimestampTwoYearsLaterSameMonth = (timestamp, num) => {
  // 创建一个Date对象
  const date = new Date(timestamp);

  // 获取当前年份，并增加2年
  const newYear = date.getFullYear() + num;

  // 设置新日期的年份
  date.setFullYear(newYear);

  // 如果日期超出了新月份的天数（例如，从2020-02-29到2022-02，因为2022不是闰年），
  // 则date对象会自动调整为该月的最后一天。

  // 获取新的时间戳
  const newTimestamp = date.getTime();
  const monthList = getMonthsBetweenTimestamps(newTimestamp);
  return monthList;
};

const getMonthsBetweenTimestamps = (startTimestamp) => {
  // 获取当前时间戳
  const currentTimestamp = Date.now();

  // 将时间戳转换为Date对象
  const startDate = new Date(startTimestamp);
  const currentDate = new Date(currentTimestamp);

  // 初始化月份列表数组
  const monthsList = [];

  // 设置开始日期的月份为下个月（因为我们想要的是“之间”的月份，所以不包括开始的那个月）
  const currentMonth = new Date(startDate);
  currentMonth.setMonth(startDate.getMonth() + 1);

  // 循环直到当前月份
  while (currentMonth <= currentDate) {
    // 创建一个新的Date对象，只包含年份和月份（日期设置为1，以确保它总是有效的）
    const monthDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);

    // 将月份添加到列表中（格式为YYYY-MM）
    monthsList.push(monthDate.toISOString().slice(0, 7));

    // 增加到下一个月
    currentMonth.setMonth(currentMonth.getMonth() + 1);
  }

  return monthsList;
};

const truncateToTwoDecimalPlaces = (number) => {
  // 将数字转为字符串，便于后续操作
  const numberStr = number.toString();

  // 查找小数点的位置
  const decimalPointIndex = numberStr.indexOf('.');

  // 如果没有小数点，说明是整数，直接返回并添加.00
  if (decimalPointIndex === -1) {
    return number + '.00';
  }

  // 截取小数点后两位字符
  const integerPart = numberStr.slice(0, decimalPointIndex); // 整数部分
  const decimalPart = numberStr.slice(decimalPointIndex + 1, decimalPointIndex + 3); // 小数部分（仅取两位）

  // 若小数部分不足两位，则用0填充
  const truncatedNumberStr = integerPart + '.' + (decimalPart.length === 2 ? decimalPart : decimalPart + '0');

  // 将字符串转回数字并返回（注意：这可能会引入浮点误差，但在此场景下通常可接受）
  return parseFloat(truncatedNumberStr);
};

const getTimestampFromString = (yearMonth) => {
  // 使用正则表达式解析年份和月份
  const [year, month] = yearMonth.split('-').map(Number);

  // 创建一个新的 Date 对象，表示该月份的第一天
  const date = new Date(year, month - 1, 1);

  // 获取时间戳
  const timestamp = date.getTime();

  return timestamp;
};
const roundToTwoDecimalPlaces = (number) => {
  return (Math.round(number * 100) / 100).toFixed(2);
};
</script>
<style lang="scss" scoped>
.flex-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.addCQGDF-main {
}
</style>
