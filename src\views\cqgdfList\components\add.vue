<!-- 发放 -->
<template>
  <div class="add-main">
    <div class="handle-title"><span style="cursor: pointer" @click="goBack">返回</span></div>
    <div class="handle-search">
      <div class="item">
        <div class="label">项目名称</div>
        <div class="right">
          <el-select v-model="search.moduleName" placeholder="请选择项目" @change="moduleChange">
            <el-option v-for="item in moduleList" :key="item.id" :label="item.moduleName" :value="item.id"> </el-option>
          </el-select>
        </div>
      </div>
      <div class="item">
        <div class="label">小区名称</div>
        <div class="right">
          <el-input v-model="search.areaName" placeholder="请输入小区名称"></el-input>
        </div>
      </div>
      <div class="item">
        <div class="label">日期</div>
        <div class="right">
          <el-date-picker v-model="search.rangMonth" type="month" placeholder="选择月" value-format="timestamp"> </el-date-picker>
        </div>
      </div>
      <div class="item" style="margin-left: 40px">
        <el-button type="primary" @click="handleSearch"
          ><el-icon><Search /></el-icon>查询</el-button
        >
        <el-button type="info" @click="handleReset"
          ><el-icon><RefreshRight /></el-icon>重置</el-button
        >
      </div>
      <div class="item"></div>
    </div>
    <el-table
      :data="tableData"
      @selection-change="handleSelectionChange"
      height="400px"
      style="width: 100%"
      ref="multipleTable"
      v-loading="loading"
      :default-sort="{ prop: 'isFfok', order: 'descending' }"
      border
    >
      <el-table-column type="selection" width="55" :selectable="selectable"> </el-table-column>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="被征收人姓名" prop="qlrName"></el-table-column>
      <el-table-column label="金额" prop="nowgdf" sortable></el-table-column>
      <el-table-column label="发放状态" sortable prop="isFfok">
        <template #default="scope">{{ scope.row.isFfok ? '已发放' : '未发放' }}</template>
      </el-table-column>
    </el-table>
    <div class="jf-box">
      <div class="fj-title">附件信息</div>
      <el-upload
        style="padding: 10px"
        class="upload-demo"
        :headers="headers"
        :action="`${baseUrl}/qjt/file/multi/upload`"
        :on-success="(response) => handleSuccessFJ(response, fjField)"
        :on-remove="(file) => handleRemoveFJ(file, fjField)"
        multiple
        name="files"
        :before-upload="(file) => beforeAvatarUpload(file, fjField)"
        :limit="fjField.attribution.picNum"
        :on-exceed="(files, fileList) => handleExceed(files, fileList, fjField)"
        :file-list="addMsg.FJ"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip" style="color: red">只能上传{{ onlyAccpt }}文件</div>
        </template>
        <template #file="{ file }">
          <div class="fj-row">
            {{ file.name }}
            <div style="cursor: pointer" @click="handleRemove(file)">×</div>
          </div>
        </template>
      </el-upload>
    </div>
    <el-button type="primary" size="small" @click="submit">提交</el-button>
    <el-tooltip class="item" effect="dark" content="费用发放需要先查询发放的项目以及月份，然后勾选需要发放的人员" placement="top-start">
      <i class="el-icon-question" style="margin-left: 10px; margin-top: 8px"></i>
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, inject } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getModuleList, selectRules, selectParcelFromTree, getPlaceList } from '@/api/modal';
import { getToken } from '@/utils/auth';
import { isArray } from '@/utils/validate';
import { saveSimple } from '@/api/project';
import { getFileType } from '@/utils/publicFun';

interface SearchForm {
  moduleName: string | number;
  areaName: string;
  rangMonth: string;
  name?: string;
}

interface ModuleItem {
  id: number;
  moduleName: string;
}

interface Attribution {
  picNum: number;
  acceptType?: string[] | string;
}

interface FjField {
  attribution: Attribution;
  fieldName?: string;
}

interface TableItem {
  id: number;
  qlrName: string;
  nowgdf: string | number;
  isFfok?: boolean;
  disabled?: boolean;
  custName?: string;
}

interface TreeNodeChildren {
  children?: {
    children?: {
      children?: {
        children?: {
          needData: boolean;
          conditionFields?: any[];
        };
      };
    };
  };
}

interface TreeNode extends TreeNodeChildren {
  parcelName: string;
  ruleId: number;
  needData: boolean;
  value?: any;
  list?: any[];
  fieldGroupModelList?: any[];
}

interface ProjectMsg {
  id: number;
  parcelName: string;
  ruleAttribution: any;
  ruleId: number;
}

interface FileItem {
  uid: number;
  name: string;
  url: string;
  [key: string]: any;
}

interface UserInfo {
  custName?: string;
  [key: string]: any;
}

const store = useUserStore();
const user = computed<UserInfo>(() => store.user);

const search = reactive<SearchForm>({
  moduleName: '',
  areaName: '',
  rangMonth: '',
  name: ''
});

const tableData = ref<TableItem[]>([]);
const moduleList = ref<ModuleItem[]>([]);
const chooseNode = ref<TreeNode[]>([]);
const linkId = ref(null);
const loading = ref(false);
const moduleName = ref('');
const qlrGroupLinkId = ref(null);
const monthTimestamps = ref([]);
const tableKey = ref(0);
const baseUrl = import.meta.env.VITE_APP_BASE_API || '';
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};
const fit = ref('cover');
const token = ref(getToken());
const batchGroup = ref<{ id: number; linkId: number } | null>(null);
const batchNode = ref(null);
const fjField = reactive<FjField>({
  attribution: {
    picNum: 10
  }
});
const addMsg = reactive({
  FJ: [] as FileItem[]
});
const onlyAccpt = ref([]);
const multipleSelection = ref<any[]>([]);
const cqgdfBillGroup = ref<{ id: number; linkId: number } | null>(null);
const projectMsg = ref<ProjectMsg>({
  id: 0,
  parcelName: '',
  ruleAttribution: null,
  ruleId: 0
});
const multipleTable = ref();
const treeNode = computed(() => chooseNode.value[0]);

// 注入父组件提供的方法
const changeType = inject<(type: number, obj?: any) => void>('changeType');

const goBack = () => {
  if (changeType) {
    changeType(1);
  } else {
    console.error('无法访问父组件方法 changeType');
  }
};

const getProjectNode = (id: number) => {
  const params = {
    areaCode: '',
    ifCheck: false,
    moduleId: id,
    pageNum: 1,
    pageSize: 10,
    ifTree: true
  };

  getPlaceList(params).then((res: any) => {
    if (res.code === 200) {
      for (let i = 0; i < res.data.list.length; i++) {
        if (res.data.list[i].parcelName === '项目资料') {
          projectMsg.value = res.data.list[i];
          break;
        }
      }
    }
  });
};

const getData = () => {
  getModuleList([1] as unknown as string[]).then(async (res: any) => {
    if (res.code === 200) {
      moduleList.value = res.data;
      moduleChange(res.data[0].id);
      search.moduleName = res.data[0].id;
      getProjectNode(res.data[0].id);
    }
  });
};

const handleSearch = async () => {
  if (!treeNode.value) {
    ElMessage.warning('请选择项目');
    return;
  }

  if (!search.rangMonth) {
    ElMessage.warning('请输入月份');
    return;
  }

  const monthNum = parseInt(search.rangMonth);
  if (isNaN(monthNum)) {
    ElMessage.warning('月份必须为数字');
    return;
  }

  search.rangMonth = monthNum.toString();

  const selectedNode = treeNode.value;
  if (!selectedNode) return;

  if (search.areaName) {
    selectedNode.parcelName = search.areaName;
  }

  const targetNode = selectedNode?.children?.children?.children?.children as any;
  if (targetNode) {
    targetNode.needData = true;

    const conditionFields = [];
    if (search.name) {
      conditionFields.push({
        linkId: linkId.value,
        name: 'QLRXX_0',
        operator: '=',
        relation: 'and',
        type: 1,
        value: [search.name]
      });
    } else {
      conditionFields.push({
        linkId: linkId.value,
        name: 'QLRXX_0',
        operator: 'is not null',
        relation: 'and',
        type: 1
      });
    }
    if (conditionFields.length !== 0) {
      targetNode.conditionFields = conditionFields;
    }
  }

  loading.value = true;
  selectParcelFromTree(selectedNode).then((res) => {
    loading.value = false;
    if (res.code == 200) {
      tableData.value = [];
      tableKey.value += 1;
      const list: any[] = [];
      res.data.forEach((v: any) => {
        v.list.forEach((q: any) => {
          q.list.forEach((k: any) => {
            k.list.forEach((o: any) => {
              o.list.forEach((w: any) => {
                w.areaName = v.parcelName;
                list.push(w);
              });
            });
          });
        });
      });
      list.forEach((v, vdx) => {
        const obj: any = {
          moduleName: moduleName.value,
          qlrName: '',
          idCard: '',
          phone: '',
          areaName: v.areaName,
          ffgdfok: [],
          nowgdf: '',
          countParameter: {
            FWYSRQ: '',
            GDMJ: '',
            GDQX: ''
          },
          yffgdf: '',
          isFfok: false,
          id: v.id,
          parcelName: v.parcelName,
          ruleAttribution: v.ruleAttribution,
          ruleId: v.ruleId
        };
        for (let i = 0; i < v.fieldInstanceModels.length; i++) {
          if (v.fieldInstanceModels[i].groupName == '权利人') {
            obj.qlrName = v.fieldInstanceModels[i].attribution.QLRXX_0 || '';
            obj.idCard = v.fieldInstanceModels[i].attribution.QLRXX_5 || '';
            obj.phone = v.fieldInstanceModels[i].attribution.LXDH || '';
            break;
          }
        }
        v.fieldInstanceModels.forEach((v: any) => {
          if (v.groupName == '过渡费支付') {
            obj.ffgdfok.push({
              'ZFJE': v.attribution.ZFJE,
              'ZFRQ': v.attribution.ZFRQ
            });
          }
        });
        if (obj.ffgdfok.length != 0) {
          let yffgdf = 0;
          obj.ffgdfok.forEach((v: any) => {
            yffgdf = yffgdf + Number(v.ZFJE);
          });
          obj.yffgdf = yffgdf.toString();
        }
        for (let i = 0; i < v.fieldInstanceModels.length; i++) {
          if (v.fieldInstanceModels[i].groupName == '超期过渡费') {
            obj.countParameter.FWYSRQ = v.fieldInstanceModels[i].attribution.FWYSRQ;
            obj.countParameter.GDMJ = v.fieldInstanceModels[i].attribution.GDMJ;
            obj.countParameter.GDQX = v.fieldInstanceModels[i].attribution.GDQX;
            break;
          }
        }
        const money = handleResultMap(monthNum, Number(obj.countParameter.GDMJ));
        obj.nowgdf = money.toString();
        let flg = false;
        for (let i = 0; i < obj.ffgdfok.length; i++) {
          if (areInSameMonth(monthNum, Number(obj.ffgdfok[i].ZFRQ))) {
            flg = true;
            break;
          }
        }
        obj.isFfok = flg;
        tableData.value.push(obj);
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const areInSameMonth = (timestamp1: number, timestamp2: number) => {
  const date1 = new Date(timestamp1);
  const date2Obj = new Date(timestamp2);

  const year1 = date1.getFullYear();
  const month1 = date1.getMonth();
  const year2 = date2Obj.getFullYear();
  const month2 = date2Obj.getMonth();

  return year1 === year2 && month1 === month2;
};

const handleResultMap = (monthNum: number, areaSize: number) => {
  let result = 0;
  if (monthNum === 1) {
    result = Number((areaSize * 11).toFixed(2));
  } else if (monthNum === 2) {
    result = Number((areaSize * 12.1).toFixed(2));
  } else if (monthNum === 3) {
    result = Number((areaSize * 13.31).toFixed(2));
  } else if (monthNum === 4) {
    result = Number((areaSize * 14.64).toFixed(2));
  } else if (monthNum === 5) {
    result = Number((areaSize * 16.1).toFixed(2));
  } else if (monthNum === 6) {
    result = Number((areaSize * 17.71).toFixed(2));
  } else if (monthNum === 7) {
    result = Number((areaSize * 19.48).toFixed(2));
  } else if (monthNum === 8) {
    result = Number((areaSize * 21.43).toFixed(2));
  } else if (monthNum === 9) {
    result = Number((areaSize * 23.57).toFixed(2));
  } else if (monthNum === 10) {
    result = Number((areaSize * 25.93).toFixed(2));
  } else if (monthNum === 11) {
    result = Number((areaSize * 28.52).toFixed(2));
  } else if (monthNum === 12) {
    result = Number((areaSize * 31.37).toFixed(2));
  } else if (monthNum === 13) {
    result = Number((areaSize * 34.51).toFixed(2));
  } else if (monthNum === 14) {
    result = Number((areaSize * 37.96).toFixed(2));
  } else if (monthNum === 15) {
    result = Number((areaSize * 41.76).toFixed(2));
  } else if (monthNum === 16) {
    result = Number((areaSize * 45.94).toFixed(2));
  } else if (monthNum >= 17) {
    result = Number((areaSize * 50).toFixed(2));
  }
  return result;
};

const calculateMonthsDifference = (specifiedMonthTimestamp: string | number, houseAcceptanceTimestamp: number): number => {
  const monthDate = new Date(Number(specifiedMonthTimestamp));
  const inspectionDate = new Date(houseAcceptanceTimestamp);

  const twoYearsLater = new Date(inspectionDate);
  twoYearsLater.setDate(1);
  twoYearsLater.setFullYear(inspectionDate.getFullYear() + 2);

  let monthDifference = 0;
  const currentDate = new Date(twoYearsLater);

  while (currentDate < monthDate) {
    currentDate.setMonth(currentDate.getMonth() + 1);
    monthDifference++;
  }

  return monthDifference > 0 ? monthDifference : 0;
};

const handleReset = () => {
  search.rangMonth = '';
  search.moduleName = '';
  search.areaName = '';
  search.name = '';
};

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

const moduleChange = (e: any) => {
  for (let i = 0; i < moduleList.value.length; i++) {
    if (moduleList.value[i].id == e) {
      moduleName.value = moduleList.value[i].moduleName;
      break;
    }
  }
  selectRules({ moduleId: e }).then((res) => {
    if (res.code == 200) {
      for (let i = 0; i < res.data.length; i++) {
        if (res.data[i].typeName == '被征收区域') {
          chooseNode.value = simplifyTree([res.data[i]]);
          setConditionRule([res.data[i]]);
          break;
        }
      }
      for (let i = 0; i < res.data.length; i++) {
        if (res.data[i].typeName == '项目文件') {
          batchNode.value = res.data[i];
          for (let j = 0; j < res.data[i].fieldGroupModelList.length; j++) {
            if (res.data[i].fieldGroupModelList[j].typeName == '批次信息') {
              batchGroup.value = res.data[i].fieldGroupModelList[j];
              for (let o = 0; o < res.data[i].fieldGroupModelList[j].fieldModelList.length; o++) {
                if (res.data[i].fieldGroupModelList[j].fieldModelList[o].valueMethod == 'xtfj') {
                  fjField.attribution.acceptType = res.data[i].fieldGroupModelList[j].fieldModelList[o].attribution.acceptType.join(',');
                  onlyAccpt.value = res.data[i].fieldGroupModelList[j].fieldModelList[o].attribution.acceptType.join(',');
                  break;
                }
              }
              break;
            }
          }
          break;
        }
      }
    }
  });
};

const setConditionRule = (list: any[]) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].typeName == '被征收户') {
      for (let j = 0; j < list[i].fieldGroupModelList.length; j++) {
        if (list[i].fieldGroupModelList[j].typeName == '权利人') {
          linkId.value = list[i].fieldGroupModelList[j].linkId;
          break;
        }
      }
      for (let j = 0; j < list[i].fieldGroupModelList.length; j++) {
        if (list[i].fieldGroupModelList[j].typeName == '过渡费支付') {
          cqgdfBillGroup.value = list[i].fieldGroupModelList[j];
          break;
        }
      }
      break;
    }
    if (list[i].list.length != 0) {
      setConditionRule(list[i].list);
    }
  }
};

const simplifyTree = (tree: any[]): TreeNode[] => {
  return tree.map((node) => {
    const simplifiedNode: any = {
      parcelName: node.parcelName || '',
      ruleId: node.id,
      needData: false
    };
    if (node.list && node.list.length > 0) {
      const item = simplifyTree(node.list);
      if (item[0]) {
        simplifiedNode.children = item[0];
      }
    }
    return simplifiedNode;
  });
};

const submit = () => {
  let moduleName = '';
  for (let i = 0; i < moduleList.value.length; i++) {
    if (moduleList.value[i].id == search.moduleName) {
      moduleName = moduleList.value[i].moduleName;
      break;
    }
  }
  if (multipleSelection.value.length == 0) {
    ElMessage.error('请选择需要发放的人员！！！');
    return;
  }
  const nowBtch = `${Date.now()}${Math.floor(Math.random() * 1000000)}`;
  const btchItem = {
    appType: 2,
    id: projectMsg.value.id,
    parcelName: projectMsg.value.parcelName,
    ruleAttribution: projectMsg.value.ruleAttribution,
    ruleId: projectMsg.value.ruleId,
    fieldInstanceModels: [
      {
        appId: 0,
        attribution: {
          batch: nowBtch,
          FJ: addMsg.FJ,
          remark: '',
          xmmc: moduleName,
          xqmc: search.areaName,
          rq: search.rangMonth,
          czr: user.value?.custName || ''
        },
        groupId: batchGroup.value?.id || 0,
        linkId: batchGroup.value?.linkId || 0,
        parcelLinkId: 0
      }
    ]
  };
  const cqgdfItems: any[] = [];
  multipleSelection.value.forEach((v) => {
    const ite = {
      appType: 2,
      id: v.id,
      parcelName: v.parcelName,
      ruleAttribution: v.ruleAttribution,
      ruleId: v.ruleId,
      fieldInstanceModels: [
        {
          appId: 0,
          attribution: {
            batch: nowBtch,
            ZFJE: v.nowgdf,
            ZFRQ: Date.now(),
            zq: search.rangMonth
          },
          groupId: cqgdfBillGroup.value?.id || 0,
          linkId: cqgdfBillGroup.value?.linkId || 0,
          parcelLinkId: 0
        }
      ]
    };
    cqgdfItems.push(ite);
  });
  saveSimple([btchItem]).then((res) => {
    if (res.code == 200) {
      saveSimple(cqgdfItems).then((resp) => {
        if (resp.code == 200) {
          ElMessage({
            type: 'success',
            message: '发放成功'
          });
          multipleTable.value.clearSelection();
          goBack();
        }
      });
    }
  });
};

const handleSuccessFJ = (response: any, item: any) => {
  if (response.data && isArray(response.data)) {
    const obj: FileItem = {
      uid: Date.now(),
      name: response.data[0].name,
      url: response.data[0].path
    };
    obj[`${item.fieldName}_0`] = response.data[0].path;
    obj[`${item.fieldName}_1`] = response.data[0].name;
    addMsg.FJ.push(obj);
  }
};

const handleRemoveFJ = (file: any, item: any) => {
  let num = 0;
  if (item.content && isArray(item.content)) {
    for (let index = 0; index < item.content.length; index++) {
      if (file.response.data[0].path == item.content[index].url) {
        num = index;
        break;
      }
    }
    item.content.splice(num, 1);
  }
};

const beforeAvatarUpload = (file: any, item: any) => {
  const type = getFileType(file.type);
  let flg = false;
  for (let index = 0; index < item.attribution.acceptType.length; index++) {
    if (type.includes(item.attribution.acceptType[index])) {
      flg = true;
      break;
    }
  }
  if (!flg) {
    ElMessage.error(`不支持上传${file.type}格式！！！`);
  }
  return flg;
};

const handleExceed = (files: any, fileList: any, item: any) => {
  if (fileList.length >= item.attribution.picNum) {
    ElMessage.error(`${item.fieldCn}最多允许上传${item.attribution.picNum}`);
  }
};

const handleRemove = (file: any) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      for (let i = 0; i < addMsg.FJ.length; i++) {
        if (addMsg.FJ[i].uid == file.uid) {
          addMsg.FJ.splice(i, 1);
          break;
        }
      }
    })
    .catch(() => {});
};

const selectable = (row: TableItem, rowIndex: number): boolean => {
  if (row.isFfok) {
    return !!row.disabled;
  }
  return !row.disabled;
};

onMounted(() => {
  getData();
});

defineExpose({
  multipleTable
});
</script>

<style lang="scss" scoped>
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
}
.fj-row :hover {
  color: #409eff;
}
.add-main {
  width: 100%;
  height: 100%;
  .handle-title {
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: rgba(0, 0, 0, 0.1) solid 1px;
  }
  .handle-search {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 10px;
    .item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }
      .right {
        flex: 1;
      }
    }
  }
  .jf-box {
    width: 100%;
    height: 200px;
    border: rgba(0, 0, 0, 0.1) solid 1px;
    margin: 10px 0px;
    .fj-title {
      padding: 6px 10px;
      color: #409eff;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
