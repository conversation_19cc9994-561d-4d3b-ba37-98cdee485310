<!-- 选择字段的弹框 -->
<template>
  <div>
    <div>
      <el-dialog
        title="选择字段"
        v-model="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        @close="handleClose"
        @open="handleOpen"
        width="874px"
      >
        <div class="dialog-row">
          <div class="dialog-label">字段中文</div>
          <el-input placeholder="请输入字段中文" v-model="queryParams.fieldCn" size="small" style="width: 216px; margin-right: 24px"></el-input>
          <div class="dialog-label">字段英文</div>
          <el-input placeholder="请输入字段英文" v-model="queryParams.fieldName" size="small" style="width: 216px; margin-right: 24px"></el-input>
          <el-button type="primary" :icon="Search" size="small" @click="getList">查询</el-button>
          <el-button size="small" :icon="RefreshRight" @click="resetQuery">重置</el-button>
        </div>
        <el-table
          ref="multipleTableRef"
          :data="fieldData"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClickName"
          row-key="id"
        >
          <el-table-column type="selection" reserve-selection width="55"> </el-table-column>
          <el-table-column label="字段中文名" prop="fieldCn"> </el-table-column>
          <el-table-column label="字段英文名" prop="fieldName"> </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: space-between">
          <div style="padding: 32px 16px">
            <span>已选择</span><span style="color: var(--current-color); padding-left: 4px">{{ multipleSelection.length }}</span>
          </div>
          <div>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              :pageSizes="pageSizes"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="handleSubmitSelected">确 认</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { Search, RefreshRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { TableInstance } from 'element-plus';
import { getfieldList } from '@/api/fieldManagement';

interface FieldItem {
  id: number | string;
  fieldCn: string;
  fieldName: string;
  flag?: boolean;
  [key: string]: any;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  fieldCn: string;
  fieldName: string;
}

interface Props {
  selectFielsVisibleDialog: boolean;
  appType: number;
  fieldCate: number;
  fieldList: (number | string)[];
}

const props = withDefaults(defineProps<Props>(), {
  selectFielsVisibleDialog: false,
  appType: 7,
  fieldCate: 13,
  fieldList: () => []
});

const emit = defineEmits<{
  (e: 'closeField'): void;
  (e: 'selectedField', fields: FieldItem[]): void;
}>();

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.selectFielsVisibleDialog,
  set: () => {
    // 只保留get逻辑，set通过事件处理
  }
});

// 表格引用
const multipleTableRef = ref<TableInstance>();
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  fieldCn: '',
  fieldName: ''
});

const total = ref(0);
const multipleSelection = ref<FieldItem[]>([]);
const pageSizes = [10, 50, 100, 200, 1000, 2000];
const fieldData = ref<FieldItem[]>([]);

// 重置查询
const resetQuery = () => {
  queryParams.fieldCn = '';
  queryParams.fieldName = '';
  getList();
};

/**
 * 获取字段列表
 * @param appType 应用类型
 * @param pageNum 页码
 * @param pageSize 每页条数
 * @param fieldCn 字段中文
 * @param fieldName 字段英文
 * @param fieldCate 字段分类
 */
const getList = () => {
  const params = {
    appType: props.appType, // 1 农林一体 2 林业调查
    pageNum: queryParams.pageNum,
    pageSize: queryParams.pageSize,
    fieldCn: queryParams.fieldCn,
    fieldName: queryParams.fieldName,
    fieldCate: props.fieldCate
  };

  getfieldList(params).then((res) => {
    if (res.code == 200) {
      fieldData.value = res.data.rows;
      total.value = res.data.total;
      toggleSelectionItem();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 回显数据
const toggleSelectionItem = () => {
  const ids = props.fieldList;
  const list = fieldData.value || [];
  const rowItem = list.filter((item) => {
    return ids.indexOf(item.id) != -1;
  });

  nextTick(() => {
    if (multipleTableRef.value) {
      multipleTableRef.value.clearSelection();
      rowItem.forEach((row) => {
        multipleTableRef.value?.toggleRowSelection(row, true);
      });
    }
  });
};

// 关闭对话框
const handleClose = () => {
  emit('closeField');
};

// 打开对话框
const handleOpen = () => {
  getList();
};

/**
 * 批量选择
 * @param val 字段列表
 */
const handleSelectionChange = (val: FieldItem[]) => {
  multipleSelection.value = val;
};

/**
 * 行点击处理
 * @param row 字段
 */
const handleRowClickName = (row: FieldItem) => {
  row.flag = !row.flag;
  multipleTableRef.value?.toggleRowSelection(row, row.flag);
};

/**
 * 确定提交选择的字段
 */
const handleSubmitSelected = () => {
  emit('selectedField', multipleSelection.value);
};

/**
 * 监听props变化, 重新获取数据
 * @param newVal 新值
 */
watch(
  () => props.selectFielsVisibleDialog,
  (newVal) => {
    if (newVal) {
      getList();
    }
  }
);
</script>

<style lang="scss" scoped>
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .dialog-label {
    color: #161d26;
    margin-right: 10px;
  }
}
</style>
