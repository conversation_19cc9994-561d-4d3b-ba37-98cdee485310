<!-- 这是一个文件的展示组件，图片显示略缩图，图片可以查看大图，其他文件可以预览 图片在上其他文件在下方 -->
<template>
  <div class="file-list-container">
    <!-- 图片展示区 -->
    <!-- <div class="file-item-pic" v-if="imageFiles.length > 0">
      <div v-for="(item, index) in imageFiles" :key="index" class="image-item">
        <auth-img
          class="img"
          :authSrc="`${baseUrl}${item.url}`"
          :width="'100px'"
          :height="'100px'"
          :radios="'12px'"
        ></auth-img>
        <div
          class="image-delete"
          @click="handleDeletefile(item)"
          v-if="isDeleteBtn"
        >
          <i class="el-icon-remove-outline"></i>
        </div>
      </div>
    </div> -->

    <!-- PDF/Excel 文件展示区 -->
    <div class="file-item-else" v-if="documentFiles.length > 0">
      <div v-for="(item, index) in documentFiles" :key="index" class="document-item">
        <div class="item-left">
          <span style="padding-right: 8px">
            <el-icon><Document /></el-icon>
          </span>
          <span>{{ item.urlName }}</span>
        </div>
        <div class="item-right">
          <div class="btn-a" style="color: #00d689" @click="handlePreviewFile(item)">预览</div>
          <div class="btn-a" style="color: #227dff" @click="handleDownLoad(item)">下载</div>
          <div class="btn-a" style="color: red" @click="handleDeletefile(item)" v-if="isDeleteBtn">删除</div>
        </div>
      </div>
    </div>
    <!-- 添加文件预览的内容 -->
    <preview-file ref="previewFileRef"></preview-file>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import authImg from './authImg.vue';
import { downLoadFile } from '@/utils/publicFun';
import previewFile from './previewFile.vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';

// 定义文件接口
interface FileItem {
  urlName: string;
  filesUrl: string;
  delFlag?: number;
  name?: string;
  url?: string;
  [key: string]: any;
}

// 定义Props
interface Props {
  fileList: FileItem[];
  isDeleteBtn?: boolean;
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  fileList: () => [],
  isDeleteBtn: true
});

// 定义emit
const emit = defineEmits<{
  (e: 'deletefile', item: FileItem): void;
}>();

// 引用元素
const previewFileRef = ref();

// 状态
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
const fileName = ref('');
const fileUrl = ref('');
const isDownLoadFile = ref(false);

// 计算属性
// 分离图片和其他文档
const imageFiles = computed(() => {
  return props.fileList.filter((file) => {
    let picType: string | undefined = undefined;
    const path = file.filesUrl;
    picType = path.substring(path.lastIndexOf('.') + 1, path.length);
    file.name = `${file.urlName}`;
    file.url = file.filesUrl;
    // !file.delFlag 代表是新上传的内容 这个时候是undefined
    return ['png', 'jpeg', 'jpg'].includes(picType) && (file.delFlag === 0 || !file.delFlag);
  });
});

const documentFiles = computed(() => {
  return props.fileList.filter((file) => {
    file.name = `${file.urlName}`;
    file.url = `${file.filesUrl}`;
    return file.delFlag === 0 || file.delFlag === undefined;
  });
});

// 方法
// 预览查看附件dialog
const handlePreviewFile = (obj: FileItem) => {
  const item = {
    ...obj,
    name: obj.urlName,
    url: obj.filesUrl
  };
  previewFileRef.value.handlePreviewFile(item);
};

// 关闭预览
const handleCloseFj = () => {
  // 此方法不再需要但保留以兼容
};

// 下载
const handleDownLoad = (item: FileItem) => {
  fileName.value = item.name || '';
  fileUrl.value = item.url || '';
  const text = `是否确认下载【${item.name}】文件`;

  ElMessageBox.confirm(text, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      handleDownLoadFile();
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 确认下载文件的按钮
const handleDownLoadFile = async () => {
  await downLoadFile(fileUrl.value, fileName.value);
  await handleCancleDownLoad();
};

// 取消下载内容
const handleCancleDownLoad = async () => {
  isDownLoadFile.value = false;
  fileName.value = '';
  fileUrl.value = '';
};

// 删除新生成的某一个文件
const handleDeletefile = (item: FileItem) => {
  emit('deletefile', item);
};
</script>

<style lang="scss" scoped>
.file-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  .file-item-pic {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 10px; /* 项目之间的间距 */

    .image-item {
      position: relative;
      &:hover {
        .image-delete {
          display: block;
        }
      }
      img {
        max-width: 100%;
        height: auto;
      }
      .image-delete {
        display: none;
        position: absolute;
        right: -4px;
        top: -8px;
        width: 20px;
        height: 20px;
        cursor: pointer;
        i {
          font-size: 20px;
          color: red;
        }
      }
    }
  }
  // 除图片以外的其他样式
  .file-item-else {
    margin-top: 20px;
    .document-item {
      margin-bottom: 10px;
      display: flex;
      width: calc(100% - 16px);
      background-color: #f7f7f7;
      align-content: center;
      align-items: center;
      margin: 8px;
      border-radius: 8px;
      justify-content: space-evenly;
      height: 44px;
      padding: 8px;
      &:first-child {
        margin: 0 8px;
      }
      .item-left {
        flex: 2;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 14px;
        font-weight: 600;
      }
      .item-right {
        display: flex;
        justify-content: flex-end;
        justify-items: center;
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        .btn-a {
          cursor: pointer;
          margin: 0 6px;
          width: 30px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
