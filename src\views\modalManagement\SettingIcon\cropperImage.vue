<template>
  <div>
    <!-- <div class="user-info-head" @click="editCropper()">
      <img v-bind:src="options.img" title="自定义图标" class="img-circle img-lg" /></div> -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body @opened="modalOpened" @close="closeDialog" :close-on-click-modal="false">
      <el-row>
        <el-col :xs="24" :md="12" :style="{ height: '200px' }">
          <vue-cropper
            ref="cropperRef"
            :img="options.img"
            :info="true"
            :autoCrop="options.autoCrop"
            :autoCropWidth="options.autoCropWidth"
            :autoCropHeight="options.autoCropHeight"
            :fixedBox="options.fixedBox"
            :outputType="options.outputType"
            @realTime="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{ height: '200px' }">
          <div class="cropper-upload-preview">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload action="#" :http-request="requestUpload" :show-file-list="false" :before-upload="beforeUpload">
            <el-button size="small">
              选择
              <el-icon class="el-icon--right"><UploadFilled /></el-icon>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{ span: 1, offset: 2 }" :sm="2" :xs="2">
          <el-button :icon="Plus" size="small" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button :icon="Minus" size="small" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button :icon="RefreshLeft" size="small" @click="rotateLeft()"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button :icon="RefreshRight" size="small" @click="rotateRight()"></el-button>
        </el-col>
        <el-col :lg="{ span: 2, offset: 6 }" :sm="2" :xs="2">
          <el-button type="primary" size="small" @click="handleCorpperImg()">提 交</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { VueCropper } from 'vue-cropper';
import 'vue-cropper/dist/index.css';
import { uploadCopperImg } from '@/api/modal/index';
import { debounce } from '@/utils';
import { useModalStore } from '@/store/modules/modal';
import { UploadFilled, Plus, Minus, RefreshLeft, RefreshRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// 引入组件
defineOptions({
  components: { VueCropper }
});

// 定义 props
const props = defineProps<{
  user: Record<string, any>;
  iconName: string;
}>();

// 获取 store
const userStore = useUserStore();
const modalStore = useModalStore();

// 定义响应式数据
const open = ref(false);
const visible = ref(false);
const title = ref('设置图标');
const options = ref({
  img: modalStore.cropperIcon,
  autoCrop: true,
  autoCropWidth: 56,
  autoCropHeight: 56,
  fixedBox: true,
  outputType: 'png',
  name: ''
});
const previews = ref({});
const resizeHandler = ref<(() => void) | null>(null);
const base = ref(import.meta.env.VITE_APP_BASE_API);
const cropperRef = ref<InstanceType<typeof VueCropper> | null>(null);

// 编辑头像
const editCropper = () => {
  open.value = true;
};

// 打开弹出层结束时的回调
const modalOpened = () => {
  visible.value = true;
  if (!resizeHandler.value) {
    resizeHandler.value = debounce(() => {
      refresh();
    }, 100);
  }
  window.addEventListener('resize', resizeHandler.value);
};

// 刷新组件
const refresh = () => {
  if (cropperRef.value) {
    cropperRef.value.refresh();
  }
};

// 覆盖默认的上传行为
const requestUpload = () => {};

// 向左旋转
const rotateLeft = () => {
  if (cropperRef.value) {
    cropperRef.value.rotateLeft();
  }
};

// 向右旋转
const rotateRight = () => {
  if (cropperRef.value) {
    cropperRef.value.rotateRight();
  }
};

// 图片缩放
const changeScale = (num: number = 1) => {
  if (cropperRef.value) {
    cropperRef.value.changeScale(num);
  }
};

// 上传预处理
const beforeUpload = (file: File) => {
  if (file.type.indexOf('image/') === -1) {
    // 假设 this.$modal 是全局方法，这里可根据实际情况修改
    (window as any).$modal.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。');
  } else {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (reader.result) {
        options.value.img = reader.result as string;
        options.value.name = file.name;
      }
    };
  }
};

// 确认提交剪裁的图标
const handleCorpperImg = () => {
  if (cropperRef.value) {
    cropperRef.value.getCropBlob((data: Blob) => {
      const formData = new FormData();
      formData.append('files', data);
      uploadCopperImg(formData).then((response) => {
        open.value = false;
        options.value.img = response.data[0].path;
        // 将数据拼凑成一个对象
        const obj = {
          iconName: props.iconName,
          selected: false,
          name: options.value.img
        };
        modalStore.setCustomPic(obj);
        if (props.iconName === '自定义照片') {
          sessionStorage.setItem('customPic', JSON.stringify(obj));
        } else if (props.iconName === '模块自定义照片') {
          sessionStorage.setItem('modalCustomPic', JSON.stringify(obj));
        } else if (props.iconName === '属性组自定义照片') {
          sessionStorage.setItem('GroupCustomPic', JSON.stringify(obj));
        } else if (props.iconName === '树图标自定义照片') {
          sessionStorage.setItem('TreeCustomPic', JSON.stringify(obj));
        }
        // 假设 this.$modal 是全局方法，这里可根据实际情况修改
        // (window as any).$modal.msgSuccess('提交成功');
        ElMessage({
          message: '提交成功',
          type: 'success'
        });
        visible.value = false;
      });
    });
  }
};

// 实时预览
const realTime = (data: any) => {
  previews.value = data;
};

// 关闭窗口
const closeDialog = () => {
  options.value.img = modalStore.cropperIcon;
  visible.value = false;
  if (resizeHandler.value) {
    window.removeEventListener('resize', resizeHandler.value);
  }
};
// 暴露方法，让父组件可以访问
defineExpose({
  editCropper
});
</script>

<style scoped lang="scss">
.cropper-upload-preview {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 56px;
  height: 56px;
  border-radius: 4px;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  border-radius: 50%;
}
</style>
