<!-- 导出设置 -->
<template>
  <div class="addExport-main">
    <div class="content">
      <div class="left">
        <div class="back">
          <el-link type="info" @click="back"> <i class="el-icon-arrow-left"></i> 返回导出功能列表</el-link>
        </div>
        <div class="menu" v-for="(item, index) in menu" :key="index" :class="{ 'active': item.checked }" @click="changeTab(item)">
          <span style="margin-left: 24px">{{ item.label }}</span>
        </div>
      </div>
      <div class="right">
        <!-- 导出报告 -->
        <div v-show="menu[0].checked">
          <!-- :detail="exportMsg.detail" -->
          <exportTemp
            :exportType="exportMsg.exportType"
            :detailInfo="exportMsg.detail"
            @changeExportType="changeExportType"
            @editTreeDetail="editTreeDetail"
            @editTreeGroupInfo="editTreeGroupInfo"
            :getDetailToParent="getDetailToParent"
            ref="exportTempRef"
          ></exportTemp>
        </div>
        <!-- 坐标系统 -->
        <div v-show="menu[1].checked">
          <exportZBSys :zbsys="exportMsg.coordinate" ref="childZBXT"></exportZBSys>
        </div>
        <!-- 导出面积单位 -->
        <!-- <template v-if="menu[2].checked">
          <exportMJDW :mjdw="exportMsg.mjdw"></exportMJDW>
        </template> -->
        <!-- 数据列表 -->
        <div v-show="menu[2].checked">
          <exportData :sjList="exportMsg.dataList" ref="childData"></exportData>
        </div>
        <!-- 导出矢量 -->
        <!-- <div v-show="menu[3].checked">
          <exportSL
          :fileSwitch="exportMsg.fileSwitch"
          :downTree="exportMsg.detail"
          @submitSL="submitSL"
          ref="exportSL"></exportSL>
        </div> -->
        <!-- 面积计算方式 -->
        <div v-show="menu[3].checked" style="padding: 20px 16px">
          <div class="title" style="margin-bottom: 16px">面积计算方式</div>
          <el-radio-group v-model="exportMsg.areaType">
            <el-radio :value="1">投影坐标系</el-radio>
            <el-radio :value="2">大地坐标系</el-radio>
          </el-radio-group>
        </div>
        <!-- 变量设置 -->
        <div v-show="menu[4].checked" style="padding: 20px 16px">
          <div class="title" style="margin-bottom: 16px">变量设置</div>
          <el-link type="primary" @click="addVariable">新增变量</el-link>
          <div class="flex-row" v-for="(item, index) in variableList" :key="index">
            <div class="flex-item">
              <el-tooltip class="item" effect="dark" content="必须用$开头并且是英文字符" placement="top">
                <div class="lable">变量key</div>
              </el-tooltip>
              <div class="end">
                <el-input v-model="item.key" placeholder="请输入变量key" @input="handleInput($event, index)"></el-input>
              </div>
            </div>
            <div class="flex-item" style="margin-top: 10px">
              <div class="lable">表达式</div>
              <div class="end">
                <el-input v-model="item.expression" placeholder="请输入表达式" @click.native="openExpress(item, index)"></el-input>
              </div>
            </div>
            <el-link type="danger" class="del-link" @click="delVariable(index)">删除</el-link>
          </div>
        </div>
      </div>
      <div class="finish-btn">
        <el-button type="primary" size="small" @click="endSubmit">完成</el-button>
      </div>
    </div>
    <!-- 打开公式编辑的弹框 -->
    <!-- <formula-editing-dialog
      :formulaVisible="formulaVisible"
      :expression="expression"
      :isCopy="true"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
    ></formula-editing-dialog> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import exportData from './exportData/index.vue';
import exportTemp from './exportTemp/index.vue';
import exportZBSys from './exportZBSys/index.vue';
import { saveExportSetting, getExportDetail } from '@/api/modal';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRoute } from 'vue-router';

const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();

// 定义 props
const props = defineProps<{
  exportTitle?: string;
}>();

// 定义 emits
const emit = defineEmits(['backList']);

// 定义响应式数据
const menu = reactive([
  { label: '导出报告', checked: true },
  { label: '坐标系统', checked: false },
  // { label: '导出面积单位', checked: false },
  { label: '数据列表', checked: false },
  // { label: '导出矢量', checked: false }
  { label: '面积计算方式', checked: false },
  { label: '变量设置', checked: false }
]);

const exportMsg = reactive({
  exportName: props.exportTitle, // 导出按钮名
  exportType: 2, // 导出报告类型 1自定义 2文件树结构
  coordinate: {}, // 坐标系统 json字符串
  dataList: {}, // 数据列表 json字符串
  detail: {
    checked: {}, // 属性映射的列表  包含图片的映射源
    fileName: '文件夹', // 文件名
    fileType: 0, // 0,文件夹 1,word,2,excel,3,gdb,4,shp
    list: [],
    ifFolder: 1, // 是否文件夹 1是 2不是
    vector: {}, // 矢量配置 json
    ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8) // 用于删除处理
  }, // 文件结构树
  moduleId: modalStore.moduleId,
  areaType: 1, // 新增面积计算方式默认值
  variable: {} // 新增变量对象
});

const variableList = ref<any[]>([]);
const formulaVisible = ref(false);
const expression = ref('');
const appType = ref('#word');
const activeVariable = ref<any>(null);

// 定义 ref
const exportTempRef = ref<any>(null);
const childZBXT = ref<any>(null);
const childData = ref<any>(null);

// 定义方法
const getNowData = (row: any) => {
  getExportDetail(row.id).then((res) => {
    if (res.code === 200) {
      Object.assign(exportMsg, res.data);
      if (Object.keys(res.data.variable).length !== 0) {
        const keys = Object.keys(res.data.variable);
        const expressions = Object.values(res.data.variable);
        variableList.value = [];
        keys.forEach((v, vdx) => {
          variableList.value.push({ key: v, expression: expressions[vdx] });
        });
      }
      // exportTempRef.value?.initDetail(res.data.detail);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const back = () => {
  emit('backList');
};

const changeTab = (item: any) => {
  menu.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
};

const submitSL = (obj: any, type: number) => {
  if (type === 1) {
    // exportMsg.exportSL.shp.list.push(obj);
  } else if (type === 2) {
    // exportMsg.exportSL.GDB.list.push(obj);
  }
};

const changeExportType = (val: number) => {
  exportMsg.exportType = val;
};

const setDetail = (list: any[], item: any) => {
  editNodeToDetail(list, item);
};

const editNodeToDetail = (list: any[], item: any) => {
  list.forEach((v) => {
    if (v.ruleId) {
      if (v.ruleId === item.ruleId) {
        Object.assign(v, item);
      }
    } else {
      if (v.id === item.id) {
        Object.assign(v, item);
      }
    }
    if (v.list) {
      editNodeToDetail(v.list, item);
    }
  });
};

const endSubmit = () => {
  const coordinate = childZBXT.value?.sumbit();
  const dataList = childData.value?.sumbit();
  exportMsg.coordinate = coordinate;
  exportMsg.dataList = dataList;
  const result = variableList.value.reduce((acc: any, obj: any) => {
    acc[obj.key] = obj.expression;
    return acc;
  }, {});
  exportMsg.variable = result;
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    exportMsg.companyId = companyId;
  }
  saveExportSetting(exportMsg).then((res) => {
    if (res.code === 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
      emit('backList');
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const submitZBXT = (obj: any) => {
  exportMsg.coordinate = obj;
};

const submitData = (obj: any) => {
  exportMsg.dataList = obj;
};

const init = () => {
  Object.assign(exportMsg, {
    exportName: props.exportTitle, // 导出按钮名
    exportType: 2, // 导出报告类型 1自定义 2文件树结构
    coordinate: {
      disable: 0,
      placeholder: '请选择坐标系统',
      must: 1,
      defaultValue: ['CGCS_2000', 'cgcs2000_3', 35]
    }, // 坐标系统 json字符串
    dataList: {
      placeholder: '请选择数据',
      minNum: '1',
      maxNum: '',
      must: '1'
    }, // 数据列表 json字符串
    detail: {
      checked: {}, // 属性映射的列表  包含图片的映射源
      fileName: '文件夹', // 文件名
      fileType: 0, // 0,文件夹 1,word,2,excel,3,gdb,4,shp
      list: [],
      ifFolder: 1, // 是否文件夹 1是 2不是
      vector: {}, // 矢量配置 json
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8) // 用于删除处理
    }, // 文件结构树
    moduleId: modalStore.moduleId,
    createTime: undefined,
    createUserId: undefined,
    id: undefined,
    mainFolder: undefined,
    updateTime: undefined,
    updateUserId: undefined
  });
};

const getDetailToParent = () => {
  return exportMsg;
};

const editTreeDetail = (detail: any) => {
  exportMsg.detail = detail;
};

const editTreeGroupInfo = (str: string) => {
  exportMsg.groupInfo = str;
};

const addVariable = () => {
  variableList.value.push({
    key: '',
    expression: ''
  });
};

const handleCloseFormulation = () => {
  formulaVisible.value = false;
  // store.commit('SET_IsAllGroup', false);
};

const handleSubmitFormulation = (expressionVal: string) => {
  if (activeVariable.value) {
    activeVariable.value.expression = expressionVal;
  }
};

const openExpress = (obj: any) => {
  activeVariable.value = obj;
  expression.value = obj.expression;
  formulaVisible.value = true;
};

const delVariable = (index: number) => {
  ElMessageBox.confirm('确定要删除该变量吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      variableList.value.splice(index, 1);
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {});
};

const handleInput = (value: string, index: number) => {
  // 正则表达式：第一个字符是$，后面是0个或多个英文或数字字符
  const regex = /^\$[a-zA-Z0-9]*$/;
  // 如果输入的内容不符合正则表达式，则进行修正
  if (!regex.test(value)) {
    // 如果第一个字符不是$，则修正为$
    if (value.length > 0 && value[0] !== '$') {
      variableList.value[index].key = '$' + value.slice(1).replace(/[^a-zA-Z0-9]/g, '');
    } else {
      // 如果第一个字符是$，但后续有非法字符，则移除非法字符
      variableList.value[index].key = value.replace(/^(\$[a-zA-Z0-9]*).*$/, '$1');
    }
  }
};

// 初始化
onMounted(() => {
  // 可根据需求添加初始化逻辑
});
defineExpose({
  getNowData,
  init
});
</script>
<style lang="scss" scoped>
.flex-row {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  border: #d3d3d3 solid 1px;
  padding: 22px 12px 12px 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  .flex-item {
    flex: 1;
    display: flex;
    align-items: center;
    .lable {
      width: 80px;
      text-align: right;
      font-size: 14px;
    }
    .end {
      margin-left: 10px;
      flex: 1;
    }
  }
  .del-link {
    position: absolute;
    top: 0;
    right: 5px;
  }
}
.flex-row:hover {
  border: #46a6ff solid 1px;
}
.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}
.addExport-main {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .content {
    width: 723px;
    height: 100%;
    overflow: auto;
    background: #fff;
    display: flex;
    flex-direction: row;
    position: relative;
    .left {
      width: 164px;
      height: 100%;
      border-right: 1px solid rgba(219, 231, 238, 1);
      .back {
        padding: 8px 10px;
      }
      .menu {
        width: 100%;
        height: 40px;
        font-size: 14px;
        color: #161d26;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .active {
        background-color: rgba(237, 244, 251, 1);
        color: var(--current-color);
        font-weight: bold;
      }
    }
    .right {
      flex: 1;
      height: 100%;
      overflow: auto;
      position: relative;
    }
    .finish-btn {
      position: absolute;
      bottom: 100px;
      right: 52px;
    }
  }
}
</style>
