<!-- 采集要素 -->
<template>
  <div class="acquire-element-setting-main">
    <div class="item-title">
      <div class="handle-title">
        <span class="text">采集要素设置</span>
      </div>
    </div>
    <div class="item-form">
      <el-form :model="elementForm" :rules="elementRules" ref="elementRulesRef">
        <el-row>
          <el-form-item label="要素类型选择" label-width="110px">
            <el-radio-group v-model="elementForm.graphicalType">
              <el-radio v-for="(item, index) in graphicalTypes" :key="index" :disabled="setVisble()" :value="item.value">{{ item.label }}</el-radio>
              <!-- <el-radio :disabled="setVisble()" v-model="elementForm.graphicalType" :value="1">点</el-radio>
              <el-radio :disabled="setVisble()" v-model="elementForm.graphicalType" :value="2">线</el-radio>
              <el-radio :disabled="setVisble()" v-model="elementForm.graphicalType" :value="3">面</el-radio>
              <el-radio :disabled="setVisble()" v-model="elementForm.graphicalType" :value="4">无图形</el-radio> -->
            </el-radio-group>
          </el-form-item>
        </el-row>
        <!-- 面 -->
        <template v-if="elementForm.graphicalType == 3">
          <div class="flex-content">
            <div class="flex-item">
              <el-form-item label="面填充色" label-width="110px">
                <!-- <el-popover placement="bottom" width="200" trigger="click">
                  <template v-slot:reference>
                    <el-button class="color-box" :style="{ backgroundColor: elementForm.styleAttribution.polygonFillColor, padding: '0px' }">
                      <div style="width: 100%; height: 100%; border: 2px solid gainsboro"></div>
                    </el-button>
                  </template>
                  <sketch-picker v-model="elementForm.styleAttribution.polygonFillColor" @input="changeFillColor" />
                </el-popover> -->
                <!-- <el-color-picker
                  style="vertical-align: middle;"
                  v-model="elementForm.styleAttribution.polygonFillColor"
                  show-alpha
                  :predefine="predefineColors"
                  @change="changeFillColor"
                >
                </el-color-picker> -->
                <el-color-picker v-model="elementForm.styleAttribution.polygonFillColor" show-alpha @change="changeFillColor" />
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('polygon')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.polygonFillColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('polygon')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="面样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.polygonType" placeholder="请选择">
                  <el-option v-for="item in polygonTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="线颜色" label-width="110px">
                <el-color-picker
                  style="vertical-align: middle"
                  v-model="elementForm.styleAttribution.polylineColor"
                  :predefine="predefineColors"
                  color-format="hex"
                >
                </el-color-picker>
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('line')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.polylineColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('line')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="线样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.polylineType" placeholder="请选择线样式">
                  <el-option v-for="item in polyLineTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="线宽度" label-width="110px">
                <el-input
                  v-model="elementForm.styleAttribution.polylineWidth"
                  type="number"
                  max="10"
                  placeholder="请输入线宽度 范围：1-10"
                  @input="changeWidth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.pointType" placeholder="请选择点样式">
                  <el-option v-for="item in pointTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点大小" label-width="110px">
                <el-input
                  v-model="elementForm.styleAttribution.pointSize"
                  type="number"
                  placeholder="请输入点大小 范围：0-20"
                  @input="changeSize"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点颜色" label-width="110px">
                <el-color-picker style="vertical-align: middle" v-model="elementForm.styleAttribution.pointColor" :predefine="predefineColors">
                </el-color-picker>
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('point')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.pointColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('point')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </template>
        <!-- 线 -->
        <template v-else-if="elementForm.graphicalType == 2">
          <div class="flex-content">
            <div class="flex-item">
              <el-form-item label="线颜色" label-width="110px">
                <el-color-picker
                  style="vertical-align: middle"
                  v-model="elementForm.styleAttribution.polylineColor"
                  :predefine="predefineColors"
                  color-format="hex"
                >
                </el-color-picker>
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('line')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.polylineColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('line')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="线样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.polylineType" placeholder="请选择线样式">
                  <el-option v-for="item in polyLineTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="线宽度" label-width="110px">
                <el-input
                  v-model="elementForm.styleAttribution.polylineWidth"
                  type="number"
                  max="10"
                  placeholder="请输入线宽度 范围：1-10"
                  @input="changeWidth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.pointType" placeholder="请选择点样式">
                  <el-option v-for="item in pointTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点大小" label-width="110px">
                <el-input
                  v-model="elementForm.styleAttribution.pointSize"
                  type="number"
                  placeholder="请输入点大小 范围：0-20"
                  @input="changeSize"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点颜色" label-width="110px">
                <el-color-picker style="vertical-align: middle" v-model="elementForm.styleAttribution.pointColor" :predefine="predefineColors">
                </el-color-picker>
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('point')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.pointColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('point')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </template>
        <!-- 点 -->
        <template v-else-if="elementForm.graphicalType == 1">
          <div class="flex-content">
            <div class="flex-item">
              <el-form-item label="点样式" label-width="110px">
                <el-select v-model="elementForm.styleAttribution.pointType" placeholder="请选择点样式">
                  <el-option v-for="item in pointTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点大小" label-width="110px">
                <el-input
                  v-model="elementForm.styleAttribution.pointSize"
                  type="number"
                  placeholder="请输入点大小 范围：0-20"
                  @input="changeSize"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-item">
              <el-form-item label="点颜色" label-width="110px">
                <el-color-picker style="vertical-align: middle" v-model="elementForm.styleAttribution.pointColor" :predefine="predefineColors">
                </el-color-picker>
                <!-- <el-link type="primary" style="margin-left: 10px" @click="handleOpenColor('point')">配置动态色值</el-link> -->
                <el-input
                  v-model="elementForm.styleAttribution.pointColorExpress"
                  placeholder="请设置表达式"
                  readonly
                  @click="handleOpenExpree('point')"
                  @keydown.prevent
                  style="width: 240px; margin-left: 10px"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </template>
        <div class="flex-content">
          <div class="flex-item" v-if="elementForm.graphicalType == 1 || elementForm.graphicalType == 2">
            <!-- 放样只有点放样或者线放样 -->
            <el-form-item label="是否放样" label-width="110px">
              <el-checkbox :label="1" :false-label="0" v-model="elementForm.lofting" @change="handleLofting"></el-checkbox>
            </el-form-item>
          </div>
          <div class="flex-item">
            <el-form-item label="周边信息" label-width="110px">
              <el-checkbox v-model="peripheryFlg" @change="handlePeriphery"></el-checkbox>
              <!-- <el-checkbox-group v-model="checkList">
                <el-checkbox v-for="item in peripheryList" :label="item.label" :key="item.id">{{ item.label }}</el-checkbox>
              </el-checkbox-group> -->
            </el-form-item>
          </div>
          <div class="flex-item" v-if="elementForm.graphicalType !== 1">
            <el-form-item label="子要素属性设置" label-width="110px" v-if="elementForm.graphicalType != 4">
              <div class="son-element-main">
                <div class="element-item" v-if="elementForm.graphicalType == 3 || elementForm.graphicalType == 2">
                  <el-checkbox style="padding-left: 8px" label="点" v-model="isPointGraphicalType" @change="handleIsPointGraphicalType"
                    >点</el-checkbox
                  >
                  <div
                    class="element-setting"
                    @click="handleSetting('点要素设置')"
                    :style="{
                      'pointer-events': isPointGraphicalType ? 'auto' : 'none',
                      cursor: isPointGraphicalType ? 'pointer' : ''
                    }"
                  >
                    <el-icon :style="{ color: isPointGraphicalType ? 'var(--current-color)' : '' }"><Setting /></el-icon>
                  </div>
                </div>
                <div class="element-item" v-if="elementForm.graphicalType == 3">
                  <el-checkbox style="padding-left: 8px" label="线" v-model="isLineGraphicalType" @change="handleIsLineGraphicalType">线</el-checkbox>
                  <div
                    class="element-setting"
                    @click="handleSetting('线要素设置')"
                    :style="{
                      'pointer-events': isLineGraphicalType ? 'auto' : 'none',
                      cursor: isLineGraphicalType ? 'pointer' : ''
                    }"
                  >
                    <el-icon :style="{ color: isLineGraphicalType ? 'var(--current-color)' : '' }"><Setting /></el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
        <div class="flex-content" v-if="elementForm.container === 2">
          <div class="flex-small-item">
            <el-form-item label="东至" label-width="110px">
              <el-input v-model="elementForm.attribution.eastTo" placeholder="请输入东至"></el-input>
            </el-form-item>
          </div>
          <div class="flex-small-item">
            <el-form-item label="西至" label-width="110px">
              <el-input v-model="elementForm.attribution.westTo" placeholder="请输入西至"></el-input>
            </el-form-item>
          </div>
          <div class="flex-small-item">
            <el-form-item label="南至" label-width="110px">
              <el-input v-model="elementForm.attribution.southTo" placeholder="请输入南至"></el-input>
            </el-form-item>
          </div>
          <div class="flex-small-item">
            <el-form-item label="北至" label-width="110px">
              <el-input v-model="elementForm.attribution.northTo" placeholder="请输入北至"></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <!-- 点线面要素的设置 -->
    <element-setting
      :settingTitle="settingTitle"
      :settingVisible="settingVisible"
      :checkedTreeMsg="checkedTreeMsg"
      @closeElementSetting="handleCloseElementSetting"
      @openFieldForm="handleOpenFieldForm"
      @showField="handleShowField"
      @saveRule="handleSaveRule"
    ></element-setting>
    <!-- 动态色值配置 -->
    <dynamic-color-setting
      :checkedTreeMsg="checkedTreeMsg"
      :dynamicColorDialog="dynamicColorDialog"
      :dynamicType="dynamicType"
      @handleSubmitDynamicColor="handleSubmitDynamicColor"
      @handleCloseDynamicColor="handleCloseDynamicColor"
    ></dynamic-color-setting>

    <!-- 打开公式编辑的弹框TODO -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="expression"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
      :isYSCJ="true"
    ></formula-editing-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import elementSetting from '../elementSettingDialog/index.vue';
// import { Sketch } from 'vue-sketch-picker';
import { saveFieldGroup } from '@/api/modal/index';
import { rgbaToHex } from '@/utils/validate';
import type { FormInstance, FormRules } from 'element-plus';
import { Setting } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import dynamicColorSetting from './dynamicColorSetting.vue';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();

// 定义 props
const props = defineProps<{
  checkedLevel: number;
  checkedNodeId: number;
  checkedTreeMsg: Record<string, any>;
  groupObj?: Record<string, any>;
}>();

// 定义 emits
const emit = defineEmits<{
  (e: 'saveRule', type: string): void;
  (e: 'lineHeightTree'): void;
  (e: 'openFieldForm', groupId: any, data: any): void;
  (e: 'showField', formData: any): void;
}>();

// 定义响应式数据
const elementForm = ref(props.checkedTreeMsg);

const elementRules = {
  graphicalType: [{ required: true, message: '请选择要素类型', trigger: 'change' }]
};

const elementRulesRef = ref<FormInstance>(null);
const isPointGraphicalType = ref(true);
const isLineGraphicalType = ref(true);
const settingTitle = ref('子元素设置');
const settingVisible = ref(false);
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
];

const polygonTypeList = [
  { label: '面', value: 'Solid' },
  { label: '右到左斜线', value: 'BackwardDiagonal' },
  { label: '左到右斜线', value: 'ForwardDiagonal' },
  { label: '叉网格', value: 'DiagonalCross' },
  { label: '横线', value: 'Horizontal' },
  { label: '竖线', value: 'Vertical' },
  { label: '横竖网格', value: 'Cross' },
  { label: '无', value: 'Null' }
];

const polyLineTypeList = [
  { label: '实线', value: 'solid' },
  { label: '虚线', value: 'dash' }
];

const pointTypeList = [
  { label: '圆形', value: 'Circle' },
  { label: '叉', value: 'Cross' },
  { label: '菱形', value: 'Diamond' },
  { label: '正方形', value: 'Square' },
  { label: '三角形', value: 'Triangle' },
  { label: 'X', value: 'X' }
];

// 计算属性
const colorLabel = computed(() => {
  if (elementForm.value.graphicalType === 1) {
    return '点颜色';
  } else if (elementForm.value.graphicalType === 2) {
    return '线颜色';
  } else if (elementForm.value.graphicalType === 3) {
    return '面颜色';
  }
  return '';
});

const moduleId = computed(() => modalStore.moduleId);
const modal_state = Number(route.query.status);
const peripheryFlg = ref(false);
const dynamicColorDialog = ref(false); //动态色值配置弹窗
const dynamicType = ref('point'); //当前配置的动态色值类型 point 点 line 线 polygon 面
const formulaVisible = ref(false);
const expression = ref('');
const appType = ref('#field');
const expressType = ref(''); // 表达式类型 point 点 line 线 polygon 面
const graphicalTypes = ref([
  {
    label: '点',
    value: 1
  },
  {
    label: '线',
    value: 2
  },
  {
    label: '面',
    value: 3
  },
  {
    label: '无图形',
    value: 4
  }
]);

watch(
  () => props.groupObj,
  (val) => {
    if (val && val.isShowGroup) {
      emit('lineHeightTree');
      settingTitle.value = val.title;
      settingVisible.value = val.isShowGroup;
    }
  },
  { deep: true }
);

/**
 * 初始化数据
 * @param val 传入的值
 */
const initData = (val: any) => {
  // 初始化
  elementForm.value = val;
  Object.assign(elementForm, val);
  if (val.graphicalType === 1) {
    elementForm.value.lofting = val.loftingPoint === 0 ? false : true;
  } else if (val.graphicalType === 2) {
    elementForm.value.lofting = val.loftingLine === 0 ? false : true;
  }
  if (val.periphery === 1) {
    peripheryFlg.value = true;
  }
  // 初始化的时候需要判断节点类型
  if (val.container === 2) {
    //四至节点 四至节点下面不允许添加子节点 并且不允许修改graphicalType类型
    graphicalTypes.value = [
      {
        label: '点',
        value: 1
      }
    ];
  } else if (val.container === 1 || val.container === 0) {
    // 图层节点或者要素节点
    graphicalTypes.value = [
      {
        label: '点',
        value: 1
      },
      {
        label: '线',
        value: 2
      },
      {
        label: '面',
        value: 3
      },
      {
        label: '无图形',
        value: 4
      }
    ];
  }
};

// 方法
const handleSetting = async (str: string) => {
  settingTitle.value = str;
  emit('saveRule', 'element');
};

const funCES = () => {
  settingVisible.value = true;
  if (settingTitle.value === '点要素设置') {
    modalStore.setElementType('point');
  } else if (settingTitle.value === '线要素设置') {
    modalStore.setElementType('line');
  }
};

const handleCloseElementSetting = () => {
  settingVisible.value = false;
};

const handleOpenFieldForm = (groupId: any, data: any) => {
  settingVisible.value = false;
  emit('openFieldForm', groupId, data);
};

const handleIsPointGraphicalType = async (val: boolean) => {
  const groupList: any[] = [];
  const list = ['commonPoint', 'graphicalPoint'];
  props.checkedTreeMsg.fieldGroupModelList = elementForm.value.fieldGroupModelList.filter((item) => item.groupScope !== 1);
  elementForm.value.fieldGroupModelList.forEach((item) => {
    if (item.groupScope === 2 && item.ruleAttribution != null && list.includes(item.ruleAttribution.type)) {
      groupList.push(item);
    }
  });

  for (let i = 0; i < groupList.length; i++) {
    const params = groupList[i];
    try {
      if (val) {
        await handleUpdateGroup(params, 1);
      } else {
        await handleUpdateGroup(params, -1);
      }
    } catch (error) {
      console.error('更新组异常', error);
    }
  }
};

const handleIsLineGraphicalType = async (val: boolean) => {
  const groupList: any[] = [];
  const list = ['commonLine', 'graphicalLine'];
  props.checkedTreeMsg.fieldGroupModelList = elementForm.value.fieldGroupModelList.filter((item) => item.groupScope !== 1);
  elementForm.value.fieldGroupModelList.forEach((item) => {
    if (item.groupScope === 2 && item.ruleAttribution != null && list.includes(item.ruleAttribution.type)) {
      groupList.push(item);
    }
  });

  for (let i = 0; i < groupList.length; i++) {
    const params = groupList[i];
    try {
      if (val) {
        await handleUpdateGroup(params, 1);
      } else {
        await handleUpdateGroup(params, -1);
      }
    } catch (error) {
      console.error('更新组异常', error);
    }
  }
};

const handleUpdateGroup = (item: any, num: number) => {
  item.status = num;
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    item.companyId = companyId;
  }
  return saveFieldGroup(item).then((res) => {
    if (res.code === 200) {
    } else {
      alert(res.msg);
    }
  });
};

const handleShowField = (formData: any) => {
  settingVisible.value = false;
  emit('showField', formData);
};

const handleValidateForm = async () => {
  let flag = false;
  if (elementRulesRef.value) {
    await elementRulesRef.value.validate((valid, fields) => {
      if (valid) {
        flag = valid;
      } else {
      }
    });
  }
  return flag;
};

const handleSaveRule = () => {
  emit('saveRule', 'noElement');
};

const handleLofting = (val: boolean) => {
  let flgNum = 0;
  if (val === true) {
    flgNum = 1;
  }
  if (elementForm.value.graphicalType === 1) {
    elementForm.value.loftingPoint = flgNum;
  } else if (elementForm.value.graphicalType === 2) {
    elementForm.value.loftingLine = flgNum;
  }
};

const handlePeriphery = (val: boolean) => {
  let flgNum = 0;
  if (val === true) {
    flgNum = 1;
  }
  props.checkedTreeMsg.periphery = flgNum;
};

const changeFillColor = (val: any) => {
  elementForm.value.styleAttribution.polygonFillColor = rgbaToHex(val);
  // elementForm.styleAttribution.polygonFillColor = val.hex8;
  // if (!val.includes('#')) {
  //   elementForm.styleAttribution.polygonFillColor = rgbaToHex(val);
  // }
};

const changeWidth = () => {
  if (elementForm.value.styleAttribution.polylineWidth > 10) {
    elementForm.value.styleAttribution.polylineWidth = 10;
  } else if (elementForm.value.styleAttribution.polylineWidth < 1) {
    elementForm.value.styleAttribution.polylineWidth = 1;
  }
};

const changeSize = () => {
  if (elementForm.value.styleAttribution.pointSize > 20) {
    elementForm.value.styleAttribution.pointSize = 20;
  } else if (elementForm.value.styleAttribution.pointSize <= 0) {
    elementForm.value.styleAttribution.pointSize = 0;
  }
};

/**
 * 是否允许删除
 * @returns boolean
 */
const setVisble = () => {
  if (elementForm.value.id && [-1, 1].includes(modal_state)) {
    return true;
  }
  return false;
};

/**
 * 打开动态色值配置
 */
const handleOpenColor = (type: string) => {
  dynamicType.value = type;
  dynamicColorDialog.value = true;
};

/**
 * 提交动态色值配置
 * @param val 提交的值
 */
const handleSubmitDynamicColor = (val: any) => {
  if (dynamicType.value === 'point') {
    elementForm.value.styleAttribution.pointColorDynamic = val;
  } else if (dynamicType.value === 'line') {
    elementForm.value.styleAttribution.polylineColorDynamic = val;
  } else if (dynamicType.value === 'polygon') {
    elementForm.value.styleAttribution.polygonFillColorDynamic = val;
  }
  dynamicColorDialog.value = false;
};

/**
 * 关闭动态色值配置
 * @param val 提交的值
 */
const handleCloseDynamicColor = () => {
  dynamicColorDialog.value = false;
};

// 关闭公式弹框
const handleCloseFormulation = () => {
  formulaVisible.value = false;
  // modalStore.setIsAllGroup(false);
};

const handleSubmitFormulation = (expressionCopy: any) => {
  expression.value = expressionCopy;
  copyFun(expression.value);
};

// 得到表达式
const copyFun = (expression: any) => {
  if (expressType.value === 'point') {
    elementForm.value.styleAttribution.pointColorExpress = expression;
  } else if (expressType.value === 'line') {
    elementForm.value.styleAttribution.polylineColorExpress = expression;
  } else if (expressType.value === 'polygon') {
    elementForm.value.styleAttribution.polygonFillColorExpress = expression;
  }
  formulaVisible.value = false;
};

/**
 * 打开公式编辑的弹框
 * @param type 类型 point 点 line 线 polygon 面
 */
const handleOpenExpree = (type: string) => {
  expressType.value = type;
  if (type === 'point') {
    expression.value = elementForm.value.styleAttribution.pointColorExpress;
  } else if (type === 'line') {
    expression.value = elementForm.value.styleAttribution.polylineColorExpress;
  } else if (type === 'polygon') {
    expression.value = elementForm.value.styleAttribution.polygonFillColorExpress;
  }
  formulaVisible.value = true;
};

// 组件注册
defineExpose({
  handleValidateForm,
  funCES,
  initData
});

// 组件创建钩子
onMounted(() => {
  // 这里可以放创建时需要执行的逻辑
});
</script>

<style lang="scss" scoped>
// :deep(.el-checkbox__label) {
//   display: none;
// }
.color-box {
  width: 100px;
  height: 23px;
  padding: 0;
  border: 6px;
}
.flex-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .flex-one {
    flex: 100%;
    margin-right: 16px;
  }
  .flex-item {
    flex: 0 0 calc(50% - 16px); /* 25% width minus the gap */
    margin-right: 16px; /* Right margin for the gap */
    // margin-bottom: 16px; /* Bottom margin for the gap */
    box-sizing: border-box; /* Include padding and border in the width */
  }
  .flex-row {
    width: calc(100% - 16px);
  }
  .flex-small-item {
    flex: 0 0 calc(25% - 16px); /* 25% width minus the gap */
    margin-right: 16px; /* Right margin for the gap */
    // margin-bottom: 16px; /* Bottom margin for the gap */
    box-sizing: border-box; /* Include padding and border in the width */
  }
}
:deep(.el-form-item__label) {
  color: #161d26;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-size: 14px;
}
.acquire-element-setting-main {
  // min-width: 460px;
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;

    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }

  &:hover {
    border: 1px solid var(--current-color);
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .item-form {
    margin: 24px 16px 0;
    .el-form {
      position: relative;
      .area-color-main {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        .fill-color {
          height: 36px;
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-right: 16px;
          .text {
            padding: 0 8px;
            width: 60px;
            height: 23px;
            line-height: 23px;
            color: #161d26;
            font-size: 14px;
            text-align: left;
            font-family:
              Helvetica Neue,
              Helvetica,
              PingFang SC,
              Hiragino Sans GB,
              Microsoft YaHei,
              Arial,
              sans-serif;
          }
          .color {
            padding: 0 8px;
            width: 100px;
            height: 23px;
            line-height: 20px;
          }
        }
        .stroke-color {
          height: 36px;
          display: flex;
          flex-direction: row;
          align-items: center;
          .text {
            padding: 0 8px;
            width: 60px;
            height: 23px;
            line-height: 23px;
            color: #161d26;
            font-size: 14px;
            text-align: left;
            font-family:
              Helvetica Neue,
              Helvetica,
              PingFang SC,
              Hiragino Sans GB,
              Microsoft YaHei,
              Arial,
              sans-serif;
          }
          .color {
            padding: 0 8px;
            width: 100px;
            height: 23px;
            line-height: 20px;
            border-radius: 4px;
          }
        }
      }
      .son-element-main {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        width: 100%;
        .element-item {
          margin: 0 8px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-content: center;
          align-items: center;
          width: 50%;
          height: 36px;
          line-height: 36px;
          border-radius: 6px;
          border: 1px solid #e6e9ee;
          .element-setting {
            width: 40px;
            height: 36px;
            background: #f6f7f8;
            border-radius: 0px 6px 6px 0px;
            opacity: 1;
            border: 1px solid #e6e9ee;
            text-align: center;
            cursor: pointer;
          }
        }
      }
    }
  }
}

:deep(&) {
  .el-color-picker {
    width: 100px;
    height: 23px;
    margin-right: 8px;
    border: 6px;
    z-index: 99;
    .el-color-picker__trigger {
      width: 100px;
      height: 23px;
      padding: 0;
      border: 6px;
      .el-color-picker__icon {
        display: none;
      }
    }
  }
}
</style>
