import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { QuestionParams, QuestionListParams, QuestionResultParams } from '@/api/question/types';

/**
 * 保存问卷
 * @param params 问卷参数
 * @returns {AxiosPromise}
 */
export function saveQuestion(params: QuestionParams): AxiosPromise<any> {
  return request({
    url: '/qjt/questionnaire/save',
    method: 'post',
    data: params
  });
}

/**
 * 问卷列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function questionList(params: QuestionListParams): AxiosPromise<any> {
  return request({
    url: '/qjt/questionnaire/selectList',
    method: 'post',
    data: params
  });
}

/**
 * 根据id查询详情
 * @param params 问卷ID
 * @returns {AxiosPromise}
 */
export function getQuestionDetail(params: string | number): AxiosPromise<any> {
  return request({
    url: '/qjt/questionnaire/selectOne?id=' + params,
    method: 'post'
  });
}

/**
 * 保存问卷调查结果
 * @param params 结果参数
 * @returns {AxiosPromise}
 */
export function saveQuestionResult(params: QuestionResultParams): AxiosPromise<any> {
  return request({
    url: '/qjt/questionnaire/saveResult',
    method: 'post',
    data: params
  });
}

/**
 * 导出问卷报告
 * @param params 问卷ID
 * @returns {AxiosPromise}
 */
export function exportWord(params: string | number): AxiosPromise<Blob> {
  return request({
    url: '/qjt/questionnaire/downloadone/' + params,
    method: 'get',
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 导出问卷详情
 * @param params 问卷ID
 * @returns {AxiosPromise}
 */
export function exportExcel(params: string | number): AxiosPromise<Blob> {
  return request({
    url: '/qjt/questionnaire/downloadoneExcel/' + params,
    method: 'get',
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 查看文件详情
 * @param params 问卷ID
 * @returns {AxiosPromise}
 */
export function exportList(params: string | number): AxiosPromise<Blob> {
  return request({
    url: '/qjt/questionnaire/downloadoneList/' + params,
    method: 'get',
    responseType: 'blob' // 将文件流转成blob对象
  });
}
