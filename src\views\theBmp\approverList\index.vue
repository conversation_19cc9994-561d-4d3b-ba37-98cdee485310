<template>
  <div class="main">
    <el-card class="card">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="设计流程" name="first">
          <instance-list :showType="'edit'" ref="instanceListRefSecond"></instance-list>
        </el-tab-pane>
        <el-tab-pane label="发起申请" name="second ">
          <instance-list :showType="'apply'" ref="instanceListRef"></instance-list>
        </el-tab-pane>
        <el-tab-pane label="审批中心" name="third">
          <approve-deal-list ref="approveDealListRef"></approve-deal-list>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import type { TabsPaneContext } from 'element-plus';
import InstanceList from './instanceList.vue';
import ApproveDealList from './approveDealList.vue';
// 定义当前激活的标签页
const activeName = ref('first');
const approveDealListRef = ref('approveDealListRef');
const instanceListRefSecond = ref('instanceListRefSecond');
const instanceListRef = ref('instanceListRef');
/**
 * 处理标签页点击事件
 * @param tab 原生标签的处理事件
 */
const handleClick = (tab: TabsPaneContext) => {
  // 在这里可以添加标签页点击后的逻辑
  console.log('当前页面切换的分页图片', tab.paneName);
  if (tab.paneName === 'third') {
    approveDealListRef.value.handleUndoList();
  } else if (tab.paneName === 'second') {
    instanceListRefSecond.value.handleProcessList();
  } else {
    instanceListRef.value.handleProcessList();
  }
};
</script>

<style lang="scss" scoped>
.main {
  height: 100%;
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  .card {
    height: 100%;
    width: 100%;
  }
}
</style>
