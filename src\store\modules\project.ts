import { defineStore } from 'pinia';
import { ref } from 'vue';

// 定义类型接口
interface ParcelInfo {
  [key: string]: any;
}

interface GraphInfo {
  [key: string]: any;
}

type AreaUnitType = 'square-meters' | 'mu' | 'hectare' | string;

export const useProjectStore = defineStore('project', () => {
  // 定义状态
  const selectCityCode = ref<string[]>([]); // 项目里面外层选中的省市区全code
  const parcelInfo = ref<ParcelInfo>({}); // 选中的宗地
  const selectGraph = ref<GraphInfo>({}); // 选中的图形数据
  const selectProjectType = ref<number>(1); // 选中的项目类型 1房地一体 2林业调查
  const areaUnit = ref<AreaUnitType>('square-meters'); // 默认图形面积为平方米
  const srcList = ref<string[]>([]); // 大图预览列表
  const proModuleId = ref<number>(0); // 当前模块的id
  const nodeStyles = ref<any[]>([]); // 规则树node样式平铺列表

  // 设置省市区全code
  const setCityCode = (list: string[]): void => {
    selectCityCode.value = list;
  };

  // 设置宗地信息
  const setParceInfo = (obj: ParcelInfo): void => {
    parcelInfo.value = obj;
  };

  // 设置图形数据
  const setGraph = (obj: GraphInfo): void => {
    selectGraph.value = obj;
  };

  // 设置项目类型
  const setProjectType = (type: number): void => {
    selectProjectType.value = type;
  };

  // 设置面积单位
  const setAreaunit = (unit: AreaUnitType): void => {
    areaUnit.value = unit;
  };

  // 设置大图预览列表
  const setSrclist = (list: string[]): void => {
    srcList.value = list;
  };

  // 设置模块ID
  const setModuleId = (id: number): void => {
    proModuleId.value = id;
  };

  // 设置当前规则树的node样式平铺列表
  const setNodeStyles = (list: any[]): void => {
    nodeStyles.value = list;
  };

  return {
    // 状态
    selectCityCode,
    parcelInfo,
    selectGraph,
    selectProjectType,
    areaUnit,
    srcList,
    proModuleId,
    nodeStyles,

    // 方法
    setCityCode,
    setParceInfo,
    setGraph,
    setProjectType,
    setAreaunit,
    setSrclist,
    setModuleId,
    setNodeStyles
  };
});
