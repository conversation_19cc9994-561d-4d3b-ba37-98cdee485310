<template>
  <el-form label-width="90px">
    <el-form-item label="文字大小">
      <el-input-number :min="13" :max="200" v-model="attributeCopy.fontSize" style="width: 100%" />
    </el-form-item>
    <el-form-item label="文字颜色">
      <el-color-picker v-model="attributeCopy.color" size="small" />
    </el-form-item>
    <el-form-item label="边距">
      <el-input-number :min="1" :max="200" v-model="attributeCopy.padding" style="width: 100%" />
    </el-form-item>
    <el-form-item label="边框颜色">
      <el-color-picker v-model="attributeCopy.borderColor" size="small" />
    </el-form-item>
    <el-form-item label="背景颜色">
      <el-color-picker v-model="attributeCopy.bgColor" size="small" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-rect-num-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);
</script>

<style scoped></style>
