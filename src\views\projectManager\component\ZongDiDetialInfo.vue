<!-- 宗地详细信息 -->
<template>
  <div class="zongdi-detail-info-contianer" v-if="isShow">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <div class="info-title">
          <span class="title">{{ parceInfoItem.parcelName }}</span>
          <span class="icon" @click="handleColseZongDiInfo">
            <i class="el-icon-circle-close"></i>
          </span>
        </div>
        <!-- <div class="info-select-tab">
          <div class="tab-left" @click="handlePrevTab">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="tab-content" ref="tabContent">
            <div
              class="tab-continer"
              :style="`width:${
                signleWidth * selectTitleList.length
              }px;transform:translate(${scrollResultWidth}px,0);transition:1s;`"
              v-for="item in selectTitleList"
              :key="item.value"
              ref="tabContiner"
            >
              <div
                class="tab-label"
                @click="handleCheckItem(item)"
                :class="item.isCkeck ? 'current-Check' : ''"
              >
                <span>{{ item.label }}</span>
              </div>
            </div>
          </div>
          <div class="tab-right" @click="handleNextTab">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div> -->
        <div class="info-select-tab">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link"> {{ showTitle }}<i class="el-icon-arrow-down el-icon--right"></i> </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="(item, index) in selectTitleList" :key="index" :command="item.value">{{ item.label }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <div class="info-end-handle">
            <el-button type="primary" @click="jump(1)" :disabled="currentValue == 1">上一项</el-button>
            <el-button type="primary" @click="jump(2)" :disabled="currentValue == selectTitleList.length">下一项</el-button>
            <!-- <el-button type="primary" size="default" v-if="currentValue == 4" @click="addZJR">新增指界人</el-button> -->
          </div>
        </div>
        <!-- 图形信息 -->
        <div v-if="currentValue == 1">
          <graphical-info :parceInfoItem="parceInfoItem" @centerFun="centerFun" />
        </div>
        <!-- 宗地信息 -->
        <div v-if="currentValue == 2">
          <zong-di-info :parceInfoItem="parceInfoItem" ref="zongdiInfoRef"></zong-di-info>
        </div>
        <!--权利人  -->
        <div v-if="currentValue == 3" class="obligee-main">
          <obligee-info :parceInfoItem="parceInfoItem" />
        </div>
        <!-- 指界人 -->
        <div v-if="currentValue == 4" class="referee-main">
          <referee-info :parceInfoItem="parceInfoItem" />
        </div>
        <!-- 界址点描述 -->
        <div v-if="currentValue == 5">
          <boundaryInfo ref="boundaryInfo"></boundaryInfo>
          <div class="submit-btn">
            <el-button type="primary" v-if="currentValue == 5" @click="submitJZDMS">保存</el-button>
          </div>
        </div>
        <!-- 放线点 -->
        <div v-if="currentValue == 6">
          <fangxian></fangxian>
        </div>
        <!-- 现场照片 -->
        <div v-if="currentValue == 7" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="6" />
        </div>
        <!-- 户口本 -->
        <div v-if="currentValue == 8" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="9" />
        </div>
        <!-- 用地审核书 -->
        <div v-if="currentValue == 9" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="10" />
        </div>
        <!-- 房产证 -->
        <div v-if="currentValue == 10" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="11" />
        </div>
        <!-- 土地使用证-->
        <div v-if="currentValue == 11" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="12" />
        </div>
        <!-- 建设用地批准证-->
        <div v-if="currentValue == 12" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="13" />
        </div>
        <!-- 结婚证-->
        <div v-if="currentValue == 13" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="14" />
        </div>
        <!-- 备用照片-->
        <div v-if="currentValue == 14" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="5" />
        </div>
        <!-- 房产图-->
        <div v-if="currentValue == 15" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="17" />
        </div>
        <!-- 宗地图-->
        <div v-if="currentValue == 16" class="tem-content">
          <zong-di-photo :parceInfoItem="parceInfoItem" :img-type="16" />
        </div>
        <!-- 日志 -->
        <div v-if="currentValue == 17" class="tem-content">
          <logList :dataId="parceInfoItem.id" :dataType="101"></logList>
        </div>
        <!-- 第X幢 -->
        <div v-if="currentValue > 17">
          <houseInfo :houseAttribution="houseAttribution"></houseInfo>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import GraphicalInfo from './info/GraphicalInfo.vue';
import ObligeeInfo from './info/ObligeeInfo.vue';
import RefereeInfo from './info/RefereeInfo.vue';
import ZongDiInfo from './info/ZongDiInfo.vue';
import ZongDiPhoto from './info/ZongDiPhoto.vue';
import boundaryInfo from './info/boundaryInfo.vue';
import fangxian from './info/fangxian.vue';
import houseInfo from './info/houseInfo.vue';
import logList from '@/components/logList/index.vue';
import { modifyZD } from '@/api/project';

interface SelectTitleItem {
  label: string;
  value: number;
  isCkeck: boolean;
  attribution?: any;
}

interface Props {
  parceInfoItem: {
    id: string | number;
    parcelName: string;
    houseList: any[];
  };
  isZongdiInfo: boolean;
  btnType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  btnType: 'normal'
});

const emit = defineEmits(['closeZongInfo', 'changeGraph']);

// 宗地详细信息的切换选项
const selectTitleList = ref<SelectTitleItem[]>([]);
const currentValue = ref(1);
const isShow = ref(false);
const showTitle = ref('图形信息');
const houseAttribution = ref({});
const zongdiInfoRef = ref();
const boundaryInfoRef = ref();

// 初始化标题列表
const initTitleList = () => {
  selectTitleList.value = [
    { label: '图形信息', value: 1, isCkeck: true },
    { label: '宗地信息', value: 2, isCkeck: false },
    { label: '权利人', value: 3, isCkeck: false },
    { label: '指界人', value: 4, isCkeck: false },
    { label: '界址点描述', value: 5, isCkeck: false },
    { label: '放线点', value: 6, isCkeck: false },
    { label: '现场照片', value: 7, isCkeck: false },
    { label: '户口簿', value: 8, isCkeck: false },
    { label: '用地送审书', value: 9, isCkeck: false },
    { label: '房产证', value: 10, isCkeck: false },
    { label: '土地使用证', value: 11, isCkeck: false },
    { label: '用地批准书', value: 12, isCkeck: false },
    { label: '结婚证', value: 13, isCkeck: false },
    { label: '备用照片', value: 14, isCkeck: false },
    { label: '房产图', value: 15, isCkeck: false },
    { label: '宗地图', value: 16, isCkeck: false },
    { label: '操作日志', value: 17, isCkeck: false }
  ];
};

// 监听 isZongdiInfo 变化
watch(
  () => props.isZongdiInfo,
  (val) => {
    isShow.value = val;
  }
);

// 监听 parceInfoItem 变化
watch(
  () => props.parceInfoItem,
  (val) => {
    if (val) {
      initTitleList();
      if (val.houseList.length !== 0) {
        val.houseList.forEach((v, idx) => {
          selectTitleList.value.push({
            label: v.tag,
            value: 17 + idx + 1,
            isCkeck: false,
            attribution: v.attribution
          });
        });
      } else if (val.houseList.length === 0 && currentValue.value > 16) {
        currentValue.value = 1;
        showTitle.value = '图形信息';
      }
    }
  },
  { deep: true }
);

/**
 * 处理下拉菜单选择
 * @param command 命令
 */
const handleCommand = (command: number) => {
  const selectedItem = selectTitleList.value.find((item) => item.value === command);
  if (selectedItem) {
    showTitle.value = selectedItem.label;
    currentValue.value = command;
    if (command > 17) {
      houseAttribution.value = selectedItem.attribution;
    }
  }
};

/**
 * 关闭宗地详细信息
 */
const handleColseZongDiInfo = () => {
  emit('closeZongInfo');
};

/**
 * 用于显示图形结构的中间方法
 * @param obj 对象
 */
const centerFun = (obj: any) => {
  emit('changeGraph', obj);
};

/**
 * 保存界址点描述
 */
const submitJZDMS = () => {
  const geomAttribution = boundaryInfoRef.value.getGeomAttribution();

  ElMessageBox.confirm('您确定要修改吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = {
        geomAttribution,
        id: props.parceInfoItem.id
      };
      modifyZD(104, params).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '保存成功'
          });
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

/**
 * 上一个、下一个
 * @param val 值
 */
const jump = (val: number) => {
  if (val === 1) {
    currentValue.value--;
  } else {
    currentValue.value++;
  }
  const selectedItem = selectTitleList.value.find((item) => item.value === currentValue.value);
  if (selectedItem) {
    showTitle.value = selectedItem.label;
    if (selectedItem.value > 17) {
      houseAttribution.value = selectedItem.attribution;
    }
  }
};

// 初始化
onMounted(() => {
  initTitleList();
});
</script>

<style lang="scss" scoped>
.submit-btn {
  position: absolute;
  bottom: 10px;
  right: 20px;
}
:deep(.el-dropdown) {
  position: relative;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 10px;
  background-color: #1890ff;
  border-radius: 16px;
  height: 28px;
  padding: 0px 10px;
}
.zongdi-detail-info-contianer {
  height: auto;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  backdrop-filter: blur(5px);
  //  z-index: 1;
  &:after {
    content: '';
    width: 512;
    height: auto;
    position: absolute;
    left: 0;
    top: 0;
    background: inherit;
    //  z-index: 2;
    backdrop-filter: blur(10px);
  }
  .info-title {
    width: 512;
    height: 40px;
    line-height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 0px 0px;
    display: flex;
    justify-content: center;
    align-content: center;
    position: relative;
    border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
    //  z-index: 3;
    .title {
      // width: 56px;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .icon {
      width: 14px;
      height: 14px;
      position: absolute;
      right: 12px;
      color: #ffffff;
      cursor: pointer;
    }
  }
  .info-select-tab {
    display: flex;
    flex-direction: row;
    width: 512px;
    height: 40px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    .info-end-handle {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
    .tab-left {
      position: absolute;
      left: 10px;
      top: 16px;
      color: #ffffff;
      // z-index: 5;
    }
    .tab-content {
      display: flex;
      align-content: center;
      width: 450px;
      overflow: hidden;
      // z-index: 4;
      height: 40px;
      margin-left: 20px;
      margin-right: 30px;
      margin-top: 4px;
      .tab-continer {
        margin-left: 16px;
        flex: 1;
        cursor: pointer;
        .current-Check {
          height: 30px;
          background: var(--current-color);
          border-radius: 16px 16px 16px 16px;
          opacity: 1;
          color: #ffffff !important;
        }
        .tab-label {
          width: 70px;
          height: 20px;
          font-size: 14px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.8);
          line-height: 20px;
          margin: 10px;
          text-align: center;
        }
      }
    }
    .tab-right {
      position: absolute;
      right: 10px;
      top: 16px;
      color: #ffffff;
    }
  }
  //  权利人信息
  .obligee-main {
    height: calc(100vh - 300px);
    overflow-y: auto;
  }
  .referee-main {
    height: calc(100vh - 300px);
    overflow-y: auto;
  }
  .tem-content {
    height: 629px;
    overflow: auto;
  }
  /*滚动条样式*/
  .tem-content::-webkit-scrollbar {
    width: 4px;
  }
  .tem-content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .tem-content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
</style>
