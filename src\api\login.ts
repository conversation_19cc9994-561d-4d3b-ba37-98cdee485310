import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LoginData, LoginResult, VerifyCodeResult } from './types';
import { UserInfo } from '@/api/system/user/types';
/**
 * 登录
 * @param data {LoginData}
 * @returns
 */
export function login(data: LoginData): AxiosPromise<LoginResult> {
  return request({
    url: '/system/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  });
}

//根据手机号获取短信验证码
export function getMessageCode(params: any) {
  return request({
    url: '/system/smscode',
    method: 'post',
    data: params
  });
}
//公共登录
export function loginFirst(params) {
  return request({
    url: '/system/loginFirst',
    method: 'post',
    data: params
  });
}

// 忘记密码
export function forgetPassword(params) {
  return request({
    url: '/system/forgetpwd',
    method: 'post',
    data: params
  });
}

// 注册方法
export function register(data: any) {
  return request({
    url: '/system/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  });
}

// 短信+验证码++图形验证码
export function loginMessage(captcha, uuid, companyId, mobile) {
  const data = {
    mobile: mobile,
    captcha: captcha,
    companyId: companyId,
    from: 'web'
  };
  return request({
    url: '/system/login/sms',
    method: 'post',
    data
  });
}

/**
 * 注销
 */
export function logout() {
  return request({
    url: '/system/logout',
    method: 'delete'
  });
}

/**
 * 获取验证码
 */
export function getCodeImg(): AxiosPromise<VerifyCodeResult> {
  return request({
    url: '/auth/code',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  });
}
// 获取用户详细信息
export function getInfo(): AxiosPromise<UserInfo> {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  });
}
