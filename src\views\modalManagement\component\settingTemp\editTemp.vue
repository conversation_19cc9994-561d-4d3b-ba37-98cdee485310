<!-- 新增或者编辑office -->
<template>
  <div class="editTemp-main">
    <div class="back">
      <el-link type="info" @click="back"><i class="el-icon-arrow-left"></i> 返回</el-link>
    </div>
    <div class="content">
      <!-- 文档内容ifrem -->
      <div class="office-content">
        <iframe
          :src="`/office.html?downloadUrl=${url}&templateName=${templateName}&tempId=${tempId}&baseUrl=${baseUrl}&token=${token}`"
          style="width: 100%; height: 100%"
        ></iframe>
      </div>
      <!-- 复制操作 -->
      <div class="handle-content">
        <!-- 选择要素 -->
        <div class="step-title" @click="showYs = !showYs">
          <span>第一步：选择需要复制的要素</span>
          <i class="el-icon-arrow-down" v-if="!showYs"></i>
          <i class="el-icon-arrow-up" v-else></i>
        </div>
        <div class="show-content" v-show="showYs">
          <el-tree
            ref="treeRef"
            :data="ysTree"
            :props="defaultProps"
            highlight-current
            default-expand-all
            node-key="id"
            :current-node-key="defaultExpand"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
        <!-- 选择属性组 -->
        <div class="step-title" @click="showAttrs = !showAttrs">
          <span>第二步：选择属性组</span>
          <i class="el-icon-arrow-down" v-if="!showAttrs"></i>
          <i class="el-icon-arrow-up" v-else></i>
        </div>
        <div class="show-content" v-show="showAttrs">
          <div
            class="flex-row"
            v-for="(item, index) in fieldGroupModelList"
            :class="{ 'flex-active': item.checked }"
            :key="index"
            @click="changeAtt(item)"
          >
            <div class="label">{{ item.typeName }}</div>
            <div class="ico">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="flex-row no-span" v-show="fieldGroupModelList.length == 0">暂无数据</div>
        </div>
        <!-- 操作最终字段 -->
        <div class="step-title" @click="showFileds = !showFileds">
          <span>第三步：复制字段</span>
          <i class="el-icon-arrow-down" v-if="!showFileds"></i>
          <i class="el-icon-arrow-up" v-else></i>
        </div>
        <div class="show-content" v-show="showFileds">
          <div class="flex-row" v-for="(item, index) in fieldModelList" :key="index">
            <div class="label">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
            <el-link type="primary" @click="copy(item)">复制</el-link>
          </div>
          <div class="flex-row no-span" v-show="fieldModelList.length == 0">暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import { getToken } from '@/utils/auth';
import { selectRules } from '@/api/modal/index';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
// 定义 props
const props = defineProps<{
  url: string;
  showEdit: boolean;
  templateName: string;
  tempId: string;
  moduleId: string;
}>();

// 定义 emit
const emit = defineEmits(['toList']);

// 定义响应式数据
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const token = getToken();
const ysTree = ref<any[]>([]);
const showYs = ref(true); // 是否展开要素树
const showAttrs = ref(false); // 是否展开属性组
const showFileds = ref(false); // 是否展开字段组
const defaultProps = ref({
  children: 'list',
  label: 'typeName'
});
const defaultExpand = ref(0);
const checkedYS = ref<any>({}); // 选中的要素
const fieldGroupModelList = ref<any[]>([]); // 需要显示的属性组
const fieldModelList = ref<any[]>([]); // 需要显示的字段列表
const checkedAttr = ref<any>({}); // 选中的属性组
const treeRef = ref<any>(null);

// 返回模板列表
const back = () => {
  emit('toList');
};

// 初始化
const init = () => {
  const params = {
    moduleId: props.moduleId
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  selectRules(params).then((res) => {
    if (res.code === 200) {
      ysTree.value = res.data;
      defaultExpand.value = res.data[0].id;
      fieldGroupModelList.value = res.data[0].fieldGroupModelList;
      checkedYS.value = res.data[0];
      nextTick(() => {
        if (treeRef.value) {
          treeRef.value.setCurrentKey(res.data[0].id);
        }
      });
      showAttrs.value = true;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 节点点击事件
const handleNodeClick = (data: any) => {
  checkedYS.value = data;
  fieldGroupModelList.value = data.fieldGroupModelList;
  fieldModelList.value = [];
};

// 改变选中属性组
const changeAtt = (item: any) => {
  fieldModelList.value = item.fieldModelList;
  fieldGroupModelList.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  checkedAttr.value = item;
  showFileds.value = true;
};

// 复制
const copy = (item: any) => {
  const variable = `getAttr("${checkedYS.value.typeName}.${checkedAttr.value.typeName}.${item.fieldCn}")`;
  const tag = document.createElement('textarea'); // 创建 textarea 标签，注意：创建 input 标签则不会换行
  document.body.appendChild(tag); // 添加到 body 中
  tag.value = variable; // 给 textarea 设置 value 属性为需要 copy 的内容
  tag.select(); // 选中
  document.execCommand('copy', false); // copy 已经选中的内容
  ElMessage({
    message: '复制成功',
    type: 'success'
  });
  tag.remove();
};

// 监听 showEdit 变化
watch(
  () => props.showEdit,
  (val) => {
    if (val) {
      init();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.editTemp-main {
  width: 100%;
  height: 100%;
  .back {
    height: 40px;
    display: flex;
    align-items: center;
  }
  .content {
    height: calc(100% - 40px);
    width: 100%;
    display: flex;
    .office-content {
      flex: 2;
    }
    .handle-content {
      flex: 1;
      overflow-y: auto;
      margin-left: 10px;
      .step-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
      }
      .step-title:hover {
        color: var(--current-color);
      }
      .show-content {
        max-height: 400px;
        width: calc(100% - 0px);
        overflow: auto;
        margin-bottom: 10px;
        .flex-row {
          height: 32px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          color: rgba(22, 29, 38, 1);
          cursor: pointer;
          .label {
            font-size: 12px;
            padding-left: 12px;
          }
          .ico {
            padding-right: 8px;
          }
        }
        .flex-row:hover {
          background-color: #f5f7fa;
        }
        .flex-active {
          background: #edf4fb;
        }
        .no-span {
          color: #d3d3d3 !important;
          cursor: not-allowed;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
