<!-- 新增问卷内容弹窗 -->
<template>
  <div class="addQuestion-main flex flex-col">
    <div @click="handleBack" class="back flex-none">
      <div>
        <el-icon><ArrowLeft /></el-icon> <span>返回</span>
      </div>
    </div>
    <div class="dialog-content flex-auto">
      <el-form
        :model="localQuestionOne"
        :rules="props.questionOneRule"
        ref="ruleFormRef"
        label-width="100px"
        class="demo-ruleForm"
        label-position="top"
      >
        <div class="quesiton-main">
          <div class="question-item" v-for="(item, index) in props.questionList" :key="index">
            <el-form-item :label="item.fieldCn" :prop="item.fieldName">
              <!-- 输入框 -->
              <el-input v-model="localQuestionOne[item.fieldName]" :placeholder="item.inputHint" v-if="item.valueMethod == 'input'"></el-input>
              <!-- 下拉选择框 -->
              <el-select
                v-model="localQuestionOne[item.fieldName]"
                :placeholder="item.inputHint"
                v-if="item.valueMethod == 'select'"
                style="width: 100%"
              >
                <el-option v-for="option in item.attribution.options" :key="option.value" :label="option.label" :value="option.value"> </el-option>
              </el-select>
              <!-- 单选框 -->
              <el-radio-group v-model="localQuestionOne[item.fieldName]" v-if="item.valueMethod == 'radio'">
                <el-radio v-for="option in item.attribution.options" :key="option.value" :value="option.label"></el-radio>
              </el-radio-group>
              <!-- 数字输入框 -->
              <el-input
                v-model="localQuestionOne[item.fieldName]"
                v-if="item.valueMethod == 'number'"
                :placeholder="item.inputHint"
                type="number"
              ></el-input>
              <!-- 多行输入框 -->
              <el-input
                v-if="item.valueMethod == 'textarea'"
                type="textarea"
                :rows="1"
                :placeholder="item.inputHint"
                v-model="localQuestionOne[item.fieldName]"
              >
              </el-input>
              <!-- 日期选择框 -->
              <el-date-picker
                v-if="item.valueMethod == 'date'"
                v-model="localQuestionOne[item.fieldName]"
                :type="item.attribution.type as any"
                value-format="YYYY-MM-DD"
                :placeholder="item.inputHint"
                style="width: 100%"
              >
              </el-date-picker>
              <!-- 附件上传 -->
              <el-upload
                v-if="item.valueMethod == 'xtfj'"
                class="upload-demo"
                :headers="headers"
                :action="`${baseUrl}/qjt/file/multi/upload`"
                :on-success="(response) => handleSuccessFJ(response, item)"
                :on-remove="(file) => handleRemoveFJ(file, item)"
                multiple
                name="files"
                :before-upload="(file) => beforeAvatarUpload(file, item)"
                :limit="item.attribution.picNum"
                :on-exceed="(files, fileList) => handleExceed(files, fileList, item)"
                :file-list="localQuestionOne[item.fieldName] || []"
              >
                <template #trigger>
                  <el-button size="small" type="primary">点击上传</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip" style="color: red">只能上传{{ item.attribution.acceptType.join(',') }}文件</div>
                </template>
                <template #file="{ file }">
                  <div class="fj-row">
                    {{ file[`${item.fieldName}_1`] }}
                    <div style="cursor: pointer" @click="handleRemove(file, localQuestionOne[item.fieldName])">×</div>
                  </div>
                </template>
              </el-upload>
              <!-- 图片上传 -->
              <el-upload
                v-if="item.valueMethod == 'upload'"
                :action="`${baseUrl}/qjt/file/multi/upload`"
                :headers="headers"
                name="files"
                accept=".jpg,.png"
                list-type="picture-card"
                :limit="item.attribution.picNum"
                :file-list="localQuestionOne[item.fieldName] || []"
                :on-success="(response) => handleSuccessTP(response, item)"
                :on-remove="(file) => handleRemoveTP(file, item)"
                :on-exceed="(files, fileList) => handleExceed(files, fileList, item)"
              >
                <template #default>
                  <el-icon><Plus /></el-icon>
                </template>
                <template #file="{ file }">
                  <img
                    class="el-upload-list__item-thumbnail"
                    :fit="fit"
                    :src="`${baseUrl}/qjt/file/otherDownload/${file.url}?token=${token}`"
                    alt=""
                  />
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <el-icon><ZoomIn /></el-icon>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleDownload(file)">
                      <el-icon><Download /></el-icon>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleRemove(file, localQuestionOne[item.fieldName])">
                      <el-icon><Delete /></el-icon>
                    </span>
                  </span>
                </template>
              </el-upload>
              <!-- 视频上传 -->
              <!-- 身份证识别 -->
              <div v-if="item.valueMethod == 'idCardScan'" style="color: rgba(0, 0, 0, 0.5)">暂不支持</div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="footer flex-none">
      <el-button type="info" @click="handleBack">取消</el-button>
      <el-button type="primary" @click="submitAddQuestion">提交</el-button>
    </div>
    <el-dialog v-model="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { ArrowLeft, ZoomIn, Download, Delete, Plus } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';
import { isArray } from '@/utils/validate';
import { downLoadFile } from '@/utils/publicFun';

interface Question {
  fieldCn: string;
  fieldName: string;
  valueMethod: string;
  inputHint: string;
  attribution: {
    options?: Array<{
      value: string | number;
      label: string;
    }>;
    type?: string;
    picNum?: number;
    acceptType?: string[];
  };
  content?: Array<{
    url: string;
    name?: string;
  }>;
}

interface UploadFile {
  uid: string;
  url?: string;
  response?: {
    data: Array<{
      path: string;
      name: string;
    }>;
  };
  [key: string]: any;
}

interface Props {
  questionOne: Record<string, any>;
  editQuestionType?: string;
  questionOneRule: Record<string, any>;
  questionList: Question[];
}

const props = defineProps<Props>();
const emit = defineEmits(['closeAddQuestion', 'saveOne']);

const ruleFormRef = ref<FormInstance>();
const path = ref('');
const baseUrl = computed(() => import.meta.env.VITE_APP_BASE_API || '');
const headers = computed(() => ({
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
}));
const fit = ref('cover');
const token = ref(getToken());
const dialogVisible = ref(false);
const dialogImageUrl = ref(''); // 当前查看的大图

const localQuestionOne = computed(() => props.questionOne);

// 初始化本地数据
onMounted(() => {
  // 初始化localQuestionOne
  // Object.assign(localQuestionOne, JSON.parse(JSON.stringify(props.questionOne)));
});

// 关闭弹窗
const handleClose = () => {
  emit('closeAddQuestion');
};

// 提交问卷
const submitAddQuestion = () => {
  ruleFormRef.value?.validate((valid) => {
    if (valid) {
      emit('saveOne', localQuestionOne.value);
    } else {
      return false;
    }
  });
};

// 返回日期类型
const getFormat = (type: string): string => {
  if (type === 'year') {
    return 'yyyy';
  } else if (type === 'month') {
    return 'yyyy.MM';
  } else if (type === 'date') {
    return 'yyyy.MM.dd';
  }
  return '';
};

// 自定义解析函数，将字符串转换为 Date 对象
const parser = (value: string): Date => {
  if (!value) return new Date();
  const [year, month, day] = value.split('.');
  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
};

// 返回列表
const handleBack = () => {
  // window.history.back();
  emit('closeAddQuestion');
};

// 附件上传成功
const handleSuccessFJ = (response: any, item: Question) => {
  if (response.data && isArray(response.data)) {
    if (!localQuestionOne.value[item.fieldName]) {
      //没有值的时候赋值为数组
      localQuestionOne.value[item.fieldName] = [];
    }
    const obj: Record<string, string> = {};
    obj[`${item.fieldName}_0`] = response.data[0].path;
    obj[`${item.fieldName}_1`] = response.data[0].name;
    localQuestionOne.value[item.fieldName].push(obj);
  }
};

// 附件移除
const handleRemoveFJ = (file: UploadFile, item: Question) => {
  let num = 0;
  if (item.content && isArray(item.content)) {
    for (let index = 0; index < item.content.length; index++) {
      if (file.response?.data[0].path === item.content[index].url) {
        num = index;
        break;
      }
    }
    item.content.splice(num, 1);
  }
};

// 上传前验证
const beforeAvatarUpload = (file: File, item: Question): boolean => {
  let flg = false;
  if (item.attribution.acceptType) {
    for (let index = 0; index < item.attribution.acceptType.length; index++) {
      if (file.type.includes(item.attribution.acceptType[index])) {
        flg = true;
        break;
      }
    }
  }
  if (!flg) {
    ElMessage.error(`不支持上传${file.type}格式！！！`);
  }
  return flg;
};

// 图片上传超过最大限制提示
const handleExceed = (files: File[], fileList: UploadFile[], item: Question) => {
  if (item.attribution.picNum && fileList.length >= item.attribution.picNum) {
    ElMessage.error(`${item.fieldCn}最多允许上传${item.attribution.picNum}`);
  }
};

// 图片上传成功事件
const handleSuccessTP = (response: any, item: Question) => {
  if (response.data && isArray(response.data)) {
    if (!localQuestionOne.value[item.fieldName]) {
      //没有值的时候赋值为数组
      localQuestionOne.value[item.fieldName] = [];
    }
    const obj = {
      url: response.data[0].path
    };
    localQuestionOne.value[item.fieldName].push(obj);
  }
};

// 图片上传移除事件
const handleRemoveTP = (file: UploadFile, item: Question) => {
  let num = 0;
  if (item.content && isArray(item.content)) {
    for (let index = 0; index < item.content.length; index++) {
      if (file.response?.data[0].path === item.content[index].url) {
        num = index;
        break;
      }
    }
    item.content.splice(num, 1);
  }
};

// 图片预览
const handlePictureCardPreview = (file: UploadFile) => {
  if (file.url) {
    dialogImageUrl.value = `${baseUrl.value}/qjt/file/otherDownload/${file.url}?token=${token.value}`;
    dialogVisible.value = true;
  }
};

// 下载文件
const handleDownload = (file: UploadFile) => {
  if (file.url) {
    downLoadFile(file.url, file.url);
  }
};

// 移除照片
const handleRemove = (file: UploadFile, item: any[]) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      for (let i = 0; i < item.length; i++) {
        if (item[i].uid === file.uid) {
          item.splice(i, 1);
          break;
        }
      }
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
}
.fj-row :hover {
  color: #409eff;
}
.addQuestion-main {
  background-color: #fff;
  position: relative;
  height: 100%;
  .back {
    font-size: 14px;
    border-bottom: solid 1px #d3d3d3;
    padding-bottom: 10px;
    > div {
      display: flex;
      align-items: center;
      cursor: pointer;
      width: 45px;
    }
  }
  .dialog-content {
    overflow-y: auto;
    padding-right: 20px;
    .quesiton-main {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      margin-top: 12px;
      .question-item {
        flex: 0 0 calc(25% - 20px); /* 25% width minus the gap */
        margin-right: 20px; /* Right margin for the gap */
        margin-bottom: 20px; /* Bottom margin for the gap */
        box-sizing: border-box; /* Include padding and border in the width */
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #80808033;
  }
}
</style>
