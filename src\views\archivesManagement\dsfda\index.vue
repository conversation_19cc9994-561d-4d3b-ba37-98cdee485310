<!-- 第三方档案 -->
<template>
  <container-card>
    <div class="app-container main">
      <div class="card-container">
        <div class="card-item" v-for="item in cardList" :key="item.id">
          <div class="item-label">{{ item.label }}(份)</div>
          <div class="item-num">{{ item.num }}</div>
        </div>
      </div>
      <!-- 下方这里是表格-->
      <div class="table-container">
        <div class="search-main">
          <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="small">
            <el-form-item label="档案名称">
              <el-input v-model="searchForm.filesName" placeholder="请输入档案名称" clearable @clear="handleFilesList"></el-input>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm:ss"
                date-format="YYYY/MM/DD ddd"
                time-format="A hh:mm:ss"
                @change="handleFilesList"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilesList"
                ><el-icon><Search /></el-icon>查询</el-button
              >
              <el-button type="info" @click="handleSearchCancel"
                ><el-icon><RefreshRight /></el-icon>重置</el-button
              >
            </el-form-item>
          </el-form>
          <div>
            <el-button type="success" plain @click="handleAddArchives"
              ><el-icon><Plus /></el-icon>新增</el-button
            >
          </div>
        </div>
        <el-table :data="tableList" style="width: 100%" border :height="tableHeight">
          <el-table-column prop="filesNum" label="档案编号" width="180"> </el-table-column>
          <el-table-column prop="filesName" label="档案名称"> </el-table-column>
          <el-table-column prop="filesTypeName" label="档案类型" width="120"> </el-table-column>
          <el-table-column prop="remark" label="备注" width="180"> </el-table-column>
          <el-table-column prop="address" label="附件" width="280">
            <template #default="scope">
              <div v-for="f in scope.row.list" :key="f.fileId" class="text-over" :title="f.urlName" @click="handleDownloadOneFile(f)">
                {{ f.urlName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createUserName" label="创建人员" width="100"> </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="120">
            <template #default="scope">
              <span>{{ formatDate(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140">
            <template #default="scope">
              <el-button type="text" @click="handleDelete(scope.row)" style="color: red">删除</el-button>
              <el-button type="text" @click="handleManager(scope.row)">管理</el-button>
              <el-button type="text" @click="handleDownLoad(scope.row)" :disabled="scope.row.list.length === 0">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="list-pagination">
          <pagination
            :total="searchForm.total"
            v-model:page="searchForm.pageNum"
            v-model:limit="searchForm.pageSize"
            layout="total,sizes,pager"
            :page-sizes="[10, 50, 100, 200]"
            @pagination="handleFilesList"
          />
        </div>
      </div>
      <!-- 文件的预览 -->
      <preview-file ref="previewFileRef"></preview-file>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, onActivated } from 'vue';
import { saveFilesOpera, getFilesList, getselectMap } from '@/api/archives';
import { downLoadFile } from '@/utils/publicFun';
import previewFile from '../component/previewFile.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

// 定义接口
interface CardItem {
  id: number;
  label: string;
  num: number;
}

interface FileItem {
  fileId: string | number;
  urlName: string;
  filesUrl: string;
  name?: string;
  url?: string;
  [key: string]: any;
}

interface TableItem {
  id: number;
  filesName: string;
  filesNum: string;
  filesTypeName: string;
  remark: string;
  createUserName: string;
  createTime: number;
  list: FileItem[];
  delFlag?: number;
  operaType?: number;
  [key: string]: any;
}

interface SearchForm {
  pageNum: number;
  pageSize: number;
  total: number;
  filesName?: string;
  timeRange: string[];
}

// 初始化router
const router = useRouter();
const previewFileRef = ref();

// 数据初始化
const cardList = ref<CardItem[]>([]);
const tableList = ref<TableItem[]>([]);
const tableHeight = computed(() => window.innerHeight - 380);

// 搜索表单
const searchForm = reactive<SearchForm>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  filesName: undefined,
  timeRange: []
});

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleString();
};

// 查询按钮
const handleSearchCancel = () => {
  searchForm.filesName = undefined;
  searchForm.timeRange = [];
  handleFilesList();
};

// 统计值
const getSelectCount = () => {
  const data = {
    type: 3
  };
  getselectMap(data).then((res: any) => {
    if (res.code === 200) {
      cardList.value = handleTransList(res.data);
    } else {
      ElMessage.error(res.msg || '获取统计数据失败');
    }
  });
};

const handleTransList = (item: Record<string, number>): CardItem[] => {
  return Object.entries(item).map(([key, value], index) => ({
    id: index + 1,
    label: key,
    num: value
  }));
};

// 列表
const handleFilesList = () => {
  const data = {
    filesType: 3,
    filesName: searchForm.filesName,
    beginTime: searchForm.timeRange && searchForm.timeRange.length > 0 ? Number(searchForm.timeRange[0]) : 0,
    endTime: searchForm.timeRange && searchForm.timeRange.length > 0 ? Number(searchForm.timeRange[1]) : 32503651200000,
    pageNum: searchForm.pageNum,
    pageSize: searchForm.pageSize
  };

  getFilesList(data).then((res: any) => {
    if (res.code === 200) {
      tableList.value = res.data.list;
      searchForm.pageNum = res.data.pageNum;
      searchForm.pageSize = res.data.pageSize;
      searchForm.total = res.data.total;
    } else {
      ElMessage.error(res.msg || '获取档案列表失败');
    }
  });
};

// 新增页面跳转按钮
const handleAddArchives = () => {
  router.push({
    path: '/archives/add',
    query: {
      type: '3'
    }
  });
};

// 管理页面编辑功能
const handleManager = (row: TableItem) => {
  router.push({
    path: '/archives/add',
    query: {
      type: '3',
      id: row.id.toString()
    }
  });
};

// 点击下载按钮
const handleDownLoad = (row: TableItem) => {
  const text = `是否确认下载【${row.filesName}】的附件?`;
  ElMessageBox.confirm(text, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const list = JSON.parse(JSON.stringify(row.list));
      list.forEach(async (item: FileItem) => {
        const name = item.urlName;
        const path = item.filesUrl;
        await downLoadFile(path, name);
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 点击附件一行上传的文件内容
const handleDownloadOneFile = (file: FileItem) => {
  const obj = {
    ...file,
    name: file.urlName,
    url: file.filesUrl
  };
  previewFileRef.value.handlePreviewFile(obj);
};

// 删除当前新增的数据
const handleDelete = (row: TableItem) => {
  const text = `确认删除档案【${row.filesName}】吗?`;
  ElMessageBox.confirm(text, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const data = { ...row };
      data.delFlag = 1;
      data.operaType = 2;

      saveFilesOpera([data]).then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('删除成功！');
          handleFilesList();
          getSelectCount();
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 页面初始化
onMounted(() => {
  handleFilesList();
  getSelectCount();
});

// 组件激活时重新获取列表数据（解决从添加页面返回后列表未更新的问题）
onActivated(() => {
  handleFilesList();
  getSelectCount();
});
</script>

<style lang="scss" scoped>
.text-over {
  height: 36px;
  line-height: 36px;
  color: #0081ff;
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main {
  background-color: #fff;
  .card-container {
    display: flex;
    flex-wrap: nowrap;
    justify-items: center;
    justify-content: center; /* 整体居中 */
    height: 13%;
    margin-bottom: 24px;
    .card-item {
      flex: 0 0 calc(25% - 20px); /* 每个item占据25%宽度，减去外边距 */
      margin: 10px; /* 给定item之间的外边距 */
      border: 1px solid #ededed;
      height: 100px;
      border-radius: 4px;
      background-color: #f6f7f8;
      box-shadow: 1px 2px 3px #ededed;
      padding: 8px;
      width: 100%;
      text-align: center;
      .item-label {
        height: 30px;
        line-height: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #333;
        width: 100%;
        max-width: calc(100% - 1px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .item-num {
        width: 100%;
        height: 60px;
        line-height: 60px;
        font-size: 24px;
        color: #111112;
        font-weight: 600;
      }
    }
  }
  .table-container {
    height: calc(100% - 150px);
    width: 100%;
    position: relative;
    .search-main {
      display: flex;
      justify-content: space-between;
    }
    .list-pagination {
      position: absolute;
      right: 16px;
      bottom: -10px;
      width: 100%;
      .pagination-container {
        width: 100% !important;
      }
      :deep(.el-pagination) {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        height: 36px !important;
      }
    }
  }
}
</style>
