<template>
  <component
    :key="refreshFlagKey"
    :is="option.attribute.decorationType"
    :style="{ width: width + 'px', height: height + 'px', color: option.attribute.textColor }"
    :color="[option.attribute.color1, option.attribute.color2]"
    >{{ option.attribute.text }}</component
  >
</template>

<script setup lang="ts">
import { v1 as uuidv1 } from 'uuid';
defineOptions({
  name: 'cpt-dataV-decoration'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
const refreshFlagKey = ref(uuidv1());
watch(
  () => props.option,
  (newObj) => {
    refreshFlagKey.value = uuidv1(); //强制刷新视图
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
</script>

<style scoped></style>
