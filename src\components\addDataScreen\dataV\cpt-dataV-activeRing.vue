<template>
  <dv-active-ring-chart :key="refreshFlagKey" :config="config" style="width: 100%; height: 100%" />
</template>

<script setup lang="ts">
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-dataV-activeRing'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const uuid = ref(null);
const config = ref({});
const refreshFlagKey = ref(null);
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadData();
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
watch(
  () => props.height,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
onMounted(() => {
  uuid.value = uuidv1();
  refreshFlagKey.value = uuidv1();
  refreshCptData();
});
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
  refreshFlagKey.value = uuidv1();
};
defineExpose({
  refreshCptData
});
const loadData = () => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    // 表达式必填
    if (!props.option.cptDataForm.apiUrl) {
      ElMessage.warning('表达式不能为空');
      return;
    }
    const parmas = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };

    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.map) {
          const names = Object.keys(res.data.map);
          const values = Object.values(res.data.map);
          config.value = [];
          names.forEach((v, idx) => {
            config.value.push({ name: v, value: Number(values[idx]) });
          });
          config.value = JSON.parse(JSON.stringify(props.option.attribute));
          config.value.data = config.value;
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      config.value = JSON.parse(JSON.stringify(props.option.attribute));
      config.value.data = res;
    });
  }
};
</script>

<style scoped></style>
