<template>
  <div class="content-main">
    <div class="left-board">
      <div class="left-scrollbar">
        <div class="components-list">
          <div class="components-title">
            <div class="title-color"></div>
            快捷控件
          </div>
          <div class="components-draggable">
            <el-button type="primary" size="small" @click="autoField">shp导入生成字段</el-button>
          </div>
          <!-- <div class="components-title">
              <div class="title-color"></div> 布局控件
            </div>
            <draggable
              class="components-draggable"
              :list="layoutComponents"
              :group="{ name: 'componentsGroup', pull: 'clone', put: false }"
              :clone="cloneComponent"
              draggable=".components-item"
              :sort="false"
              @end="onEnd"
            >
              <div
                v-for="(element, index) in layoutComponents"
                :key="index"
                class="components-item"
                @click="addComponent(element)"
              >
                <div class="components-body">
                  <svg-icon :icon-class="element.icon" />
                  {{ element.label }}
                </div>
              </div>
            </draggable> -->
          <div class="components-title">
            <div class="title-color"></div>
            基础控件
          </div>
          <draggable
            class="components-draggable"
            :list="commonComponents"
            :group="{ name: 'componentsGroup', pull: 'clone', put: false }"
            :clone="cloneComponent"
            draggable=".components-item"
            :sort="false"
            @end="onEnd"
          >
            <template #item="{ element, index }">
              <div class="components-item" @click="addComponent(element)" v-show="!element.isHide">
                <div class="components-body">
                  <svg-icon :icon-class="element.icon" />
                  {{ element.label }}
                </div>
              </div>
            </template>
          </draggable>
          <div class="components-title">
            <div class="title-color"></div>
            增强控件
          </div>
          <draggable
            class="components-draggable"
            :list="customComponents"
            :group="{ name: 'componentsGroup', pull: 'clone', put: false }"
            :clone="cloneComponent"
            draggable=".components-item"
            :sort="false"
            @end="onEnd"
          >
            <template #item="{ element, index }">
              <div class="components-item" @click="addComponent(element)">
                <div class="components-body">
                  <svg-icon :icon-class="element.icon" />
                  {{ element.label }}
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </div>
    <div class="center-board">
      <div class="board-title">
        <el-button v-show="type != 2" :icon="ArrowLeft" style="color: #161d26; margin-left: 10px" type="text" @click="handleBack">返回</el-button>
        <div class="group-title" :title="currentGroupItem.typeName">
          {{ spanRule(currentGroupItem.typeName, 30) }}
        </div>
        <el-button class="delete-btn" style="margin-right: 16px" icon="el-icon-circle-check" type="text" @click="handleSubmitSave">保存</el-button>
      </div>
      <div class="center-scrollbar" style="margin-bottom: 20px">
        <el-row class="center-board-row">
          <el-form label-position="top" :disabled="formConf.disabled" style="width: 100%">
            <!-- :label-width="formConf.labelWidth + 'px'" -->
            <draggable class="drawing-board" itemKey="formId" :list="drawingList" :animation="200" group="componentsGroup" @end="onMianDragEnd">
              <template #item="{ element, index }">
                <NewDraggableItem
                  v-show="element.delFlag != 1"
                  :drawing-list="drawingList"
                  :element="element"
                  :index="index"
                  :active-id="activeId"
                  :form-conf="formConf"
                  :put="shouldClone"
                  @click="activeFormItem(element)"
                  @activeChild="activeChild"
                  @copyItem="drawingItemCopy"
                  @deleteItem="drawingItemDelete"
                  @updateLabel="handleupdateTableLabel"
                ></NewDraggableItem>
              </template>
            </draggable>
            <div v-show="!drawingList.filter((item) => item.delFlag != 1).length" class="empty-info">从左侧拖入或点选组件进行表单设计</div>
          </el-form>
        </el-row>
      </div>
    </div>
    <!-- TODO -->
    <right-panel
      :active-data-prop="activeData"
      :form-conf-prop="formConf"
      :show-field-prop="drawingList.length > 0 ? true : false"
      :drawing-list-prop="drawingList"
      @tag-change="tagChange"
      ref="activeDataRef"
      @updateField="handleConfToFieldList"
      @updateLabel="handleupdateLabel"
    />
    <!-- 读取shp字段弹窗TODO -->
    <getFieldForShp
      :fieldShpDialog="fieldShpDialog"
      :drawingList="drawingList"
      @handlCloseFieldDialog="handlCloseFieldDialog"
      @submitShpField="submitShpField"
    ></getFieldForShp>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter } from 'vue-router';
import draggable from 'vuedraggable';
import render from './components/render';
import RightPanel from './RightPanel';
import DraggableItem from './DraggableItem.vue';
import getFieldForShp from './getFieldForShp.vue';
import {
  inputComponents,
  selectComponents,
  layoutComponents,
  commonComponents,
  formConf as defaultFormConf,
  customComponents
} from './components/generator/config.ts';
import { saveFieldGroup, saveFieldInOwner } from '@/api/modal/index';
import { spanRule } from '@/utils/filters';
import { ArrowLeft } from '@element-plus/icons-vue';
import { any } from 'vue-types';
const oldActiveId = 0;
let tempActiveData = {
  formId: oldActiveId,
  renderKey: 'input',
  label: '姓名'
};

interface FormItem {
  formId: string | number;
  renderKey?: string;
  label: string;
  placeholder?: string;
  tagIcon?: string;
  delFlag?: number;
  id?: number;
  // ... other properties
}

interface FormConfig {
  size: string;
  labelPosition: string;
  disabled: boolean;
  // ... other properties
}

interface BankCardExpendItem {
  label: number;
  text: string;
  enName: string;
  cnName: string;
  strLength: string;
  maxlength: string;
  inputHint: string;
  valueMethod: string;
  options?: any;
}

interface bankCardItem {
  label: string;
  vModel: string;
  inputType: string;
  placeholder: string;
  tagIcon: string;
  required: boolean;
  id?: number;
  groupId?: number;
  delFlag?: number;
  layout?: string;
  bankCardSelectList: number[];
  expendList: BankCardExpendItem[];
  [key: string]: any; // 允许有其他属性
}

const props = defineProps({
  confProp: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  },
  isFieldFormProp: {
    type: Boolean,
    default: false
  },
  isTypeProp: {
    type: String,
    default: ''
  },
  typeProp: {
    type: Number,
    default: 1
  }
});
const conf = computed(() => props.confProp);
const isfieldForm = computed(() => props.isFieldFormProp);
const isType = computed(() => props.isTypeProp);
const type = computed(() => props.typeProp);

const emit = defineEmits(['goBack', 'submitField', 'updateGroup']);

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();

const formConf = ref<FormConfig>({ ...defaultFormConf });
const drawingList = ref<FormItem[]>([]);
const activeId = ref<number>(0);
const activeData = ref<FormItem>({} as FormItem);
const fieldShpDialog = ref(false);
const drawLeng = ref(0);
const oldDrawingList = ref<FormItem[]>([]);
const activeDataRef = ref<InstanceType<typeof RightPanel>>();
const formData = ref<any>(null);
const confParams = ref<any>(null); // 组中存取字段中的数据

// Computed properties
const isGroupForm = computed(() => JSON.parse(JSON.stringify(modalStore.isGroupForm)));
const elementType = computed(() => modalStore.elementType);
const groupId = computed(() => modalStore.groupId);
const currentGroupItem = computed(() => modalStore.currentGroupItem);
const placeholderValue = computed({
  get() {
    return activeData.value.placeholder;
  },
  set(val) {
    const inputList = ['input', 'textarea', 'number', 'xtsjy'];
    const selectList = ['select', 'radio', 'checkbox', 'time', 'date', 'date-range', 'xtzsdl', 'cascader'];
    const uploadList = ['upload', 'xtfj', 'xtsjjt', 'xtaudio', 'xtvideo'];
    let strValue = '';
    if (activeData.value.isPlaceholder) {
      strValue = activeData.value.placeholder;
    } else {
      if (activeData.value.tagIcon == 'area') {
        // 行政区域暂时去掉
        // const list =[
        //   {value:2,text:'(省市)'},
        //   {value:3,text:'(省市区)'},
        //   {value:4,text:'(省市区街道)'},
        //   {value:5,text:'(省市区街道社区)'},
        //   {value:6,text:'(省市区街道社区-详细信息)'},
        // ]
        // let arr = list.filter(item =>item.value == this.activeData.xzqyLevel)
        // if(arr.length >0){
        //   strValue = `请选择${this.activeData.label}${arr[0].text}`
        // }
        strValue = `请选择${val}`;
      } else if (activeData.value.tagIcon == 'idCardScan') {
        strValue = activeData.value.placeholder;
      } else if (inputList.includes(activeData.value.tagIcon)) {
        strValue = `请输入${val}`;
      } else if (selectList.includes(activeData.value.tagIcon)) {
        strValue = `请选择${val}`;
      } else if (uploadList.includes(activeData.value.tagIcon)) {
        strValue = `请上传${val}`;
      } else {
        if (!(activeData.value.id && activeData.value.placeholder)) {
          strValue = `${val}`;
        }
      }
      activeData.value.placeholder = strValue;
    }
  }
});
// Methods
// 父组件主动调用 适用于问卷模板类型
const initCof = (conf: any) => {
  if (typeof conf === 'object' && conf !== null) {
    if (conf.fields && conf.fields.length != 0) {
      drawingList.value = conf.fields;
      // 回显数据的时候 如果expression 有值的时候。defaultSelect = 3
      // TODO 这个是解决了当字段没有保存退出去之后，在进入页面的时候该字段还在的问题
      // 重要，请暂时不要删除这里的注释内容
      // Object.assign(this.formConf, this.conf)
    } else {
      drawingList.value = [];
    }
  }
};

const activeFormItem = (element: any) => {
  if (element) {
    activeData.value = element;
    activeId.value = element.formId;
    // placeholderValue.value = element.label;
    // this.activeData.placeholder = this.placeholderValue
    if (element.tagIcon == 'xtsjjt') {
      activeDataRef.value.getModuleDetial();
    }
  }
};
/**
 * 组件的子数据选中
 * @param element 子数据
 */
const activeChild = (element: any) => {
  if (element) {
    activeData.value = element;
    activeId.value = element.formId;
    placeholderValue.value = element.label;
    // this.activeData.placeholder = this.placeholderValue
    if (element.tagIcon == 'xtsjjt') {
      activeDataRef.value.getModuleDetial();
    }
  }
};
// 更新值内容数据
const handleupdateLabel = (val: string) => {
  placeholderValue.value = val;
};
/**
 * 阻止表格中嵌套行容器（和动物/植物识别、身份证识别、联系人控件）
 * 定制组件不能添加子组件
 * 行容器中不能套行
 */
const shouldClone = (to: any, from: any, target: any, event: any, conf: any) => {
  // .drawing-row-item —— 行容器的类名 ipad里面的组件才会带有
  // 直接拖拽的行容器 最外层含有.drawing-row-item
  // 定制组件 内部含有.drawing-row-item
  // const hasRow = target.classList.contains('.drawing-row-item') || target.querySelector('.drawing-row-item') !== null
  // const isRowContainer = ['布局容器', '表格/明细'].includes(target.innerText) //是阻止从左侧拖拽嵌套
  // const isCusFromLeft = target.classList.contains('custom-component')
  const targetConf = target._underlying_vm_;
  const isRowContainer = conf.cmpType === 'common' && conf.rowType === 'layout';
  // 行容器中不能套行
  if (isRowContainer) {
    if (targetConf.layout === 'rowFormItem') {
      return false;
    }
    return true;
  }
  // 定制组件不能再嵌套数据
  if (conf.cmpType === 'custom') return false;
  // 表格中不能添加容器和动物/植物识别
  if (conf.rowType === 'table') {
    if (targetConf.layout === 'rowFormItem') {
      return false;
    } else if (targetConf.tagIcon == 'xtzwsb' || targetConf.tagIcon == 'xtdwsb' || targetConf.tagIcon == 'idCardScan') {
      // 表格中禁止使用动植物识别打补丁
      // this.$message.warning("表格中不能添加动植物识别")
      return false;
    } else if (targetConf.tagIcon == 'xtlxr') {
      // 因为联系人的2个字段是写死的，我直接拿出来传给app 并没有把姓名和手机号拿出来放到children 中
      // 导致再删除的时候只删除了姓名和手机号的字段 而没有删除联系人的外层的字段
      return false;
    }
  }
  return true;
};
// 这里是处理表格中的提示语的回显的问题
const handleupdateTableLabel = (item: any, parentList: any[]) => {
  // this.activeData 当前最新拖进来的数据  每次只给当前最新的数据赋值placeholde
  for (let i = 0; i < parentList.length; i++) {
    const e = parentList[i];
    if (e.rowType == 'table' && e.children.length > 0) {
      for (let j = 0; j < e.children.length; j++) {
        const c = e.children[j];
        if (activeData.value.formId == c.formId) {
          placeholderValue.value = c.label;
          break;
        }
      }
    }
    //  break  这里不该有break  因为可能会有多个表格的情况
  }
  return placeholderValue.value;
};
const onEnd = (obj: any, a: any) => {
  if (obj.from !== obj.to) {
    activeId.value = tempActiveData.formId;
    activeData.value = tempActiveData;
  }
};

const onMianDragEnd = (obj: any, a: any) => {
  activeFormItem(drawingList.value[obj.newIndex]);
};
const getSameTagCmpNum = (tagIcon: string) => {
  return drawingList.value.reduce((count, item) => {
    if (item.children) {
      return (
        count +
        item.children.reduce((c, t) => {
          return t.tagIcon === tagIcon ? c + 1 : c;
        }, 0)
      );
    }
    return item.tagIcon === tagIcon ? count + 1 : count;
  }, 0);
};
const createCmpLabel = (cmp: any) => {
  // const len = this.getSameTagCmpNum(cmp.tagIcon)
  // return len ? cmp.label + len : cmp.label
  return cmp.label;
};

const addComponent = (item: FormItem) => {
  // item.placeholder = ''
  const clone = cloneComponent(item);
  drawingList.value.push(clone);
  activeFormItem(clone);
  // 在这里更新最新添加的控件 供当前属性组选中其中的字段内容
  handleConfToFieldList();
  // 更新当前控件的提示语内容
  placeholderValue.value = item.label;
  item.placeholder = placeholderValue.value;
};
// 处理当前表格中已有的字段，实时监听字段，共表达式中的当前属性组添加的字段选择
const handleConfToFieldList = () => {
  // this.conf.fields  只有在编辑的时候才会有值 新建的没有值 所有使用this.drawingList
  // 在这里的特殊情况的值要全部处理成formulaEditorDialog 中的所需要处理城的字段，见下面的标记~~~
  const fieldList = [];
  // 处理成树结构想要的数据结构
  drawingList.value.forEach((item) => {
    if (item.delFlag != 1) {
      // 身份证识别
      if (item.tagIcon == 'idCardScan') {
        const sfzItem = {
          fieldCn: item.label,
          fieldName: item.vModel,
          fieldType: item.inputType,
          inputHint: item.placeholder,
          valueMethod: item.tagIcon,
          required: item.required ? 1 : 2,
          defaults: '否',
          id: item.id,
          groupId: this.groupId,
          children: [],
          attribution: {
            list: [] // 这里存放添加下级~~~
          }
        };
        const SFZSBOptions = [
          { label: 0, text: '姓名', enName: 'xm', cnName: '姓名' },
          { label: 1, text: '性别', enName: 'xb', cnName: '性别' },
          { label: 2, text: '民族', enName: 'mz', cnName: '民族' },
          {
            label: 3,
            text: '出生日期',
            enName: 'csrq',
            cnName: '出生日期'
          },
          { label: 4, text: '住址', enName: 'zz', cnName: '住址' },
          {
            label: 5,
            text: '身份证号',
            enName: 'sfzh',
            cnName: '身份证号'
          },
          {
            label: 6,
            text: '签发机关',
            enName: 'qfjg',
            cnName: '签发机关'
          },
          {
            label: 7,
            text: '有效期限',
            enName: 'yxqx',
            cnName: '有效期限'
          },
          {
            label: 8,
            text: '身份证正面',
            enName: 'sfzzm',
            cnName: '身份证正面'
          },
          {
            label: 9,
            text: '身份证反面',
            enName: 'sfzfm',
            cnName: '身份证反面'
          }
        ];
        SFZSBOptions.forEach((sfz) => {
          if (item.sfzsbList && item.sfzsbList.length > 0) {
            if (item.sfzsbList.includes(sfz.label)) {
              const obj = {
                fieldCn: sfz.text,
                fieldName: sfz.enName,
                fieldType: sfz.inputType,
                inputHint: sfz.placeholder,
                valueMethod: sfz.tagIcon,
                required: sfz.required ? 1 : 2,
                defaults: '否',
                id: sfz.id,
                isChild: true, // 判断是不是子要素
                groupId: groupId.value //  当前添加的字段属于权属组的id
                // delFlag:item.delFlag?item.delFlag:0,
              };
              sfzItem.attribution.list.push(sfz.label); //~~~
              sfzItem.children.push(obj); //~~~
            }
          }
        });
        fieldList.push(sfzItem);
        // 身份证识别处理结束
      } else if (item.tagIcon == 'xtdwsb') {
        const dwItem = {
          fieldCn: item.label,
          fieldName: item.vModel,
          fieldType: item.inputType,
          inputHint: item.placeholder,
          valueMethod: item.tagIcon,
          required: item.required ? 1 : 2,
          defaults: '否',
          id: item.id,
          isChild: true, // 判断是不是子要素
          groupId: groupId.value,
          children: [],
          attribution: {
            list: [] // 这里存放添加下级~~~
          }
        };
        // 处理动植物识别
        // 动物识别和植物识别是都需要分开写---防止同一个属性组中同事添加了动植物识别，英文名无法区分
        const DZWSBOptions = [
          { label: 0, text: '名称', enName: 'dwmc' },
          { label: 1, text: '图片', enName: 'dwtp' },
          { label: 2, text: '类型', enName: 'dwlx' },
          { label: 3, text: '描述', enName: 'dwms' }
        ];
        DZWSBOptions.forEach((dzw) => {
          if (item.sbList && item.sbList.length > 0) {
            if (item.sbList.includes(dzw.label)) {
              const obj = {
                fieldCn: dzw.text,
                fieldName: dzw.enName,
                fieldType: item.inputType,
                inputHint: item.placeholder,
                valueMethod: item.tagIcon,
                required: item.required ? 1 : 2,
                defaults: '否',
                id: item.id,
                isChild: true, // 判断是不是子要素
                groupId: groupId.value
              };
              // 推送到对应的字段下面
              dwItem.attribution.list.push(dzw.label); //~~~
              dwItem.children.push(obj); //~~~
            }
          }
        });
        fieldList.push(dwItem);
        // 动植物识别处理结束
      } else if (item.tagIcon == 'xtzwsb') {
        const zwItem = {
          fieldCn: item.label,
          fieldName: item.vModel,
          fieldType: item.inputType,
          inputHint: item.placeholder,
          valueMethod: item.tagIcon,
          required: item.required ? 1 : 2,
          defaults: '否',
          id: item.id,
          groupId: groupId.value,
          children: [],
          attribution: {
            list: [] // 这里存放添加下级~~~
          }
        };
        // 处理植物识别
        const DZWSBOptions = [
          { label: 0, text: '名称', enName: 'zwmc' },
          { label: 1, text: '图片', enName: 'zwms' },
          { label: 2, text: '类型', enName: 'zwlx' },
          { label: 3, text: '描述', enName: 'zwms' }
        ];
        DZWSBOptions.forEach((dzw) => {
          if (item.sbList && item.sbList.length > 0) {
            if (item.sbList.includes(dzw.label)) {
              const obj = {
                fieldCn: dzw.text,
                fieldName: dzw.enName,
                fieldType: item.inputType,
                inputHint: item.placeholder,
                valueMethod: item.tagIcon,
                required: item.required ? 1 : 2,
                defaults: '否',
                id: item.id,
                isChild: true, // 判断是不是子要素
                groupId: this.groupId
              };
              // 推送到对应的字段下面
              zwItem.attribution.list.push(dzw.label); //~~~
              zwItem.children.push(obj); //~~~
            }
          }
        });
        fieldList.push(zwItem);
        // 动植物识别处理结束
      } else if (item.tagIcon == 'xttable') {
        // 处理表格
        // ！！！重要说明 如果将来添加了布局容器，并且布局容器中的字段也存在attribution的children 中
        // ！！！可以借鉴这里来实现单个取值的问题
        const tableItem = {
          fieldCn: item.label,
          fieldName: item.vModel,
          fieldType: item.inputType,
          inputHint: item.placeholder,
          valueMethod: item.tagIcon,
          required: item.required ? 1 : 2,
          defaults: '否',
          id: item.id,
          groupId: groupId.value,
          attribution: {
            // 这里存放添加下级
            children: [] //~~~
          }
        };
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            const obj = {
              fieldCn: child.label,
              fieldName: child.vModel,
              fieldType: child.inputType,
              inputHint: child.placeholder,
              valueMethod: child.tagIcon,
              required: child.required ? 1 : 2,
              defaults: '否',
              id: child.id,
              isChild: true, // 判断是不是子要素
              groupId: groupId.value
            };
            // 推送到对应的字段下面
            tableItem.attribution.children.push(obj); //~~~
          });
          fieldList.push(tableItem);
        }
        // 表格处理结束
      } else {
        const obj = {
          fieldCn: item.label,
          fieldName: item.vModel,
          fieldType: item.inputType,
          inputHint: item.placeholder,
          valueMethod: item.tagIcon,
          required: item.required ? 1 : 2,
          defaults: '否',
          id: item.id,
          groupId: groupId.value //  当前添加的字段属于权属组的id
        };
        fieldList.push(obj);
      }
    }
  });
  // TODO 未知问题
  // resetSetItem('noSaveField', JSON.stringify(fieldList));
};
const getNextId = () => {
  const maxId = getMaxId() + 1;
  return maxId;
};
const getMaxId = () => {
  if (drawingList.value.length) {
    let maxId = 0;
    const loop = (data, parent) => {
      if (!data) return;
      Array.isArray(data.children) && data.children.forEach((child) => loop(child, data));
      if (Array.isArray(data)) {
        data.forEach(loop);
      } else {
        maxId = Math.max(data.formId, maxId);
      }
    };
    loop(drawingList.value);
    return maxId;
  }
  return 0;
};
const cloneComponent = (origin: any) => {
  const clone = JSON.parse(JSON.stringify(origin));
  clone.formId = getNextId();
  // clone.span = formConf.span;
  clone.renderKey = clone.formId + new Date().getTime(); // 改变renderKey后可以实现强制更新组件
  if (!clone.layout) clone.layout = 'colFormItem';
  if (clone.layout === 'colFormItem') {
    clone.label = createCmpLabel(clone);
    clone.vModel = `field${clone.formId}`;
    if (clone.tagIcon == 'idCardScan') {
      clone.placeholder !== undefined && clone.placeholder;
    } else {
      clone.placeholder !== undefined && (clone.placeholder += clone.label);
    }
    tempActiveData = clone;
  } else if (clone.layout === 'rowFormItem') {
    if (clone.rowType === 'table') {
      clone.vModel = `field${clone.formId}`;
    } else if (clone.rowType == 'layout') {
      // 行布局容器的代码
      clone.vModel = `field${clone.formId}`;
    }
    // delete clone.label;
    clone.componentName = `row${clone.formId}`;
    clone.gutter = formConf.value.gutter;
    cloneChildrenOfRowFormItem(clone);
    tempActiveData = clone;
  }
  // else if (clone.layout === "tableFormItem") {
  //   if (clone.rowType === 'table') {
  //     clone.vModel = `field${clone.formId}`;
  //   }else if(clone.rowType == 'layout'){
  //     // 行布局容器的代码
  //     clone.vModel = `field${clone.formId}`;
  //   }
  //   // delete clone.label;
  //   clone.componentName = `row${clone.formId}`;
  //   clone.gutter = this.formConf.gutter;
  //   this.cloneChildrenOfRowFormItem(clone);
  //   tempActiveData = clone;
  // }
  return tempActiveData;
};
const cloneChildrenOfRowFormItem = (rowFormItem: any) => {
  if (rowFormItem.children && rowFormItem.children.length) {
    const children = rowFormItem.children;
    children.forEach((clone, index) => {
      clone.formId = rowFormItem.formId + index + 1;
      // clone.span = formConf.span;
      clone.renderKey = clone.formId + new Date().getTime(); // 改变renderKey后可以实现强制更新组件
      if (!clone.layout) clone.layout = 'colFormItem';
      if (clone.layout === 'colFormItem') {
        clone.vModel = `field${clone.formId}`;
        clone.placeholder !== undefined && (clone.placeholder += clone.label);
        // 特殊处理  处理身份证识别、签名和指纹的内容
      } else if (clone.layout === 'rowFormItem') {
        delete clone.label;
        clone.componentName = `row${clone.formId}`;
        clone.gutter = formConf.value.gutter;
        cloneChildrenOfRowFormItem(clone);
      }
    });
  }
};

const isEmptyRowContainer = () => {
  // const rowContainer = this.drawingList.find(t => t.layout === 'rowFormItem')
  // if(rowContainer){
  //   return rowContainer.children.length === 0
  // }
  const rowContainerList = drawingList.value.filter((t) => t.rowType === 'layout');
  if (rowContainerList.length > 0) {
    return rowContainerList.some((item) => {
      return item.children.length === 0;
    });
  }
};

const isEmptyTableContainer = () => {
  // const rowContainer = this.drawingList.find(t => t.layout === 'rowFormItem')
  // if(rowContainer){
  //   return rowContainer.children.length === 0
  // }
  const tableContainerList = drawingList.value.filter((t) => t.rowType === 'table');
  if (tableContainerList.length > 0) {
    return tableContainerList.some((item) => {
      return item.children.length === 0;
    });
  }
};

const AssembleFormData = () => {
  formData.value = {
    ...formConf.value,
    fields: JSON.parse(JSON.stringify(drawingList.value))
  };
};

const drawingItemCopy = (item: FormItem, parent: FormItem[]) => {
  let clone = JSON.parse(JSON.stringify(item));
  clone = createIdAndKey(clone);
  parent.push(clone);
  activeFormItem(clone);
};
const createIdAndKey = (item: FormItem) => {
  item.formId = getNextId();
  item.renderKey = clone.formId + new Date().getTime();
  if (item.layout === 'colFormItem') {
    item.vModel = `field${item.formId}`;
  } else if (item.layout === 'rowFormItem') {
    item.componentName = `row${item.formId}`;
  }
  if (Array.isArray(item.children)) {
    item.children = item.children.map((childItem) => createIdAndKey(childItem));
  }
  return item;
};

/**
 * 删除字段
 * @param index 索引
 * @param isChild 是否是子项
 * @param field 表格字段
 */
const drawingItemDelete = async (index: number, isChild: boolean, field: any) => {
  let parent = <any[]>[];
  if (!isChild) {
    parent = drawingList.value;
  } else {
    parent = field.children;
  }
  // 因为联系人控件在回显映射id 的时，会把id 映射在子级上导致直接删除这个字段，所以要特殊处理联系人控件以下
  if (parent[index].tagIcon == 'xtlxr' && !parent[index].id) {
    parent[index].childrenList.forEach((ite) => {
      // this.$set(ite,'delFlag',1)
      ite.delFlag = 1;
      // 直接调用接口删除字段
      // 这里需要把所有字段穿都传值
      const p1 = handleShareField(parent[index]);
      saveFieldInOwner([p1]).then((res) => {});
    });
    parent[index].delFlag = 1;
  } else {
    // 非联系人控件继续使用之前的删除处理方法
    //先判断是否有id 有id就deflag = 1 没id就直接删除
    if (parent[index].id) {
      // parent[index].delFlag = 1;
      // for(let i = 0;i<this.drawingList.length;i++){
      //   if(this.drawingList[i].id == parent[index].id){ //找到删除
      //     this.drawingList.splice(this.drawingList[i],1)
      //   }
      // }
      // this.$set(parent[index],'delFlag',1)
      // 添加一个字段，判断表单中的字段是否有被删除，如果有 则做返回提示
      parent[index].delItem = true;
      // this.$set(parent[index],'delFlag',1)
      // 直接调用接口删除字段
      const params = [{ id: parent[index].id, delFlag: 1 }];
      // 这里需要把所有字段穿都传值
      const p1 = handleShareField(parent[index]);
      saveFieldInOwner([p1]).then((res) => {
        if (res.code == 200) {
          parent.splice(index, 1);
        }
      });
    } else {
      parent.splice(index, 1);
    }
  }
  nextTick(() => {
    // 但数组中没有有用的值的时候 要清空右侧内容
    const list = drawingList.value.filter((item) => item.delFlag != 1);
    const len = list.length;
    if (len) {
      activeFormItem(drawingList.value[len - 1]);
    } else {
      activeFormItem({});
    }
  });
  // 在这里更新最新添加的控件 供当前属性组选中其中的字段内容
  handleConfToFieldList();
};

const tagChange = (newTag: any) => {
  newTag = cloneComponent(newTag);
  newTag.vModel = activeData.value.vModel;
  newTag.formId = activeId.value;
  newTag.span = activeData.value.span;
  delete activeData.value.tag;
  delete activeData.value.tagIcon;
  //   delete this.activeData.document;
  Object.keys(newTag).forEach((key) => {
    if (activeData.value[key] !== undefined && typeof activeData.value[key] === typeof newTag[key]) {
      newTag[key] = activeData.value[key];
    }
  });
  activeData.value = newTag;
  updateDrawingList(newTag, drawingList.value);
};
const updateDrawingList = (newTag: any, list: any[]) => {
  const index = list.findIndex((item) => item.formId === activeId.value);
  if (index > -1) {
    list.splice(index, 1, newTag);
  } else {
    list.forEach((item) => {
      if (Array.isArray(item.children)) updateDrawingList(newTag, item.children);
    });
  }
};

const handleShareField = (item: FormItem) => {
  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType,
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    id: item.id,
    status: item.status,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      layout: item.layout,
      // 多行/单行文本最大字数的限制
      maxLength: item.maxlength == '' || !item.maxlength ? 3000 : item.maxlength,
      // 数字输入框的小数位数
      precision: item.precision,
      // 输入框的精度位数
      accuracy: item.accuracy,
      // 数字类型
      numberType: item.numberType,
      // 数字输入框的单位
      unit: item.unit,
      // 省市区/级联选择的是否可过滤
      filterable: item.filterable,
      // checkBox的多选组的最多选项和最少选项
      checkBoxMax: item.max,
      checkBoxMin: item.min,
      picNum: item.picNum == '' || item.picNum == '不限制' ? 100 : parseInt(item.picNum),
      // 图片的文件上传大小
      fileSize: item.fileSize ? parseInt(item.fileSize) : 0,
      // 手机号和身份证正则校验
      regItem: item.regItem,
      // 表达式
      expression: item.expression,
      //  是否表达式单次刷新
      isOnceRefeshExpression: item.isOnceRefeshExpression,
      // 默认值
      defaultValue: item.defaultValue,
      // 计数器专用
      isStep: item.isStep,
      // 步长
      step: item.step,
      // 是否合计
      isCount: item.isCount,
      // 是否加入合计
      isTotal: item.isTotal,
      // 是否统计
      isTotalCount: item.isTotalCount
    }
  };
  // 视频和音频设置为不限制上传文件大小---图片限制默认1
  if (item.tagIcon == 'xtvideo' || item.tagIcon == 'xtaudio') {
    fieldItem.attribution.fileSize = item.fileSize == '' || item.fileSize == '不限制' ? 0 : parseInt(item.fileSize);
    fieldItem.attribution.picNum = item.picNum == '' || item.picNum == '不限制' ? 10 : parseInt(item.picNum);
    // 视频文件时长
    fieldItem.attribution.timeSize = item.timeSize ? parseInt(item.timeSize) : 0;
  }
  // 追溯地类
  if (item.tagIcon == 'xtzsdl') {
    fieldItem.attribution.dileiTypelist = item.dileiTypelist;
  }
  // 数据源
  if (item.tagIcon == 'xtsjy') {
    fieldItem.attribution.sjyCheckList = item.sjyCheckList;
  }
  // 数据截图（草图的计算）
  if (item.tagIcon == 'xtsjjt') {
    fieldItem.attribution.attrIdList = item.attrIdList;
  }
  // 日期区间的内容
  if (item.tagIcon == 'date-range') {
    if (Array.isArray(item.defaultValue) && item.defaultValue.length > 0) {
      fieldItem.attribution.defaultValue = item.defaultValue.join(' 至 ');
    }
  }
  // 日期选择的内容
  if (item.tagIcon == 'date' && item.icon == 'dateIcon') {
    fieldItem.attribution.timeWarning = item.timeWarning;
    fieldItem.attribution.timeDate = item.timeDate;
    fieldItem.attribution.frequency = item.frequency;
    fieldItem.attribution.message = item.message;
    fieldItem.attribution.type = item.type;
  }

  // 图片的是否开启方位角和是否显示属性
  if (item.tagIcon == 'upload') {
    fieldItem.attribution.attrList = item.attrList;
    fieldItem.attribution.sizeList = item.sizeList;
  }
  // 上传附件的内容
  if (item.tagIcon == 'xtfj') {
    //附件数量
    fieldItem.attribution.fileNum = item.fileNum;
    // 单个附件大小
    fieldItem.attribution.oneFileSize = item.oneFileSize;
    // 接受的类型
    fieldItem.attribution.acceptType = item.acceptType;
  }
  // 多行文本的最大行数和最小行数
  if (item.tagIcon == 'textarea') {
    fieldItem.attribution.maxRows = item.autosize.maxRows;
    fieldItem.attribution.minRows = item.autosize.minRows;
  }
  // 省市区/级联选择是否可多选
  if (item.tagIcon == 'cascader' || item.tagIcon == 'area') {
    // fieldItem.attribution.xzqyLevel =  item.xzqyLevel
    fieldItem.attribution.areaOptions = item.areaOptions;
  } else {
    // 选项值
    fieldItem.attribution.options = item.options;
    // 把当前字符串的类型加入进入
    fieldItem.attribution.optionIsJson = item.optionIsJson;
  }
  // 设置多选框的默认值  行政区域 图片和附件
  if (item.tagIcon == 'checkbox' || item.tagIcon == 'area' || item.tagIcon == 'upload' || item.tagIcon == 'xtfj' || item.tagIcon == 'xtsjjt') {
    if (Array.isArray(item.defaultValue)) {
      fieldItem.attribution.defaultValue = item.defaultValue.join(',');
    } else {
      fieldItem.attribution.defaultValue = item.defaultValue;
    }
  } else {
    // 选项值
    fieldItem.attribution.options = item.options;
  }
  if (item.id) {
    fieldItem.id = item.id;
  }
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  return fieldItem;
};
// 供父组件使用 获取表单JSON
const getData = () => {
  return new Promise((resolve, reject) => {
    if (drawingList.value.length === 0) {
      reject('表单不允许为空！');
      return;
    }
    if (isEmptyRowContainer()) {
      reject('您的行容器中没有组件！');
      return;
    }
    if (isEmptyTableContainer()) {
      reject('表格控件中没有设置组件！');
      return;
    }
    AssembleFormData();
    resolve({ formData: formData.value });
  });
};
const execCopy = (data: any) => {
  document.getElementById('copyNode').click();
};
const handleSubmitSave = () => {
  if (isType.value == '') {
    handleSave();
  } else {
    handleTucengSave(isType.value);
  }
};
// 保存图层设置中的字段
const handleTucengSave = (type: number) => {
  // 使用链式筛选有用的数据
  const validateState = drawingList.value
    .filter((item) => item.delFlag != 1)
    .filter((item) => !item.delItem)
    .every((item) => {
      if (item.vModel !== '' && item.label !== '') {
        return item.vModel !== '' && item.label !== '';
      }
    });
  if (validateState) {
    const name = getData()
      .then((res) => {
        const list = sendToServer(res);
        const endList = list.filter((item) => item.delFlag != 1);
        endList.forEach((v) => {
          // 输入框 多行输入框如果最多输入为非的时候传参默认1000
          if (v.valueMethod == 'input' || v.valueMethod == 'textarea') {
            if (!v.attribution.maxLength) {
              v.attribution.maxLength = -1;
            } else {
              v.attribution.maxLength = parseInt(v.attribution.maxLength);
            }
          } else if (v.valueMethod == 'xttable') {
            //表格特殊处理
            v.attribution.children.forEach((k) => {
              // 联系人控件 要在下面handleExlxrLayout()方法中特殊处理 添加maxlength 的值
              if (!k.attribution.maxLength) {
                k.attribution.maxLength = -1;
              } else {
                k.attribution.maxLength = parseInt(k.attribution.maxLength);
              }
            });
          }
        });
        sessionStorage.removeItem('noSaveField');
        emit('submitField', endList, confParams.value);
      })
      .catch((res) => {
        ElMessage.error(res);
      });
  } else {
    ElMessage.error('请检查字段名称和字段别名的输入是否正确');
  }
};
// 保存属性组中的字段
const handleSave = () => {
  // 保存前需要先判断是否存在移动已保存过的字段到表格里面 该移动是内部处理的，会导致外面的字段未提交给后台删除，还会存在
  // 所以需要判断oldDrawingList的某个字段是否在this.drawingList中的某个表格字段里存在，
  // 如果存在那么就需要把这条数据在this.drawingList体现出来，并赋值为删除状态！！！！
  const delList: any[] = []; // 已经删除的字段 需要提交给后台
  drawingList.value.forEach((item) => {
    if (item.tagIcon == 'xttable') {
      // 循环表格中的字段 判断在oldDrawingList中是否存在
      item.children.forEach((child) => {
        for (let i = 0; i < oldDrawingList.value.length; i++) {
          if (oldDrawingList.value[i].id && oldDrawingList.value[i].vModel == child.vModel) {
            delList.push(JSON.parse(JSON.stringify(oldDrawingList.value[i])));
            break;
          }
        }
      });
    }
  });

  // 如果delList 存在值，那么需要把delList中的字段赋值给this.drawingList 并且赋值为删除状态
  delList.forEach((item) => {
    item.delFlag = 1;
  });
  drawingList.value.push(...delList);
  // 使用链式筛选有用的数据
  const validateState = drawingList.value
    .filter((item) => item.delFlag != 1)
    .filter((item) => !item.delItem)
    .every((item) => {
      if (item.vModel !== '' && item.label !== '') {
        return item.vModel !== '' && item.label !== '';
      }
    });

  if (validateState) {
    const name = getData()
      .then((res) => {
        const list = sendToServer(res);
        // let endList = list.filter((item) => item.delFlag != 1);
        const endList = list;
        endList.forEach((v) => {
          // 输入框 多行输入框如果最多输入为非的时候传参默认1000
          if (v.valueMethod == 'input' || v.valueMethod == 'textarea') {
            if (!v.attribution.maxLength) {
              v.attribution.maxLength = -1;
            } else {
              v.attribution.maxLength = parseInt(v.attribution.maxLength);
            }
          } else if (v.valueMethod == 'xttable') {
            //表格特殊处理
            v.attribution.children.forEach((k) => {
              // 联系人控件 要在下面handleExlxrLayout()方法中特殊处理 添加maxlength 的值
              if (!k.attribution.maxLength) {
                k.attribution.maxLength = -1;
              } else {
                k.attribution.maxLength = parseInt(k.attribution.maxLength);
              }
            });
          }
        });
        if (type.value == 1) {
          saveFieldInOwner(endList).then((res) => {
            if (res.code == 200) {
              ElMessage.success('保存成功');
              if (isGroupForm.value && isGroupForm.value.isShowGroup) {
                emit('submitField', isGroupForm.value);
              } else {
                emit('submitField');
              }
              nextTick(() => {
                drawingList.value = [];
              });
              endList.forEach((v) => {
                res.data.forEach((k) => {
                  if (v.fieldName == k.fieldName) {
                    v.id = k.id;
                  }
                });
              });
              handleSaveGroup(endList, list);
              // 保存之后恢复到默认值的值
              activeData.value.defalutSelect = 1;
              // 清空存在缓存中的新建的字段
              sessionStorage.removeItem('noSaveField');

              // 再点击返回和保存的时候 需要清空数据草图的默认值 当前选中的节点的默认值defaultCheckedKeys
              const defaultList = JSON.parse(JSON.stringify(activeDataRef.value.defaultCheckedKeys));
              if (defaultList.length > 0) {
                activeDataRef.value.defaultCheckedKeys = [];
              }
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else if (type.value == 2) {
          //问卷调查
          if (list.length == 0) {
            ElMessage.error('问卷调查不能没有内容！！！');
            return;
          }
          emit('submitField', list, confParams.value);
        }
      })
      .catch((res) => {
        ElMessage.error(res);
      });
  } else {
    ElMessage.error('请检查字段名称和字段别名的输入是否正确');
  }
};
// 保存组数据下的字段，方便后期回显数据
const handleSaveGroup = (list: any, allList: any) => {
  // list  是字段数据，判断字段中有没有list[index].attribution.expression 是否有值，
  // 如果有值是界址线或者界址点，如果没有就是普通的点和线
  // let groupItem= JSON.parse(sessionStorage.getItem('groupItem'))
  // 子要素中：groupSoce == 2
  // 点：有表达式 graphicalPoint  无表达式 commonPoint
  // 线：有表达式 graphicalLine   无表达式 commonLine

  // 子要素中选择采集权属人中 不管有没有表达式
  // 点 graphicalPoint
  // 面：graphicalLine
  // 其他中：（采集权属人） groupscope ==1
  // graphicalType == 1 graphicalPoint  点
  // graphicalType == 2 graphicalLine  线
  // graphicalType == 3 graphicalArea  面
  // graphicalType == 4 graphicalNull  无图行
  //
  const groupItem = JSON.parse(JSON.stringify(currentGroupItem.value));

  let ruleAttribution = null;
  // let isExpression = list.some(item => {
  //   if(item.attribution && item.attribution.expression){
  //     return (item.attribution.expression != null) || (item.attribution.expression != '')
  //   }
  // })
  // if(isExpression && this.elementType === "point"){
  //   ruleAttribution ={
  //     type:'graphicalPoint',
  //   }
  // }else if(!isExpression && this.elementType === "point"){
  //   ruleAttribution ={
  //     type:'commonPoint',
  //   }
  // }else if(isExpression && this.elementType === "line"){
  //   ruleAttribution ={
  //     type:'graphicalLine',
  //   }
  // }else if(!isExpression && this.elementType === "line"){
  //   ruleAttribution ={
  //     type:'commonLine',
  //   }
  // }else if(!isExpression && !this.elementType){
  //   ruleAttribution = null
  // }
  if (groupItem.groupScope == 1 && elementType.value === 'point') {
    ruleAttribution = {
      type: 'graphicalPoint'
    };
  } else if (groupItem.groupScope == 2 && elementType.value === 'point') {
    ruleAttribution = {
      type: 'commonPoint'
    };
  } else if (groupItem.groupScope == 1 && elementType.value === 'line') {
    ruleAttribution = {
      type: 'graphicalLine'
    };
  } else if (groupItem.groupScope == 2 && elementType.value === 'line') {
    ruleAttribution = {
      type: 'commonLine'
    };
  } else {
    ruleAttribution = null;
  }
  const ite_confParams = JSON.parse(confParams.value);
  //  这里需要给后端在 回显的Json 中拼凑数据
  const confList = handleConfParamsByConfParams(ite_confParams, list);
  const groupParams = {
    // 提交的全数组属于范围1 模块 2规则 可以采用复制方式到另一个模块即可实现公司共享
    groupScope: groupItem.groupScope,
    iconUrl: groupItem.iconUrl,
    moduleId: groupItem.moduleId,
    operaType: 1, //操作类型 1 新增  2 删除
    typeName: groupItem.typeName,
    wordName: '',
    linkType: 1,
    remark: groupItem.remark,
    id: groupItem.id,
    fieldModelList: allList,
    //  因为如果属性组中设置了管理的属性组中 会有 linkGroupScope 和 isMoreNode 字段，如下这样的写法会覆盖这俩个值，所以修改
    // attribution: JSON.parse(this.confParams),
    // attribution: {
    // 	formData:confParams.formData
    // },
    ruleAttribution: ruleAttribution
  };
  groupParams.attribution = {
    formData: confList.formData,
    // 引用属性组
    linkGroupScope: groupItem.attribution && groupItem.attribution.linkGroupScope ? groupItem.attribution.linkGroupScope : undefined,
    //  是否跨节点
    isMoreNode: groupItem.attribution && groupItem.attribution.isMoreNode ? groupItem.attribution.isMoreNode : undefined
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    groupParams.companyId = companyId;
  }
  // 调用组接口保存配置
  saveFieldGroup(groupParams).then((res) => {
    if (res.code == 200) {
      // 更新数据到groupModelList 中
      emit('updateGroup', groupParams);
      // 更新组的设置内容之后再清空组的类型
      modalStore.setElementType('');
    }
  });
};
// 判断当前的json 中里面的字段是否是没有 id ,如果没有id则需要从list 中取出对应的id
const handleConfParamsByConfParams = (params: any, list: any[]) => {
  const fieldList = JSON.parse(JSON.stringify(params.formData.fields));
  const resultList = [];
  fieldList.forEach((item) => {
    if (item.id) {
      resultList.push(item);
    } else {
      // if 不存在的情况下 需要从list 找
      const finds = list.filter((ite) => ite.fieldName == item.vModel && ite.fieldCn == item.label);
      if (finds.length > 0) {
        const findId = finds[0].id;
        item.id = findId;
        resultList.push(item);
      }
    }
  });

  params.formData.fields = JSON.parse(JSON.stringify(resultList));
  return params;
};
// 如果是行布局的情况  （已禁用——————行可以嵌套行(一层),但不可以多个行嵌套 ）
const handleRowLayout = (item: any, fieldList: any[]) => {
  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType, //  当行布局的时候没有这个字段
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    id: item.id,
    status: item.status,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      children: [],
      tableType: item.tableType, // 表格的时候，显示展示的内容，table 表格，list 是列表
      defaultRow: item.defaultRow,
      attrExpend: item.attrExpend,
      openMonthArea: item.openMonthArea,
      isTotalCount: item.isTotalCount
    }
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  // if(item.rowType == 'layout'){
  // }else if(item.rowType == 'table'){
  //   this.handleTableRowLayout(item,fieldList)
  // }
  if (item.children && item.children.length > 0) {
    try {
      item.children.forEach((ite, index) => {
        // 这里只有行布局中添加表格，而不是表格中添加表格（表格中不允许再继续添加表格）
        if (ite.rowType == 'table' && ite.children && ite.children.length > 0) {
          // 对表格数据做特殊处理
          const objItem = handleTableLayout(ite);
          fieldItem.attribution.children.push(objItem);
        } else if (ite.tagIcon == 'xtlxr' && ite.childrenList && ite.childrenList.length > 0) {
          // 在这里处理联系人的情况
          const list = handleExlxrLayout(ite.childrenList);
          const arrList = JSON.parse(JSON.stringify(list));
          // 直接复制会再次套用一层，是否深拷贝 转换数据推送一项item 到数组中
          arrList.forEach((a) => {
            fieldItem.attribution.children.push(a);
          });
        } else {
          // 签名和指纹的时候 相对应attribution的值为null  默认值
          const isCustomType = ite.tagIcon == 'xtqm' || ite.tagIcon == 'xtzw' || ite.tagIcon == 'xtzwsb' || ite.tagIcon == 'xtdwsb';
          if (isCustomType) {
            const oneItem = handleSpecialField(ite);
            fieldItem.attribution.children.push(oneItem);
            // fieldList.push(fieldItem)
          } else {
            // 这里一行的数据应该处理成一个数据内容
            const oneItem = handleShareField(ite);
            fieldItem.attribution.children.push(oneItem);
            // fieldList.push(fieldItem)
          }
        }
        // 当角标和数组长度相等的时候  要跳出循环
        if (index == item.children.length - 1) {
          fieldList.push(fieldItem);
          throw Error('循环完毕');
        }
      });
    } catch (error) {
      //
    }
  }
  return fieldList;
};
// 处理布局容器中套用表格控件
const handleTableLayout = (item: any) => {
  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType, //  当行布局的时候没有这个字段
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    id: item.id,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      children: [] //  目前联系人控件暂时没有什么属性内容
    }
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  // 签名和指纹的时候 相对应attribution的值为null  默认值
  item.children.forEach((it) => {
    const isCustomType = it.tagIcon == 'xtqm' || it.tagIcon == 'xtzw' || it.tagIcon == 'xtzwsb' || it.tagIcon == 'xtdwsb';
    // 判断是不是联系人控件
    const isXtlxr = it.tagIcon == 'xtlxr';

    if (isCustomType) {
      const oneItem = handleSpecialField(it);
      fieldItem.attribution.children.push(oneItem);
    } else if (isXtlxr) {
      // 特殊处理联系人
      const list = handleExlxrLayout(it.childrenList);
      const arrList = JSON.parse(JSON.stringify(list));
      // 直接复制会再次套用一层，是否深拷贝 转换数据推送一项item 到数组中
      arrList.forEach((a) => {
        fieldItem.attribution.children.push(a);
      });
    } else {
      // 这里一行的数据应该处理成一个数据内容
      const oneItem = handleShareField(it);
      fieldItem.attribution.children.push(oneItem);
    }
  });
  return fieldItem;
};
// 签名指纹、动物识别和植物识别调用的接口
const handleSpecialField = (item: any) => {
  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType,
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2, //  必填是1 不必填是2
    defaults: '否',
    delFlag: item.delFlag ? item.delFlag : 0,
    id: item.id,
    status: item.status,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    attribution: {}
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  // 动物识别和植物识别的内容
  if (item.tagIcon == 'xtzwsb' || item.tagIcon == 'xtdwsb') {
    // 0 名称 1图片 2 类型 3 描述
    fieldItem.attribution.list = [0, 1, 2, 3];
  }
  // 是否允许电子签名
  if (item.tagIcon == 'xtqm') {
    if (item.inputType == 'qm') {
      fieldItem.fieldType = 'Pic';
    }
    fieldItem.attribution.esign = item.esign;
    // 为了湖北交投项目添加的取权利人下面的个人签名，试用表达式，app 在签名的位置直接展示用户的签名内容。
    fieldItem.attribution.expression = item.expression;
    fieldItem.attribution.isOnceRefeshExpression = item.isOnceRefeshExpression;
  }
  // 打补丁--- 在这里处理老数据中的json 中的签名和指纹是inputType 的值是 qm/zw
  if (item.tagIcon == 'xtzw') {
    if (item.inputType == 'zw') {
      fieldItem.fieldType = 'Pic';
    }
    // 为了湖北交投项目添加的取权利人下面的个人签名，试用表达式，app 在签名的位置直接展示用户的签名内容。
    fieldItem.attribution.expression = item.expression;
    fieldItem.attribution.isOnceRefeshExpression = item.isOnceRefeshExpression;
  }
  // if (item.tagIcon == 'xtqm' && item.inputType == 'qm') {
  // 	fieldItem.fieldType = 'Pic'
  // }
  return fieldItem;
};
// 处理布局容器中套用联系人控件
const handleExlxrLayout = (list: any[]) => {
  const fieldList = [];
  list.forEach((item) => {
    const fieldItem = {
      fieldCn: item.label,
      fieldName: item.vModel,
      status: item.status,
      fieldType: item.inputType, //  当行布局的时候没有这个字段
      inputHint: item.placeholder,
      valueMethod: item.tagIcon,
      required: item.required ? 1 : 2,
      defaults: '否',
      id: item.id,
      groupId: groupId.value, //  当前添加的字段属于权属组的id
      delFlag: item.delFlag ? item.delFlag : 0,
      attribution: {
        // children:[],  //  目前联系人控件暂时没有什么属性内容
        maxLength: item.maxlength
      }
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = router.currentRoute.value.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      fieldItem.companyId = companyId;
    }
    fieldList.push(fieldItem);
  });

  return fieldList;
};
const sendToServer = (options: any) => {
  // let  groupId = this.$store.state.modal.groupId

  // 在这里每次删除delItem = true  的值
  options.formData.fields.map((item) => {
    delete item.delItem;
  });
  const params = {};
  params.config = JSON.stringify(options);
  confParams.value = params.config;
  const fieldList = [];
  let fieldItem = {};
  options.formData.fields.forEach((item) => {
    if (item.layout == 'rowFormItem' && item.children && item.children.length > 0) {
      // 行布局
      handleRowLayout(item, fieldList);
      // if(item.rowType == 'layout'){

      // }else if(item.rowType == 'table'){
      //   this.handleTableRowLayout(item,fieldList)
      // }
    } else {
      // 签名和指纹的时候 相对应attribution的值为null  默认值
      const isCustomType = item.tagIcon == 'xtqm' || item.tagIcon == 'xtzw' || item.tagIcon == 'xtzwsb' || item.tagIcon == 'xtdwsb';
      // 判断是不是联系人控件
      const isXtlxr = item.tagIcon == 'xtlxr';
      // 判断是不是身份证识别

      const isSfzsb = item.tagIcon == 'idCardScan';
      //  判断是不是缴费控件
      const isPay = item.tagIcon == 'xtpay';
      // 判断是不是银行卡识别控件
      const isBankCard = item.tagIcon == 'xtBankCard';
      if (isCustomType) {
        fieldItem = handleSpecialField(item);
        fieldList.push(fieldItem);
      } else if (isXtlxr) {
        // 特殊处理联系人
        item.childrenList.forEach((child) => {
          // 因为字段生效和失效的值在外层字段中，这里需要特殊处理一个字段
          child.status = item.status;
          fieldItem = handleShareField(child);
          fieldList.push(fieldItem);
        });
      } else if (isSfzsb) {
        //  处理身份证 识别的控件
        fieldItem = handleSFZSBField(item);
        fieldList.push(fieldItem);
      } else if (isPay) {
        //  处理身份证 识别的控件
        fieldItem = handlePayField(item);
        fieldList.push(fieldItem);
      } else if (isBankCard) {
        //  处理银行卡 识别的控件
        fieldItem = handleBankCardField(item);
        fieldList.push(fieldItem);
      } else {
        fieldItem = handleShareField(item);
        fieldList.push(fieldItem);
      }
    }
  });
  return fieldList || [];
};
/**
 * 处理银行卡识别的字段
 * @param item 当前 需要更新的这一项
 */
const handleBankCardField = (item: bankCardItem) => {
  const expendList = [];
  if (item.bankCardSelectList.length > 0 && item.expendList && item.expendList.length > 0) {
    item.expendList.forEach((attr) => {
      if (item.bankCardSelectList.includes(attr.label)) {
        expendList.push({
          label: attr.label,
          text: attr.text,
          enName: attr.enName,
          cnName: attr.cnName,
          strLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          maxLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          inputHint: `${attr.inputHint}${attr.cnName}`,
          valueMethod: attr.valueMethod,
          options: attr.options ? attr.options : undefined
        });
      }
    });
  }
  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType,
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    id: item.id,
    status: item.status,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      layout: item.layout,
      // 支付识别的选择
      list: item.bankCardSelectList.sort(),
      // 支付
      expendList: expendList
    }
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  return fieldItem;
};
// 处理身份证识别的控件内容
const handleSFZSBField = (item: any) => {
  const expendList = [];
  if (item.sfzsbList.length > 0 && item.expendList && item.expendList.length > 0) {
    item.expendList.forEach((attr) => {
      if (item.sfzsbList.includes(attr.label)) {
        expendList.push({
          label: attr.label,
          text: attr.text,
          enName: attr.enName,
          cnName: attr.cnName,
          strLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          maxLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          inputHint: `${attr.inputHint}${attr.cnName}`,
          valueMethod: attr.valueMethod,
          options: attr.options ? attr.options : undefined
        });
      }
    });
  }

  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType,
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    id: item.id,
    status: item.status,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      layout: item.layout,
      // 身份证识别的选择值
      list: item.sfzsbList.sort(),
      // 身份证识别的内容
      expendList: expendList,
      // 身份证识别是否共享
      isShare: item.isShare
    }
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  return fieldItem;
};
//处理缴费这个字段
const handlePayField = (item: any) => {
  const payList = [];
  if (item.paySelectList.length > 0 && item.payList && item.payList.length > 0) {
    item.payList.forEach((attr) => {
      if (item.paySelectList.includes(attr.label)) {
        payList.push({
          label: attr.label,
          text: attr.text,
          enName: attr.enName,
          cnName: attr.cnName,
          strLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          maxLength: attr.strLength != '' && attr.strLength != null ? parseInt(attr.strLength) : 3000,
          inputHint: `${attr.inputHint}${attr.cnName}`,
          valueMethod: attr.valueMethod,
          options: attr.options ? attr.options : undefined
        });
      }
    });
  }

  const fieldItem = {
    fieldCn: item.label,
    fieldName: item.vModel,
    fieldType: item.inputType,
    inputHint: item.placeholder,
    valueMethod: item.tagIcon,
    required: item.required ? 1 : 2,
    defaults: '否',
    status: item.status,
    id: item.id,
    groupId: groupId.value, //  当前添加的字段属于权属组的id
    delFlag: item.delFlag ? item.delFlag : 0,
    attribution: {
      layout: item.layout,
      // 支付识别的选择
      paySelectList: item.paySelectList.sort(),
      // 支付
      payList: payList
    }
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    fieldItem.companyId = companyId;
  }
  return fieldItem;
};

//  返回按钮
const handleBack = () => {
  // delItem  判断字段如果改变了，则提示语，没有改变则直接返回
  // const isUpdate = drawingList.value.some((item) => item.id == undefined || item.delItem == true || item.delFlag == 1);
  // const isUpdate = JSON.stringify(oldDrawingList.value) === JSON.stringify(drawingList.value);
  let isUpdate = true;
  //首先判断数组长度是否一致
  if (drawingList.value.length != oldDrawingList.value.length) {
    isUpdate = false;
  } else {
    // 然后需要判断每条数据转换成json字符串进行判断
    for (let i = 0; i < drawingList.value.length; i++) {
      if (JSON.stringify(drawingList.value[i]) !== JSON.stringify(oldDrawingList.value[i])) {
        isUpdate = false;
        break;
      }
    }
  }
  // 判断当前按数组字段长度是否一样
  const isLeng = drawLeng.value !== drawingList.value.length;
  if (!isUpdate || isLeng) {
    ElMessageBox.confirm('字段已改变，请确定是否保存?', '提示', {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        handleSave();
        sessionStorage.removeItem('noSaveField');
      })
      .catch(() => {
        drawingList.value = oldDrawingList.value;
        // 判断当前字段是否时子要素添加的 如果是子要素添加则在返回之后再打开子要素的弹框
        // if (isGroupForm.value && isGroupForm.value.isShowGroup) {
        //   emit('goBack', isGroupForm.value);
        // } else {
        //   emit('goBack');
        // }
        handleSave();
        // 清空存在缓存中的新建的字段
        sessionStorage.removeItem('noSaveField');
      });
  } else {
    // 字段未发生任何改变的的时候 直接返回
    nextTick(() => {
      drawingList.value = [];
    });

    if (conf.value.fields && conf.value.fields.length != 0) {
      // 直接返回 代表不对字段进行修改，所以还是这样的删除
      conf.value.fields.forEach((item) => {
        item.delFlag = 0;
      });
    }
    // 保存之后恢复到默认值的值
    activeData.value.defalutSelect = 1;
    // 判断当前字段是否时子要素添加的 如果是子要素添加则在返回之后再打开子要素的弹框
    if (isGroupForm.value && isGroupForm.value.isShowGroup) {
      emit('goBack', isGroupForm.value);
    } else {
      emit('goBack');
    }
  }
  // 再点击返回和保存的时候 需要清空数据草图的默认值 当前选中的节点的默认值defaultCheckedKeys
  const defaultList = JSON.parse(JSON.stringify(activeDataRef.value.defaultCheckedKeys));
  if (defaultList.length > 0) {
    activeDataRef.value.defaultCheckedKeys = [];
  }
};
// 自动生成字段
const autoField = () => {
  fieldShpDialog.value = true;
};
const handlCloseFieldDialog = () => {
  fieldShpDialog.value = false;
};
// shp里面提交
const submitShpField = (fileds) => {
  fileds.forEach((v) => {
    drawingList.value.push(v);
    // 这里每次激活最后一个
    activeFormItem(v);
  });
  // 这里每次激活第一个
  // this.activeFormItem(this.drawingList[0])

  fieldShpDialog.value = false;
};

// Watchers
watch(
  () => props.isFieldFormProp,
  (val) => {
    // ... watch implementation
    if (!val) return;
    if (val) {
      if (typeof conf.value === 'object' && conf.value !== null) {
        drawingList.value = [];
        if (conf.value.fields && conf.value.fields.length != 0) {
          drawLeng.value = conf.value.fields.length;
          conf.value.fields.forEach((ite) => {
            if (ite.tagIcon == 'xttable' && !('attrExpend' in ite)) {
              // 为了湖北交投的项目新增表格属性字段的扩展属性，但是已经建立数据 是没有 attrExpend 这个属性的，所以强制推送一个
              ite.attrExpend = {
                tableList: [],
                tableData: [],
                rows: '',
                attrs: []
              };
              ite.openMonthArea = false;
            } else if (ite.tagIcon == 'select' || ite.tagIcon == 'radio' || ite.tagIcon == 'checkbox') {
              //强制推送一个条件跳转默认值 下拉选择、单选、多选
              ite.conditionSetting = [];
            }
            //为了处理 从模板过来的情况没有初始化
            if (ite.status === undefined || ite.status === null || ite.status === '') {
              ite.status = 1; //默认生效状态
            }
            drawingList.value.push(ite);
          });
          // 针对字段的生效和失效排序
          drawingList.value.sort((a: any, b: any) => (b.status ?? 0) - (a.status ?? 0));
          oldDrawingList.value = JSON.parse(JSON.stringify(drawingList.value));
          // this.drawingList = this.conf.fields;
          // 回显数据的时候 如果expression 有值的时候。defaultSelect = 3
          // TODO 这个是解决了当字段没有保存退出去之后，在进入页面的时候该字段还在的问题
          // 重要，请暂时不要删除这里的注释内容
          // Object.assign(this.formConf, this.conf)
        }
        // else {
        //   this.drawingList = [];
        // }
      } else {
        // const drawingListInDB = getDrawingList()
        // const hasStorage = Array.isArray(drawingListInDB) && drawingListInDB.length > 0
        // this.drawingList = hasStorage ? drawingListInDB : drawingDefalut
        // formConfInDB && (this.formConf = formConfInDB)
      }
      // 更新右侧 rightPannel 的值
      const currentActiveDate = drawingList.value.find((item) => item.delFlag != 1);
      activeFormItem(currentActiveDate);
    }
  },
  { immediate: true, deep: true }
);

watch(
  drawingList,
  (val) => {
    //用于右侧菜单是否显示
    activeDataRef.value.initShow(val);
    // 特殊处理下表格字段 标注出来是表格的子字段 主要是来处理要加的表格合计以及总计等内容
    drawingList.value.forEach((e) => {
      if (e.tagIcon == 'xttable') {
        if (e.isTotalCount == undefined) {
          //是否需要总计
          e.isTotalCount = false;
        }
        e.children.forEach((child) => {
          if (child.tagIcon == 'number' && child.isCount == undefined) {
            //只有数字框才允许加合计
            child.isCount = true;
            child.isTotal = false;
          }
        });
      }
    });
  },
  { deep: true }
);

// Lifecycle hooks
onMounted(() => {
  // ... initialization code
});
defineExpose({
  initCof
});
</script>

<style lang="scss">
@import './styles/home.scss';

.app-container {
  overflow-y: hidden;
}

.el-date-editor .el-range-separator {
  box-sizing: content-box;
}
</style>

<style lang="scss" scoped>
.content-main {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  display: flex;
  border-radius: 8px;
}
</style>
