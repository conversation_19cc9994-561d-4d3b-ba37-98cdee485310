import request from '@/utils/request';
import { getToken } from '@/utils/auth';
import { AxiosPromise } from 'axios';
import { ForumData, ForumQuery, UploadData } from '@/api/forum/types';

/**
 * 获取论坛列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getForumCheckList(params: ForumQuery): AxiosPromise<any> {
  return request({
    url: '/system/option/list',
    method: 'post',
    data: params
  });
}

/**
 * 审核论坛 同意checkStatus = 1 拒绝 checkStatus = 0 取消delFlag = 1
 * @param params 论坛数据
 * @returns {AxiosPromise}
 */
export function editForum(params: ForumData): AxiosPromise<any> {
  return request({
    url: '/system/option/edit',
    method: 'post',
    data: params
  });
}

/**
 * 添加主题
 * @param params 主题数据
 * @returns {AxiosPromise}
 */
export function addForum(params: ForumData): AxiosPromise<any> {
  return request({
    url: '/system/option/add',
    method: 'post',
    data: params
  });
}

/**
 * 论坛添加主题时的上传图片
 * @param params 上传数据
 * @returns {AxiosPromise}
 */
export function uploadImgae(params: UploadData): AxiosPromise<any> {
  return request({
    url: '/system/option/multi/upload',
    method: 'post',
    data: params
  });
}

/**
 * 上传测试
 * @param params 上传数据
 * @returns {AxiosPromise}
 */
export function uploadTestHuaWEei(params: UploadData): AxiosPromise<any> {
  return request({
    url: '/qjt/smvideo/upload',
    method: 'post',
    data: params
  });
}

/**
 * 下载测试
 * @param id 文件ID
 * @returns {AxiosPromise}
 */
export function downLaodTestHuaWEei(id: string | number): AxiosPromise<any> {
  return request({
    url:`/qjt/smvideo/detail/${id}`,
    method: 'get',
    headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' }
  });
}

/**
 * 下载文件的路径
 * @param path 文件路径
 * @returns {AxiosPromise}
 */
export function downloadFile(path: string): AxiosPromise<Blob> {
  return request({
    url: `/system/user/profile/downloadone/${path}`,
    method: 'get',
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Authorization': "Bearer " + getToken()
    },
    responseType: 'blob'
  });
} 