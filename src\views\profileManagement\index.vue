<template>
  <container-card>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <base-info></base-info>
      </el-tab-pane>
      <el-tab-pane label="当前公司信息" name="four" v-hasPermi="['system:company:edit']">
        <company></company>
      </el-tab-pane>
      <el-tab-pane label="加入的组织" name="second">
        <organization-index></organization-index>
      </el-tab-pane>
      <el-tab-pane label="订单详情" name="third">
        <order-index></order-index>
      </el-tab-pane>
      <el-tab-pane
        label="数据迁移"
        name="five"
        v-if="user?.phonenumber === '18285070490' || user?.phonenumber === '15286029713' || user?.phonenumber === '13885752069'"
      >
        <dataTransfer ref="dataTransferRef"></dataTransfer>
      </el-tab-pane>
    </el-tabs>
    <!-- <div
      class="resizable-box"
      :style="{ width: boxWidth + 'px', height: boxHeight + 'px' }"
      
    >
      <div class="handle" @mousedown.stop.prevent="initResize"></div>
    </div> -->
  </container-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, computed, reactive } from 'vue';
import { useUserStore } from '@/store/modules/user';
import BaseInfo from './component/baseInfo.vue';
import OrganizationIndex from './component/organizationIndex.vue';
import OrderIndex from './component/OrderIndex.vue';
import company from './component/company.vue';
import dataTransfer from './component/dataTransfer.vue';

interface UserInfo {
  phonenumber?: string;
  [key: string]: any;
}

interface ResizeState {
  dragging: boolean;
  resizing: boolean;
  boxWidth: number;
  boxHeight: number;
  startX: number;
  startY: number;
  startWidth: number;
  startHeight: number;
}

const userStore = useUserStore();
const activeName = ref('first');
const dataTransferRef = ref();

// 获取用户信息
const user = computed<UserInfo>(() => userStore.user);
const roles = computed<string[]>(() => userStore.roles);

// 监听activeName变化
watch(activeName, (val) => {
  if (val === 'five') {
    dataTransferRef.value?.getData();
  }
});

// 拖拽和调整大小相关的状态
const state = reactive<ResizeState>({
  dragging: false,
  resizing: false,
  boxWidth: 200,
  boxHeight: 200,
  startX: 0,
  startY: 0,
  startWidth: 0,
  startHeight: 0
});

const doDrag = (event: MouseEvent) => {
  if (state.dragging) {
    state.boxWidth = Math.max(20, event.clientX - state.startX);
    state.boxHeight = Math.max(20, event.clientY - state.startY);
  }
};

const stopDrag = () => {
  state.dragging = false;
};

const doResize = (event: MouseEvent) => {
  if (state.resizing) {
    state.boxWidth = Math.max(20, state.startWidth + event.clientX - state.startX);
    state.boxHeight = Math.max(20, state.startHeight + event.clientY - state.startY);
  }
};

const stopResize = () => {
  state.resizing = false;
};

// 生命周期钩子
onMounted(async () => {
  // 确保获取用户信息
  await userStore.getInfo();

  document.addEventListener('mousemove', doDrag);
  document.addEventListener('mouseup', stopDrag);
  document.addEventListener('mousemove', doResize);
  document.addEventListener('mouseup', stopResize);
});

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', doDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', doResize);
  document.removeEventListener('mouseup', stopResize);
});
</script>

<style lang="scss" scoped>
.resizable-box {
  position: relative;
  background-color: #ddd;
  border: 1px solid #ccc;
}

.handle {
  position: absolute;
  top: 100%;
  left: 100%;
  width: 10px;
  height: 10px;
  background-color: #fff;
  cursor: nwse-resize;
}
</style>
