// 首页页面的所有接口
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import JSONbig from 'json-bigint'; // 解决超过 16 位数字精度丢失问题
import { HomeQuery, HomeData, TaskData, TransportData } from '@/api/home/<USER>';

/**
 * 当前宗地连和新增数据统计
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getWebStatistics(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/web/statistics',
    method: 'get',
    params: params
  });
}

/**
 * 查询宗地任务--管理人可以查看全部数据，其他角色只能看自己创建的自己收到的任务
 * @param type 任务类型
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getSearchTask(type: string | number, params: TaskData): AxiosPromise<any> {
  return request({
    url: `/qjt/qjttask/search/task/${type}`,
    method: 'post',
    data: params
  });
}

/**
 * 任务量列表，参数不传表示全部
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getWebworkloadList(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/web/workloadList',
    method: 'get',
    params: params
  });
}

/**
 * 任务量统计，参数不传递表示全部
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getworkloadStatistics(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/web/workloadStatistics',
    method: 'get',
    params: params
  });
}

/**
 * 任务量排名
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getworkloadRanking(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/web/workloadRanking',
    method: 'get',
    params: params
  });
}

/**
 * 数据迁移记录
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function transportList(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/data/transport/record',
    method: 'get',
    params: params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 新增迁移数据
 * @param params 迁移数据
 * @returns {AxiosPromise}
 */
export function doTransport(params: TransportData): AxiosPromise<any> {
  return request({
    url: '/qjt/data/transport/do',
    method: 'post',
    data: params
  });
}

/**
 * 迁移进度
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function transportSpeed(params: HomeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/data/transport/speed',
    method: 'get',
    params: params
  });
}
