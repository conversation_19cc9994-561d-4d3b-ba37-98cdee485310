<template>
  <div class="container-card">
    <el-card class="card">
      <slot></slot>
    </el-card>
  </div>
</template>
<style lang="scss" scoped>
.container-card {
  width: 100%;
  height: 100%;
  padding: 10px 10px 20px 10px;
  box-sizing: border-box;
  .card {
    width: 100%;
    height: 100%;
  }
  :deep(.el-card__body) {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 15px !important;
    overflow-y: auto;
  }
}
</style>
