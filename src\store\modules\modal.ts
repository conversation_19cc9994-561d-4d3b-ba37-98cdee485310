import { defineStore } from 'pinia';
import { ref } from 'vue';

// 定义类型接口
interface ProcessCondition {
  formId: string | number;
  [key: string]: any;
}

interface GroupItem {
  [key: string]: any;
}

export const useModalStore = defineStore('modal', () => {
  // 定义状态
  const cropperIcon = ref<string>(''); // 设置剪裁的图标
  const customPic = ref<Record<string, any>>({}); // 设置照片对象
  const moduleId = ref<number>(0); // 存取模块管理出的moduleId
  const groupId = ref<number>(0); // 存取模块管理出的groupId
  const ruleId = ref<number>(0); // 规则ID
  const processConditions = ref<ProcessCondition[]>([]); // processConditions 用于传递流程图需要的条件
  const elementType = ref<string>(''); // 存取点/线要素设置  点 point  线  line
  const pointGroupList = ref<any[]>([]); // 存取界址点下的属性组选择的属性组
  const currentGroupItem = ref<GroupItem>({}); // TODO存取最新增加的组的数据
  const isGroupForm = ref<Record<string, any>>({}); // 判断字段弹框打开或者关闭
  const isHasAcquistion = ref<boolean>(false); // 判断是否是采集要素设置的时候进入到增加字段的内容，用来展示公式列表是否有属性组函数表达式
  const checkedNodeItem = ref<Record<string, any>>({}); // 存取当前树结构中的某一个节点
  const isAllGroup = ref<boolean>(false); // 判断设置自定义表达式的时候调用接口取出数据 拼凑属性组的设置
  const speGroups = ref<any[]>([]); //存取特殊属性组的数据 第二步
  const nodeTree = ref<any[]>([]); // 存取树结构的数据

  // 判断是否存在条件的辅助函数
  const hasCondition = (formId: string | number, needIndex = false): boolean | number => {
    const index = processConditions.value.findIndex((d) => d.formId === formId);
    return needIndex ? index : index > -1;
  };

  // 设置剪裁的图标
  const setCropperIcon = (icon: string): void => {
    cropperIcon.value = icon;
  };

  // 设置照片对象
  const setCustomPic = (pic: Record<string, any>): void => {
    customPic.value = pic;
  };

  // 设置模块ID
  const setModuleId = (id: number): void => {
    moduleId.value = id;
  };

  // 设置组ID
  const setGroupId = (id: number): void => {
    groupId.value = id;
  };

  // 设置规则ID
  const setRuleId = (id: number): void => {
    ruleId.value = id;
  };

  // 删除流程条件
  const delPCondition = (formId: string | number): void => {
    if (formId === null || formId === undefined) return;
    const index = hasCondition(formId, true) as number;
    if (index > -1) {
      processConditions.value.splice(index, 1);
    }
  };

  // 添加流程条件
  const addPCondition = (data: ProcessCondition): void => {
    if (data.formId === null || data.formId === undefined) return;
    if (!hasCondition(data.formId)) {
      processConditions.value.unshift(data);
    }
  };

  // 初始化流程条件
  const initPConditions = (data: ProcessCondition[]): void => {
    processConditions.value = data;
  };

  // 设置点和线的内容
  const setElementType = (type: string): void => {
    elementType.value = type;
  };

  // 设置点线的组
  const setPointGroupList = (list: any[]): void => {
    pointGroupList.value = list;
  };

  // 设置当前组项
  const setCurrentGroupItem = (item: GroupItem): void => {
    currentGroupItem.value = item;
  };

  // 判断是否是子要素组进入
  const setIsGroupForm = (form: Record<string, any>): void => {
    isGroupForm.value = form;
  };

  // 判断当前的进入字段的选项是从权属组还是采集设置组进入的
  const setIsHasAcquition = (value: boolean): void => {
    isHasAcquistion.value = value;
  };

  // 存取当前的树下的树结构
  const setCheckedNodeItem = (item: Record<string, any>): void => {
    checkedNodeItem.value = item;
  };

  // 判断当前的进入字段的选项是从权属组还是采集设置组进入的
  const setIsAllGroup = (value: boolean): void => {
    isAllGroup.value = value;
  };

  // 存入当前属性组的特殊属性组内容
  const setSpeGroups = (value: any[]): void => {
    speGroups.value = value;
  };

  // 存入当前模块的树结构
  const setNodeTree = (value: any[]): void => {
    nodeTree.value = value;
  };

  return {
    // 状态
    cropperIcon,
    customPic,
    moduleId,
    groupId,
    ruleId,
    processConditions,
    elementType,
    pointGroupList,
    currentGroupItem,
    isGroupForm,
    isHasAcquistion,
    checkedNodeItem,
    isAllGroup,
    speGroups,
    nodeTree,

    // 方法
    setCropperIcon,
    setCustomPic,
    setModuleId,
    setGroupId,
    setRuleId,
    delPCondition,
    addPCondition,
    initPConditions,
    setElementType,
    setPointGroupList,
    setCurrentGroupItem,
    setIsGroupForm,
    setIsHasAcquition,
    setCheckedNodeItem,
    setIsAllGroup,
    setSpeGroups,
    setNodeTree
  };
});
