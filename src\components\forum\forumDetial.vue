<!-- 论坛详细信息 -->
<template>
  <div class="forum-detial-contianer">
    <el-drawer
      :model-value="props.isOpenforumDetialDrawer"
      @update:modelValue="emit('update:isOpenforumDetialDrawer', $event)"
      direction="rtl"
      :modal="false"
      style="margin-top: 85px"
      class="forum-drawer-contianer"
      :before-close="handleClose"
    >
      <template #title>
        <span class="forum-title" @click="handleClose"
          ><el-icon><ArrowLeft /></el-icon>返回</span
        >
      </template>
      <div class="forum-drawer-content">
        <forum-content :forum-item="props.detialItem" :is-show-title-type="isShowTitleType"></forum-content>
        <forum-comments-list :comments-list="props.commentsList"></forum-comments-list>
      </div>
      <div class="forum-drawer-footer">
        <el-button type="primary" :icon="EditPen" size="small" class="forum-btn">我要评论</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { PropType } from 'vue';
import { ArrowLeft, EditPen } from '@element-plus/icons-vue';
import forumContent from './forumContent.vue';
import forumCommentsList from './forumCommentsList.vue';

// Define basic interfaces for props (replace with actual structure if available)
interface DetialItem {
  avatar: string;
  createBy: string;
  createTime: string;
  titleType: number;
  titleInfo: string;
  contents: string;
  picUrls: string[];
}

interface CommentItem {
  id: string | number;
  // Define other properties of comment items
  [key: string]: any; // Placeholder for additional properties
}

// Props definition
const props = defineProps({
  isOpenforumDetialDrawer: {
    type: Boolean,
    default: false
  },
  detialItem: {
    type: Object as PropType<DetialItem>,
    default: () => ({})
  },
  commentsList: {
    type: Array as PropType<CommentItem[]>,
    default: () => []
  }
});

// Emits definition
const emit = defineEmits(['closeForum', 'update:isOpenforumDetialDrawer']);

// State
const isShowTitleType = ref(false);

// Methods
const handleClose = () => {
  emit('closeForum');
};
</script>

<style lang="scss" scoped>
.forum-detial-contianer {
  .forum-drawer-contianer {
    :deep(.el-drawer__header) {
      height: 44px;
      background: #fff;
      border-radius: 0px 0px 0px 0px;
      border-bottom: 1px solid #dbe7ee;
      margin: 0;

      span.forum-title {
        width: auto; // Adjust width as needed
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #161d26;
        line-height: 20px;
        padding: 0;
        margin-bottom: 0; // Reset margin
        cursor: pointer; // Add cursor pointer
        display: inline-flex; // Align icon and text
        align-items: center;
        .el-icon {
          margin-right: 5px; // Space between icon and text
        }
      }

      .el-drawer__close-btn {
        font-size: 16px;
        margin-bottom: 0; // Reset margin
      }
    }
    :deep(.el-drawer__body) {
      // Style body if needed, e.g., padding
      padding-bottom: 60px; // Ensure content doesn't overlap footer
    }

    .forum-drawer-content {
      // Add styles if needed
    }

    .forum-drawer-footer {
      position: absolute;
      height: 48px;
      background: #ffffff;
      border-radius: 0px 0px 0px 0px;
      bottom: 0;
      right: 0;
      left: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center; // Center button vertically
      padding: 0 20px; // Add padding
      box-sizing: border-box; // Include padding in height
      border-top: 1px solid #dbe7ee;

      .forum-btn {
        // Remove absolute positioning
        margin: 0; // Reset margin
      }
    }
  }
}
</style>
