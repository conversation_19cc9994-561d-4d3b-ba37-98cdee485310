import wkidMap from '@/data/wkidMap.json';
import { string } from 'vue-types';

/**
 * 路径匹配器
 * @param {string} pattern
 * @param {string} path
 * @returns {Boolean}
 */
export function isPathMatch(pattern: string, path: string) {
  const regexPattern = pattern
    .replace(/\//g, '\\/')
    .replace(/\*\*/g, '__DOUBLE_STAR__')
    .replace(/\*/g, '[^\\/]*')
    .replace(/__DOUBLE_STAR__/g, '.*');
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(path);
}

/**
 * 判断url是否是http或https
 * @returns {Boolean}
 * @param url
 */
export const isHttp = (url: string): boolean => {
  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1;
};

/**
 * 判断path是否为外链
 * @param {string} path
 * @returns {Boolean}
 */
export const isExternal = (path: string) => {
  return /^(https?:|mailto:|tel:)/.test(path);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validUsername = (str: string) => {
  const valid_map = ['admin', 'editor'];
  return valid_map.indexOf(str.trim()) >= 0;
};

/**
 * @param {string} url
 * @returns {Boolean}
 */
export const validURL = (url: string) => {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return reg.test(url);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validLowerCase = (str: string) => {
  const reg = /^[a-z]+$/;
  return reg.test(str);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validUpperCase = (str: string) => {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validAlphabets = (str: string) => {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
};

/**
 * @param {string} email
 * @returns {Boolean}
 */
export const validEmail = (email: string) => {
  const reg =
    /^(([^<>()\]\\.,;:\s@"]+(\.[^<>()\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const isString = (str: any) => {
  return typeof str === 'string' || str instanceof String;
};

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export const isArray = (arg: string | string[]) => {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]';
  }
  return Array.isArray(arg);
};

/**
 * 判断是否为手机号
 * @param {string} str
 * @returns {Boolean}
 */
export const isMobile = (str: string) => {
  return /^1[3|4|5|6|7|8|9]\d{9}$/.test(str);
};

/**
 * 判断是否为手机号
 * @param {string} str
 * @returns {Boolean}
 */
export function isPhone(str: string) {
  const reg = /^1\d{10}$/;
  return reg.test(str);
}

//处理geometry转wkt
export function geometryToWkt(geometry: any) {
  const { type, coordinates } = geometry;

  let coordinateWkt = '';

  const typeMap = {
    point: getPointWkt,
    linestring: getLineWkt,
    multiLinestring: getMultiLineWkt,
    polygon: getPolygonWkt,
    multiPolygon: getMultiPolygonWkt
  };

  // 根据类型调取方法
  if (typeMap[type]) {
    // 点特殊处理
    if (type === 'point') {
      coordinateWkt = `(${typeMap[type](coordinates)})`;
    } else {
      coordinateWkt = typeMap[type](coordinates);
    }
  }

  // 点
  function getPointWkt(coordinates: any) {
    if (isArr(coordinates)) {
      return `${coordinates[1]} ${coordinates[0]}`;
    }
  }

  // 线
  function getLineWkt(coordinates: any) {
    let str = '';
    if (isArr(coordinates)) {
      coordinates.forEach((coordinate: any) => {
        const symbol = str ? ',' : '';
        str += `${symbol}${getPointWkt(coordinate)}`;
      });
    }
    return `(${str})`;
  }

  // 多线
  function getMultiLineWkt(coordinates: any) {
    let str = '';
    if (isArr(coordinates)) {
      coordinates.forEach((coordinate: any) => {
        const symbol = str ? ',' : '';
        str += `${symbol}${getLineWkt(coordinate)}`;
      });
    }
    return `(${str})`;
  }

  // 面(处理方式同多线)
  function getPolygonWkt(coordinates: any) {
    return getMultiLineWkt(coordinates);
  }

  // 多面
  function getMultiPolygonWkt(coordinates: any) {
    let str = '';
    if (isArr(coordinates)) {
      coordinates.forEach((coordinate: any) => {
        const symbol = str ? ',' : '';
        str += `${symbol}${getPolygonWkt(coordinate)}`;
      });
    }
    return `(${str})`;
  }

  // 判断是否为数组
  function isArr(arr: any) {
    return Array.isArray(arr);
  }

  return `${type.toUpperCase()}${coordinateWkt}`;
}

export function hexToRgba(hex: string, aph?: string) {
  // 确保hex字符串以#开头，并且是8个字符长
  if (hex[0] !== '#') {
    throw new Error('Invalid hex color code');
  }

  // 分别获取RGB和Alpha的值
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  let a = '1';
  if (aph) {
    a = aph;
  }
  if (hex.length == 9) {
    a = (parseInt(hex.slice(7, 9), 16) / 255).toString(); // 转换为0-1之间的浮点数
  }
  // 返回RGBA字符串
  return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')';
}

export function rgbaToHex(rgba: string) {
  // 将RGBA字符串拆分
  const arr = rgba.split('(')[1].split(')')[0].split(',');
  // 转换RGBA值为16进制
  let r = parseInt(arr[0], 10).toString(16);
  let g = parseInt(arr[1], 10).toString(16);
  let b = parseInt(arr[2], 10).toString(16);
  let a = Math.round(parseFloat(arr[3]) * 255).toString(16);

  // 补齐16进制值，确保每个值都是两位数字
  r = r.length === 2 ? r : '0' + r;
  g = g.length === 2 ? g : '0' + g;
  b = b.length === 2 ? b : '0' + b;
  a = a.length === 2 ? a : '0' + a;

  // 返回16进制颜色值
  return '#' + r + g + b + a;
}

// 根据数据整理属性组展示信息
export function initAttr(list: any[], nodeForFieldGroupModelList: any[]) {
  const endList = [];
  list.forEach((v) => {
    let fieldModelList = []; //对应属性组的属性
    for (let index = 0; index < nodeForFieldGroupModelList.length; index++) {
      if (v.linkId == nodeForFieldGroupModelList[index].linkId) {
        fieldModelList = nodeForFieldGroupModelList[index].fieldModelList;
        break;
      }
    }
    if (v.attribution.list) {
      //代表是子要素根据图形生成的 list
      const attrList = v.attribution.list; //list特殊处理
      attrList.forEach((o) => {
        const fieldList = []; //所有组装的字段
        fieldModelList.forEach((k) => {
          if (k.fieldType == 'Integer[]' && k.valueMethod == 'idCardScan') {
            //需要判断特殊情况 身份证识别
            // 老的 只显示存在数据的
            // let key_list = Object.keys(v.attribution).sort()
            // let newKeyList = []
            // for (let index = 0; index < key_list.length; index++) { //删除除开身份证识别外的属性
            //   if (key_list[index].includes('_')) {
            //     newKeyList.push(key_list[index])
            //   }
            // }
            if (k.attribution.expendList) {
              //新版身份证识别
              k.attribution.expendList.forEach((q: any, qdx: number) => {
                const obj: any = {
                  value: o[`${k.fieldName}_${k.attribution.list[qdx]}`],
                  field: {
                    fieldCn: q.cnName,
                    fieldName: `${k.fieldName}_${k.attribution.list[qdx]}`,
                    valueMethod: k.valueMethod,
                    attribution: {
                      maxLength: q.strLength,
                      options: q.options
                    }
                  },
                  label: q.cnName,
                  field_index: q.label
                };
                if (q.valueMethod == 'input' || q.valueMethod == 'date-range') {
                  obj.type = 1;
                } else if (q.valueMethod == 'date') {
                  obj.type = 3;
                } else if (q.valueMethod == 'idCardBitmap') {
                  //身份证正面背面
                  // 这里需要特殊判断一下url如果是数组，就代表是之前在网页编辑身份证导致把身份证正反面传成数组了，修正错误数据
                  // if (isArray(v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`])) {
                  //   obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`][0].url }];
                  // } else {
                  //   obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`] }];
                  // }
                  if (isArray(v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`])) {
                    obj.value = [{ url: o[`${k.fieldName}_${k.attribution.list[qdx]}`][0].url }];
                  } else {
                    obj.value = [{ url: o[`${k.fieldName}_${k.attribution.list[qdx]}`] }];
                  }
                  obj.type = 2;
                } else if (q.valueMethod == 'radio') {
                  //单选
                  obj.type = 10;
                }
                fieldList.push(obj);
              });
            } else {
              //老版身份证识别
              // 新的显示所有
              const newKeyList = [];
              k.attribution.list.forEach((o: any) => {
                newKeyList.push(`${k.fieldName}_${o}`);
              });
              newKeyList.forEach((q: string) => {
                const index = q.indexOf('_');
                const idx = q.substring(index + 1, q.length);
                const obj = {
                  type: 1, // 1代表是正常的 2代表是图片 3代表日期 4代表时间
                  value: o[q],
                  field: k,
                  field_index: idx,
                  label: '',
                  fieldList: []
                };
                switch (parseInt(idx)) {
                  case 0:
                    obj.label = '姓名';
                    break;
                  case 1:
                    obj.label = '性别';
                    break;
                  case 2:
                    obj.label = '民族';
                    break;
                  case 3:
                    obj.label = '出生日期';
                    obj.type = 3;
                    break;
                  case 4:
                    obj.label = '住址';
                    break;
                  case 5:
                    obj.label = '身份证';
                    break;
                  case 6:
                    obj.label = '签发机关';
                    break;
                  case 7:
                    obj.label = '有效期限';
                    break;
                  case 8:
                    obj.label = '身份证正面';
                    obj.value = [{ url: v.attribution[q] }];
                    obj.fieldList = [];
                    obj.type = 2;
                    break;
                  case 9:
                    obj.label = '身份证反面';
                    obj.value = [{ url: v.attribution[q] }];
                    obj.fieldList = [];
                    obj.type = 2;
                    break;
                  default:
                    break;
                }
                fieldList.push(obj);
              });
            }
          } else if (k.fieldType == 'Integer[]' && k.valueMethod == 'xtBankCard') {
            if (k.attribution?.expendList.length > 0) {
              //新版身份证识别
              k.attribution.expendList.forEach((q: any, qdx: number) => {
                const obj: any = {
                  value: o[`${k.fieldName}_${k.attribution.list[qdx]}`],
                  field: {
                    fieldCn: q.cnName,
                    fieldName: `${k.fieldName}_${k.attribution.list[qdx]}`,
                    valueMethod: k.valueMethod,
                    attribution: {
                      maxLength: q.strLength,
                      options: q.options
                    }
                  },
                  label: q.cnName,
                  field_index: q.label,
                  type: 1 // 银行卡识别字段全部都是input 类型固定为1
                };
                if (q.valueMethod == 'BankCardBitmap') {
                  if (isArray(v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`])) {
                    obj.value = [{ url: o[`${k.fieldName}_${k.attribution.list[qdx]}`][0].url }];
                  } else {
                    obj.value = [{ url: o[`${k.fieldName}_${k.attribution.list[qdx]}`] }];
                  }
                  obj.type = 2;
                }
                fieldList.push(obj);
              });
            }
          } else {
            // if (o[k.fieldName]!=undefined) {
            const obj: any = {
              type: 1, //1代表是正常的 2代表是图片 3代表日期 4代表时间
              label: k.fieldCn,
              field: k,
              showDeg: false,
              value: '',
              fieldList: []
            };
            if (k.valueMethod == 'upload' || k.valueMethod == 'xtqm' || k.valueMethod == 'xtzw' || k.valueMethod == 'xtsjjt') {
              obj.type = 2;
              if (k.attribution.showDeg) {
                obj.showDeg = true;
              } else {
                obj.showDeg = false;
              }
            } else if (k.valueMethod == 'date') {
              //代表是日期
              obj.type = 3;
            } else if (k.valueMethod == 'time') {
              //代表是时间
              obj.type = 4;
            } else if (k.valueMethod == 'xtzwsb' || k.valueMethod == 'xtdwsb') {
              //5 植物 或动物
              obj.type = 5;
            } else if (k.valueMethod == 'xtvideo') {
              //视频
              obj.type = 6;
            } else if (k.valueMethod == 'xttable') {
              //表格
              obj.type = 7;
            } else if (k.valueMethod == 'xtfj') {
              //附件
              obj.type = 8;
            } else if (k.valueMethod == 'cascader') {
              //级联选择
              obj.type = 9;
            } else if (k.valueMethod == 'area') {
              //行政区划
              obj.type = 11;
            } else if (k.valueMethod == 'xtaudio') {
              // 音频
              obj.type = 12;
            }

            if (k.valueMethod == 'xtqm' || k.valueMethod == 'xtzw' || k.valueMethod == 'xtsjjt') {
              //指纹、签名特殊处理 变成数组
              if (o[k.fieldName]) {
                obj.value = [o[k.fieldName]][{ url: o[k.fieldName] }];
              } else {
                obj.value = '';
              }
              obj.fieldList = [];
            } else if (k.valueMethod == 'upload') {
              obj.value = '';
              let sourceList = o[k.fieldName];
              if (typeof o[k.fileName] == 'string') {
                sourceList = JSON.parse(o[k.fieldName]);
              }
              if (sourceList && typeof sourceList[0] == 'string') {
                //兼容老版图片
                const valueList = [];
                sourceList.forEach((u: any) => {
                  valueList.push({ url: u });
                });
                obj.value = valueList;
              } else {
                obj.value = [];
                if (sourceList) {
                  sourceList.forEach((f: any, fdx: number) => {
                    const flist = Object.keys(f);
                    flist.forEach((w: string) => {
                      if (!k.attribution.attrList) {
                        const valueList = [];
                        sourceList.forEach((u: any) => {
                          valueList.push({ url: u });
                        });
                        obj.value = valueList;
                      } else {
                        for (let j = 0; j < k.attribution.attrList.length; j++) {
                          if (k.attribution.attrList[j].label == w) {
                            const fobj: any = {};
                            fobj.isSy = k.attribution.attrList[j].isSy;
                            fobj.value = sourceList[fdx][w];
                            fobj.text = k.attribution.attrList[j].text;
                            sourceList[fdx][w] = fobj;
                          }
                        }
                      }
                    });
                  });
                  obj.value = o[k.fieldName];
                }
              }
              obj.fieldList = [];
            } else if (k.valueMethod == 'xtvideo') {
              //视频
              if (o[k.fieldName]) {
                if (isArray(o[k.fieldName])) {
                  obj.value = o[k.fieldName];
                } else {
                  obj.value = JSON.parse(o[k.fieldName]);
                }
              }
            } else if (k.valueMethod == 'xttable') {
              //表格
              const tableChildren = k.attribution.children; //表格的子字段
              let tempValue = [];
              if (typeof o[k.fieldName] == 'string') {
                tempValue = JSON.parse(o[k.fieldName]);
              } else {
                tempValue = o[k.fieldName];
              }
              obj.value = {
                tableTh: tableChildren,
                tableTr: tempValue
              };
            } else if (k.valueMethod == 'xtfj') {
              //附件
              const fjList = [];
              if (typeof o[k.fieldName] === 'string') {
                o[k.fieldName] = JSON.parse(o[k.fieldName]);
              }
              o[k.fieldName].forEach((fj: any) => {
                const fjTypeIndex = fj[`${k.fieldName}_1`].lastIndexOf('.');
                const fjobj = {
                  title: Object.values(fj)[1],
                  url: fj[`${k.fieldName}_0`],
                  type: fj[`${k.fieldName}_1`].slice(fjTypeIndex + 1)
                };
                fjList.push(fjobj);
              });
              obj.value = fjList;
            } else if (k.valueMethod == 'select') {
              //下拉列表要处理
              // if (k.attribution.optionIsJson) { //这种需要特殊处理
              //   for (let i = 0; i < k.attribution.options.length; i++) {
              //     if (o[k.fieldName] == k.attribution.options[i].value) {
              //       obj.value = k.attribution.options[i].label
              //       break;
              //     }
              //   }
              // } else {
              //   obj.value = o[k.fieldName]
              // }

              obj.value = o[k.fieldName] || [];
            } else if (k.valueMethod == 'checkbox') {
              obj.value = o[k.fieldName] || [];
            } else if (k.valueMethod == 'xtaudio') {
              obj.value = JSON.parse(o[k.fileName]);
            } else {
              obj.value = o[k.fieldName];
            }
            fieldList.push(obj);
            // }
          }
        });
        const item = {
          typeName: v.groupName,
          linkId: v.linkId,
          fieldList: fieldList,
          id: v.id,
          timeStamp: v.timeStamp,
          createTime: v.createTime,
          parcelLinkId: v.parcelLinkId,
          linkTimeStamp: v.linkTimeStamp,
          linkType: v.linkType,
          linkAttribution: v.linkAttribution,
          isCQGDFGroup: false,
          isChooseAZF: false,
          seq: v.seq
        };
        // 超期过渡费单独判断一下 多采 云岩区征地专用
        if (v.groupName == '超期过渡费') {
          item.isCQGDFGroup = true;
        } else if (v.groupName.includes('执行情况')) {
          //执行相关 是拆迁户选择安置房
          item.isChooseAZF = true;
        }
        endList.push(item);
      });
    } else {
      const fieldList = []; //所有组装的字段
      fieldModelList.forEach((k) => {
        if (k.fieldType == 'Integer[]' && k.valueMethod == 'idCardScan') {
          //需要判断特殊情况 身份证识别
          // 老的 只显示存在数据的
          // let key_list = Object.keys(v.attribution).sort()
          // let newKeyList = []
          // for (let index = 0; index < key_list.length; index++) { //删除除开身份证识别外的属性
          //   if (key_list[index].includes('_')) {
          //     newKeyList.push(key_list[index])
          //   }
          // }
          if (k.attribution.expendList) {
            //新版身份证识别
            k.attribution.expendList.forEach((q: any, qdx: number) => {
              const obj = {
                value: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`],
                field: {
                  fieldCn: q.cnName,
                  fieldName: `${k.fieldName}_${k.attribution.list[qdx]}`,
                  valueMethod: k.valueMethod,
                  attribution: {
                    maxLength: q.strLength,
                    options: q.options
                  }
                },
                label: q.cnName,
                field_index: q.label,
                type: undefined
              };
              if (q.valueMethod == 'input' || q.valueMethod == 'date-range') {
                obj.type = 1;
              } else if (q.valueMethod == 'date') {
                obj.type = 3;
              } else if (q.valueMethod == 'idCardBitmap') {
                //身份证正面背面
                // 这里需要特殊判断一下url如果是数组，就代表是之前在网页编辑身份证导致把身份证正反面传成数组了，修正错误数据
                if (isArray(v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`])) {
                  obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`][0].url }];
                } else {
                  obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`] }];
                }
                obj.type = 2;
              } else if (q.valueMethod == 'radio') {
                //单选
                obj.type = 10;
              }
              fieldList.push(obj);
            });
          } else {
            //老版身份证识别
            // 新的显示所有
            const newKeyList = [];
            k.attribution.list.forEach((o: any) => {
              newKeyList.push(`${k.fieldName}_${o}`);
            });
            newKeyList.forEach((q: string) => {
              const index = q.indexOf('_');
              const idx = q.substring(index + 1, q.length);
              const obj: any = {
                type: 1, // 1代表是正常的 2代表是图片 3代表日期 4代表时间
                value: v.attribution[q],
                field: k,
                field_index: idx,
                label: '',
                fieldList: []
              };
              switch (parseInt(idx)) {
                case 0:
                  obj.label = '姓名';
                  break;
                case 1:
                  obj.label = '性别';
                  break;
                case 2:
                  obj.label = '民族';
                  break;
                case 3:
                  obj.label = '出生日期';
                  obj.type = 3;
                  break;
                case 4:
                  obj.label = '住址';
                  break;
                case 5:
                  obj.label = '身份证';
                  break;
                case 6:
                  obj.label = '签发机关';
                  break;
                case 7:
                  obj.label = '有效期限';
                  break;
                case 8:
                  obj.label = '身份证正面';
                  obj.value = [{ url: v.attribution[q] }];
                  obj.fieldList = [];
                  obj.type = 2;
                  break;
                case 9:
                  obj.label = '身份证反面';
                  obj.value = [{ url: v.attribution[q] }];
                  obj.fieldList = [];
                  obj.type = 2;
                  break;
                default:
                  break;
              }

              fieldList.push(obj);
            });
          }
        } else if (k.fieldType == 'Integer[]' && k.valueMethod == 'xtBankCard') {
          if (k.attribution.expendList) {
            //新版身份证识别
            k.attribution.expendList.forEach((q: any, qdx: number) => {
              const obj = {
                value: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`],
                field: {
                  fieldCn: q.cnName,
                  fieldName: `${k.fieldName}_${k.attribution.list[qdx]}`,
                  valueMethod: k.valueMethod,
                  attribution: {
                    maxLength: q.strLength,
                    options: q.options
                  }
                },
                label: q.cnName,
                field_index: q.label,
                // 银行卡识别都是输入框 input 类型这里写死为1
                type: 1
              };

              if (q.valueMethod == 'BankCardBitmap') {
                //身份证正面背面
                // 这里需要特殊判断一下url如果是数组，就代表是之前在网页编辑身份证导致把身份证正反面传成数组了，修正错误数据
                if (isArray(v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`])) {
                  obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`][0].url }];
                } else {
                  obj.value = [{ url: v.attribution[`${k.fieldName}_${k.attribution.list[qdx]}`] }];
                }
                obj.type = 2;
              }

              fieldList.push(obj);
            });
          }
        } else {
          // if (v.attribution[k.fieldName]!=undefined) {
          const obj: any = {
            type: 1, //1代表是正常的 2代表是图片 3代表日期 4代表时间
            label: k.fieldCn,
            field: k,
            showDeg: false,
            value: '',
            fieldList: []
          };
          if (k.valueMethod == 'upload' || k.valueMethod == 'xtqm' || k.valueMethod == 'xtzw' || k.valueMethod == 'xtsjjt') {
            obj.type = 2;
            if (k.attribution.showDeg) {
              obj.showDeg = true;
            } else {
              obj.showDeg = false;
            }
          } else if (k.valueMethod == 'date') {
            //代表是日期
            obj.type = 3;
          } else if (k.valueMethod == 'time') {
            //代表是时间
            obj.type = 4;
          } else if (k.valueMethod == 'xtzwsb' || k.valueMethod == 'xtdwsb') {
            //5 植物 或动物
            obj.type = 5;
          } else if (k.valueMethod == 'xtvideo') {
            //视频
            obj.type = 6;
          } else if (k.valueMethod == 'xttable') {
            //表格
            obj.type = 7;
          } else if (k.valueMethod == 'xtfj') {
            //附件
            obj.type = 8;
          } else if (k.valueMethod == 'cascader') {
            //级联选择
            obj.type = 9;
          } else if (k.valueMethod == 'area') {
            //行政区划
            obj.type = 11;
          } else if (k.valueMethod == 'xtaudio') {
            // 音频
            obj.type = 12;
          }
          if (k.valueMethod == 'xtqm' || k.valueMethod == 'xtzw' || k.valueMethod == 'xtsjjt') {
            //指纹、签名特殊处理 变成数组
            if (v.attribution[k.fieldName]) {
              obj.value = [{ url: v.attribution[k.fieldName] }];
            } else {
              obj.value = '';
            }
            obj.fieldList = [];
          } else if (k.valueMethod == 'upload') {
            obj.value = '';
            let sourceList = v.attribution[k.fieldName];
            if (v.attribution[k.fieldName] && typeof v.attribution[k.fieldName] == 'string') {
              //表示是需要解析的
              try {
                sourceList = JSON.parse(v.attribution[k.fieldName]);
              } catch (error) {
                sourceList = [];
              }
            }
            if (sourceList && typeof sourceList[0] == 'string') {
              //兼容老版图片
              const valueList = [];
              sourceList.forEach((u: any) => {
                valueList.push({ url: u });
              });
              obj.value = valueList;
            } else {
              if (sourceList) {
                sourceList.forEach((f: any, fdx: number) => {
                  const flist = Object.keys(f);
                  if (!k.attribution.attrList) {
                    const valueList = [];
                    sourceList.forEach((u: any) => {
                      valueList.push({ url: u });
                    });
                    obj.value = valueList;
                  } else {
                    flist.forEach((w: string) => {
                      for (let j = 0; j < k.attribution.attrList.length; j++) {
                        if (k.attribution.attrList[j].label == w) {
                          const fobj: any = {};
                          fobj.isSy = k.attribution.attrList[j].isSy;
                          fobj.value = sourceList[fdx][w];
                          fobj.text = k.attribution.attrList[j].text;
                          sourceList[fdx][w] = fobj;
                        }
                      }
                    });
                  }
                });
                obj.value = sourceList;
              }
            }
            obj.fieldList = [];
          } else if (k.valueMethod == 'xtvideo') {
            //视频
            if (v.attribution[k.fieldName]) {
              // 兼容视频存的可能是字符串 可能是数组
              if (isArray(v.attribution[k.fieldName])) {
                obj.value = v.attribution[k.fieldName];
              } else {
                obj.value = JSON.parse(v.attribution[k.fieldName]);
              }
            }
          } else if (k.valueMethod == 'xttable') {
            //表格
            const tableChildren = k.attribution.children; //表格的子字段
            let tempValue = [];
            if (typeof v.attribution[k.fieldName] == 'string' && v.attribution[k.fieldName]) {
              tempValue = JSON.parse(v.attribution[k.fieldName]);
            } else {
              tempValue = v.attribution[k.fieldName];
            }
            obj.value = {
              tableTh: tableChildren,
              tableTr: tempValue
            };
          } else if (k.valueMethod == 'xtfj') {
            //附件
            const fjList = [];
            if (v.attribution[k.fieldName]) {
              //可能会出现是json字符串的情况
              if (typeof v.attribution[k.fieldName] === 'string') {
                v.attribution[k.fieldName] = JSON.parse(v.attribution[k.fieldName]);
              }
              if (isArray(v.attribution[k.fieldName])) {
                v.attribution[k.fieldName].forEach((fj: any) => {
                  const fjTypeIndex = fj[`${k.fieldName}_0`].lastIndexOf('.');
                  const fjobj = {
                    title: fj[`${k.fieldName}_1`],
                    url: fj[`${k.fieldName}_0`],
                    type: fj[`${k.fieldName}_0`].slice(fjTypeIndex + 1)
                  };
                  fjList.push(fjobj);
                });
              }
              obj.value = fjList;
            }
          } else if (k.valueMethod == 'select') {
            //下拉列表要处理
            // if (k.attribution.optionIsJson) { //这种需要特殊处理
            //   for (let i = 0; i < k.attribution.options.length; i++) {
            //     if (v.attribution[k.fieldName] == k.attribution.options[i].value) {
            //       obj.value = k.attribution.options[i].label
            //       break;
            //     }
            //   }
            // } else {
            //   obj.value = v.attribution[k.fieldName]
            // }
            obj.value = v.attribution[k.fieldName];
          } else if (k.valueMethod == 'checkbox') {
            obj.value = v.attribution[k.fieldName] || [];
          } else if (k.valueMethod == 'xtaudio') {
            obj.value = v.attribution[k.fieldName];
          } else {
            obj.value = v.attribution[k.fieldName];
          }
          fieldList.push(obj);
          // }
        }
      });
      // 判断是否有linkAttribution 如果有需要把指定的点或者线加入到fieldList里面
      if (v.linkAttribution) {
        const obj = {
          type: 1, //1代表是正常的 2代表是图片 3代表日期 4代表时间
          value: v.linkAttribution[v.ruleAttribution.type]
        };
        if (v.ruleAttribution.type == 'graphicalLine' || v.ruleAttribution.type == 'commonLine') {
          obj.label = '选择的线';
        } else {
          obj.label = '选择的点';
        }
        fieldList.push(obj);
      }
      const item = {
        typeName: v.groupName,
        linkId: v.linkId,
        fieldList: fieldList,
        id: v.id,
        timeStamp: v.timeStamp,
        createTime: v.createTime,
        parcelLinkId: v.parcelLinkId,
        linkTimeStamp: v.linkTimeStamp,
        linkType: v.linkType,
        linkAttribution: v.linkAttribution,
        isCQGDFGroup: false,
        isChooseAZF: false,
        seq: v.seq
      };
      // 超期过渡费单独判断一下 多采 云岩区征地专用
      if (v.groupName == '超期过渡费') {
        item.isCQGDFGroup = true;
      } else if (v.groupName.includes('执行情况')) {
        //执行相关 是拆迁户选择安置房
        item.isChooseAZF = true;
      }
      endList.push(item);
    }
  });
  return endList;
}

// 传入一个集合和一个属性、来判断该属性是否出现过多次
export function hasDuplicates(array: any[], property: string) {
  // 创建一个 Set 对象来存储属性值
  const values = new Set();

  // 遍历数组中的每个对象
  for (const obj of array) {
    // 检查对象的属性是否已经存在于 Set 对象中
    if (values.has(obj[property])) {
      // 如果存在，返回 true 表示有重复的属性值
      return true;
    } else {
      // 如果不存在，将属性值添加到 Set 对象中
      values.add(obj[property]);
    }
  }
  // 如果遍历完整个数组都没有找到重复的属性值，返回 false
  return false;
}

export function copyWord(expression: string) {
  const variable = expression;
  const tag = document.createElement('textarea'); // create textarea标签，注意：创建input标签则不会换行
  document.body.appendChild(tag); // 添加到body中
  tag.value = variable; // 给textarea设置value属性为需要copy的内容
  tag.select(); // 选中
  document.execCommand('copy', false); // copy已经选中的内容
  Message({ message: '复制成功', type: 'success' });
  tag.remove();
}

// base64转file 用于签名
export function base64ToFile(base64Data: string, fileName: string) {
  const arr = base64Data.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  const blob: any = new Blob([u8arr], { type: mime });
  blob.lastModifiedDate = new Date();
  blob.name = fileName;
  return new File([blob], fileName, { type: blob.type, lastModified: Date.now() });
}

// 验证是否是日期输入
export function isValidDateFormat(str: string) {
  str = str + '';
  const regex = /^\d{4}\.\d{2}\.\d{2}$/;
  return (
    regex.test(str) &&
    parseInt(str.substring(0, 4)) > 0 && // 年份大于0
    parseInt(str.substring(5, 7)) <= 12 && // 月份在1到12之间
    parseInt(str.substring(8, 10)) <= 31
  );
}

//验证是否输入的年
export function isValidYearFormat(yearStr: string) {
  const regex = /^\d{4}$/;
  return regex.test(yearStr);
}

//验证是否输入的月
export function isValidMonthFormat(monthStr: string) {
  monthStr = monthStr + '';
  const regex = /^\d{4}\.\d{2}$/;
  return regex.test(monthStr) && parseInt(monthStr.substring(5, 7)) <= 12;
}

// 通过传入的srsCode 得到wkid wkidMap
export function getWkidForSrsCode(code: string) {
  const flg = wkidMap.some((obj) => obj.srsCode === code);
  let wkid;
  if (flg) {
    //存在 就去找到对应的srsCode
    for (let i = 0; i < wkidMap.length; i++) {
      if (wkidMap[i].srsCode == code) {
        wkid = wkidMap[i].wkid;
        break;
      }
    }
    return wkid;
  } else {
    //不存在特殊处理
    if (code.includes('_Degree')) {
      //处理类似于GCS_China_Geodetic_Coordinate_System_2000_3_Degree_GK_Zone_35
      const index1 = code.indexOf('_Degree') - 1;
      const index2 = code.indexOf('Zone_') + 5;
      let Degree1, Degree2;
      Degree1 = parseInt(code.substring(index1, index1 + 1));
      Degree2 = parseInt(code.substring(index2, code.length));
      if (Degree2 < 25) {
        //六度带
        wkid = 4478 + Degree2;
      } else if (Degree2 >= 25) {
        //三度带
        wkid = 4513 + Degree2 - 25;
      }
      return wkid;
    } else {
      return '';
    }
  }
}

// 根据某个字段进行去重 数组
export function deWeight(items: any[], idKey: string) {
  return items.filter((item, index, array) => {
    return array.findIndex((t) => t[idKey] === item[idKey]) === index;
  });
}

// 把excel的日期转换成真正的日期 如42876 就是指离1900年1月0日多少天
export function excelDateToJsDate(excelDate: number) {
  // Excel的基准日期是1899年12月30日，但考虑到Excel将1900年视为闰年的错误，我们通常使用1899年12月31日作为基准
  const epoch = new Date(Date.UTC(1899, 11, 31)); // 注意月份是从0开始的，所以11代表12月

  // Excel的日期是从基准日期开始的天数偏移
  const date = new Date(epoch.getTime() + (excelDate - 1) * 86400000); // 减去1是因为Excel的日期从1开始，且一天有86400000毫秒

  // 获取时间戳（毫秒）
  const timestamp = date.getTime();

  return timestamp;
}
