<!-- 根据传过来的人员以及数据分配每个人对应的数据 任务分配 -->
<template>
  <el-dialog
    title="分配任务数据"
    v-model="dialogVisible"
    width="90%"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    :before-close="handleClose"
  >
    <div class="handle-div">
      <el-radio-group v-model="radioType" @change="changeAllotType">
        <el-radio v-for="item in typeList" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
      </el-radio-group>
    </div>
    <div class="allot-main" :style="{ height: bodyHeight }">
      <div class="left">
        <div class="user-box">
          <div class="user-item" :class="{ 'active-user': activeSpe == 1 }" @click="speDraw(1)">总数据（{{ safeLength(localSelectedList) }}）条</div>
          <div class="user-item" :class="{ 'active-user': activeSpe == 2 }" @click="speDraw(2)">已分配（ {{ allotCount }}）条</div>
          <div class="user-item" :class="{ 'active-user': activeSpe == 3 }" @click="speDraw(3)">
            未分配（{{ safeLength(localSelectedList) - allotCount }}）条
          </div>
          <div class="user-item" :class="{ 'active-user': item.checked }" v-for="(item, index) in userList" :key="index" @click="changeUser(item)">
            <span>{{ item.custName }}</span>
            <span v-if="item.isContains && safeLength(item.isContains) > 0">({{ safeLength(item.isContains) }}条)</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div id="viewDiv" ref="gisMap" class="map"></div>
        <div class="tuli-box">
          <div class="tuli-row tuli-title">图例：</div>
          <div class="tuli-row">
            <div class="tuli-right-gree"></div>
            <div class="label">未分配数据</div>
          </div>
          <div class="tuli-row">
            <div class="tuli-right-red"></div>
            <div class="label">已分配数据</div>
          </div>
        </div>
        <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
          <svg-icon icon-class="map" />
        </div>
        <div class="map-change" v-show="showChangeMap">
          <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
            <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">天地图地图矢量</div>
          </div>
          <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
            <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">天地图影像</div>
          </div>
        </div>
      </div>
      <div class="left end" v-if="(activeUser.isContains && safeLength(activeUser.isContains) > 0) || activeSpe !== null">
        <div class="end-title">灰色字体数据表示该条数据已被上传过，如果要移除请谨慎操作！！！</div>
        <div class="end-removeall" v-if="activeUser.isContains && safeLength(activeUser.isContains) > 0 && !activeSpe">
          <el-link type="primary" @click="removeAllOnce">一键移除</el-link>
        </div>
        <div class="user-box end-box" v-if="!activeSpe && activeUser.isContains && safeLength(activeUser.isContains) > 0">
          <div
            class="user-item"
            :class="{ 'gary-div': item.attributes && item.attributes.lockNum != 0 }"
            v-for="(item, index) in activeUser.isContains"
            :key="item._refId || index"
            @click="showOneDraw(item)"
          >
            <div>{{ item.attributes ? item.attributes.parcelName : '' }}</div>
            <div @click.stop="removeOne(item, index)">
              <el-link type="primary">移除</el-link>
            </div>
          </div>
        </div>
        <!-- 这个是统计的speList -->
        <div class="user-box end-box" v-if="activeSpe !== null">
          <div v-if="safeLength(speList) === 0" class="user-item">
            <div>暂无数据</div>
          </div>
          <div
            v-else
            class="user-item"
            :class="{ 'gary-div': item.attributes && item.attributes.lockNum != 0 }"
            v-for="(item, index) in speList"
            :key="item._refId || index"
            @click="showOneDraw(item)"
          >
            <div>{{ item.attributes ? item.attributes.parcelName : '' }}</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, reactive, toRefs, defineProps, defineEmits, toRaw } from 'vue';
import { loadModules } from 'esri-loader';
import { ElMessage, ElMessageBox } from 'element-plus';

// 常量定义
const tiandituBaseUrl = 'http://{subDomain}.tianditu.gov.cn'; // 天地图服务地址
const token = 'f250cc0a2b1fe177a3a5b8ce821a6c8d'; // 天地图管网申请token
const config = {
  css: import.meta.env.VUE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VUE_APP_ARCGIS_CONFIGJS
};

// 类型定义
interface User {
  userId: number;
  custName: string;
  checked?: boolean;
  isContains?: any[];
  uuid?: string; // 添加uuid可选属性
}

interface ParcelData {
  id?: string | number;
  parcelId: string | number;
  parcelName: string;
  userId?: number;
  custName?: string;
  geomArcgis: any;
  lockNum?: number;
  [key: string]: any;
}

interface TypeItem {
  label: string;
  value: number;
}

interface SketchEvent {
  state: string;
  graphics: any[];
  [key: string]: any;
}

// Props 和 Emits
const props = defineProps<{
  allotDialog: boolean;
  users: User[];
  selectedList?: ParcelData[];
}>();

const emit = defineEmits<{
  closeAllot: [];
  submitAllotData: [data: any[]];
}>();

// 响应式数据
const gisMap = ref<HTMLElement | null>(null);
const dialogVisible = ref(false);
const bodyHeight = ref(window.innerHeight - 260 + 'px');
const showChangeMap = ref(false);
const checkedMap = ref('image'); // 显示的底图 默认影像
const activeUser = reactive<User>({} as User);
const oldHighlight = ref<any>(null);
const radioType = ref(1); // 1手动分配 2自动分配
const activeSpe = ref<number | null>(null); // 高亮特殊的
const speList = ref<any[]>([]); // 统计的列表
const userList = ref<User[]>([]); // 用户列表的本地副本

// 非响应式地图相关变量，避免与Vue响应式系统冲突
let mapInstance = null as any;
let viewInstance = null as any;

// 地图对象引用
// 这些变量使用普通JavaScript对象存储而不是响应式对象
const mapObjects = {
  graphicsLayer: null as any,
  sketchLayer: null as any,
  tiledLayer: null as any,
  tiledLayerAnno: null as any,
  normalLayer: null as any,
  normalAnno: null as any,
  labelLayer: null as any
};

// 类型列表
const typeList: TypeItem[] = [
  { label: '手动分配', value: 1 },
  { label: '自动分配', value: 2 }
];

// 创建本地selectedList状态，避免直接依赖props
const localSelectedList = ref<any[]>([]);

// 计算属性
const allotCount = computed(() => {
  let num = 0;
  userList.value.forEach((v) => {
    if (v.isContains && v.isContains.length > 0) {
      num += v.isContains.length;
    }
  });
  return num;
});

// 监听props
watch(
  () => props.allotDialog,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      radioType.value = 1;

      // 初始化本地selectedList
      localSelectedList.value = props.selectedList || [];

      // 创建用户列表的本地副本，同时保留已分配的数据
      userList.value = props.users.map((user) => {
        // 查找该用户已分配的数据
        const userAssignedData = localSelectedList.value
          .filter((item) => item.userId === user.userId)
          .map((item) => ({
            attributes: {
              userId: item.userId,
              parcelId: item.parcelId,
              lockNum: item.lockNum || 0,
              id: item.id,
              parcelName: item.parcelName,
              custName: item.custName
            },
            geometry: item.geomArcgis ? JSON.parse(item.geomArcgis) : null,
            _refId: Math.random().toString(36).substring(2, 15)
          }));

        return {
          ...user,
          checked: false,
          isContains: userAssignedData
        };
      });

      if (userList.value.length !== 0) {
        // 设置第一个用户为当前活动用户
        const firstUser = userList.value[0];
        Object.assign(activeUser, {
          userId: firstUser.userId,
          custName: firstUser.custName,
          checked: true,
          isContains: firstUser.isContains || []
        });
        firstUser.checked = true;
      }

      // 在下一个异步周期初始化地图
      setTimeout(() => {
        init(true, true);
      }, 0);
    }
  },
  { deep: true }
);

// 方法定义
const handleClose = () => {
  // 清理地图资源
  if (viewInstance) {
    try {
      viewInstance.container = null;
    } catch (e) {
      console.error('清理地图视图失败', e);
    }
  }

  // 重置地图对象引用
  mapInstance = null;
  viewInstance = null;
  // 使用类型安全的方式清空对象
  mapObjects.graphicsLayer = null;
  mapObjects.sketchLayer = null;
  mapObjects.tiledLayer = null;
  mapObjects.tiledLayerAnno = null;
  mapObjects.normalLayer = null;
  mapObjects.normalAnno = null;
  mapObjects.labelLayer = null;

  emit('closeAllot');
};

const init = (flg: boolean, isLook: boolean) => {
  if (!gisMap.value) {
    console.error('地图容器未找到');
    return;
  }

  // 初始化地图的函数
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine',
      'esri/widgets/Sketch',
      'esri/layers/FeatureLayer'
    ],
    config
  )
    .then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine, Sketch]) => {
      try {
        // 球面墨卡托投影矢量底图
        mapObjects.tiledLayer = new WebTileLayer({
          urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
          subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          spatialReference: new SpatialReference({
            wkid: 102100
          }),
          getTileUrl: function (level: number, row: number, col: number) {
            if (level <= 18) {
              return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=img_w/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
            } else {
              return '';
            }
          }
        });

        // 矢量标注(球面墨卡托投影)
        mapObjects.tiledLayerAnno = new WebTileLayer({
          urlTemplate: tiandituBaseUrl + '/DataServer?T=cia_w?T=vec_c/wmts&x={col}&y={row}&l={level}&tk=' + token,
          subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          spatialReference: new SpatialReference({
            wkid: 102100
          }),
          getTileUrl: function (level: number, row: number, col: number) {
            if (level <= 18) {
              return (
                'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=cia_w?T=vec_c/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token
              );
            } else {
              return '';
            }
          }
        });

        // 经纬度投影 矢量底图
        mapObjects.normalLayer = new WebTileLayer({
          urlTemplate: tiandituBaseUrl + '/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
          subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          visible: false,
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });

        // 矢量注记
        mapObjects.normalAnno = new WebTileLayer({
          urlTemplate: tiandituBaseUrl + '/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
          subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          visible: false,
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });

        mapInstance = new Map({
          basemap: {
            baseLayers: [mapObjects.tiledLayer, mapObjects.tiledLayerAnno, mapObjects.normalLayer, mapObjects.normalAnno]
          },
          logo: false,
          spatialReference: {
            wkid: 102100
          }
        });

        // 使用HTMLElement接口而非Vue引用
        const mapContainer = gisMap.value instanceof HTMLElement ? gisMap.value : document.getElementById('viewDiv');

        if (!mapContainer) {
          console.error('无法找到地图容器元素');
          return;
        }

        // 创建视图
        viewInstance = new MapView({
          container: mapContainer,
          map: mapInstance,
          center: [116.39126, 39.90763],
          zoom: 18,
          ui: {
            components: []
          },
          spatialReference: new SpatialReference({
            wkid: 102100
          })
        });

        // 设置最大缩放等级
        viewInstance.constraints = {
          minZoom: 2,
          maxZoom: 24
        };

        // 添加控件
        const compassWidget = new Compass({ view: viewInstance });
        viewInstance.ui.add(compassWidget, 'bottom-right');

        const zoom = new Zoom({
          view: viewInstance
        });
        viewInstance.ui.add(zoom, 'bottom-right');
        viewInstance.ui.remove('attribution');

        const scaleBar = new ScaleBar({
          view: viewInstance,
          unit: 'metric',
          style: 'line'
        });
        viewInstance.ui.add(scaleBar, {
          position: 'bottom-right'
        });

        // 添加对象初始化标记
        let initCompleted = false;

        // 添加宗地图形layer到map
        mapObjects.graphicsLayer = new GraphicsLayer({
          id: '123'
        });
        mapInstance.add(mapObjects.graphicsLayer);

        mapObjects.sketchLayer = new GraphicsLayer({
          id: 'sketch-1'
        });
        mapInstance.add(mapObjects.sketchLayer);

        mapObjects.labelLayer = new GraphicsLayer({
          id: 'label-1',
          minScale: 5000
        });
        mapInstance.add(mapObjects.labelLayer);

        // 标记初始化完成
        initCompleted = true;

        // 为视图添加事件监听
        viewInstance
          .when(() => {
            try {
              // 只有在初始化完成后才调用绘制函数
              if (initCompleted) {
                drawAll(flg, isLook);
              } else {
                console.error('地图图层未初始化完成，无法绘制数据');
              }

              // 创建草图工具
              const sketch = new Sketch({
                layer: mapObjects.sketchLayer,
                view: viewInstance,
                creationMode: 'update',
                availableCreateTools: ['polygon']
              });

              sketch.visibleElements = {
                selectionTools: {
                  'lasso-selection': false,
                  'rectangle-selection': false
                },
                settingsMenu: false
              };

              viewInstance.ui.add(sketch, 'top-left');

              sketch.on('update', function (event: SketchEvent) {
                sketch.visible = true;
                const rectangle = event.graphics[0];
                if (event.state === 'start') {
                  comparisonGeometry(rectangle, true);
                }
              });
            } catch (error) {
              console.error('设置视图事件监听时出错:', error);
            }
          })
          .catch((error: any) => {
            console.error('视图初始化失败:', error);
          });
      } catch (error) {
        console.error('地图初始化时出错:', error);
      }
    })
    .catch((error) => {
      console.error('加载ArcGIS模块失败:', error);
    });
};

// 绘制所有数据
const drawAll = (flg?: boolean, isLook?: boolean) => {
  if (!mapObjects.graphicsLayer) {
    console.error('graphicsLayer未初始化，无法绘制数据');
    return;
  }

  if (!localSelectedList.value || localSelectedList.value.length === 0) {
    console.warn('没有可绘制的数据');
    return;
  }

  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config)
    .then(([jsonUtils, Graphic, TextSymbol]) => {
      try {
        mapObjects.graphicsLayer.removeAll();
        let num = 0;

        localSelectedList.value.forEach((v, idx) => {
          if (!v.geomArcgis) {
            console.warn('数据缺少几何信息:', v.parcelId);
            return;
          }

          // 判断是否显示此图形：
          // 1. 如果没有分配给任何用户(userId为空)，则显示
          // 2. 如果分配给当前活动用户，则显示
          const shouldDisplay = !v.userId || Number(v.userId) === activeUser.userId;

          if (shouldDisplay) {
            let outLineColor = [255, 0, 0]; // 已分配的显示红色
            if (!v.userId) {
              outLineColor = [84, 255, 159]; // 未分配的显示绿色
            }

            try {
              const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
              const rawPolygon = toRaw(polygon);

              if (!rawPolygon) {
                console.warn('无法解析几何数据:', v.parcelId);
                return;
              }

              let graphic = null;
              const attributes = {
                userId: v.userId,
                parcelId: v.parcelId,
                lockNum: v.lockNum || 0,
                id: v.id,
                parcelName: v.parcelName,
                index: idx,
                custName: v.custName
              };

              // 根据几何类型创建不同的图形
              if (rawPolygon.type === 'point') {
                const textSymbol = {
                  type: 'simple-marker',
                  color: outLineColor,
                  width: 2
                };

                graphic = new Graphic({
                  geometry: rawPolygon,
                  symbol: textSymbol,
                  attributes: attributes
                });
              } else if (rawPolygon.type === 'polyline') {
                const lineSymbol = {
                  type: 'simple-line',
                  color: outLineColor,
                  width: 1
                };

                graphic = new Graphic({
                  geometry: rawPolygon,
                  symbol: lineSymbol,
                  attributes: attributes
                });
              } else if (rawPolygon.type === 'polygon') {
                const simpleFillSymbol = {
                  type: 'simple-fill',
                  color: [240, 230, 140, 0.2],
                  outline: {
                    color: outLineColor,
                    width: 1,
                    style: 'solid'
                  }
                };

                graphic = new Graphic({
                  geometry: rawPolygon,
                  symbol: simpleFillSymbol,
                  attributes: attributes
                });
              }

              if (graphic) {
                mapObjects.graphicsLayer.add(graphic);
                if (flg) {
                  initUserContains(graphic);
                }

                // 处理第一个图形的定位
                if (num === 0 && isLook) {
                  centerMapOnGeometry(rawPolygon);
                }
                num++;
              }
            } catch (err) {
              console.error('绘制图形出错:', err, v);
            }
          }
        });

        if (flg) {
          changeUser(userList.value[0]);
          drawLabel();
        }
      } catch (error) {
        console.error('drawAll执行出错:', error);
      }
    })
    .catch((error) => {
      console.error('drawAll加载模块出错:', error);
    });
};

// 添加一个辅助函数用于地图定位
const centerMapOnGeometry = (geometry: any) => {
  try {
    const rawGeometry = toRaw(geometry);
    if (!rawGeometry || !viewInstance) return;

    if (rawGeometry.type === 'point' && rawGeometry.longitude && rawGeometry.latitude) {
      viewInstance.center = [rawGeometry.longitude, rawGeometry.latitude];
      viewInstance.zoom = 18;
    } else if (rawGeometry.type === 'polygon' && rawGeometry.centroid) {
      const rawCentroid = toRaw(rawGeometry.centroid);
      viewInstance.center = [rawCentroid.longitude, rawCentroid.latitude];
      viewInstance.zoom = 18;
    } else if (rawGeometry.extent && rawGeometry.extent.center) {
      const rawCenter = toRaw(rawGeometry.extent.center);
      viewInstance.center = [rawCenter.longitude, rawCenter.latitude];
      viewInstance.zoom = 18;
    }
  } catch (err) {
    console.warn('设置地图中心点失败:', err);
  }
};

// 初始化用户包含的图形
const initUserContains = (polygonGraphic: any) => {
  const rawGraphic = toRaw(polygonGraphic);

  if (rawGraphic?.attributes?.userId) {
    const userIndex = userList.value.findIndex((user) => user.userId === rawGraphic.attributes.userId);

    if (userIndex !== -1) {
      if (!userList.value[userIndex].isContains) {
        userList.value[userIndex].isContains = [];
      }

      const existingIndex = userList.value[userIndex].isContains.findIndex((item) => item.attributes?.parcelId === rawGraphic.attributes.parcelId);

      if (existingIndex === -1) {
        const graphicData = {
          attributes: { ...rawGraphic.attributes },
          geometry: rawGraphic.geometry,
          _refId: Math.random().toString(36).substring(2, 15)
        };
        userList.value[userIndex].isContains.push(graphicData);
      }
    }
  }
};

// 比对图形
const comparisonGeometry = (rectangle: any, flg: boolean) => {
  // rectangle 框选对边形 flg true新增 false 删除 取消高亮并从isContains删除数据
  loadModules(['esri/geometry/geometryEngine'], config).then(([geometryEngine]) => {
    const attributes = {
      custName: activeUser.custName,
      userId: activeUser.userId,
      uuid: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
    };
    rectangle = toRaw(rectangle);
    rectangle.attributes = attributes;

    if (!activeUser.isContains) {
      // 给当前用户赋值包含的图形数据
      activeUser.isContains = [];
    }

    // 遍历GraphicsLayer中的所有图形
    if (mapObjects.graphicsLayer) {
      // 使用普通数组复制而非直接操作响应式数组
      const graphicsItems = [...toRaw(mapObjects.graphicsLayer).graphics.items];

      // 创建一个临时数组存储需要更新的selectedItem
      interface UpdateItem {
        index: number;
        userId: number;
        custName: string;
      }
      const itemsToUpdate: UpdateItem[] = [];

      graphicsItems.forEach((graphic: any) => {
        if (geometryEngine.contains(rectangle.geometry, graphic.geometry)) {
          if (!graphic.attributes) {
            console.warn('图形缺少属性数据');
            return;
          }

          // 检查是否已经包含该图形
          const existingItem = activeUser.isContains?.find((item) => item.attributes && item.attributes.parcelId === graphic.attributes.parcelId);

          if (!existingItem) {
            // 没找到才追加进去
            // 还需要判断当前宗地绑定的userId是不是在当前选择的成员中，如果在，那么就不应该添加进去，而且需要把该宗地变成红色标识不能框选
            if (!userList.value.some((item) => Number(item.userId) === Number(graphic.attributes.userId))) {
              // 不在 加入到用户的isContains列表里，并且高亮 这个是为了解决可能是之前绑定的，现在任务人员都移除了
              graphic.attributes.userId = activeUser.userId;
              graphic.attributes.custName = activeUser.custName;

              if (activeUser.isContains) {
                // 创建一个普通对象副本，避免直接存储ArcGIS对象
                const graphicData = {
                  attributes: { ...graphic.attributes },
                  geometry: graphic.geometry,
                  // 添加一个引用ID，用于后续操作
                  _refId: Math.random().toString(36).substring(2, 15)
                };
                activeUser.isContains.push(graphicData);
              }

              // 不高亮 改成标注
              drawLabel();

              // 收集需要更新的项，避免直接操作响应式数组
              const index = graphic.attributes.index;
              if (index !== undefined && index < localSelectedList.value.length) {
                itemsToUpdate.push({
                  index,
                  userId: activeUser.userId,
                  custName: activeUser.custName
                });
              }
            }
          }
        }
      });

      // 批量更新selectedList，避免频繁触发响应式更新
      if (itemsToUpdate.length > 0) {
        // 创建一个本地副本进行修改
        const tempSelectedList = [...localSelectedList.value];

        itemsToUpdate.forEach((item: UpdateItem) => {
          if (tempSelectedList[item.index]) {
            tempSelectedList[item.index].userId = item.userId;
            tempSelectedList[item.index].custName = item.custName;
          }
        });

        // 一次性更新响应式数组
        localSelectedList.value = tempSelectedList;
      }
    }

    drawAll();
    // 标注框选多边形的归属
    // drawLabel()
    if (mapObjects.sketchLayer) mapObjects.sketchLayer.removeAll();
  });
};

// 统计用的绘制 1总数据 2已分配 3未分配
const speDraw = (type: number) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/geometry/Point', 'esri/geometry/SpatialReference'], config)
    .then(([jsonUtils, Graphic, Point, SpatialReference]) => {
      try {
        // 清空数组
        speList.value = [];

        if (mapObjects.graphicsLayer) {
          mapObjects.graphicsLayer.removeAll();
        } else {
          console.warn('graphicsLayer未初始化');
          return;
        }

        // 重置用户选中状态
        userList.value.forEach((v) => {
          v.checked = false;
        });

        // 创建临时数组存储解析结果
        const list: ParcelData[] = [];

        if (type === 1) {
          // 总数据
          activeSpe.value = 1;
          list.push(...(localSelectedList.value || []));
        } else if (type === 2) {
          // 已分配
          activeSpe.value = 2;
          (localSelectedList.value || []).forEach((v: ParcelData) => {
            if (v.userId) {
              list.push(v);
            }
          });
        } else if (type === 3) {
          // 未分配
          activeSpe.value = 3;
          (localSelectedList.value || []).forEach((v: ParcelData) => {
            if (!v.userId) {
              list.push(v);
            }
          });
        }

        // 遍历并处理每个图形
        list.forEach((v, idx) => {
          try {
            if (!v.geomArcgis) {
              console.warn('数据缺少几何信息:', v.parcelId);
              return;
            }

            let geomJSON;
            try {
              geomJSON = JSON.parse(v.geomArcgis);
            } catch (parseError) {
              console.error('解析几何JSON失败:', parseError, v.parcelId);
              return;
            }

            const polygon = jsonUtils.fromJSON(geomJSON);
            // 确保polygon是原始对象
            const rawPolygon = toRaw(polygon);
            if (!rawPolygon) {
              console.warn('无法创建几何对象:', v.parcelId);
              return;
            }

            let outLineColor = [255, 0, 0];
            if (!v.userId) {
              outLineColor = [84, 255, 159];
            }

            let graphic = null;
            const attributes = {
              userId: v.userId,
              parcelId: v.parcelId,
              lockNum: v.lockNum || 0,
              id: v.id,
              parcelName: v.parcelName,
              index: idx,
              custName: v.custName
            };

            if (rawPolygon.type === 'point') {
              // 点符号
              const textSymbol = {
                type: 'simple-marker',
                color: outLineColor,
                width: 2
              };

              graphic = new Graphic({
                geometry: rawPolygon,
                symbol: textSymbol,
                attributes: attributes
              });

              // 如果是第一个点，设置地图中心
              if (idx === 0) {
                try {
                  if (rawPolygon.longitude && rawPolygon.latitude) {
                    viewInstance.center = [rawPolygon.longitude, rawPolygon.latitude];
                    viewInstance.zoom = 18;
                  }
                } catch (err) {
                  console.warn('无法设置点的中心:', err);
                }
              }
            } else if (rawPolygon.type === 'polyline') {
              // 线符号
              const lineSymbol = {
                type: 'simple-line',
                color: outLineColor,
                width: 1
              };

              graphic = new Graphic({
                geometry: rawPolygon,
                symbol: lineSymbol,
                attributes: attributes
              });

              // 如果是第一条线，设置地图中心
              if (idx === 0) {
                try {
                  if (rawPolygon.extent && rawPolygon.extent.center) {
                    viewInstance.center = [toRaw(rawPolygon.extent.center).longitude, toRaw(rawPolygon.extent.center).latitude];
                    viewInstance.zoom = 18;
                  }
                } catch (err) {
                  console.warn('无法设置线的中心:', err);
                }
              }
            } else if (rawPolygon.type === 'polygon') {
              // 面符号
              const simpleFillSymbol = {
                type: 'simple-fill',
                color: [255, 230, 140, 0.2],
                outline: {
                  color: outLineColor,
                  width: 1,
                  style: 'solid'
                }
              };

              graphic = new Graphic({
                geometry: rawPolygon,
                symbol: simpleFillSymbol,
                attributes: attributes
              });

              // 如果是第一个多边形，设置地图中心
              if (idx === 0) {
                try {
                  if (rawPolygon.centroid) {
                    viewInstance.center = [toRaw(rawPolygon.centroid).longitude, toRaw(rawPolygon.centroid).latitude];
                    viewInstance.zoom = 18;
                  } else if (rawPolygon.extent && rawPolygon.extent.center) {
                    viewInstance.center = [toRaw(rawPolygon.extent.center).longitude, toRaw(rawPolygon.extent.center).latitude];
                    viewInstance.zoom = 18;
                  }
                } catch (err) {
                  console.warn('无法设置多边形中心:', err);
                }
              }
            }

            if (graphic && mapObjects.graphicsLayer) {
              mapObjects.graphicsLayer.add(graphic);
              // 创建一个普通对象副本，避免直接存储ArcGIS对象
              const graphicData = {
                attributes: { ...graphic.attributes },
                geometry: graphic.geometry,
                // 添加一个引用ID，用于后续操作
                _refId: Math.random().toString(36).substring(2, 15)
              };
              speList.value.push(graphicData);
            }
          } catch (error) {
            console.error('处理单个图形时出错:', error, v);
          }
        });

        // 清空标注图层
        if (mapObjects.labelLayer) {
          mapObjects.labelLayer.removeAll();
        } else {
          console.warn('labelLayer未初始化');
          return;
        }

        // 为已分配的数据添加标注
        if (type !== 3 && mapObjects.graphicsLayer && mapObjects.graphicsLayer.graphics && mapObjects.graphicsLayer.graphics.items) {
          mapObjects.graphicsLayer.graphics.items.forEach((v: any) => {
            try {
              // 安全检查
              if (!v || !v.attributes || !v.attributes.custName || !v.geometry || !v.geometry.centroid) {
                return;
              }

              // 文本符号
              const textSymbol = {
                type: 'text',
                text: v.attributes.custName,
                color: [255, 0, 0],
                haloSize: '1px',
                xoffset: 0,
                yoffset: 0,
                font: {
                  // family: 'Arial-BoldItalic',
                  size: 12
                  // style: 'italic',
                  // weight: 'bold'
                }
              };

              // 创建标注点
              const label_graphic = new Graphic(
                new Point(v.geometry.centroid.x, v.geometry.centroid.y, new SpatialReference({ wkid: 102100 })),
                textSymbol
              );

              if (mapObjects.labelLayer) {
                mapObjects.labelLayer.add(label_graphic);
              }
            } catch (labelError) {
              console.warn('创建标注时出错:', labelError);
            }
          });
        }
      } catch (error) {
        console.error('speDraw执行出错:', error);
      }
    })
    .catch((error) => {
      console.error('speDraw加载模块失败:', error);
    });
};

// 切换用户
const changeUser = (user: User) => {
  if (activeSpe.value !== null) {
    activeSpe.value = null;
    speList.value = [];
  }

  // 重置所有用户的选中状态
  userList.value.forEach((v) => {
    v.checked = false;
  });

  // 设置当前选中用户
  user.checked = true;

  // 更新activeUser，确保包含完整的isContains数据
  Object.assign(activeUser, {
    userId: user.userId,
    custName: user.custName,
    checked: true,
    isContains: user.isContains || []
  });

  // 重新绘制地图
  drawAll(false, false);
  drawLabel();
};

/**
 * 移除所有
 * 1. 移除activeUser.isContains
 * 2. 移除localSelectedList中的对应项
 * 3. 移除graphicsLayer中的图形属性
 * 4. 移除标注
 * 5. 移除视图
 */
const removeAllOnce = () => {
  ElMessageBox.confirm('确定要移除所有数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      if (!activeUser.isContains || activeUser.isContains.length === 0) {
        return;
      }

      // 遍历当前用户的所有数据进行移除
      activeUser.isContains.forEach((item) => {
        if (item && item.attributes) {
          // 更新 selectedList 中对应的数据
          const selectedItem = localSelectedList.value.find((selItem) => selItem.parcelId === item.attributes.parcelId);
          if (selectedItem) {
            selectedItem.userId = undefined;
            selectedItem.custName = undefined;
          }

          // 更新 graphicsLayer 中的图形属性
          if (mapObjects.graphicsLayer) {
            for (let i = 0; i < mapObjects.graphicsLayer.graphics.items.length; i++) {
              const graphic = mapObjects.graphicsLayer.graphics.items[i];
              if (graphic.attributes && graphic.attributes.parcelId === item.attributes.parcelId) {
                graphic.attributes.userId = null;
                graphic.attributes.custName = null;
                break;
              }
            }
          }
        }
      });

      // 清空当前用户的 isContains
      activeUser.isContains = [];

      // 更新 userList 中当前用户的数据
      const currentUserIndex = userList.value.findIndex((user) => user.userId === activeUser.userId);
      if (currentUserIndex !== -1) {
        userList.value[currentUserIndex].isContains = [];
      }

      // 重新绘制
      drawLabel();
      drawAll();

      ElMessage({
        type: 'success',
        message: '移除成功!'
      });
    })
    .catch(() => {});
};

/**
 * 自动分配未分配的数据
 * 1. 获取未分配的数据
 * 2. 将未分配的数据按照用户数量进行分配
 * 3. 将分配后的数据更新到activeUser.isContains中
 * 4. 更新localSelectedList中的对应项
 * 5. 更新视图
 */
const autoAllot = () => {
  const noAllotData = [];

  if (mapObjects.graphicsLayer) {
    // 安全地遍历 graphics.items 数组
    for (let i = 0; i < mapObjects.graphicsLayer.graphics.items.length; i++) {
      const v = mapObjects.graphicsLayer.graphics.items[i];
      if (v && v.attributes && !v.attributes.userId) {
        try {
          // 创建一个深拷贝，避免直接操作 ArcGIS 对象
          const safeCopy = {
            // 只保留需要的属性
            attributes: JSON.parse(JSON.stringify(v.attributes)),
            geometry: v.geometry,
            // 添加一个唯一标识，用于后续操作
            _refId: Math.random().toString(36).substring(2, 15)
          };
          noAllotData.push(safeCopy);
        } catch (error) {
          console.warn('复制图形对象时出错:', error);
        }
      }
    }
  }

  if (noAllotData.length > 0 && userList.value.length > 0) {
    const endNoAllot = splitArrayIntoChunks(noAllotData, userList.value.length);

    userList.value.forEach((v, vdx) => {
      if (endNoAllot[vdx]) {
        // 更新每个分配项的用户信息
        endNoAllot[vdx].forEach((k) => {
          if (k.attributes) {
            k.attributes.userId = v.userId;
            k.attributes.custName = v.custName;
          }
        });

        // 确保 isContains 已初始化
        if (!v.isContains) {
          v.isContains = [];
        }

        // 将深拷贝的对象添加到用户的 isContains 中
        v.isContains.push(...endNoAllot[vdx]);

        // 同时更新 localSelectedList 中的对应项
        endNoAllot[vdx].forEach((item) => {
          if (item.attributes && item.attributes.parcelId) {
            const selectedItem = localSelectedList.value.find((selItem) => selItem.parcelId === item.attributes.parcelId);
            if (selectedItem) {
              selectedItem.userId = v.userId;
              selectedItem.custName = v.custName;
            }
          }
        });
      }
    });
  }

  // 更新视图
  drawLabel();
  drawAll();
};

// 移除某条数据
const removeOne = (item: any, index: number) => {
  if (!item || !item.attributes) {
    console.warn('图形缺少属性数据，无法移除');
    return;
  }

  ElMessageBox.confirm('确定要移除该条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 还需要把selectedList对应的也移除userId
      const selectedItem = localSelectedList.value.find((selItem) => selItem.parcelId === item.attributes.parcelId);
      if (selectedItem) {
        selectedItem.userId = undefined;
        selectedItem.custName = undefined;
      }

      // 在graphicsLayer中找到对应的图形并更新其属性
      if (mapObjects.graphicsLayer) {
        for (let i = 0; i < mapObjects.graphicsLayer.graphics.items.length; i++) {
          const graphic = mapObjects.graphicsLayer.graphics.items[i];
          if (graphic.attributes && graphic.attributes.parcelId === item.attributes.parcelId) {
            // 更新图形属性
            graphic.attributes.userId = null;
            graphic.attributes.custName = null;
            break;
          }
        }
      }

      // 从activeUser.isContains中移除该项
      if (activeUser.isContains) {
        activeUser.isContains.splice(index, 1);
      }

      drawLabel();
      drawAll();

      ElMessage({
        type: 'success',
        message: '移除成功!'
      });
    })
    .catch(() => {});
};

// 高亮显示当前选择的图形
const showOneDraw = (item: any) => {
  // 需要去graphicsLayer里面找，因为可能重新绘制的图形对象不一定是同一个
  if (!mapObjects.graphicsLayer) {
    console.warn('graphicsLayer不存在，无法显示单个图形');
    return;
  }

  if (!item || !item.attributes || !item.attributes.parcelId) {
    console.warn('图形缺少属性数据，无法显示');
    return;
  }

  try {
    // 使用常规循环查找匹配的图形
    for (let i = 0; i < mapObjects.graphicsLayer.graphics.items.length; i++) {
      const graphicItem = mapObjects.graphicsLayer.graphics.items[i];

      if (graphicItem.attributes && graphicItem.attributes.parcelId === item.attributes.parcelId) {
        // 先取消上一个高亮宗地
        if (oldHighlight.value && typeof oldHighlight.value.remove === 'function') {
          oldHighlight.value.remove();
        }

        // 设置地图中心点 - 改进的中心点获取逻辑
        if (graphicItem.geometry) {
          let center;
          const rawGeometry = toRaw(graphicItem.geometry);

          // 尝试不同的方式获取中心点
          if (rawGeometry.centroid) {
            // 多边形通常有centroid
            center = [rawGeometry.centroid.longitude, rawGeometry.centroid.latitude];
          } else if (rawGeometry.extent && rawGeometry.extent.center) {
            // 线通常可以通过extent.center获取中心
            center = [rawGeometry.extent.center.longitude, rawGeometry.extent.center.latitude];
          } else if (rawGeometry.paths && rawGeometry.paths.length > 0) {
            // 如果是线，取中间点
            const path = rawGeometry.paths[0];
            const midIndex = Math.floor(path.length / 2);
            if (path[midIndex]) {
              center = path[midIndex];
            }
          }

          if (center) {
            viewInstance.center = center;
            // 确保缩放级别足够
            if (viewInstance.zoom < 18) {
              viewInstance.zoom = 18;
            }
          }
        }

        // 高亮当前图形
        viewInstance
          .whenLayerView(graphicItem.layer)
          .then((layerView: any) => {
            oldHighlight.value = layerView.highlight(graphicItem);
          })
          .catch((error: any) => {
            console.error('高亮图形失败:', error);
          });

        // 找到匹配项后退出循环
        break;
      }
    }
  } catch (error) {
    console.error('显示单个图形时出错:', error);
  }
};

// 改变分配方式
const changeAllotType = (val: string | number | boolean | undefined) => {
  if (val == 2) {
    // 自动分配
    ElMessageBox.confirm('自动分配只会分配未分配的数据，确定还要自动分配吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        autoAllot();
      })
      .catch(() => {
        radioType.value = 1;
      });
  }
};

// 切换底图
const changeMap = (type: number) => {
  if (type == 1) {
    // 地图
    checkedMap.value = 'normal';
    if (mapObjects.tiledLayer) mapObjects.tiledLayer.visible = false;
    if (mapObjects.tiledLayerAnno) mapObjects.tiledLayerAnno.visible = false;
    if (mapObjects.normalLayer) mapObjects.normalLayer.visible = true;
    if (mapObjects.normalAnno) mapObjects.normalAnno.visible = true;
  } else if (type == 2) {
    // 影像图
    checkedMap.value = 'image';
    if (mapObjects.tiledLayer) mapObjects.tiledLayer.visible = true;
    if (mapObjects.tiledLayerAnno) mapObjects.tiledLayerAnno.visible = true;
    if (mapObjects.normalLayer) mapObjects.normalLayer.visible = false;
    if (mapObjects.normalAnno) mapObjects.normalAnno.visible = false;
  }
};

// 将数组分成n个块
const splitArrayIntoChunks = (arr: any[], n: number) => {
  if (n <= 0) {
    throw new Error('n must be greater than 0');
  }
  const chunkSize = Math.floor(arr.length / n); // 每份的基础大小
  const remainder = arr.length % n; // 余数
  const result = [];

  let index = 0;
  for (let i = 0; i < n; i++) {
    // 计算当前子数组应该包含的元素数量
    // 如果还有余数且当前子数组是余数可以分配到的（即索引小于余数），则多分配一个元素
    const chunkSizeForThisChunk = chunkSize + (i < remainder ? 1 : 0);

    // 截取arr中的一部分元素作为当前子数组
    result.push(arr.slice(index, index + chunkSizeForThisChunk));

    // 更新索引，以便下一次截取
    index += chunkSizeForThisChunk;
  }

  return result;
};

// 提交
const submit = () => {
  try {
    const updatedSelectedList = localSelectedList.value.map((item) => {
      const assignedUser = userList.value.find((user) => user.isContains?.some((graphic) => graphic.attributes?.parcelId === item.parcelId));

      if (assignedUser) {
        return {
          ...item,
          userId: assignedUser.userId,
          custName: assignedUser.custName
        };
      }

      return {
        ...item,
        userId: undefined,
        custName: undefined
      };
    });

    emit('submitAllotData', updatedSelectedList);
    emit('closeAllot');
  } catch (error) {
    console.error('提交数据时出错:', error);
    ElMessage.error('提交数据失败');
  }
};

// 初始化当前用户对应的标注
const drawLabel = () => {
  // 如果没有标注图层或者没有数据，直接返回
  if (!mapObjects.labelLayer) {
    console.warn('标注图层未初始化');
    return;
  }

  if (!activeUser.isContains || activeUser.isContains.length === 0) {
    // 清空标注图层
    mapObjects.labelLayer.removeAll();
    return;
  }

  // 使用一个新的方法来安全地获取图形中心点
  const findGraphicsInLayer = () => {
    // 如果没有graphicsLayer，直接返回空数组
    if (!mapObjects.graphicsLayer || !activeUser.isContains || !Array.isArray(activeUser.isContains)) {
      return [];
    }

    const result = [];

    // 安全地获取所有 parcelId
    const parcelIds = activeUser.isContains
      .filter((item) => item && item.attributes && item.attributes.parcelId)
      .map((item) => {
        try {
          return item.attributes.parcelId;
        } catch (e) {
          console.warn('获取 parcelId 失败:', e);
          return null;
        }
      })
      .filter(Boolean);

    // 遍历图层中的图形
    // 使用 toRaw 获取原始图层对象，避免Vue3响应式系统干扰
    const rawGraphicsLayer = toRaw(mapObjects.graphicsLayer);
    if (rawGraphicsLayer.graphics && rawGraphicsLayer.graphics.items) {
      for (let i = 0; i < rawGraphicsLayer.graphics.items.length; i++) {
        try {
          // 使用 toRaw 获取原始图形对象
          const graphic = toRaw(rawGraphicsLayer.graphics.items[i]);
          if (!graphic || !graphic.attributes) continue;

          const parcelId = graphic.attributes.parcelId;
          if (!parcelId || !parcelIds.includes(parcelId)) continue;

          // 查找匹配项
          let custName = null;
          for (const item of activeUser.isContains) {
            try {
              if (item && item.attributes && item.attributes.parcelId === parcelId) {
                custName = item.attributes.custName;
                break;
              }
            } catch (e) {
              console.warn('比较 parcelId 失败:', e);
            }
          }

          if (custName && graphic.geometry && graphic.geometry.centroid) {
            result.push({
              custName,
              geometry: graphic.geometry
            });
          }
        } catch (e) {
          console.warn('处理图形时出错:', e);
        }
      }
    }

    return result;
  };

  // 加载必要的模块
  loadModules(['esri/Graphic', 'esri/geometry/SpatialReference', 'esri/geometry/Point'], config)
    .then(([Graphic, SpatialReference, Point]) => {
      try {
        // 清空标注图层
        mapObjects.labelLayer.removeAll();

        // 获取匹配的图形
        const matchedGraphics = findGraphicsInLayer();

        // 为每个图形创建标注
        matchedGraphics.forEach((item) => {
          try {
            // 创建文本符号
            const textSymbol = {
              type: 'text',
              text: item.custName,
              color: [255, 0, 0],
              haloSize: '1px',
              xoffset: 0,
              yoffset: 0,
              font: {
                // family: 'Arial-BoldItalic',
                size: 12
                // style: 'italic',
                // weight: 'bold'
              }
            };

            // 安全地获取中心点坐标
            let x, y;
            if (item.geometry.centroid) {
              // 使用toRaw确保获取原始对象
              const rawCentroid = toRaw(item.geometry.centroid);

              if (typeof rawCentroid.x === 'function') {
                x = rawCentroid.x();
                y = rawCentroid.y();
              } else if (rawCentroid.x !== undefined) {
                x = rawCentroid.x;
                y = rawCentroid.y;
              } else if (rawCentroid.longitude !== undefined) {
                x = rawCentroid.longitude;
                y = rawCentroid.latitude;
              }
            }

            // 如果成功获取了坐标，创建标注
            if (x !== undefined && y !== undefined) {
              const point = new Point(x, y, new SpatialReference({ wkid: 102100 }));
              const label_graphic = new Graphic({
                geometry: point,
                symbol: textSymbol
              });

              mapObjects.labelLayer.add(label_graphic);
            }
          } catch (itemError) {
            console.error('绘制单个标注时出错:', itemError);
          }
        });
      } catch (error) {
        console.error('drawLabel绘制标注出错:', error);
      }
    })
    .catch((error) => {
      console.error('drawLabel加载模块失败:', error);
    });
};

// 辅助函数：安全获取长度
const safeLength = (arr: any[] | undefined): number => (Array.isArray(arr) ? arr.length : 0);

// 其他方法将在后续转换...
</script>

<style lang="scss" scoped>
:deep(.esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
:deep(.esri-sketch__panel) {
  background: #fff;
}
:deep(.esri-view .esri-view-surface:focus::after) {
  outline: none !important;
}
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom .esri-widget--button) {
  background-color: transparent;
  color: #fff;
}
:deep(.esri-zoom .esri-widget--button:hover) {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 10px;
}
.handle-div {
  margin-bottom: 10px;
  padding: 0px 16px;
}
.allot-main {
  display: flex;
  flex-direction: row;
  .left {
    width: 200px;
    border-right: #d3d3d3 solid 1px;
    .gary-div {
      color: rgba(0, 0, 0, 0.5);
      cursor: no-drop !important;
    }
    .title-div {
      height: 70px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0px 16px;
      .big-span {
        font-size: 16px;
        font-weight: 600;
      }
      .success-span {
        color: #67c23a;
      }
      .warry-span {
        color: #f56c6c;
      }
    }
    .end-title {
      display: flex;
      padding: 0px 16px;
      color: #f56c6c;
    }
    .end-removeall {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding-right: 15px;
      margin: 5px 0px;
    }
    .end-box {
      height: calc(100% - 50px) !important;
    }
    .user-box {
      height: calc(100% - 0px);
      overflow: auto;
      .user-item {
        height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
        padding: 0px 16px;
        justify-content: space-between;
        flex-wrap: wrap;
      }
      .user-item:hover {
        background: #d3d3d3;
      }
      .active-user {
        background: #edf6ff;
        color: #409eff;
      }
    }
  }
  .right {
    flex: 1;
    position: relative;
    .map {
      height: 100%;
    }
    .map-change {
      position: absolute;
      bottom: 180px;
      right: 60px;
      display: flex;
      flex-direction: row;
      .map-item {
        width: 96px;
        height: 72px;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: flex-end;
        .map-footer {
          width: 100%;
          height: 22px;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
          border-radius: 0px 0px 8px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #fff;
        }
        .map-footer-active {
          color: #1b9af7;
        }
      }
      .map-left {
        background-image: url('../../assets/images/normal-map.png');
        background-size: cover;
      }
      .map-right {
        background-image: url('../../assets/images/image-map.png');
        background-size: cover;
        margin-left: 8px;
      }
      .map-active {
        border: #1b9af7 solid 1px;
      }
    }
    .tuli-box {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.4);
      padding: 16px;
      border-radius: 8px;
      color: #fff;
      .tuli-title {
        font-size: 14px !important;
        font-weight: 600;
      }
      .tuli-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 12px;
        .label {
          margin-left: 10px;
          text-align: left;
        }
        .tuli-right-gree {
          height: 20px;
          width: 50px;
          border: rgba(84, 255, 159) solid 1px;
          background: rgba(240, 230, 140, 0.2);
        }
        .tuli-right-red {
          height: 20px;
          width: 50px;
          border: rgba(255, 0, 0) solid 1px;
          background: rgba(240, 230, 140, 0.2);
        }
        .tuli-spe {
          height: 20px;
          width: 50px;
          border: rgb(5, 250, 250) solid 3px;
          background: rgb(81, 153, 135);
          display: flex;
          align-items: center;
          justify-content: center;
          .tuli-right-hight {
            height: 24px;
            width: 94px;
            border: rgba(255, 0, 0) solid 1px;
            background: rgba(240, 230, 140, 0.2);
          }
        }
      }
    }
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #6a6a6a;
      position: absolute;
      bottom: 165px;
      right: 16px;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .end {
    width: 260px !important;
  }
}
</style>
