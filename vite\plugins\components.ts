import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import IconsResolver from 'unplugin-icons/resolver';
import path from 'path';

export default () => {
  return Components({
    // 添加这个配置来自动导入src/components目录下的组件
    dirs: ['src/components'],
    // 指定组件的有效扩展名
    extensions: ['vue'],
    // 搜索子目录
    deep: true,
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver(),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ['ep']
      })
    ],
    dts: path.resolve(path.resolve(__dirname, '../../src'), 'types', 'components.d.ts')
  });
};
