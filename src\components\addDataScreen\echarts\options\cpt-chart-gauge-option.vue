<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.title" />
    </el-form-item>
    <el-form-item label="标题字体">
      <el-input-number v-model="attributeCopy.titleSize" :min="1" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" />
    </el-form-item>
    <el-form-item label="最小值">
      <el-input-number v-model="attributeCopy.min" />
    </el-form-item>
    <el-form-item label="最大值">
      <el-input-number v-model="attributeCopy.max" />
    </el-form-item>
    <el-form-item label="起始角度">
      <el-slider v-model="attributeCopy.startAngle" :min="-360" :max="360" :step="1" />
    </el-form-item>
    <el-form-item label="结束角度">
      <el-slider v-model="attributeCopy.endAngle" :min="-360" :max="360" :step="1" />
    </el-form-item>
    <el-form-item label="表盘宽度">
      <el-input-number v-model="attributeCopy.lineWidth" :min="1" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="表盘颜色1">
      <el-color-picker v-model="attributeCopy.color1" />
    </el-form-item>
    <el-form-item label="表盘颜色2">
      <el-color-picker v-model="attributeCopy.color2" />
    </el-form-item>
    <el-form-item label="表盘颜色3">
      <el-color-picker v-model="attributeCopy.color3" />
    </el-form-item>
    <el-form-item label="刻度边距">
      <el-input-number v-model="attributeCopy.lineDistance" :min="-10" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="刻度长度">
      <el-input-number v-model="attributeCopy.tickLength" :min="1" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="刻度值边距">
      <el-input-number v-model="attributeCopy.labelDistance" :min="-10" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="刻度字体">
      <el-input-number v-model="attributeCopy.labelSize" :min="1" :max="100" :step="1" />
    </el-form-item>
    <el-form-item label="指针长度">
      <el-input-number v-model="attributeCopy.pointerLength" :min="1" :max="120" :step="1" />
    </el-form-item>
    <el-form-item label="指针宽度">
      <el-input-number v-model="attributeCopy.pointerWidth" :min="1" :max="120" :step="1" />
    </el-form-item>
    <el-form-item label="指针颜色">
      <el-color-picker v-model="attributeCopy.itemColor" />
    </el-form-item>
    <el-form-item label="数值字体">
      <el-input-number v-model="attributeCopy.detailSize" :min="2" :max="120" :step="1" />
    </el-form-item>
    <el-form-item label="数值颜色">
      <el-color-picker v-model="attributeCopy.detailColor" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-gauge-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>
