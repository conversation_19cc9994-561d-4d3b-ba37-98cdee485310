/**
 * 区域查询参数
 */
export interface AreaParams {
  code: string;
  notTree?: boolean;
}

/**
 * 宗地查询参数
 */
export interface ParcelParams {
  [key: string]: any;
}

/**
 * 宗地详情参数
 */
export interface ParcelDetailParams {
  id: string | number;
  dataScope: string | number;
}

/**
 * 字段查询参数
 */
export interface FieldParams {
  [key: string]: any;
}

/**
 * 导出参数
 */
export interface ExportParams {
  [key: string]: any;
}

/**
 * 林业查询参数
 */
export interface LinyeParams {
  [key: string]: any;
}

/**
 * 像控查询参数
 */
export interface XKParams {
  [key: string]: any;
}

/**
 * 删除宗地参数
 */
export interface DeleteParcelParams {
  [key: string]: any;
}

/**
 * 修改宗地参数
 */
export interface ModifyZDParams {
  [key: string]: any;
}

/**
 * APK版本查询参数
 */
export interface ApkParams {
  [key: string]: any;
}

/**
 * 核查列表参数
 */
export interface GDParams {
  [key: string]: any;
}

/**
 * 异步消息参数
 */
export interface AsyncParams {
  id: string | number;
}

/**
 * 异步文件参数
 */
export interface AsyncFileParams {
  [key: string]: any;
}

/**
 * 批次参数
 */
export interface PCParams {
  [key: string]: any;
}

/**
 * 征地查询参数
 */
export interface ZDParams {
  [key: string]: any;
}

/**
 * 日志查询参数
 */
export interface LogParams {
  id: string | number;
}

/**
 * SHP更新参数
 */
export interface UpdateShpParams {
  [key: string]: any;
}

/**
 * 数据操作参数
 */
export interface OperaParams {
  [key: string]: any;
}

/**
 * 表达式参数
 */
export interface FormulaParams {
  [key: string]: any;
}

/**
 * 简易数据参数
 */
export interface SimpleParams {
  [key: string]: any;
}

/**
 * 数据检查参数
 */
export interface CheckParams {
  [key: string]: any;
}

/**
 * 拓扑检查参数
 */
export interface TuopuParams {
  [key: string]: any;
}

/**
 * 数据回退参数
 */
export interface RetrievalParams {
  linkId: string | number;
  fieldName: string;
  ids: any[];
}

/**
 * 批量修改字段内容参数
 */
export interface UpdateFieldParams {
  [key: string]: any;
}
