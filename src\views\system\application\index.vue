<!-- 邀请列表 -->
<template>
  <container-card>
    <el-form :model="queryParams" ref="queryFormRef" size="default" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请人">
        <el-input v-model="queryParams.custName" placeholder="请输入申请人" clearable />
      </el-form-item>
      <el-form-item label="手机号码">
        <el-input v-model="queryParams.custPhone" placeholder="请输入手机号码"> </el-input>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="getClientTableList">查询</el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain :icon="Check" :disabled="single" @click="handleDealInvited(ids[0], 1)">同意</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain :icon="Close" :disabled="single" @click="handleDealInvited(ids[0], 2)">拒绝</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getClientTableList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请人" align="center" prop="custName" />
      <el-table-column label="手机号码" align="center" prop="custPhone" />
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="primary" plain :icon="Check" @click="handleDealInvited(scope.row, 1)">同意</el-button>
          <el-button type="danger" :icon="Close" plain @click="handleDealInvited(scope.row, 2)">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getClientTableList"
    />

    <!-- 同意加入 -->
    <el-dialog title="同意加入" v-model="open" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-row>
          <el-col :span="11">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入成员姓名" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" :disabled="form.userId != undefined" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="11">
            <el-form-item label="部门" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门" />
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="角色" prop="roleIds">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色" style="width: 100%">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="岗位" prop="postIds">
              <el-select v-model="form.postIds" multiple placeholder="请选择岗位" style="width: 100%">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="性别">
              <el-select v-model="form.sex" placeholder="请选择性别" style="width: 100%">
                <el-option v-for="dict in dictType.sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleAddUser">确 定</el-button>
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </container-card>
</template>

<script lang="ts">
export default {
  name: 'Application'
};
</script>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, defineComponent } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { getClientList, updateClient } from '@/api/client';
import { addUser, getUser, deptTreeSelect } from '@/api/system/user';
import Treeselect from '@zanmato/vue3-treeselect';
import '@zanmato/vue3-treeselect/dist/vue3-treeselect.min.css';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import type { FormItemRule } from 'element-plus';
import { Search, Refresh, Check, Close } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import { useDictStore } from '@/store/modules/dict';

// 字典数据
const dictStore = useDictStore();
dictStore.getDict('sys_normal_disable');
dictStore.getDict('sys_user_sex');
// @ts-expect-error dictStore.getters类型定义问题
const dictType = computed(() => dictStore.getters);

// 获取Pinia中用户数据
const userStore = useUserStore();
const user = computed(() => userStore.user || {});

// 表单引用
const queryFormRef = ref<FormInstance>();
const formRef = ref<FormInstance>();

// 遮罩层
const loading = ref(true);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// Table数据
const applicationList = ref<any[]>([]);
// 显示搜索条件
const showSearch = ref(true);
// 总数
const total = computed(() => applicationList.value.length);
// 选中ID数组
const ids = ref<any[]>([]);

// 查询参数
const queryParams = reactive({
  custPhone: '',
  custName: '',
  dateRange: [] as string[],
  pageSize: 10,
  pageNum: 1
});

// 表单校验规则
const rules = reactive<Record<string, FormItemRule[]>>({
  userName: [
    { required: true, message: '用户名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  password: [
    { required: true, message: '用户密码不能为空', trigger: 'blur' },
    { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
  ],
  deptId: [{ required: true, message: '用户部门不能为空', trigger: ['blur', 'change'] }],
  roleIds: [{ required: true, message: '用户角色不能为空', trigger: ['blur', 'change'] }],
  postIds: [{ required: true, message: '用户岗位不能为空', trigger: ['blur', 'change'] }],
  phonenumber: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
      required: true
    }
  ]
});

// 表单参数
const form = reactive({
  userName: '',
  nickName: '',
  custName: '',
  phonenumber: '',
  roleIds: [] as number[],
  postIds: [] as number[],
  deptId: undefined as number | undefined,
  deptIds: [] as number[],
  remark: '',
  sex: '',
  userId: undefined as number | undefined,
  password: ''
});

// 是否显示弹出层
const open = ref(false);
// 岗位选项
const postOptions = ref<any[]>([]);
// 角色选项
const roleOptions = ref<any[]>([]);
// 部门选项
const deptOptions = ref<any[]>([]);
// 当前项
const currentItem = ref<any>({});
// 当前状态选项 - 同意/拒绝
const currentStatus = ref<number | string>('');

// 初始化数据
onMounted(() => {
  getClientTableList();
  getDeptTree();
  getRoleAndPostList();
});

// 重置查询条件
const handleReset = () => {
  queryParams.custName = '';
  queryParams.custPhone = '';
  queryParams.dateRange = [];
};

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 获取申请列表
const getClientTableList = () => {
  const params = {
    direction: 2,
    status: 0,
    type: 2,
    custName: queryParams.custName,
    custPhone: queryParams.custPhone
  };

  loading.value = true;
  getClientList(params)
    .then((res) => {
      if (res.code === 200) {
        applicationList.value = res.data;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 处理邀请(同意/拒绝)
const handleDealInvited = (row: any, status: number) => {
  const str = status === 1 ? `是否确认同意【${row.custName}】加入?` : `是否确认拒绝【${row.custName}】加入?`;

  ElMessageBox.confirm(str, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      if (status === 1) {
        open.value = true;
        currentItem.value = row;
        currentStatus.value = status;
        form.nickName = row.custName;
        form.phonenumber = row.custPhone;
      } else {
        currentItem.value = row;
        currentStatus.value = status;
        getAdd();
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 同意前先新增用户
const handleAddUser = () => {
  return new Promise<void>((resolve, reject) => {
    const params = {
      userName: form.phonenumber,
      nickName: form.phonenumber,
      custName: form.nickName,
      phonenumber: form.phonenumber,
      roleIds: form.roleIds.map((id) => String(id)),
      postIds: form.postIds.map((id) => String(id)),
      deptIds: [form.deptId].map((id) => (id ? String(id) : '')),
      remark: form.remark,
      sex: form.sex,
      type: 2,
      password: form.password,
      status: '0'
    };
    addUser(params).then((response) => {
      if (response.code === 200) {
        getAdd();
        resolve();
      } else {
        ElMessage.error(response.msg);
        reject(response.msg);
      }
    });
  });
};

// 更新客户状态
const getAdd = () => {
  const params = {
    id: currentItem.value.id,
    companyId: currentItem.value.companyId,
    custPhone: currentItem.value.custPhone,
    status: currentStatus.value, // 取消-1 同意是1 拒绝2
    type: 2
  };

  updateClient(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success(res.msg);
      open.value = false;
      getClientTableList();
    } else {
      ElMessage.error(res.msg);
      open.value = false;
    }
  });
};

// 获取部门树
const getDeptTree = () => {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data;
  });
};

// 获取角色和岗位数据
const getRoleAndPostList = () => {
  // @ts-expect-error userId类型访问问题
  const userId = user.value.userId;
  getUser(userId).then((response: any) => {
    postOptions.value = response.posts;
    roleOptions.value = response.roles.filter((i: any) => {
      return i.roleKey !== 'admin';
    });
    form.password = Math.random().toString(36).slice(-6);
  });
};
</script>

<style lang="scss" scoped></style>
