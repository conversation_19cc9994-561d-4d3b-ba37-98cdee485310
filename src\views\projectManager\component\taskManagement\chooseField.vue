<!-- 选择任务字段 -->
<template>
  <div class="mapField-main">
    <el-dialog
      title="选择任务字段"
      :model-value="chooseFieldDialog"
      @update:model-value="(val: any) => emit('update:chooseFieldDialog', val)"
      width="80%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="getTree"
    >
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">结构树</span></div>
          <div class="content">
            <el-tree
              ref="treeRef"
              :data="treeList"
              :props="defaultProps"
              highlight-current
              default-expand-all
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
        <div class="center">
          <div class="title-div"><span class="normal-sapn">属性组</span></div>
          <div class="content">
            <div v-if="attrbutionGroup.length === 0" class="empty-span">暂无属性组数据</div>
            <template v-else>
              <div
                v-for="(item, index) in attrbutionGroup"
                :key="index"
                class="flex-row"
                :class="{ 'flex-active': item.checked }"
                @click="changeAtt(item)"
              >
                <div class="label" :class="{ 'gry-span': !item.isChoose }" :title="item.typeName">
                  {{ truncateText(item.typeName, 12) }}
                  <span v-if="item.ruleAttribution && (item.ruleAttribution.type === 'graphicalPoint' || item.ruleAttribution.type === 'commonPoint')"
                    >(点)</span
                  >
                  <span v-if="item.ruleAttribution && (item.ruleAttribution.type === 'graphicalLine' || item.ruleAttribution.type === 'commonLine')"
                    >(线)</span
                  >
                </div>
                <div class="ico" style="width: 30px" :class="{ 'gry-span': !item.isChoose }">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="right">
          <div class="title-div">
            <span class="normal-sapn">字段({{ fieldList.length }}条)</span>
          </div>
          <div class="content-handle" v-if="fieldList.length !== 0">
            <div>
              <el-checkbox v-model="checkedAll" @change="checkAll(1)">全选</el-checkbox>
            </div>
            <div style="display: flex; justify-content: space-between; width: 60%">
              <el-checkbox v-model="checkedAllReq" @change="checkAll(2)">全必填</el-checkbox>
              <div style="padding-left: 10px; width: 100%; text-align: center">默认值</div>
            </div>
          </div>
          <div class="content">
            <div v-if="fieldList.length === 0" class="empty-span">暂无字段数据</div>
            <template v-else>
              <div v-for="(item, index) in fieldList" :key="index">
                <!-- 身份证识别 -->
                <div
                  v-if="(item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard') && hasExpendList(item)"
                  class="flex-all check-item"
                >
                  <div class="big-title">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                  <div v-for="ite in item.attribution?.expendList || []" :key="ite.enName" class="flex-row-all">
                    <div class="label">
                      <el-tooltip placement="right" :content="ite.enName + '(' + ite.cnName + ')'">
                        <el-checkbox v-model="ite.checked" @change="changeField(item, ite)">
                          {{ ite.enName }}&nbsp;&nbsp;({{ ite.cnName }})
                        </el-checkbox>
                      </el-tooltip>
                    </div>
                    <div class="flex-right">
                      <div class="ico">
                        <span style="margin-right: 5px" :class="{ gray: !ite.newRequired }">必填</span>
                        <el-checkbox v-model="ite.newRequired" @change="changeReq(ite)" disabled></el-checkbox>
                      </div>
                      <div class="input">
                        <!-- 下拉、单选、多选控件 -->
                        <el-select
                          v-if="['radio', 'select', 'checkbox'].includes(ite.valueMethod) && ite.options && ite.options.length > 0"
                          v-model="ite.default"
                          placeholder="请选择"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                          :multiple="['checkbox'].includes(ite.valueMethod)"
                        >
                          <el-option v-for="op in ite.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
                        </el-select>
                        <!-- 级联选择控件 -->
                        <el-cascader
                          v-else-if="
                            ['cascader'].includes(ite.valueMethod) && ite.attribution && ite.attribution.options && ite.attribution.options.length > 0
                          "
                          v-model="ite.default"
                          :options="ite.attribution.options"
                          placeholder="请选择"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-cascader>
                        <!-- 时间选择器 -->
                        <el-time-picker
                          v-else-if="['time'].includes(ite.valueMethod)"
                          v-model="ite.default"
                          placeholder="请选择时间"
                          value-format="HH:mm:ss"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-time-picker>
                        <!-- 日期选择器 -->

                        <el-date-picker
                          v-else-if="['date', 'date-range'].includes(ite.valueMethod)"
                          v-model="ite.default"
                          :type="ite.valueMethod === 'date' ? 'date' : 'daterange'"
                          :range-separator="ite.valueMethod === 'date-range' ? '至' : ''"
                          :start-placeholder="ite.valueMethod === 'date-range' ? '开始日期' : ''"
                          :end-placeholder="ite.valueMethod === 'date-range' ? '结束日期' : ''"
                          value-format="x"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-date-picker>
                        <!-- 默认输入框 -->
                        <el-input
                          v-else
                          style="width: 100%"
                          type="text"
                          v-model="ite.default"
                          placeholder="默认值"
                          :disabled="
                            [
                              'idCardBitmap',
                              // 银行卡照片不允许设置
                              'BankCardBitmap'
                            ].includes(ite.valueMethod)
                          "
                          @input="handleInput($event, ite, item)"
                        ></el-input>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 表格类型 -->
                <div v-else-if="item.valueMethod === 'xttable' && item.attribution?.children" class="flex-all check-item">
                  <div class="big-title">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                  <div v-for="ite in item.attribution.children" :key="ite.fieldName" class="flex-row-all">
                    <div class="label">
                      <el-tooltip placement="right" :content="ite.fieldName + '(' + ite.fieldCn + ')'">
                        <el-checkbox v-model="ite.checked" @change="changeField(item, ite)">
                          {{ ite.fieldName }}&nbsp;&nbsp;({{ ite.fieldCn }})
                        </el-checkbox>
                      </el-tooltip>
                    </div>
                    <div class="flex-right">
                      <div class="ico">
                        <span style="margin-right: 5px" :class="{ gray: !ite.newRequired }">必填</span>
                        <el-checkbox v-model="ite.newRequired" @change="changeReq(ite, item)"></el-checkbox>
                      </div>
                      <div class="input">
                        <!-- 下拉、单选、多选控件 -->
                        <el-select
                          v-if="
                            ['radio', 'select', 'checkbox'].includes(ite.valueMethod) &&
                            ite.attribution &&
                            ite.attribution.options &&
                            ite.attribution.options.length > 0
                          "
                          v-model="ite.default"
                          placeholder="请选择"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                          :multiple="['checkbox'].includes(ite.valueMethod)"
                        >
                          <el-option v-for="op in ite.attribution.options" :key="op.value" :label="op.label" :value="op.value"></el-option>
                        </el-select>
                        <!-- 级联选择控件 -->
                        <el-cascader
                          v-else-if="
                            ['cascader'].includes(ite.valueMethod) && ite.attribution && ite.attribution.options && ite.attribution.options.length > 0
                          "
                          v-model="ite.default"
                          :options="ite.attribution.options"
                          placeholder="请选择"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-cascader>
                        <!-- 时间选择器 -->
                        <el-time-picker
                          v-else-if="['time'].includes(ite.valueMethod)"
                          v-model="ite.default"
                          placeholder="请选择时间"
                          value-format="HH:mm:ss"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-time-picker>
                        <!-- 日期选择器 -->
                        <el-date-picker
                          v-else-if="['date', 'date-range'].includes(ite.valueMethod)"
                          v-model="ite.default"
                          :type="ite.valueMethod === 'date' ? 'date' : 'daterange'"
                          :range-separator="ite.valueMethod === 'date-range' ? '至' : ''"
                          :start-placeholder="ite.valueMethod === 'date-range' ? '开始日期' : ''"
                          :end-placeholder="ite.valueMethod === 'date-range' ? '结束日期' : ''"
                          value-format="x"
                          @change="handleInput($event, ite, item)"
                          style="width: 100%"
                        >
                        </el-date-picker>
                        <!-- 默认输入框 -->
                        <el-input
                          v-else
                          style="width: 100%"
                          type="text"
                          v-model="ite.default"
                          placeholder="默认值"
                          @input="handleInput($event, ite, item)"
                          :disabled="
                            ['idCardScan', 'xtzw', 'xtqm', 'xtzwsb', 'xtdwsb', 'xtvideo', 'upload', 'xtsjjt', 'xtfj', 'xtpay'].includes(
                              ite.valueMethod
                            )
                          "
                        ></el-input>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="flex-row check-item">
                  <div class="label">
                    <el-tooltip placement="right" :content="item.fieldName + '(' + item.fieldCn + ')'">
                      <el-checkbox v-model="item.checked" @change="changeField(item)">
                        {{ item.fieldName }}&nbsp;&nbsp;(
                        {{ item.fieldCn ? (item.fieldCn.length > 5 ? item.fieldCn.substring(0, 5) + '...' : item.fieldCn) : '' }})
                      </el-checkbox>
                    </el-tooltip>
                  </div>
                  <div class="flex-right">
                    <div class="ico">
                      <span style="margin-right: 5px" :class="{ gray: !item.newRequired }">必填</span>
                      <el-checkbox v-model="item.newRequired" @change="changeReq(item)"></el-checkbox>
                    </div>
                    <div class="input">
                      <!-- 这是是下拉，单选，多选设置的选项值拿出来让用户自己选择 -->
                      <el-select
                        v-model="item.default"
                        placeholder="请选择"
                        @change="handleInput($event, item)"
                        style="width: 100%"
                        :multiple="['checkbox'].includes(item.valueMethod)"
                        v-if="['radio', 'select', 'checkbox'].includes(item.valueMethod) && item.attribution && item.attribution.options.length > 0"
                      >
                        <el-option v-for="item in item.attribution.options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                      <!-- 这是级联选择 -->
                      <el-cascader
                        :disabled="['cascader'].includes(item.valueMethod)"
                        v-else-if="['cascader'].includes(item.valueMethod) && item.attribution && item.attribution.options.length > 0"
                        v-model="item.default"
                        :options="item.attribution.options"
                        placeholder="请选择"
                        @change="handleInput($event, item)"
                        style="width: 100%"
                      >
                      </el-cascader>
                      <!-- 这里是时间选择器 -->
                      <el-time-picker
                        v-else-if="['time'].includes(item.valueMethod)"
                        v-model="item.default"
                        placeholder="请选择时间"
                        value-format="HH:mm:ss"
                        @change="handleInput($event, item)"
                        style="width: 100%"
                      >
                      </el-time-picker>
                      <!-- 日期时间控件 -->
                      <el-date-picker
                        @change="handleInput($event, item)"
                        value-format="x"
                        style="width: 100%"
                        v-else-if="['date'].includes(item.valueMethod)"
                        v-model="item.default"
                        type="date"
                      >
                      </el-date-picker>
                      <!-- 把日期区间分开 -->
                      <el-date-picker
                        v-else-if="item.valueMethod == 'date-range'"
                        v-model="item.default"
                        type="daterange"
                        value-format="x"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 100%"
                      >
                      </el-date-picker>
                      <!-- 默认的其他可输入的类型的选项 -->
                      <!-- :disabled="item.valueMethod == 'idCardScan'"  这是展示先只限制老数据的身份证识别，动植物识别展示不考虑 -->
                      <el-input
                        style="width: 100%"
                        type="text"
                        v-model="item.default"
                        placeholder="默认值"
                        @input="handleInput($event, item)"
                        v-else
                        :disabled="
                          ['idCardScan', 'xtzw', 'xtqm', 'xtzwsb', 'xtdwsb', 'xtvideo', 'upload', 'xtsjjt', 'xtfj', 'xtpay'].includes(
                            item.valueMethod
                          )
                        "
                      ></el-input>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose"> 取 消 </el-button>
          <el-button type="primary" @click="submitField">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import { selectRules } from '@/api/modal';
import type { ElTree } from 'element-plus';

// 定义接口
interface FieldModel {
  id: string | number;
  fieldId?: string | number;
  fieldName: string;
  fieldCn: string;
  valueMethod: string;
  required?: number;
  checked?: boolean;
  newRequired?: boolean;
  default?: any;
  groupId: string | number;
  attribution?: {
    default?: any;
    defaultValue?: any;
    options?: Array<{ label: string; value: any }>;
    expendList?: Array<{
      enName: string;
      cnName: string;
      checked?: boolean;
      newRequired?: boolean;
      default?: any;
      label?: string;
    }>;
    children?: Array<{
      fieldName: string;
      fieldCn: string;
      checked?: boolean;
      newRequired?: boolean;
      default?: any;
      valueMethod?: string;
    }>;
  };
}

interface FieldGroupModel {
  id: string | number;
  linkId: string | number;
  typeName: string;
  isChoose?: boolean;
  checked?: boolean;
  fieldModelList: FieldModel[];
  ruleAttribution?: {
    type: string;
  };
}

interface TreeItem {
  id: string | number;
  wordName: string;
  typeName: string;
  list: TreeItem[];
  fieldGroupModelList: FieldGroupModel[];
}

interface TaskGroupModel {
  linkId: string | number;
  groupId: string | number;
  fieldModels: Array<{
    fieldId: string | number;
    required: number;
    attribution: {
      default?: any;
      expendList?: Array<{
        enName: string;
        cnName: string;
        checked?: boolean;
        newRequired?: boolean;
        default?: any;
      }>;
      children?: Array<{
        fieldName: string;
        fieldCn: string;
        checked?: boolean;
        newRequired?: boolean;
        default?: any;
        valueMethod?: string;
      }>;
    };
  }>;
}

interface ExpendListItem {
  enName: string;
  cnName: string;
  checked?: boolean;
  newRequired?: boolean;
  default?: any;
  label?: string;
}

// Props
const props = defineProps<{
  chooseFieldDialog: boolean;
  moduleId: string | number;
  taskGroupModels: TaskGroupModel[];
  title: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:chooseFieldDialog', value: boolean): void;
  (e: 'closeFieldDialog'): void;
  (e: 'submitField', value: TaskGroupModel[]): void;
  (e: 'update:taskGroupModels', value: TaskGroupModel[]): void;
}>();

// Refs
const treeRef = ref<InstanceType<typeof ElTree>>();
const treeList = ref<TreeItem[]>([]);
const attrbutionGroup = ref<FieldGroupModel[]>([]);
const fieldList = ref<FieldModel[]>([]);
const checkedYS = ref<string>('');
const checkedAtt = ref<FieldGroupModel | null>(null);
const fieldCn = ref<string>('');
const checkedAll = ref<boolean>(false);
const checkedAllReq = ref<boolean>(false);

// 本地副本
const localTaskGroups = ref<TaskGroupModel[]>([]);

// 默认属性
const defaultProps = {
  children: 'list',
  label: 'typeName'
};

/**
 * 截取文本
 * @param text 文本
 * @param length 长度
 * @returns 截取后的文本
 */
const truncateText = (text: string, length: number): string => {
  return text.length > length ? text.slice(0, length) + '...' : text;
};
/**
 * 处理输入
 * @param val 值
 * @param item 字段
 */

const handleInput = (val: any, item: any, parentItem?: FieldModel) => {
  if (val && val !== '' && val != null) {
    // 处理特殊类型的值（多选、日期范围等）
    if (['checkbox', 'date-range'].includes(item.valueMethod) && Array.isArray(val)) {
      item.default = val;
    } else {
      item.default = val;
    }
    // 需要判断是不是子要素的 通过是否传输第三个参数判断
    const isChild = parentItem ? true : false;
    if (isChild) {
      // 特殊 身份证、银行卡、表格
      for (let i = 0; i < localTaskGroups.value.length; i++) {
        if (localTaskGroups.value[i].groupId === parentItem?.groupId) {
          for (let j = 0; j < localTaskGroups.value[i].fieldModels.length; j++) {
            if (localTaskGroups.value[i].fieldModels[j].fieldId === parentItem.id) {
              let child_list = [];
              let child_type = 1; //判断类型 1为身份证识别、银行卡识别 2为表格
              if (localTaskGroups.value[i].fieldModels[j].attribution.expendList) {
                child_list = localTaskGroups.value[i].fieldModels[j].attribution.expendList;
                child_type = 1;
              } else if (localTaskGroups.value[i].fieldModels[j].attribution.children) {
                child_list = localTaskGroups.value[i].fieldModels[j].attribution.children;
                child_type = 2;
              }
              for (let k = 0; k < child_list.length; k++) {
                if (child_type === 1 && child_list[k].enName === item.enName) {
                  child_list[k].default = item.default;
                  break;
                } else if (child_type === 2 && child_list[k].fieldName === item.fieldName) {
                  child_list[k].default = item.default;
                  break;
                }
              }
              break;
            }
          }
          break;
        }
      }
    } else {
      for (let i = 0; i < localTaskGroups.value.length; i++) {
        if (localTaskGroups.value[i].groupId === item.groupId) {
          for (let j = 0; j < localTaskGroups.value[i].fieldModels.length; j++) {
            if (localTaskGroups.value[i].fieldModels[j].fieldId === item.id) {
              localTaskGroups.value[i].fieldModels[j].attribution.default = item.default;
              break;
            }
          }
          break;
        }
      }
    }
  }
};

/**
 * 获取树形数据
 * @param moduleId 模块ID
 */
const getTree = async () => {
  try {
    const res = await selectRules({ moduleId: props.moduleId });
    if (res.code === 200) {
      treeList.value = res.data;
      attrbutionGroup.value = res.data[0].fieldGroupModelList;
      handleNodeClick(res.data[0]);
      checkedYS.value = res.data[0].wordName;
      await nextTick();
      treeRef.value?.setCurrentKey(res.data[0].id);
      changeAtt(attrbutionGroup.value[0]);

      if (props.title === '新增任务' && props.taskGroupModels.length === 0) {
        checkedAll.value = true;
        treeList.value.forEach((v) => {
          initAll(v);
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

/**
 * 初始化所有
 * @param obj 树形数据
 */
const initAll = (obj: TreeItem) => {
  obj.fieldGroupModelList.forEach((v) => {
    const item: TaskGroupModel = {
      groupId: v.id,
      linkId: v.linkId,
      fieldModels: []
    };
    console.log(v.fieldModelList);
    v.fieldModelList.forEach((k) => {
      const ite = {
        fieldId: k.id,
        required: k.required || 2,
        attribution: {
          default: k.default && k.default !== '' && k.default != null ? k.default : undefined,
          expendList: k.attribution?.expendList
        }
      };

      if (k.attribution?.defaultValue) {
        k.default = k.attribution.defaultValue;
      }

      if (k.valueMethod === 'idCardScan' || k.valueMethod === 'xtBankCard') {
        const expendList = k.attribution?.expendList;
        if (expendList && expendList.length > 0) {
          expendList.forEach((e) => {
            e.checked = true;
          });
          ite.attribution = {
            default: undefined,
            expendList
          };
        }
      } else if (k.valueMethod === 'xttable' && k.attribution?.children) {
        // 添加对xttable控件子字段的处理，使其默认全选
        k.attribution.children.forEach((child) => {
          child.checked = true;
          if (child.required === 1) {
            child.newRequired = true;
          }
        });

        // 确保attribution中包含children
        ite.attribution = {
          ...ite.attribution,
          children: k.attribution.children
        };
      }

      k.checked = true;
      if (k.required === 1) {
        k.newRequired = true;
      }
      item.fieldModels.push(ite);
    });

    v.isChoose = true;
    const newTaskGroups = [...localTaskGroups.value, item];
    updateTaskGroups(newTaskGroups);
  });

  if (obj.list.length !== 0) {
    obj.list.forEach((v) => {
      initAll(v);
    });
  }
};

/**
 * 关闭
 */
const handleClose = () => {
  fieldList.value = [];
  attrbutionGroup.value = [];
  localTaskGroups.value = [];
  emit('update:chooseFieldDialog', false);
  nextTick(() => {
    emit('closeFieldDialog');
  });
};

/**
 * 提交字段
 */
const submitField = () => {
  //打印字段的默认值
  if (localTaskGroups.value.length === 0) {
    ElMessage.error('您未选中字段');
    return;
  }
  // 过滤掉未选中的子字段
  localTaskGroups.value.forEach((group) => {
    group.fieldModels = group.fieldModels.filter((field) => {
      if (field.attribution?.expendList) {
        field.attribution.expendList = field.attribution.expendList.filter((e) => e.checked);
        return field.attribution.expendList.length > 0;
      } else if (field.attribution?.children) {
        field.attribution.children = field.attribution.children.filter((c) => c.checked);
        return field.attribution.children.length > 0;
      }
      return true; // 普通字段始终保留，如果需要也可添加 checked 检查
    });
  });

  // 处理date-range类型的数据，将数组转换为逗号分隔的字符串
  localTaskGroups.value.forEach((group) => {
    group.fieldModels.forEach((field) => {
      // 处理普通字段的date-range类型
      if (
        field.attribution?.default &&
        Array.isArray(field.attribution.default) &&
        fieldList.value.find((f) => f.id === field.fieldId)?.valueMethod === 'date-range'
      ) {
        field.attribution.default = field.attribution.default.join(',');
      }

      // 处理expendList中的date-range类型（身份证识别等）
      if (field.attribution?.expendList) {
        field.attribution.expendList.forEach((expendItem) => {
          if (expendItem.default && Array.isArray(expendItem.default)) {
            const originalField = fieldList.value.find((f) => f.id === field.fieldId);
            const originalExpendItem = originalField?.attribution?.expendList?.find((e) => e.enName === expendItem.enName);
            if (originalExpendItem?.valueMethod === 'date-range') {
              expendItem.default = expendItem.default.join(',');
            }
          }
        });
      }

      // 处理children中的date-range类型（表格类型）
      if (field.attribution?.children) {
        field.attribution.children.forEach((child) => {
          if (child.default && Array.isArray(child.default)) {
            const originalField = fieldList.value.find((f) => f.id === field.fieldId);
            const originalChild = originalField?.attribution?.children?.find((c) => c.fieldName === child.fieldName);
            if (originalChild?.valueMethod === 'date-range') {
              child.default = child.default.join(',');
            }
          }
        });
      }
    });
  });
  // 判断是否存在上一次的数据
  if (props.taskGroupModels.length > 0) {
    // 如果存在数据，先清空再提交新数据
    const newData = [...localTaskGroups.value]; // 保存新数据的副本
    emit('submitField', []);
    setTimeout(() => {
      emit('submitField', newData);
      // 发送完新数据后再清空本地数据
      fieldList.value = [];
      attrbutionGroup.value = [];
      localTaskGroups.value = [];
      checkedAll.value = false;
      checkedAllReq.value = false;
      checkedAtt.value = null;
    }, 100);
  } else {
    // 如果不存在上一次数据，直接提交
    emit('submitField', localTaskGroups.value);
    // 发送完新数据后再清空本地数据
    fieldList.value = [];
    attrbutionGroup.value = [];
    localTaskGroups.value = [];
    checkedAll.value = false;
    checkedAllReq.value = false;
    checkedAtt.value = null;
  }
};

/**
 * 处理节点点击
 * @param data 树形数据
 */
const handleNodeClick = (data: TreeItem) => {
  attrbutionGroup.value = data.fieldGroupModelList;
  attrbutionGroup.value.forEach((v) => {
    v.isChoose = false;
  });

  attrbutionGroup.value.forEach((v) => {
    for (let i = 0; i < props.taskGroupModels.length; i++) {
      if (v.linkId === props.taskGroupModels[i].linkId) {
        v.isChoose = true;
        break;
      }
    }
  });

  checkedYS.value = data.wordName;
  fieldList.value = [];
};

/**
 * 改变属性
 * @param item 属性
 */
const changeAtt = (item: FieldGroupModel) => {
  attrbutionGroup.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  checkedAtt.value = item;
  fieldList.value = item.fieldModelList;
  // 查找当前属性组在本地数据中的状态
  const currentGroupData = localTaskGroups.value.find((group) => group.linkId === item.linkId && group.groupId === item.id);

  // 保存当前字段的默认值，以便在重置后恢复
  const savedDefaults = new Map();
  fieldList.value.forEach((field) => {
    if (field.valueMethod === 'idCardScan' || field.valueMethod === 'xtBankCard') {
      field.attribution?.expendList?.forEach((subField) => {
        if (subField.default) {
          savedDefaults.set(`${field.id}_${subField.enName}`, subField.default);
        }
      });
    } else if (field.valueMethod === 'xttable') {
      field.attribution?.children?.forEach((child) => {
        if (child.default) {
          savedDefaults.set(`${field.id}_${child.fieldName}`, child.default);
        }
      });
    } else if (field.default) {
      savedDefaults.set(field.id, field.default);
    }
  });

  // 重置所有字段的状态
  fieldList.value.forEach((v) => {
    // 所有字段类型默认都不选中
    v.checked = false;
    v.newRequired = false;

    // 如果是表格类型，子字段也不选中
    if (v.valueMethod === 'xttable' && v.attribution?.children) {
      v.attribution.children.forEach((child) => {
        child.checked = false;
        // 恢复保存的默认值
        child.default = savedDefaults.get(`${v.id}_${child.fieldName}`) || '';
      });
    } else {
      // 恢复保存的默认值
      v.default = savedDefaults.get(v.id) || v.attribution?.defaultValue || '';
    }

    // 重置身份证识别类型的子字段
    if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && hasExpendList(v)) {
      v.attribution?.expendList?.forEach((sfz) => {
        sfz.checked = false;
        sfz.newRequired = false;
        // 恢复保存的默认值
        sfz.default = savedDefaults.get(`${v.id}_${sfz.enName}`) || '';
      });
    }
  });
  // 如果在本地数据中找到了这个属性组，使用保存的状态
  if (currentGroupData) {
    currentGroupData.fieldModels.forEach((fieldData) => {
      const field = fieldList.value.find((f) => f.id === fieldData.fieldId);
      if (field) {
        // 处理身份证识别类型的子字段
        if ((field.valueMethod === 'idCardScan' || field.valueMethod === 'xtBankCard') && hasExpendList(field)) {
          field.attribution?.expendList?.forEach((sfz) => {
            const savedState = fieldData.attribution?.expendList?.find((e) => e.enName === sfz.enName);
            if (savedState && savedState.checked) {
              sfz.checked = true;
              sfz.newRequired = savedState.newRequired;
              sfz.default = savedState.default || '';
            }
          });
        } else if (field.valueMethod === 'xttable' && field.attribution?.children) {
          field.attribution.children.forEach((child) => {
            const savedChild = fieldData.attribution?.children?.find((c) => c.fieldName === child.fieldName);
            if (savedChild && savedChild.checked) {
              child.checked = true;
              child.newRequired = savedChild.newRequired;
              child.default = savedChild.default || '';
            }
          });
        } else {
          // 普通字段
          field.checked = true;
          field.newRequired = fieldData.required === 1;
          if (fieldData.attribution.default) {
            field.default = fieldData.attribution.default;
          }
        }
      }
    });
  }

  // 更新全选和全必填状态
  const allChecked = fieldList.value.every((v) => {
    if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && hasExpendList(v)) {
      return v.attribution?.expendList?.every((sfz) => sfz.checked) ?? false;
    } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
      return v.attribution.children.every((child) => child.checked);
    }
    return v.checked;
  });

  const allRequired = fieldList.value.every((v) => {
    if (v.valueMethod === 'xttable' && v.attribution?.children) {
      return v.attribution.children.every((child) => child.newRequired);
    }
    return v.newRequired;
  });

  checkedAll.value = allChecked;
  checkedAllReq.value = allRequired;

  // 更新当前属性组的选中状态
  item.isChoose = currentGroupData !== undefined;
};

/**
 * 改变字段
 * @param item 字段
 * @param childItem 子字段
 */
const changeField = (item: FieldModel, childItem?: any) => {
  if (item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard') {
    handleSpecialSelectField(item, childItem);
  } else if (item.valueMethod === 'xttable') {
    handleTableSelectField(item, childItem);
  } else {
    handleSelectField(item);
  }
};

/**
 * 处理选择字段
 * @param item 字段
 */
const handleSelectField = (item: FieldModel) => {
  if (item.checked) {
    // 选中字段时
    let attrFlg = false;
    let attr: TaskGroupModel | null = null;
    let required = 2;

    if (item.newRequired) {
      required = 1;
    }

    // 查找当前属性组是否已存在于本地数据中
    const attrIndex = localTaskGroups.value.findIndex((group) => group.groupId === item.groupId && group.linkId === checkedAtt.value?.linkId);

    if (attrIndex !== -1) {
      // 如果存在，获取属性组
      attrFlg = true;
      attr = localTaskGroups.value[attrIndex];
    }

    if (attrFlg && attr) {
      // 如果属性组存在，检查字段是否已存在
      let fieldFlg = false;
      for (let i = 0; i < attr.fieldModels.length; i++) {
        if (attr.fieldModels[i].fieldId === item.id) {
          fieldFlg = true;
        }
      }

      if (!fieldFlg) {
        // 如果字段不存在，添加到字段列表
        attr.fieldModels.push({
          fieldId: item.id,
          required,
          attribution: {
            default: item.default && item.default !== '' && item.default != null ? item.default : undefined
          }
        });

        // 更新本地副本
        const newTaskGroups = [...localTaskGroups.value];
        for (let i = 0; i < newTaskGroups.length; i++) {
          if (newTaskGroups[i].groupId === attr.groupId && newTaskGroups[i].linkId === attr.linkId) {
            newTaskGroups[i] = attr;
            break;
          }
        }
        updateTaskGroups(newTaskGroups);

        // 更新当前属性组在UI中的选中状态
        if (checkedAtt.value) {
          checkedAtt.value.isChoose = true;
        }
      }
    } else {
      // 如果属性组不存在，创建新的属性组
      const newAttr: TaskGroupModel = {
        linkId: checkedAtt.value?.linkId || '',
        groupId: checkedAtt.value?.id || '',
        fieldModels: [
          {
            fieldId: item.id,
            required,
            attribution: {
              default: item.default && item.default !== '' && item.default != null ? item.default : undefined
            }
          }
        ]
      };

      const newTaskGroups = [...localTaskGroups.value, newAttr];
      updateTaskGroups(newTaskGroups);

      // 更新当前属性组在UI中的选中状态
      if (checkedAtt.value) {
        checkedAtt.value.isChoose = true;
      }
    }

    // 检查是否所有字段都已选中，如果是则更新全选状态
    const allChecked = fieldList.value.every((v) => {
      if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && hasExpendList(v)) {
        return v.attribution?.expendList?.every((sfz) => sfz.checked) ?? false;
      } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
        return v.attribution.children.every((child) => child.checked);
      }
      return v.checked;
    });
    checkedAll.value = allChecked;
  } else {
    // 取消选中字段时
    // 查找当前属性组
    const attrIndex = localTaskGroups.value.findIndex((group) => group.groupId === item.groupId && group.linkId === checkedAtt.value?.linkId);

    if (attrIndex !== -1) {
      const attr = localTaskGroups.value[attrIndex];
      const fieldModelIndex = attr.fieldModels.findIndex((fm) => fm.fieldId === item.id);

      if (fieldModelIndex !== -1) {
        // 如果只有一个字段且要删除它，则删除整个属性组
        if (attr.fieldModels.length === 1) {
          const newTaskGroups = [...localTaskGroups.value];
          newTaskGroups.splice(attrIndex, 1);
          updateTaskGroups(newTaskGroups);

          // 如果没有字段被选中，更新属性组状态
          if (checkedAtt.value && attr.fieldModels.length <= 1) {
            checkedAtt.value.isChoose = false;
          }
        } else {
          // 如果有多个字段，只删除当前字段
          const newTaskGroups = [...localTaskGroups.value];
          newTaskGroups[attrIndex].fieldModels.splice(fieldModelIndex, 1);
          updateTaskGroups(newTaskGroups);

          // 如果删除后还有字段，属性组仍然保持选中状态
          if (checkedAtt.value) {
            checkedAtt.value.isChoose = newTaskGroups[attrIndex].fieldModels.length > 0;
          }
        }
      }

      // 更新全选状态
      checkedAll.value = false;
    }
  }
};

/**
 * 处理特殊选择字段
 * @param item 字段
 * @param child 子字段
 */
const handleSpecialSelectField = (item: FieldModel, child: ExpendListItem) => {
  // 获取当前属性组在本地数据中的索引
  const attrIndex = localTaskGroups.value.findIndex((group) => group.groupId === item.groupId && group.linkId === checkedAtt.value?.linkId);

  if (child.checked) {
    // 选中时的处理
    if (attrIndex === -1) {
      // 如果属性组不存在，创建新的
      const newAttr: TaskGroupModel = {
        linkId: checkedAtt.value?.linkId || '',
        groupId: item.groupId,
        fieldModels: [
          {
            fieldId: item.id,
            required: child.newRequired ? 1 : 2,
            attribution: {
              expendList: [{ ...child }]
            }
          }
        ]
      };
      localTaskGroups.value.push(newAttr);
    } else {
      // 如果属性组存在
      const fieldIndex = localTaskGroups.value[attrIndex].fieldModels.findIndex((f) => f.fieldId === item.id);

      if (fieldIndex === -1) {
        // 如果字段不存在，添加新字段
        localTaskGroups.value[attrIndex].fieldModels.push({
          fieldId: item.id,
          required: child.newRequired ? 1 : 2,
          attribution: {
            expendList: [{ ...child }]
          }
        });
      } else {
        // 如果字段存在，更新或添加子字段
        const currentField = localTaskGroups.value[attrIndex].fieldModels[fieldIndex];
        if (!currentField.attribution) {
          currentField.attribution = { expendList: [] };
        }
        if (!currentField.attribution.expendList) {
          currentField.attribution.expendList = [];
        }

        // 检查子字段是否已存在
        const existingChildIndex = currentField.attribution.expendList.findIndex((e: any) => e.enName === child.enName);

        if (existingChildIndex === -1) {
          currentField.attribution.expendList.push({ ...child });
        } else {
          currentField.attribution.expendList[existingChildIndex] = { ...child };
        }
      }
    }
  } else {
    // 取消选中：只更新 checked，不移除字段或组
    child.checked = false;
    // 可选：更新 localTaskGroups 中的状态，但不删除
    const group = localTaskGroups.value.find((g) => g.groupId === checkedAtt.value?.id);
    if (group) {
      const fieldModel = group.fieldModels.find((f) => f.fieldId === item.id);
      if (fieldModel && fieldModel.attribution?.expendList) {
        const expendItem = fieldModel.attribution.expendList.find((e) => e.enName === child.enName);
        if (expendItem) expendItem.checked = false;
      }
    }
    // 更新全选状态
    checkedAll.value = false;
    // 移除原有的移除逻辑
  }
  // 更新本地数据
  updateTaskGroups([...localTaskGroups.value]);

  // 更新UI状态
  if (checkedAtt.value) {
    checkedAtt.value.isChoose = localTaskGroups.value.some((group) => group.groupId === item.groupId && group.linkId === checkedAtt.value?.linkId);
  }
};

/**
 * 添加处理表格字段的方法
 * @param item 字段
 * @param childItem 子字段
 */
const handleTableSelectField = (item: FieldModel, child: any) => {
  if (child.checked) {
    // 选中逻辑保持不变
    let attrFlg = false;
    let attr: TaskGroupModel | null = null;

    for (let i = 0; i < localTaskGroups.value.length; i++) {
      if (item.groupId === localTaskGroups.value[i].groupId && localTaskGroups.value[i].linkId === checkedAtt.value?.linkId) {
        attrFlg = true;
        attr = localTaskGroups.value[i];
        break;
      }
    }

    if (attrFlg && attr) {
      let fieldFlg = false;
      let fieldIndex = -1;

      for (let i = 0; i < attr.fieldModels.length; i++) {
        if (attr.fieldModels[i].fieldId === item.id) {
          fieldFlg = true;
          fieldIndex = i;
          break;
        }
      }

      if (!fieldFlg) {
        // 如果字段不存在，添加新字段
        attr.fieldModels.push({
          fieldId: item.id,
          required: child.newRequired ? 1 : 2,
          attribution: {
            children: [child]
          }
        });
      } else {
        // 如果字段存在，更新子字段
        const existingChildren = attr.fieldModels[fieldIndex].attribution.children || [];
        const childExists = existingChildren.some((c: any) => c.fieldName === child.fieldName);

        if (!childExists) {
          existingChildren.push(child);
          attr.fieldModels[fieldIndex].attribution.children = existingChildren;
        }
      }

      // 更新本地副本
      const newTaskGroups = [...localTaskGroups.value];
      updateTaskGroups(newTaskGroups);
    } else {
      // 如果属性组不存在，创建新的属性组
      const newAttr: TaskGroupModel = {
        linkId: checkedAtt.value?.linkId || '',
        groupId: checkedAtt.value?.id || '',
        fieldModels: [
          {
            fieldId: item.id,
            required: child.newRequired ? 1 : 2,
            attribution: {
              children: [child]
            }
          }
        ]
      };

      const newTaskGroups = [...localTaskGroups.value, newAttr];
      updateTaskGroups(newTaskGroups);
    }
  } else {
    // 取消选中：只更新 checked，不移除
    child.checked = false;
    // 更新 localTaskGroups，但不删除
    const group = localTaskGroups.value.find((g) => g.groupId === checkedAtt.value?.id);
    if (group) {
      const fieldModel = group.fieldModels.find((f) => f.fieldId === item.id);
      if (fieldModel && fieldModel.attribution?.children) {
        const childItem = fieldModel.attribution.children.find((c) => c.fieldName === child.fieldName);
        if (childItem) childItem.checked = false;
      }
    }
    // 移除原有的移除逻辑（如 if (item.attribution.children.every(c => !c.checked)) { remove }）
    const newTaskGroups = [...localTaskGroups.value];
    updateTaskGroups(newTaskGroups);
  }
};

/**
 * 改变请求
 * @param item 字段
 */
const changeReq = (item: any, parentItem?: FieldModel) => {
  let required = 2;
  if (item.newRequired) {
    required = 1;
  }

  if ('id' in item && 'groupId' in item) {
    let attr: TaskGroupModel | null = null;

    for (let i = 0; i < localTaskGroups.value.length; i++) {
      if (localTaskGroups.value[i].groupId === item.groupId) {
        attr = localTaskGroups.value[i];
        break;
      }
    }

    attr?.fieldModels.forEach((v) => {
      if (v.fieldId === item.id) {
        v.required = required;
      }
    });
  } else if (parentItem) {
    // 处理表格子字段的必填状态
    localTaskGroups.value.forEach((ite) => {
      if (ite.groupId === parentItem.groupId) {
        ite.fieldModels.forEach((field) => {
          if (field.fieldId === parentItem.id && field.attribution?.children) {
            const child = field.attribution.children.find((c: any) => c.fieldName === item.fieldName);
            if (child) {
              child.newRequired = item.newRequired;
            }
          }
        });
      }
    });
  }

  let count = 0;
  fieldList.value.forEach((v) => {
    if (v.newRequired) {
      count++;
    }
  });

  checkedAllReq.value = count === fieldList.value.length;
};

/**
 * 全选
 * @param type 类型
 */
const checkAll = (type: number) => {
  // 先找到当前属性组在 taskGroupModels 中的位置
  const currentGroupIndex = props.taskGroupModels.findIndex(
    (group) => group.groupId === checkedAtt.value?.id && group.linkId === checkedAtt.value?.linkId
  );

  if (type === 1) {
    // 全选/取消全选字段
    checkAllFiled(currentGroupIndex !== -1, currentGroupIndex);
  } else {
    // 全选/取消全选必填
    checkAllRequired(currentGroupIndex !== -1, currentGroupIndex);
  }

  // 确保UI状态同步更新
  nextTick(() => {
    if (checkedAtt.value) {
      checkedAtt.value.isChoose = localTaskGroups.value.some(
        (group) => group.groupId === checkedAtt.value?.id && group.linkId === checkedAtt.value?.linkId
      );
    }
  });
};

/**
 * 全选字段
 * @param attrFlg 是否选中
 * @param num 索引
 */
const checkAllFiled = (attrFlg: boolean, num: number) => {
  if (checkedAll.value) {
    // 全选操作
    const fieldModels: Array<{
      fieldId: string | number;
      required: number;
      attribution: {
        default?: any;
        expendList?: Array<{
          enName: string;
          cnName: string;
          checked?: boolean;
          newRequired?: boolean;
          default?: any;
        }>;
        children?: Array<{
          fieldName: string;
          fieldCn: string;
          checked?: boolean;
          newRequired?: boolean;
          default?: any;
          valueMethod?: string;
        }>;
      };
    }> = [];

    fieldList.value.forEach((v) => {
      // 处理身份证识别类型
      if (v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') {
        const expendList: any[] = [];
        if (v.attribution?.expendList) {
          v.attribution.expendList.forEach((e) => {
            e.checked = true;
            expendList.push({
              ...e,
              checked: true,
              newRequired: e.newRequired || false,
              default: e.default || ''
            });
          });
        }
        v.checked = true;
        fieldModels.push({
          fieldId: v.id,
          required: v.newRequired ? 1 : 2,
          attribution: {
            default: v.default && v.default !== '' && v.default != null ? v.default : undefined,
            expendList
          }
        });
      } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
        // 处理表格类型字段
        const children = v.attribution.children.map((child) => ({
          ...child,
          checked: true,
          newRequired: child.newRequired || false,
          default: child.default || ''
        }));
        v.attribution.children.forEach((child) => {
          child.checked = true;
        });
        v.checked = true;
        fieldModels.push({
          fieldId: v.id,
          required: v.newRequired ? 1 : 2,
          attribution: {
            children
          }
        });
      } else {
        v.checked = true;
        fieldModels.push({
          fieldId: v.id,
          required: v.newRequired ? 1 : 2,
          attribution: {
            default: v.default && v.default !== '' && v.default != null ? v.default : undefined
          }
        });
      }
    });

    // 更新或添加属性组
    const newTaskGroups = [...localTaskGroups.value];
    const existingGroupIndex = newTaskGroups.findIndex(
      (group) => group.groupId === checkedAtt.value?.id && group.linkId === checkedAtt.value?.linkId
    );

    if (existingGroupIndex !== -1) {
      newTaskGroups[existingGroupIndex].fieldModels = fieldModels;
    } else {
      newTaskGroups.push({
        linkId: checkedAtt.value?.linkId || '',
        groupId: checkedAtt.value?.id || '',
        fieldModels
      });
    }
    updateTaskGroups(newTaskGroups);

    // 更新当前属性组在UI中的选中状态
    if (checkedAtt.value) {
      checkedAtt.value.isChoose = true;
    }
  } else {
    // 取消全选操作
    fieldList.value.forEach((v) => {
      if (v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') {
        v.attribution?.expendList?.forEach((e) => {
          e.checked = false;
          e.newRequired = false;
          // e.default = ''; // 移除这行，保留默认值
        });
      } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
        // 处理表格类型字段的取消全选
        v.attribution.children.forEach((child) => {
          child.checked = false;
          child.newRequired = false;
          // child.default = ''; // 移除这行，保留默认值
        });
      }
      v.checked = false;
      v.newRequired = false;
      // v.default = ''; // 移除这行，保留默认值
    });

    // 清空本地数据但保留默认值
    localTaskGroups.value = [];
    // 更新属性组状态
    if (checkedAtt.value) {
      checkedAtt.value.isChoose = false;
    }
  }
};

/**
 * 全选请求
 * @param attrFlg 是否选中
 * @param num 索引
 */
const checkAllRequired = (attrFlg: boolean, num: number) => {
  fieldList.value.forEach((v) => {
    v.newRequired = checkedAllReq.value;
    if (v.valueMethod === 'xttable' && v.attribution?.children) {
      v.attribution.children.forEach((child) => {
        child.newRequired = checkedAllReq.value;
      });
    }
  });

  const fieldModels: Array<{
    fieldId: string | number;
    required: number;
    attribution: {
      default?: any;
      expendList?: any[];
      children?: any[];
    };
  }> = [];

  fieldList.value.forEach((v) => {
    if (v.checked) {
      let required = 2;
      if (v.newRequired) {
        required = 1;
      }

      if (v.valueMethod === 'xttable' && v.attribution?.children) {
        const children = v.attribution.children.map((child) => ({
          ...child,
          newRequired: checkedAllReq.value
        }));
        fieldModels.push({
          fieldId: v.id,
          required,
          attribution: {
            children
          }
        });
      } else {
        fieldModels.push({
          fieldId: v.id,
          required,
          attribution: {
            default: v.default && v.default !== '' && v.default != null ? v.default : undefined
          }
        });
      }
    }
  });

  const newTaskGroups = [...localTaskGroups.value];
  newTaskGroups[num].fieldModels = fieldModels;
  updateTaskGroups(newTaskGroups);
};

/**
 * 更新方法
 * @param newValue 新值
 */
const updateTaskGroups = (newValue: TaskGroupModel[]) => {
  localTaskGroups.value = JSON.parse(JSON.stringify(newValue));
  emit('update:taskGroupModels', [...localTaskGroups.value]);
};

/**
 * 初始化
 */
onMounted(() => {
  localTaskGroups.value = [...props.taskGroupModels];
});

/**
 * 监听 props 变化
 * @param newVal 新值
 */
watch(
  () => props.taskGroupModels,
  (newVal) => {
    if (newVal) {
      localTaskGroups.value = [...newVal];
    }
  },
  { deep: true }
);

// // Watchers
// watch(
//   () => props.chooseFieldDialog,
//   (val) => {
//     if (val) {
//       getTree();
//     }
//   }
// );

watch(
  () => props.taskGroupModels,
  (val) => {
    if (val) {
    }
  }
);

/**
 * 是否存在扩展列表
 * @param item 字段
 * @returns 是否存在扩展列表
 */
const hasExpendList = (item: FieldModel) => {
  const expendList = item.attribution?.expendList;
  return expendList && expendList.length > 0;
};

const hasOptions = (item: FieldModel) => {
  const options = item.attribution?.options;
  return options && options.length > 0;
};
</script>

<style lang="scss" scoped>
.gray {
  color: rgb(0, 0, 0, 0.2);
}
.mapField-main {
  .dialog-box {
    height: 600px;
    border: 1px solid rgba(219, 231, 238, 1);
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    .left {
      flex: 2;
    }
    .center {
      flex: 2;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .right {
      flex: 5;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .title-div {
      width: 100%;
      height: 37px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      .normal-sapn {
        margin-left: 20px;
      }
    }
    .content-handle {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 10px;
      padding: 0px 25px;
      justify-content: space-between;
    }
    .content {
      height: calc(100% - 77px);
      padding: 0px 8px;
      width: calc(100% - 16px);
      margin-left: 8px;
      overflow: auto;
      :deep(.el-tree-node__content) {
        height: 32px;
        font-size: 12px;
      }
      .empty-span {
        color: #909399;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      .gry-span {
        color: #909399 !important;
      }
      .flex-row {
        min-height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        align-content: center;
        cursor: pointer;
        font-size: 12px;
        height: 40px;
        :deep(.el-checkbox:last-of-type) {
          padding: 5px 0px;
        }

        :deep(.el-checkbox__label) {
          display: inline-grid;
          white-space: pre-line;
          word-wrap: break-word;
          overflow: hidden;
          font-size: 12px;
        }
        .label {
          color: rgba(22, 29, 38, 1);
          font-size: 12px;
          padding-left: 12px;
          flex: 1;
          min-height: 32px;
          line-height: 32px;
        }
        .flex-right {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 40px;
          align-content: center;
          width: 60%;
          .ico {
            min-height: 32px;
            width: 70px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            justify-items: center;
            padding-right: 8px;
            text-align: center;
          }
          .input {
            min-height: 30px;
            min-width: 150px;
            width: 100%;
            flex: 1;
            // 穿透.el-input组件作用域
            :deep(.el-input) {
              .el-input__inner {
                min-width: 150px;
                width: 100%;
                flex: 1;
                height: 30px;

                // 现代浏览器标准写法（覆盖95%场景）
                &::placeholder {
                  font-size: 12px;
                }

                // 兼容性处理（按需保留）
                @supports not (selector(::placeholder)) {
                  // 检测浏览器是否支持标准语法
                  &::-webkit-input-placeholder {
                    /* Chrome/Safari/Edge */
                    font-size: 12px;
                  }
                  &::-moz-placeholder {
                    /* Firefox */
                    font-size: 12px;
                    opacity: 1; // Firefox默认降低透明度需要重置
                  }
                  &::-ms-input-placeholder {
                    /* IE10-11 */
                    font-size: 12px;
                  }
                }
              }

              // 后缀图标（使用嵌套简化）
              .el-input__suffix {
                .el-select__caret {
                  height: line-height(30px); // 使用函数保证单位一致
                }
              }

              // 前缀图标（合并重复属性）
              .el-input__prefix {
                width: 16px;
                height: 30px;
                line-height: 30px;

                .el-input__prefix__icon {
                  @extend .el-input__prefix; // 继承相同属性
                }
              }
            }
            .el-date-editor {
              .el-range-separator {
                height: 30px;
                line-height: 30px;
              }
              .el-input__inner {
                height: 30px;
                :deep(.el-range__icon) {
                  height: 30px;
                  line-height: 30px;
                }
              }
            }
          }
        }
      }
      .flex-row:hover {
        background-color: #f5f7fa;
      }
      .flex-active {
        background: #edf4fb;
      }
      .check-item {
        height: auto;
        min-height: 32px;
        align-items: flex-start;
      }
      .flex-all {
        min-height: 32px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        cursor: pointer;
        font-size: 12px;
        .big-title {
          font-size: 12px;
          margin-left: 10px;
        }
        .flex-row-all {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          align-content: center;
          height: 40px;
          :deep(.el-checkbox) {
            &:last-of-type {
              padding: 5px 0;
            }

            .el-checkbox__label {
              display: inline-grid;
              white-space: pre-line;
              word-wrap: break-word;
              overflow: hidden;
              font-size: 12px; // 合并重复的.label样式
            }
          }
          .label {
            color: rgba(22, 29, 38, 1);
            font-size: 12px;
            padding-left: 12px;
            flex: 1;
            min-height: 32px;
            line-height: 32px;
            width: 40%;
          }
          .flex-right {
            display: flex;
            justify-content: space-between;
            width: 60%;
            .ico {
              min-height: 32px;
              width: 70px;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              justify-items: center;
              padding-right: 8px;
              text-align: center;
            }
            .input {
              min-height: 30px;
              min-width: 150px;
              width: 100%;
              flex: 1;
              :deep(.el-input) {
                // 输入框主体样式
                .el-input__inner {
                  min-width: 150px;
                  width: 100%;
                  flex: 1;
                  height: 30px;

                  // 统一placeholder样式（现代浏览器标准写法）
                  &::placeholder {
                    font-size: 12px;
                  }

                  // 浏览器兼容性处理
                  &::-webkit-input-placeholder {
                    /* Chrome/Safari/Edge */
                    font-size: 12px;
                  }
                  &::-moz-placeholder {
                    /* Firefox */
                    font-size: 12px;
                    opacity: 1; // 修复Firefox默认透明度问题
                  }
                  &::-ms-input-placeholder {
                    /* IE10-11 */
                    font-size: 12px;
                  }
                }

                // 后缀图标区域
                .el-input__suffix {
                  .el-select__caret {
                    height: 30px;
                    line-height: 30px;
                  }
                }

                // 前缀图标区域（使用继承避免重复代码）
                .el-input__prefix {
                  height: 30px;
                  width: 16px;
                  line-height: 30px;

                  .el-input__prefix__icon {
                    @extend .el-input__prefix; // 继承相同属性
                  }
                }
              }
              .el-date-editor {
                .el-range-separator {
                  height: 30px;
                  line-height: 30px;
                }
                .el-input__inner {
                  height: 30px;
                  :deep(.el-range__icon) {
                    height: 30px;
                    line-height: 30px;
                  }
                }
              }
            }
          }
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
    /*滚动条样式*/
    .content::-webkit-scrollbar {
      width: 4px;
    }
    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(255, 255, 255, 0.5);
    }
    .content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
