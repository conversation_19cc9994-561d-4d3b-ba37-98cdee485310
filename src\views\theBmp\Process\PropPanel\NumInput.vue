<template>
  <el-row :gutter="4">
    <el-col :span="isRange ? 24 : 12">
      <el-select v-model="cloneData.matchType" size="small" placeholder="请选择" style="width: 100%" @change="onTypeChange">
        <el-option label="小于" value="lt" />
        <el-option label="小于等于" value="lte" />
        <el-option label="大于" value="gt" />
        <el-option label="大于等于" value="gte" />
        <el-option label="等于" value="eq" />
        <el-option label="介于两数之间" value="bet" />
      </el-select>
    </el-col>
    <el-col :span="isRange ? 24 : 12">
      <el-input-number
        v-if="!isRange"
        size="small"
        style="width: 100%"
        v-model="cloneData.conditionNumValue"
        controls-position="right"
        @change="update"
      />
      <div v-else style="width: 100%; overflow: hidden; margin-top: 10px">
        <div style="border: 1px dashed #ededed; padding: 8px">
          <el-row :gutter="4">
            <el-col :span="18">
              <el-input-number size="small" v-model="cloneData.conditionNumValue[0]" controls-position="right" @change="update" style="width: 100%" />
            </el-col>
            <el-col :span="6">
              <el-select v-model="cloneData.conditionNumValue[1]" size="small" placeholder="请选择" @change="update">
                <el-option label="<" value="lt" />
                <el-option label="≤" value="lte" />
              </el-select>
            </el-col>
          </el-row>
          <el-row :gutter="4">
            <el-col :span="24" class="range-title" :title="title">{{ title }}</el-col>
          </el-row>
          <el-row :gutter="4">
            <el-col :span="6">
              <el-select v-model="cloneData.conditionNumValue[2]" size="small" placeholder="请选择" @change="update">
                <el-option label="<" value="lt" />
                <el-option label="≤" value="lte" />
              </el-select>
            </el-col>
            <el-col :span="18" style="padding-left: 0">
              <el-input-number size="small" v-model="cloneData.conditionNumValue[3]" controls-position="right" @change="update" style="width: 100%" />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineProps, defineEmits } from 'vue';

interface ConditionData {
  type: string;
  paramKey: string;
  paramLabel: string;
  lowerBound: string;
  upperBound: string;
  lowerBoundEqual: string;
  upperBoundEqual: string;
  boundEqual: string;
  valid: string;
  matchType: string;
  conditionNumValue: number | [number, string, string, number] | null;
}

interface Props {
  value: ConditionData;
  title: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update', value: ConditionData): void;
  (e: 'updateNumItem', value: ConditionData): void;
}>();

const defaultData: ConditionData = {
  type: 'condition_range',
  paramKey: '',
  paramLabel: '',
  lowerBound: '',
  upperBound: '',
  lowerBoundEqual: '',
  upperBoundEqual: '',
  boundEqual: '',
  valid: 'valid',
  matchType: 'gt',
  conditionNumValue: null
};

const cloneData = ref<ConditionData>({
  ...defaultData,
  ...JSON.parse(JSON.stringify(props.value || {}))
});

const isRange = computed(() => cloneData.value.matchType === 'bet');

const onTypeChange = (newVal: string) => {
  if (newVal === 'bet') {
    cloneData.value.conditionNumValue = [1, 'lt', 'lt', 2];
  }
  update();
};

const update = () => {
  const list = [
    { type: 'lt', key: 'upperBound' },
    { type: 'lte', key: 'upperBound' },
    { type: 'gt', key: 'lowerBound' },
    { type: 'gte', key: 'lowerBound' },
    { type: 'eq', key: 'boundEqual' }
  ];

  if (cloneData.value.matchType !== 'bet') {
    const item = list.find((it) => it.type === cloneData.value.matchType);
    if (item) {
      cloneData.value[item.key] = cloneData.value.conditionNumValue;
    }
  } else {
    cloneData.value.lowerBound = cloneData.value.conditionNumValue[0];
    cloneData.value.upperBound = cloneData.value.conditionNumValue[3];
  }

  emit('update', cloneData.value);
  emit('updateNumItem', cloneData.value);
};

watch(
  () => props.value,
  (val) => {
  },
  { deep: true }
);

onMounted(() => {
  update();
});
</script>

<style lang="scss" scoped>
.cmp-container {
  line-height: 30px;
  padding: 10px;

  :deep(.el-input--small .el-input__inner) {
    padding-left: 10px;
    padding-right: 15px;
  }

  :deep(.el-input-number.is-controls-right .el-input__inner) {
    padding-left: 15px;
    padding-right: 0;
    text-align: left;
  }

  :deep(.el-input:hover .el-input__inner) {
    border-color: #529eff;
  }
}

.label {
  font-size: 12px;
  padding-right: 16px !important;
}

.range-title {
  font-size: 12px;
  text-align: center;
}

.icon-wrapper {
  :deep(i) {
    cursor: pointer;
    color: #c5c5c5;

    &:hover {
      color: #333;
    }
  }
}

@mixin ellipsis($n) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $n > 1 {
    display: -webkit-box;
    -webkit-line-clamp: $n;
    -webkit-box-orient: vertical;
  } @else {
    white-space: nowrap;
  }
}
</style>
