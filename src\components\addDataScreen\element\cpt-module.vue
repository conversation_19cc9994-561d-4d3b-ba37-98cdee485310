<template>
  <div style="width: 100%; height: 100%" class="module-main" @click="redirect">
    <div class="module-title">{{ moduleName }}</div>
    <div class="flex-row">采集数量：{{ moduleNum }}</div>
    <div class="table-div">
      <dv-scroll-ranking-board :config="config" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();

// ---定义emit---
const emit = defineEmits<{
  (e: 'reload'): void;
  (e: 'changeTaskId', taskId: string): void;
}>();

// ---定义变量
const cptData = ref({
  option: []
});
const uuid = ref('');
const taskId = ref('');
const config = ref({});
const moduleName = ref('');
const moduleNum = ref(0);
const selectStyle = ref();

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (id: string) => {
  const url = window.location.href;
  const list = url.split('/');
  const moduleId = list[list.length - 2];
  if (id != '' || id == 0) {
    taskId.value = id;
  }
  if (props.option.cptDataForm.dataSource == 2) {
    // 该组件为 模块组件，只需要用户绑定对应的模块，然后自动内置模块需要的表达式得到数据
    // 获取模块名称
    const parmas = {
      expression: '$ModuleName',
      moduleId: moduleId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      moduleName.value = res.data;
    });
    // 获取模块采集数量
    const parmas1 = {
      expression: 'getGroupData("cust_name","","count").getCellSum(1)',
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas1.companyId = companyId;
    }
    getDataForFormula(parmas1).then((res) => {
      moduleNum.value = res.data;
    });
    // 获取模块数量列表
    const parmas2 = {
      expression: 'getGroupData("cust_name","","count")',
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas2.companyId = companyId;
    }
    getDataForFormula(parmas2).then((res) => {
      const list = [];
      res.data.listList.forEach((v) => {
        list.push({
          name: v[0],
          value: v[1]
        });
      });
      config.value = JSON.parse(JSON.stringify(props.option.attribute));
      config.value.data = list;
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    cptData.value.option = JSON.parse(props.option.cptDataForm.dataText);
    getDataJson(props.option.cptDataForm).then((res) => {
      config.value = JSON.parse(JSON.stringify(props.option.attribute));
      config.value.data = res;
    });
  }
};
const redirect = () => {
  if (props.option.attribute.url) {
    if (props.option.attribute.url.startsWith('view')) {
      router.push(props.option.attribute.url);
      emit('reload');
    } else {
      window.open(props.option.attribute.url);
    }
  }
};
// 切换任务
const changeTask = () => {
  for (let i = 0; i < cptData.value.option.length; i++) {
    if (cptData.value.option[i].value == taskId.value) {
      document.title = cptData.value.option[i].label + '任务数据大屏';
      break;
    }
  }
  emit('changeTaskId', taskId.value);
};

onMounted(() => {
  selectStyle.value = {
    backgroundColor: props.option.attribute.bgColor,
    color: props.option.attribute.textColor,
    height: props.option.attribute.textLineHeight + 'px',
    border: 'transparent',
    fontSize: props.option.attribute.textSize + 'px',
    fontFamily: props.option.attribute.textFamily,
    textAlign: props.option.attribute.textAlign
  };
  uuid.value = uuidv1();
  refreshCptData();
});

defineOptions({
  name: 'cpt-module'
});
</script>

<style lang="scss" scoped>
select:focus-visible {
  outline: none;
}
option {
  font-size: 14px;
  color: #333;
  text-align: left;
}
.module-main {
  display: flex;
  flex-direction: column;
  position: relative;
  .module-title {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border: #1890ff solid 1px;
    border-radius: 4px;
    position: absolute;
    top: 0;
    width: 100%;
  }
  .flex-row {
    padding: 10px 0px;
    color: #fff;
    position: absolute;
    top: 60px;
  }
  .table-div {
    position: absolute;
    height: calc(100% - 92px);
    top: 92px;
    width: 100%;
  }
}
</style>
