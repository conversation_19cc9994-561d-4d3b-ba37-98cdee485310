<!-- 添加shp或者gdb -->
<template>
  <div class="shpOrGdbDialog-main">
    <el-dialog
      :title="addType === 1 ? '新建SHP' : '新建GDB'"
      v-model="editShpGdbDialogCopy"
      width="875px"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="dialog-content" @click="closeSel(1)">
        <el-form :model="newMsg" :rules="newMsgRules" ref="newMsgRef" class="demo-ruleForm" label-position="top">
          <div class="dialog-row">
            <div class="dialog-item">
              <el-form-item :label="addType === 1 ? 'SHP类型' : 'GDB类型'" prop="type">
                <el-radio-group v-model="newMsg.type" @change="changeType">
                  <el-radio :value="1">点</el-radio>
                  <el-radio :value="2">线</el-radio>
                  <el-radio :value="3">面</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="dialog-item">
              <el-form-item label="设置导出数据" prop="exportData">
                <div class="input-box">
                  <div class="input-div" :class="{ 'input-height': newMsg.exportData.length === 0 }" @click.stop="showSel = !showSel">
                    <i class="end-ico el-icon-arrow-down" :class="{ 'min-height': newMsg.exportData.length === 0 }" v-show="!showSel"></i>
                    <i class="end-ico el-icon-arrow-up" :class="{ 'min-height': newMsg.exportData.length === 0 }" v-show="showSel"></i>
                    <!-- <div class="input-item" v-for="item in newMsg.exportData" :key="item.id">
                  {{item.typeName}}
                  <div class="close" @click.stop="delSelTree(item)">×</div>
                </div> -->
                    <div style="margin-left: 10px">{{ newMsg.exportData.label }}</div>
                    <div class="input-empty" v-show="newMsg.exportData.length === 0">请选择</div>
                  </div>
                  <div class="sel-div">
                    <div class="sel-box" v-show="showSel">
                      <el-tree :data="ysTree" node-key="id" default-expand-all :props="defaultProps" :expand-on-click-node="false">
                        <template #default="{ data }">
                          <div class="tree-row" @click="changeTreeBox(data)" :class="{ 'no-class': data.graphicalType !== newMsg.type }">
                            <div class="tree-left">
                              <!-- <authImg
                          :authSrc="`${baseUrl}${data.iconUrl}?att=1`"
                          :width="'20px'"
                          :height="'20px'"
                          v-show="data.iconUrl"
                          /> -->
                              <div v-if="data.iconUrl && data.iconUrl.substring(data.iconUrl.lastIndexOf('_') + 1) === 'blob'">
                                <el-image
                                  style="width: 20px; height: 20px; margin-right: 8px"
                                  :src="`${baseUrl}${data.iconUrl}?token=${token}`"
                                  :fit="'cover'"
                                />
                              </div>
                              <div v-else style="display: flex">
                                <svg-icon class-name="svg-item" :icon-class="data.iconUrl" />
                              </div>
                              <span style="margin-left: 8px" :class="{ 'no-span': data.graphicalType !== newMsg.type }">{{ data.label }}</span>
                            </div>
                            <div class="tree-right">
                              <!-- <el-checkbox v-model="data.checked" :disabled="data.graphicalType!==newMsg.type" @change="changeTreeBox(data)"></el-checkbox> -->
                            </div>
                          </div>
                        </template>
                      </el-tree>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
            <div class="dialog-item" v-if="showRepeat">
              <el-form-item label="导出是否允许重复">
                <el-radio-group v-model="newMsg.repeat">
                  <el-radio :value="false">不允许重复</el-radio>
                  <el-radio :value="true">允许重复</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="dialog-item">
              <el-form-item :label="addType === 1 ? 'SHP文件名称' : 'GDB文件名称'" prop="name">
                <el-input placeholder="请输入内容" v-model="newMsg.name">
                  <!-- <template slot="append"><span style="cursor: pointer;" @click="chooseMapField(1)"> 选择映射字段&nbsp;&nbsp;<i class="el-icon-arrow-right"></i></span></template> -->
                </el-input>
              </el-form-item>
            </div>
            <div class="dialog-item" v-if="addType === 2">
              <el-form-item label="GDB是否显示图形">
                <el-checkbox :data-a="responsive" v-model="newMsg.displayGraph" @change="changeDisGra">GDB是否显示图形</el-checkbox>
              </el-form-item>
            </div>
            <div class="dialog-item">
              <el-form-item label="驱动字段" v-show="newMsg.exportData.levelNum !== 1">
                <el-input v-model="driveFileName" @click="showDrive" placeholder="请选择驱动字段" readonly></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dialog-row">
            <div class="dialog-one">
              <el-form-item :label="addType === 1 ? 'SHP属性' : 'GDB属性'" prop="fieldGroupModelList">
                <div class="spe-a"><el-link type="primary" @click="showSort">排序</el-link></div>
                <div class="chooseAttr-main">
                  <div class="empty" v-if="list.length === 0">暂无数据</div>
                  <div class="content" v-else>
                    <div class="attr-box">
                      <div class="title">属性组</div>
                      <div class="attr-content">
                        <div
                          class="flex-row check-item"
                          v-for="(item, index) in list"
                          :key="index"
                          @click="chooseAttr(item)"
                          :class="{ 'active': item.checked }"
                        >
                          <div>{{ item.typeName }}</div>
                          <i class="el-icon-arrow-right flex-ico"></i>
                        </div>
                      </div>
                    </div>
                    <div class="fied-box">
                      <div class="title">
                        <div class="item">字段</div>
                        <div class="item">
                          <span style="margin-left: 5px">shp字段名</span>
                        </div>
                        <div class="item">
                          <span style="margin-left: 20px">别名</span>
                        </div>
                        <el-checkbox :indeterminate="isIndeterminate" v-model="checkedAll" style="padding-right: 10px" @change="checkAllField"
                          >全选</el-checkbox
                        >
                      </div>
                      <div class="attr-content">
                        <div v-for="(item, index) in fieldModelList" :key="index">
                          <div class="flex-row check-item">
                            <div class="item" v-if="item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard'">
                              <template v-if="item.attribution.expendList">
                                {{ item.attribution.expendList[0].cnName }}
                                <!-- ({{ item.attribution.expendList[0].enName }}) -->
                              </template>
                              <template v-else> </template>
                            </div>
                            <div class="item" v-else>
                              {{ item.fieldCn }}
                              <!-- ({{ item.fieldName }}) -->
                            </div>
                            <div class="item" style="margin-right: 10px">
                              <el-input
                                v-if="item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard'"
                                v-model="item.attribution.expendList[0].shpName"
                                placeholder="请输入shp字段名"
                                style="width: 100%"
                                size="small"
                                maxlength="10"
                                @change="changeShpName(item, 2)"
                              ></el-input>
                              <el-input
                                v-else
                                v-model="item.attribution.shpName"
                                placeholder="请输入shp字段名"
                                style="width: 100%"
                                size="small"
                                maxlength="10"
                                @change="changeShpName(item, 1)"
                              ></el-input>
                            </div>
                            <div class="item" style="margin-right: 10px">
                              <el-input
                                v-if="item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard'"
                                v-model="item.attribution.expendList[0].anotherName"
                                placeholder="请输入别名"
                                style="width: 100%"
                                size="small"
                                maxlength="10"
                                @change="changeOtherName(item, 2)"
                              ></el-input>
                              <el-input
                                v-else
                                v-model="item.attribution.anotherName"
                                placeholder="请输入别名"
                                style="width: 100%"
                                size="small"
                                maxlength="10"
                                @change="changeOtherName(item, 1)"
                              ></el-input>
                            </div>
                            <el-checkbox
                              v-model="item.checked"
                              @change="chooseField(item)"
                              :disabled="item.fieldName.toUpperCase() === 'ID' || item.fieldName.toUpperCase() === 'PARENTID'"
                            ></el-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 映射字段弹窗 -->
    <mapField
      :mapFieldDialog="mapFieldDialog"
      @closeFieldDialog="closeFieldDialog"
      @submitField="submitField"
      :mapFielType="mapFielType"
      :checked="newMsg.checked"
    ></mapField>
    <!-- 驱动字段弹窗 -->
    <driveCommpont
      :showDriveDialog="showDriveDialog"
      :list="showDriverFieldGroupModelList"
      :driveFieldGroupModelList="newMsg.driveFieldGroupModelList"
      :fieldGroupModelList="newMsg.fieldGroupModelList"
      @closeDriveDialog="closeDriveDialog"
      @submitDriveField="submitDriveField"
    ></driveCommpont>
    <!-- 字段排序 -->
    <sortFile
      :sortDialog="sortDialog"
      :fieldGroupModelList="newMsg.fieldGroupModelList"
      @closeSortDialog="closeSortDialog"
      @submitSortFiled="submitSortFiled"
      :sortMsgProp="sortMsg"
    ></sortFile>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import authImg from '@/components/authImg/index.vue';
import mapField from '../../mapField/index.vue';
import { selectRules } from '@/api/modal';
import driveCommpont from './driveCommpont.vue';
import svgIcon from '../../../../svgIcon/index.vue';
import sortFile from './sortFile.vue';
import { useModalStore } from '@/store/modules/modal';
import { useRouter, useRoute } from 'vue-router';

import type { Action, FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getToken } from '@/utils/auth';
const token = getToken();

const modalStore = useModalStore();
const router = useRouter();
const route = useRoute();
// 定义 props
const props = defineProps<{
  editShpGdbDialog: boolean;
  addTypeProp: number;
  newMsgProp: {
    type?: number;
    exportData?: {
      label?: string;
      length?: number;
      id?: number | null;
      typeName?: string;
      special?: boolean;
      levelNum?: number;
      fieldGroupModelList?: any[];
    };
    repeat?: boolean;
    name?: string;
    displayGraph?: boolean;
    driveFieldGroupModelList?: any[];
    fieldGroupModelList?: any[];
    checked?: any;
  };
}>();

const editShpGdbDialogCopy = computed({
  get() {
    return props.editShpGdbDialog;
  },
  set(value) {
    // 触发关闭事件
    // emit('closeShpDialog');
  }
});

const newMsg = computed(() => {
  return props.newMsgProp;
});

const addType = computed(() => {
  return props.addTypeProp;
});

// 定义 emits
const emit = defineEmits(['closeShpDialog', 'submitShpOrGdb']);

// 定义响应式数据
const showSel = ref(false); // 导出数据下拉
const ysTree = ref<any[]>([]); // 要素树 如宗地->房产->楼层
const defaultProps = {
  children: 'list',
  label: 'typeName'
};
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const mapFieldDialog = ref(false);
const mapFielType = ref(1); // 映射字段类型 1为映射名称 2为映射源(数据 专业图片)
const newMsgRules = computed(() => ({
  type: [{ required: true, message: `请选择${addType.value === 1 ? 'SHP' : 'GDB'}类型`, trigger: 'change' }],
  exportData: [{ required: true, message: '请设置导出数据', trigger: 'change' }],
  name: [{ required: true, message: `请选择导出${addType.value === 1 ? 'SHP' : 'GDB'}文件名称`, trigger: 'change' }]
  // fieldGroupModelList:[
  //   { required: true, message: `请设置${props.addType===1?'SHP':'GDB'}属性`, trigger: 'change' },
  // ]
}));
const list = ref<any[]>([]); // 属性组列表
const fieldModelList = ref<any[]>([]); // 属性列表
const showDriveDialog = ref(false); // 驱动字段弹窗
const showDriverFieldGroupModelList = ref<any[]>([]); // 驱动字段列表 选择的是父级节点的普通属性组
const oldFieldName = ref(''); // 当前选中的原始身份证识别英文字段名
const responsive = ref(true);
const driveNode = ref({}); // 驱动数据对应的树节点
const driveFileName = ref('');
const checkedAll = ref(false); // 全选字段
const isIndeterminate = ref(false); // 半选
const repeat = ref(true); // 是否允许重复
const showRepeat = ref(false); // 是否展示是否重复选项
const sortDialog = ref(false); // 排序弹窗
const sortMsg = ref({
  fiedList: []
}); // 给字段排序用的对象 里面包含属性组和字段列表
const newMsgRef = ref<FormInstance>(null);

// 计算 moduleId
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = Number(router.currentRoute.value.query.id);
  }
  return moduleId;
});

// 监听 fieldModelList 变化
watch(
  fieldModelList,
  (val) => {
    let checkedNum = 0;
    val.forEach((v) => {
      if (v.checked) {
        checkedNum++;
      }
    });
    if (checkedNum > 0 && checkedNum < val.length) {
      isIndeterminate.value = true;
      checkedAll.value = false;
    } else if (checkedNum === 0) {
      isIndeterminate.value = false;
      checkedAll.value = false;
    } else if (checkedNum === val.length) {
      isIndeterminate.value = false;
      checkedAll.value = true;
    }
  },
  { deep: true }
);

// 监听 editShpGdbDialog 变化
watch(
  () => props.editShpGdbDialog,
  (val) => {
    if (val) {
      list.value = []; // 属性组列表
      fieldModelList.value = []; // 属性列表
      if (props.newMsgProp.driveFieldGroupModelList) {
        let str = '';
        if (props.newMsgProp.driveFieldGroupModelList && props.newMsgProp.driveFieldGroupModelList.length !== 0) {
          str = `${props.newMsgProp.driveFieldGroupModelList[0].typeName}->${props.newMsgProp.driveFieldGroupModelList[0].fieldModelList[0].fieldCn}`;
        }
        driveFileName.value = str;
      }
      const id = props.newMsgProp.exportData?.id || null;
      getTree(props.newMsgProp.exportData?.typeName, props.newMsgProp.exportData?.label);
    }
  },
  { deep: true }
);

// 定义方法
const showSort = () => {
  // 整理排序字段以及属性组
  const groupList: any[] = [];
  const fiedList: any[] = [];
  const driveGroup: any[] = [];
  let index = 1; // 排序用
  props.newMsgProp.fieldGroupModelList?.forEach((v) => {
    groupList.push(v);
    v.fieldModelList?.forEach((k) => {
      if (k.fieldSort < index) {
        // 代表是新的
        index = index + 1;
        k.fieldSort = index;
      } else {
        index = k.fieldSort;
      }
      fiedList.push(k);
    });
  });
  // 驱动字段
  props.newMsgProp.driveFieldGroupModelList?.forEach((v) => {
    if (!groupList.find((item) => item.id === v.id)) {
      driveGroup.push(v);
      v.fieldModelList?.forEach((k) => {
        k.isDrive = true;
        fiedList.push(k);
      });
    }
  });
  // 根据fieldSort排序
  fiedList.sort((a, b) => a.fieldSort - b.fieldSort);
  sortMsg.value = {
    groupList: groupList,
    fiedList: fiedList,
    driveGroup: driveGroup
  };

  if (fiedList.length === 0) {
    ElMessage.error('您未选择字段，请先选择字段！！！');
    return;
  }
  sortDialog.value = true;
};

// 获取要素树
const getTree = async (typeName?: string, label?: string) => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  selectRules(params).then((res) => {
    if (res.code === 200) {
      ysTree.value = res.data;
      initYStree(ysTree.value);
      if (typeName && label) {
        // 代表是修改 这个时候需要把选中的要素初始化最新的 并且把选中的属性反显

        initChooseYS(ysTree.value, typeName, label);
        // 整理选择的属性组以及属性组下的字段，因为可能数据字段或者属性组已经删除，需要实时在查询树
        if (props.newMsgProp.exportData) {
          props.newMsgProp.fieldGroupModelList = initAttrGroup(res.data, props.newMsgProp.exportData.typeName, props.newMsgProp.fieldGroupModelList);
        }
      }
      emit('closeShpDialog', true);
    } else {
      (window as any).$message.error(res.msg);
    }
  });
};

// 整理选择的属性组以及字段，把已删除的给删除掉
const initAttrGroup = (tree: any[], typeName: string | undefined, fieldGroupModelList: any[] | undefined) => {
  if (!typeName || !fieldGroupModelList) return;
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].typeName === typeName) {
      // 找到匹配的节点
      // 通过循环找到对应的属性组，然后在用树的属性组跟需要反显的属性组里面的字段进行去重匹配
      const attrGroup: any[] = [];
      tree[i].fieldGroupModelList?.forEach((v: any, vdx: number) => {
        const flg = fieldGroupModelList.some((obj) => obj.typeName === v.typeName);
        let itemGroup: any = {};
        if (flg) {
          let fieldModelList: any[] = [];
          for (let i = 0; i < fieldGroupModelList.length; i++) {
            // 拿到反显属性组对应的字段列表
            if (fieldGroupModelList[i].typeName === v.typeName) {
              fieldModelList = fieldGroupModelList[i].fieldModelList;
              itemGroup = fieldGroupModelList[i];
              break;
            }
          }
          fieldModelList.forEach((k: any, kdx: number) => {
            const flg = v.fieldModelList.some((obj: any) => obj.fieldName === k.fieldName);
            if (!flg) {
              // 没找到就需要删除
              fieldModelList.splice(kdx, 1);
            }
          });
          itemGroup.fieldModelList = fieldModelList;
          attrGroup.push(itemGroup);
        }
      });
      return attrGroup;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      // 递归查找子节点
      const result = initAttrGroup(tree[i].list, typeName, fieldGroupModelList);
      if (result) {
        // 如果在子节点中找到，返回结果
        return result;
      }
    }
  }
  // 如果没有找到匹配的节点，返回 null 或其他默认值
  return;
};

const initChooseYS = (ysList: any[], typeName: string, label: string) => {
  for (let index = 0; index < ysList.length; index++) {
    if (ysList[index].typeName === typeName && ysList[index].label === label) {
      // id存在的时候代表选择了某个要素 把要素重新赋值一下
      // props.newMsgProp.exportData = ysList[index]
      list.value = [];
      if (ysList[index].fieldGroupModelList) {
        ysList[index].fieldGroupModelList.forEach((v: any) => {
          if (props.newMsgProp.exportData?.special) {
            // 子要素
            showRepeat.value = true;
            list.value.push(v);
          } else {
            if (!v.ruleAttribution || (v.ruleAttribution && v.ruleAttribution.type === 'graphicalArea')) {
              list.value.push(v);
            }
          }
        });
        if (list.value.length !== 0) {
          chooseAttr(list.value[0]);
        }
      }
      break;
    } else if (ysList[index].list) {
      initChooseYS(ysList[index].list, typeName, label);
    }
  }
};

// 根据获取的树 把子要素整理进去
const initYStree = (list: any[], id: any) => {
  list.forEach((v) => {
    v.label = v.typeName;
    if (v.fieldGroupModelList.length != 0) {
      //代表可能该要素下有子要素
      v.fieldGroupModelList.forEach((k) => {
        if (k.ruleAttribution && (k.ruleAttribution.type == 'graphicalLine' || k.ruleAttribution.type == 'commonLine')) {
          const node = {
            typeName: v.typeName,
            label: `${k.typeName}(线)`,
            id: v.id,
            graphicalType: 2, //子要素线
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true, //避免死循环
            parentId: v.id,
            isChild: true //标识为子要素
          };
          v.list.unshift(node);
        } else if (k.ruleAttribution && (k.ruleAttribution.type == 'graphicalPoint' || k.ruleAttribution.type == 'commonPoint')) {
          const node = {
            typeName: v.typeName,
            label: `${k.typeName}(点)`,
            id: v.id,
            graphicalType: 1, //子要素点
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true,
            parentId: v.id,
            isChild: true //标识为子要素
          };
          v.list.unshift(node);
        } else if (k.ruleAttribution && k.ruleAttribution.type == 'graphicalArea') {
          //面
          const node = {
            typeName: `${v.typeName}`,
            label: `${k.typeName}(面)`,
            id: v.id,
            graphicalType: 3, //子要素点
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true,
            parentId: v.id,
            isChild: true //标识为子要素
          };
          v.list.unshift(node);
        }
      });
    }
    if (v.list.length != 0) {
      const itemList = [];
      v.list.forEach((k) => {
        if (!k.special) {
          //不是子要素
          itemList.push(k);
        }
      });
      if (itemList.length != 0) {
        initYStree(itemList);
      }
    }
  });
};

const changeType = (val) => {
  newMsg.value.exportData = {};
  newMsg.value.fieldGroupModelList = [];
  list.value = [];
  fieldModelList.value = [];
  showSel.value = false;
};

const closeSel = (param: number) => {
  showSel.value = false;
};

const changeTreeBox = (obj: any) => {
  if (obj.isChild) {
    //子节点可以设置导出是否重复 用于解决共用一条界址线界址点问题
    showRepeat.value = true;
  } else {
    showRepeat.value = false;
  }
  if (obj.graphicalType == newMsg.value.type) {
    newMsg.value.exportData = JSON.parse(JSON.stringify(obj));
    const ite_list = [];
    obj.fieldGroupModelList.forEach((v) => {
      if (obj.special) {
        //子要素
        ite_list.push(v);
      } else {
        if (!v.ruleAttribution || (v.ruleAttribution && v.ruleAttribution.type == 'graphicalArea')) {
          ite_list.push(v);
        }
      }
    });
    list.value = ite_list;
    if (list.value.length != 0) {
      chooseAttr(list.value[0]);
    }
    showSel.value = false;
  }
};

const chooseAttr = (item: any) => {
  list.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  fieldModelList.value = [];
  // this.fieldModelList = item.fieldModelList
  item.fieldModelList.forEach((v: any) => {
    if (v.valueMethod == 'idCardScan' || v.valueMethod === 'xtBankCard') {
      //身份证识别
      if (v.attribution.expendList) {
        //新版
        v.attribution.expendList.forEach((k) => {
          const item_obj = JSON.parse(JSON.stringify(v));
          if (!k.anotherName) {
            k.anotherName = k.enName;
          }
          if (!k.shpName) {
            k.shpName = k.enName;
          }
          item_obj.attribution.expendList = [k];
          fieldModelList.value.push(item_obj);
        });
      } else {
        //老版
        v.attribution.list.forEach((k) => {
          const item_obj = JSON.parse(JSON.stringify(v));
          item_obj.attribution.list = [k];
          fieldModelList.value.push(item_obj);
        });
      }
    } else {
      if (!v.attribution.anotherName) {
        v.attribution.anotherName = v.fieldName;
      }
      if (!v.attribution.shpName) {
        v.attribution.shpName = v.fieldName;
      }
      fieldModelList.value.push(v);
    }
  });
  fieldModelList.value.forEach((v) => {
    v.checked = false;
  });
  let groupFlag = false; //是否存在反显可能
  let item_fieldModelList = []; //需要反显的字段列表
  // 找到已经选择的属性组对应的字段列表
  newMsg.value.fieldGroupModelList.forEach((v) => {
    if (v.typeName == item.typeName) {
      //找到了
      groupFlag = true;
      item_fieldModelList = v.fieldModelList;
    }
  });
  // 找到对于属性组才反显
  if (groupFlag) {
    fieldModelList.value.forEach((v) => {
      for (let i = 0; i < item_fieldModelList.length; i++) {
        if (v.fieldName == item_fieldModelList[i].fieldName) {
          if (v.valueMethod == 'idCardScan' || v.valueMethod === 'xtBankCard') {
            //身份证识别
            if (v.attribution.expendList) {
              //新版身份证识别
              if (v.attribution.expendList[0].enName == item_fieldModelList[i].attribution.expendList[0].enName) {
                v.checked = true;
                v.attribution.expendList[0].anotherName = item_fieldModelList[i].attribution.expendList[0].anotherName;
                v.attribution.expendList[0].shpName = item_fieldModelList[i].attribution.expendList[0].shpName;
                break;
              }
            }
          } else {
            v.attribution.anotherName = item_fieldModelList[i].attribution.anotherName;
            v.attribution.shpName = item_fieldModelList[i].attribution.shpName;
            v.checked = true;
            break;
          }
        }
      }
    });
  }
};

const chooseField = (item: any) => {
  if (item.checked) {
    //选中的时候
    let enName = '';
    if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
      enName = item.attribution.expendList[0].enName;
    } else {
      enName = item.fieldName;
    }
    // 先判断其他已选择的属性组的字段是否包含了该选项的item.fieldName
    let fieldFlg = false; //字段是否在其他属性组找到
    for (let index = 0; index < newMsg.value.fieldGroupModelList.length; index++) {
      for (let idx = 0; idx < newMsg.value.fieldGroupModelList[index].fieldModelList.length; idx++) {
        if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
          //身份证特殊判断
          if (
            newMsg.value.fieldGroupModelList[index].fieldModelList[idx].fieldName == item.fieldName &&
            newMsg.value.fieldGroupModelList[index].fieldModelList[idx].attribution.expendList[0].enName == enName
          ) {
            fieldFlg = true;
            break;
          }
        } else {
          if (newMsg.value.fieldGroupModelList[index].fieldModelList[idx].fieldName == enName) {
            fieldFlg = true;
            break;
          }
        }
      }
    }
    if (fieldFlg) {
      ElMessage.error('字段名不允许重复！！！');
      item.checked = false;
      return;
    }
    let groupFlag = false; //组是否添加了
    let item_fieldGroupMode = {
      fieldModelList: [],
      ruleAttribution: null
    }; //找到的组
    for (let i = 0; i < newMsg.value.fieldGroupModelList.length; i++) {
      if (newMsg.value.fieldGroupModelList[i].typeName == item.groupName) {
        //找到了
        groupFlag = true;
        item_fieldGroupMode = newMsg.value.fieldGroupModelList[i];
        break;
      }
    }
    if (groupFlag) {
      //组存在的时候 判断字段是否存在，然后在组里面添加字段
      let fieldFlag = false; //字段是否存在
      // item_fieldGroupMode.fieldModelList.forEach(v=>{
      //   if (v.id == item.id) {
      //     fieldFlag = true
      //   }
      // })
      let enName = '';
      if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
        enName = item.attribution.expendList[0].enName;
      }
      for (let i = 0; i < item_fieldGroupMode.fieldModelList.length; i++) {
        if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
          //身份证特殊判断
          if (
            item_fieldGroupMode.fieldModelList[i].attribution.expendList &&
            item_fieldGroupMode.fieldModelList[i].attribution.expendList[0].enName == enName
          ) {
            fieldFlag = true;
            break;
          }
        } else {
          if (item_fieldGroupMode.fieldModelList[i].id == item.id) {
            fieldFlag = true;
            break;
          }
        }
      }
      if (!fieldFlag) {
        //不存在就添加进去
        item_fieldGroupMode.fieldModelList.push(item);
      }
    } else {
      //属性组不存在的时候 先把组加进去然后把字段加到组里面
      list.value.forEach((v) => {
        if (v.id == item.groupId) {
          //找到了
          item_fieldGroupMode = JSON.parse(JSON.stringify(v)); //深度拷贝 避免改变字段的时候把源数据变了}
          if (!v.ruleAttribution) {
            item_fieldGroupMode.ruleAttribution = null;
          }
        }
      });
      item_fieldGroupMode.fieldModelList = []; //初次加属性组需要把字段列表初始化
      item_fieldGroupMode.fieldModelList.push(item);
      // 添加进去
      newMsg.value.fieldGroupModelList.push(item_fieldGroupMode);
    }
  } else {
    //取消的时候
    let item_fieldGroupMode = {
      fieldModelList: []
    }; //找到的组
    let fieldGroupModeIndex = 0; //组对应的下标
    let fieldModelListIndex = 0; //字段对应的下标
    newMsg.value.fieldGroupModelList.forEach((v, vdx) => {
      if (v.typeName == item.groupName) {
        //找到了
        fieldGroupModeIndex = vdx;
        item_fieldGroupMode = v;
      }
    });
    if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
      for (let i = 0; i < item_fieldGroupMode.fieldModelList.length; i++) {
        if (item_fieldGroupMode.fieldModelList[i].attribution.expendList[0].enName == item.attribution.expendList[0].enName) {
          item_fieldGroupMode.fieldModelList.splice(i, 1);
          if (item_fieldGroupMode.fieldModelList.length == 0) {
            //如果属性组下面字段都没有了 直接把属性组都删除
            newMsg.value.fieldGroupModelList.splice(fieldGroupModeIndex, 1);
          }
          break;
        }
      }
    } else {
      item_fieldGroupMode.fieldModelList.forEach((v, vdx) => {
        //找到对应字段下标
        if (v.id == item.id) {
          fieldModelListIndex = vdx;
        }
      });
      item_fieldGroupMode.fieldModelList.splice(fieldModelListIndex, 1); //删除属性组下的字段
      if (item_fieldGroupMode.fieldModelList.length == 0) {
        //如果属性组下面字段都没有了 直接把属性组都删除
        newMsg.value.fieldGroupModelList.splice(fieldGroupModeIndex, 1);
      }
    }
  }
};

const handleClose = () => {
  newMsgRef.value.clearValidate();
  emit('closeShpDialog');
};
// 提交新建shp或者gdb
const submit = async () => {
  // 手动触发验证gdb或者shp显示错误信息
  if (addType.value == 1) {
    //shp
    newMsgRules.value.name[0].message = '请选择导出SHP文件名称';
    // this.newMsgRules.fieldGroupModelList[0].message = '请选择导出SHP属性'
  } else {
    //gbd
    newMsgRules.value.name[0].message = '请选择导出GDB文件名称';
    // this.newMsgRules.fieldGroupModelList[0].message = '请选择导出GDB属性'
  }
  if (newMsg.value.driveFieldGroupModelList.length != 0) {
    //代表选了驱动字段
    const driveFiled = newMsg.value.driveFieldGroupModelList[0].fieldModelList[0].fieldName;
    const verifyField = []; //shp、gdb已经选择的属性的英文名 避免驱动字段的英文名跟已选择的重复
    newMsg.value.fieldGroupModelList.forEach((v) => {
      v.fieldModelList.forEach((k) => {
        verifyField.push(k.fieldName);
      });
    });
    if (verifyField.includes(driveFiled)) {
      ElMessage.error('选择的驱动字段名字跟已选择的字段重复!!!!');
      return;
    }
  }
  await newMsgRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submitShpOrGdb', newMsg.value);
    } else {
    }
  });
};
// 关闭字段映射弹窗
const closeFieldDialog = () => {
  mapFieldDialog.value = false;
};
// 子组件提交过来的映射字段
const submitField = (list: any, fieldCn: string) => {
  const checked = {
    checkedFiledList: [],
    checkedFiledSouseList: [],
    souse: ''
  };
  checked.checkedFiledList = list;
  newMsg.value.checked = checked;
  newMsg.value.name = '';
  list.value.forEach((v: any) => {
    newMsg.value.name = newMsg.value.name + `${v.allWorkName}`;
  });
  mapFieldDialog.value = false;
};

const showDrive = async () => {
  // 驱动字段默认都找规则树第一级的普通属性组
  const parentId = newMsg.value.exportData.parentId; //通过parentId找到当前要素的上一级
  driveNode.value = {};
  await getPrentNode(ysTree.value, parentId);
  showDriverFieldGroupModelList.value = [];
  driveNode.value.fieldGroupModelList.forEach((v: any) => {
    if (v.linkType == 1) {
      //普通属性组
      showDriverFieldGroupModelList.value.push(v);
    }
  });
  showDriveDialog.value = true;
};

const getPrentNode = async (list: any[], parentId: any) => {
  for (let index = 0; index < list.length; index++) {
    if (list[index].id == parentId) {
      driveNode.value = JSON.parse(JSON.stringify(list[index]));
      break;
    } else if (list[index].id != parentId && list[index].list.length != 0) {
      getPrentNode(list[index].list, parentId);
    }
  }
};

const closeDriveDialog = () => {
  showDriveDialog.value = false;
};

const submitDriveField = (obj) => {
  newMsg.value.driveFieldGroupModelList = [];
  if (obj.id) {
    //有值才加
    newMsg.value.driveFieldGroupModelList.push(obj);
    let str = '';
    if (newMsg.value.driveFieldGroupModelList && newMsg.value.driveFieldGroupModelList.length != 0) {
      str = `${newMsg.value.driveFieldGroupModelList[0].typeName}->${newMsg.value.driveFieldGroupModelList[0].fieldModelList[0].fieldCn}`;
    }
    driveFileName.value = str;
  }
  showDriveDialog.value = false;
};

const closeSortDialog = () => {
  sortDialog.value = false;
};

const submitSortFiled = (parmas) => {
  newMsg.value.driveFieldGroupModelList = parmas.driveFieldGroupModelList;
  newMsg.value.fieldGroupModelList = parmas.fieldGroupModelList;
  sortDialog.value = false;
};
// gdb是否显示图形 改变
const changeDisGra = (val) => {
  // emit('changeDisplayGraph', val);
  responsive.value = !responsive.value; //数据驱动vue视图更新，解决checkbox视图不更新的问题
};
// 全选所有字段
const checkAllField = (val: boolean) => {
  // 先去判断该属性组是否已经添加
  let checkedAttrName = null;
  let checkedAttrMsg = {};
  for (let i = 0; i < list.value.length; i++) {
    if (list.value[i].checked) {
      checkedAttrName = list.value[i].typeName;
      checkedAttrMsg = JSON.parse(JSON.stringify(list.value[i])); //深度拷贝避免改变原数组
      break;
    }
  }
  let groupFlag = false; //组是否添加了
  let fieldGroupMode = {}; //找到的组
  newMsg.value.fieldGroupModelList.forEach((v) => {
    if (v.typeName == checkedAttrName) {
      //找到了
      groupFlag = true;
      fieldGroupMode = v;
    }
  });

  if (val) {
    // 再其他属性组里面存在 即重复的
    const errorList: any[] = [];
    // 可以添加的字段
    const okList: any[] = [];
    newMsg.value.fieldGroupModelList.forEach((v) => {
      if (v.id != fieldGroupMode.id) {
        //排除自己
        v.fieldModelList.forEach((k) => {
          if (fieldModelList.value.some((item) => item.fieldName == k.fieldName)) {
            errorList.push(k);
          }
        });
      }
    });
    fieldModelList.value.forEach((v) => {
      if (!errorList.some((item) => item.fieldName == v.fieldName)) {
        okList.push(v);
      }
    });
    // 先进行是否选中处理
    fieldModelList.value.forEach((v) => {
      if (okList.some((item) => item.fieldName == v.fieldName)) {
        v.checked = true;
      }
    });
    if (groupFlag) {
      //组存在的时候 直接给组里面添加可以选中的字段
      // 然后把所有允许添加的字段添加进入属性组
      fieldGroupMode.fieldModelList = okList;
    } else {
      //组不存在的时候需要先构建组， 然后把可以添加的字段追加进去
      checkedAttrMsg.fieldModelList = okList;
      newMsg.value.fieldGroupModelList.push(checkedAttrMsg);
    }
    if (errorList.length != 0) {
      //有不允许勾选的需要提示出来
      const errorNames: any[] = [];
      errorList.forEach((v) => {
        errorNames.push(v.fieldCn);
      });
      ElMessageBox.alert(`以下字段已存在，不允许添加【${errorNames.join(',')}】`, '错误提示', {
        confirmButtonText: '确定',
        callback: (action: Action) => {}
      });
    }
  } else {
    //取消该数组下的所有字段
    //首先取消所有字段选中
    fieldModelList.value.forEach((v) => {
      v.checked = false;
    });
    // 直接去this.newMsg.fieldGroupModelList找到该属性组，然后把该组都删除
    newMsg.value.fieldGroupModelList.forEach((v, vdx) => {
      if (v.typeName == checkedAttrName) {
        newMsg.value.fieldGroupModelList.splice(vdx, 1);
      }
    });
  }
};

/**
 * 修改shp名称后需要同步数据
 * @param val 修改的对象
 * @param type 1:普通字段 2:身份证识别或银行卡字段
 */
const changeShpName = (val: any, type: number) => {
  if (val.checked) {
    //选中的时候 才执行
    for (let i = 0; i < newMsg.value.fieldGroupModelList.length; i++) {
      if (newMsg.value.fieldGroupModelList[i].id === val.groupId) {
        for (let j = 0; j < newMsg.value.fieldGroupModelList[i].fieldModelList.length; j++) {
          if (type === 1) {
            //普通字段
            if (newMsg.value.fieldGroupModelList[i].fieldModelList[j].fieldName === val.fieldName) {
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.shpName = val.attribution.shpName;
              break;
            }
          } else {
            //身份证识别或银行卡字段
            if (
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].fieldName === val.fieldName &&
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].enName === val.attribution.expendList[0].enName
            ) {
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].shpName = val.attribution.expendList[0].shpName;
            }
          }
        }
        break;
      }
    }
  }
};

/**
 * 修改字段别名后需要同步数据
 * @param val 修改的对象
 * @param type 1:普通字段 2:身份证识别或银行卡字段
 */
const changeOtherName = (val: any, type: number) => {
  if (val.checked) {
    //选中的时候 才执行
    for (let i = 0; i < newMsg.value.fieldGroupModelList.length; i++) {
      if (newMsg.value.fieldGroupModelList[i].id === val.groupId) {
        for (let j = 0; j < newMsg.value.fieldGroupModelList[i].fieldModelList.length; j++) {
          if (type === 1) {
            //普通字段
            if (newMsg.value.fieldGroupModelList[i].fieldModelList[j].fieldName === val.fieldName) {
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.anotherName = val.attribution.anotherName;
              break;
            }
          } else {
            //身份证识别或银行卡字段
            if (
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].fieldName === val.fieldName &&
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].enName === val.attribution.expendList[0].enName
            ) {
              newMsg.value.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].anotherName = val.attribution.expendList[0].anotherName;
            }
          }
        }
        break;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.svg-item {
  width: 16px !important;
  height: 16px !important;
  fill: currentColor;
  overflow: hidden;
}
.shpOrGdbDialog-main {
}
.title {
  height: 36px;
  padding-left: 12px;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.attr-content {
  height: calc(100% - 36px);
  overflow: auto;
  .flex-row {
    height: 32px;
    display: flex;
    padding: 0px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-size: 12px;
    .flex-ico {
      margin-top: 12px;
      width: 14px;
      height: 14px;
    }
    .item {
      flex: 1;
    }
  }
  .check-item {
    height: auto;
    align-items: flex-start;
  }
  .active {
    background: #edf4fb;
    color: var(--current-color);
  }
  .flex-row:hover {
    background-color: #f5f7fa;
  }
  .flex-row-spe {
    height: auto;
    .spe-title {
      color: #d3d3d3;
      padding-left: 12px;
      cursor: not-allowed;
    }
    .spe-item {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      font-size: 12px;
      padding-left: 24px;
    }
    .spe-item:hover {
      background: #edf4fb;
    }
    .spe-item-active {
      background: #edf4fb;
    }
  }
}
.attr-content ::-webkit-scrollbar {
  width: 1px;
}
/*滚动条样式*/
.attr-content::-webkit-scrollbar {
  width: 4px;
}
.attr-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgb(255, 255, 255, 0.5);
}
.attr-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.spe-a {
  position: absolute;
  top: -36px;
  left: 80px;
}
.chooseAttr-main {
  display: flex;
  flex-direction: row;
  width: 100%;
  .empty {
    color: #8291a9;
  }
  .content {
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    width: 100%;
    height: 280px;
    display: flex;
    .attr-box {
      width: 220px;
      height: 100%;
      border-right: #dcdfe6 solid 1px;
    }
    .fied-box {
      flex: 2;
      height: 100%;
      .item {
        flex: 1;
      }
    }
  }
}
.no-class {
  cursor: no-drop;
}
.no-span {
  color: #d3d3d3;
}
.tree-row {
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .tree-left {
    display: flex;
    align-items: center;
    .tree-ico {
      width: 20px;
      height: 20px;
    }
  }
  .tree-right {
    margin-right: 16px;
  }
}
.dialog-content {
  max-height: 600px;
  overflow: auto;
}
.dialog-content ::-webkit-scrollbar {
  width: 1px;
}
/*滚动条样式*/
.dialog-content::-webkit-scrollbar {
  width: 4px;
}
.dialog-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgb(255, 255, 255, 0.5);
}
.dialog-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.dialog-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; // 元素间隔
  .dialog-one {
    width: 100%;
  }
  .dialog-item {
    width: calc(33.333% - 10px * 2 / 3); // 计算宽度(总宽度减去间隔空间)
    margin: 0;
    .input-box {
      position: relative;
      width: 100%;
      .input-height {
        padding: 0px 12px 8px 12px;
      }
      .input-div {
        min-height: 30px;
        display: flex;
        flex-direction: row;
        align-items: center;
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        flex-wrap: wrap;
        position: relative;
        cursor: pointer;
        .end-ico {
          position: absolute;
          right: 5px;
          width: 16px;
          height: 16px;
          cursor: pointer;
        }
        .min-height {
          top: 12px;
        }
        .input-empty {
          color: #8291a9;
          font-size: 14px;
          margin-left: 12px;
        }
        .input-normal {
          color: #101010;
          font-size: 14px;
          margin-left: 12px;
        }
        .input-item {
          background: #edf4fb;
          font-size: 14px;
          color: #161d26;
          cursor: pointer;
          height: 28px;
          border-radius: 4px;
          padding: 0px 25px 0px 8px;
          white-space: nowrap;
          display: block;
          position: relative;
          margin-right: 8px;
          margin-top: 5px;
          line-height: 28px;
          .close {
            background-color: rgba(130, 145, 169, 1);
            width: 14px;
            height: 14px;
            border-radius: 50%;
            color: #fff;
            margin-left: 4px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 8px;
            right: 8px;
          }
        }
      }
      .sel-div {
        margin-top: 5px;
        position: relative;
        .sel-box {
          position: absolute;
          top: 0px;
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.16);
          border-radius: 8px;
          width: 100%;
          height: auto;
          z-index: 2;
          background: #fff;
          padding: 10px 0px;
          height: 300px;
          overflow: auto;
        }
      }
    }
  }
}
</style>
