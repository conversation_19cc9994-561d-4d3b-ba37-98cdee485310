<!-- 雷达图 -->
<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-radar'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData = ref();
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.map) {
          const names = Object.keys(res.data.map);
          const list = [];
          names.forEach((v, idx) => {
            list.push({
              name: v,
              value: res.data.map[v]
            });
          });
          cptData.value = list;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};
const loadChart = (attribute) => {
  chartOption.value = {
    title: {
      text: attribute.titleText,
      textStyle: {
        color: attribute.titleColor
      }
    },
    legend: {
      data: cptData.value.legendData,
      textStyle: {
        color: '#fff'
      },
      bottom: '0'
    },
    radar: {
      // shape: 'circle',
      indicator: attribute.indicator
    },
    series: [
      {
        name: 'Budget vs spending',
        type: 'radar',
        label: {
          show: true,
          color: '#fff',
          position: 'top'
        },
        data: cptData.value.seriesData
      }
    ]
  };
  chart?.setOption(chartOption.value);
};
// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>
<style lang="scss" scoped></style>
