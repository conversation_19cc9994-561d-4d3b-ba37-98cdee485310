<!-- 审批的数据 内容 -->
<template>
  <div class="instance-list-mian">
    <div class="handle-div">
      <div class="item">
        <el-button v-if="showType === 'edit'" type="primary" size="small" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加流程
        </el-button>
      </div>
    </div>
    <div class="list-main" v-if="tableData.length > 0">
      <instance-item class="item-main" v-for="item in tableData" :key="item.processId" :currentItem="item" :showType="showType"></instance-item>
    </div>
    <el-empty v-else>
      <template v-slot:description>
        <div style="font-size: 14px; font-style: italic; color: #8291a9">
          <span>暂无数据,</span>
          <span style="color: #0081ff; cursor: pointer" @click="handleAdd">去添加</span>
        </div>
      </template>
    </el-empty>
    <el-pagination
      v-model:current-page="search.page"
      v-model:page-size="search.size"
      :page-sizes="[10, 50, 100, 200]"
      :layout="'total,sizes, prev, pager, next'"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="page-div"
      v-show="total > 0"
    />
    <!-- 查看实例详情 -->
    <!-- <instance-detial
      :isShowInstanceDetial="isShowInstanceDetial"
      :instanceId="processInstanceId"
      @closeInstance="handleCloseInstance"
    ></instance-detial> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getPdefinitionList } from '@/api/process';
// import InstanceDetial from '../components/instanceDetial.vue';
import InstanceItem from './instanceItem.vue';

interface SearchParams {
  page: number;
  size: number;
}

interface TableItem {
  processId: string | number;
  [key: string]: any;
}

// Props
const props = defineProps({
  showType: {
    type: String,
    default: 'apply'
  }
});

// 路由
const router = useRouter();

// 数据
const tableData = ref<TableItem[]>([]);
const search = ref<SearchParams>({
  page: 1,
  size: 50
});
const total = ref(0);
const processInstanceId = ref<string | undefined>(undefined);

/**
 * 获取列表数据
 */
const handleProcessList = async () => {
  try {
    const res = await getPdefinitionList(search.value);

    if (res.code === 200) {
      tableData.value = res.data.list;
      total.value = res.data.total;
      search.value.page = res.data.pageNum;
      search.value.size = res.data.pageSize;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    ElMessage.error('获取列表失败');
  }
};

/**
 * 根据状态值，获取节点类型颜色
 * @param type 状态值
 */
const getType = (type: number) => {
  if (type === 10) {
    return { color: '#111112' };
  } else if (type === 100) {
    return { color: '#1CCB12' };
  } else if (type === 0) {
    return { color: '#0079FF' };
  } else if (type === 40) {
    return { color: '#FF9219' };
  } else if (type === 50) {
    return { color: '#Ff3434' };
  } else if (type === 6) {
    return { color: '#5ACFF0' };
  } else {
    return { color: '#F07B5A' };
  }
};

/**
 * 添加流程
 */
const handleAdd = () => {
  router.push({
    path: `/addBmp`,
    query: { id: '0' }
  });
};

/**
 * 分页处理
 * @param size 当前页数的长度
 */
const handleSizeChange = (size: number) => {
  search.value.size = size;
  handleProcessList();
};
/**
 * 分页处理
 * @param page 当前页
 */
const handleCurrentChange = (page: number) => {
  search.value.page = page;
  handleProcessList();
};

// 关闭实例详情
// const handleCloseInstance = () => {
//   isShowInstanceDetial.value = false;
// };

// 生命周期
onMounted(() => {
  handleProcessList();
});
defineExpose({
  handleProcessList
});
</script>

<style lang="scss" scoped>
.instance-list-mian {
  overflow: hidden;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  .head-div {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 16px;
    grid-row-gap: 0px;
    .flex-item {
      background: #f8f8f8;
      border-radius: 12px 12px 12px 12px;
      height: 88px;
      padding: 12px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .flex-title {
          font-size: 14px;
          font-weight: 400;
        }
        .flex-num {
          font-size: 28px;
          font-weight: 600;
          margin-top: 5px;
        }
      }
      .right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .img {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
  .handle-div {
    // margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .item {
      flex: 1;
      display: flex;
      .tab-div {
        width: 76px;
        height: 28px;
        background: #ededed;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #ededed;
        margin-left: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .tab-item {
          width: 36px;
          height: 24px;
          border-radius: 2px 2px 2px 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
        }
        .tab-active {
          background: #fff;
          color: var(--current-color);
        }
      }
    }
    .end {
      justify-content: flex-end;
    }
  }
  .list-main {
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    margin: 4px 0;
    padding-left: 4px;
    padding-right: 4px;
    .item-main {
      width: calc((100% / 5) - 16px);
      min-width: 150px;
    }
  }
  .page-div {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    position: absolute;
    bottom: 16px;
    right: 16px;
  }
}
</style>
