<!-- 处理图片组件 -->
<template>
  <div class="autoImage-main">
    <div v-if="!url" :style="{ width: width + 'px', height: height + 'px' }" class="no-style">暂无数据</div>
    <el-image :style="{ width: width + 'px', height: height + 'px' }" :src="url" :fit="fit" :preview-src-list="[url]" v-if="url"></el-image>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  url: string;
  fit: string;
  width: string;
  height: string;
}>();
</script>
<style lang="scss" scoped>
.autoImage-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .no-style {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    background: #f6f7f8;
    color: #999999;
  }
}
</style>
