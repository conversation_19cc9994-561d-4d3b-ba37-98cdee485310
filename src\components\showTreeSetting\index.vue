<!-- 设置树显示层级弹窗 -->
<template>
  <div>
    <el-dialog
      title="查看图层"
      v-model="treeSettingDialogCopy"
      width="30%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="initData"
    >
      <el-tree
        class="tree-div"
        :data="ruleTree"
        show-checkbox
        node-key="id"
        default-expand-all
        ref="treeRef"
        @check-change="handleNodeClick"
        :default-checked-keys="checkTreeList"
        :check-strictly="true"
        :props="defaultProps"
      >
      </el-tree>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// --- 定义props ---
interface Props {
  // 打开弹框
  treeSettingDialog: boolean;
  // 模块id
  ruleTree?: any[];
  queryParams: any;
  showGraphs: any;
}

const props = withDefaults(defineProps<Props>(), {
  treeSettingDialog: false,
  ruleTree: () => [],
  queryParams: () => {}
});

const treeSettingDialogCopy = computed(() => props.treeSettingDialog);
// --- 定义变量 ---
const defaultProps = {
  children: 'list',
  label: 'typeName'
};
const checkTreeList = ref([]);
const childNode = ref([]); // 选中的子集id集合
const chooseTree = ref([]); // 根树的 id
const treeRef = ref(null);

// 定义emit
const emit = defineEmits<{
  (e: 'handleCloseTreeSetting'): void;
  (e: 'handleSubmitTreeSetting', checkTreeList: any[], chooseTree: any[]): void;
}>();

// --- 定义方法 ---
const initData = () => {
  childNode.value = [];
  chooseTree.value = [];
  treeRef.value.setCheckedKeys([]);

  if (!props.queryParams.ruleIds || props.queryParams.ruleIds.length == 0) {
    // 如果没有 代表需要初始化
    props.ruleTree.forEach((item) => {
      checkTreeList.value.push(item.id);
      chooseTree.value.push(item.id);
    });
  } else {
    checkTreeList.value = props.showGraphs;
    chooseTree.value = props.queryParams.ruleIds;
  }
  treeRef.value.setCheckedKeys(checkTreeList.value);
};
const handleClose = () => {
  emit('handleCloseTreeSetting');
};
const submit = () => {
  emit('handleSubmitTreeSetting', checkTreeList.value, chooseTree.value);
};
const handleNodeClick = (data, flg) => {
  if (flg) {
    // 需要判断数组中是否存在
    const isHas = checkTreeList.value.indexOf(data.id) == -1;
    if (isHas) {
      checkTreeList.value.push(data.id);
      if (data.levelNum == 1) {
        chooseTree.value.push(data.id);
      }
    }
  } else {
    for (let i = 0; i < checkTreeList.value.length; i++) {
      if (checkTreeList.value[i] == data.id) {
        checkTreeList.value.splice(i, 1);
        break;
      }
    }
    // 取消的时候 需要判断是不是根节点 根节点需要删除选中的根数据
    if (data.levelNum == 1) {
      for (let i = 0; i < chooseTree.value.length; i++) {
        if (chooseTree.value[i] == data.id) {
          chooseTree.value.splice(i, 1);
          break;
        }
      }
    }
  }
};
const handleNodeClick1 = (data, flg) => {
  if (flg) {
    //选中
    const isHas = checkTreeList.value.indexOf(data.id) == -1;
    if (isHas) {
      //  不存在才推送
      checkTreeList.value.push(data.id);
      childNode.value.push(data.id);
    }

    if (data.list.length > 0) {
      //  变量取消所有节点
      data.list.forEach((item) => {
        handleNodeClick(item, true);
      });
    }
    treeRef.value.setCheckedKeys(checkTreeList.value);
  } else {
    for (let i = 0; i < childNode.value.length; i++) {
      if (childNode.value[i].id == data.id) {
        childNode.value.splice(i, 1);
        break;
      }
    }
    // 取消反选
    data.delFlag = 0;
    const index = checkTreeList.value.indexOf(data.id);
    checkTreeList.value.splice(index, 1);
    if (data.list.length > 0) {
      data.list.forEach((item) => {
        handleNodeClick(item, false);
      });
    }
    treeRef.value.setCheckedKeys(checkTreeList.value);
  }
};
</script>
<style lang="scss" scoped>
.tree-div {
  max-height: 400px;
  overflow: auto;
}
</style>
