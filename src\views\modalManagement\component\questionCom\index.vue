<!-- 问卷调查 -->
<template>
  <div class="owner-main">
    <!-- 新增字段表单 -->
    <dynamic-from
      :class="{ 'dynamic-form-class': true }"
      @goBack="handleGoBack"
      @submitField="handleSubmitField"
      :confProp="formData"
      :isFieldFormProp="isFieldForm"
      @updateGroup="handleUpdateOwnerList"
      :typeProp="2"
      ref="dynamicRef"
    ></dynamic-from>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import DynamicFrom from '@/components/DynamicForm/index.vue';
import { selectRules, saveFieldGroup, saveFieldInOwner, addRule } from '@/api/modal';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();

interface FieldItem {
  id?: number;
  groupId?: number;
  // 根据实际情况补充完整类型定义
}

const dynamicRef = ref<InstanceType<typeof DynamicFrom>>();

// 响应式数据
const formData = ref<any>(null);
const isFieldForm = ref(false);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);

// 生命周期
onMounted(() => {
  loadInitialData();
});
// 方法声明
const loadInitialData = async () => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  try {
    selectRules(params).then((res) => {
      if (res.code == 200) {
        if (res.data.length != 0) {
          formData.value = res.data[0].fieldGroupModelList[0].attribution.formData;
          res.data[0].fieldGroupModelList[0].fieldModelList.find((i: any) => {
            formData.value.fields.filter((e: any) => {
              // 回显字段时  对联系人进行特殊处理
              if (e.tagIcon == 'xtlxr' && e.tag == 'el-xtlxr') {
                e.childrenList.forEach((child: any) => {
                  if (i.fieldName == child.vModel) {
                    return (child.id = i.id);
                  }
                });
              } else {
                if (i.fieldName == e.vModel) {
                  return (e.id = i.id);
                }
              }
            });
          });
          isFieldForm.value = true;
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } catch (error) {}
};
// 保存完字段之后反赋值id给子组件
const initField = async () => {
  try {
    const params = {
      moduleId: moduleId.value
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      params.companyId = companyId;
    }
    selectRules(params).then((res) => {
      if (res.code == 200) {
        if (res.data.length != 0) {
          formData.value = res.data[0].fieldGroupModelList[0].attribution.formData;
          res.data[0].fieldGroupModelList[0].fieldModelList.find((i: any) => {
            formData.value.fields.filter((e: any) => {
              // 回显字段时  对联系人进行特殊处理
              if (e.tagIcon == 'xtlxr' && e.tag == 'el-xtlxr') {
                e.childrenList.forEach((child: any) => {
                  if (i.fieldName == child.vModel) {
                    return (child.id = i.id);
                  }
                });
              } else {
                if (i.fieldName == e.vModel) {
                  return (e.id = i.id);
                }
              }
            });
          });
          dynamicRef.value.initCof(formData.value);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } catch (error) {}
};

// 提交字段之后的新增保存 问卷调查专用
const handleSubmitField = async (list: FieldItem[], confParams: string) => {
  try {
    // 思路 1：如果已经有固定的节点和属性组 则更新相应节点下的属性组的字段
    // 2：如果没有节点和属性组 则新增一个默认节点和默认属性组
    const params = {
      moduleId: moduleId.value
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      params.companyId = companyId;
    }
    selectRules(params).then((res) => {
      if (res.code == 200) {
        if (res.data.length == 0) {
          //没有节点需要新增一个默认的节点和属性组
          const fieldModelList = []; //属性列表
          //没有节点的时候 需要先保存属性组，然后再把属性组赋值给默认要素，再保存默认要素
          const defaultYS = {
            //默认要素
            typeName: '问卷调查',
            id: 0,
            iconUrl: 'diagram',
            color: 'var(--current-color)',
            aliasName: '', //别名
            wordName: 'wjdc', // 英文名
            aliasNameCn: '', //用于显示的别名中文名
            graphicalType: 4, //类型 1点 2线 3面 4无图形
            // 保存属性组的内容
            fieldGroupModelList: [],
            // 权属人的选择
            list: [],
            moduleId: moduleId.value || 0,
            levelNum: 1,
            graphicalNum: 10
          };
          let groupParams = {
            // 创建一个普通属性组就好了
            groupScope: 2,
            iconUrl: 'information',
            moduleId: moduleId.value,
            operaType: 1, //操作类型 1 新增  2 删除
            typeName: '问卷调查属性组',
            wordName: '',
            linkType: 1,
            remark: '问卷调查默认属性组',
            fieldModelList: [],
            attribution: JSON.parse(confParams),
            ruleAttribution: null
          };

          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            groupParams.companyId = companyId;
          }
          // 先创建默认属性组
          saveFieldGroup(groupParams).then((group) => {
            if (group.code == 200) {
              groupParams = group.data; //包含了属性组的id
              //保存字段 建立字段和属性组的关联关系
              list.forEach((v) => {
                v.groupId = groupParams.id;
                v.companyId = groupParams.companyId;
              });
              saveFieldInOwner(list).then((field) => {
                if (field.code == 200) {
                  groupParams.fieldModelList = field.data;
                  //保存好了属性组和字段的关联关系 只需要再保存一个要素就好了
                  defaultYS.fieldGroupModelList = [groupParams];
                  // 设置公司私有模块的数据 需要传递公司id
                  const companyId1 = route.query.companyId;
                  if (companyId1 && companyId1 !== undefined && companyId1 !== null) {
                    defaultYS.companyId = companyId1;
                  }
                  addRule([defaultYS]).then((rule) => {
                    if (rule.code == 200) {
                      ElMessage({
                        type: 'success',
                        message: '保存成功'
                      });
                      // 第一次建属性组列表的时候需要把字段列表赋值回去
                      initField();
                      //保存成功了之后返回列表
                      emit('jumpNext');
                    } else {
                      ElMessage.error(rule.msg);
                    }
                  });
                } else {
                  ElMessage.error(field.msg);
                }
              });
            } else {
              ElMessage.error(group.msg);
            }
          });
        } else {
          //代表已经保存过
          const attrGroup = res.data[0].fieldGroupModelList[0]; //唯一的属性组
          //保存字段 建立字段和属性组的关联关系
          const companyId1 = route.query.companyId;
          list.forEach((v) => {
            v.groupId = attrGroup.id;
            v.companyId = companyId1;
          });
          saveFieldInOwner(list).then((field) => {
            if (field.code == 200) {
              attrGroup.fieldModelList = field.data;
              // 设置公司私有模块的数据 需要传递公司id
              const companyId1 = route.query.companyId;
              if (companyId1 && companyId1 !== undefined && companyId1 !== null) {
                attrGroup.companyId = companyId1;
              }
              (attrGroup.attribution = JSON.parse(confParams)),
                saveFieldGroup(attrGroup).then((rule) => {
                  if (rule.code == 200) {
                    ElMessage({
                      type: 'success',
                      message: '修改成功'
                    });
                    // 修改属性组列表的时候需要把字段列表赋值回去
                    initField();
                    //保存成功了之后返回列表
                    emit('jumpNext');
                  } else {
                    ElMessage.error(rule.msg);
                  }
                });
            } else {
              ElMessage.error(field.msg);
            }
          });
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } catch (error) {
    ElMessage.error('操作失败');
  }
};

// 事件处理
const handleGoBack = () => {
  formData.value = {};
  isFieldForm.value = false;
};

const handleUpdateOwnerList = () => {
  emit('updateOwnerList', moduleId.value);
};

// 定义emit事件
const emit = defineEmits(['updateOwnerList', 'jumpNext', 'nextStep']);
const handleNext = () => {
  emit('nextStep', 3);
};
const handlePrev = () => {
  emit('nextStep', 1);
};
defineExpose({
  handleNext,
  handlePrev
});
</script>

<style lang="scss" scoped>
.owner-main {
  position: relative;
  .owner-list {
    width: 70%;
    margin: 16px 15% 16px;
    background-color: #fff;
    min-width: 400px;
    height: calc(100vh - 220px);
    overflow: auto;
    border-radius: 8px;
    box-shadow: 0px 4px 10px 0px rgba(130, 145, 169, 0.12);
    .owner-no-data {
      text-align: center;
      margin-top: 150px;
      .text {
        height: 22px;
        font-size: 14px;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        font-weight: 400;
        color: #8291a9;
        line-height: 22px;
        margin-top: -16px;
      }
    }
  }
  .btn-next-step {
    position: absolute;
    right: 16%;
    left: 16%;
    top: calc(100vh - 300px);
    display: flex;
    justify-content: flex-end;
  }
}

.dynamic-form-class {
  width: 100%;
  background-color: #fff;
}
</style>
