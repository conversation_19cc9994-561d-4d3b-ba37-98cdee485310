<template>
  <section v-if="show" class="h-transfer single-tab">
    <div class="mask"></div>
    <!-- 内容面板 -->
    <div class="transfer__content">
      <!-- 面板顶部标题 -->
      <header class="transfer__header">
        <el-icon><Monitor /></el-icon>
        {{ title }}
        <el-icon class="close-icon" @click="closeTransfer"><Close /></el-icon>
      </header>
      <!-- 穿梭框主要内容 -->
      <div class="transfer__body">
        <!-- 左边穿梭框 -->
        <div class="transfer-pane" style="border-right: 1px solid #dcdfe6 !important">
          <!-- 操作栏 -->
          <!-- <div class="transfer-pane__tools">

          </div> -->
          <!-- 穿梭框 -->
          <div class="transfer-pane__body" style="height: 530px">
            <div class="enough-mask" v-show="isEnough">
              <span class="p-center">最多选择{{ maxNum }}项</span>
            </div>
            <div class="searchResPane" :class="{ active: searchMode }" v-loading="searchLoading">
              <div class="hidden-tag" @click="searchString = ''">关闭</div>
              <div v-for="(item, index) in searchRes" :key="index" class="item">
                <div>
                  <div>{{ getNodeProp(item, 'label') }}</div>
                  <div class="text-ellipsis search-res-tip">
                    {{ getNodeProp(item, 'searchResTip') }}
                  </div>
                </div>
                <el-checkbox @change="(checked) => (checked ? addData(item) : removeData(item, true))"></el-checkbox>
              </div>

              <div v-if="searchRes.length <= 0" class="item" style="text-align: center; display: block">无匹配项</div>
            </div>

            <el-input
              v-model="searchString"
              class="search-input"
              size="small"
              style="width: 95%; height: 40px"
              type="search"
              placeholder="搜索"
              :disabled="!searchable"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-divider content-position="right" style="margin: 12px 0px"></el-divider>

            <el-scrollbar style="height: 460px">
              <el-tree
                :ref="(el) => (treeRef = el)"
                :lazy="true"
                show-checkbox
                :props="{
                  children: config.child,
                  label: config.label,
                  isLeaf: config.isLeaf,
                  disabled: config.disabled
                }"
                :default-expanded-keys="['1']"
                :load="onLoad"
                node-key="nodeId"
                :check-strictly="true"
                @check-change="onCheckChange"
                style="min-height: 370px"
              >
              </el-tree>
            </el-scrollbar>
          </div>
        </div>
        <!-- 右边穿梭框 -->
        <div class="transfer-pane">
          <div class="transfer-pane__tools">
            <span>
              <span style="margin-right: 1rem; font-size: 14px">{{ selectedData.length }} / {{ maxNum }}</span>
              <el-tooltip placement="top" content="清空">
                <el-icon @click="removeAll"><Delete /></el-icon>
              </el-tooltip>
            </span>
          </div>

          <el-divider content-position="center">已选择</el-divider>

          <el-scrollbar class="transfer-pane__body shadow right-pane">
            <div v-for="(item, index) in selectedData" :key="index" class="selected-item">
              <span>
                <!-- <i v-if="item.deptName" class="iconfont iconbumen"></i>
                  <i v-else class="iconfont iconyuangong"></i> &nbsp; -->
                <span>{{ getNodeProp(item, 'label') }}</span>
              </span>
              <el-icon @click="removeData(item)"><Delete /></el-icon>
            </div>
            <div v-for="(item, index) in aloneCheckedData" :key="'alone' + index" class="selected-item">
              <span>
                <!-- <i v-if="item.deptName" class="iconfont iconbumen"></i>
                  <i v-else class="iconfont iconyuangong"></i> &nbsp; -->
                <span>{{ getNodeProp(item, 'label') }}</span>
              </span>
              <el-icon @click="removeData(item, true)"><Delete /></el-icon>
            </div>
          </el-scrollbar>
          <footer class="transfer__footer" style="float: right">
            <el-button plain size="small" @click="closeTransfer">取消</el-button>
            <el-button type="primary" plain size="small" @click="confirm">确定</el-button>
          </footer>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, defineExpose } from 'vue';
import { Monitor, Close, Search, Delete } from '@element-plus/icons-vue';
import { debounce } from '../../utils/index';
import { CONFIG_LIST } from './config';
import type { OrgTransferProps, OrgTransferEmits, TreeNode } from './types';

const props = withDefaults(defineProps<OrgTransferProps>(), {
  value: () => [],
  type: 'dep',
  title: '组织机构',
  show: true,
  searchable: true,
  maxNum: 1
});
const emit = defineEmits<OrgTransferEmits>();

// 状态
const searchRes = ref<any[]>([]);
const selectedData = ref<any[]>([]);
const aloneCheckedData = ref<any[]>([]);
const isEnough = ref(false);
const searchString = ref('');
const searchMode = ref(false);
const searchLoading = ref(false);
const currentTrees = ref<any[]>([]);
const config = ref<any>({});
const treeRef = ref();

// 计算属性
const selectedNum = computed(() => selectedData.value.length);

// 方法
const onLoad = async (node: any, resolve: (data: any[]) => void) => {
  try {
    const res = await config.value.onload(node);
    const nodes = res.map((t: any) => ({
      nodeId: config.value.nodeId(t),
      ...t
    }));
    currentTrees.value = nodes;
    resolve(nodes);

    const tree = config.value.type;
    aloneCheckedData.value.forEach((data) => {
      currentTrees.value.forEach((curr) => {
        if (curr.memberType === data.memberType && data.nodeId === curr.nodeId) {
          tree.setChecked(data.nodeId, true);
        }
      });
    });
  } catch (error) {}
};

const search = async () => {
  if (!searchString.value) {
    searchRes.value = [];
    return;
  }
  searchLoading.value = true;
  try {
    const res = await new Promise((resolve) => {
      config.value.onsearch(searchString.value, resolve);
    });
    searchRes.value = res.map((t: any) => ({
      nodeId: config.value.nodeId(t),
      ...t
    }));
  } catch (error) {
  } finally {
    searchLoading.value = false;
  }
};

const onCheckChange = (data: TreeNode, checked: boolean) => {
  const index = aloneCheckedData.value.findIndex((t) => t.nodeId === data.nodeId);
  if (index > -1) {
    aloneCheckedData.value.splice(index, 1);
  }
  nextTick(() => {
    const nodes =
      treeRef.value?.getCheckedNodes().map((t: TreeNode) => {
        if (!t.nodeId) {
          t.nodeId = getNodeProp(t, 'nodeId');
        }
        return t;
      }) || [];
    selectedData.value = handleUniquWithMap(nodes, (item) => JSON.stringify(item));
    isNumEnough();
  });
};

const handleUniquWithMap = (arr: any[], keyFn: (item: any) => string) => {
  const map = new Map();
  arr.forEach((item) => {
    const key = keyFn(item);
    if (!map.has(key)) {
      map.set(key, item);
    }
  });
  return Array.from(map.values());
};

const addData = (data: TreeNode) => {
  const tree = config.value.type;
  tree.setChecked(data.nodeId, true);
  const flag = !tree.getCheckedKeys(data).includes(data.nodeId) && !aloneCheckedData.value.find((t) => t.nodeId === data.nodeId);
  if (flag) {
    aloneCheckedData.value.push(data);
  }
};

const removeData = (data: TreeNode, fromAloneData = false) => {
  if (fromAloneData) {
    const index = aloneCheckedData.value.findIndex((t) => t.nodeId === data.nodeId);
    if (index > -1) {
      aloneCheckedData.value.splice(index, 1);
      isEnough.value = false;
    }
  } else {
    debugger;
    // config.value.type.setChecked(data.nodeId, false);
    treeRef.value.setChecked(data.nodeId, false);
  }
};

const removeAll = () => {
  // const tree = config.value.type;
  const tree = treeRef.value;
  tree.getCheckedKeys().forEach((key: string) => {
    tree.setCheckedKeys([]);
  });
  selectedData.value = [];
  aloneCheckedData.value = [];
};

const isNumEnough = () => {
  const count = selectedData.value.length + aloneCheckedData.value.length;
  isEnough.value = count >= props.maxNum;
};

const closeTransfer = () => {
  emit('update:show', false);
  isEnough.value = false;
  searchString.value = '';
};

const confirm = () => {
  const selectedDataCopy = JSON.parse(JSON.stringify(selectedData.value));
  const selectedDataMap: Record<string, any> = {};

  selectedDataCopy.forEach((item: TreeNode) => {
    selectedDataMap[item.nodeId] = item;
  });

  aloneCheckedData.value.forEach((item: TreeNode) => {
    if (!selectedDataMap[item.nodeId]) {
      selectedDataCopy.push(item);
    }
  });

  emit('confirm', selectedDataCopy);
  closeTransfer();
};

const getConfProp = (propName: string) => {
  return config.value ? config.value[propName] : null;
};

const getNodeProp = (data: any, propName: string) => {
  try {
    const prop = getConfProp(propName);
    if (typeof prop === 'string') {
      return data[prop];
    }
    if (typeof prop === 'function') {
      return prop(data);
    }
  } catch (e) {
    return '执行出错，可联系开发人员';
  }
};

const dataInit = () => {
  aloneCheckedData.value = [];
  selectedData.value = [];
  config.value = {};

  const foundConfig = CONFIG_LIST.find((cfg) => cfg.type === props.type);
  if (foundConfig) {
    config.value = foundConfig;
  }

  if (props.value && props.value.length > 0) {
    props.value.forEach((item) => {
      item.nodeId = config.value.nodeId(item);
    });
    aloneCheckedData.value = props.value;
  }
};

// 监听
watch(searchString, () => {
  searchMode.value = !!searchString.value;
  debounceSearch();
});

watch(
  () => props.show,
  (show) => {
    if (show) {
      dataInit();
      isNumEnough();
    }
  },
  { immediate: true }
);

watch(
  () => props.type,
  () => {
    dataInit();
  },
  { immediate: true, deep: true }
);

// 生命周期
onMounted(() => {
  isNumEnough();
  debounceSearch = debounce(search, 500);
});

let debounceSearch: () => void;
defineExpose({
  getNodeProp
});
</script>

<style lang="scss" scoped>
.h-transfer {
  text-align: left;
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 2999;
  line-height: 32px;

  > .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .el-tabs--border-card {
    box-shadow: none;
  }

  .el-tabs__content {
    overflow: visible;
    min-height: 250px;
  }

  .el-tabs__nav {
    width: 100%;
    display: flex;

    > .el-tabs__item {
      flex-grow: 1;
    }
  }

  &.single-tab {
    .el-tabs__item {
      text-align: center;
      background: #f5f7fa !important;
      border-bottom: 1px solid #e4e7ed;
      border-right-width: 0;
    }
  }

  .el-tree-node__content > label.el-checkbox {
    // position: absolute;
    // right: 0;
  }

  .searchResPane {
    position: absolute;
    overflow-y: auto;
    z-index: 99;
    left: 0;
    top: 100%;
    width: 100%;
    height: 100%;
    background: white;
    border: 1px solid #dcdfe6;
    transition: top 0.5s;
    margin-top: 50px;

    &.active {
      top: 0;
    }

    .hidden-tag {
      color: #999;
      font-size: 12px;
      text-align: right;
      padding-top: 4px;
      padding-right: 12px;
      cursor: pointer;

      &:hover {
        color: #66b1ff;
      }
    }

    .item {
      padding: 4px 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 1.5;

      &:hover {
        background-color: #ecf5ff;
        color: #66b1ff;
        cursor: pointer;
      }

      .search-res-tip {
        font-size: 12px;
        color: #999;
        max-width: 280px;
      }
    }
  }

  .enough-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    color: white;
    font-size: 16px;
    z-index: 100;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    letter-spacing: 4px;
  }

  .p-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .transfer__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 600px;
    background: white;
    overflow: hidden;
    border-radius: 4px;

    .el-divider--horizontal {
      display: block;
      height: 1px;
      width: 100%;
      margin: 12px 0;
    }

    .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
  }

  .transfer__header {
    margin-bottom: 6px;
    background: #565656;
    padding: 6px 24px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .close-icon {
      cursor: pointer;

      &:hover {
        color: #f56c6c;
      }
    }
  }

  .transfer__body {
    padding: 12px 0;
    display: flex;
    justify-content: space-around;
  }

  .transfer-pane {
    width: 360px;
  }

  .search-input {
    input {
      border: 1px solid #dcdfe6 !important;

      &:focus {
        border-color: #409eff !important;
      }
    }
  }

  .transfer-pane__tools {
    margin-bottom: 0 !important;
    height: 40px !important;
    display: block !important;
    justify-content: space-between;
    align-items: center;
    text-align: right;
    padding-right: 13px;

    span:last-child {
      cursor: pointer;
    }
  }

  .transfer-pane__body {
    position: relative;
    width: 100%;
    height: 330px;
    overflow: hidden;
    font-size: 14px;

    :deep(.el-scrollbar__view) {
      height: 100%;
    }

    .el-tabs__item {
      height: 26px;
      line-height: 26px;
    }

    .el-scrollbar__view {
      overflow-x: visible !important;
    }

    .is-disabled {
      display: none;
    }
  }

  .transfer-icons {
    display: flex;
    flex-direction: column;
    justify-content: center;

    i {
      margin: 20px 0;
      cursor: pointer;
      font-size: 20px;
      color: #696969;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;

    .node-label {
      max-width: 250px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .node-checkbox {
      position: absolute;
      right: 0;
    }

    i {
      font-size: 10px;

      &:hover {
        color: #1485f8;
        cursor: pointer;
      }
    }
  }

  .right-pane {
    box-sizing: border-box;
    overflow-x: hidden;
    height: 430px !important;
    margin-bottom: 10px;

    .selected-item {
      padding: 0 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:hover {
        background-color: #f5f7fa;
      }

      span {
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      i:hover {
        color: #1485f8;
        cursor: pointer;
      }
    }
  }

  .dot {
    width: 2px;
    height: 2px;
    display: inline-block;
    border-radius: 50%;
    background: #4caf50;
  }

  .text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
