import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TemplateParams, TemplateFieldParams, TemplateUploadParams } from '@/api/templateManager/types';

/**
 * 获取字段列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getTemplate(params: TemplateParams): AxiosPromise<any> {
  return request({
    url: '/qjt/output/template/get',
    method: 'get',
    params: params
  });
}

/**
 * 查询导出模板需要的字段
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getTemplateFields(params: TemplateFieldParams): AxiosPromise<any> {
  return request({
    url: '/qjt/output/template/fields',
    method: 'post',
    data: params
  });
}

/**
 * 上传模板
 * @param params 上传参数
 * @param appType 应用类型
 * @returns {AxiosPromise}
 */
export function uploadTemplate(params: TemplateUploadParams, appType: string): AxiosPromise<any> {
  return request({
    url: `/qjt/output/template/upload/${appType}`,
    method: 'post',
    data: params
  });
}

/**
 * 下载项目
 * @param oname 文件名
 * @returns {AxiosPromise}
 */
export function downLoadTemplate(oname: string): AxiosPromise<Blob> {
  return request({
    url: `/qjt/file/downloadone/${oname}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 删除模板
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delTemplate(params: TemplateParams): AxiosPromise<any> {
  return request({
    url: `/qjt/output/template/delete`,
    method: 'post',
    data: params
  });
}
