<!-- 反显省市区并可选择省市区 用于宗地管理 -->
<template>
  <div class="areaCodeTemp-main">
    <el-cascader
      :props="cascaderProps"
      :size="size"
      v-model="selectCityCode"
      clearable
      @change="handleChangeCity"
      ref="cascaderHandle"
      style="width: 100%"
    ></el-cascader>
    <el-input v-model="detailAddr" placeholder="请输入详细地址" maxlength="30" @change="changeDetailAddrFun" style="margin-top: 10px"></el-input>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { getAreaCode } from '@/api/project';
import type { CascaderNode } from 'element-plus';
import { ElMessage } from 'element-plus/es';

// 定义props
interface Props {
  selectAreaCode?: string;
  size?: 'default' | 'small' | 'large';
  defautCode?: number | string;
}

const props = withDefaults(defineProps<Props>(), {
  selectAreaCode: '',
  size: 'default',
  defautCode: 0
});

// 定义emit
const emit = defineEmits<{
  (e: 'changeCityCode', parmas: string): void;
  (e: 'changeDetailAddr', name: string): void;
}>();

// 级联选择器的引用
const cascaderHandle = ref();
const selectCityCode = ref<number[]>([]);
const isShowAddressInfo = ref(false);
const detailAddr = ref(''); //详细地址

// 加载级联选择器节点数据
async function loadNode(node: CascaderNode, resolve: (data: any[]) => void) {
  if (node.level === 0) {
    try {
      const res = await getAreaCode('86');
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error(error);
      resolve([]);
    }
  } else {
    const areaCode = node.data?.areaCode;
    if (!areaCode) {
      resolve([]);
      return;
    }
    try {
      const res = await getAreaCode(String(areaCode));
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
        resolve([]);
      }
    } catch (error) {
      console.error(error);
      resolve([]);
    }
  }
}

// 级联选择器配置
const cascaderProps = {
  lazy: true,
  lazyLoad: loadNode,
  value: 'areaCode',
  label: 'areaName',
  expandTrigger: 'click' as const,
  children: 'children',
  checkStrictly: true
};

// 选择城市改变事件
async function handleChangeCity(val: number[]) {
  // 选择后关闭下拉
  if (cascaderHandle.value) {
    cascaderHandle.value.dropDownVisible = false;
  }
  const selectedCode = val[val.length - 1];
  //   emit('changeCityCode', selectedCode);

  try {
    const res = await getAreaCode(String(selectedCode), true);
    if (res.data && res.data.length > 0) {
      const params = {
        fullName: res.data[0].fullName + detailAddr.value,
        fullCode: val[val.length - 1]
      };
      emit('changeCityCode', JSON.stringify(params));
      //   emit('changeCodeGetName', res.data[0].fullName);
    }
  } catch (error) {
    console.error(error);
  }
}

// 主动清除areaCode
function clearAreaCode() {
  selectCityCode.value = [];
}

/**
 * 改变详细地址
 * @param val 详细地址
 */
const changeDetailAddrFun = (val: string) => {
  emit('changeDetailAddr', val);
};

// 对外暴露方法
defineExpose({
  clearAreaCode
});
</script>

<style lang="scss" scoped></style>
