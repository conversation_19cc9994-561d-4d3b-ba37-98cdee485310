const dataText = {
  legendData: ['类型1', '类型2'],
  seriesData: [
    { value: [4200, 3000, 20000, 35000, 50000, 18000], name: '类型1' },
    { value: [5000, 14000, 28000, 26000, 42000, 21000], name: '类型2' }
  ]
};
export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: JSON.stringify(dataText)
  },
  attribute: {
    titleText: '雷达图', //标题
    titleColor: '#fff', //标题颜色
    subLableColor: '#fff', //副标题颜色
    indicator: [
      { name: '数据1', max: 6500 },
      { name: '数据2', max: 16000 },
      { name: '数据3', max: 30000 },
      { name: '数据4', max: 38000 },
      { name: '数据5', max: 52000 },
      { name: '数据6', max: 25000 }
    ] //程度范围
  }
};
