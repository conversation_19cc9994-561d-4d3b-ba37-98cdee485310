<template>
  <dv-digital-flop :config="flopConfig" style="width: 100%; height: 100%" />
</template>

<script setup lang="ts">
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-dataV-digitalFlop'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
const uuid = ref(null);
const flopConfig = ref({});
watch(
  () => props.option.attribute,
  (newObj) => {
    loadData();
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  () => {
    loadData();
  }
);

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId?: any) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    // 表达式必填
    if (!props.option.cptDataForm.apiUrl) {
      ElMessage.warning('表达式不能为空');
      return;
    }
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      taskId: taskId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        flopConfig.value = {
          number: res.data && res.data != '' && res.data != null ? [Number(res.data)] : [0],
          content: props.option.attribute.content,
          toFixed: props.option.attribute.toFixedNum,
          textAlign: props.option.attribute.textAlign,
          rowGap: props.option.attribute.rowGap,
          style: props.option.attribute.style
        };
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      flopConfig.value = {
        number: res.value.split(',').map(Number),
        content: props.option.attribute.content,
        toFixed: props.option.attribute.toFixedNum,
        textAlign: props.option.attribute.textAlign,
        rowGap: props.option.attribute.rowGap,
        style: props.option.attribute.style
      };
    });
  }
};
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});
</script>

<style scoped></style>
