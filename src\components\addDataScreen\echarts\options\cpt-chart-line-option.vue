<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.title" />
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleTextColor" show-alpha />
    </el-form-item>
    <el-form-item label="副标题">
      <el-input v-model="attributeCopy.subtext" />
    </el-form-item>
    <el-form-item label="副标题颜色">
      <el-color-picker v-model="attributeCopy.subtextColor" show-alpha />
    </el-form-item>
    <el-form-item label="标题位置(左)">
      <el-input v-model="attributeCopy.titleLeft" />
    </el-form-item>
    <el-form-item label="标题位置(上)">
      <el-input v-model="attributeCopy.titleTop" />
    </el-form-item>
    <el-form-item label="x轴文字">
      <el-switch v-model="attributeCopy.xLabelShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="x轴字体颜色">
      <el-color-picker v-model="attributeCopy.xLabelColor" show-alpha />
    </el-form-item>
    <el-form-item label="x轴线显示">
      <el-switch v-model="attributeCopy.xLineShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="x轴线颜色">
      <el-color-picker v-model="attributeCopy.xLineColor" show-alpha />
    </el-form-item>
    <el-form-item label="x轴刻度线">
      <el-switch v-model="attributeCopy.xTickShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="y轴文字">
      <el-switch v-model="attributeCopy.yLabelShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="y轴字体颜色">
      <el-color-picker v-model="attributeCopy.yLabelColor" show-alpha />
    </el-form-item>
    <el-form-item label="y轴线显示">
      <el-switch v-model="attributeCopy.yLineShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="y轴颜色">
      <el-color-picker v-model="attributeCopy.yLineColor" show-alpha />
    </el-form-item>
    <el-form-item label="y轴网格线">
      <el-switch v-model="attributeCopy.yGridLineShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="y轴刻度线">
      <el-switch v-model="attributeCopy.yTickShow" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="平滑曲线">
      <el-switch v-model="attributeCopy.smooth" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="是否开启年份选择">
      <el-switch v-model="attributeCopy.openYear" active-text="开" inactive-text="关" />
    </el-form-item>
    <el-form-item label="渐变颜色1">
      <el-color-picker v-model="attributeCopy.areaColor1" show-alpha />
    </el-form-item>
    <el-form-item label="渐变颜色2">
      <el-color-picker v-model="attributeCopy.areaColor2" show-alpha />
    </el-form-item>
    <el-form-item label="渐变颜色3">
      <el-color-picker v-model="attributeCopy.areaColor3" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-line-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>
