<template>
  <div class="zong-di-list-continer" v-loading.fullscreen.lock="fullscreenLoading">
    <el-row :gutter="20" v-if="isUnfold">
      <el-col :span="24" :xs="24">
        <div class="list-title">
          <div class="filter-btn" @click="showFilter">筛选</div>
          <span class="title">{{ projectType == 1 ? '宗地列表' : '小班列表' }}</span>
          <span @click="handleUnfold">
            <el-image class="icon" :src="sqIcon" style="cursor: pointer"></el-image>
          </span>
        </div>
        <div class="list-search">
          <el-input v-model="filterMsg.parcelName" @clear="searchZD" placeholder="请输入宗地名称" clearable>
            <template #prefix>
              <i class="el-icon-search"></i>
            </template>
          </el-input>
          <el-button type="primary" class="search-button" @click="searchZD">搜索</el-button>
        </div>
        <div class="list-content">
          <div v-for="item in parcelList" :key="item.id">
            <div class="content-item" @click="handleParceItem(item)" :class="{ 'list-content-active': nowCheckedZD == item.id }">
              <span class="item-name" :class="{ 'active-span': nowCheckedZD == item.id }">
                <span v-show="projectType == 1" :title="item.parcelName">{{ formatParcelName(item.parcelName) }}</span>
                <span v-show="projectType == 2">{{ item.title }}</span>
              </span>
              <i class="el-icon-arrow-right icon-right"></i>
            </div>
          </div>
        </div>
        <div class="list-pagination">
          <pagination
            v-show="total > 0"
            :total="total"
            :page="queryParams.pageNum"
            :limit="queryParams.pageSize"
            :pager-count="pagerCount"
            layout="jumper,sizes,pager"
            :page-sizes="[10, 50, 100, 200]"
            @update:page="(val) => emit('update:queryParams', { ...queryParams, pageNum: val })"
            @update:limit="(val) => emit('update:queryParams', { ...queryParams, pageSize: val })"
            @pagination="getParceListByPage"
          />
        </div>
        <div class="page-div" v-show="total > 0">
          <div class="page-total">共{{ total }}条</div>
          <div class="page-right">
            <el-button type="primary" :disabled="btnType == 'noUp' || nowCheckedZD == 0" @click="btnUPandDown(1)">上一个</el-button>
            <el-button type="primary" :disabled="btnType == 'noDown' || nowCheckedZD == 0" @click="btnUPandDown(2)">下一个</el-button>
          </div>
        </div>
        <div class="list-btn">
          <el-button type="primary" icon="el-icon-download" @click="handleExport">导出报告</el-button>
          <el-button style="background-color: #ff8a48; border: none; color: #fff" icon="el-icon-download" @click="handleSHP">导出SHP</el-button>
          <el-button type="success" size="default" icon="el-icon-attract" @click="handleGDB">导出GDB</el-button>
          <el-dropdown size="default" @command="handleCommand" style="margin-left: 12px">
            <span>
              <i class="el-icon-setting" style="color: #fff"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item icon="el-icon-takeaway-box" command="handleManager">管理宗地</el-dropdown-item>
                <el-dropdown-item icon="el-icon-upload" command="handleUpdate">更新宗地</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
    <el-row v-else>
      <div class="zongdi-icon" @click="handleUnfoldZongDi">
        <el-image class="icon" :src="zdIcon"></el-image>
      </div>
    </el-row>

    <!-- 导出报告对话框 -->
    <el-dialog
      title="导出报告"
      v-model="exportVisibleDialog"
      :modal-append-to-body="false"
      width="874px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <el-form :model="exportForm" :rules="exportFormRule" ref="exportForm" label-width="100px" class="demo-ruleForm" :label-position="`top`">
        <el-form-item label="选择坐标系统" prop="wkId">
          <el-cascader v-model="exportForm.wkId" :options="wkidList" :show-all-levels="false" style="width: 100%"></el-cascader>
        </el-form-item>
        <el-form-item label="宗地列表" prop="zdIds">
          <el-input v-model="exportForm.zdNames" placeholder="请选择需要导出的宗地" readonly @click="showChooseZD(1)"></el-input>
        </el-form-item>
        <el-form-item label="模板选择" prop="templateIds">
          <el-checkbox v-model="exportForm.temAll" class="dialog-row-allChecked" @change="changeBox(2)">全选/全不选</el-checkbox>
          <el-select v-model="exportForm.templateIds" placeholder="请选择需要导出的模板" clearable multiple style="width: 100%">
            <el-option v-for="item in templateList" :key="item.tid" :label="item.templateName" :value="item.tid"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="parcelAreaUnit" label="导出宗地面积单位">
          <el-select v-model="exportForm.parcelAreaUnit" placeholder="请选择导出宗地面积单位" style="width: 100%">
            <el-option v-for="item in parcelAreaUnitList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="导出内容">
          <el-checkbox v-model="exportForm.contentAll" class="dialog-row-allChecked" @change="changeBox(3)">全选/全不选</el-checkbox>
          <el-select v-model="exportForm.content" placeholder="请选择导出内容" clearable multiple style="width: 100%">
            <el-option v-for="item in contentList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="导出样式">
          <el-checkbox-group v-model="exportForm.downLoad">
            <el-checkbox value="word" class="checkbox-item">导出word</el-checkbox>
            <el-checkbox value="pdf" class="checkbox-item">导出pdf</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="界址种类">
          <el-select v-model="exportForm.jblx" placeholder="请选择界标类型" clearable style="width: 100%">
            <el-option v-for="item in JBTypeList" :key="item.label" :label="item.label" :value="item.label"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="界址线位置">
          <el-select v-model="exportForm.jxlx" placeholder="请选择界线类型" clearable style="width: 100%">
            <el-option v-for="item in JXTypeList" :key="item.label" :label="item.label" :value="item.label"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportVisibleDialog = false">取 消</el-button>
          <el-button type="primary" @click="downloadBtn">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出shp弹窗 -->
    <el-dialog
      :title="dialogTitle"
      v-model="shpDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="874px"
    >
      <el-form :model="downSHPMsg" :rules="downSHPMsgRule" ref="downSHPMsg" label-width="100px" class="demo-ruleForm" :label-position="`top`">
        <el-form-item label="选择坐标系统" prop="wkId">
          <el-cascader v-model="downSHPMsg.wkId" :options="wkidList" :show-all-levels="false" style="width: 100%"></el-cascader>
        </el-form-item>
        <el-form-item label="宗地列表" prop="zdIds">
          <el-input v-model="downSHPMsg.zdNames" placeholder="请选择需要导出的宗地" readonly @click="showChooseZD(2)"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shpDialog = false">取 消</el-button>
          <el-button type="primary" @click="downLoadSHP">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 宗地管理弹窗 -->
    <el-dialog
      :title="managerZDDialogTile"
      v-model="zdMangerDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="874px"
    >
      <div class="dialog-row">
        <div class="dialog-item">
          <div class="dialog-label">宗地位置</div>
          <div class="dialog-content">
            <areaCodeTemp v-if="zdMangerDialog" :size="`default`" @changeCityCode="changeCityCode"></areaCodeTemp>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">宗地名称</div>
          <div class="dialog-content">
            <el-input placeholder="请输入宗地名称" v-model="managerSearch.parcelName" size="default"></el-input>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">采集人</div>
          <div class="dialog-content">
            <el-select
              v-model="managerSearch.createUserId"
              filterable
              clearable
              placeholder="请选择"
              style="width: 100%"
              size="default"
              remote
              :remote-method="remoteMethod"
              v-loadmore="getMoreUser"
            >
              <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId"> </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="dialog-row" v-show="showManagerSearch">
        <div class="dialog-item">
          <div class="dialog-label">最后修改人</div>
          <div class="dialog-content">
            <el-select
              v-model="managerSearch.optUserId"
              filterable
              clearable
              remote
              :remote-method="remoteMethod"
              size="default"
              placeholder="请选择"
              style="width: 100%"
              v-loadmore="getMoreUser"
            >
              <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">任务</div>
          <div class="dialog-content">
            <el-select v-model="managerSearch.taskId" filterable clearable placeholder="请选择" style="width: 100%" size="default">
              <el-option v-for="item in taskList" :key="item.taskId" :label="item.taskName" :value="item.taskId"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">权利人姓名</div>
          <div class="dialog-content">
            <el-input v-model.trim="managerSearch.qlrxm" size="default" maxlength="20" placeholder="请输入权利人姓名"></el-input>
          </div>
        </div>
      </div>
      <div class="dialog-row" v-show="showManagerSearch">
        <div class="dialog-item">
          <div class="dialog-label">创建时间</div>
          <div class="dialog-content">
            <el-date-picker
              size="default"
              v-model="filterCreateTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">最后修改时间</div>
          <div class="dialog-content">
            <el-date-picker
              size="default"
              v-model="filterUpdateTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-label">是否勘界</div>
          <div class="dialog-content">
            <el-select v-model="managerSearch.kj" placeholder="请选择">
              <el-option label="全部" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="dialog-row flex-end">
        <span v-show="!showManagerSearch" @click="showManagerSearch = !showManagerSearch" class="zankai-shouqi">展开更多</span>
        <span v-show="showManagerSearch" @click="showManagerSearch = !showManagerSearch" class="zankai-shouqi">收起更多</span>
        <el-button type="primary" icon="el-icon-search" size="default" @click="getPlaceForManager">查询</el-button>
        <el-button size="default" icon="el-icon-refresh-right" @click="resertManager">重置</el-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="managerZDData"
        tooltip-effect="dark"
        style="width: 100%; height: 490px; overflow: auto"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" reserve-selection width="55"> </el-table-column>
        <el-table-column label="宗地名称" prop="parcelName"></el-table-column>
        <el-table-column label="创建时间" prop="createTime"></el-table-column>
        <el-table-column label="宗地位置">
          <template #default="scope">
            {{ scope.row.fullAddress ? scope.row.fullAddress : '- -' }}
          </template>
        </el-table-column>
      </el-table>
      <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="managerSearch.pageNum"
          :page-sizes="[10, 50, 100, 200, 1000, 2000]"
          :page-size="managerSearch.pageSize"
          layout="total, sizes, prev, pager, next"
          :total="managerTotal"
        >
        </el-pagination>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="danger" @click="delZD" v-if="managerZDDialogTile == '管理宗地'">删 除</el-button>
          <el-button type="primary" @click="submitChoosZd" v-else>确 定</el-button>
          <el-button @click="zdMangerDialog = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 宗地筛选 -->
    <el-dialog
      title="宗地筛选"
      v-model="filterDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="874px"
    >
      <el-form :model="filterMsg" ref="filterMsg" label-width="120px" class="demo-ruleForm" :label-position="`right`">
        <el-form-item label="行政区划">
          <areaCodeTemp style="width: 100%" :defautCode="filterMsg.areaCode" @changeCityCode="changeCityCode"></areaCodeTemp>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="filterCreateTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最后修改时间">
          <el-date-picker
            v-model="filterUpdateTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="采集人">
          <el-select
            v-model="filterMsg.createUserId"
            filterable
            clearable
            remote
            :remote-method="remoteMethod"
            v-loadmore="getMoreUser"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最后修改人">
          <el-select
            v-model="filterMsg.optUserId"
            filterable
            clearable
            remote
            :remote-method="remoteMethod"
            placeholder="请选择"
            v-loadmore="getMoreUser"
            style="width: 100%"
          >
            <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务">
          <el-select v-model="filterMsg.taskId" filterable clearable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in taskList" :key="item.taskId" :label="item.taskName" :value="item.taskId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权利人姓名">
          <el-input v-model.trim="filterMsg.qlrxm" maxlength="20" placeholder="请输入权利人姓名"></el-input>
        </el-form-item>
        <el-form-item label="权利人证件号码">
          <el-input v-model.trim="filterMsg.qlrzjhm" maxlength="18" placeholder="请输入权利人证件号码"></el-input>
        </el-form-item>
        <el-form-item label="权利人手机号">
          <el-input v-model.trim="filterMsg.qlrPhone" maxlength="11" placeholder="请输入权利人手机号码"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="filterSubmit">确 定</el-button>
          <el-button @click="resertFilter">重 置</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      title="数据下载中"
      v-model="progressDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="874px"
    >
      <el-progress :text-inside="true" :stroke-width="16" :percentage="progress"></el-progress>
    </el-dialog>

    <updateSHP :appType="1" :uploadProjectDialog="uploadProjectDialog" @closeDialog="handleClose"></updateSHP>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import shouqiIcon from '@/assets/images/shouqi.png';
import zhankaizongdi from '../../../assets/images/zhankaizongdi.png';
import { getTemplate } from '@/api/templateManager';
import { exportWord, getParcelList, delZDFun, findAsyncMsg, findAsyncFile } from '@/api/project';
import { downLoadSHP as apiDownLoadSHP, downLoadGDB } from '@/api/projectData';
import areaCodeTemp from './areaCodeTemp/index.vue';
import { listUser } from '@/api/system/user';
import { getAllTask } from '@/api/task';
import { loadModules } from 'esri-loader';
import updateSHP from '@/components/updateSHP/index.vue';

// 类型定义
interface ParcelItem {
  id: number;
  parcelName: string;
  title?: string;
  fullAddress?: string;
  createTime?: string;
}

interface UserItem {
  userId: string | number;
  nickName: string;
}

interface TaskItem {
  taskId: number;
  taskName: string;
}

interface TemplateItem {
  tid: number;
  templateName: string;
}

interface ExportForm {
  zdIds: number[];
  templateIds: number[];
  content: string[];
  downLoad: string[];
  wkId: string;
  jblx: string;
  jxlx: string;
  zdAll: boolean;
  temAll: boolean;
  contentAll: boolean;
  parcelAreaUnit: number;
  zdNames: string;
}

interface DownSHPMsg {
  zdIds: number[];
  wkId: string;
  zdAll: boolean;
  zdNames: string;
}

interface ManagerSearch {
  areaCode: string;
  createTimeStart: string;
  createTimeEnd: string;
  createUserId: number;
  optUserId: number;
  taskId: number;
  updateTimeStart: string;
  updateTimeEnd: string;
  pageSize: number;
  pageNum: number;
  parcelName: string;
  qlrxm: string;
  qlrzjhm: string;
  qlrPhone: string;
  kj: number;
}

// Props定义
const props = defineProps<{
  parcelList: ParcelItem[];
  total: number;
  queryParams: {
    pageNum: number;
    pageSize: number;
  };
  nowCheckedZD: number;
  pageCount: number;
}>();

// Emits定义
const emit = defineEmits<{
  (e: 'getParceListByPage', filterMsg: any): void;
  (e: 'getParceItem', item: ParcelItem): void;
  (e: 'getParceListByUpAndDown', type: number, filterMsg: any): void;
  (e: 'update:queryParams', params: any): void;
}>();

// Store
const store = useProjectStore();

// 响应式状态
const isUnfold = ref(true);
const fullscreenLoading = ref(false);
const exportVisibleDialog = ref(false);
const shpDialog = ref(false);
const zdMangerDialog = ref(false);
const filterDialog = ref(false);
const progressDialog = ref(false);
const showManagerSearch = ref(false);
const uploadProjectDialog = ref(false);
const dialogTitle = ref('');
const managerZDDialogTile = ref('管理宗地');
const chooseZDType = ref(1);
const progress = ref(0);
const pagerCount = ref(5);

// 数据列表
const templateList = ref<TemplateItem[]>([]);
const userList = ref<UserItem[]>([]);
const taskList = ref<TaskItem[]>([]);
const managerZDData = ref<ParcelItem[]>([]);
const multipleSelection = ref<ParcelItem[]>([]);
const multipleSelectionShp = ref<ParcelItem[]>([]);

// 表单数据
const exportForm = ref<ExportForm>({
  zdIds: [],
  templateIds: [],
  content: [],
  downLoad: ['word'],
  wkId: '',
  jblx: '',
  jxlx: '',
  zdAll: false,
  temAll: false,
  contentAll: false,
  parcelAreaUnit: 1,
  zdNames: ''
});

const downSHPMsg = ref<DownSHPMsg>({
  zdIds: [],
  wkId: '',
  zdAll: false,
  zdNames: ''
});

const managerSearch = ref<ManagerSearch>({
  areaCode: '',
  createTimeStart: '',
  createTimeEnd: '',
  createUserId: 0,
  optUserId: 0,
  taskId: 0,
  updateTimeStart: '',
  updateTimeEnd: '',
  pageSize: 10,
  pageNum: 1,
  parcelName: '',
  qlrxm: '',
  qlrzjhm: '',
  qlrPhone: '',
  kj: 0
});

const filterMsg = ref({
  areaCode: '',
  createTimeStart: '',
  createTimeEnd: '',
  createUserId: 0,
  optUserId: 0,
  taskId: 0,
  updateTimeStart: '',
  updateTimeEnd: '',
  pageSize: props.queryParams.pageSize,
  pageNum: props.queryParams.pageNum,
  parcelName: sessionStorage.getItem('filterTitle') || '',
  qlrxm: '',
  qlrzjhm: '',
  qlrPhone: ''
});

const filterCreateTime = ref<string[]>([]);
const filterUpdateTime = ref<string[]>([]);

// 计算属性
const projectType = computed(() => store.selectProjectType);

const btnType = computed(() => {
  if (!props.nowCheckedZD) {
    return 'normal';
  } else {
    let index: number = -1;
    props.parcelList.forEach((v, idx) => {
      if (props.nowCheckedZD == v.id) {
        index = idx;
      }
    });
    if (index == 0 && props.queryParams.pageNum == 1) {
      return 'noUp';
    } else if (index == props.parcelList.length - 1 && props.pageCount == props.queryParams.pageNum) {
      return 'noDown';
    } else {
      return 'normal';
    }
  }
});

// 常量定义
const sqIcon = shouqiIcon;
const zdIcon = zhankaizongdi;

// 坐标系统列表
const wkidList = ref([
  {
    value: '1',
    label: 'CGCS2000',
    children: [
      {
        value: '1',
        label: '3度带',
        children: []
      },
      {
        value: '2',
        label: '6度带',
        children: []
      }
    ]
  }
]);

// 导出表单校验规则
const exportFormRule = {
  wkId: [{ required: true, message: '请选择坐标系统', trigger: 'change' }],
  zdIds: [{ required: true, message: '请选择宗地', trigger: 'change' }],
  templateIds: [{ required: true, message: '请选择模板', trigger: 'change' }],
  content: [{ required: true, message: '请选择导出内容', trigger: 'change' }],
  downLoad: [{ required: true, message: '请选择导出样式', trigger: 'change' }]
};

// SHP导出表单校验规则
const downSHPMsgRule = {
  wkId: [{ required: true, message: '请选择坐标系统', trigger: 'change' }],
  zdIds: [{ required: true, message: '请选择宗地', trigger: 'change' }]
};

// 面积单位列表
const parcelAreaUnitList = [
  { value: 1, label: '平方米' },
  { value: 2, label: '公顷' },
  { value: 3, label: '亩' }
];

// 导出内容列表
const contentList = [
  { value: '1', label: '宗地基本信息' },
  { value: '2', label: '权利人信息' },
  { value: '3', label: '界址点信息' },
  { value: '4', label: '界址线信息' }
];

// 界标类型列表
const JBTypeList = [
  { label: '钢钉', value: '钢钉' },
  { label: '木桩', value: '木桩' },
  { label: '水泥桩', value: '水泥桩' },
  { label: '喷漆', value: '喷漆' }
];

// 界线类型列表
const JXTypeList = [
  { label: '围墙', value: '围墙' },
  { label: '栅栏', value: '栅栏' },
  { label: '铁丝网', value: '铁丝网' },
  { label: '篱笆', value: '篱笆' }
];

// 管理宗地总数
const managerTotal = ref(0);

/**
 * 格式化宗地名称
 * @param name 宗地名称
 * @returns 格式化后的宗地名称
 */
const formatParcelName = (name: string): string => {
  return name.length > 18 ? name.substring(0, 18) + '...' : name;
};

/**
 * 搜索宗地
 */
const searchZD = () => {
  filterMsg.value.pageNum = 1;
  filterMsg.value.pageSize = props.queryParams.pageSize;
  sessionStorage.setItem('filterTitle', filterMsg.value.parcelName);
  sessionStorage.setItem('filterMsg', JSON.stringify(filterMsg.value));
  emit('getParceListByPage', filterMsg.value);
};

/**
 * 获取宗地列表
 */
const getParceListByPage = () => {
  filterMsg.value.pageNum = props.queryParams.pageNum;
  filterMsg.value.pageSize = props.queryParams.pageSize;
  sessionStorage.setItem('filterMsg', JSON.stringify(filterMsg.value));
  emit('getParceListByPage', filterMsg.value);
};

/**
 * 展开宗地
 */
const handleUnfold = () => {
  isUnfold.value = false;
};

/**
 * 展开宗地
 */
const handleUnfoldZongDi = () => {
  isUnfold.value = true;
};

/**
 * 获取宗地信息
 * @param item 宗地信息
 */
const handleParceItem = (item: ParcelItem) => {
  emit('getParceItem', item);
};

/**
 * 显示过滤
 */
const showFilter = () => {
  filterDialog.value = true;
};

/**
 * 上移下移
 * @param type 类型
 */
const btnUPandDown = (type: number) => {
  emit('getParceListByUpAndDown', type, filterMsg.value);
};

/**
 * 导出
 */
const handleExport = () => {
  exportVisibleDialog.value = true;
  getTemplateList();
};

/**
 * 导出SHP
 */
const handleSHP = () => {
  dialogTitle.value = '导出SHP';
  shpDialog.value = true;
};

const handleGDB = () => {
  dialogTitle.value = '导出GDB';
  shpDialog.value = true;
};

/**
 * 管理宗地
 */
const handleCommand = (command: string) => {
  switch (command) {
    case 'handleManager':
      handleManager();
      break;
    case 'handleUpdate':
      handleUpdate();
      break;
  }
};

/**
 * 管理宗地
 */
const handleManager = () => {
  managerZDDialogTile.value = '管理宗地';
  zdMangerDialog.value = true;
  getPlaceForManager();
  getTaskList();
};

/**
 * 上传项目
 */
const handleUpdate = () => {
  uploadProjectDialog.value = true;
};

/**
 * 关闭上传项目
 */
const handleClose = () => {
  uploadProjectDialog.value = false;
};

/**
 * 改变宗地
 * @param type 类型
 */
const changeBox = (type: number) => {
  switch (type) {
    case 1:
      if (exportForm.value.zdAll) {
        exportForm.value.zdIds = multipleSelection.value.map((item) => item.id);
        exportForm.value.zdNames = multipleSelection.value.map((item) => item.parcelName).join(',');
      } else {
        exportForm.value.zdIds = [];
        exportForm.value.zdNames = '';
      }
      break;
    case 2:
      if (exportForm.value.temAll) {
        exportForm.value.templateIds = templateList.value.map((item) => item.tid);
      } else {
        exportForm.value.templateIds = [];
      }
      break;
    case 3:
      if (exportForm.value.contentAll) {
        exportForm.value.content = contentList.map((item) => item.value);
      } else {
        exportForm.value.content = [];
      }
      break;
  }
};

/**
 * 下载
 */
const downloadBtn = async () => {
  const formRef = document.querySelector('.demo-ruleForm') as any;
  await formRef.validate();

  fullscreenLoading.value = true;
  progressDialog.value = true;

  try {
    const res = await exportWord({
      ...exportForm.value,
      projectId: store.proModuleId
    });

    if (res.data.type === 'application/json') {
      // 导出异常
      const reader = new FileReader();
      reader.readAsText(res.data, 'utf-8');
      reader.onload = (data) => {
        const result = JSON.parse((data.target as FileReader).result as string);
        ElMessage.error(result.msg || '未知异常');
      };
    } else {
      // 导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      const endFileName = name.substring(index + 1, name.length) || 'file.docx';
      const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });

      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, endFileName);
      } else {
        // 非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = endFileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      }
    }
    progressDialog.value = false;
    fullscreenLoading.value = false;
  } catch (error) {
    console.error(error);
    progressDialog.value = false;
    fullscreenLoading.value = false;
  }
};

/**
 * 下载SHP
 */
const downLoadSHP = async () => {
  const formRef = document.querySelector('.demo-ruleForm') as any;
  await formRef.validate();

  fullscreenLoading.value = true;
  progressDialog.value = true;

  try {
    const res = await apiDownLoadSHP({
      ...downSHPMsg.value,
      projectId: store.proModuleId
    });

    if (res.code === 200) {
      const timer = setInterval(async () => {
        const msg = await findAsyncMsg({ id: res.data.id });
        if (msg.code === 200) {
          progress.value = msg.data;
          if (msg.data >= 100) {
            clearInterval(timer);
            const file = await findAsyncFile({ id: res.data.id });
            if (file.code === 200) {
              const url = window.URL.createObjectURL(file.data);
              window.open(url);
              window.URL.revokeObjectURL(url);
            }
            progressDialog.value = false;
            fullscreenLoading.value = false;
          }
        }
      }, 1000);
    }
  } catch (error) {
    console.error(error);
    progressDialog.value = false;
    fullscreenLoading.value = false;
  }
};

/**
 * 获取模板列表
 */
const getTemplateList = async () => {
  try {
    const res = await getTemplate({ projectId: store.proModuleId });
    if (res.code === 200) {
      templateList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 * 显示选择宗地
 * @param type 类型
 */
const showChooseZD = (type: number) => {
  chooseZDType.value = type;
  managerZDDialogTile.value = '选择宗地';
  zdMangerDialog.value = true;
  getPlaceForManager();
};

/**
 * 提交选择宗地
 */
const submitChoosZd = () => {
  if (chooseZDType.value === 1) {
    exportForm.value.zdIds = multipleSelection.value.map((item) => item.id);
    exportForm.value.zdNames = multipleSelection.value.map((item) => item.parcelName).join(',');
  } else {
    downSHPMsg.value.zdIds = multipleSelection.value.map((item) => item.id);
    downSHPMsg.value.zdNames = multipleSelection.value.map((item) => item.parcelName).join(',');
  }
  zdMangerDialog.value = false;
};

/**
 * 获取宗地列表
 */
const getPlaceForManager = async () => {
  try {
    const res = await getParcelList({
      ...managerSearch.value,
      projectId: store.proModuleId
    });
    if (res.code === 200) {
      managerZDData.value = res.data.list;
      managerTotal.value = res.data.total;
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 * 选择宗地
 * @param selection 宗地列表
 */
const handleSelectionChange = (selection: ParcelItem[]) => {
  multipleSelection.value = selection;
};

/**
 * 改变大小
 * @param val 大小
 */
const handleSizeChange = (val: number) => {
  managerSearch.value.pageSize = val;
  getPlaceForManager();
};

/**
 * 改变当前页
 * @param val 当前页
 */
const handleCurrentChange = (val: number) => {
  managerSearch.value.pageNum = val;
  getPlaceForManager();
};

/**
 * 删除宗地
 */
const delZD = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的宗地');
    return;
  }

  try {
    const res = await delZDFun({
      ids: multipleSelection.value.map((item) => item.id),
      projectId: store.proModuleId
    });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      zdMangerDialog.value = false;
      emit('getParceListByPage', filterMsg.value);
    }
  } catch (error) {
    console.error(error);
  }
};

const resertManager = () => {
  managerSearch.value = {
    areaCode: '',
    createTimeStart: '',
    createTimeEnd: '',
    createUserId: 0,
    optUserId: 0,
    taskId: 0,
    updateTimeStart: '',
    updateTimeEnd: '',
    pageSize: 10,
    pageNum: 1,
    parcelName: '',
    qlrxm: '',
    qlrzjhm: '',
    qlrPhone: '',
    kj: 0
  };
  filterCreateTime.value = [];
  filterUpdateTime.value = [];
  getPlaceForManager();
};

/**
 * 过滤提交
 */
const filterSubmit = () => {
  if (filterCreateTime.value && filterCreateTime.value.length > 0) {
    filterMsg.value.createTimeStart = filterCreateTime.value[0];
    filterMsg.value.createTimeEnd = filterCreateTime.value[1];
  }
  if (filterUpdateTime.value && filterUpdateTime.value.length > 0) {
    filterMsg.value.updateTimeStart = filterCreateTime.value[0];
    filterMsg.value.updateTimeEnd = filterCreateTime.value[1];
  }
  filterDialog.value = false;
  emit('getParceListByPage', filterMsg.value);
};

/**
 * 重置过滤
 */
const resertFilter = () => {
  filterMsg.value = {
    areaCode: '',
    createTimeStart: '',
    createTimeEnd: '',
    createUserId: 0,
    optUserId: 0,
    taskId: 0,
    updateTimeStart: '',
    updateTimeEnd: '',
    pageSize: props.queryParams.pageSize,
    pageNum: props.queryParams.pageNum,
    parcelName: '',
    qlrxm: '',
    qlrzjhm: '',
    qlrPhone: ''
  };
  filterCreateTime.value = [];
  filterUpdateTime.value = [];
};

/**
 * 改变城市编码
 * @param code 城市编码
 */
const changeCityCode = (code: string) => {
  if (filterDialog.value) {
    filterMsg.value.areaCode = code;
  } else {
    managerSearch.value.areaCode = code;
  }
};

/**
 * 远程方法
 * @param query 查询
 */
const remoteMethod = async (query: string) => {
  if (query) {
    try {
      const res = await listUser({
        pageNum: 1,
        pageSize: 10,
        userName: query
      });
      if (res.code === 200) {
        userList.value = res.data;
      }
    } catch (error) {
      console.error(error);
    }
  }
};

/**
 * 获取更多用户
 */
const getMoreUser = async () => {
  try {
    const res = await listUser({
      pageNum: 1,
      pageSize: 10
    });
    if (res.code === 200) {
      userList.value = [...userList.value, ...res.data];
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 * 获取任务列表
 */
const getTaskList = async () => {
  try {
    const res = await getAllTask({
      projectId: store.proModuleId
    });
    if (res.code === 200) {
      taskList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 * 生命周期钩子
 */
onMounted(() => {
  // 初始化坐标系统列表
  const list_3 = [];
  for (let i = 25; i < 46; i++) {
    list_3.push({ value: i, label: `${i}` });
  }
  const list_6 = [];
  for (let i = 13; i < 24; i++) {
    list_6.push({ value: i, label: `${i}` });
  }
  wkidList.value[0].children[0].children = list_3;
  wkidList.value[0].children[1].children = list_6;
});

// 监听器
watch(
  filterCreateTime,
  (val) => {
    if (val && val.length == 0) {
      if (filterDialog.value) {
        filterMsg.value.createTimeStart = '';
        filterMsg.value.createTimeEnd = '';
      } else {
        managerSearch.value.createTimeStart = '';
        managerSearch.value.createTimeEnd = '';
      }
    }
  },
  { deep: true }
);

watch(
  filterUpdateTime,
  (val) => {
    if (val && val.length == 0) {
      if (filterDialog.value) {
        filterMsg.value.updateTimeStart = '';
        filterMsg.value.updateTimeEnd = '';
      } else {
        managerSearch.value.updateTimeStart = '';
        managerSearch.value.updateTimeEnd = '';
      }
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.row-span {
  display: flex;
  flex-direction: row;
  align-items: center;
  .span-item {
    padding: 0px 5px;
    cursor: pointer;
    margin-right: 5px;
    background-color: #dbe7ee;
  }
  .active {
    color: var(--current-color);
  }
}
.table-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  .tc-span {
    width: 100px;
    text-align: right;
  }
  .attribute-div {
    flex: 1;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .attribute {
      margin-right: 10px;
    }
  }
}
.flex-end-row {
  display: flex;
  justify-content: flex-end;
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .dialog-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    .dialog-label {
      width: 150px;
      color: #161d26;
      margin-right: 10px;
      text-align: right;
    }
    .flex-content {
      flex: 1;
    }
  }
  .zankai-shouqi {
    color: var(--current-color);
    cursor: pointer;
    margin-right: 10px;
  }
}
.flex-end {
  justify-content: flex-end;
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0px;
}
.dialog-row-allChecked {
  position: absolute;
  right: 0px;
  top: -40px;
}
.zong-di-list-continer {
  height: auto;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  &:after {
    content: '';
    width: 340px;
    height: auto;
    position: absolute;
    left: 0;
    top: 0;
    background: inherit;
    z-index: 2;
    backdrop-filter: blur(10px);
  }
  .list-title {
    width: 355px;
    height: 40px;
    line-height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 0px 0px;
    display: flex;
    justify-content: center;
    align-content: center;
    position: relative;
    border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
    z-index: 3;
    .filter-btn {
      position: absolute;
      left: 16px;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
    }
    .filter-btn:hover {
      color: var(--current-color);
    }
    .title {
      width: 56px;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .icon {
      width: 14px;
      height: 14px;
      position: absolute;
      right: 12px;
      top: 12px;
    }
  }
  .list-search {
    border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
    padding: 12px;
    width: 355px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-content: center;
    position: relative;
    z-index: 3;
    .el-input {
      :deep(.el-input__inner) {
        color: #fff;
        width: 100%;
        height: 32px;
        background-color: rgb(255, 255, 255, 0.3);
        border-radius: 4px;
        border-color: none;
        border: none;
        z-index: 8;
        &::placeholder {
          color: rgb(255, 255, 255, 0.5);
        }
        /* 谷歌 */
        &::-webkit-input-placeholder {
          color: rgb(255, 255, 255, 0.5);
        }
        /* 火狐 */
        &::-moz-placeholder {
          color: rgb(255, 255, 255, 0.5);
        }
        /*ie*/
        &::-ms-input-placeholder {
          color: rgb(255, 255, 255, 0.5);
        }
      }
      :deep(.el-input__prefix) {
        height: 32px;
        line-height: 32px;
        left: 12px;
        color: #ffffff;
      }
    }
    .search-button {
      width: 40px;
      height: 24px;
      background: var(--current-color);
      border-radius: 4px 4px 4px 4px;
      opacity: 1;
      margin-left: 8px;
      padding-left: 8px;
      margin-top: 4px;
    }
  }
  /*滚动条样式*/
  .list-content::-webkit-scrollbar {
    width: 4px;
  }
  .list-content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .list-content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
  .list-content {
    width: 355px;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 3;
    .content-item {
      width: 340px;
      height: 44px;
      border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      color: #ffffff;
      padding: 16px;
      position: relative;
      z-index: 4;
      cursor: pointer;
      .item-name {
        width: 42px;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      .icon-right {
        position: absolute;
        right: 16px;
      }
    }
  }
  .list-content-active {
    background: rgb(0, 0, 0, 0.5);
    color: var(--current-color) !important;
  }
  .active-span {
    color: var(--current-color) !important;
  }
  .list-pagination {
    width: 340px;
    .pagination-container {
      z-index: 3;
      background-color: transparent !important;
      height: 40px;
      width: 350px;
      display: flex;
      // justify-content: center;
      // flex-wrap: wrap;
      :deep(.el-pagination) {
        top: 0;
        height: 40px;
        // width: 300px;
        .el-pagination__total {
          color: #dbe7ee;
          width: 70px;
        }
        .btn-prev {
          background: transparent !important;
          background-color: transparent !important;
        }
        .btn-next {
          background: transparent !important;
          background-color: transparent !important;
        }
        .el-pager {
          width: 130px;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-right: 7px;
          .number {
            background-color: transparent;
            color: #dbe7ee;
            opacity: 1;
            width: 20px;
          }
          .active {
            width: 32px;
            // height: 32px;
            line-height: 32px;
            background: var(--current-color) !important;
            border-radius: 5px 5px 5px 5px;
            opacity: 1;
          }
          li {
            margin: 0;
          }
        }
        .el-pagination__jump {
          background-color: transparent;
          color: #dbe7ee;
          .el-pagination__editor.el-input {
            background-color: transparent !important;
            height: 20px;
            line-height: 20px;
            width: 30px;
          }
          .el-input {
            .el-input__inner {
              background-color: transparent !important;
              height: 20px;
              line-height: 20px;
              width: 30px;
            }
          }
        }
      }
      :deep(.el-pagination .el-select .el-input .el-input__inner) {
        background-color: transparent;
        border-color: transparent;
        color: rgb(255, 255, 255, 0.7);
      }
      :deep(.el-pagination__sizes .el-input .el-input__inner:hover) {
        border-color: transparent;
      }
      :deep(.el-pagination.is-background .el-pager li) {
        background-color: transparent;
        color: #fff;
      }
    }
  }
  .page-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    .page-total {
      color: #dbe7ee;
      margin-left: 17px;
      font-size: 12px;
    }
    .page-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
  }

  .list-btn {
    width: 340px;
    z-index: 3;
    // display: flex;
    // justify-content: center;
    margin-top: 27px;
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 24px;
    .el-button {
      z-index: 4;
    }
  }
  .zongdi-icon {
    display: flex;
    justify-content: center;
    align-self: center;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      margin: auto;
    }
  }
}
</style>
