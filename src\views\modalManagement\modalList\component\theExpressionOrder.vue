<template>
  <div>
    <el-dialog
      title="表达式排序"
      v-model="dialogVisible"
      width="90%"
      :before-close="handleOrderClose"
      :close-on-click-modal="false"
      @open="getfieldOrderList"
    >
      <div class="top">上下拖动进行排序 <span style="color: red; padding-left: 16px">注:数字越小代表最先计算，*代表未排序</span></div>
      <div style="position: absolute; right: 16px; top: 16px"><el-button type="success" @click="handleQuote" plain>自动排序（参考）</el-button></div>
      <div class="handle-div">
        <div class="head-xh">优先级(从高到低)</div>
        <div class="item-title tree">节点名称</div>
        <div class="item-title group">属性组名称</div>
        <div class="item-title guanl">关联类型</div>
        <div class="item-title ziduan">字段名</div>
        <div class="item-title expression">表达式</div>
        <div class="item" style="width: 150px; display: flex; justify-content: center">操作</div>
      </div>
      <div class="order-content" :style="{ height: height }">
        <draggable :list="fieldOrderListData" itemKey="id" @end="handleOrederEnd" v-if="fieldOrderListData">
          <template #item="{ element, index }">
            <div
              class="flex-item"
              @click="clickOne(element, index)"
              @mouseenter="handleMouseEnter(element, index)"
              @mouseleave="handleMouseLeave(element, index)"
            >
              <div class="flex-xh">{{ element.seq === 0 ? '*' : index + 1 }}</div>
              <div class="item tree">{{ element.ruleName }}</div>
              <div class="item group">{{ element.groupName }}</div>
              <div class="item guanl">{{ filterLinkType(element.linkType) }}</div>
              <div class="item ziduan">{{ element.fieldName }}({{ element.fieldModel.fieldCn }})</div>
              <div class="item expression" v-if="!element.isExpression">{{ element.expression }}</div>
              <div class="item expression" v-else>
                <el-input placeholder="请输入表达式" v-model.trim="expressionValue" clearable @change="handleEnterExpression($event, element)">
                </el-input>
              </div>
              <div class="item" style="width: 150px; display: flex; justify-content: center">
                <el-button type="primary" size="small" @click="handleEditExpression(element)">编辑</el-button>
                <el-button v-show="element.isHandle && index != 0" type="success" size="small" @click="handleChangePostion(element, index, 1)"
                  >置顶</el-button
                >
                <el-button
                  v-show="element.isHandle && index != fieldOrderListData.length - 1"
                  type="info"
                  size="small"
                  @click="handleChangePostion(element, index, 2)"
                  >置底</el-button
                >
              </div>
            </div>
          </template>
        </draggable>
        <div v-else style="width: 100%; margin-top: 100px; text-align: center">暂无表达式</div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleOrderClose">取 消</el-button>
          <el-button type="primary" @click="handleOrderSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, defineProps, defineEmits, provide, reactive } from 'vue';
// @ts-ignore
import { fieldOrderList, insertFieldOrderList, saveFieldQuote } from '@/api/fieldManagement/index';
import draggable from 'vuedraggable';
import { useUserStore } from '@/store/modules/user';
import { Index } from 'mathjs';

// 定义 props
const props = defineProps<{
  orderVisible: boolean;
  moduleId: number;
}>();

// 定义 emit
const emit = defineEmits(['closeOrder']);

const dialogVisible = computed({
  get() {
    return props.orderVisible;
  },
  set(value) {
    // 触发关闭事件
    emit('closeOrder');
  }
});

const store = useUserStore();

// 定义响应式数据
const fieldOrderListData = ref();
const expressionValue = ref('');
const height = ref(`${window.innerHeight - 340}px`);

// 定义过滤器
const filterLinkType = (val: number): string => {
  switch (val) {
    case 2:
      return '面共享';
    case 3:
      return '线共享';
    case 4:
      return '点共享';
    case 5:
      return '子要素-线';
    case 6:
      return '子要素-点';
    case 7:
      return '无图形';
    default:
      return '普通';
  }
};

// 获取用户信息
const user = store.user;

// 获取表达式的字段
const getfieldOrderList = async () => {
  const data = {
    moduleId: props.moduleId,
    // 假设 user 类型定义不准确，明确 user 类型并添加类型断言
    companyId: (user as { companyId: any }).companyId
  };
  try {
    const res = await fieldOrderList(data);
    if (res.code === 200) {
      fieldOrderListData.value = res.data.map((item: any) => {
        if (item) {
          return {
            ...item,
            isExpression: false
          };
        }
        // 处理空值，返回默认对象
        return {
          seq: 0,
          ruleName: '',
          groupName: '',
          linkType: 0,
          fieldName: '',
          expression: '',
          isExpression: false
        };
      });
    } else {
      ElMessage({
        showClose: true,
        message: res.msg,
        type: 'error'
      });
    }
  } catch (error) {
    ElMessage({
      showClose: true,
      message: '请求出错',
      type: 'error'
    });
  }
};

// 自动排序（参考）
const handleQuote = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  const params = {
    moduleId: props.moduleId,
    // 假设 user 类型定义不准确，明确 user 类型并添加类型断言
    companyId: (user as { companyId: any }).companyId
  };
  try {
    const res = await saveFieldQuote(params);
    loading.close();
    if (res.code === 200) {
      const text = res.data;
      ElMessageBox.alert(text, '标题名称', {
        confirmButtonText: 'OK',
        // 由于找不到 'Action' 类型，将其替换为 'any' 类型以避免类型错误
        callback: (action: any) => {
          getfieldOrderList();
        }
      });
    } else {
      ElMessage({
        showClose: true,
        message: res.msg,
        type: 'error'
      });
    }
  } catch (error) {
    loading.close();
    ElMessage({
      showClose: true,
      message: '请求出错',
      type: 'error'
    });
  }
};

// 排序保存结果
const handleOrderSubmit = async () => {
  const list1 = fieldOrderListData.value.filter((item) => item.seq === 0);
  if (list1.length > 0) {
    ElMessage({
      showClose: true,
      message: '排序未结束',
      type: 'error'
    });
    return;
  }
  const list = fieldOrderListData.value.map((item, index) => ({
    ...item,
    seq: index + 1
  }));
  try {
    const res = await insertFieldOrderList(list);
    if (res.code === 200) {
      ElMessage({
        showClose: true,
        message: '排序成功!',
        type: 'success'
      });
      handleOrderClose();
    } else {
      ElMessage({
        showClose: true,
        message: res.msg,
        type: 'error'
      });
    }
  } catch (error) {
    ElMessage({
      showClose: true,
      message: '请求出错',
      type: 'error'
    });
  }
};

// 排序结束处理
const handleOrederEnd = (event: any) => {
  const oldIndex = event.oldIndex;
  const newIndex = event.newIndex;
  if (oldIndex !== newIndex) {
    fieldOrderListData.value[newIndex].seq = newIndex + 1;
  }
};

// 编辑表达式的保存事件
const handleEnterExpression = (val: string, item: { expression: string; isExpression: boolean }) => {
  const oldExpression = item.expression;
  if (item.isExpression && val) {
    item.expression = val;
  } else {
    item.expression = oldExpression;
  }
  item.isExpression = false;
};

// 编辑表达式
const handleEditExpression = (item: { expression: string; isExpression: boolean }) => {
  fieldOrderListData.value.forEach((item) => {
    item.isExpression = false;
  });
  expressionValue.value = item.expression;
  item.isExpression = true;
};

// 关闭弹框
const handleOrderClose = () => {
  emit('closeOrder');
  expressionValue.value = '';
};

// 点击单项
const clickOne = (item: any, index: number) => {
  item.seq = index + 1;
};

// 提供过滤器给模板使用
provide('filterLinkType', filterLinkType);

// 监听窗口大小变化
watch(
  () => window.innerHeight,
  () => {
    height.value = `${window.innerHeight - 300}px`;
  }
);

/**
 * 鼠标移入事件
 * @param item 当前内容
 * @param index 索引
 */
const handleMouseEnter = (item: any, index: number) => {
  item.isHandle = true;
};

/**
 * 鼠标移出事件
 * @param item 当前内容
 * @param index 索引
 */
const handleMouseLeave = (item: any) => {
  item.isHandle = false;
};

/**
 * 置顶或者置底操作
 * @param item 操作的内容
 * @param index 下标
 * @param type 类型 1:置顶 2:置底
 */
const handleChangePostion = (item: any, index: number, type: number) => {
  if (type === 1) {
    // 置顶
    moveElement(fieldOrderListData.value, index, 0);
  } else if (type === 2) {
    // 置底
    moveElement(fieldOrderListData.value, index, fieldOrderListData.value.length - 1);
  }
};

/**
 * 移动到指定位置
 * @param arr 操作的数组
 * @param fromIndex 目标下标
 * @param toIndex 结果下标
 * @returns 结果数组
 */
function moveElement(arr: any, fromIndex: number, toIndex: number) {
  const element = arr.splice(fromIndex, 1)[0]; // 移除元素
  arr.splice(toIndex, 0, element); // 插入到指定位置
  return arr;
}

// 挂载时获取数据
onMounted(() => {
  if (props.orderVisible) {
    getfieldOrderList();
  }
});

// 暴露给模板使用
defineExpose({
  getfieldOrderList,
  dialogVisible
});
</script>

<style lang="scss" scoped>
.top {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #edf4fb;
  margin-bottom: 10px;
  padding: 0px 16px;
  border-radius: 4px;
}
.handle-div {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #f5f7fa;
  padding: 0px 16px;
  .head-xh {
    width: 130px;
  }
  .item-title {
    padding: 0px 16px;
  }
  .tree {
    min-width: 120px;
  }
  .guanl {
    min-width: 120px;
  }
  .ziduan {
    min-width: 240px;
  }
  .group {
    min-width: 120px;
  }
  .expression {
    width: calc(100% - 650px);
  }
  margin-bottom: 10px;
}
.order-content {
  // height: 500px;
  overflow: auto;
  .flex-item {
    padding: 0px 16px;
    min-height: 32px;
    display: flex;
    align-items: center;
    cursor: move;
    .flex-xh {
      width: 130px;
    }
    .item {
      padding: 0px 16px;
    }
    .tree {
      min-width: 120px;
    }
    .guanl {
      min-width: 120px;
    }
    .ziduan {
      min-width: 240px;
    }
    .group {
      min-width: 120px;
    }
    .expression {
      width: calc(100% - 650px);
    }
    border-bottom: #edf4fb solid 1px;
  }
}
</style>
