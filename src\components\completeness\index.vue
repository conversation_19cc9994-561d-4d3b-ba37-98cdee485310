<!-- 完整性检查 -->
<template>
  <div class="completeness-main">
    <el-dialog
      :title="`【${parcelName}】数据完整性`"
      v-model="completenessDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="875px"
      :before-close="handleClose"
    >
      <div v-loading="loading">
        <div class="empty-div" v-if="mustYsNum == 0">
          <el-empty description="当前模块暂无必填数据"></el-empty>
        </div>
        <div class="dialog-box" v-else>
          <div class="left">
            <div class="title-div"><span class="normal-sapn">结构树</span></div>
            <div class="content">
              <div v-if="list.length == 0" class="empty-span">暂无要素数据</div>
              <div v-for="(item, index) in list" :key="index">
                <div
                  class="flex-row"
                  :class="{ 'flex-active': item.checked, 'error-span': item.empty }"
                  @click="changeAtt(item)"
                  v-show="item.mustAttrNum"
                >
                  <div class="label" :class="{ 'success': !item.empty, 'error': item.empty  }">{{ item.parcelName }}({{ item.ruleName }})</div>
                </div>
              </div>
            </div>
          </div>
          <div class="center">
            <div class="title-div"><span class="normal-sapn">属性组</span></div>
            <div class="content">
              <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
              <div v-for="(item, index) in attrbutionGroup" :key="index" @click="changeField(item)">
                <div class="flex-row" :class="{ 'flex-active': item.checked }" v-show="item.mustFieldNum">
                  <div class="label" :class="{ 'success': !item.empty, 'error': item.empty }">
                    {{ item.typeName }}
                    <span v-if="item.ruleAttribution && (item.ruleAttribution.type == 'graphicalPoint' || item.ruleAttribution.type == 'commonPoint')"
                      >(点)</span
                    >
                    <span v-if="item.ruleAttribution && (item.ruleAttribution.type == 'graphicalLine' || item.ruleAttribution.type == 'commonLine')"
                      >(线)</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="title-div"><span class="normal-sapn">字段</span></div>
            <div class="content">
              <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
              <div v-for="(item, index) in fieldList" :key="index">
                <div class="flex-row" v-show="item.required != 2">
                  <div class="label" :class="{ 'success': item.appValue, 'error': !item.appValue }">
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div class="ico" style="width: 30px" :class="{ 'success': item.appValue, 'error': !item.appValue }">
                    <i class="el-icon-check" v-if="item.appValue">
                      <el-icon><Select /></el-icon
                    ></i>

                    <i class="el-icon-close" v-else>
                      <el-icon><Close /></el-icon
                    ></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getPlaceDetail } from '@/api/modal';
// --- 数据声明 ---
const loading = ref(false);
const list = ref([]); //完整性列表
const attrbutionGroup = ref([]);
const fieldList = ref([]);
const mustYsNum = ref(0); //要素必填数据量
const parcelName = ref('');
// ---emit ---

const emit = defineEmits<{
  (e: 'closeCompleteDialog'): void;
}>();

// --- props 声明 ---
interface Props {
  zdid: any;
  completenessDialog: boolean;
  ruleTree: any[];
}
const props = withDefaults(defineProps<Props>(), {
  zdid: '',
  completenessDialog: false,
  ruleTree: () => []
});
const completenessDialogCopy = computed(() => props.completenessDialog);

// --- watch ---
watch(
  completenessDialogCopy,
  (newVal) => {
    if (newVal) {
      getDetail();
    }
  },
  { deep: true }
);
// --- 方法声明 ---
const handleClose = () => {
  emit('closeCompleteDialog');
};

/**
 * 获取详情
 */
const getDetail = () => {
  list.value = [];
  loading.value = true;
  getPlaceDetail(props.zdid).then((res) => {
    loading.value = false;
    parcelName.value = res.data.parcelName;
    if (res.code == 200) {
      initComplete(res.data);
      list.value.reverse();
      // 初始化选中第一个有必填字段属性组的要素 mustAttrNum
      for (let index = 0; index < list.value.length; index++) {
        if (list.value[index].mustAttrNum) {
          list.value[index].checked = true;
          attrbutionGroup.value = list.value[index].fieldGroupModels;
          for (let j = 0; j < list.value[index].fieldGroupModels.length; j++) {
            if (list.value[index].fieldGroupModels[j].mustFieldNum) {
              list.value[index].fieldGroupModels[j].checked = true;
              fieldList.value = list.value[index].fieldGroupModels[j].fieldModelList;
              break;
            }
          }
          break;
        }
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 处理数据完整性验证
 * @param obj
 */
const initComplete = (obj) => {
  if (obj.list && obj.list.length != 0) {
    obj.list.forEach((v) => {
      initComplete(v);
    });
  }
  let emptyAttrNum = 0; //要素对应的属性组是否存在空数据
  let mustAttrNum = 0; //要素对应必填的属性组数量
  // 对比数据完整性 1、先把具体数据放入对应的属性组 2再去循环属性组的字段判断字段是否有值
  obj.fieldGroupModels.forEach((v) => {
    let emptyNum = 0; //属性组对应的字段是否存在空数据
    let mustFieldNum = 0; //属性组对应的必填字段数量
    if (obj.fieldInstanceModels.length != 0) {
      for (let index = 0; index < obj.fieldInstanceModels.length; index++) {
        if (obj.fieldInstanceModels[index].linkId == v.linkId) {
          v.fieldValue = obj.fieldInstanceModels[index].attribution;
          break;
        }
      }
    }
    v.fieldModelList.forEach((k) => {
      if (k.required != 2) {
        //首先看是否必填
        // 具体到某个字段进行完整性验证 需要处理特殊数据 如身份证识别、动植物识别、表格等...
        if (k.valueMethod == 'idCardScan') {
          const checkList = k.attribution.list; //身份证识别选择的内容
          checkList.forEach((o) => {
            if (v.fieldValue && !v.fieldValue[k.fieldName + '_' + o]) {
              emptyNum++;
            } else if (!v.fieldValue) {
              emptyNum++;
            }
          });
        } else {
          if (v.fieldValue) {
            k.appValue = v.fieldValue[k.fieldName];
            if (!v.fieldValue[k.fieldName]) {
              emptyNum++;
            }
          } else {
            emptyNum++;
          }
        }
        mustFieldNum++;
      }
    });
    if (emptyNum > 0) {
      //属性组对应空的字段 必填字段才计算
      v.empty = emptyNum;
      emptyAttrNum++;
    }
    v.mustFieldNum = mustFieldNum; //属性组对应必填的字段数量
    if (v.mustFieldNum > 0) {
      mustAttrNum++;
    }
  });
  if (emptyAttrNum > 0) {
    obj.empty = emptyAttrNum;
  }
  obj.mustAttrNum = mustAttrNum; //要素对应的属性组必填项
  if (obj.mustAttrNum > 0) {
    //如果要素有必填 则++
    mustYsNum.value++;
  }
  list.value.push(obj);
};

/**
 * 选择属性组
 */
const changeAtt = (item) => {
  list.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  attrbutionGroup.value = item.fieldGroupModels;
  // 给第一个有必填字段的属性组 设置为选中
  for (let index = 0; index < attrbutionGroup.value.length; index++) {
    if (attrbutionGroup.value[index].mustFieldNum) {
      attrbutionGroup.value[index].checked = true;
      fieldList.value = attrbutionGroup.value[index].fieldModelList;
      break;
    }
  }
};
/**
 * 改变属性组
 */
const changeField = (item) => {
  attrbutionGroup.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  fieldList.value = item.fieldModelList;
};
</script>
<style lang="scss" scoped>
.empty-div {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-box {
  height: 500px;
  border: 1px solid rgba(219, 231, 238, 1);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  .left {
    flex: 2;
  }
  .center {
    flex: 2;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .right {
    flex: 3;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .title-div {
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    .normal-sapn {
      margin-left: 20px;
    }
  }
  .content-handle {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    padding: 0px 25px;
    justify-content: space-between;
  }
  .content {
    height: calc(100% - 77px);
    padding: 0px 8px;
    width: calc(100% - 16px);
    margin-left: 8px;
    overflow: auto;
    :deep(.el-tree-node__content) {
      height: 32px;
      font-size: 12px;
    }
    .empty-span {
      color: #909399;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .gry-span {
      color: #909399 !important;
    }
    .flex-row {
      min-height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      font-size: 12px;
      :deep(.el-checkbox) {
        // 最后一个复选框的特殊样式
        &:last-of-type {
          padding: 5px 0; // 移除多余的px单位
        }

        // 标签样式（合并重复定义）
        .el-checkbox__label {
          display: inline-grid;
          white-space: pre-line;
          word-wrap: break-word;
          overflow: hidden;
          font-size: 12px;
        }
      }
      .label {
        color: rgba(22, 29, 38, 1);
        font-size: 12px;
        padding-left: 12px;
        flex: 1;
      }
      .ico {
        width: 70px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        padding-right: 8px;
      }
      .error-span {
        color: #f56c6c !important;
      }
      .success {
        color: #67c23a;
      }
      .error {
        color: #f56c6c;
      }
    }
    .flex-row:hover {
      background-color: #f5f7fa;
    }
    .flex-active {
      background: #edf4fb;
    }
    .check-item {
      height: auto;
      align-items: flex-start;
    }
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
