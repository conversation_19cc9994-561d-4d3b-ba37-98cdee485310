<!-- 新建项目 -->
<template>
  <container-card>
    <div class="addProject-main">
      <div class="content" id="box" ref="box">
        <!-- 左侧列表的位置 -->
        <div class="column left" id="left">
          <div class="list-content" v-show="list.length != 0">
            <div class="list-item" v-for="(item, index) in list" :key="index">
              <div class="item-title" :class="{ 'active': item.checked }" @click="chooseOnece(item)">
                <span>{{ item.parcelName }}</span>
                <el-icon v-show="!item.checked">
                  <el-icon><ArrowRight /></el-icon>
                </el-icon>
                <el-icon v-show="item.checked">
                  <el-icon><ArrowUp /></el-icon>
                </el-icon>
              </div>
              <!-- 显示的树 -->
              <div class="item-tree" v-show="item.checked">
                <el-tree
                  :data="nowTreeNode"
                  node-key="ruleId"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  default-expand-all
                  highlight-current
                  :current-node-key="defaultExpand"
                  @node-click="checkedNode"
                >
                  <template #default="{ data }">
                    <span class="custom-tree-node">
                      <authImg
                        :authSrc="`${baseUrl}${data.iconUrl}?att=1`"
                        :width="'20px'"
                        :height="'20px'"
                        v-if="data.iconUrl && data.iconUrl.includes('_blob')"
                      />
                      <svg-icon
                        v-else
                        class="svg-ico"
                        :class="{
                          'no-span': !data.geomArcgis,
                          'checked-span': data.ruleId == defaultExpand
                        }"
                        :icon-class="data.iconUrl"
                      />
                      <div
                        class="tree-span"
                        :class="{
                          'no-span': !data.geomArcgis,
                          'checked-span': data.ruleId == defaultExpand
                        }"
                      >
                        {{ data.parcelName }}
                      </div>
                    </span>
                  </template>
                </el-tree>
              </div>
            </div>
          </div>
          <el-empty description="暂无数据" v-show="list.length == 0"></el-empty>
          <div class="bottom-handle">
            <el-button type="primary" :disabled="!isAllowHandle" icon="Plus" @click="addData">新建</el-button>
            <el-button type="primary" icon="Check" @click="save">保存</el-button>
          </div>
        </div>
        <div class="resizer left-resizer" id="leftResize">⋮</div>
        <!-- 中间的地图位置 -->
        <div class="column middle" id="middle" ref="gisMapRef"></div>
        <div class="resizer left-resizer" id="RightResize" v-show="isLinyeInfo">⋮</div>
        <!-- 右侧的属性组的位置 -->
        <div class="column right" id="right" v-show="isLinyeInfo">
          <msgInfo ref="msgInfoRef" :checkedLinye="nowEditNode" @closeLinyeInfo="handleCloseLinyeInfo"></msgInfo>
        </div>
      </div>
      <!-- 新建树弹窗 多个跟节点才会出现 -->
      <el-dialog title="新建树" v-model="addTreeDialog" width="30%" :close-on-click-modal="false" :before-close="handleCloseAddTreeDialog">
        <div class="tree-list" v-for="(item, index) in tree" :key="index" @click="chooseOneNode(item)">
          <div class="tree-item">{{ item['typeName'] }}</div>
        </div>
      </el-dialog>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import { loadModules } from 'esri-loader';
import { TDT_BASE_URL, TDT_TOKEN } from '@/constants';

// 扩展 Window 接口
declare global {
  interface Window {
    screenHeight: number;
  }
}

const tiandituBaseUrl = TDT_BASE_URL; //天地图服务地址
const token = TDT_TOKEN; //天地图管网申请token
let fontLayer = null; //文字layer和点
let graphicsLayer: any = null; //所有宗地layer
let childGraphics = null; //查询详情 子要素图形layer
let degGraphics = null; //方位角layer
let labelLayer = null; //标注层
let jzdzbLayer = null; //界址点坐标 高亮用
import authImg from '@/components/authImg/index.vue';
import { selectRules } from '@/api/modal';
import { saveSimple } from '@/api/project';
let tiledLayer = null; //影像
let tiledLayerAnno = null; //影像标记
let normalLayer = null; //矢量底图
let normalAnno = null; //矢量标记
let sketch = null;
import msgInfo from './components/msgInfo.vue';
import { geojsonToWKT } from '@terraformer/wkt';
import { useRoute, useRouter } from 'vue-router';
import { useAppStore } from '@/store';
const appStore = useAppStore();
const route = useRoute();
const router = useRouter();
const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

// ---定义变量 ---
let map: any = null; //地图容器
let view: any = null; //mapview
const mapWidth = ref(''); //左侧地图宽度
const attrWidth = ref(''); //右侧属性宽度
const tree = ref([]); //当前模块树结构
const addTreeDialog = ref(false); //新建根节点树弹窗
const nowNode = ref<any>({}); //当前新增的根节点
const list = ref<any[]>([]); //列表数据
const isAllowHandle = ref(false); //是否允许新增
const nowTreeNode = ref<any[]>([]); //当前显示树数据
const defaultProps = ref({
  children: 'list',
  label: 'parcelName'
});
const defaultExpand = ref(0); //默认展开
//当前编辑的节点
const nowEditNode = ref<any>({
  parcelName: ''
});
const mainHeight = ref(window.innerHeight); //主高度
const isLinyeInfo = ref(false); //是否展示详情
const gisMapRef = ref(null); //地图容器
const msgInfoRef = ref<any>(null); //详情弹窗

onMounted(() => {
  // 拖动左侧的div 数据
  handleDragControllerLeftDiv();
  // 拖动右侧的div 数据
  handleDragControllerRightDiv();
  // window.onresize:浏览器尺寸变化响应事件
  window.onresize = () => {
    return (() => {
      // window.innerHeight:浏览器的可用高度
      window.screenHeight = window.innerHeight;
      mainHeight.value = window.screenHeight;
    })();
  };
  initMap();
});

// ---定义方法 ---
const initMap = () => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine',
      'esri/widgets/Sketch'
    ],
    config
  ).then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine, Sketch]) => {
    //球面墨卡托投影矢量底图
    tiledLayer = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      }),
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          // return `http://t0.tianditu.com/DataServer?T=img_w&x=${col}&y=${row}&l=${level}&tk=${token}`
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=img_w/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
        } else {
          return '';
          // return "http://t" +(207748 % 8) +".tianditu.com/DataServer?T=img_w/wmts&x="+ col +"&y=" +110397 +"&l="+18+"&tk=" + token
        }
      }
    });
    //矢量标注(球面墨卡托投影)
    tiledLayerAnno = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cia_w?T=vec_c/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      }),
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=cia_w?T=vec_c/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
        } else {
          return '';
        }
      }
    });
    //经纬度投影 矢量底图
    normalLayer = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });
    //矢量注记
    normalAnno = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });
    map = new Map({
      // topo-vector
      // basemap: "satellite",
      basemap: {
        baseLayers: [tiledLayer, tiledLayerAnno, normalLayer, normalAnno]
      },
      // ground: "world-elevation",
      logo: false,
      spatialReference: {
        wkid: 102100
      }
    });
    view = new MapView({
      //这个是让地图显示在id为map_contentView的标签上，是id叫他不是其他的
      container: gisMapRef.value,
      map: map,
      center: [106.62062, 26.65129],
      zoom: 18,
      ui: {
        // components: ["zoom", "compass"]
        components: []
      },
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });
    //设置最大缩放等级
    view.constraints = {
      minZoom: 2,
      maxZoom: 24
    };
    const compassWidget = new Compass({ view: view });
    view.ui.add(compassWidget, 'bottom-right');
    const zoom = new Zoom({
      view: view
    });
    view.ui.add(zoom, 'bottom-right');
    view.ui.remove('attribution');
    const scaleBar = new ScaleBar({
      view: view,
      unit: 'metric',
      style: 'line'
    });
    // Add widget to the bottom left corner of the view
    view.ui.add(scaleBar, {
      position: 'bottom-right'
    });
    getData();
    //添加文字标注layer到map
    fontLayer = new GraphicsLayer({
      id: '234',
      visible: false
    });
    map.add(fontLayer, 9999);
    //添加文字标注layer到map
    labelLayer = new GraphicsLayer({
      id: 'labelLayer',
      minScale: 5000
    });
    map.add(labelLayer, 9999);
    //添加宗地图形layer到map
    graphicsLayer = new GraphicsLayer({
      id: '123'
    });
    map.add(graphicsLayer);
    //子要素layer
    childGraphics = new GraphicsLayer({
      id: 'childEle'
    });
    map.add(childGraphics);
    degGraphics = new GraphicsLayer({
      id: 'degEle'
    });
    map.add(degGraphics);
    jzdzbLayer = new GraphicsLayer({
      id: 'jzd'
    });
    view.when(function () {
      sketch = new Sketch({
        layer: graphicsLayer,
        view: view,
        // graphic will be selected as soon as it is created
        creationMode: 'update'
        // availableCreateTools:["point","line","polygon", "rectangle", "circle"]
      });
      view.ui.add(sketch, 'top-left');
      sketch.visibleElements = {
        selectionTools: {
          'lasso-selection': false,
          'rectangle-selection': false
        },
        settingsMenu: false
      };
      sketch.on('create', function (event: any) {
        if (event.state === 'complete') {
          let geomArcgis: any = {};
          let itemGeo: any = {};
          const geometry = event.graphic.geometry;
          if (geometry.type == 'point') {
            itemGeo = {
              coordinates: [geometry.x, geometry.y],
              type: geometry.type.charAt(0).toUpperCase() + geometry.type.slice(1)
            };
          } else if (geometry.type == 'polygon') {
            itemGeo = {
              coordinates: geometry.rings,
              type: geometry.type.charAt(0).toUpperCase() + geometry.type.slice(1)
            };
          } else if (geometry.type == 'polyline') {
            itemGeo = {
              coordinates: geometry.paths,
              type: 'LineString'
            };
          }
          const geomWkb = `SRID=3857;${geojsonToWKT(itemGeo)}`;
          geomArcgis = {
            geomArcgis: JSON.stringify(geometry),
            geomWkb: geomWkb
          };
          if (nowEditNode.value.ruleId) {
            nowEditNode.value.geomArcgis = geomArcgis.geomArcgis;
            nowEditNode.value.geomWkb = geomArcgis.geomWkb;
            // 设置wkid
            nowEditNode.value.wkId = 3857;
          }
        }
      });
    });
  });
};

/**
 * 监听左侧可以左右拖动
 */
const handleDragControllerLeftDiv = () => {
  const resize = document.getElementById('leftResize') as any;
  const left = document.getElementById('left') as any;
  const middle = document.getElementById('middle') as any;
  const right = document.getElementById('right') as any;
  const box = document.getElementById('box') as any;
  resize.onmousedown = (e: any) => {
    //  坐标的起始位置
    const startX = e.clientX;
    //  左侧位置的起始宽度
    resize.left = left.offsetWidth;
    // 获取右侧的初始的宽度
    const rightWidth = right.offsetWidth;
    // 监听鼠标的拖动事件
    document.onmousemove = (e) => {
      // 鼠标拖动的终止位置
      const endX = e.clientX;
      // 移动的距离  = 终止位置 endX -开始位置 startX
      // 左侧和右侧分别应该有一个固定的区域
      //  左侧的最小的宽度
      let leftWidth = resize.left + (endX - startX);

      // 限制左边区域的最小宽度为30px
      if (leftWidth < 250) leftWidth = 250;
      // 中间宽度  = box的宽度 - (计算之后的左侧宽度 + 初始化的右侧跨度)
      // 设置左边区域的宽度
      left.style.width = leftWidth + 'px';
      // 设置中间区域的宽度
      middle.style.width = box.clientWidth - (leftWidth + rightWidth) + 'px';
      //  设置右侧区域的宽度
      // right.style.width = rightWidth + "px";
    };
    // 鼠标松开事件
    document.onmouseup = (evt) => {
      document.onmousemove = null;
      document.onmouseup = null;
      resize.releaseCapture && resize.releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
    };
    resize.setCapture && resize.setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
    return false;
  };
};
/**
 * 监听中间可以左右拖动
 */
const handleDragControllerRightDiv = () => {
  const resize = document.getElementById('RightResize') as any;
  const left = document.getElementById('left') as any;
  const middle = document.getElementById('middle') as any;
  const right = document.getElementById('right') as any;
  const box = document.getElementById('box') as any;

  resize.onmousedown = (e: any) => {
    //  坐标的起始位置
    const startX = e.clientX;
    //  左侧位置的起始宽度
    resize.left = right.offsetWidth;
    // 获取左侧的初始宽度
    const leftWidth = left.offsetWidth;
    // 监听鼠标的拖动事件
    document.onmousemove = (e) => {
      // 鼠标拖动的终止位置
      const endX = e.clientX;
      // 移动的距离  = 开始位置 startX - 终止位置 endX （初始位置大，不管是向左侧拖动 还是向右侧拖动 都是试用初始位置减 最后结束的位置）
      // 左侧和右侧分别应该有一个固定的区域
      // 右侧侧的最小的宽度
      let rightWidth = resize.left + (startX - endX);
      // 限制左边区域的最小宽度为30px
      if (rightWidth < 650) rightWidth = 650;
      //  设置右侧区域的宽度
      right.style.width = rightWidth + 'px';
      //  设置中间区域的宽度 = box的宽度 - (计算之后的右侧宽度 + 初始化的左侧宽度)
      middle.style.width = box.clientWidth - (rightWidth + leftWidth) + 'px';
      // 设置左侧区域的宽度
      // left.style.width = leftWidth + "px";
    };
    // 鼠标松开事件
    document.onmouseup = (evt) => {
      document.onmousemove = null;
      document.onmouseup = null;
      resize.releaseCapture && resize.releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
    };
    resize.setCapture && resize.setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
    return false;
  };
};
/**
 * 根据模块id获取模块规则树
 */
const getData = () => {
  const id = route.params.id;
  if (id) {
    selectRules({ moduleId: id }).then((res) => {
      isAllowHandle.value = true;
      tree.value = res.data;
    });
  }
};
/**
 * 新建数据
 */
const addData = () => {
  if (tree.value.length > 1) {
    //代表模块不止一个根节点 需要选择新增哪个跟节点
    addTreeDialog.value = true;
  } else {
    //代表模块只有一个跟节点 直接默认选择该根节点
    nowNode.value = tree.value[0];
    // disposeNode([nowNode.value]);
    addTreeDialog.value = true;
  }
};
/**
 * 关闭新建树弹窗
 */
const handleCloseAddTreeDialog = () => {
  addTreeDialog.value = false;
};
/**
 * 提交新建树弹窗
 */
const submitAddTreeDialog = () => {
  addTreeDialog.value = false;
};
/**
 * 当前选择的节点
 */
const chooseOneNode = (node: any) => {
  nowNode.value = node;
  disposeNode([node]);
  list.value.push(nowNode.value);
  addTreeDialog.value = false;
};
/**
 * 迭代树初始化 默认parcelName为ruleName
 */
const disposeNode = (list: any[]) => {
  list.forEach((v) => {
    v['parcelName'] = v['typeName'];
    v['ruleId'] = v['id'];
    delete v['id'];
    if (v['list']) {
      disposeNode(v.list);
    }
  });
};
/**
 * 选中某条数据
 */
const chooseOnece = (item: any) => {
  if (item.checked) {
    item.checked = false;
  } else {
    item.checked = true;
    nowTreeNode.value = [item];
    defaultExpand.value = item.ruleId;
    isLinyeInfo.value = true;
    nowEditNode.value = item;
    msgInfoRef.value.initData(nowEditNode.value);
  }
};
/**
 * 选中某条数据
 */
const checkedNode = (node: any) => {
  defaultExpand.value = node.ruleId;
  isLinyeInfo.value = true;
  nowEditNode.value = node;
  msgInfoRef.value.initData(nowEditNode.value);
};
/**
 * 关闭林业信息弹窗
 */
const handleCloseLinyeInfo = () => {
  isLinyeInfo.value = false;
};
/**
 * 迭代单独处理每个节点的属性组数据
 */
const disposeSubmitNode = (list: any[]) => {
  list.forEach((v) => {
    const fieldInstanceModels: any[] = [];
    // 属性组
    v.fieldGroupModelList.forEach((k: any) => {
      if (k.list && k.list.length > 0) {
        //多采
        k.list.forEach((q: any) => {
          const groupItem: any = {
            appId: 0,
            attribution: {},
            groupId: q.groupId,
            groupName: q.typeName,
            linkId: q.linkId,
            ruleAttribution: q.ruleAttribution
          };
          // 每个属性组的字段
          q.fieldModelList.forEach((w: any) => {
            if (w.content) {
              groupItem.attribution[`${w.fieldName}`] = w.content;
            }
          });
          fieldInstanceModels.push(groupItem);
        });
      } else {
        const groupItem: any = {
          appId: 0,
          attribution: {},
          groupId: k.id,
          groupName: k.typeName,
          linkId: k.linkId,
          ruleAttribution: k.ruleAttribution
        };
        // 每个属性组的字段
        k.fieldModelList.forEach((q: any) => {
          if (q.content) {
            groupItem.attribution[`${q.fieldName}`] = q.content;
          }
        });
        fieldInstanceModels.push(groupItem);
      }
    });

    v.fieldInstanceModels = fieldInstanceModels;
    // 任务id
    v.taskId = 0;
    // 删除id
    if (v.list.length != 0) {
      disposeSubmitNode(v.list);
    }
  });
};
/**
 * 提交数据
 */
const save = () => {
  list.value.forEach((v: any) => {
    disposeSubmitNode([v]);
  });
  saveSimple(list.value).then((res) => {
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
      appStore.setRefresh(true); // 设置需要刷新
      router.go(-1);
    } else {
      ElMessage({
        type: 'error',
        message: res.msg
      });
    }
  });
};
</script>
<style lang="scss" scoped>
.no-span {
  color: #ff3333 !important;
}
.checked-span {
  color: var(--current-color) !important;
}
:deep(.esri-view .esri-view-surface:focus::after) {
  outline: none !important;
}
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom .esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
:deep(.esri-zoom .esri-widget--button:hover) {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 10px;
}
:deep(.esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
.addProject-main {
  width: 100%;
  height: 100%;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    .column {
      min-width: 180px; /* 最小宽度 */
      height: 100%;
    }
    .left {
      min-width: 250px; /* 最小宽度 */
      // flex-grow: 0.5;
      width: 250px;
      // border-right: 1px solid #ededed;
      border: solid #e5e5e5 1px;
      height: 100%;
      position: relative;
      .bottom-handle {
        width: 100%;
        position: absolute;
        bottom: 0;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0px 10px;
        border-top: solid #e5e5e5 1px;
      }
      .list-content {
        height: calc(100% - 40px);
        overflow: auto;
        .list-item {
          font-size: 14px;
          .item-title {
            border-bottom: #e5e5e5 solid 1px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            cursor: pointer;
          }
          .active {
            color: #409eff;
          }
          .item-tree {
            .tree-span {
              font-size: 14px;
              margin-left: 12px;
            }
          }
          & :deep(.el-tree-node__content) {
            width: 512px; /* Added px unit */
            height: 44px;
            background: rgba(0, 0, 0, 0);
            border-radius: 0px 0px 0px 0px;
            .custom-tree-node {
              display: flex;
              align-items: center;
              position: relative;
              width: 100%;
              height: 44px;
              .zd-icon {
                width: 20px;
                .el-image {
                  & :deep(.el-image__inner) {
                    width: 20px;
                    height: 20px;
                    margin: 12px;
                    vertical-align: bottom;
                  }
                }
              }
              .zd-name {
                height: 20px;
                width: 300px;
                font-size: 14px;
                font-family:
                  PingFang SC-Medium,
                  PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 20px;
                margin-left: 12px;
              }
              .zd-leng {
                position: absolute;
                right: 40px;
                height: 20px;
                font-size: 14px;
                font-family:
                  PingFangSC-328080,
                  PingFang SC;
                font-weight: normal;
                color: rgba(255, 255, 255, 0.5);
                line-height: 20px;
              }
              .zd-arrow {
                position: absolute;
                right: 10px;
                height: 20px;
                font-size: 14px;
                font-family:
                  PingFangSC-328080,
                  PingFang SC;
                font-weight: normal;
                color: rgba(255, 255, 255, 0.5);
                line-height: 20px;
              }
            }
            &:hover {
              width: 512px; /* Added px unit */
              height: 44px;
              border-radius: 0px 0px 0px 0px;
              opacity: 1;
            }
          }
        }
      }
    }
    .right {
      min-width: 650px;
      width: 650px;
    }
    .middle {
      width: 60%;
      flex-grow: 2;
      border-right: 1px solid #ededed;
      // border-left: 1px solid #ededed;
    }
    .resizer {
      cursor: col-resize;
      float: left;
      position: relative;
      background-color: #d6d6d6;
      border-radius: 5px;
      margin-top: -10px;
      width: 10px;
      height: 40px;
      background-size: cover;
      background-position: center;
      /*z-index: 99999;*/
      font-size: 32px;
      color: white;
    }
    /*拖拽区鼠标悬停样式*/
    .resizer:hover {
      color: #409eff;
    }
  }
}
.tree-list {
  display: flex;
  flex-direction: column;
  overflow: auto;
  .tree-item {
    padding: 10px 20px;
    border-bottom: #e5e5e5 solid 1px;
    font-size: 14px;
    cursor: pointer;
  }
  .tree-item:hover {
    color: #409eff;
    background: rgba(0, 0, 0, 0.2);
  }
}
</style>
