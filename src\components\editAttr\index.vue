<!-- web编辑属性组的属性 可用于新增或编辑 -->
<template>
  <div class="editAttr-main">
    <div class="field-row" v-for="(item, index) in checkedAtt.fieldModelList" :key="index">
      <div class="field-label">
        <template v-if="item.valueMethod == 'idCardScan'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(身份证识别暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtzw'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(指纹识别暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtzwsb'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(植物识别暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtdwsb'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(动物识别暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtzsdl'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(追溯地类暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtsjy'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(数据源暂不支持)</span>
        </template>
        <template v-else-if="item.valueMethod == 'xtsjjt'">
          <span style="color: rgba(0, 0, 0, 0.5)">{{ item.fieldCn }}(数据截图暂不支持)</span>
        </template>
        <template v-else>
          {{ item.fieldCn }}
        </template>
        <span v-show="item.required == 1" style="color: red">*</span>
      </div>
      <!-- 输入框 -->
      <template v-if="item.valueMethod == 'input'">
        <el-input v-model="item.content" :placeholder="item.inputHint"></el-input>
      </template>
      <!-- 多行输入框 -->
      <template v-else-if="item.valueMethod == 'textarea'">
        <el-input v-model="item.content" :placeholder="item.inputHint" type="textarea" :autosize="{ minRows: 2, maxRows: 6 }"></el-input>
      </template>
      <!-- 行政区划 -->
      <template v-else-if="item.valueMethod == 'area'">
        <areaCodeTemp style="width: 100%" :defautCode="86" @changeCodeGetName="changeCodeGetName($event, item)"></areaCodeTemp>
        <el-input v-model="item.contentRemark" :placeholder="item.inputHint" style="margin-top: 5px"></el-input>
      </template>
      <!-- 数字输入框 -->
      <template v-else-if="item.valueMethod == 'number'">
        <el-input v-model="item.content" :placeholder="item.inputHint" type="number"></el-input>
      </template>
      <!-- date时间编辑器 -->
      <template v-else-if="item.valueMethod == 'date'">
        <el-date-picker v-model="item.content" type="date" value-format="timestamp" :placeholder="item.inputHint" style="width: 100%">
        </el-date-picker>
      </template>
      <!-- 时间选择 -->
      <template v-else-if="item.valueMethod == 'time'">
        <el-time-picker v-model="item.content" :placeholder="item.inputHint" style="width: 100%" value-format="HH:mm:ss"> </el-time-picker>
      </template>
      <!-- 日期区间 -->
      <template v-else-if="item.valueMethod == 'date-range'">
        <el-date-picker
          v-model="item.content"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          style="width: 100%"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </template>
      <!-- 下拉选择框 -->
      <template v-else-if="item.valueMethod == 'select'">
        <el-select v-model="item.content" :placeholder="item.inputHint">
          <el-option v-for="ite in item.attribution.options" :key="ite.value" :label="ite.label" :value="ite.value"> </el-option>
        </el-select>
      </template>
      <!-- 单选框 -->
      <template v-else-if="item.valueMethod == 'radio'">
        <el-radio-group v-model="item.content">
          <el-radio :value="ite.value" v-for="ite in item.attribution.options" :key="ite.value"></el-radio>
        </el-radio-group>
      </template>
      <!-- 多选框 -->
      <template v-else-if="item.valueMethod == 'checkbox'">
        <el-checkbox-group v-model="item.content">
          <el-checkbox :value="ite.value" v-for="ite in item.attribution.options" :key="ite.value"></el-checkbox>
        </el-checkbox-group>
      </template>
      <!-- 图片上传 -->
      <template v-else-if="item.valueMethod == 'upload'">
        <el-upload
          :action="`${baseUrl}/qjt/file/multi/upload`"
          :headers="headers"
          name="files"
          accept=".jpg,.png"
          list-type="picture-card"
          :limit="item.attribution.picNum"
          :on-preview="handlePictureCardPreview"
          :on-success="(response) => handleSuccessTP(response, item)"
          :on-remove="(file) => handleRemoveTP(file, item)"
          :on-exceed="(files, fileList) => handleExceed(files, fileList, item)"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </template>
      <!-- 附件上传 -->
      <template v-else-if="item.valueMethod == 'xtfj'">
        <el-upload
          class="upload-demo"
          :action="`${baseUrl}/qjt/file/multi/upload`"
          :on-success="(response) => handleSuccessFJ(response, item)"
          :on-remove="(file) => handleRemoveFJ(file, item)"
          multiple
          :before-upload="(file) => beforeAvatarUpload(file, item)"
          :limit="item.attribution.picNum"
          :on-exceed="(files, fileList) => handleExceed(files, fileList, item)"
          :file-list="item.content || []"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传{{ item.attribution.acceptType.join(',') }}文件</div>
        </el-upload>
      </template>
      <!-- 视频上传 -->
      <template v-else-if="item.valueMethod == 'xtvideo'">
        <!-- <el-upload
            class="upload-demo"
            ref="upload"
            :action="`${baseUrl}/qjt/file/multi/upload`"
            :on-change="(file) => handleChangeSP(file,item)"
            :file-list="item.content || []"
            :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
        </el-upload> -->
      </template>
      <!-- 身份证识别 改为直接输入数据 -->
      <!-- 签名 -->
      <template v-else-if="item.valueMethod == 'xtqm'">
        <webSignature :width="500" :height="200" @submitSign="submitSign($event, item)"></webSignature>
      </template>
      <!-- 表格 -->
      <template v-else-if="item.valueMethod == 'xttable'">
        <div class="table-handle">
          <el-button type="primary" size="mini">添加</el-button>
          <el-button type="danger" size="mini">删除</el-button>
        </div>
        <el-table :data="item.content" style="width: 100%; margin-top: 10px" border>
          <el-table-column v-for="(ite, idx) in item.attribution.children" :key="idx" :label="ite.fieldCn">
            <template slot-scope="scope">
              <template v-if="scope.row.valueMethod == 'upload'">
                <authImg
                  v-for="(o, odx) in scope.row[ite.fieldName]"
                  :key="odx"
                  :authSrc="`${baseUrl}${o.url}?att=1`"
                  :width="'20px'"
                  :height="'20px'"
                />
              </template>
              <template v-else>
                {{ scope.row[ite.fieldName] }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </div>
  </div>
</template>

<script>
import authImg from '../authImg';
import areaCodeTemp from '@/components/areaCodeTemp/index.vue';
import { getToken } from '@/utils/auth';
import webSignature from '../webSignature/index.vue';
import { base64ToFile } from '@/utils/validate';
import Axios from 'axios';
export default {
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      }
    };
  },
  props: ['checkedAtt', 'mainHeight'],

  watch: {
    checkedAtt: {
      handler(val) {},
      deep: true
    }
  },

  components: { authImg, areaCodeTemp, webSignature },

  computed: {
    height() {
      return this.mainHeight - 300;
    }
  },

  mounted() {},

  methods: {
    // 得到选中的行政区域名字
    changeCodeGetName(name, item) {
      this.$set(item, 'content', name);
    },
    handlePictureCardPreview(event, file) {
    },
    // 输入框正则验证
    verifyInput(value, item) {
      if (!item.regItem) {
        return value;
      } else {
        value = value.replace(item.regItem.pattern, '');
        return value;
      }
    },
    // 图片上传成功事件
    handleSuccessTP(response, item) {
      item.content.push({
        url: response.data[0].path,
        name: response.data[0].name
      });
    },
    // 图片上传移除事件
    handleRemoveTP(file, item) {
      let num = 0;
      for (let index = 0; index < item.content.length; index++) {
        if (file.response.data[0].path == item.content[index].url) {
          num = index;
          break;
        }
      }
      item.content.splice(num, 1);
    },
    // 图片上传超过最大限制提示
    handleExceed(files, fileList, item) {
      if (fileList.length >= item.attribution.picNum) {
        this.$message.error(`${item.fieldCn}最多允许上传${item.attribution.picNum}`);
      }
    },
    // 附件上传成功
    handleSuccessFJ(response, item) {
      item.content.push({
        url: response.data[0].path,
        name: response.data[0].name
      });
    },
    // 附件移除
    handleRemoveFJ(file, item) {
      let num = 0;
      for (let index = 0; index < item.content.length; index++) {
        if (file.response.data[0].path == item.content[index].url) {
          num = index;
          break;
        }
      }
      item.content.splice(num, 1);
    },
    // 上传前验证
    beforeAvatarUpload(file, item) {
      let flg = false;
      for (let index = 0; index < item.attribution.acceptType.length; index++) {
        if (file.type.includes(item.attribution.acceptType[index])) {
          flg = true;
          break;
        }
      }
      if (!flg) {
        this.$message.error(`不支持上传${file.type}格式！！！`);
      }
      return flg;
    },
    // 提交签名
    submitSign(img, item) {
      let file = base64ToFile(img, `${Date.now()}_签名`);
      // 创建FormData
      let formData = new FormData();
      formData.append('files', file);
      Axios({
        method: 'post',
        url: `${this.baseUrl}/qjt/file/multi/upload`,
        headers: {
          Authorization: 'Bearer ' + getToken(),
          'Access-Control-Allow-Origin': '*'
        },
        data: formData
      }).then((res) => {
        item.content = res.data.data[0].path;
      });
    },
    // 视频上传
    handleChangeSP(file, item) {
    },
    // 提交数据给父组件
    submitAttr() {
      // 需要对特殊的进行处理
      let parmas = JSON.parse(JSON.stringify(this.checkedAtt));
      parmas.fieldModelList.forEach((v) => {
        if (v.content) {
          if (v.content.length != 0 && v.valueMethod == 'checkbox') {
            //多选框要转换成英文逗号分隔的字符串
            v.content = v.content.join(',');
          } else if (v.content.length != 0 && v.valueMethod == 'date-range') {
            //日期区间也要用-拼接
            v.content = v.content.join('至');
          }
        }
      });
      return parmas;
    }
  }
};
</script>
<style lang="scss" scoped>
.editAttr-main {
  width: 100%;
  height: 100%;
}
.field-row {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  .field-label {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    display: block;
    margin-bottom: 5px;
  }
}
</style>
