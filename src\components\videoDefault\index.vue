<!-- 默认视频图片 第一张图 -->
<template>
  <div class="videoDefault-main">
    <el-image ref="img" fit="cover" :src="img" :style="{ width: width, height: height, borderRadius: radios }">
      <template #error>
        <div class="image-slot">
          <img src="@/assets/images/img-error.png" alt="" :style="{ width: width, height: height }" />
        </div>
      </template>
    </el-image>
    <div class="player-dialog" :style="{ width: width, height: height, borderRadius: radios }" @click="showVideo">
      <img src="@/assets/images/player.png" alt="" class="img" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { getToken } from '@/utils/auth';
import Axios from 'axios';

interface Props {
  authSrc?: string;
  width?: string;
  height?: string;
  radios?: string;
  isList?: boolean;
  videoSrc?: string;
  videoId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  authSrc: '',
  width: '300px',
  height: '200px',
  radios: '0',
  isList: false,
  videoSrc: '',
  videoId: ''
});

const emit = defineEmits<{
  (e: 'showVideo', id: string): void;
}>();

const img = ref('');
const bigImg = ref<string[]>([]);

const srcList = computed(() => [img.value]);

/**
 * 获取图片
 */
const getImg = () => {
  return new Promise((resolve) => {
    Axios({
      method: 'get',
      url: props.authSrc,
      headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' },
      responseType: 'blob'
    }).then((res) => {
      const blob = res.data;
      const reader = new FileReader();
      reader.readAsDataURL(blob); // 转换为base64
      reader.onload = function () {
        bigImg.value = [reader.result as string];
        img.value = reader.result as string;
      };
      resolve(img.value);
    });
  });
};

/**
 * 子组件调父组件触发播放视频弹窗
 */
const showVideo = () => {
  if (props.videoId && props.videoId != '') {
    emit('showVideo', props.videoId);
  } else {
    emit('showVideo', props.videoSrc);
  }
};

// 监听
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.img-item {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 300px;
  height: 200px;
}
.samll-img {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 50px;
  height: 40px;
}
.el-image {
  margin: 0px;
}
:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd dashed 1px;
  background: rgba(0, 0, 0, 0.1);
}
.videoDefault-main {
  width: 100%;
  height: 100%;
  position: relative;
  .player-dialog {
    position: absolute;
    top: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
    .img {
      width: 32px;
      height: 32px;
    }
  }
}
</style>
