<template>
  <div class="modal-item-main">
    <div class="row-left">
      <div class="left-icon">
        <div v-if="currentItem.iconUrl && currentItem.iconUrl.substring(currentItem.iconUrl.lastIndexOf('_') + 1) === 'blob'">
          <el-image style="width: 44px; height: 44px; margin-right: 8px" :src="`${baseUrl}${currentItem.iconUrl}?token=${token}`" :fit="fit" />
        </div>
        <div v-else style="margin-top: -5px">
          <svg-icon class-name="svg-item" :icon-class="currentItem.iconUrl" />
        </div>
        <div
          class="modal-icon-svg"
          v-if="
            currentItem.status === -1 && currentItem.iconUrl && currentItem.iconUrl.substring(currentItem.iconUrl.lastIndexOf('_') + 1) !== 'blob'
          "
        >
          <!-- 这里是当添加停用模块的时候  给添加一个蒙层的 -->
        </div>
        <div
          class="modal-icon-pic"
          v-if="
            currentItem.status === -1 && currentItem.iconUrl && currentItem.iconUrl.substring(currentItem.iconUrl.lastIndexOf('_') + 1) === 'blob'
          "
        >
          <!-- 这里是当添加停用模块的时候  给添加一个蒙层的 -->
        </div>
      </div>
      <div class="left-module">
        <div class="module-info">
          <div class="title">
            <div
              class="text"
              :title="currentItem.moduleName"
              :style="{
                'text-decoration': currentItem.status === -1 ? 'line-through' : '',
                color: currentItem.status === -1 ? '#8291a9' : '#161d26'
              }"
            >
              {{ currentItem.moduleName }}
            </div>
            <div v-if="currentItem.status === -1" class="stop"><span>已停用</span></div>
            <!-- <div v-if="currentItem.defaultFlag"  class="defalut"><span>默认模块</span></div> -->
            <div v-if="currentItem.status === 8" class="testing"><span>测试中</span></div>
            <div v-if="currentItem.status === 9" class="testing-end"><span>测试完成</span></div>
          </div>
          <div class="remark" :title="currentItem.remark" :style="{ color: currentItem.status === -1 ? '#8291a9' : '#161d26' }">
            {{ currentItem.remark }}
          </div>
        </div>
      </div>
      <div class="left-span">
        <div class="module-people">
          <div class="creater">创建人</div>
          <div class="people">{{ currentItem.createUserName }}</div>
        </div>
      </div>
      <span class="left-span">
        <div class="module-people">
          <div class="creater">创建时间</div>
          <div class="people">{{ formatDateYmdhm(currentItem.createTime) }}</div>
        </div>
      </span>
      <span class="left-span">
        <div class="module-people">
          <div class="creater">模块类型</div>
          <div class="people" v-show="currentItem.type === 1">地理信息类调查</div>
          <div class="people" v-show="currentItem.type === 2">问卷调查</div>
        </div>
      </span>
    </div>
    <div class="row-right">
      <!-- status 1 启用 -1 停用 -->
      <el-button
        type="info"
        size="small"
        v-if="isPublish && currentItem.status === 1"
        @click="handlePublishModule(-1, `停用【${currentItem.moduleName}】`)"
        >停用</el-button
      >
      <el-button
        type="success"
        size="small"
        v-if="isPublish && currentItem.status === -1"
        @click="handlePublishModule(1, `启用【${currentItem.moduleName}】`)"
        >启用</el-button
      >
      <el-button size="small" v-if="isUnpublish && currentItem.status === 0" @click="handlePublishModule(8, `开始测试【${currentItem.moduleName}】`)"
        >开始测试</el-button
      >
      <el-button
        type="warning"
        size="small"
        v-if="isTestpublish && currentItem.status === 9"
        @click="handlePublishModule(8, `重新测试【${currentItem.moduleName}】`)"
        >重新测试</el-button
      >
      <el-button
        type="danger"
        size="small"
        v-if="isTestpublish && currentItem.status === 8"
        @click="handlePublishModule(9, `结束测试【${currentItem.moduleName}】`)"
        >结束测试</el-button
      >
      <el-button
        type="primary"
        size="small"
        :disabled="isTestpublish && currentItem.status !== 9"
        v-if="isTestpublish"
        @click="handlePublishModuleImportant(1, `发布【${currentItem.moduleName}】`)"
        >发布</el-button
      >
      <el-dropdown trigger="click" @command="handleCommandMenu">
        <span class="el-dropdown-link" style="margin-left: 24px; cursor: pointer">
          操作<el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- v-if="isUnpublish || isTestpublish || (isPublish && currentItem.status == -1)" -->
            <!-- toolType === 1 代表是勘界 selectRule 接口中的tools 这个值是有值，这是特殊定制模块只能我们编辑(权籍通后台可以编辑)，不允许用户编辑 QJT-6-1516 和 QJT-6-1516 中的任务 -->
            <el-dropdown-item :icon="Edit" command="edit" v-if="currentItem.toolType !== 1 || user.companyId === '1'">编辑</el-dropdown-item>
            <el-dropdown-item :icon="Delete" command="delete" v-if="isUnpublish || isTestpublish || (isPublish && currentItem.status === -1)"
              >删除</el-dropdown-item
            >
            <el-dropdown-item :icon="Promotion" command="share" v-if="isPublish">生成分享码</el-dropdown-item>
            <el-dropdown-item :icon="Promotion" command="downloadJson">下载设计文件</el-dropdown-item>
            <el-dropdown-item :icon="Sort" command="orderExpression">表达式排序</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!-- 分享码弹窗 -->
    <el-dialog title="分享码" v-model="shareDialog" width="400px" :before-close="handleClose" :close-on-click-modal="false">
      <div>
        拷贝成功，分享码为
        <el-tooltip class="item" effect="dark" content="单击复制" placement="bottom-start">
          <span style="color: #1890ff; cursor: pointer" @click="copyCode()">{{ shareCode }}</span>
        </el-tooltip>
        ;10分钟内有效
      </div>
    </el-dialog>
    <!-- 二维码 -->
    <el-dialog :title="ewmTitle" v-model="erwDialog" width="600px" :close-on-click-modal="false" :before-close="handleCloseEWM">
      <div class="handle-row">
        <div class="row">大小：<el-input type="number" class="row-input" size="small" v-model="QRImgUrlW"></el-input></div>
        <div class="row">
          <el-button size="small" type="primary" @click="editEWM">确定</el-button>
          <el-button size="small" type="primary" @click="downEWM">下载二维码</el-button>
        </div>
      </div>
      <img :src="QRImgUrl" />
    </el-dialog>
    <!-- 排序弹框 -->
    <the-expression-order :orderVisible="orderVisible" @closeOrder="handleOrderClose" :moduleId="currentItem.id"></the-expression-order>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue';
import { modifyModule, copyModal } from '@/api/modal/index.js';
import { downLoadJson } from '@/api/project/index.js';
import QRCode from 'qrcode';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDateYmdhm } from '@/utils/filters.js';
import theExpressionOrder from './component/theExpressionOrder.vue';
const fit = 'cover';
import { ArrowDown, Edit, Delete, Promotion, Sort } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
// 为user添加类型定义
interface UserInfo {
  userId: string | number;
  [key: string]: any;
}
const user = computed<UserInfo>(() => userStore.user as UserInfo);
// 定义 props
const props = defineProps<{
  currentItem: {
    id: number;
    moduleName: string;
    status: number;
    remark: string;
    iconUrl: string;
    type: number;
    createUserName: string;
    createTime: string;
    areaType?: string;
    attribution?: {
      roleIds?: number[];
    };
  };
  isPublish: boolean;
  isTestpublish: boolean;
  isUnpublish: boolean;
}>();
const token = getToken();

// 定义 emits
const emits = defineEmits(['updateList']);

// 定义响应式数据
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/otherDownload/');
const shareDialog = ref(false);
const shareCode = ref('');
const QRImgUrl = ref('');
const erwDialog = ref(false);
const ewmTitle = ref('');
const QRImgUrlW = ref(128);
const orderVisible = ref(false);
const router = useRouter();
const route = useRoute();
// 更多操作
const handleCommandMenu = (command: string) => {
  if (command === 'edit') {
    handleEditModal();
  } else if (command === 'delete') {
    handleDeleteModule();
  } else if (command === 'share') {
    handleShare();
  } else if (command === 'downloadJson') {
    ElMessageBox.confirm('确认要导出设计文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        downLoadJson(props.currentItem.id).then((res) => {
          if (res.data.type === 'application/json') {
            // 导出异常
            const read = new FileReader();
            read.readAsText(res.data, 'utf-8');
            read.onload = (data) => {
              // 由于 EventTarget 上不存在 result 属性，将 data 类型断言为 FileReader 以访问 result 属性
              const bugMsg = JSON.parse((data.currentTarget as FileReader).result as string).msg;
              ElMessage({
                type: 'error',
                message: bugMsg || '未知异常'
              });
            };
          } else {
            ElMessage({
              type: 'success',
              message: '导出成功'
            });
            // 导出正常
            const name = decodeURI(res.headers['content-disposition']);
            const index = name.indexOf('=');
            const endFileName = props.currentItem.moduleName;
            const blob = new Blob([res.data], { type: 'application/json' });
            const downloadElement = document.createElement('a');
            const href = window.URL.createObjectURL(blob);
            downloadElement.href = href;
            downloadElement.download = `${endFileName}.json`;
            document.body.appendChild(downloadElement);
            downloadElement.click();
            document.body.removeChild(downloadElement);
            window.URL.revokeObjectURL(href);
          }
        });
      })
      .catch(() => {});
  } else if (command === 'orderExpression') {
    orderVisible.value = true;
  }
};

// 发布、启用和停用 当前新建的模块数据
const handlePublishModule = (num: number, str: string) => {
  ElMessageBox.confirm(`确认${str}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const data = {
        id: props.currentItem.id,
        status: num,
        delFlag: 0,
        remark: props.currentItem.remark,
        moduleName: props.currentItem.moduleName,
        iconUrl: props.currentItem.iconUrl,
        type: props.currentItem.type
      };
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        data.companyId = companyId;
      }
      const params = {
        moudleId: props.currentItem.id
      };
      modifyModule(data, params).then((res) => {
        if (res.code === 200) {
          emits('updateList');
          ElMessage({
            message: `${str}成功`,
            type: 'success'
          });
          // eslint-disable-next-line vue/no-mutating-props
          props.currentItem.status = num;
        } else {
          ElMessage({
            message: `${res.msg}`,
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};

const handlePublishModuleImportant = (num: number, str: string) => {
  const text = `正式发布模块，将会清除测试中产生的全部任务及数据，且无法恢复！确认${str}模块吗？`;
  const confirm = window.confirm(text);
  if (confirm) {
    // eslint-disable-next-line vue/no-mutating-props
    props.currentItem.status = num;
    const params = {
      moudleId: props.currentItem.id
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      props.currentItem.companyId = companyId;
    }
    modifyModule(props.currentItem, params).then((res) => {
      if (res.code === 200) {
        emits('updateList');
        ElMessage({
          message: `${str}成功`,
          type: 'error'
        });
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    });
  }
};

// 删除当前模块数据
const handleDeleteModule = () => {
  ElMessageBox.confirm(`删除模块，将会清除全部任务及数据，且无法恢复！确认删除【${props.currentItem.moduleName}】模块吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // eslint-disable-next-line vue/no-mutating-props
      props.currentItem.delFlag = 1;
      const params = {
        moudleId: props.currentItem.id
      };
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        props.currentItem.companyId = companyId;
      }
      modifyModule(props.currentItem, params).then((res) => {
        if (res.code === 200) {
          emits('updateList');
          ElMessage({
            type: 'success',
            message: '删除成功'
          });
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      });
    })
    .catch(() => {});
};

// 编辑模块
const handleEditModal = () => {
  const flag = props.currentItem.attribution && props.currentItem.attribution.roleIds && props.currentItem.attribution.roleIds.length > 0;
  const query = {
    name: props.currentItem.moduleName,
    icon: props.currentItem.iconUrl,
    desc: props.currentItem.remark,
    id: props.currentItem.id,
    status: props.currentItem.status,
    delFlag: props.currentItem.delFlag,
    list: props.currentItem.list,
    createUserId: props.currentItem.createUserId,
    createTime: props.currentItem.createTime,
    type: props.currentItem.type,
    areaType: props.currentItem.areaType,
    roleIds: flag ? props.currentItem.attribution.roleIds : []
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    query.companyId = companyId;
  }
  if (!props.currentItem.type || props.currentItem.type === 1) {
    router.push({ path: '/project/addModal', query: query });
  } else if (props.currentItem.type === 2) {
    router.push({ path: '/project/wenjuan', query: query });
  }
};

// 生成分享码
const handleShare = () => {
  ElMessageBox.confirm(`确认要分享【${props.currentItem.moduleName}】模块吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      copyModal(props.currentItem.id).then((res) => {
        if (res.code === 200) {
          shareDialog.value = true;
          shareCode.value = res.data;
          copyCode();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

// 拷贝分享码
const copyCode = () => {
  const variable = shareCode.value;
  const tag = document.createElement('textarea');
  document.body.appendChild(tag);
  tag.value = variable;
  tag.select();
  document.execCommand('copy');
  ElMessage({
    message: '分享码复制成功',
    type: 'success'
  });
  tag.remove();
};

const handleClose = () => {
  shareCode.value = '';
  shareDialog.value = false;
};

const handleCloseEWM = () => {
  erwDialog.value = false;
};

// 修改二维码大小
const editEWM = () => {
  const url = `http://192.168.31.25:60/modalQuestion/${props.currentItem.id}`;
  QRCode.toDataURL(url, { errorCorrectionLevel: 'L', margin: 2, width: QRImgUrlW.value }, (err, url) => {
    if (err) throw err;
    QRImgUrl.value = url;
  });
};

const downEWM = () => {
  // 这里可以添加下载二维码的逻辑
};

// 关闭排序弹框
const handleOrderClose = () => {
  orderVisible.value = false;
};

onMounted(() => {
  // 可以在这里添加初始化逻辑
});

// 导出组件
defineExpose({
  handleCommandMenu,
  handlePublishModule,
  handlePublishModuleImportant,
  handleDeleteModule,
  handleEditModal,
  handleShare,
  copyCode,
  handleClose,
  handleCloseEWM,
  editEWM,
  downEWM,
  handleOrderClose,
  shareDialog
});
</script>

<style lang="scss" scoped>
.handle-row {
  display: flex;
  align-items: center;
  .row {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .row-input {
    width: 100px;
  }
}
.modal-item-main {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  height: 72px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  margin-top: 4px;
  .row-left {
    display: flex;
    width: 70%;
    align-items: center;
    .left-icon {
      // width: 10%;
      min-width: 48px;
      padding: 8px;
      text-align: left;
      margin-left: 16px;
      position: relative;
      .svg-item {
        width: 44px;
        height: 44px;
      }
      .modal-icon-svg {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 0px;
      }
      .modal-icon-pic {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 8px;
      }
    }
    .left-module {
      width: 30%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-info {
        // width: 35%;
        min-width: 156px;
        .title {
          display: flex;
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 600;
          min-width: 156px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .stop {
            width: 48px;
            height: 20px;
            background: rgba(255, 61, 87, 0.1);
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #ff3d57;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .defalut {
            width: 60px;
            height: 20px;
            background: #e6ebf5;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #8291a9;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing {
            width: 48px;
            height: 20px;
            background: #f2752157;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #e53e07af;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing-end {
            width: 60px;
            height: 20px;
            background: #adffbf;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #089145;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
        }
        .remark {
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .left-span {
      width: 20%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-people {
        width: 25%;
        min-width: 156px;
        .creater {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .people {
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .row-right {
    width: 30%;
    margin-right: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .back {
      &:hover {
        color: #22cce2;
      }
    }
    .stop {
      &:hover {
        color: #ff8a48;
      }
    }
    .edit {
      &:hover {
        color: var(--current-color);
      }
    }
    .delete {
      &:hover {
        color: #ff375d;
      }
    }
    .open {
      &:hover {
        color: #09b66d;
      }
    }
    .item {
      margin: 0 8px;
    }
  }
  &:hover {
    background-color: #f6f7f8;
    border-radius: 8px;
    margin-top: 4px;
  }
}
</style>
