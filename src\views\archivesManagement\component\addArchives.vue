<!-- 新增档案 -->
<template>
  <container-card>
    <div class="app-container main">
      <div class="page__header">
        <div class="flex-left">
          <el-button size="small" type="info" @click="handleGoBack" :icon="ArrowLeft">返回</el-button>
        </div>
      </div>
      <div class="page__content">
        <el-form :model="archivesForm" :rules="archivesRules" ref="archivesRef" class="demo-archivesForm">
          <el-form-item label="档案编号" prop="filesNum">
            <el-input v-model="archivesForm.filesNum" placeholder="请输入档案编号"></el-input>
          </el-form-item>
          <el-form-item label="档案名称" prop="filesName">
            <el-input v-model="archivesForm.filesName" placeholder="请输入档案名称"></el-input>
          </el-form-item>
          <el-form-item label="档案类型" prop="filesTypeName">
            <el-input v-model="archivesForm.filesTypeName" placeholder="请输入档案类型"></el-input>
            <!-- <el-select
            v-model="archivesForm.filesType"
            placeholder="请选择档案类型"
            style="width: 100%"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in typeList"
              :key="item.id"
            ></el-option>
          </el-select> -->
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="archivesForm.remark" placeholder="请输入" maxlength="500" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="附件" prop="desc">
            <el-upload
              :on-change="handleChangeUpload"
              :on-remove="handleRemoveUpload"
              :on-success="handleSuccessFile"
              :action="`${base}/qjt/file/multi/upload`"
              :auto-upload="true"
              name="files"
              accept=".jpg,.png,.jpeg,.txt,.pdf,.docx,.doc,.xlsx,.xls"
              :headers="headers"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">只能上传后缀为.jpg,.png,.jpeg,.txt,.pdf,.docx,.doc,.xlsx,.xls文件，且文件大小不超过100M</div>
              </template>
            </el-upload>
            <the-show-file :fileList="archivesFileList" @deletefile="handleDeleteFile"></the-show-file>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('archivesRef')">提交</el-button>
            <!-- <el-button @click="resetForm('archivesRef')">重置</el-button> -->
          </el-form-item>
        </el-form>
      </div>
    </div>
  </container-card>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';
import { saveFilesOpera, getFilesById, uploadFile } from '@/api/archives';
import theShowFile from './TheShowFile.vue';

// 定义类型接口
interface ArchivesForm {
  id?: string | number;
  filesName?: string;
  filesNum?: string;
  filesType?: number;
  filesTypeName?: string;
  operaType?: number;
  remark?: string;
  filesUrl?: string;
  list?: FileItem[];
}

interface FileItem {
  urlName: string;
  filesUrl: string;
  uid?: string | number;
  delFlag?: number;
  [key: string]: any;
}

interface TypeItem {
  id: number;
  label: string;
  value: number;
}

// 路由相关
const router = useRouter();
const route = useRoute();

// 表单引用
const archivesRef = ref();

// 基础数据
const base = computed(() => import.meta.env.VITE_APP_BASE_API || '');
const headers = computed(() => ({
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
}));

// 表单数据
const archivesForm = reactive<ArchivesForm>({
  filesName: undefined,
  filesNum: undefined,
  filesType: undefined,
  filesTypeName: undefined,
  operaType: undefined,
  remark: undefined,
  filesUrl: ''
});

// 表单验证规则
const archivesRules = reactive({
  filesName: [
    { required: true, message: '请输入档案名称', trigger: 'blur' },
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ],
  filesNum: [{ required: true, message: '请输入档案编号', trigger: 'blur' }],
  filesTypeName: [{ required: true, message: '请选择档案类型', trigger: 'change' }],
  remark: [
    { required: false, message: '请输入备注', trigger: 'blur' },
    { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
  ]
});

// 上传附件列表
const archivesFileList = ref<FileItem[]>([]);

// 档案类型列表
const typeList = ref<TypeItem[]>([
  { id: 1, label: '方案档案', value: 1 },
  { id: 2, label: '征收档案', value: 2 },
  { id: 3, label: '回迁档案', value: 3 },
  { id: 4, label: '房源档案', value: 4 },
  { id: 5, label: '财务档案', value: 5 },
  { id: 6, label: '司法档案', value: 6 },
  { id: 7, label: '信访档案', value: 7 },
  { id: 8, label: '办公室档案', value: 8 }
]);

// 生命周期钩子
onMounted(() => {
  loadDataFromQuery();
});

// 监听路由参数变化
watch(
  () => route.query,
  () => {
    loadDataFromQuery();
  },
  { deep: true }
);

// 从路由参数加载数据
const loadDataFromQuery = () => {
  const type = route.query.type as string;
  if (type) {
    archivesForm.filesType = parseInt(type);
  }
  const id = route.query.id as string;
  if (id !== undefined && id !== '' && id !== null) {
    handleDetial(id);
  }
};

// 根据id获取当前页面的数据
const handleDetial = (id: string) => {
  const params = { id };
  // 清空之前的数据
  Object.keys(archivesForm).forEach((key: string) => {
    const k = key as keyof typeof archivesForm;
    if (k !== 'filesType') {
      archivesForm[k] = undefined;
    }
  });
  archivesFileList.value = [];

  getFilesById(params).then((res) => {
    if (res.code == 200) {
      Object.assign(archivesForm, res.data);
      // 上传的附件回显
      archivesFileList.value = res.data.list || [];
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 返回上一页
const handleGoBack = () => {
  router.go(-1);
};

// 提交表单
const submitForm = (formName: string) => {
  archivesRef.value.validate((valid: boolean) => {
    if (valid) {
      const data: any = {
        filesName: archivesForm.filesName,
        filesNum: archivesForm.filesNum,
        filesTypeName: archivesForm.filesTypeName,
        filesType: archivesForm.filesType,
        operaType: 1, // 操作类型 1 新增 2 修改
        remark: archivesForm.remark,
        list: archivesFileList.value
      };

      const id = route.query.id as string;
      if (archivesForm.id && id) {
        // 有id 代表是编辑的情况
        data.id = id;
        data.operaType = 2;
      }

      saveFilesOpera([data]).then((res) => {
        if (res.code == 200) {
          ElMessage.success('新增成功！');
          // 判断要返回那个页面
          handleBackList(archivesForm.filesType);
          //清空表单
          resetForm('archivesRef');
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      return false;
    }
  });
};

// 根据类型返回不同的列表页面
const handleBackList = (num?: number) => {
  if (num == 1) {
    router.push('/archives/fa');
  } else if (num == 2) {
    router.push('/archives/zs');
  } else if (num == 3) {
    router.push('/archives/dsf');
  } else if (num == 4) {
    router.push('/archives/hq');
  } else if (num == 5) {
    router.push('/archives/fy');
  } else if (num == 6) {
    router.push('/archives/cw');
  } else if (num == 7) {
    router.push('/archives/sf');
  } else if (num == 8) {
    router.push('/archives/xf');
  } else if (num == 9) {
    router.push('/archives/bgs');
  } else {
    // 没有的时候默认跳转到第一个
    router.push('/archives/fa');
  }
};

// 重置表单
const resetForm = (formName: string) => {
  archivesRef.value.resetFields();
};

// 上传文件相关方法
const handleChangeUpload = (file: any, fileList: any[]) => {
  const isSize = file.size / 1024 / 1024;
  if (isSize > 100) {
    const index = fileList.findIndex((e) => e.size / 1024 / 1024 == isSize);
    fileList.splice(index, 1);
    ElMessage.warning('上传的文件不能超过100MB');
    return false;
  }
  return isSize < 100;
};

// 删除上传的文件
const handleRemoveUpload = (file: any, fileList: any[]) => {
  const index = archivesFileList.value.findIndex((item) => item.uid == file.uid);
  if (index !== -1) {
    archivesFileList.value.splice(index, 1);
  }
};

// 删除附件内容重新赋值 - 标记为删除而不是真正删除
const handleDeleteFile = (item: FileItem) => {
  const index = archivesFileList.value.findIndex((f) => f.urlName == item.urlName && f.filesUrl == item.filesUrl);
  if (index !== -1) {
    // Vue3中不需要使用$set
    archivesFileList.value[index].delFlag = 1;
  }
};

// 文件上传成功
const handleSuccessFile = (response: any, file: any, fileList: any[]) => {
  fileList.forEach((item) => {
    if (item.response && item.response.data && item.response.data.length > 0) {
      item.response.data.forEach((ite: any) => {
        const obj = {
          urlName: ite.name,
          filesUrl: ite.path
        };
        archivesFileList.value.push(obj);
      });
    }
  });
};

// 上传文件
const handleUploadFileServe = () => {
  const formData = new FormData();
  archivesFileList.value.forEach((file) => {
    formData.append('files', file.raw);
  });
  return new Promise((resolve, reject) => {
    uploadFile(formData).then((res) => {
      if (res.code == 200) {
        const list: FileItem[] = [];
        res.data.forEach((item: any) => {
          const obj = {
            urlName: item.name,
            filesUrl: item.path
          };
          list.push(obj);
        });
        resolve(list);
      } else {
        ElMessage.error(res.msg);
        reject();
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  // height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .page__header {
    width: calc(100% - 30px);
    margin-top: 10px;
    border-radius: 4px;
    height: 54px;
    min-height: 54px;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ededed;
    .flex-left {
      flex: 1;
      margin-left: 10px;
    }
  }
  .page__content {
    height: calc(100% - 66px);
    margin-top: 24px;
    width: calc(100% - 30px);
    display: flex;
    justify-content: center;
    overflow: auto;
    :deep(.el-form) {
      width: 40%;
      height: 100%;
      // overflow: hidden;
    }
  }
}
</style>
