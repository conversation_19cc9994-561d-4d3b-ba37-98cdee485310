<!-- 申请公司的list -->
<template>
  <div class="main">
    <container-card>
      <div class="title-row">
        <span class="text">申请软件</span>
      </div>
      <div class="search-div">
        <div class="search-item">
          <div class="label" style="width: 40px">姓名</div>
          <div class="search-content">
            <el-input v-model="searchParams.contactName" clearable placeholder="请输入姓名" @keyup.enter="fetchApplyList" />
          </div>
        </div>
        <div class="search-item">
          <div class="label">手机号</div>
          <div class="search-content">
            <el-input v-model="searchParams.contactNumber" clearable placeholder="请输入手机号" @keyup.enter="fetchApplyList" />
          </div>
        </div>
        <div class="search-item">
          <div class="label">处理状态</div>
          <div class="search-content">
            <el-select v-model="searchParams.status" placeholder="请选择" @change="fetchApplyList">
              <el-option :value="0" label="未处理" />
              <el-option :value="1" label="同意" />
              <el-option :value="-1" label="拒绝" />
            </el-select>
          </div>
        </div>
        <div class="search-item search-end">
          <el-button type="primary" @click="fetchApplyList">搜索</el-button>
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" :height="tableHeight" class="table-content" border>
        <el-table-column type="index" label="序号" align="center" width="60"/>
        <el-table-column label="姓名" prop="contactName" align="center" />
        <el-table-column label="申请时间" align="center" width="120">
          <template #default="{ row }">
            {{ formatDateYmdhm(row.insertTime) }}
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="contactNumber" align="center" />
        <el-table-column label="所属单位" prop="contactCompanyName" align="center" />
        <el-table-column label="常涉业务" prop="purpose" align="center" />
        <el-table-column label="操作" fixed="right">
          <template #default="{ row }">
            <div v-if="row.status === 0">
              <el-link type="primary" @click="handleDeal(row, 1)">同意</el-link>
              <el-link type="danger" @click="handleDeal(row, -1)" style="margin-left: 10px">拒绝</el-link>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer-page">
        <el-pagination
          v-model:currentPage="searchParams.pageNum"
          v-model:pageSize="searchParams.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </container-card>
    <!-- 申请注册公司 -->
    <el-dialog v-model="addCompanyDialog" title="注册公司" width="500px" :close-on-click-modal="false" :before-close="handleCloseAddCompany">
      <el-form ref="registCompanyFormRef" :model="registCompany" :rules="registCompanyRules" label-position="top" class="demo-ruleForm">
        <el-form-item label="注册类型">
          <el-radio-group v-model="registCompany.companyType">
            <el-radio :value="1">公司</el-radio>
            <el-radio :value="2">个人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName" v-if="registCompany.companyType === 1">
          <el-input v-model="registCompany.companyName" placeholder="请输入公司名称" maxlength="30" />
        </el-form-item>
        <el-form-item label="客户姓名" prop="custName">
          <el-input v-model="registCompany.custName" placeholder="请输入客户姓名" maxlength="20" />
        </el-form-item>
        <el-form-item label="手机号" prop="username">
          <el-input v-model.trim="registCompany.username" placeholder="请输入手机号" maxlength="11" autocomplete="off" show-word-limit />
        </el-form-item>
        <el-form-item label="公司等级">
          <el-radio-group v-model="registCompany.vipType">
            <el-radio :value="3">企业版</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseAddCompany">取 消</el-button>
          <el-button type="primary" @click="submitAddCompany">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { formatDateYmdhm } from '@/utils/filters';
import { getApplyList, updateApply } from '@/api/apply/index';
import { register } from '@/api/login/index';
// 状态管理
const userStore = useUserStore();
// 为user添加类型定义
interface UserInfo {
  userId: string | number;
  [key: string]: any;
}
const user = computed<UserInfo>(() => userStore.user as UserInfo);
// 表格数据
const tableData = ref<any[]>([]);
const total = ref(1);

// 表格高度
const tableHeight = computed(() => window.innerHeight - 280);

// 搜索参数
const searchParams = reactive({
  contactName: '',
  contactNumber: '',
  contactCompanyName: '',
  insertTime: '',
  status: 0,
  pageSize: 10,
  pageNum: 1
});

// 注册公司弹窗相关
const addCompanyDialog = ref(false);
const registCompanyFormRef = ref<FormInstance>();
const registCompany = reactive({
  companyName: '',
  companyType: 1,
  custName: '',
  username: '',
  // password: '',
  vipType: 3
});

// 表单校验规则
const registCompanyRules = reactive<FormRules>({
  custName: [
    { required: true, trigger: 'blur', message: '请输入单位名称' },
    { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!/^(1[0-9])\d{9}$/i.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  companyName: [{ required: true, trigger: 'blur', message: '请输入公司名称' }]
});

// 获取申请列表
const fetchApplyList = async () => {
  try {
    const params = {
      contactName: searchParams.contactName,
      contactNumber: searchParams.contactNumber,
      contactCompanyName: searchParams.contactCompanyName,
      insertTime: searchParams.insertTime,
      status: searchParams.status,
      pageSize: searchParams.pageSize,
      pageNum: searchParams.pageNum
    };
    const response = await getApplyList(params);
    if (response) {
      tableData.value = response.data.rows;
      total.value = response.total;
    }
  } catch (err: any) {
    ElMessage.error(err?.message || '获取列表失败');
  }
};

// 处理分页
const handleSizeChange = (val: number) => {
  searchParams.pageSize = val;
  searchParams.pageNum = 1;
  fetchApplyList();
};

const handleCurrentChange = (val: number) => {
  searchParams.pageNum = val;
  fetchApplyList();
};

// 处理同意/拒绝
const handleDeal = (item: any, status: number) => {
  const action = status === 1 ? '同意' : '拒绝';
  const text = `确认${action}【${item.contactName}】（所属公司：${item.contactCompanyName}）申请神马调查的软件试用吗？`;

  ElMessageBox.confirm(text, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await updateApply({ ...item, status });
        ElMessage.success('操作成功');
        if (status === 1) {
          addCompanyDialog.value = true;
        }

        fetchApplyList();
      } catch (err: any) {
        ElMessage.error(err?.message || '操作失败');
      }
    })
    .catch(() => {
      ElMessage.info('已取消');
    });
};

// 关闭注册公司弹框
const handleCloseAddCompany = () => {
  Object.assign(registCompany, {
    companyName: '',
    companyType: 1,
    custName: '',
    username: '',
    // password: '',
    vipType: 3
  });
  registCompanyFormRef.value?.clearValidate();
  addCompanyDialog.value = false;
};

// 提交注册公司
const submitAddCompany = () => {
  registCompanyFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) {
      return;
    }

    try {
      const params: any = {
        companyName: registCompany.companyType === 2 ? registCompany.custName : registCompany.companyName,
        companyType: registCompany.companyType,
        custName: registCompany.custName,
        username: registCompany.username,
        mobile: registCompany.username,
        ifCaptcha: false,
        vipType: registCompany.vipType,
        promoterUseId: user.value.userId,
        // password: `qjt${registCompany.username.substring(5, 11)}`,
        expireTime: new Date().getTime() + 30 * 24 * 3600 * 1000
      };

      const res = await register(params);

      if (res.code === 200) {
        ElMessageBox.alert('恭喜你注册成功', '注册成功', {
          confirmButtonText: '确定'
        });

        handleCloseAddCompany();
        fetchApplyList();
      } else {
        ElMessage.error(res.msg || '注册失败,请稍后再试');
      }
    } catch (err: any) {
      ElMessage.error(err?.msg || '注册失败,请稍后再试');
    }
  });
};

// 初始加载
onMounted(() => {
  fetchApplyList();
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;

  .title-row {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dbe7ee;
    align-items: center;

    .text {
      height: 22px;
      font-size: 16px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
      line-height: 22px;
      margin-bottom: 4px;
    }
  }

  .search-div {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      .label {
        width: 70px;
        text-align: right;
      }
      .search-content {
        margin-left: 10px;
        flex: 1;
      }
    }

    .search-end {
      justify-content: flex-end;
    }
  }

  .table-content {
    margin-top: 10px;
    height: calc(100% - 115px);
    overflow: auto;

    /*滚动条样式*/
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }

  .footer-page {
    margin-top: 10px;
    text-align: right;
  }
}

// Responsive styles
@media (max-width: 768px) {
  .search-div {
    flex-direction: column;

    .search-item {
      margin-bottom: 8px;
      width: 100%;
    }
  }
}
</style>
