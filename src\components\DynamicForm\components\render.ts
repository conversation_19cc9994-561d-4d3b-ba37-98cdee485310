import { defineComponent, h, ref, watch } from 'vue';
import { makeMap } from '../utils';
import type { JSX } from 'vue/jsx-runtime';
import { Plus, UploadFilled, Check } from '@element-plus/icons-vue';
import SvgIcon from '@/components/SvgIcon/index.vue';
import {
  ElInput,
  ElFormItem,
  ElCol,
  ElRow,
  ElSelect,
  ElOption,
  ElRadioGroup,
  ElCheckboxGroup,
  ElCheckbox,
  ElCheckboxButton,
  ElUpload,
  ElButton,
  ElInputNumber,
  ElDatePicker,
  ElCascader
} from 'element-plus';
// 参考https://github.com/vuejs/vue/blob/v2.6.10/src/platforms/web/server/util.js
const isAttr = makeMap(
  'accept,accept-charset,accesskey,action,align,alt,async,autocomplete,' +
    'autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,' +
    'checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,' +
    'name,contenteditable,contextmenu,controls,coords,data,datetime,default,' +
    'defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,' +
    'form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,' +
    'icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,' +
    'manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,' +
    'muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,' +
    'preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,' +
    'scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,' +
    'spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,' +
    'target,title,type,usemap,value,width,wrap'
);

interface FormItemConfig {
  tag: string;
  defaultValue?: any;
  [key: string]: any;
}

interface ComponentChildMethods {
  [key: string]: (conf: FormItemConfig, key: string) => JSX.Element | JSX.Element[];
}

const vModel = (emit: any, dataObject: any, defaultValue: any) => {
  dataObject.props.value = defaultValue;
  dataObject.onInput = (val: any) => {
    emit('update:modelValue', val);
  };
};

const componentChild: Record<string, ComponentChildMethods> = {
  'el-input': {
    prepend(conf, key) {
      return h('template', { 'v-slot:prepend': true }, conf[key]);
    },
    append(conf, key) {
      return h('template', { 'v-slot:append': true }, conf[key]);
    }
  },
  'el-input-phone': {
    vModel(conf, key) {
      conf.defaultValue = conf.defaultValue?.replace(/\D/g, '') || '';
      return null;
    }
  },
  'el-select': {
    options(conf, key) {
      return (
        conf.options?.map((item) =>
          h(ElOption, {
            label: item.label,
            value: item.value,
            disabled: item.disabled
          })
        ) || []
      );
    }
  },
  'el-radio-group': {
    options(conf, key) {
      return (
        conf.options?.map((item) => {
          return conf.optionType === 'button'
            ? h(ElRadioButton, { label: item.value }, () => item.label)
            : h(ElRadio, { label: item.value, border: conf.border }, () => item.label);
        }) || []
      );
    }
  },
  'el-checkbox-group': {
    options(conf, key) {
      return (
        conf.options?.map((item) => {
          return conf.optionType === 'button'
            ? h(ElCheckboxButton, { label: item.value }, () => item.label)
            : h(ElCheckbox, { label: item.value, border: conf.border }, () => item.label);
        }) || []
      );
    }
  },
  'el-upload': {
    'list-type'(conf, key) {
      const list = [];
      if (conf.cmpType == 'common') {
        // 原本的代码样式
        if (conf['list-type'] == 'picture-card') {
          list.push(h('el-icon', { style: 'font-size:24px' }, '+'));
        } else {
          list.push(h('el-button', { size: 'small', type: 'primary', icon: 'UploadFilled' }, conf.buttonText));
        }
      } else if (conf.cmpType == 'custom') {
        // 自定义的 代表这里要修改签名和指纹的样式
        list.push(h(SvgIcon, { 'class-name': 'svg-item', 'icon-class': conf.icon, style: 'font-size:24px' }));
        // if (conf.tagIcon === 'xtqm') {
        //   // 设置签名的样式
        //   list.push(h(SvgIcon, { 'class-name': 'svg-item', 'icon-class': conf.tagIcon, style: 'font-size:24px' }));
        // } else if (conf.tagIcon === 'xtzw') {
        //   // 设置指纹样式
        //   list.push(h(SvgIcon, { 'class-name': 'svg-item', 'icon-class': conf.tagIcon, style: 'font-size:24px' }));
        // } else if (conf.tagIcon == 'xtzwsb') {
        //   // 设置植物识别
        //   list.push('<svg-icon icon-class={conf.icon} style="font-size:24px" />');
        // } else if (conf.tagIcon == 'xtdwsb') {
        //   // 设置动物识别
        //   list.push('<svg-icon icon-class={conf.icon} style="font-size:24px" />');
        // } else if (conf.tagIcon == 'xtvideo') {
        //   // 设置视频
        //   list.push('<svg-icon icon-class={conf.icon} style="font-size:24px" />');
        // } else if (conf.tagIcon == 'xtaudio') {
        //   // 设置动物识别
        //   list.push('<svg-icon icon-class={conf.icon} style="font-size:24px" />');
        // } else if (conf.tagIcon == 'xtsjjt') {
        //   // 设置系统数据草图
        //   list.push('<svg-icon icon-class={conf.icon} style="font-size:24px" />');
        // }
      }
      if (conf.showTip) {
        list.push(h('div', { class: 'el-upload__tip' }, `只能上传不超过 ${conf.fileSize}${conf.sizeUnit} 的${conf.accept}文件`));
      }
      return list;
    }
  }
};

const getComponent = (tag: string) => {
  const componentMap = {
    'el-input': ElInput,
    'el-select': ElSelect,
    'el-radio-group': ElRadioGroup,
    'el-checkbox-group': ElCheckboxGroup,
    'el-upload': ElUpload,
    'el-button': ElButton,
    'el-option': ElOption,
    'el-checkbox': ElCheckbox,
    'el-checkbox-button': ElCheckboxButton,
    'el-input-number': ElInputNumber,
    'el-time-picker': ElTimePicker,
    'el-date-picker': ElDatePicker,
    'el-cascader': ElCascader
  };
  const component = componentMap[tag];
  if (!component) {
    console.warn(`Component ${tag} not found in componentMap`);
    return tag;
  }
  return component;
};

export default defineComponent({
  name: 'FormItemRenderer',
  props: {
    conf: {
      type: Object as () => FormItemConfig,
      required: true
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const confClone = ref(JSON.parse(JSON.stringify(props.conf)));

    const renderComponent = () => {
      const dataObject: any = {
        attrs: {},
        props: {},
        on: {},
        style: {}
      };

      const children: (JSX.Element | null)[] = [];
      let childObjs = componentChild[confClone.value.tag];
      if (confClone.value.icon === 'phoneIcon') {
        childObjs = componentChild['el-input-phone'];
      } else {
        childObjs = componentChild[confClone.value.tag];
      }
      if (childObjs) {
        Object.keys(childObjs).forEach((key) => {
          const childFunc = childObjs[key];
          if (confClone.value[key]) {
            const child = childFunc(confClone.value, key);
            if (child) {
              if (Array.isArray(child)) {
                children.push(...child);
              } else {
                children.push(child);
              }
            }
          }
        });
      }

      Object.keys(confClone.value).forEach((key) => {
        const val = confClone.value[key];
        if (key === 'vModel') {
          vModel(emit, dataObject, confClone.value.defaultValue);
        } else if (dataObject[key]) {
          dataObject[key] = val;
        } else if (!isAttr(key)) {
          dataObject.props[key] = val;
        } else {
          dataObject.attrs[key] = val;
        }
      });
      const renderH = getComponent(confClone.value.tag);
      if (typeof renderH === 'string') {
        console.warn(`Component ${confClone.value.tag} not registered, falling back to native element`);
      }
      // 这里是render 最后回调结果的标签内容
      // 这里 h 的第二个参数只需要写一级别就可以 不需要嵌套
      const resultDataObject = {
        ...dataObject.props,
        ...dataObject.attrs,
        ...dataObject.on,
        ...dataObject.style
      };

      return h(renderH, resultDataObject, children);
    };

    // 监听 conf 变化
    watch(
      () => props.conf,
      (newVal) => {
        confClone.value = JSON.parse(JSON.stringify(newVal));
      },
      { deep: true }
    );

    return () => renderComponent();
  }
});
