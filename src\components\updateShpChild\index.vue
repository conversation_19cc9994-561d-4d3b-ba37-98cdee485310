<!-- 跟新界址点界址线 -->
<template>
  <div class="updateShpChild-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="更新子要素"
      v-model="updateShpChildDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      :before-close="handleClose"
    >
      <div class="dialog-row">1、选择编码方式</div>
      <div class="dialog-row">
        <el-radio-group v-model="bmType">
          <el-radio label="utf-8">utf-8</el-radio>
          <el-radio label="gbk">gbk</el-radio>
        </el-radio-group>
      </div>
      <div class="dialog-row">2、选择更新节点</div>
      <div class="dialog-row">
        <el-cascader
          v-model="updateType"
          :options="treeOptions"
          :show-all-levels="false"
          :props="cascaderProps as any"
          @change="handleTreeChange"
        ></el-cascader>
      </div>
      <div class="dialog-row">3、选择文件 <span style="color: red">(请选择.shp,.dbf,.prj,.shx文件)</span></div>
      <form enctype="multipart/form-data" method="post" id="uploadForm" ref="uploadForm">
        <div class="file-div">
          <label class="file-upload">
            <input ref="fileRef" type="file" multiple accept=".shp,.dbf,.prj,.shx" @change="updateFileList" />
            <ul id="fileList"></ul>
            <!-- 这里将显示选择的文件名 -->
          </label>
        </div>
      </form>
      <div class="dialog-row" style="margin-top: 10px">4、读取文件</div>
      <div class="dialog-row">
        <el-button type="primary" @click="submitUploadProject" size="small">读取</el-button>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitUpdateShp">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 坐标系选择 -->
    <el-dialog
      title="提示"
      v-model="chooseWkidDialog"
      width="30%"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="handleCloseChooseWkid"
    >
      <div style="margin-bottom: 10px; color: #ff4343">您上传的shp坐标系暂未兼容，请您主动选择一个正确的坐标系，否则会导致导入之后位置不对！！！</div>
      <el-select v-model="shpWkid" placeholder="请选择对应的wkid" filterable style="width: 100%">
        <el-option v-for="item in wkidMap" :key="item.wkid" :label="`${item.srsCode}(${item.wkid})`" :value="item.wkid"> </el-option>
      </el-select>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseChooseWkid">取 消</el-button>
          <el-button type="primary" @click="sumitChooseWkid">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="映射字段"
      v-model="dialogVisible"
      width="500px"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleDialogVisible"
    >
      <el-tooltip
        class="item"
        effect="dark"
        content="会展示第一个图形的属性数据，如果发现乱码就需要返回选择另一个编码方式重新读取！！！"
        placement="top-start"
      >
        <el-link type="primary" @click="verifyCoding">校验编码格式</el-link>
      </el-tooltip>
      <el-form :model="selectFrom" :rules="selectFromRules" ref="ruleFormRef" label-position="top">
        <el-row>
          <el-form-item label="关联字段">
            <el-select v-model="ysChooseGroupId" placeholder="请选择关联属性组" style="width: 100%" @change="chooseYSGroup">
              <el-option v-for="item in fieldGroupModelListAll" :key="item.id" :label="item.typeName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-select v-model="ysChooseField" placeholder="请选择关联字段" clearable filterable style="width: 100%">
            <el-option v-for="item in ysChooseFieldList" :key="item.fieldName" :label="item.fieldCn" :value="item.fieldName"> </el-option>
          </el-select>
        </el-row>
        <el-row>
          <el-form-item label="属性组字段映射">
            <el-col :span="24">
              <el-select
                v-model="selectFrom.currentGroup"
                style="width: 100%"
                filterable
                clearable
                placeholder="属性组字段映射"
                value-key="id"
                @change="handleFieldCurrentGroup"
              >
                <el-option v-for="item in fieldGroupModelList" :key="item.id" :label="getGroupLabel(item)" :value="item"> </el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </el-row>
        <el-table :data="localfields" style="width: 100%; overflow: auto" :row-style="{ height: '49px' }" height="300" border>
          <el-table-column label="属性组字段">
            <template #default="scope">
              {{ scope.row.fieldCn }}
              <span v-if="scope.row.fieldType == 'String'">(文本)</span>
              <span v-if="scope.row.fieldType == 'Long'">(整数)</span>
              <span v-if="scope.row.fieldType == 'Date'">(日期)</span>
              <span v-if="scope.row.fieldType == 'Double'">(小数)</span>
            </template>
          </el-table-column>
          <el-table-column label="SHP字段">
            <template #default="scope">
              <!--  -->
              <el-select v-model="scope.row.yz" placeholder="请选择" clearable filterable @change="changeShpField(scope.row)">
                <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogVisible">取 消</el-button>
          <el-button type="primary" @click="submitFields('ruleFormRef')">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 验证编码格式弹窗 -->
    <el-dialog title="校验编码格式" v-model="verifyDialog" :modal-append-to-body="false" :append-to-body="true" width="30%">
      <div style="color: #ff4343; margin-bottom: 10px; font-weight: bold">注意：字段内容如出现乱码，请返回重新选择编码方式！！！</div>
      <div class="verify-content">
        <div class="flex-row" v-for="(item, index) in shpFields" :key="index">{{ item }}：{{ shpList[0].properties[item] }}</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="verifyDialog = false">取 消</el-button>
          <el-button type="primary" @click="verifyDialog = false">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 数据上传进度 -->
    <el-dialog
      title="数据导入中"
      v-model="uploadLodingDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadProgress"></el-progress>
        <div style="margin-top: 10px">{{ uploadMsg }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { loadModules } from 'esri-loader';
import wkidMapData from '@/data/wkidMap.json';
import proj4 from 'proj4';
import { read as shapeRead } from 'shapefile';
import { selectRules } from '@/api/modal';
import { updateParcelFromGroup } from '@/api/project';
import { getWkidForSrsCode } from '@/utils/validate';
// const updateChildNodeDemoVideo = require('@/assets/video/更新子要素界址点界址线.mp4'); // todo  视频待补充
const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

// ---Props---
interface Props {
  // 打开弹框
  updateShpChildDialog: boolean;
  // 模块id
  moduleId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  updateShpChildDialog: false,
  moduleId: ''
});

const updateShpChildDialogCopy = computed(() => props.updateShpChildDialog);

// 定义emit
const emit = defineEmits<{
  (e: 'closeShpChild'): void;
}>();

// --- 监听 ---
watch(
  updateShpChildDialogCopy,
  (val) => {
    if (val) {
      getData();
    }
  },
  { deep: true }
);

// --- 定义变量 ---
const treeOptions = ref([]);
const bmType = ref('utf-8'); //默认编码方式gbk
const updateType = ref([]);
// 选择字段类型的节点
const cascaderProps = {
  value: 'id',
  label: 'typeName',
  expandTrigger: 'click',
  children: 'list',
  checkStrictly: true
};
const updateRuleId = ref(0); // 当前要更新的节点id
const wkidMap = ref(wkidMapData); //上传的shp的wkid
const shpWkid = ref(''); //上传的shp的wkid
const fullscreenLoading = ref(false);
const chooseWkidDialog = ref(false); //选择坐标系弹窗
const localfields = ref([]); //本地字段
const shpFields = ref([]); //shp的字段
const fieldGroupModelList = ref([]); //子要素的 界址点、界址线
const fieldGroupModelListAll = ref([]); //所有的
const dialogVisible = ref(false);
const currentTreeItem: any = reactive({}); // 当前选中的树节点中的某一项
const attrList = ref([]); //选中节点的属性组
const verifyDialog = ref(false); //验证编码方式结果弹窗
const selectFrom: any = reactive({
  // 当前选中的属性组
  currentGroup: undefined,
  // 当前选择的映射宗地名称
  currentParcelName: undefined,
  // 映射父级id
  currentParentId: undefined,
  // 映射当前节点id
  currentNodeId: undefined,
  souseKey1: '',
  souseKey2: ''
});

// 表单校验
const selectFromRules = {
  // 映射当前节点id
  currentNodeId: [{ required: true, message: '请选择节点唯一ID', trigger: 'change' }],
  currentGroup: [{ required: true, message: '请选择属性组字段映射', trigger: 'change' }],
  // 映射父级id
  currentParentId: [{ required: true, message: '请选择上级节点唯一ID', trigger: 'change' }]
};

const ysChooseGroupId = ref(''); //映射属性组选择的id
const ysChooseGroup = ref({}); //映射的属性组
const ysChooseFieldList = ref([]); //映射选择的属性组对应的字段列表
const ysChooseField = ref(''); //映射的字段
const uploadLodingDialog = ref(false); //数据上传进度弹窗
const uploadProgress = ref(0); //上传进度
const uploadMsg = ref(''); //上传成功条数信息
const uploadShpNum = ref(0); //上传的excel总条数
const shpType = ref(1); //1点 2线
const successNum = ref(0); //上传成功条数
const fileRef = ref(null); //文件ref
const shpSource = ref(null); //shp源
const shpList = ref([]); //shp列表
const transitionNum = ref(0); //转换进度
const ruleFormRef = ref(null); //表单ref
const submitList = ref([]); //提交列表
const operaType = ref(null); //操作类型

// --- 定义方法 ---

/**
 * 获取树形图
 */
const getData = () => {
  selectRules({ moduleId: props.moduleId }).then((res) => {
    if (res.code == 200) {
      treeOptions.value = checkList(res.data);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const checkList = (arr) => {
  return arr.map((item) => {
    if (item.list && item.list.length > 0) {
      checkList(item.list);
      return item;
    } else {
      delete item.list;
      return item;
    }
  });
};
const handleTreeChange = (value) => {
  updateRuleId.value = value[value.length - 1];
  handleFindTreeItem(treeOptions.value, updateRuleId.value);
  // 如果不是选择的第一级，需要找到父级的属性组  key2的时候要用
  // if (value.length != 1) {
  //   getParentAttr(treeOptions.value, value[value.length - 2]);
  // }
};

// 文件变化的时候
const updateFileList = () => {
  const fileList = fileRef.value.files; // 获取文件列表
  const list = document.getElementById('fileList');
  // 清空现有列表
  list.innerHTML = '';
  // 遍历文件列表，并添加到页面上的列表中
  for (let i = 0; i < fileList.length; i++) {
    const fileName = fileList[i].name; // 获取文件名
    const listItem = document.createElement('li'); // 创建新的列表项
    listItem.textContent = fileName; // 设置列表项的文本内容为文件名
    list.appendChild(listItem); // 将列表项添加到页面上的列表中
  }
};

/**
 * 提交更新项目
 */
const submitUploadProject = () => {
  // 判断第二步是否选择了
  if (updateType.value.length == 0) {
    ElMessage.error('请选择更新宗地!!!');
    return;
  }
  let files = fileRef.value.files;
  // files = Array.from(new Array(files.length), (i, idx) => files[idx]) // 等效下面写法
  files = Array.from(files); // FileList => Array, 方便使用 Array 方法
  // 解析 shp
  parseShapefile(files); // 解析选择的 shp 并绘制显示
};

/**
 * 解析shp文件
 */
const parseShapefile = async (files) => {
  if (files.length == 0) {
    ElMessage.error('请选择文件!!!');
    return;
  }
  if (files.length != 4) {
    ElMessage.error('请选择正确的文件!!!');
    return;
  }
  fullscreenLoading.value = true;
  const shpFile = files.find((f) => f.name.endsWith('.shp'));
  const dbfFile = files.find((f) => f.name.endsWith('.dbf'));
  const prjFile = files.find((f) => f.name.endsWith('.prj'));
  const promises = [shpFile, dbfFile].map((i) => readInputFile(i));
  promises.push(readInputFile(prjFile, 'Text'));
  let prjCrs;
  Promise.all(promises)
    .then(([shp, dbf, prj]) => {
      prjCrs = new proj4.Proj(prj);
      if (prjCrs.AUTHORITY) {
        shpWkid.value = prjCrs.AUTHORITY.EPSG;
      } else {
        shpWkid.value = getWkidForSrsCode(prjCrs.srsCode);
        if (!shpWkid.value) {
          //如果没有匹配到坐标系就提示用户，并让用户自己选择一个坐标系
          chooseWkidDialog.value = true;
        }
      }
      // return shapeOpen(shp, dbf)
      // 指定 dbf 编码 'utf-8', 解决geojson properties乱码
      return shapeRead(shp, dbf, { encoding: bmType.value });
    })
    .then(async (source) => {
      // 节点为点 shp数据不是点的话报错
      if (chooseWkidDialog.value) {
        //wkid没有对应上的话，需要等用户在弹窗里面把wkid选了之后再往下执行
        shpSource.value = source;
        fullscreenLoading.value = false;
        return;
      }
      if (!source.features[0].geometry.type.includes('Point') && !source.features[0].geometry.type.includes('Line')) {
        ElMessage.error('更新子要素时，shp类型必须是点或者线！！！');
        fullscreenLoading.value = false;
        return;
      }
      if (!source.features && source.features.length == 0) {
        ElMessage.error('没有数据，请重新上传!!!');
        return;
      }
      const sourceList = source.features;
      fullscreenLoading.value = false;
      if (shpWkid.value && shpWkid.value != '3857') {
        //需要转换坐标系
        shpList.value = [];
        // 转换坐标系
        const chunkSize = 10;
        const chunks = [];
        transitionNum.value = sourceList.length;
        // 拆分数组
        for (let i = 0; i < sourceList.length; i += chunkSize) {
          chunks.push(sourceList.slice(i, i + chunkSize));
        }
        // 初始化转换进度弹窗
        const loading = ElLoading.service({
          lock: true,
          text: '坐标系转换中',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });
        // 依次转换子数组
        for (let index = 0; index < chunks.length; index++) {
          try {
            await initWkid(chunks[index], index + 1);
          } catch (error) {
            console.error('转换失败:', error);
            // 处理错误，比如跳过当前子数组或中断整个上传过程
            continue;
          }
        }
        loading.close();
      } else {
        shpList.value = sourceList;
      }
      const shpFieldsTemp = Object.keys(source.features[0].properties);
      shpFields.value = shpFieldsTemp;
      const list = JSON.parse(JSON.stringify(treeOptions.value));
      await getModelGroupListById(list);
    });
};

/**
 * 读取文件
 */
const readInputFile = async (file, type = 'ArrayBuffer') => {
  // 读取文件
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    switch (type) {
      case 'ArrayBuffer':
        reader.readAsArrayBuffer(file);
        break;
      case 'Text':
        reader.readAsText(file);
        break;
      case 'BinaryString':
        reader.readAsBinaryString(file);
        break;
      case 'DataURL':
        reader.readAsDataURL(file);
        break;
    }

    reader.onload = function () {
      resolve(this.result);
    };

    reader.onerror = function () {
      reject(this);
    };
  });
};

// 关闭选择坐标系弹窗
const handleCloseChooseWkid = () => {
  ElMessageBox.confirm('确定要关闭手动选择坐标系弹窗吗？如果不选择坐标系则不允许导入！！！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      chooseWkidDialog.value = false;
    })
    .catch(() => {});
};

/**
 * 提交选择的坐标系
 */
const sumitChooseWkid = () => {
  if (!shpWkid.value) {
    ElMessage.error('请务必选择正确的坐标系！！！');
    return;
  }
  chooseWkidDialog.value = false;
  initSouse();
};

/**
 * 当wkid没有对应上的时候 再次调用转换坐标系以及shp类型跟节点类型匹配验证
 */
const initSouse = async () => {
  if (currentTreeItem.value.graphicalType == 1 && !shpSource.value.features[0].geometry.type.includes('Point')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  // 节点为线 shp数据不是线的话报错
  if (currentTreeItem.value.graphicalType == 2 && !shpSource.value.features[0].geometry.type.includes('Line')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  // 节点为面 shp数据不是面的话报错
  if (currentTreeItem.value.graphicalType == 3 && !shpSource.value.features[0].geometry.type.includes('Polygon')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  if (!shpSource.value.features && shpSource.value.features.length == 0) {
    ElMessage.error('没有数据，请重新上传!!!');
    return;
  }
  const sourceList = []; //处理图形数据 去除多面
  shpSource.value.features.forEach((v) => {
    if (!v.geometry.type.includes('MultiPolygon')) {
      sourceList.push(v);
    }
  });
  if (sourceList.length < shpSource.value.features.length) {
    //代表有多面被去除了
    ElMessageBox.alert('暂不支持MultiPolygon(多面类型的导入)，本次导入已剔除！！！', '警告', {
      confirmButtonText: '确定',
      callback: (action) => {}
    });
  }
  fullscreenLoading.value = false;
  if (shpWkid.value && shpWkid.value != '3857') {
    //需要转换坐标系
    shpList.value = [];
    // 转换坐标系
    const chunkSize = 10;
    const chunks = [];
    transitionNum.value = sourceList.length;
    // 拆分数组
    for (let i = 0; i < sourceList.length; i += chunkSize) {
      chunks.push(sourceList.slice(i, i + chunkSize));
    }
    // 初始化转换进度弹窗
    const loading = ElLoading.service({
      lock: true,
      text: '坐标系转换中',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255, 255, 0.7)'
    });
    // 依次转换子数组
    for (let index = 0; index < chunks.length; index++) {
      try {
        await initWkid(chunks[index], index + 1);
      } catch (error) {
        console.error('转换失败:', error);
        ElMessage.error(error);
        // 处理错误，比如跳过当前子数组或中断整个上传过程
        continue;
      }
    }
    loading.close();
  } else {
    shpList.value = sourceList;
  }
  const shpFieldsTemp = Object.keys(shpSource.value.features[0].properties);
  shpFields.value = shpFieldsTemp;
  const list = JSON.parse(JSON.stringify(treeOptions.value));
  await getModelGroupListById(list);
};

const initWkid = async (list, num) => {
  return new Promise((resolve, reject) => {
    loadModules(
      [
        'esri/geometry/SpatialReference',
        'esri/geometry/projection',
        'esri/geometry/Polygon',
        'esri/Graphic',
        'esri/geometry/Point',
        'esri/geometry/Polyline'
      ],
      config
    ).then(([SpatialReference, projection, Polygon, Graphic, Point, Polyline]) => {
      projection.load().then(() => {
        const newList = [];
        if (list[0].geometry.type.includes('Polygon')) {
          //面
          list.forEach((v) => {
            const polygon = new Polygon({
              type: 'polygon',
              rings: v.geometry.coordinates,
              spatialReference: { wkid: shpWkid.value || 3857 }
            });
            newList.push(polygon);
          });
        } else if (list[0].geometry.type.includes('Point')) {
          //点
          list.forEach((v) => {
            const point = new Point({
              x: v.geometry.coordinates[0],
              y: v.geometry.coordinates[1],
              spatialReference: {
                wkid: shpWkid.value || 3857
              }
            });
            newList.push(point);
          });
        } else if (list[0].geometry.type.includes('LineString')) {
          //线
          list.forEach((v) => {
            const line = new Polyline({
              paths: v.geometry.coordinates,
              spatialReference: {
                wkid: shpWkid.value || 3857
              }
            });
            newList.push(line);
          });
        }
        // 设置目标坐标系
        const outSR = new SpatialReference({ wkid: 3857 }); // WKID为3857代表WGS 1984 Web Mercator
        const projectedPoints = projection.project(newList, outSR);
        if (list[0].geometry.type.includes('Polygon')) {
          //面
          list.forEach((v, idx) => {
            if (projectedPoints[idx]) {
              v.geometry.coordinates = projectedPoints[idx].rings;
            } else {
              //如果转换图形失败 需要一个一个的转换
              const coordinates = [];
              v.geometry.coordinates.forEach((k) => {
                const list = [];
                k[0].forEach((o) => {
                  const itePoint = new Point({
                    x: o[0],
                    y: o[1],
                    spatialReference: {
                      wkid: shpWkid.value || 3857
                    }
                  });
                  const projectedPoints = projection.project(itePoint, outSR);
                  list.push([projectedPoints.x, projectedPoints.y]);
                });
                coordinates.push([list]);
              });
              v.geometry.coordinates = coordinates;
            }
          });
        } else if (list[0].geometry.type.includes('Point')) {
          //点
          list.forEach((v, idx) => {
            v.geometry.coordinates = [projectedPoints[idx].x, projectedPoints[idx].y];
          });
        } else if (list[0].geometry.type.includes('LineString')) {
          //线
          list.forEach((v, idx) => {
            v.geometry.coordinates = projectedPoints[idx].paths[0];
          });
        }
        shpList.value.push(...list);
        resolve(null);
      });
    });
  });
};

/**
 * 根据id 遍历树结构 返回当前节点相等所有属性组（返回属性组中的字段）
 */
const getModelGroupListById = async (list) => {
  try {
    if (list && list.length > 0) {
      list.forEach((item) => {
        if (item.id == updateRuleId.value) {
          //  这里是数组去重，暂时先不要删除这里的代码
          // let obj = {}
          // this.fieldGroupModelList = item.fieldGroupModelList.reduce((pre,cur)=>{
          //   obj[cur.id]?'':obj[cur.id] = true && pre.push(cur)
          //   return pre
          // },[])
          // 这里应该只要子要素类型
          fieldGroupModelList.value = [];
          fieldGroupModelListAll.value = [];
          item.fieldGroupModelList.forEach((v) => {
            if (
              v.ruleAttribution &&
              (v.ruleAttribution.type == 'graphicalPoint' ||
                v.ruleAttribution.type == 'commonPoint' ||
                v.ruleAttribution.type == 'graphicalLine' ||
                v.ruleAttribution.type == 'commonLine')
            ) {
              fieldGroupModelList.value.push(v);
            }
            fieldGroupModelListAll.value.push(v);
          });
          dialogVisible.value = true;
        } else {
          // 上面处理了list  在这里要做一个判断 不是每个节点下都有一个list
          if (item.list && item.list.length > 0) {
            getModelGroupListById(item.list);
          }
        }
      });
    }
  } catch (error) {}
};

/**
 * 关闭映射字段弹框
 */
const handleDialogVisible = () => {
  localfields.value = [];
  shpFields.value = [];
  ruleFormRef.value.clearValidate();
  dialogVisible.value = false;
};

/**
 * 提交映射字段
 */
const submitFields = async (formName) => {
  ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      const count = shpList.value.length; //总数据量
      submitList.value = [];
      // 映射的字段
      const groupModel = {
        id: selectFrom.currentGroup.id,
        linkId: selectFrom.currentGroup.linkId,
        fieldModelList: [
          {
            fieldName: selectFrom.ysChooseField
          }
        ]
      };
      if (shpList.value[0].geometry.type.includes('Line')) {
        //线
        shpType.value = 2;
      }
      // 分段处理数据 避免卡死
      const chunkSize = 10;
      const chunks = [];
      // 拆分数组
      for (let i = 0; i < shpList.value.length; i += chunkSize) {
        chunks.push(shpList.value.slice(i, i + chunkSize));
      }
      for (let i = 0; i < chunks.length; i++) {
        await disposeFiled(chunks[i], shpType.value, groupModel);
        const planNum = ((((i + 1) * chunkSize) / count) * 100).toFixed(2);
      }
      dialogVisible.value = false;
    }
  });
};
/**
 * 映射完字段 之后的异步处理
 * @param list
 * @param shpType
 * @param groupModel
 */
const disposeFiled = (list, shpType, groupModel) => {
  //shpType 1点 2线  点需要组装x、y 线需要组装开始点xy、结束点xy
  return new Promise((resolve, reject) => {
    list.forEach((v) => {
      const item: any = {
        attribution: {},
        id: selectFrom.currentGroup.id,
        groupName: selectFrom.currentGroup.typeName,
        linkId: selectFrom.currentGroup.linkId,
        ruleAttribution: selectFrom.currentGroup.ruleAttribution,
        groupModel: groupModel,
        ruleId: updateRuleId
      };
      if (shpType == 1) {
        //点
        item.beginX = v.geometry.coordinates[0];
        item.beginY = v.geometry.coordinates[1];
      } else if (shpType == 2) {
        //线
        item.beginX = v.geometry.coordinates[0][0];
        item.beginY = v.geometry.coordinates[0][1];
        item.endX = v.geometry.coordinates[1][0];
        item.endY = v.geometry.coordinates[1][1];
      }
      selectFrom.currentGroup.fieldModelList.forEach((k) => {
        let value = v.properties[k.yz];
        if (v.properties[k.yz] && (v.properties[k.yz] + '').includes('\u0000')) {
          value = '';
        }
        item.attribution[k.fieldName] = value;
      });
      submitList.value.push(item);
    });
    resolve(null);
  });
};

/**
 * 循环遍历查找当前的某一项
 * @param list
 * @param id
 */
const handleFindTreeItem = (list, id) => {
  list.forEach((item) => {
    if (item.id == id) {
      currentTreeItem.value = JSON.parse(JSON.stringify(item));
      // 用于让用户指定属性组的字段  key1 key2
      attrList.value = getAttrList(currentTreeItem.value.fieldGroupModelList);
      initDataTree([currentTreeItem.value]);
    } else if (item.list) {
      handleFindTreeItem(item.list, id);
    }
  });
};

/**
 * 组装属性组和字段
 * @param list
 */
const getAttrList = (list) => {
  list.forEach((v) => {
    v.label = v.typeName;
    v.value = v.linkId;
    v.fieldModelList.forEach((k) => {
      k.label = `${k.fieldCn}(${k.fieldName})`;
      k.value = k.fieldName;
    });
  });
  return list;
};

/**
 * 整理规则树结构为数据树结构
 * @param list
 */
const initDataTree = (list) => {
  list.forEach((v) => {
    v.ruleId = v.id;
    v.ruleName = v.parcelName;
    v.dataState = 0;
    v.appType = 2;
    v.parcelName = v.typeName;
    delete v.id;
    if (v.graphicalMaxNum == v.graphicalMinNum) {
      for (let index = 0; index < v.graphicalMinNum - 1; index++) {
        const item = JSON.parse(JSON.stringify(v));
        list.push(item);
      }
    }
    if (v.list && v.list.length != 0) {
      initDataTree(v.list);
    }
  });
};

/**
 * 弹出校验编码
 */
const verifyCoding = () => {
  verifyDialog.value = true;
};

/**
 * 根据当前选择的属性组自己拿出来其中的字段
 * @param val
 */
const handleFieldCurrentGroup = (val) => {
  if (
    (val.ruleAttribution.type.includes('Point') && shpList.value[0].geometry.type.includes('Line')) ||
    (val.ruleAttribution.type.includes('Line') && shpList.value[0].geometry.type.includes('Point'))
  ) {
    //类型是点，但是shp里面类型是线 或者类型是线shp里面类型是点 抛错
    ElMessage.error('shp类型和您选择更新的属性组类型不匹配，请重新选择');
    selectFrom.currentGroup = '';
    return;
  }
  // 设置当前页面的值完成，在页面做一个标识
  val.finished = true;
  const list = [];
  val.fieldModelList.forEach((item) => {
    if (item.valueMethod == 'idCardScan') {
      // 用于在页面上不展示
      item.tempStatus = true;
      // 身份证识别
      const SFZSBOptions = [
        { label: 0, text: '姓名', enName: `${item.fieldName}_0`, fieldType: 'String' },
        { label: 1, text: '性别', enName: `${item.fieldName}_1`, fieldType: 'String' },
        { label: 2, text: '民族', enName: `${item.fieldName}_2`, fieldType: 'String' },
        { label: 3, text: '出生日期', enName: `${item.fieldName}_3`, fieldType: 'Date' },
        { label: 4, text: '住址', enName: `${item.fieldName}_4`, fieldType: 'String' },
        { label: 5, text: '身份证号', enName: `${item.fieldName}_5`, fieldType: 'String' },
        { label: 6, text: '签发机关', enName: `${item.fieldName}_6`, fieldType: 'String' },
        { label: 7, text: '有效期限', enName: `${item.fieldName}_7`, fieldType: 'String' },
        { label: 8, text: '身份证正面', enName: `${item.fieldName}_8`, fieldType: 'Pic' },
        { label: 9, text: '身份证反面', enName: `${item.fieldName}_9`, fieldType: 'Pic' }
      ];
      if (item.attribution && item.attribution.list && item.attribution.list.length > 0) {
        SFZSBOptions.forEach((sfz) => {
          if (item.attribution.list.includes(sfz.label)) {
            const obj = {
              fieldName: sfz.enName,
              fieldCn: sfz.text,
              fieldType: sfz.fieldType,
              valueMethod: 'idCardScan'
            };
            list.push(obj);
          }
        });
      }
    } else if (item.valueMethod == 'xttable') {
      // 用于在页面上不展示
      item.tempStatus = true;
      if (item.attribution && item.attribution.children && item.attribution.children.length > 0) {
        item.attribution.children.forEach((child) => {
          const obj = {
            fieldName: child.fieldName,
            fieldCn: child.fieldCn,
            fieldType: child.fieldType,
            valueMethod: 'xttable'
          };
          list.push(obj);
        });
      }
    }
  });
  if (list.length > 0) {
    list.forEach((v) => {
      val.fieldModelList.push(v);
    });
  }
  localfields.value = val.fieldModelList.filter((item) => !item.tempStatus);
  getLocalFild();
};

/**
 * 获取本地字段
 */
const getLocalFild = async () => {
  localfields.value.forEach((f) => {
    for (let index = 0; index < shpFields.value.length; index++) {
      if (f.fieldName.toUpperCase() == shpFields.value[index]) {
        f.yz = shpFields.value[index];
        break;
      }
    }
  });
};

/**
 * 获取属性组标签
 * @param item
 */
const getGroupLabel = (item) => {
  if (item.ruleAttribution && (item.ruleAttribution.type == 'graphicalPoint' || item.ruleAttribution.type == 'commonPoint')) {
    if (item.finished) {
      return `${item.typeName}  (点)(已映射)`;
    } else {
      return `${item.typeName}  (点)`;
    }
  } else if (item.ruleAttribution && (item.ruleAttribution.type == 'graphicalLine' || item.ruleAttribution.type == 'commonLine')) {
    if (item.finished) {
      return `${item.typeName}  (线)(已映射)`;
    } else {
      return `${item.typeName}  (线)`;
    }
  } else {
    if (item.finished) {
      return `${item.typeName} (已映射)`;
    } else {
      return `${item.typeName}  `;
    }
  }
};

/**
 * shp字段改变
 * @param val
 */
const changeShpField = (val) => {
  fieldGroupModelList.value.map((item) => {
    item.fieldModelList.map((field) => {
      if (field.valueMethod == 'xttable' && field.tempStatus) {
        if (field.attribution && field.attribution.children && field.attribution.children.length > 0)
          field.attribution.children.map((f) => {
            if (f.fieldName == val.fieldName) {
              f.yz = val.yz;
              return f;
            }
          });
        return field;
      }
    });
    return item;
  });
};

/**
 * 关闭shp子组件
 */
const handleClose = () => {
  //还需要把 file清除
  shpList.value = [];
  updateType.value = [];
  // 清空选择的shp列表
  const list = document.getElementById('fileList');
  // 清空现有列表
  list.innerHTML = '';
  // 清空上传
  const fileInput = fileRef.value;
  fileInput.value = '';
  operaType.value = null;
  emit('closeShpChild');
};

/**
 * 最终提交更新shp请求
 */
const submitUpdateShp = async () => {
  if (submitList.value.length == 0) {
    ElMessage.error('请勾选需要更新的数据');
    return;
  }

  // 拆分数组需要遵循 根据唯一值来分组，每组数据不能超过100条，如果某个唯一值数据超过100条，那么该批就这一批数据
  // 定义分组对象
  const groupedData = {};
  // 根据关联字段分组
  submitList.value.forEach((item) => {
    const key = item.attribution[selectFrom.ysChooseField];
    if (!groupedData[key]) {
      groupedData[key] = [];
    }
    groupedData[key].push(item);
  });

  const initChunks = [];
  // 处理分组后的数据，每个批次最多100条
  for (const key in groupedData) {
    const group = groupedData[key];
    initChunks.push(group);
  }
  // 再次分解 initChunks
  const finalChunks = [];
  let currentChunk = [];
  let currentCount = 0;

  for (let i = 0; i < initChunks.length; i++) {
    const group = initChunks[i];
    const groupLength = group.length;

    if (currentCount + groupLength <= 100) {
      // 如果加入当前组后不超过 100 条，就加入当前批次
      currentChunk.push(...group);
      currentCount += groupLength;
    } else {
      // 如果加入当前组后超过 100 条，将当前批次添加到最终结果，并开始新的批次
      if (currentChunk.length > 0) {
        finalChunks.push(currentChunk);
      }
      currentChunk = [...group];
      currentCount = groupLength;
    }
  }

  // 处理最后一个批次
  if (currentChunk.length > 0) {
    finalChunks.push(currentChunk);
  }

  uploadShpNum.value = submitList.value.length;
  // 使用最终拆分的批次列表
  const chunks = finalChunks;

  // this.batchDialog = false
  // 初始化上传进度弹窗
  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  // 依次上传子数组
  for (let index = 0; index < chunks.length; index++) {
    try {
      await subsectionSubmit(chunks[index], index + 1, 100);
    } catch (error) {
      console.error('上传失败:', error);
      ElMessage.error(error);
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  uploadLodingDialog.value = false;
  handleClose();
  const str = `成功${operaType.value == 1 ? '新增' : '更新'}${submitList.value.length}条数据`;
  operaType.value = null;
  ElMessageBox.alert(str, `${operaType.value == 1 ? '新增' : '更新'}成功`, {
    confirmButtonText: '确定',
    callback: (action) => {
      // 在这添加是否切换公司的标识。
      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
      sessionStorage.setItem('qiehuan_company', 'false');
      location.reload();
    }
  });
};

/**
 * 选择映射关联字段属性组
 * @param val
 */
const chooseYSGroup = (val) => {
  for (let i = 0; i < fieldGroupModelListAll.value.length; i++) {
    if (fieldGroupModelListAll.value[i].id == val) {
      ysChooseGroup.value = fieldGroupModelListAll.value[i];
      ysChooseFieldList.value = fieldGroupModelListAll.value[i].fieldModelList;
      break;
    }
  }
};

// 导入分段提交
const subsectionSubmit = (list, num, chunkSize) => {
  return new Promise((resolve, reject) => {
    updateParcelFromGroup(list, props.moduleId.toString(), shpType.value.toString()).then((res) => {
      if (res.code == 200) {
        uploadProgress.value =
          Number((((num * chunkSize) / uploadShpNum.value) * 100).toFixed(2)) > 100
            ? 100
            : Number((((num * chunkSize) / uploadShpNum.value) * 100).toFixed(2));
        // this.uploadMsg = `已成功导入${num*chunkSize+list.length}条`
        successNum.value = successNum.value + res.data;
        uploadMsg.value = `已成功导入${successNum.value}条`;
        resolve(null);
      } else {
        reject(res.msg);
      }
    });
  });
};
</script>
<style lang="scss" scoped>
.question {
  position: absolute;
  top: 25px;
  left: 120px;
  cursor: pointer;
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.verify-content {
  max-height: 400px;
  overflow: auto;
  .flex-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
}
.updateShpChild-main {
}
.dialog-row {
  margin-bottom: 10px;
}
</style>
