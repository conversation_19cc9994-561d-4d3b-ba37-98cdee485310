<template>
  <div class="icon-dialog">
    <el-dialog v-bind="$attrs" width="980px" :modal-append-to-body="false" @open="onOpen" @close="onClose">
      <template #header>
        <div class="dialog-header">
          选择图标
          <el-input v-model="key" size="small" :style="{ width: '260px' }" placeholder="请输入图标名称" :prefix-icon="Search" clearable />
        </div>
      </template>
      <ul class="icon-ul">
        <li v-for="icon in iconList" :key="icon" :class="{ 'active-item': active === icon }" @click="onSelect(icon)">
          <el-icon><component :is="icon" /></el-icon>
          <div>{{ icon }}</div>
        </li>
      </ul>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import iconListData from './utils/icon.json';

interface Props {
  current?: string;
  modelValue?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  current: '',
  modelValue: false
});

const emit = defineEmits<{
  (e: 'select', icon: string): void;
  (e: 'update:modelValue', value: boolean): void;
}>();

const originList = iconListData.map((name: string) => `el-icon-${name}`);
const iconList = ref(originList);
const active = ref<string | null>(null);
const key = ref('');

watch(key, (val) => {
  if (val) {
    iconList.value = originList.filter((name) => name.indexOf(val) > -1);
  } else {
    iconList.value = originList;
  }
});

const onOpen = () => {
  active.value = props.current;
  key.value = '';
};

const onClose = () => {
  // 关闭时的处理逻辑
};

const onSelect = (icon: string) => {
  active.value = icon;
  emit('select', icon);
  emit('update:modelValue', false);
};
</script>

<style lang="scss" scoped>
.icon-ul {
  margin: 0;
  padding: 0;
  font-size: 0;

  li {
    list-style-type: none;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    width: 16.66%;
    box-sizing: border-box;
    height: 108px;
    padding: 15px 6px 6px 6px;
    cursor: pointer;
    overflow: hidden;

    &:hover {
      background: #f2f2f2;
    }

    &.active-item {
      background: #e1f3fb;
      color: #7a6df0;
    }

    > i {
      font-size: 30px;
      line-height: 50px;
    }
  }
}

.icon-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    margin-bottom: 0;
    margin-top: 4vh !important;
    display: flex;
    flex-direction: column;
    max-height: 92vh;
    overflow: hidden;
    box-sizing: border-box;

    .el-dialog__header {
      padding-top: 14px;
    }

    .el-dialog__body {
      margin: 0 20px 20px 20px;
      padding: 0;
      overflow: auto;
    }
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
