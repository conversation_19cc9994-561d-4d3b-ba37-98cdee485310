<!-- 导出设置列表 -->
<template>
  <div class="exportSetting-main">
    <div class="content" v-show="!showEdit">
      <div class="handle">
        <el-button type="primary" size="small" @click="jumpAdd">新建导出</el-button>
      </div>
      <div class="table">
        <div class="flex-orw" v-for="(item, index) in tableData" :key="index">
          <div class="left">{{ item.exportName }}</div>
          <div class="end">
            <div class="item-hande" @click="editRow(item)">
              <el-tooltip class="item" effect="dark" content="配置" placement="top">
                <el-icon><Operation /></el-icon>
              </el-tooltip>
              <!-- <i class="el-icon-setting"></i>
              <span class="small-span">配置</span> -->
            </div>
            <div class="item-hande" @click="editTitle(item)">
              <el-tooltip class="item" effect="dark" content="修改" placement="top">
                <el-icon><Edit /></el-icon>
              </el-tooltip>
              <!-- <i class="el-icon-edit"></i>
              <span class="small-span">修改</span> -->
            </div>
            <div class="item-hande" @click="delExport(item)">
              <el-tooltip class="item" effect="dark" content="删除" placement="top">
                <el-icon><Delete /></el-icon>
              </el-tooltip>
              <!-- <i class="el-icon-delete"></i> -->
              <!-- <span class="small-span">删除</span> -->
            </div>
          </div>
        </div>
      </div>
      <div class="btn-next-step">
        <el-button type="primary" @click="handleNext" size="small" v-if="!isFinish" style="margin-right: 32px"
          >下一步 <el-icon><ArrowRight /></el-icon>
        </el-button>
        <el-button type="primary" @click="handleFinsh" size="small" v-else>完成<i class="el-icon-arrow-right el-icon--right"></i></el-button>
      </div>
    </div>
    <addExport v-show="showEdit" @backList="backList" :exportTitle="exportTitle" ref="editExport"></addExport>
    <!-- 新建导出 -->
    <el-dialog :title="isAdd ? '新建导出' : '修改'" v-model="dialogVisible" width="449px" :before-close="handleClose">
      <div style="margin-bottom: 10px"><span style="color: red">*</span>导出按钮名称</div>
      <el-input v-model="exportTitle" placeholder="请输入按钮名称" v-show="isAdd"></el-input>
      <el-input v-model="exportMsg.exportName" placeholder="请输入按钮名称" v-show="!isAdd"></el-input>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitAdd">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, defineProps, defineEmits, ref as vueRef } from 'vue';
import addExport from './addExport.vue';
import { exportSettingList, saveExportSetting } from '@/api/modal';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { Operation, Edit, Delete, ArrowRight } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps<{
  isFinish: boolean;
}>();

// 定义 emits
const emit = defineEmits<{
  (event: 'nextStep', step: number): void;
}>();

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
// 响应式数据
const tableData = ref<any[]>([]);
const funId = ref(0);
const showEdit = ref(false);
const dialogVisible = ref(false);
const exportTitle = ref('');
const exportMsg = ref<{ [key: string]: any }>({});
const isAdd = ref(true);
const editExport = vueRef<InstanceType<typeof addExport>>();

// 计算属性
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = Number(router.currentRoute.value.query.id);
  }
  return moduleId;
});

// 获取导出列表
const getData = async () => {
  let moduleIdValue = moduleId.value;
  if (router.currentRoute.value.query.id) {
    moduleIdValue = Number(router.currentRoute.value.query.id);
  }
  const params: { moduleId: number; companyId?: number } = {
    moduleId: moduleIdValue
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = Number(router.currentRoute.value.query.companyId);
  if (companyId) {
    params.companyId = companyId;
  }
  try {
    const res = await exportSettingList(params);
    if (res.code === 200) {
      tableData.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取导出列表失败', error);
  }
};

// 编辑行
const editRow = (row: any) => {
  if (editExport.value) {
    editExport.value.getNowData(row);
  }
  showEdit.value = true;
};

// 返回列表
const backList = () => {
  showEdit.value = false;
  exportTitle.value = '';
  getData();
};

// 新建导出
const jumpAdd = () => {
  isAdd.value = true;
  dialogVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  exportTitle.value = '';
  dialogVisible.value = false;
};

// 提交名称
const submitAdd = async () => {
  if (isAdd.value) {
    if (exportTitle.value === '') {
      ElMessage.error('请输入导出按钮名称');
      return;
    }
    if (editExport.value) {
      editExport.value.init();
    }
    dialogVisible.value = false;
    showEdit.value = true;
  } else {
    try {
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = Number(router.currentRoute.value.query.companyId);
      if (companyId) {
        params.companyId = companyId;
      }
      const res = await saveExportSetting(exportMsg.value);
      if (res.code === 200) {
        ElMessage.success('操作成功');
        dialogVisible.value = false;
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('保存导出设置失败', error);
    }
  }
};

// 修改导出名字
const editTitle = (item: any) => {
  isAdd.value = false;
  exportMsg.value = item;
  dialogVisible.value = true;
};

// 删除导出
const delExport = (item: any) => {
  ElMessageBox.confirm('确定要删除该导出吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      item.delFlag = 1;
      try {
        // 设置公司私有模块的数据 需要传递公司id
        const companyId = Number(router.currentRoute.value.query.companyId);
        if (companyId) {
          params.companyId = companyId;
        }
        const res = await saveExportSetting(item);
        if (res.code === 200) {
          ElMessage.success('操作成功');
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('删除导出失败', error);
      }
    })
    .catch(() => {
      // 取消操作
    });
};

// 完成
const handleFinsh = () => {
  router.push('/modal');
};

// 下一步
const handleNext = () => {
  emit('nextStep', 7);
};

// 返回规则列表的时候强行保存修改导出设置
const strongSubmit = () => {
  if (showEdit.value && editExport.value) {
    editExport.value.endSubmit();
  }
};

onMounted(() => {
  getData();
});
defineExpose({
  getData
});
</script>
<style lang="scss" scoped>
.exportSetting-main {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;

  .content {
    width: 723px;
    height: 100%;
    overflow: auto;
    background: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    .handle {
      display: flex;
      justify-content: flex-start;
      margin: 16px 16px 0px 16px;
    }
    .table {
      width: 100%;
      height: calc(100% - 100px);
      overflow: auto;
      padding: 16px;
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgba(176, 175, 175, 0.5);
      }
      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        background: rgba(248, 248, 248, 0.1);
      }
      .flex-orw {
        background-color: rgba(246, 247, 248, 1);
        height: 40px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        margin-bottom: 12px;
        border-radius: 4px;
        cursor: pointer;
        .left {
          margin-left: 16px;
          font-weight: bold;
          color: #161d26;
        }
        .end {
          margin-right: 16px;
          display: flex;
          flex-direction: row;
          .item-hande {
            margin-left: 8px;
            margin-right: 8px;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
            .small-span {
              margin-left: 4px;
              font-size: 12px;
            }
          }
          .item-hande:hover {
            color: var(--current-color);
          }
        }
      }
    }
    .btn-next-step {
      position: fixed;
      bottom: 40px;
      // right: 0;
      width: 723px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
