<template>
  <div>
    <el-form label-width="80px" size="small">
      <el-form-item label="切换触发">
        <el-select v-model="attributeCopy.trigger" placeholder="请选择触发切换方式">
          <el-option label="hover" value="hover" />
          <el-option label="click" value="click" />
        </el-select>
      </el-form-item>
      <el-form-item label="图片填充">
        <el-select v-model="attributeCopy.fit" placeholder="请选择图片填充方式">
          <el-option label="fill" value="fill" />
          <el-option label="contain" value="contain" />
          <el-option label="cover" value="cover" />
          <el-option label="none" value="none" />
          <el-option label="scale-down" value="scale-down" />
        </el-select>
      </el-form-item>
      <el-form-item label="轮播图片">
        <el-upload
          :action="fileUrl"
          :headers="headers"
          name="files"
          :before-upload="beforeBgImgUpload"
          :show-file-list="false"
          :on-error="handleError"
          list-type="picture"
          :on-success="handleAvatarSuccess"
        >
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
      </el-form-item>
      <el-row :gutter="4">
        <el-col :span="12" v-for="(item, index) in attributeCopy.imgUrls" :key="index" class="imgBlock">
          <authImg :authSrc="`${baseUrl}${item}?att=1`" :width="'100%'" :height="'100%'" />
          <div class="dleMask">
            <div style="width: 24px; margin: 40px auto" @click="handleRemove(index)">
              <i class="el-icon-delete" />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import authImg from '@/components/authImg/index.vue';
defineOptions({
  name: 'cpt-carousel-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);

const fileUrl = import.meta.env.VITE_APP_BASE_API + '/qjt/file/multi/upload';
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};
const fileList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';

// --- 定义方法 ---
const beforeBgImgUpload = (file: any) => {
  const isIMG = file.type.substr(0, 5) === 'image';
  const isLt5M = file.size / 1024 / 1024 < 25;
  if (!isIMG) {
    ElMessage.error('上传图片只能是图片格式!');
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 25MB!');
  }
  return isIMG && isLt5M;
};
const handleRemove = (index: number) => {
  attributeCopy.imgUrls.splice(index, 1);
};
const handleAvatarSuccess = (res: any) => {
  if (res.code == 200) {
    attributeCopy.imgUrls.push(res.data[0].path);
    ElMessage({
      type: 'success',
      message: '上传成功'
    });
  } else {
    ElMessage.error(res.msg);
  }
};
const handleError = (err: any, file: any) => {
  ElMessage.error('图片上传失败，使用本地路径');
  attributeCopy.imgUrls.push(file.url);
};
</script>

<style scoped>
.imgBlock {
  position: relative;
  height: 100px;
}
.imgBlock:hover .dleMask {
  display: block;
  cursor: pointer;
}
.dleMask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  background: #6667;
  color: #fff;
  font-size: 20px;
  display: none;
}
</style>
