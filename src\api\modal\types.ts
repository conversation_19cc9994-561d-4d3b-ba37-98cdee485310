import { ModuleData } from '@/api/modal/types';
/**
 * 模块数据对象
 */
export interface ModuleData {
  [key: string]: any;
}

/**
 * 模块查询参数
 */
export interface ModuleQuery {
  [key: string]: any;
}

/**
 * 规则数据
 */
export interface RuleData {
  [key: string]: any;
}

/**
 * 工具包数据
 */
export interface ToolData {
  [key: string]: any;
}

/**
 * 字段组数据
 */
export interface FieldGroupData {
  [key: string]: any;
}

/**
 * 导出设置数据
 */
export interface ExportData {
  [key: string]: any;
}

/**
 * 模块id 设置
 */
export interface ModuleInfo {
  moudleId: string;
}
