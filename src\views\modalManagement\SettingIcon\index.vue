<!-- 设置模块图标 -->
<template>
  <div class="">
    <el-dialog title="设置图标" v-model="dialogVisible" width="40%" :before-close="handleClose" @opened="handleIconOpen">
      <div class="icon-contianer">
        <div class="icon-custom-contianer" @click="handleOpenCropperImage">
          <span class="custom-text">自定义</span>
        </div>
        <div @click="handleCurrentSelect(customPicItem)">
          <div
            class="kuang"
            v-if="cropperImageUrl !== null && cropperImageUrl !== ''"
            :style="{ 'background-color': customSelected ? '#f6f7f8' : '' }"
          >
            <custom-img :authSrc="`${baseUrl}${cropperImageUrl}?att=1`" :width="'36px'" :height="'36px'" :radios="'4px'" />
          </div>
        </div>
        <div v-for="item in iconList" :key="item.name" @click="handleCurrentSelect(item)">
          <div class="kuang" :style="{ 'background-color': item.selected ? '#f6f7f8' : '' }">
            <svg-icon-show
              class-name="svg-item"
              :icon-class="item.name"
              :style="{ color: item.selected && isShow ? 'var(--current-color)' : '#333' }"
            />
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 自定义照片的剪裁照片 -->
    <cropper-image ref="cropperImageRef" :iconName="iconName" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import cropperImage from './cropperImage.vue';
import customImg from './customImg.vue';
import svgIconShow from '../svgIcon/index.vue';

const userStore = useUserStore();

// 定义 props
const props = defineProps<{
  iconVisible: boolean;
  defaultList: Array<{ name: string; selected: boolean }>;
  iconSelected: string;
  isShow: boolean;
  iconName: string;
}>();

// 定义 emits
const emit = defineEmits<{
  (e: 'closeSettingIcon'): void;
  (e: 'submitIcon', icon: string): void;
}>();

const dialogVisible = computed({
  get() {
    return props.iconVisible;
  },
  set(value) {
    // 触发关闭事件
    // emit('closeOrder');
  }
});

// 回显图片的路径
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/system/user/profile/downloadone/`;
// 存在 session 中的图标地址
const cropperImageUrl = ref('');
// 当前选中的图标，传递给父组件的值
const currentIcon = ref('');
// 当前自定义的整个数据的内容
const customPicItem = ref<Record<string, any> | null>(null);
// 自定义选择项目
const customSelected = ref(false);

// 定义组件引用
const cropperImageRef = ref<InstanceType<typeof cropperImage> | null>(null);

const iconList = ref(props.defaultList);

// 计算属性
const customPic = computed(() => useModalStore().customPic);

// 监听 customPic 变化
watch(
  customPic,
  (val) => {
    if (props.iconName === '自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('customPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '模块自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('modalCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '属性组自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('GroupCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '树图标自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('TreeCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听 iconSelected 变化
watch(
  () => props.iconSelected,
  (val) => {
    props.defaultList.forEach((item) => {
      item.selected = item.name === val;
    });
  },
  { deep: true }
);

const handleGetCropperIamge = () => {
  const url = sessionStorage.getItem('cropperIcon');
  cropperImageUrl.value = url || '';
  const customPicItemData = sessionStorage.getItem('customPic');
  customPicItem.value = customPicItemData ? JSON.parse(customPicItemData) : null;
};

const handleIconOpen = () => {
  if (props.iconName === '自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('customPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '模块自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('modalCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '属性组自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('GroupCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '树图标自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('TreeCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  }
};

const handleClose = () => {
  emit('closeSettingIcon');
  props.defaultList.forEach((defaul) => {
    defaul.selected = false;
  });
};

const handleOpenCropperImage = () => {
  cropperImageRef.value?.editCropper();
};

const handleCurrentSelect = (item: any) => {
  if (item.iconName === props.iconName) {
    customSelected.value = true;
    iconList.value.forEach((defaul) => {
      defaul.selected = false;
    });
  } else {
    iconList.value.forEach((defaul) => {
      if (defaul.name == item.name) {
        defaul.selected = true;
      } else {
        defaul.selected = false;
      }
    });
    customSelected.value = false;
  }
  currentIcon.value = item.name;
};

const handleSubmit = () => {
  emit('submitIcon', currentIcon.value);
  // 关闭弹框
  handleClose();
};

onMounted(() => {
  handleGetCropperIamge();
});
</script>

<style lang="scss" scoped>
.icon-contianer {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .icon-custom-contianer {
    width: 56px;
    height: 56px;
    line-height: 20px;
    border-radius: 4px;
    background-color: #ffffff;
    text-align: center;
    border: 1px dashed #8291a980;
    margin: 8px 0 8px 8px;
    .custom-text {
      height: 56px;
      line-height: 56px;
      color: rgba(130, 145, 169, 1);
      font-size: 12px;
      text-align: center;
      font-family: PingFangSC-regular;
    }
  }
}
.kuang {
  width: 56px;
  height: 56px;
  margin: 8px 0 8px 8px;
  padding: 5px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: #f6f7f8;
    :deep(&) {
      .svg-icon {
        transform: translateX(-50px);
        z-index: 999;
      }
    }
  }
  .svg-item {
    width: 36px;
    height: 36px;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    filter: drop-shadow(50px 0 var(--current-color));
  }
}
</style>
