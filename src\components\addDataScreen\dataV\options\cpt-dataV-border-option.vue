<template>
  <el-form label-width="100px">
    <el-form-item label="边框样式">
      <el-select v-model="attributeCopy.borderType" placeholder="请选择">
        <el-option v-for="item in borderTypes" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="边框颜色1">
      <el-color-picker v-model="attributeCopy.borderColor1" show-alpha />
    </el-form-item>
    <el-form-item label="边框颜色2">
      <el-color-picker v-model="attributeCopy.borderColor2" show-alpha />
    </el-form-item>
    <el-form-item label="背景颜色">
      <el-color-picker v-model="attributeCopy.backgroundColor" show-alpha />
    </el-form-item>
    <div v-if="attributeCopy.borderType === 'dv-border-box-8'">
      <el-form-item label="动画时长">
        <el-input-number v-model="attributeCopy.dur" :min="1" :max="60" />
      </el-form-item>
      <el-form-item label="动画方向">
        <el-select v-model="attributeCopy.reverse" placeholder="请选择">
          <el-option label="逆时针" :value="true" />
          <el-option label="顺时针" :value="false" />
        </el-select>
      </el-form-item>
    </div>
    <div v-if="attributeCopy.borderType === 'dv-border-box-11'">
      <el-form-item label="标题">
        <el-input v-model="attributeCopy.borderTitle" />
      </el-form-item>
      <el-form-item label="标题宽度">
        <el-input-number v-model="attributeCopy.titleWidth" :min="10" />
      </el-form-item>
    </div>
  </el-form>
</template>

<script setup lang="ts">
defineOptions({
  name: 'cpt-dataV-border-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();
const attributeCopy = computed(() => props.attribute);

const borderTypes = [
  { value: 'dv-border-box-1', label: '样式1' },
  { value: 'dv-border-box-2', label: '样式2' },
  { value: 'dv-border-box-3', label: '样式3' },
  { value: 'dv-border-box-4', label: '样式4' },
  { value: 'dv-border-box-5', label: '样式5' },
  { value: 'dv-border-box-6', label: '样式6' },
  { value: 'dv-border-box-7', label: '样式7' },
  { value: 'dv-border-box-8', label: '样式8' },
  { value: 'dv-border-box-9', label: '样式9' },
  { value: 'dv-border-box-10', label: '样式10' },
  { value: 'dv-border-box-12', label: '样式12' },
  { value: 'dv-border-box-13', label: '样式13' }
];
</script>

<style scoped></style>
