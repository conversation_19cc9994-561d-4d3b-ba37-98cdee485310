<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" show-alpha />
    </el-form-item>
    <el-form-item label="副标题颜色">
      <el-color-picker v-model="attributeCopy.subLableColor" show-alpha />
    </el-form-item>
    <el-link type="primary" @click="addOne">新增</el-link>
    <el-table :data="attribute.indicator" style="width: 100%" border>
      <el-table-column label="名字" prop="name">
        <template #default="{ row }">
          <el-input v-model="row.name"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="最大值" prop="max">
        <template #default="{ row }">
          <el-input v-model="row.max" type="number"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="60">
        <template #default="{ row, $index }">
          <el-link type="danger" @click="delOne(row, $index)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-radar-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);

const delOne = (item: any, index: number) => {
  ElMessageBox.confirm('确定要删除该内容？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      attributeCopy.value.indicator.splice(index, 1);
    })
    .catch(() => {});
};
const addOne = () => {
  attributeCopy.value.indicator.push({
    name: `数据${attributeCopy.value.indicator.length + 1}`,
    max: 0
  });
};
</script>

<style scoped></style>
