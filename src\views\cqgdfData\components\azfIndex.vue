<!-- 拆迁户查询 -->
<template>
  <div class="azfIndex-main">
    <div class="handle-search">
      <div class="item">
        <div class="label">项目名称</div>
        <div class="right">
          <el-select v-model="search.moduleName" placeholder="请选择项目" @change="moduleChange">
            <el-option v-for="item in moduleList" :key="item.id" :label="item.moduleName" :value="item.id"> </el-option>
          </el-select>
        </div>
      </div>
      <div class="item">
        <div class="label">小区名称</div>
        <div class="right">
          <el-input v-model="search.areaName" placeholder="请输入小区名称" clearable />
        </div>
      </div>
      <div class="item">
        <div class="label">姓名</div>
        <div class="right">
          <el-input v-model="search.name" placeholder="请输入姓名" clearable />
        </div>
      </div>
      <div class="item">
        <div class="label">查询时间</div>
        <div class="right">
          <el-date-picker
            v-model="search.rangMonth"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="timestamp"
          />
        </div>
      </div>
      <div class="item" style="margin-left: 40px">
        <el-button type="primary" @click="handleSearch(false)"
          ><el-icon><Search /></el-icon>查询</el-button
        >
        <el-button type="info" @click="handleReset"
          ><el-icon><RefreshRight /></el-icon>重置</el-button
        >
      </div>
    </div>
    <el-button type="primary" @click="downloadToExcel"
      ><el-icon><Download /></el-icon>批量导出</el-button
    >
    <div :key="tableKey">
      <el-table :data="tableData" :height="tableHeight" ref="multiHeaderTable" v-loading="loading" style="width: 100%; margin-top: 20px" border>
        <el-table-column label="序号" type="index" fixed />
        <el-table-column label="被征收人姓名" prop="qlrName" width="120" fixed />
        <el-table-column label="身份证号" prop="idCard" width="180" />
        <el-table-column label="联系电话" prop="phone" width="120" />
        <el-table-column label="项目名称" prop="moduleName" width="220" />
        <el-table-column label="小区名称" prop="areaName" width="220" />
        <el-table-column label="已发放过渡费" width="120" prop="yffgdf" />
        <el-table-column :label="formatMonth(item)" v-for="(item, index) in monthTimestamps" :key="index" align="center">
          <el-table-column label="金额" align="center">
            <template #default="scope">{{ scope.row.nowgdf[index].money }}</template>
          </el-table-column>
          <el-table-column label="是否发放" align="center">
            <template #default="scope">
              <span v-if="scope.row.nowgdf[index].sfff === true" style="color: #67c23a">已发放</span>
              <span v-else style="color: #f56c6c">未发放</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElTable } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { getModuleList, selectRules, selectParcelFromTree } from '@/api/modal';
import * as XLSX from 'xlsx';
import FileSaver from 'file-saver';

// 类型定义
interface SearchForm {
  moduleName: string | number;
  areaName: string;
  rangMonth: string[];
  name: string;
}

interface Module {
  id: string | number;
  moduleName: string;
  [key: string]: any;
}

interface CountParameter {
  FWYSRQ: string;
  GDMJ: string;
  GDQX: string;
}

interface FfgdfOk {
  ZFJE: string | number;
  ZFRQ: string | number;
}

interface NowgdfItem {
  money: string;
  month: number;
  sfff: boolean | string;
}

interface TableDataItem {
  moduleName: string;
  qlrName: string;
  idCard: string;
  phone: string;
  areaName: string;
  ffgdfok: FfgdfOk[];
  nowgdf: NowgdfItem[];
  countParameter: CountParameter;
  yffgdf: string | number;
  remark: string;
}

interface ConditionField {
  linkId?: string | null;
  name?: string;
  operator?: string;
  relation?: string;
  type: number;
  value?: string[] | string;
  top?: boolean;
}

interface TreeNode {
  parcelName: string;
  ruleId: string | number;
  needData: boolean;
  children?: any;
  [key: string]: any;
}

// 数据定义
const search = reactive<SearchForm>({
  moduleName: '', // 项目名称
  areaName: '', // 小区名称
  rangMonth: [], // 查询时间
  name: '' // 姓名
});

const tableData = ref<TableDataItem[]>([]);
const tableHeight = ref(window.innerHeight - 350 + 'px');
const moduleList = ref<Module[]>([]); // 已发布模块列表
const chooseNode = ref<TreeNode[] | null>(null); // 选择的小区结构根树
const linkId = ref<string | null>(null); // 条件的属性组linkId
const loading = ref(false);
const moduleName = ref(''); // 项目名称 组装的时候需要
const qlrGroupLinkId = ref<string | null>(null); // 找权利人的linkId 用于去除没有权利人的数据
const monthTimestamps = ref<number[]>([]); // 查询时间生成的月份列表
const tableKey = ref(0); // 用于强制重新渲染的key
const zxqkLinkId = ref<string | null>(null); // 执行情况linkId
const multiHeaderTable = ref<InstanceType<typeof ElTable>>();

// 获取模块列表
const getModuleListData = async () => {
  try {
    const res = await getModuleList(['1']);
    if (res.code === 200) {
      moduleList.value = res.data;
      // 默认第一个选中
      if (res.data.length > 0) {
        search.moduleName = res.data[0].id;
        moduleName.value = res.data[0].moduleName;

        const resp = await selectRules({ moduleId: search.moduleName });
        if (resp.code === 200) {
          for (let i = 0; i < resp.data.length; i++) {
            if (resp.data[i].typeName === '被征收区域') {
              chooseNode.value = simplifyTree([resp.data[i]]);
              setConditionRule([resp.data[i]]);
              // 执行查询
              handleSearch(true);
              break;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('获取模块列表失败', error);
  }
};

// 查询
const handleSearch = (isInit: boolean) => {
  // 是否是初始化 初始化不用查月份了
  // 先根据查询时间生成过渡费列表
  if (search.rangMonth.length === 0) {
    ElMessage.error('请至少选择查询时间！！！');
    tableData.value = [];
    return;
  }
  if (!search.moduleName) {
    ElMessage.error('请选择项目！！！');
    tableData.value = [];
    return;
  }
  if (!isInit) {
    getMonthTimestamps();
  }

  if (!chooseNode.value || chooseNode.value.length === 0) {
    ElMessage.error('缺少必要的查询结构！');
    return;
  }

  const treeNode = JSON.parse(JSON.stringify(chooseNode.value[0]));

  // 如果有小区名字，需要先给结构树的小区名字赋值
  treeNode.parcelName = '';

  // 有姓名的需要在结构里面把姓名用条件筛选的方式加进去
  const conditionFields: ConditionField[] = [];
  if (search.name) {
    conditionFields.push({
      linkId: linkId.value,
      name: 'QLRXX_0',
      operator: '=',
      relation: 'and',
      type: 1,
      value: [search.name],
      top: false
    });
  } else {
    // 空的时候需要抛出没有权利人的数据
    conditionFields.push({
      linkId: linkId.value,
      name: 'QLRXX_0',
      operator: 'is not null',
      relation: 'and',
      type: 1,
      top: false
    });
  }

  conditionFields.push({
    value: 'and',
    type: 2
  });

  // 增加执行情况查询
  conditionFields.push({
    linkId: zxqkLinkId.value,
    name: 'AZFFW',
    operator: 'is not null',
    relation: 'and',
    type: 1,
    top: false
  });

  // 有小区筛选
  if (search.areaName) {
    conditionFields.push({
      value: 'and',
      type: 2
    });
    conditionFields.push({
      linkId: zxqkLinkId.value,
      name: 'AZXQ',
      operator: '=',
      relation: 'and',
      value: search.areaName,
      type: 1,
      top: false
    });
  }

  if (treeNode.children?.children?.children?.children) {
    treeNode.children.children.children.children.needData = true;
    if (conditionFields.length !== 0) {
      // 代表输入了姓名的
      treeNode.children.children.children.children.conditionFields = conditionFields;
    }
  }

  loading.value = true;

  selectParcelFromTree(treeNode)
    .then((res) => {
      loading.value = false;
      if (res.code === 200) {
        tableData.value = [];
        tableKey.value += 1; // 更新key以触发重新渲染

        // 组装数据
        const list: any[] = [];

        // 组装拆迁户信息 直接找到户
        res.data.forEach((v: any) => {
          v.list.forEach((q: any) => {
            q.list.forEach((k: any) => {
              k.list.forEach((o: any) => {
                o.list.forEach((w: any) => {
                  const names: string[] = [];
                  w.fieldInstanceModels.forEach((e: any) => {
                    if (e.groupName === '执行情况' && e.attribution.AZLX === '房屋') {
                      names.push(e.attribution.AZXQ);
                    }
                  });
                  w.areaName = names.join(','); // 小区名字
                  list.push(w);
                });
              });
            });
          });
        });

        // 得到数据 库存的拆迁户 需要组装成需要的样子

        list.forEach((v: any) => {
          const obj: TableDataItem = {
            moduleName: moduleName.value, // 项目名字
            qlrName: '',
            idCard: '',
            phone: '',
            areaName: v.areaName, // 小区名字
            ffgdfok: [], // 已发放过渡费列表
            nowgdf: [], // 当前过渡费列表
            countParameter: {
              FWYSRQ: '', // 房屋验收日期
              GDMJ: '', // 过渡面积
              GDQX: '' // 过渡期限
            },
            yffgdf: '',
            remark: '' // 备注
          };

          // 找权利人
          for (let i = 0; i < v.fieldInstanceModels.length; i++) {
            if (v.fieldInstanceModels[i].groupName === '权利人') {
              obj.qlrName = v.fieldInstanceModels[i].attribution.QLRXX_0 || '';
              obj.idCard = v.fieldInstanceModels[i].attribution.QLRXX_5 || '';
              obj.phone = v.fieldInstanceModels[i].attribution.LXDH || '';
              break;
            }
          }

          // 找所有已支付账单
          v.fieldInstanceModels.forEach((item: any) => {
            if (item.groupName === '过渡费支付') {
              obj.ffgdfok.push({
                'ZFJE': item.attribution.ZFJE,
                'ZFRQ': item.attribution.ZFRQ
              });
            }
          });

          // 得到所有已支付账单就可以得到已发放总额
          if (obj.ffgdfok.length !== 0) {
            let yffgdf = 0;
            obj.ffgdfok.forEach((item) => {
              yffgdf = yffgdf + Number(item.ZFJE);
            });
            obj.yffgdf = yffgdf;
          }

          // 找超期过渡费计算参数
          for (let i = 0; i < v.fieldInstanceModels.length; i++) {
            if (v.fieldInstanceModels[i].groupName === '过渡费支付') {
              obj.countParameter.FWYSRQ = v.fieldInstanceModels[i].attribution.FWYSRQ;
              obj.countParameter.GDMJ = v.fieldInstanceModels[i].attribution.GDMJ;
              obj.countParameter.GDQX = v.fieldInstanceModels[i].attribution.GDQX;
              break;
            }
          }

          // 最后根据选择的日期得到的月份 计算出每个月对应的超期过渡费用
          monthTimestamps.value.forEach((q) => {
            // 需要计算该月份距离 房屋验收日期+过渡期限日期的第几个月
            const monthNum = calculateMonthsDifference(q, Number(obj.countParameter.FWYSRQ));
            const money = handleResultMap(monthNum, Number(obj.countParameter.GDMJ));
            const nowgdf_item: NowgdfItem = {
              money: money, // 金钱
              month: q, // 月份
              sfff: ''
            };

            // 如果当前月份在已发放月份里面，则标记为已发放
            if (obj.ffgdfok.length !== 0) {
              let flg = false; // 表示未找到
              for (let i = 0; i < obj.ffgdfok.length; i++) {
                if (areInSameMonth(q, Number(obj.ffgdfok[i].ZFRQ))) {
                  flg = true;
                  break;
                }
              }
              if (flg) {
                nowgdf_item.sfff = true;
              }
            }
            obj.nowgdf.push(nowgdf_item);
          });

          tableData.value.push(obj);
        });

      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('查询失败', error);
    });
};

// 传入两个时间戳判断是不是一个月
const areInSameMonth = (timestamp1: number, timestamp2: number): boolean => {
  const date1 = new Date(timestamp1);
  const date2 = new Date(timestamp2);

  // 获取年份和月份
  const year1 = date1.getFullYear();
  const month1 = date1.getMonth(); // 注意：getMonth() 返回的月份是从0开始的（0-11）
  const year2 = date2.getFullYear();
  const month2 = date2.getMonth();

  // 比较年份和月份
  return year1 === year2 && month1 === month2;
};

// 通过指定时间 计算房屋验收时间+过渡期限 间隔了几个月
const calculateMonthsDifference = (specifiedMonthTimestamp: number, houseAcceptanceTimestamp: number): number => {
  // specifiedMonthTimestamp指定月份 houseAcceptanceTimestamp 房屋验收日期

  // 将时间戳转换为Date对象
  const monthDate = new Date(specifiedMonthTimestamp);
  const inspectionDate = new Date(houseAcceptanceTimestamp);

  // 设置验收日期后2年的日期
  const twoYearsLater = new Date(inspectionDate);
  // 需要把时间设置为当前月份的第一天
  twoYearsLater.setDate(1);
  twoYearsLater.setFullYear(inspectionDate.getFullYear() + 2);

  // 计算月份差
  let monthDifference = 0;
  const currentDate = new Date(twoYearsLater);

  // 逐月递增，直到超过或等于月份时间戳
  while (currentDate < monthDate) {
    currentDate.setMonth(currentDate.getMonth() + 1);
    monthDifference++;
  }

  // 如果月份时间戳小于等于验收时间戳+2年，则返回0
  return monthDifference > 0 ? monthDifference : 0;
};

// 根据计算的结果 计算当前应交的超期费用
const handleResultMap = (month: number, GDMJ: number): string => {
  let result: number = 0;
  if (month === 1) {
    result = Number((GDMJ * 11).toFixed(2));
  } else if (month === 2) {
    result = Number((GDMJ * 12.1).toFixed(2));
  } else if (month === 3) {
    result = Number((GDMJ * 13.31).toFixed(2));
  } else if (month === 4) {
    result = Number((GDMJ * 14.64).toFixed(2));
  } else if (month === 5) {
    result = Number((GDMJ * 16.1).toFixed(2));
  } else if (month === 6) {
    result = Number((GDMJ * 17.71).toFixed(2));
  } else if (month === 7) {
    result = Number((GDMJ * 19.48).toFixed(2));
  } else if (month === 8) {
    result = Number((GDMJ * 21.43).toFixed(2));
  } else if (month === 9) {
    result = Number((GDMJ * 23.57).toFixed(2));
  } else if (month === 10) {
    result = Number((GDMJ * 25.93).toFixed(2));
  } else if (month === 11) {
    result = Number((GDMJ * 28.52).toFixed(2));
  } else if (month === 12) {
    result = Number((GDMJ * 31.37).toFixed(2));
  } else if (month === 13) {
    result = Number((GDMJ * 34.51).toFixed(2));
  } else if (month === 14) {
    result = Number((GDMJ * 37.96).toFixed(2));
  } else if (month === 15) {
    result = Number((GDMJ * 41.76).toFixed(2));
  } else if (month === 16) {
    result = Number((GDMJ * 45.94).toFixed(2));
  } else if (month >= 17) {
    result = Number((GDMJ * 50).toFixed(2));
  }
  return result.toString();
};

// 重置
const handleReset = () => {
  search.moduleName = '';
  search.areaName = '';
  search.name = '';
  search.rangMonth = [];
  // handleSearch(false)
};

// 为了获取拆迁户权利人名称的条件筛选
const setConditionRule = (list: any[]): void => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].typeName === '被征收户') {
      for (let j = 0; j < list[i].fieldGroupModelList.length; j++) {
        if (list[i].fieldGroupModelList[j].typeName === '权利人') {
          linkId.value = list[i].fieldGroupModelList[j].linkId;
          break;
        }
      }
      // 得到执行情况的linkId
      for (let j = 0; j < list[i].fieldGroupModelList.length; j++) {
        if (list[i].fieldGroupModelList[j].typeName === '执行情况') {
          zxqkLinkId.value = list[i].fieldGroupModelList[j].linkId;
          break;
        }
      }
      break;
    }
    if (list[i].list && list[i].list.length !== 0) {
      setConditionRule(list[i].list);
    }
  }
};

// 选择了项目去通过模块id查询模块树结构
const moduleChange = (e: string | number) => {
  for (let i = 0; i < moduleList.value.length; i++) {
    if (moduleList.value[i].id === e) {
      moduleName.value = moduleList.value[i].moduleName;
      break;
    }
  }
  selectRules({ moduleId: e })
    .then((res) => {
      if (res.code === 200) {
        for (let i = 0; i < res.data.length; i++) {
          if (res.data[i].typeName === '被拆迁区域') {
            chooseNode.value = simplifyTree([res.data[i]]);
            break;
          }
        }
      }
    })
    .catch((error) => {
      console.error('获取模块树结构失败', error);
    });
};

// 迭代组装树结构
const simplifyTree = (tree: any[]): TreeNode[] => {
  return tree.map((node) => {
    const simplifiedNode: TreeNode = { parcelName: '', ruleId: node.id, needData: false };
    if (node.list && node.list.length > 0) {
      const item = simplifyTree(node.list);
      simplifiedNode.children = item[0];
    }
    return simplifiedNode;
  });
};

// 通过开始结束时间戳得到中间月份
const getMonthTimestamps = (): void => {
  if (!search.rangMonth || search.rangMonth.length < 2) return;

  const current = new Date(parseInt(search.rangMonth[0]));
  current.setHours(0, 0, 0, 0); // 确保时间是当天的00:00:00

  const end = new Date(parseInt(search.rangMonth[1]));
  end.setHours(0, 0, 0, 0); // 同样确保结束日期的时间是00:00:00
  // 由于我们不包含结束月份，所以我们需要将结束日期设置为下一个月的第一天的前一秒
  end.setMonth(end.getMonth());
  end.setMilliseconds(-1);

  const timestamps: number[] = [];
  while (current <= end) {
    timestamps.push(current.getTime());
    current.setMonth(current.getMonth() + 1);
    // 如果跨年了，需要重置日期部分（虽然setMonth已经处理了这个问题，但这里为了清晰可以加上）
    current.setDate(1);
  }

  monthTimestamps.value = timestamps;
};

// 格式时间戳到月
const formatMonth = (timestamp: number): string => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1并补零
  return `${year}-${month}`; // 只显示年和月
};

// 导出为excel
const downloadToExcel = () => {
  if (tableData.value.length === 0) {
    ElMessage.error('请等待数据刷新成功再导出！！！');
    return;
  }

  ElMessageBox.confirm('确定要导出数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      nextTick(() => {
        try {
          if (!multiHeaderTable.value) {
            ElMessage.error('表格元素不存在');
            return;
          }

          const $e = multiHeaderTable.value.$el;
          let $table = $e.querySelector('.el-table__fixed');
          if (!$table) {
            $table = $e;
          }

          const wb = XLSX.utils.table_to_book($table, { raw: true });
          const wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' });

          const startDate = formatMonth(parseInt(search.rangMonth[0]));
          const endtDate = formatMonth(parseInt(search.rangMonth[1]));

          FileSaver.saveAs(
            new Blob([wbout], { type: 'application/octet-stream' }),
            `${moduleName.value}${startDate}-${endtDate}超期过渡费汇总表.xlsx`
          );

          ElMessage({
            type: 'success',
            message: '导出成功'
          });
        } catch (e) {
          console.error('导出失败', e);
          ElMessage.error('导出失败');
        }
      });
    })
    .catch(() => {
      // 用户取消
    });
};

// 初始化
onMounted(() => {
  const end = new Date();
  const start = new Date();
  start.setMonth(start.getMonth() - 11);
  search.rangMonth = [start.getTime().toString(), end.getTime().toString()];

  // 通过开始和结束月份得到中间的月份
  getMonthTimestamps();

  // 先查询所有已发布的模块
  getModuleListData();
});
</script>

<style lang="scss" scoped>
.azfIndex-main {
  .handle-title {
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: rgba(0, 0, 0, 0.1) solid 1px;
  }

  .handle-search {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 10px;

    .item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }

      .right {
        flex: 1;
      }
    }
  }
}
</style>
