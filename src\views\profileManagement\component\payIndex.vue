<template>
  <container-card>
    <div class="title-row" @click="handelGoBack">
      <div style="cursor: pointer" class="text"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <!-- <benefit-index></benefit-index> -->
    <iframe :src="url" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
  </container-card>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getToken } from '@/utils/auth';

// 路由实例
const router = useRouter();

// 响应式数据
const token = ref(getToken());
const url = ref('');

// 初始化URL
onMounted(() => {
  // 权益升级线上H5页面
  url.value = `https://smstatic.smgis.com/smweixin/index.html?token=${token.value}`;

  // 其他可能的URL选项（已注释）
  // url.value = `https://qjt.smgis.com/weixinpay/index.html?token=${token.value}`;
  // 权益升级官网的权益升级页面
  // url.value = `https://www.tech-sm.com/benefit?token=${token.value}`;
  // 本地地址
  // url.value = `http://**************:5500/index.html?token=${token.value}`;
  // url.value = `http://**************:8080/benefit?token=${token.value}`;
});

// 返回上一页
const handelGoBack = () => {
  router.push('/profile');
};
</script>

<style lang="scss" scoped>
.title-row {
  overflow: hidden;
  display: flex;
  // justify-content: space-between;
  // height: 70px;
  border-bottom: 1px solid #dbe7ee;
  align-items: center;
  .text {
    height: 22px;
    font-size: 16px;
    font-family:
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;
    font-weight: 600;
    color: #161d26;
    line-height: 22px;
    margin-bottom: 4px;
  }
}
</style>
