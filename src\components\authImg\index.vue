<template>
  <el-image
    :class="{ 'active-img': active }"
    fit="cover"
    :style="{ width: width, height: height, borderRadius: radios }"
    :src="img"
    :preview-src-list="imgList"
    :initial-index="idx"
    @mouseenter="handleEnter"
    @mouseleave="handleLeave"
  >
    <template #error>
      <div class="image-slot">
        <img src="@/assets/images/img-error.png" alt="" :style="{ width: width, height: height }" />
      </div>
    </template>
  </el-image>
</template>

<script lang="ts" setup>
import { ref, watch, inject, computed } from 'vue';
import { getToken } from '@/utils/auth';
import axios from 'axios';

interface FwjInfo {
  isSy: string;
  value: string;
}

interface AttributeItem {
  wzxx?: {
    isSy: string;
    value: string;
  };
  [key: string]: any;
}

const props = defineProps({
  authSrc: {
    type: String,
    required: false,
    default: ''
  },
  width: {
    type: String,
    default: '300px'
  },
  height: {
    type: String,
    default: '200px'
  },
  radios: {
    type: String,
    default: '0'
  },
  isList: {
    type: Boolean,
    default: false
  },
  fwj: {
    type: Object as () => FwjInfo,
    default: () => ({
      isSy: '',
      value: ''
    })
  },
  wzxx: {
    type: Object as () => FwjInfo,
    default: () => ({
      isSy: '',
      value: ''
    })
  },
  list: {
    type: Array,
    default: () => []
  },
  idx: {
    type: Number,
    default: 0
  },
  srcList: {
    type: Array,
    default: () => []
  },
  attrItem: {
    type: Object as () => AttributeItem,
    default: () => ({})
  },
  fit: {
    type: String,
    default: 'cover'
  },
  showPreview: {
    // 是否显示预览
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['changeSrcList', 'highlightFWJ']);
// authImg/index.vue

const initDeg = inject('initDeg', (coords: [string, string], value: string, src: string) => {
  // 默认为空函数，防止报错
  // console.warn('initDeg 未提供，跳过执行');
});

// 使用前判断是否存在
if (initDeg) {
  const point = props.wzxx.value.split(',');
  initDeg([point[1], point[0]], props.fwj.value, props.authSrc);
}
const img = ref('');
const active = ref(false);

// 创建一个计算属性确保preview-src-list是string[]类型
const imgList = computed(() => {
  // 如果img已加载但srcList为空，则至少包含当前图片
  if (!props.showPreview) {
    return [];
  }
  if (img.value && (!props.srcList || props.srcList.length === 0)) {
    return [img.value];
  }
  // 确保srcList中的所有元素都是字符串
  return props.srcList && props.srcList.length > 0 ? props.srcList.map((item) => (typeof item === 'string' ? item : '')) : [];
});

// 获取图片
const getImg = () => {
  if (!props.authSrc) return;

  axios({
    method: 'get',
    url: props.authSrc,
    headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' },
    responseType: 'blob'
  })
    .then((res) => {
      const blob = res.data;
      const reader = new FileReader();
      reader.readAsDataURL(blob); // 转换为base64
      reader.onload = function () {
        const result = reader.result;
        if (typeof result !== 'string') {
          console.error('图片加载失败，result 不是 base64 字符串', result);
          img.value = ''; // 设置为空图或错误图
          return;
        }

        if (props.fwj.value && props.wzxx.value) {
          emit('changeSrcList', 2, result, props.idx, props.attrItem, props.authSrc);
          const point = props.wzxx.value.split(',');
          initDeg([point[1], point[0]], props.fwj.value, props.authSrc);
        } else {
          emit('changeSrcList', 1, result, props.idx);
        }
        img.value = result; //  只接受 string 类型
      };
    })
    .catch((error) => {
      console.error('图片加载失败:', error);
    });
};

// 有方位角 位置信息的图片鼠标移入事件
const handleEnter = () => {
  if (props.attrItem.wzxx && props.attrItem.wzxx.value) {
    emit('highlightFWJ', props.authSrc);
    active.value = true;
  }
};

// 鼠标离开事件
const handleLeave = () => {
  active.value = false;
};

// 监听
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.img-item {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 300px;
  height: 200px;
}
.samll-img {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 50px;
  height: 40px;
}
.el-image {
  margin: 0px;
}
:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd dashed 1px;
  background: rgba(0, 0, 0, 0.1);
}
.active-img {
  border: var(--current-color) solid 1px;
}
</style>
