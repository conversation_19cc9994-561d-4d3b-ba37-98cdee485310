import request from '@/utils/request';
import { getToken } from '@/utils/auth';
import { AxiosPromise } from 'axios';
import { SignatureData, UploadData } from '@/api/esign/types';

/**
 * 根据姓名和验证码查看宗地数据
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function selectSignature(params: SignatureData): AxiosPromise<any> {
  return request({
    url: 'qjt/warehouse/signature/pan/findSignature',
    method: 'post',
    data: params
  });
}

/**
 * 上传签名到文件服务器
 * @param data 上传数据
 * @returns {AxiosPromise}
 */
export function uploadImg(data: UploadData): AxiosPromise<any> {
  return request({
    url: '/qjt/file/pan/upload',
    method: 'post',
    data: data
  });
}

/**
 * 预览文件
 * @param oname 文件名称
 * @param fileType 文件类型
 */
export function previewFile(oname: string, fileType: string): AxiosPromise<any> {
  return request({
    url: `/qjt/file/Preview/${oname}?fileType=${fileType}`,
    method: 'get',
    data: { fileType: fileType },
    responseType: 'blob',
    headers: {
      Authorization: 'Bearer ' + getToken(),
      'Access-Control-Allow-Origin': '*'
    }
  });
}

/**
 * 保存电子签名 以图片的形式
 * @param params 签名数据
 * @returns {AxiosPromise}
 */
export function saveSignature(params: SignatureData): AxiosPromise<any> {
  return request({
    url: 'qjt/warehouse/signature/pan/updateSignature',
    method: 'post',
    data: params
  });
}
