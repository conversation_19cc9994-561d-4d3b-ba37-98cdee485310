<!-- 步骤 -->
<template>
  <div class="flow-main" v-show="!item.delFlag || item.delFlag == 0">
    <!-- 子节点第一个需要隐藏多余的横线 -->
    <div class="display-left-top-hr" v-show="displayLeft"></div>
    <div class="display-left-bottom-hr" v-show="displayLeft"></div>
    <!-- 子节点最后一个需要隐藏右侧多余的横线 -->
    <div class="display-right-top-hr" v-show="displayRight"></div>
    <div class="display-right-bottom-hr" v-show="displayRight"></div>
    <!-- 步骤/审批 -->
    <div v-if="item.stepType != 3" class="flow-item">
      <div class="flow-top-box" v-show="isChild">
        <div class="hr"></div>
      </div>
      <div class="flow-content" @mouseenter="hoverStep(item, 1)" @mouseleave="hoverStep(item, 2)">
        <div class="title-box" :class="{ 'waring-color': item.stepType == 2 }" @click.stop="changeTitleFun(item)">
          <div v-show="!item.changeTitle">{{ item.typeName }}</div>
          <el-input v-model="typeNameLocal" style="width: 150px" size="default" v-show="item.changeTitle" @keyup.enter="onTypeNameChange"></el-input>
          <div class="close-btn" v-show="(item.checked && index != 0) || (isChild && item.checked)" @click.stop="delStep()">
            <i class="el-icon-close"
              ><el-icon> <Close /> </el-icon
            ></i>
          </div>
        </div>
        <div class="item-content">
          <div class="con-title" @click="openDetail(item)" v-show="item.stepType == 1">
            <span v-show="item.receiverNames">{{ item.receiverNames }}</span>
            <span v-show="!item.receiverNames" style="color: #bfbfbf">请设置步骤内容</span>
            <i class="el-icon-arrow-right right-con" style="margin-left: 1px"></i>
          </div>
          <!-- 审批 -->
          <div class="con-title" @click="openDetailSP(item)" v-show="item.stepType == 2">
            <span v-show="item.processName">{{ item.processName }}</span>
            <span v-show="!item.processName" style="color: #bfbfbf">请设置审批内容</span>
            <i class="el-icon-arrow-right right-con" style="margin-left: 1px"></i>
          </div>
        </div>
      </div>
      <div class="add-node-box">
        <div class="add-node-btn">
          <button class="add-btn" @click.stop="changeShowHandle(item)">
            <el-icon size="large" style="margin-top: 5px">
              <Plus />
            </el-icon>
          </button>
        </div>
        <div class="handle-box" v-show="item.isShowHandle">
          <div class="handle-item" @click="addItem(item, 1)">
            <img src="../../assets/images/caiji.png" alt="" class="ico-image" />
            步骤
          </div>
          <div class="handle-item" @click="addItem(item, 2)">
            <img src="../../assets/images/fenzhi1.png" alt="" class="ico-image" />
            分支
          </div>
          <div class="handle-item" @click="addItem(item, 3)">
            <img src="../../assets/images/shenpi1.png" alt="" class="ico-image" />
            审批
          </div>
        </div>
      </div>
      <!-- 分支下面有内容 -->
      <div v-if="item.list && item.list.length != 0">
        <component
          :is="TaskFlowSelf"
          v-for="(ite, idx) in item.list"
          :key="idx"
          :item="ite"
          :index="idx"
          :isChild="true"
          :displayLeft="idx == 0"
          :displayRight="idx == item.list.length - 1"
          @changeShowHandleP="changeShowHandleP"
          @addNode="addNode"
          @delOneStep="delOneStep"
          @updateItem="$emit('updateItem', $event)"
          :flowList="flowList"
          :moduleId="moduleId"
        ></component>
      </div>
    </div>
    <!-- 分支 -->
    <div v-else-if="item.stepType == 3" class="flow-Branch-box">
      <div class="flow-Branch">
        <div
          class="add-condition-btn"
          v-if="!item.hideAddConditionBtn && item.list && item.list.filter((step) => !step.delFlag || step.delFlag == 0).length > 0"
          @click="addCondition(item)"
        >
          添加同级步骤
        </div>
        <component
          :is="TaskFlowSelf"
          v-for="(ite, idx) in item.list"
          :key="idx"
          :item="ite"
          :index="idx"
          :isChild="true"
          :displayLeft="idx == 0"
          :displayRight="idx == item.list.length - 1"
          @changeShowHandleP="changeShowHandleP"
          @addNode="addNode"
          @delOneStep="delOneStep"
          @updateItem="$emit('updateItem', $event)"
          :flowList="flowList"
          :moduleId="moduleId"
        ></component>
      </div>
      <!-- 隐藏分支节点底部的添加按钮，只保留中央的添加分支按钮 -->
      <div class="add-node-box" v-if="item.list && item.list.filter((step) => !step.delFlag || step.delFlag == 0).length > 0">
        <div class="add-node-btn">
          <button class="add-btn" @click.stop="changeShowHandle(item)">
            <el-icon size="large" style="margin-top: 5px">
              <Plus />
            </el-icon>
          </button>
        </div>
        <div class="handle-box" v-show="item.isShowHandle">
          <div class="handle-item" @click="addItem(item, 1)">
            <img src="../../assets/images/caiji.png" alt="" class="ico-image" />
            步骤
          </div>
          <div class="handle-item" @click="addItem(item, 2)">
            <img src="../../assets/images/fenzhi1.png" alt="" class="ico-image" />
            分支
          </div>
          <div class="handle-item" @click="addItem(item, 3)">
            <img src="../../assets/images/shenpi1.png" alt="" class="ico-image" />
            审批
          </div>
        </div>
      </div>
    </div>
    <div class="rest-height">
      <div class="hr"></div>
    </div>
    <!-- 选择人员 -->
    <chooseUser
      :userDialog="userDialog"
      @chageUserDialog="chageUserDialog"
      @getChooseUser="getChooseUser"
      :users="nowUsers as any[]"
      :flowList="flowList"
      :isShowDisable="isShowDisable"
    ></chooseUser>
    <!-- 设置 -->
    <el-dialog
      :title="nowCheckedMsg.title"
      v-model="drawer"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="80%"
      class="setting-dialog"
    >
      <div class="dialog-content">
        <el-form
          :model="nowCheckedMsg"
          :rules="nowCheckedMsgRules"
          ref="nowCheckedMsgRef"
          label-position="top"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="采集成员" prop="users">
                <el-input v-model="nowCheckedMsg.receiverNames" placeholder="请选择任务人员" readonly @focus="handleOpenUser(1)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采集负责人" prop="charges">
                <el-input v-model="nowCheckedMsg.chargesName" placeholder="请选择任务负责人" readonly @focus="handleOpenUser(2)"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="采集要素/属性">
            <div class="setting-content">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="group-item">
                    <div class="group-title" @click="showYS = !showYS">
                      <div class="title">
                        要素
                        <span style="color: red">(勾选即表示该节点的图形需要采集)</span>
                      </div>
                      <div class="icon">
                        <el-icon v-show="showYS">
                          <ArrowUpBold />
                        </el-icon>
                        <el-icon v-show="!showYS">
                          <ArrowDownBold />
                        </el-icon>
                      </div>
                    </div>
                    <div class="group-content" v-show="showYS">
                      <el-tree
                        ref="tree"
                        :data="treeList"
                        :props="defaultProps"
                        highlight-current
                        default-expand-all
                        node-key="id"
                        :expand-on-click-node="false"
                        @node-click="handleNodeClick"
                        @check-change="handleCheckChange"
                        :default-checked-keys="defaultCheckedNodes"
                        :check-on-click-node="false"
                        :check-on-click-leaf="false"
                        show-checkbox
                        check-strictly
                        class="tree-div"
                      >
                        <template #default="{ data }">
                          <div class="tree-row">
                            <div class="tree-row-left">
                              <div v-if="data.iconUrl && data.iconUrl.substring(data.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                                <authImg :authSrc="`${baseUrl}${data.iconUrl}?token=${token}`" :width="'20px'" :height="'20px'" />
                              </div>
                              <div v-else>
                                <svg-icon class-name="svg-item" :icon-class="data.iconUrl" />
                              </div>
                              <span style="margin-left: 4px">{{ data.typeName }}</span>
                            </div>
                          </div>
                        </template>
                      </el-tree>
                    </div>
                  </div>
                </el-col>
                <el-col :span="7">
                  <div class="group-item">
                    <div class="group-title" @click="showAttr = !showAttr">
                      <div class="title">属性组</div>
                      <div class="icon">
                        <el-icon v-show="showYS">
                          <ArrowUpBold />
                        </el-icon>
                        <el-icon v-show="!showYS">
                          <ArrowDownBold />
                        </el-icon>
                      </div>
                    </div>
                    <div class="group-content" v-show="showAttr">
                      <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
                      <template v-else>
                        <div
                          class="flex-row"
                          v-for="(item, index) in attrbutionGroup"
                          :class="{ 'flex-active': item.checked }"
                          :key="index"
                          @click="changeAtt(item)"
                        >
                          <div class="label" :class="{ 'gry-span': !item.isChoose }" :title="item.typeName">
                            {{ item.typeName ? truncateText(item.typeName, 12) : '' }}
                            <span
                              v-if="
                                item.ruleAttribution && (item.ruleAttribution.type == 'graphicalPoint' || item.ruleAttribution.type == 'commonPoint')
                              "
                              >(点)</span
                            >
                            <span
                              v-if="
                                item.ruleAttribution && (item.ruleAttribution.type == 'graphicalLine' || item.ruleAttribution.type == 'commonLine')
                              "
                              >(线)</span
                            >
                          </div>
                          <div class="ico" style="width: 30px" :class="{ 'gry-span': !item.isChoose }">
                            <el-icon>
                              <ArrowRightBold />
                            </el-icon>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </el-col>
                <el-col :span="11">
                  <div class="group-item">
                    <div class="group-title" @click="showField = !showField">
                      <div class="title">
                        字段
                        <el-checkbox v-model="chooseAllField" @change="changeAllField" @click.stop style="margin-left: 10px">全选/全不选</el-checkbox>
                        <el-checkbox v-model="chooseAllRequired" @change="changeAllRequired" @click.stop style="margin-left: 10px"
                          >全必填</el-checkbox
                        >
                        <el-checkbox v-model="chooseAllReadonly" @change="changeAllReadonly" @click.stop style="margin-left: 10px"
                          >全只读</el-checkbox
                        >
                      </div>
                      <div class="icon">
                        <el-icon v-show="showYS">
                          <ArrowUpBold />
                        </el-icon>
                        <el-icon v-show="!showYS">
                          <ArrowDownBold />
                        </el-icon>
                      </div>
                    </div>
                    <div class="group-content" v-show="showField">
                      <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
                      <template v-else>
                        <div v-for="(item, index) in fieldList" :key="index">
                          <div
                            class="flex-all check-item"
                            v-if="
                              (item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard') &&
                              item.attribution &&
                              item.attribution.expendList &&
                              item.attribution.expendList.length > 0
                            "
                          >
                            <div class="big-title">
                              {{ item.fieldName }}&nbsp;&nbsp;(
                              {{ item.fieldCn ? (item.fieldCn.length > 5 ? item.fieldCn.substring(0, 5) + '...' : item.fieldCn) : '' }})
                            </div>
                            <div class="flex-row-all" v-for="ite in item.attribution.expendList" :key="ite.label">
                              <div class="label">
                                <el-tooltip placement="right" :content="ite.enName + '(' + ite.cnName + ')'">
                                  <el-checkbox v-model="ite.checked" @change="changeField(item, ite)">
                                    {{ ite.enName }}&nbsp;&nbsp;({{ ite.cnName }})</el-checkbox
                                  ></el-tooltip
                                >
                              </div>
                              <div class="flex-right">
                                <div class="ico">
                                  <span style="margin-right: 5px" :class="{ gray: !ite.newRequired }">必填</span>
                                  <el-checkbox v-model="ite.newRequired" @change="changeReq(ite, item)" disabled></el-checkbox>
                                </div>
                                <div class="ico">
                                  <span style="margin-right: 5px" :class="{ gray: !ite.isEdit }">只读</span>
                                  <el-checkbox v-model="ite.isReadonly" @change="changeReadonly(ite, item)"></el-checkbox>
                                </div>
                                <div class="input">
                                  <!-- 这是是下拉，单选，多选设置的选项值拿出来让用户自己选择 -->
                                  <el-select
                                    v-model="ite.default"
                                    placeholder="请选择"
                                    @change="handleInput($event, ite, item)"
                                    @visible-change="handleChangeSelectShow($event, ite)"
                                    style="width: 100%"
                                    v-if="['radio', 'select', 'checkbox'].includes(ite.valueMethod) && ite.options.length > 0"
                                    :multiple="['checkbox'].includes(ite.valueMethod)"
                                    :collapse-tags="['checkbox'].includes(ite.valueMethod)"
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                  >
                                    <el-option v-for="op in ite.options" :key="op.value" :label="op.label" :value="op.value"> </el-option>
                                  </el-select>
                                  <!-- 这是级联选择 -->
                                  <!--  -->
                                  <el-cascader
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    v-else-if="['cascader'].includes(ite.valueMethod) && ite.attribution && ite.attribution.options.length > 0"
                                    v-model="ite.default"
                                    :options="ite.attribution.options"
                                    placeholder="请选择"
                                    @change="handleInput($event, ite, item)"
                                    style="width: 100%"
                                  >
                                  </el-cascader>
                                  <!-- 这里是时间选择器 -->
                                  <el-time-picker
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    v-else-if="['time'].includes(ite.valueMethod)"
                                    v-model="ite.default"
                                    placeholder="请选择时间"
                                    value-format="HH:mm:ss"
                                    @change="handleInput($event, ite, item)"
                                    style="width: 100%"
                                  >
                                  </el-time-picker>
                                  <!-- 日期时间控件 -->
                                  <el-date-picker
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    @change="handleInput($event, ite, item)"
                                    value-format="YYYY-MM-DD"
                                    style="width: 100%"
                                    v-else-if="['date', 'date-range'].includes(ite.valueMethod)"
                                    v-model="ite.default"
                                    :type="ite.valueMethod == 'date' ? 'date' : 'daterange'"
                                    :range-separator="ite.valueMethod == 'date-range' ? '至' : ''"
                                    :start-placeholder="ite.valueMethod == 'date-range' ? '开始日期' : ''"
                                    :end-placeholder="ite.valueMethod == 'date-range' ? '结束日期' : ''"
                                  />

                                  <!-- 其他默认情况下 -->
                                  <el-input
                                    v-else
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    style="width: 100%"
                                    type="text"
                                    v-model="ite.default"
                                    placeholder="默认值"
                                    @input="handleInput($event, ite, item)"
                                  ></el-input>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="flex-all check-item" v-else-if="item.valueMethod == 'xttable'">
                            <div class="big-title">
                              {{ item.fieldName }}&nbsp;&nbsp;(
                              {{ item.fieldCn ? (item.fieldCn.length > 5 ? item.fieldCn.substring(0, 5) + '...' : item.fieldCn) : '' }})
                            </div>
                            <div class="flex-row-all" v-for="ite in item.attribution.children" :key="ite.fieldName">
                              <div class="label">
                                <el-tooltip placement="right" :content="ite.fieldName + '(' + ite.fieldCn + ')'">
                                  <el-checkbox v-model="ite.checked" @change="changeField(item, ite)">
                                    {{ ite.fieldName }}&nbsp;&nbsp;({{ ite.fieldCn }})</el-checkbox
                                  ></el-tooltip
                                >
                              </div>
                              <div class="flex-right">
                                <div class="ico">
                                  <span style="margin-right: 5px" :class="{ gray: !ite.newRequired }">必填</span>
                                  <el-checkbox v-model="ite.newRequired" @change="changeReq(ite, item)"></el-checkbox>
                                </div>
                                <div class="ico">
                                  <span style="margin-right: 5px" :class="{ gray: !ite.isEdit }">只读</span>
                                  <el-checkbox v-model="ite.isReadonly" @change="changeReadonly(ite, item)"></el-checkbox>
                                </div>
                                <div class="input">
                                  <!-- 这是是下拉，单选，多选设置的选项值拿出来让用户自己选择 -->
                                  <el-select
                                    v-model="ite.default"
                                    placeholder="请选择"
                                    @change="handleInput($event, ite, item)"
                                    @visible-change="handleChangeSelectShow($event, ite)"
                                    style="width: 100%"
                                    v-if="
                                      ['radio', 'select', 'checkbox'].includes(ite.valueMethod) &&
                                      ite.attribution &&
                                      ite.attribution.options.length > 0
                                    "
                                    :multiple="['checkbox'].includes(ite.valueMethod)"
                                    :collapse-tags="['checkbox'].includes(ite.valueMethod)"
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                  >
                                    <el-option v-for="op in ite.attribution.options" :key="op.value" :label="op.label" :value="op.value"> </el-option>
                                  </el-select>
                                  <!-- 这是级联选择 -->
                                  <!--  -->
                                  <el-cascader
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    v-else-if="['cascader'].includes(ite.valueMethod) && ite.attribution && ite.attribution.options.length > 0"
                                    v-model="ite.default"
                                    :options="ite.attribution.options"
                                    placeholder="请选择"
                                    @change="handleInput($event, ite, item)"
                                    style="width: 100%"
                                  >
                                  </el-cascader>
                                  <!-- 这里是时间选择器 -->
                                  <el-time-picker
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    v-else-if="['time'].includes(ite.valueMethod)"
                                    v-model="ite.default"
                                    placeholder="请选择时间"
                                    value-format="HH:mm:ss"
                                    @change="handleInput($event, ite, item)"
                                    style="width: 100%"
                                  >
                                  </el-time-picker>
                                  <!-- 日期时间控件 -->
                                  <el-date-picker
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    @change="handleInput($event, ite, item)"
                                    :value-format="ite.valueMethod == 'date' ? '' : 'YYYY-MM-DD'"
                                    style="width: 100%"
                                    v-else-if="['date', 'date-range'].includes(ite.valueMethod)"
                                    v-model="ite.default"
                                    :type="ite.valueMethod == 'date' ? 'date' : 'daterange'"
                                    :range-separator="ite.valueMethod == 'date-range' ? '至' : ''"
                                    :start-placeholder="ite.valueMethod == 'date-range' ? '开始日期' : ''"
                                    :end-placeholder="ite.valueMethod == 'date-range' ? '结束日期' : ''"
                                  >
                                  </el-date-picker>
                                  <!-- 其他默认情况下 -->
                                  <el-input
                                    v-else
                                    :disabled="disableTypeList.includes(ite.valueMethod)"
                                    style="width: 100%"
                                    type="text"
                                    v-model="ite.default"
                                    placeholder="默认值"
                                    @input="handleInput($event, ite, item)"
                                  ></el-input>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="flex-row check-item" v-else>
                            <div class="label">
                              <el-tooltip placement="right" :content="item.fieldName + '(' + item.fieldCn + ')'">
                                <el-checkbox v-model="item.checked" @change="changeField(item, null)">
                                  {{ item.fieldName }}&nbsp;&nbsp;(
                                  {{
                                    item.fieldCn ? (item.fieldCn.length > 5 ? item.fieldCn.substring(0, 5) + '...' : item.fieldCn) : ''
                                  }})</el-checkbox
                                ></el-tooltip
                              >
                            </div>
                            <div class="flex-right">
                              <div class="ico">
                                <span style="margin-right: 5px" :class="{ gray: !item.newRequired }">必填</span>
                                <el-checkbox v-model="item.newRequired" @change="changeReq(item, null)"></el-checkbox>
                              </div>
                              <div class="ico">
                                <span style="margin-right: 5px" :class="{ gray: !item.isEdit }">只读</span>
                                <el-checkbox v-model="item.isReadonly" @change="changeReadonly(item, null)"></el-checkbox>
                              </div>
                              <div class="input">
                                <!-- 这是是下拉，单选，多选设置的选项值拿出来让用户自己选择 -->
                                <el-select
                                  v-model="item.default"
                                  placeholder="请选择"
                                  @change="handleInput($event, item, null)"
                                  @visible-change="handleChangeSelectShow($event, item)"
                                  style="width: 100%"
                                  v-if="
                                    ['radio', 'select', 'checkbox'].includes(item.valueMethod) &&
                                    item.attribution &&
                                    item.attribution.options.length > 0
                                  "
                                  :multiple="['checkbox'].includes(item.valueMethod)"
                                  :collapse-tags="['checkbox'].includes(item.valueMethod)"
                                  :disabled="disableTypeList.includes(item.valueMethod)"
                                >
                                  <el-option v-for="item in item.attribution.options" :key="item.value" :label="item.label" :value="item.value">
                                  </el-option>
                                </el-select>
                                <!-- 这是级联选择 -->
                                <el-cascader
                                  :disabled="disableTypeList.includes(item.valueMethod)"
                                  v-else-if="['cascader'].includes(item.valueMethod) && item.attribution && item.attribution.options.length > 0"
                                  v-model="item.default"
                                  :options="item.attribution.options"
                                  placeholder="请选择"
                                  @change="handleInput($event, item, null)"
                                  style="width: 100%"
                                >
                                </el-cascader>
                                <!-- 这里是时间选择器 -->
                                <el-time-picker
                                  :disabled="disableTypeList.includes(item.valueMethod)"
                                  v-else-if="['time'].includes(item.valueMethod)"
                                  v-model="item.default"
                                  placeholder="请选择时间"
                                  value-format="HH:mm:ss"
                                  @change="handleInput($event, item, null)"
                                  style="width: 100%"
                                >
                                </el-time-picker>
                                <!-- 日期时间控件 -->
                                <el-date-picker
                                  :disabled="disableTypeList.includes(item.valueMethod)"
                                  @change="handleInput($event, item, null)"
                                  :value-format="item.valueMethod == 'date' ? '' : 'YYYY-MM-DD'"
                                  style="width: 100%"
                                  v-else-if="['date', 'date-range'].includes(item.valueMethod)"
                                  v-model="item.default"
                                  :type="item.valueMethod == 'date' ? 'date' : 'daterange'"
                                  :range-separator="item.valueMethod == 'date-range' ? '至' : ''"
                                  :start-placeholder="item.valueMethod == 'date-range' ? '开始日期' : ''"
                                  :end-placeholder="item.valueMethod == 'date-range' ? '结束日期' : ''"
                                >
                                </el-date-picker>
                                <el-input
                                  v-model="item.default"
                                  placeholder="默认值"
                                  @input="handleInput($event, item, null)"
                                  v-else
                                  :disabled="disableTypeList.includes(item.valueMethod)"
                                ></el-input>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitStep">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 审批设置 -->
    <dialogDraggableSP
      :draggableDialog="draggableDialog"
      @closeDraggableDailog="closeDraggableDailog"
      @submitSPSetting="submitSPSetting"
    ></dialogDraggableSP>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick, markRaw, defineAsyncComponent } from 'vue';
import chooseUser from '@/views/projectManager/component/taskManagement/chooseUser.vue';
import { selectRules } from '@/api/modal';
import dialogDraggableSP from '@/components/dialogDraggableSP/index.vue';
import { isArray } from '../../utils/validate';
import { ElMessageBox } from 'element-plus';
import { getToken } from '@/utils/auth';
// 递归引用自身组件
const TaskFlowSelf = markRaw(defineAsyncComponent(() => import('./index.vue')));

// 定义接口
interface User {
  custName: string; // 只保留必需的属性
  [key: string]: any; // 允许任意额外属性
}

interface FieldModel {
  fieldName: string;
  valueMethod?: string;
  required?: number;
  isEdit?: boolean;
  attribution?: any;
  expendList?: any[];
  children?: any[];
  [key: string]: any;
}

interface FieldGroupModel {
  linkId: string | number;
  groupScope?: any;
  moduleId?: any;
  wordName?: string;
  typeName?: string;
  aliasName?: string;
  iconUrl?: string;
  displayType?: any;
  linkGroup?: any;
  ruleAttribution?: any;
  fieldModelList: FieldModel[];
  attribution?: any;
  [key: string]: any;
}

interface TreeNode {
  id: string | number;
  isEdit: boolean;
  fieldGroupModelList: FieldGroupModel[];
}

interface FlowItem {
  typeName: string;
  stepLevel?: any;
  users: User[];
  receiverNames: string;
  stepType: number;
  changeTitle: boolean;
  treeNodes: TreeNode[];
  charges: User[];
  chargesName: string;
  list: FlowItem[];
  isShowHandle: boolean;
  ruleId?: string;
  isChild?: boolean;
  parentKey?: string | number;
  [key: string]: any;
}

// 在类型定义部分添加
interface AttrGroup {
  checked?: boolean;
  isChoose?: boolean;
  typeName?: string;
  ruleAttribution?: any;
  linkId?: string | number;
  [key: string]: any;
}

// 定义props
const props = defineProps({
  item: {
    type: Object as () => FlowItem,
    required: true
  },
  index: {
    type: Number,
    default: 0
  },
  isChild: {
    type: Boolean,
    default: false
  },
  displayLeft: {
    type: Boolean,
    default: false
  },
  displayRight: {
    type: Boolean,
    default: false
  },
  flowList: {
    type: Array as () => any[],
    default: () => []
  },
  moduleId: {
    type: [String, Number],
    default: ''
  }
});

// 定义emit
const emit = defineEmits(['changeShowHandleP', 'addNode', 'delOneStep', 'closeFlowList', 'updateItem']);

// 添加修复Prop处理的实现，并删除重复定义
const typeNameLocal = ref('');

watch(
  () => props.item.typeName,
  (newVal) => {
    typeNameLocal.value = newVal;
  },
  { immediate: true }
);

const onTypeNameChange = () => {
  emit('updateItem', { ...props.item, typeName: typeNameLocal.value, changeTitle: false });
};
/**
 * 修改标题
 * @param item 流程项
 */
const changeTitleFun = (item: FlowItem) => {
  item.changeTitle = true;

  // 添加事件监听，当用户点击其他地方时关闭输入框
  nextTick(() => {
    const closeListener = (event: MouseEvent) => {
      // 检查点击的元素是否在输入框外部
      const target = event.target as Element;
      const inputs = document.querySelectorAll('.title-box input');
      let clickedOutside = true;

      inputs.forEach((input) => {
        if (input === target || input.contains(target)) {
          clickedOutside = false;
        }
      });

      if (clickedOutside) {
        // 更新标题并关闭输入框
        if (item.changeTitle) {
          emit('updateItem', { ...item, typeName: typeNameLocal.value, changeTitle: false });
          document.removeEventListener('click', closeListener);
        }
      }
    };

    // 避免立即触发事件监听器
    setTimeout(() => {
      document.addEventListener('click', closeListener);
    }, 100);
  });
};

// 状态
const drawer = ref(false);
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');
interface CheckedMsg {
  users: User[];
  treeNodes: TreeNode[];
  title: string;
  receiverNames: string;
  chargesName: string;
  charges: User[];
  processId?: string | number;
  processName?: string;
  item?: FlowItem; // Reference to the original item being edited
  [key: string]: any;
}
const nowCheckedMsg = reactive<CheckedMsg>({
  users: [],
  treeNodes: [],
  title: '',
  receiverNames: '',
  chargesName: '',
  charges: []
});

const nowCheckedMsgRules = {
  users: [{ required: true, message: '请选择采集人员', trigger: 'change' }],
  charges: [{ required: true, message: '请选择负责人员', trigger: 'change' }],
  treeNodes: [{ required: true, message: '请选择图形或属性', trigger: 'blur' }]
};

const userDialog = ref(false);
const showYS = ref(true);

// 定义树节点类型
interface TreeListItem {
  id: string | number;
  fieldGroupModelList: any[];
  typeName: string;
  iconUrl?: string;
  [key: string]: any;
}
const treeList = ref<TreeListItem[]>([]);

const defaultProps = {
  children: 'list',
  label: 'typeName'
};
// 修改attrbutionGroup的定义
const attrbutionGroup = ref<AttrGroup[]>([]);
const fieldList = ref<any[]>([]);
const checkedYS = ref<Record<string, any>>({});
const checkedAtt = ref<Record<string, any>>({});

const checkedField = ref({});
const nowChooseNode = ref<(string | number)[]>([]);
const showAttr = ref(true);
const showField = ref(true);
const defaultCheckedNodes = ref<(string | number)[]>([]);
const chooseAllField = ref(false);
const chooseAllRequired = ref(false);
const chooseAllReadonly = ref(false);
const userType = ref(1);
const nowUsers = ref<User[]>([]);
const isShowDisable = ref(false);
const draggableDialog = ref(false);
const token = ref(getToken());
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;

// Vue 3中不再需要.native修饰符
const nowCheckedMsgRef = ref<any>(null);
const tree = ref<any>(null);

// 禁用类型列表
const disableTypeList = [
  'xtpay',
  'xtcy',
  'xtzsdl',
  'xtsjy',
  'xtsjjt',
  'xttable',
  'xtlxr',
  'xtaudio',
  'xtvideo',
  'xtdwsb',
  'xtzwsb',
  'xtzw',
  'xtqm',
  'area',
  'idCardScan',
  'xtBankCard',
  'xtfj',
  'upload',
  'cascader',
  'idCardBitmap',
  // 银行卡照片不允许设置
  'BankCardBitmap'
];

// 代替过滤器的函数
const truncateText = (text: string, length: number): string => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

/**
 * 修改显示处理
 */
const changeShowHandleP = (item: FlowItem) => {
  emit('changeShowHandleP', item);
};

const changeShowHandle = (item: FlowItem) => {
  emit('changeShowHandleP', item);
};
/**
 * 添加节点
 * @param item 流程项
 * @param type 类型
 */
const addNode = (item: FlowItem, type: number) => {
  emit('addNode', item, type);
};
/**
 * 添加节点
 * @param item 流程项
 * @param type 类型
 */
const addItem = (item: FlowItem, type: number) => {
  emit('addNode', item, type);
};
/**
 * 添加条件
 * @param item 流程项
 */
const addCondition = (item: FlowItem) => {
  const newBranchId = (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8);
  // 创建一个具有完整属性的新分支步骤
  const newStep = {
    typeName: '步骤',
    stepLevel: item.stepLevel,
    users: [],
    receiverNames: '',
    stepType: 1,
    changeTitle: false,
    treeNodes: [],
    charges: [],
    chargesName: '',
    list: [],
    isShowHandle: false,
    ruleId: (Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0, 8),
    isChild: true,
    parentKey: item.id ? item.id : item.ruleId,
    chargesAttribution: { list: [] } // 确保有这个属性
  };

  item.list.push(newStep);

  // 通知父组件更新
  emit('updateItem', item);
};
/**
 * 鼠标悬停
 * @param item 流程项
 * @param type 类型
 */
const hoverStep = (item: FlowItem, type: number) => {
  if (type == 1) {
    item.checked = true;
  } else {
    item.checked = false;
  }
};
/**
 * 删除节点
 */
const delStep = () => {
  // 确认框
  ElMessageBox.confirm('确定要删除该流程吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('delOneStep', props.item);
    })
    .catch(() => {});
};
/**
 * 删除节点
 * @param item 流程项
 */
const delOneStep = (item: FlowItem) => {
  emit('delOneStep', item);
};
/**
 * 打开用户选择弹窗
 * @param type 类型
 */
const chageUserDialog = (flg: boolean) => {
  userDialog.value = flg;
};
/**
 * 打开用户选择弹窗
 * @param type 类型
 */
const handleOpenUser = (type: number) => {
  userType.value = type;
  if (type == 1) {
    // 成员
    nowUsers.value = JSON.parse(JSON.stringify(nowCheckedMsg.users));
    isShowDisable.value = true;
  } else if (type == 2) {
    // 负责人
    nowUsers.value = JSON.parse(JSON.stringify(nowCheckedMsg.charges));
    isShowDisable.value = false;
  }
  userDialog.value = true;
};
/**
 * 获取选择用户
 * @param list 用户列表
 */
const getChooseUser = (list: User[]) => {
  if (userType.value == 1) {
    // 成员
    nowCheckedMsg.users = list;
    const receiverNames: string[] = [];
    list.forEach((v) => {
      receiverNames.push(`${v.custName}`);
    });
    nowCheckedMsg.receiverNames = receiverNames.join(',');
    // 在Vue 3中clearValidate需要适配
    // nowCheckedMsgRef.value?.clearValidate('users');
  } else if (userType.value == 2) {
    // 负责人
    nowCheckedMsg.charges = list;
    const chargesName: string[] = [];
    list.forEach((v) => {
      chargesName.push(`${v.custName}`);
    });
    nowCheckedMsg.chargesName = chargesName.join(',');
    // nowCheckedMsgRef.value?.clearValidate('charges');
  }
  userDialog.value = false;
};
/**
 * 关闭审批设置
 */
const handleClose = () => {
  drawer.value = false;
  // if (userType.value == 1 && nowUsers.value.length > 0) {
  //   nowCheckedMsg.users = nowUsers.value;
  //   // 取消这里重新给 nowUsers 赋值
  //   const receiverNames: string[] = [];
  //   nowUsers.value.forEach((v) => {
  //     receiverNames.push(`${v.custName}`);
  //   });
  //   nowCheckedMsg.receiverNames = receiverNames.join(',');
  // } else if (userType.value == 2 && nowUsers.value.length > 0) {
  //   nowCheckedMsg.charges = nowUsers.value;
  //   //  这里给采集成员重新赋值
  //   const chargesName: string[] = [];
  //   nowUsers.value.forEach((v) => {
  //     chargesName.push(`${v.custName}`);
  //   });
  //   nowCheckedMsg.chargesName = chargesName.join(',');
  // }

  // // 更新父组件中的内容 flowList
  // emit('closeFlowList', props.item);
};

// 关闭审批设置
const closeDraggableDailog = () => {
  draggableDialog.value = false;
};
/**
 * 审批内容提交
 * @param obj 对象
 */
const submitSPSetting = (obj: any) => {
  // 更新当前选中项的审批信息
  nowCheckedMsg.processId = obj.processId;
  nowCheckedMsg.processName = obj.processName;

  // 确保原始项也被更新
  if (nowCheckedMsg.item) {
    nowCheckedMsg.item.processId = obj.processId;
    nowCheckedMsg.item.processName = obj.processName;

    // 通知父组件更新数据
    emit('updateItem', nowCheckedMsg.item);
  }

  draggableDialog.value = false;
};
/**
 * 设置审批内容
 * @param item 流程项
 */
const openDetailSP = (item: FlowItem) => {
  // 正确初始化nowCheckedMsg，而不是将item直接赋值给nowCheckedMsg.value
  nowCheckedMsg.item = item; // 保存引用用于后续更新
  nowCheckedMsg.users = item.users || [];
  nowCheckedMsg.charges = item.charges || [];
  nowCheckedMsg.treeNodes = item.treeNodes || [];
  nowCheckedMsg.receiverNames = item.receiverNames || '';
  nowCheckedMsg.chargesName = item.chargesName || '';
  nowCheckedMsg.processId = item.processId;
  nowCheckedMsg.processName = item.processName;

  draggableDialog.value = true;
};
/**
 * 打开设置弹窗
 * @param item 流程项
 */
const openDetail = async (item: FlowItem) => {
  if (!props.moduleId) {
    ElMessage.error('请先选择业务模块！！！');
    return;
  }

  // 更新当前选中的消息
  Object.assign(nowCheckedMsg, item);
  try {
    const res = await selectRules({ moduleId: props.moduleId });
    if (res.code === 200) {
      treeList.value = res.data;
      defaultCheckedNodes.value = [];
      // 判断当前步骤是否已经有节点
      if (item.treeNodes && item.treeNodes.length !== 0) {
        // 勾选需要编辑的图形
        item.treeNodes.forEach((v) => {
          if (v.isEdit) {
            defaultCheckedNodes.value.push(v.id);
            nowChooseNode.value.push(v.id);
          }
        });

        // 默认选中第一条数据
        for (let i = 0; i < treeList.value.length; i++) {
          if (treeList.value[i].id === item.treeNodes[0].id) {
            attrbutionGroup.value = treeList.value[i].fieldGroupModelList;
            setAttrChoose(item.treeNodes[0].fieldGroupModelList);
            checkedYS.value = treeList.value[i];
            fieldList.value = [];
            break;
          }
        }

        // 确保树组件更新后设置选中状态
        nextTick(() => {
          if (tree.value) {
            // 先设置当前选中节点
            tree.value.setCurrentKey(item.treeNodes[0].id);
            // 设置复选框选中状态
            tree.value.setCheckedKeys(defaultCheckedNodes.value);
          }
        });
      } else {
        attrbutionGroup.value = res.data[0].fieldGroupModelList;
        checkedYS.value = res.data[0];
        fieldList.value = [];
        nextTick(() => {
          if (tree.value) {
            tree.value.setCurrentKey(res.data[0].id);
          }
        });
      }

      drawer.value = true;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取要素树详情失败:', error);
    ElMessage.error('获取要素树详情失败');
  }
};

/**
 * 处理节点点击
 * @param data 数据
 */
const handleNodeClick = (data: any) => {
  attrbutionGroup.value = data.fieldGroupModelList || [];
  // 每次切换树的时候需要判断在nowCheckedMsg.treeNodes里是否有该树的id
  let ite_tree = null;
  // 确保treeNodes是数组
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id == data.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }
  if (ite_tree) {
    // 有的话就需要反显属性组哪些是选过的
    setAttrChoose(ite_tree.fieldGroupModelList);
  }
  checkedYS.value = data;
  fieldList.value = [];
  // 要初始化全选
  chooseAllField.value = false;
  chooseAllRequired.value = false;
  chooseAllReadonly.value = false;
  nextTick(() => {
    updateChooseAllFieldStatus();
  });
};

/**
 * 选择了树之后对应属性组变黑(也就是表示区分已有数据的属性组)
 * @param fieldGroupModelList 属性组列表
 */
const setAttrChoose = (fieldGroupModelList: any[]) => {
  // 需要把存在的属性组变为黑色（选中）状态
  attrbutionGroup.value.forEach((v) => {
    if (fieldGroupModelList.some((obj) => obj.linkId == v.linkId)) {
      // 存在
      v.isChoose = true;
    }
  });
};

/**
 * 处理选中变化
 * @param data 数据
 * @param checked 是否选中
 */
const handleCheckChange = (data: any, checked: boolean) => {
  if (checked) {
    // 选中节点
    if (!nowChooseNode.value.includes(data.id)) {
      nowChooseNode.value.push(data.id);
    }

    // 更新treeNodes
    if (!nowCheckedMsg.treeNodes) {
      nowCheckedMsg.treeNodes = [];
    }

    let ite_tree = null;
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id === data.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }

    if (ite_tree) {
      // 节点存在，设置isEdit为true
      ite_tree.isEdit = true;
    } else {
      // 节点不存在，创建新节点
      nowCheckedMsg.treeNodes.push({
        id: data.id,
        isEdit: true,
        fieldGroupModelList: []
      });
    }
  } else {
    // 取消选中
    for (let i = 0; i < nowChooseNode.value.length; i++) {
      if (nowChooseNode.value[i] === data.id) {
        nowChooseNode.value.splice(i, 1);

        // 更新treeNodes - 使用Vue2的逻辑
        for (let j = 0; j < nowCheckedMsg.treeNodes.length; j++) {
          if (nowCheckedMsg.treeNodes[j].id === data.id) {
            if (nowCheckedMsg.treeNodes[j].fieldGroupModelList.length === 0) {
              // 没有属性组的时候直接删除该节点
              nowCheckedMsg.treeNodes.splice(j, 1);
            } else {
              // 有属性组时只将isEdit设为false
              nowCheckedMsg.treeNodes[j].isEdit = false;
            }
            break;
          }
        }
        break;
      }
    }
  }

  // 同步更新defaultCheckedNodes
  defaultCheckedNodes.value = [...nowChooseNode.value];
};

/**
 * 更改属性组
 * @param item 数据
 */
const changeAtt = (item: any) => {
  // 把全选置空
  chooseAllField.value = false;
  attrbutionGroup.value.forEach((v: any) => {
    v.checked = false;
  });
  checkedAtt.value = item;
  item.checked = true;
  fieldList.value = [];
  if (item.fieldModelList && Array.isArray(item.fieldModelList)) {
    item.fieldModelList.forEach((v: any) => {
      if (v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') {
        v.attribution?.expendList?.forEach((k: any) => {
          k.checked = false;
          k.isEdit = true;
          k.newRequired = false;
          k.isReadonly = false; //显示使用
        });
      } else if (v.valueMethod == 'xttable') {
        v.attribution.children.forEach((k: any) => {
          k.checked = false;
          k.isEdit = true;
          k.newRequired = false;
          k.isReadonly = false; //显示使用
          // 如果有默认值就赋值
          if (k.attribution.defaultValue) {
            k.default = k.attribution.defaultValue;
          }
        });
      } else {
        v.checked = false;
        v.isEdit = true;
        v.newRequired = false;
        v.isReadonly = false; //显示使用
        // 如果有默认值就赋值
        if (v.attribution.defaultValue) {
          v.default = v.attribution.defaultValue;
          console.log('asd', v.default);
        }
      }
      fieldList.value.push(JSON.parse(JSON.stringify(v)));
    });
  }
  //需要查询并反显字段内容
  // 确保treeNodes存在并且是数组
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    nowCheckedMsg.treeNodes.forEach((v: any) => {
      if (v.fieldGroupModelList && Array.isArray(v.fieldGroupModelList)) {
        for (let i = 0; i < v.fieldGroupModelList.length; i++) {
          if (v.fieldGroupModelList[i].linkId == item.linkId) {
            // 找到对应的属性组了
            // 找到对应的属性组之后就需要把选择了的字段反显回来
            fieldList.value.forEach((k: any) => {
              if (k.valueMethod == 'idCardScan' || k.valueMethod === 'xtBankCard') {
                for (let q = 0; q < k.attribution?.expendList?.length; q++) {
                  for (let j = 0; j < v.fieldGroupModelList[i].fieldModelList.length; j++) {
                    if (
                      (v.fieldGroupModelList[i].fieldModelList[j].valueMethod == 'idCardScan' ||
                        v.fieldGroupModelList[i].fieldModelList[j].valueMethod === 'xtBankCard') &&
                      k.fieldName == v.fieldGroupModelList[i].fieldModelList[j].fieldName &&
                      k.attribution.expendList[q].enName == v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].enName
                    ) {
                      //找到字段了
                      k.attribution.expendList[q].checked = true;
                      //  这是展示设置身份真是别默认值的反显
                      v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList.forEach((f: any) => {
                        if (f.enName == k.attribution.expendList[q].enName) {
                          if (f.valueMethod == 'checkbox' || f.valueMethod == 'date-range') {
                            const defaultList = f.default && typeof f.default == 'string' ? f.default.split('至') : f.default ? f.default : [];
                            k.attribution.expendList[q].default = defaultList;
                            console.log('asdasd', k.attribution.expendList[q].default);
                          } else {
                            k.attribution.expendList[q].default = f.default;
                          }
                        }
                      });
                      if (v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].isEdit === true) {
                        // 反显是否只读
                        k.attribution.expendList[q].isReadonly = false;
                      } else if (v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].isEdit === false) {
                        // 反显是否只读
                        k.attribution.expendList[q].isReadonly = true;
                      }
                      // 修复必填状态反显 - 检查保存的required值
                      if (v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].required !== undefined) {
                        // 如果expendList下存在required值，使用它
                        if (v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].required === 1) {
                          k.attribution.expendList[q].newRequired = true;
                        } else if (v.fieldGroupModelList[i].fieldModelList[j].attribution.expendList[0].required === 2) {
                          k.attribution.expendList[q].newRequired = false;
                        }
                      } else {
                        // 如果expendList下不存在required值，使用外部的required值
                        if (v.fieldGroupModelList[i].fieldModelList[j].required === 1) {
                          k.attribution.expendList[q].newRequired = true;
                        } else if (v.fieldGroupModelList[i].fieldModelList[j].required === 2) {
                          k.attribution.expendList[q].newRequired = false;
                        }
                      }
                    }
                  }
                }
              } else if (k.valueMethod == 'xttable') {
                for (let q = 0; q < k.attribution.children.length; q++) {
                  for (let j = 0; j < v.fieldGroupModelList[i].fieldModelList.length; j++) {
                    if (v.fieldGroupModelList[i].fieldModelList[j].fieldName == k.fieldName) {
                      if (
                        v.fieldGroupModelList[i].fieldModelList[j].attribution.children.some(
                          (obj: any) => obj.fieldName === k.attribution.children[q].fieldName
                        )
                      ) {
                        k.attribution.children[q].checked = true;
                        // 反显值 表格中设置的值 针对多选框和日期时间选项的框内容 展示数组
                        v.fieldGroupModelList[i].fieldModelList[j].attribution.children.forEach((f: any) => {
                          if (f.fieldName == k.attribution.children[q].fieldName) {
                            if (f.valueMethod == 'checkbox' || f.valueMethod == 'date-range') {
                              const defaultList = f.default && typeof f.default == 'string' ? f.default.split('至') : f.default ? f.default : [];
                              k.attribution.children[q].default = defaultList;
                              console.log('asdasd', k.attribution.children[q].default);
                            } else {
                              k.attribution.children[q].default = f.default;
                            }
                          }
                        });
                        // 反显是否只读
                        for (let w = 0; w < v.fieldGroupModelList[i].fieldModelList[j].attribution.children.length; w++) {
                          if (
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].fieldName == k.attribution.children[q].fieldName &&
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].isEdit == false
                          ) {
                            k.attribution.children[q].isReadonly = true;
                            break;
                          } else if (
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].fieldName == k.attribution.children[q].fieldName &&
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].isEdit == true
                          ) {
                            k.attribution.children[q].isReadonly = false;
                            break;
                          }
                        }
                        for (let w = 0; w < v.fieldGroupModelList[i].fieldModelList[j].attribution.children.length; w++) {
                          //反显必填
                          if (
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].fieldName == k.attribution.children[q].fieldName &&
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].required === 2
                          ) {
                            k.attribution.children[q].newRequired = false;
                            break;
                          } else if (
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].fieldName == k.attribution.children[q].fieldName &&
                            v.fieldGroupModelList[i].fieldModelList[j].attribution.children[w].required === 1
                          ) {
                            k.attribution.children[q].newRequired = true;
                            break;
                          }
                        }
                        break;
                      }
                    }
                  }
                }
              } else {
                //   这里是正常情况
                for (let j = 0; j < v.fieldGroupModelList[i].fieldModelList.length; j++) {
                  if (k.fieldName == v.fieldGroupModelList[i].fieldModelList[j].fieldName) {
                    k.checked = true;
                    // 反显值 针对多选框和日期时间选项的框内容 展示数组
                    if (
                      v.fieldGroupModelList[i].fieldModelList[j].valueMethod == 'checkbox' ||
                      v.fieldGroupModelList[i].fieldModelList[j].valueMethod == 'date-range'
                    ) {
                      let defaultList: any[] = [];
                      if (typeof v.fieldGroupModelList[i].fieldModelList[j].default == 'string') {
                        defaultList = v.fieldGroupModelList[i].fieldModelList[j].default.split(',');
                        console.log('asdasd', defaultList);
                      } else if (Array.isArray(v.fieldGroupModelList[i].fieldModelList[j].default)) {
                        defaultList = v.fieldGroupModelList[i].fieldModelList[j].default;
                      }
                      k.default = defaultList;
                    } else {
                      k.default = v.fieldGroupModelList[i].fieldModelList[j].default;
                    }
                    if (v.fieldGroupModelList[i].fieldModelList[j].isEdit == false) {
                      // 反显是否只读
                      k.isReadonly = true;
                    } else if (v.fieldGroupModelList[i].fieldModelList[j].isEdit == true) {
                      k.isReadonly = false;
                    }
                    if (v.fieldGroupModelList[i].fieldModelList[j].required == 1) {
                      // 反显是否必填
                      k.newRequired = true;
                    } else if (v.fieldGroupModelList[i].fieldModelList[j].required == 2) {
                      k.newRequired = false;
                    }
                    break;
                  }
                }
              }
            });
            break;
          }
        }
      }
    });
  }
  nextTick(() => {
    updateChooseAllFieldStatus();
  });
};
/**
 * 全选/全不选
 * @param val 值
 */
const changeAllField = (val: CheckboxValueType) => {
  try {
    // 防御性判断
    if (!fieldList.value || fieldList.value.length === 0) {
      chooseAllField.value = false;
      return;
    }
    let ite_tree = null;
    // 找到当前节点
    if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
      for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
        if (nowCheckedMsg.treeNodes[i].id === checkedYS.value.id) {
          ite_tree = nowCheckedMsg.treeNodes[i];
          break;
        }
      }
    }

    if (val) {
      // 全选
      // 先全选字段
      fieldList.value.forEach((v: any) => {
        if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && Array.isArray(v.attribution?.expendList)) {
          v.attribution.expendList.forEach((k: any) => {
            k.checked = true;
          });
        } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
          v.attribution.children.forEach((k: any) => {
            k.checked = true;
          });
        } else {
          v.checked = true;
        }
      });

      // 当前选择的属性组显示黑色
      if (attrbutionGroup.value) {
        for (let i = 0; i < attrbutionGroup.value.length; i++) {
          if (attrbutionGroup.value[i].checked) {
            attrbutionGroup.value[i].isChoose = true;
            break;
          }
        }
      }

      if (ite_tree) {
        // 节点存在的时候
        // 找属性组
        let ite_attr = null;
        for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
          if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
            ite_attr = ite_tree.fieldGroupModelList[i];
            break;
          }
        }

        if (ite_attr) {
          // 属性组存在的时候 直接把字段放入fieldModelList即可
          ite_attr.fieldModelList = [];
          fieldList.value.forEach((v: any) => {
            if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && Array.isArray(v.attribution?.expendList)) {
              v.attribution?.expendList?.forEach((k: any) => {
                const ite_field = JSON.parse(JSON.stringify(v));
                const spe_field = JSON.parse(JSON.stringify(k));
                if (spe_field.isReadonly === false) {
                  spe_field.isEdit = true;
                } else {
                  spe_field.isEdit = false;
                }
                const required = spe_field.newRequired ? 1 : 2; // 1必填,2选填,3不能填
                spe_field.required = required;
                ite_field.attribution.expendList = [spe_field];
                ite_attr.fieldModelList.push(ite_field);
              });
            } else if (v.valueMethod === 'xttable') {
              const ite_field = JSON.parse(JSON.stringify(v));
              const children: any[] = [];
              ite_field.attribution.children.forEach((k: any) => {
                const ite_child = JSON.parse(JSON.stringify(k));
                if (ite_child.isReadonly === true) {
                  ite_child.isEdit = false;
                } else {
                  ite_child.isEdit = true;
                }
                const required = ite_child.newRequired ? 1 : 2;
                ite_child.required = required;
                children.push(ite_child);
              });
              ite_field.attribution.children = children;
              ite_attr.fieldModelList.push(ite_field);
            } else {
              const ite_field = JSON.parse(JSON.stringify(v));
              if (ite_field.isReadonly === true) {
                ite_field.isEdit = false;
              } else {
                ite_field.isEdit = true;
              }
              const required = ite_field.newRequired ? 1 : 2;
              ite_field.required = required;
              ite_attr.fieldModelList.push(ite_field);
            }
          });
        } else {
          // 属性组不存在，创建新的属性组
          ite_attr = {
            id: checkedAtt.value.id,
            linkId: checkedAtt.value.linkId,
            groupScope: checkedAtt.value.groupScope,
            moduleId: checkedAtt.value.moduleId,
            wordName: checkedAtt.value.wordName,
            typeName: checkedAtt.value.typeName,
            aliasName: checkedAtt.value.aliasName,
            iconUrl: checkedAtt.value.iconUrl,
            displayType: checkedAtt.value.displayType,
            linkGroup: checkedAtt.value.linkGroup,
            ruleAttribution: checkedAtt.value.ruleAttribution,
            fieldModelList: [],
            attribution: {
              isMoreNode: checkedAtt.value.attribution.isMoreNode,
              linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
              chooseField: checkedAtt.value.attribution.chooseField
            }
          };

          // 添加字段到新属性组
          fieldList.value.forEach((k: any) => {
            if ((k.valueMethod === 'idCardScan' || k.valueMethod === 'xtBankCard') && Array.isArray(k.attribution?.expendList)) {
              k.attribution?.expendList?.forEach((q: any) => {
                const ite_field = JSON.parse(JSON.stringify(k));
                const spe_field = JSON.parse(JSON.stringify(q));
                if (spe_field.isReadonly === false) {
                  spe_field.isEdit = true;
                } else {
                  spe_field.isEdit = false;
                }
                const required = spe_field.newRequired ? 1 : 2;
                spe_field.required = required;
                ite_field.attribution.expendList = [spe_field];
                ite_attr.fieldModelList.push(ite_field);
              });
            } else if (k.valueMethod === 'xttable') {
              const ite_field = JSON.parse(JSON.stringify(k));
              const children: any[] = [];
              ite_field.attribution.children.forEach((q: any) => {
                const ite_child = JSON.parse(JSON.stringify(q));
                if (ite_child.isReadonly === true) {
                  ite_child.isEdit = false;
                } else {
                  ite_child.isEdit = true;
                }
                const required = ite_child.newRequired ? 1 : 2;
                ite_child.required = required;
                children.push(ite_child);
              });
              ite_field.attribution.children = children;
              ite_attr.fieldModelList.push(ite_field);
            } else {
              const ite_field = JSON.parse(JSON.stringify(k));
              if (ite_field.isReadonly === true) {
                ite_field.isEdit = false;
              } else {
                ite_field.isEdit = true;
              }
              const required = ite_field.newRequired ? 1 : 2;
              ite_field.required = required;
              ite_attr.fieldModelList.push(ite_field);
            }
          });
          ite_tree.fieldGroupModelList.push(ite_attr);
        }
      } else {
        // 节点不存在，创建新节点
        const isEdit = nowChooseNode.value.includes(checkedYS.value.id);
        ite_tree = {
          id: checkedYS.value.id,
          isEdit: isEdit,
          fieldGroupModelList: []
        };

        const ite_attr = {
          id: checkedAtt.value.id,
          linkId: checkedAtt.value.linkId,
          groupScope: checkedAtt.value.groupScope,
          moduleId: checkedAtt.value.moduleId,
          wordName: checkedAtt.value.wordName,
          typeName: checkedAtt.value.typeName,
          aliasName: checkedAtt.value.aliasName,
          iconUrl: checkedAtt.value.iconUrl,
          displayType: checkedAtt.value.displayType,
          linkGroup: checkedAtt.value.linkGroup,
          ruleAttribution: checkedAtt.value.ruleAttribution,
          fieldModelList: [],
          attribution: {
            isMoreNode: checkedAtt.value.attribution.isMoreNode,
            linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
            chooseField: checkedAtt.value.attribution.chooseField
          }
        };

        // 添加字段到新属性组
        fieldList.value.forEach((k: any) => {
          if (k.valueMethod === 'idCardScan' || k.valueMethod === 'xtBankCard') {
            k.attribution.expendList.forEach((q: any) => {
              const ite_field = JSON.parse(JSON.stringify(k));
              const spe_field = JSON.parse(JSON.stringify(q));
              if (spe_field.isReadonly === false) {
                spe_field.isEdit = true;
              } else {
                spe_field.isEdit = false;
              }
              const required = spe_field.newRequired ? 1 : 2;
              spe_field.required = required;
              ite_field.attribution.expendList = [spe_field];
              ite_attr.fieldModelList.push(ite_field);
            });
          } else if (k.valueMethod === 'xttable') {
            const ite_field = JSON.parse(JSON.stringify(k));
            const children: any[] = [];
            ite_field.attribution.children.forEach((q: any) => {
              const ite_child = JSON.parse(JSON.stringify(q));
              if (ite_child.isReadonly === true) {
                ite_child.isEdit = false;
              } else {
                ite_child.isEdit = true;
              }
              const required = ite_child.newRequired ? 1 : 2;
              ite_child.required = required;
              children.push(ite_child);
            });
            ite_field.attribution.children = children;
            ite_attr.fieldModelList.push(ite_field);
          } else {
            const ite_field = JSON.parse(JSON.stringify(k));
            if (ite_field.isReadonly === true) {
              ite_field.isEdit = false;
            } else {
              ite_field.isEdit = true;
            }
            const required = ite_field.newRequired ? 1 : 2;
            ite_field.required = required;
            ite_attr.fieldModelList.push(ite_field);
          }
        });
        ite_tree.fieldGroupModelList.push(ite_attr);
        nowCheckedMsg.treeNodes.push(ite_tree);
      }
    } else {
      // 全不选
      // 先把字段都设置为未选中状态
      fieldList.value.forEach((v: any) => {
        if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && v.attribution?.expendList) {
          v.attribution.expendList.forEach((k: any) => {
            k.checked = false;
          });
        } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
          v.attribution.children.forEach((k: any) => {
            k.checked = false;
          });
        } else {
          v.checked = false;
        }
      });

      // 把当前选择的属性组置灰
      if (attrbutionGroup.value) {
        for (let i = 0; i < attrbutionGroup.value.length; i++) {
          if (attrbutionGroup.value[i].linkId === checkedAtt.value.linkId) {
            attrbutionGroup.value[i].isChoose = false;
            break;
          }
        }
      }

      // 找属性组并删除
      if (ite_tree?.fieldGroupModelList) {
        for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
          if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
            ite_tree.fieldGroupModelList.splice(i, 1);
            break;
          }
        }
      }
    }

    // 触发更新
    emit('updateItem', props.item);
  } catch (err) {
    console.error('Error in changeAllField:', err);
    ElMessage.error('全选/全不选操作失败');
  }
};

// 提交步骤
const submitStep = () => {
  //打印日期控件
  // 确保 treeNodes 存在并且具有正确的结构
  if (!nowCheckedMsg.treeNodes || nowCheckedMsg.treeNodes.length === 0) {
    // Initialize treeNodes if empty
    nowCheckedMsg.treeNodes = [
      {
        id: checkedYS.value?.id || `node_${Date.now()}`,
        isEdit: true,
        fieldGroupModelList: []
      }
    ];
  }

  // 确保每个节点都有一个 fieldGroupModelList 字段
  nowCheckedMsg.treeNodes.forEach((node) => {
    if (!node.fieldGroupModelList) {
      node.fieldGroupModelList = [];
    }
    // 如果不存在当前属性组，则添加
    if (node.fieldGroupModelList.length === 0 && checkedAtt.value) {
      node.fieldGroupModelList.push({
        id: checkedAtt.value.id || `attr_${Date.now()}`,
        linkId: checkedAtt.value.linkId || `link_${Date.now()}`,
        typeName: checkedAtt.value.typeName || '属性组',
        fieldModelList: fieldList.value || []
      });
    }
  });

  nowCheckedMsg.treeNodes.forEach((v) => {
    if (v.fieldGroupModelList) {
      v.fieldGroupModelList.forEach((k) => {
        if (k.fieldModelList) {
          k.fieldModelList.forEach((q) => {
            if (q.valueMethod && ['checkbox', 'date-range'].includes(q.valueMethod) && isArray(q.default) && q.default) {
              q.default = q.default.join(',');
            } else if (q.valueMethod == 'xttable') {
              q.attribution.children.forEach((w: any) => {
                if (['checkbox', 'date-range'].includes(w.valueMethod) && isArray(w.default)) {
                  w.default = w.default.length ? w.default.join(',') : '';
                }
              });
            }
          });
        }
      });
    }
  });
  nowCheckedMsgRef.value.validate((valid: boolean) => {
    if (valid) {
      const receiverNames: string[] = [];
      nowCheckedMsg.users.forEach((v) => {
        receiverNames.push(v.custName);
      });
      nowCheckedMsg.receiverNames = receiverNames.join(',');

      // 确保chargesAttribution正确设置
      nowCheckedMsg.chargesAttribution = {
        list: [...nowCheckedMsg.charges]
      };
      // 更新每个用户的 stepAttribution.treeNodes
      nowCheckedMsg.users.forEach((user) => {
        if (!user.stepAttribution) {
          user.stepAttribution = { treeNodes: [] };
        }
        // 为每个用户创建独立的 treeNodes 副本
        user.stepAttribution.treeNodes = JSON.parse(JSON.stringify(nowCheckedMsg.treeNodes));
      });
      // 创建更新后的对象副本而不是直接修改props
      const updatedItem = {
        ...props.item,
        receiverNames: nowCheckedMsg.receiverNames,
        users: nowCheckedMsg.users,
        treeNodes: nowCheckedMsg.treeNodes, // 确保treeNodes被保存
        charges: nowCheckedMsg.charges,
        chargesName: nowCheckedMsg.chargesName,
        chargesAttribution: nowCheckedMsg.chargesAttribution
      };

      // 通知父组件更新数据
      emit('updateItem', updatedItem);

      chooseAllField.value = false;
      drawer.value = false;
    } else {
      return false;
    }
  });
  if (props.item.isChild && props.item.parentKey) {
    emit('updateItem', props.item);
  }
  //打印提交的所有数据
  console.log('保存的所有数据', nowCheckedMsg);
};

// 在methods部分添加changeField方法
const changeField = (item: any, childItem: any) => {
  if (item.valueMethod == 'idCardScan' || item.valueMethod === 'xtBankCard') {
    handleSpecialSelectField(item, childItem);
  } else if (item.valueMethod == 'xttable') {
    //表格处理
    handleTableSelectField(item, childItem);
  } else {
    handleSelectField(item, false);
  }
  if (props.item.isChild) {
    emit('updateItem', props.item);
  }
  nextTick(() => {
    updateChooseAllFieldStatus();
  });
};

// 添加处理特殊字段的方法
const handleSpecialSelectField = (item: any, child: any) => {
  // 身份证识别的时候自动当子字段中有expendList 选中的情况 身份证item 的check 的值true 否则则为false
  // 身份证识别字段暂时不能设计是否必填和默认值的内容
  // 需要用到深拷贝 不然会影响显示
  const ite_item = JSON.parse(JSON.stringify(item));
  ite_item.attribution.expendList = [child];
  if (child && child.checked) {
    // 表示是新增 直接调用普通的字段整理
    handleSelectField(ite_item, true);
  } else {
    // 相当于是取消选中身份证识别的某个字段 需要单独处理
    let ite_tree = null;
    if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
      for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
        if (nowCheckedMsg.treeNodes[i].id === checkedYS.value.id) {
          ite_tree = nowCheckedMsg.treeNodes[i];
          break;
        }
      }
    }

    let ite_attr = null;
    if (ite_tree?.fieldGroupModelList) {
      for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
        if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
          ite_attr = ite_tree.fieldGroupModelList[i];
          break;
        }
      }
    }

    if (ite_attr?.fieldModelList) {
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (
          ite_attr.fieldModelList[i].fieldName === ite_item.fieldName &&
          ite_attr.fieldModelList[i].attribution?.expendList?.[0]?.enName === child.enName
        ) {
          // 找到了
          ite_attr.fieldModelList.splice(i, 1);
          break;
        }
      }

      // 表示属性组下面没有字段了 需要把该属性组删除
      if (ite_attr.fieldModelList.length === 0 && ite_tree) {
        for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
          if (ite_tree.fieldGroupModelList[i].linkId === ite_attr.linkId) {
            ite_tree.fieldGroupModelList.splice(i, 1);
            break;
          }
        }
      }
    }

    // 表示要素下面的属性组没有了 并且要素不是必须编辑的话 就需要把要素直接删除
    if (ite_tree?.fieldGroupModelList.length === 0 && !ite_tree.isEdit) {
      if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
        for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
          if (nowCheckedMsg.treeNodes[i].id === ite_tree.id) {
            nowCheckedMsg.treeNodes.splice(i, 1);
            break;
          }
        }
      }
    }
  }
};

/**
 * 处理表格字段
 * @param item 数据
 * @param child 子数据
 */
const handleTableSelectField = (item: any, child: any) => {
  // 需要先判断是否选中树
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id === checkedYS.value.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }
  if (!ite_tree) {
    //如果没有找到节点 需要设置节点
    const isEdit = nowChooseNode.value.includes(checkedYS.value.id);
    ite_tree = {
      id: checkedYS.value.id,
      isEdit: isEdit,
      fieldGroupModelList: []
    };
  }
  // 找属性组
  let ite_attr = null;
  if (ite_tree?.fieldGroupModelList) {
    for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
      if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
        ite_attr = ite_tree.fieldGroupModelList[i];
        break;
      }
    }
  }

  if (child.checked) {
    if (child.isEdit) {
      // 反转判断是否只读
      child.isEdit = isReadonly;
    }
    // 选中
    if (ite_attr) {
      // 找到属性组 就往属性组里面加字段
      let ite_field = null;
      // 找到属性组之后还需要找该表格字段是否已经存在
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName === item.fieldName) {
          ite_field = ite_attr.fieldModelList[i];
          break;
        }
      }

      if (!ite_field) {
        // 字段不存在的时候
        // 深拷贝 避免影响页面
        ite_field = JSON.parse(JSON.stringify(item));
        ite_field.attribution.children = [];
        ite_field.attribution.children.push(child);
        // 把字段push进属性组
        ite_attr.fieldModelList.push(ite_field);
      } else {
        // 字段存在的时候
        ite_field.attribution.children.push(child);
      }
    } else {
      item.isChoose = true;
      if (attrbutionGroup.value) {
        for (let i = 0; i < attrbutionGroup.value.length; i++) {
          if (attrbutionGroup.value[i].linkId === checkedAtt.value.linkId) {
            attrbutionGroup.value[i].isChoose = true;
            break;
          }
        }
      }

      // 没找到属性组就新增
      ite_attr = {
        id: checkedAtt.value.id,
        linkId: checkedAtt.value.linkId,
        groupScope: checkedAtt.value.groupScope,
        moduleId: checkedAtt.value.moduleId,
        wordName: checkedAtt.value.wordName,
        typeName: checkedAtt.value.typeName,
        aliasName: checkedAtt.value.aliasName,
        iconUrl: checkedAtt.value.iconUrl,
        displayType: checkedAtt.value.displayType,
        linkGroup: checkedAtt.value.linkGroup,
        ruleAttribution: checkedAtt.value.ruleAttribution,
        fieldModelList: [],
        attribution: {
          isMoreNode: checkedAtt.value.attribution.isMoreNode,
          linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
          chooseField: checkedAtt.value.attribution.chooseField
        }
      };

      const ite_field = JSON.parse(JSON.stringify(item));
      ite_field.attribution.children = [];
      ite_field.attribution.children.push(child);
      ite_attr.fieldModelList.push(ite_field);

      // 把属性组放入节点下
      if (ite_tree) {
        ite_tree.fieldGroupModelList.push(ite_attr);
        nowCheckedMsg.treeNodes.push(ite_tree);
      }
    }
  } else {
    // 未选中 取消 取消的时候代表该表格字段一定存在
    if (ite_attr) {
      let ite_field = null;
      // 找到属性组之后还需要找该表格字段是否已经存在
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName === item.fieldName) {
          ite_field = ite_attr.fieldModelList[i];
          break;
        }
      }

      if (ite_field?.attribution?.children) {
        for (let i = 0; i < ite_field.attribution.children.length; i++) {
          if (ite_field.attribution.children[i].fieldName === child.fieldName) {
            ite_field.attribution.children.splice(i, 1);
            break;
          }
        }
      }
    }
  }
};
// 添加处理普通字段的方法
const handleSelectField = (item: any, isSpe: boolean) => {
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id === checkedYS.value.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }

  let flg = false;
  if (isSpe) {
    // 特殊的 需要判断字段的attribution的expendList
    flg = item.attribution?.expendList?.[0]?.checked || false;
  } else {
    // 常规
    flg = item.checked;
  }

  if (flg) {
    if (ite_tree) {
      // 找到了节点
      // 找属性组
      let ite_attr = null;
      if (ite_tree.fieldGroupModelList) {
        for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
          if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
            ite_attr = ite_tree.fieldGroupModelList[i];
            // Vue3中不需要$set，直接赋值即可
            ite_attr.attribution = {
              isMoreNode: checkedAtt.value.attribution.isMoreNode,
              linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
              chooseField: checkedAtt.value.attribution.chooseField
            };
            break;
          }
        }
      }

      if (ite_attr) {
        // 因为 checkbox 使用下拉采用多选，所以下拉框 声明为数组[]
        let resultDefault = null;
        if (item.valueMethod === 'checkbox') {
          resultDefault = item.attribution?.default || item.default || [];
          item.default = resultDefault;
        } else {
          // 保持原有默认值，如果attribution中没有默认值的话
          resultDefault = item.attribution?.default !== undefined ? item.attribution.default : item.default;
          item.default = resultDefault;
        }

        const ite_field = { ...item, default: resultDefault };
        ite_field.isEdit = !ite_field.isReadonly;
        ite_attr.fieldModelList.push(JSON.parse(JSON.stringify(ite_field)));
      } else {
        // 没找到就新增
        ite_attr = {
          id: checkedAtt.value.id,
          linkId: checkedAtt.value.linkId,
          groupScope: checkedAtt.value.groupScope,
          moduleId: checkedAtt.value.moduleId,
          wordName: checkedAtt.value.wordName,
          typeName: checkedAtt.value.typeName,
          aliasName: checkedAtt.value.aliasName,
          iconUrl: checkedAtt.value.iconUrl,
          displayType: checkedAtt.value.displayType,
          linkGroup: checkedAtt.value.linkGroup,
          ruleAttribution: checkedAtt.value.ruleAttribution,
          fieldModelList: [],
          attribution: {
            isMoreNode: checkedAtt.value.attribution.isMoreNode,
            linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
            chooseField: checkedAtt.value.attribution.chooseField
          }
        };

        let resultDefault = null;
        if (item.valueMethod === 'checkbox') {
          resultDefault = item.attribution?.default || item.default || [];
          item.default = resultDefault;
        } else {
          // 保持原有默认值，如果attribution中没有默认值的话
          resultDefault = item.attribution?.default !== undefined ? item.attribution.default : item.default;
          item.default = resultDefault;
        }

        const ite_field = { ...item, default: resultDefault };
        ite_field.isEdit = !ite_field.isReadonly;
        ite_attr.fieldModelList.push(JSON.parse(JSON.stringify(ite_field)));
        ite_tree.fieldGroupModelList.push(ite_attr);

        // 没找到属性组相当于属性组是第一次添加，就需要把属性组设置为选过的状态
        if (attrbutionGroup.value) {
          for (let i = 0; i < attrbutionGroup.value.length; i++) {
            if (attrbutionGroup.value[i].linkId === ite_attr.linkId) {
              // Vue3中不需要$set，直接赋值即可
              attrbutionGroup.value[i].isChoose = true;
              break;
            }
          }
        }
      }
    } else {
      // 没找到节点
      const isEdit = nowChooseNode.value.includes(checkedYS.value.id);
      ite_tree = {
        id: checkedYS.value.id,
        isEdit: isEdit,
        fieldGroupModelList: []
      };
      const ite_attr = {
        id: checkedAtt.value.id,
        linkId: checkedAtt.value.linkId,
        groupScope: checkedAtt.value.groupScope,
        moduleId: checkedAtt.value.moduleId,
        wordName: checkedAtt.value.wordName,
        typeName: checkedAtt.value.typeName,
        aliasName: checkedAtt.value.aliasName,
        iconUrl: checkedAtt.value.iconUrl,
        displayType: checkedAtt.value.displayType,
        linkGroup: checkedAtt.value.linkGroup,
        ruleAttribution: checkedAtt.value.ruleAttribution,
        fieldModelList: [],
        attribution: {
          isMoreNode: checkedAtt.value.attribution.isMoreNode,
          linkGroupScope: checkedAtt.value.attribution.linkGroupScope,
          chooseField: checkedAtt.value.attribution.chooseField
        }
      };

      let resultDefault = null;
      if (item.valueMethod === 'checkbox') {
        resultDefault = item.attribution?.default || item.default || [];
        item.default = resultDefault;
      } else {
        // 保持原有默认值，如果attribution中没有默认值的话
        resultDefault = item.attribution?.default !== undefined ? item.attribution.default : item.default;
        item.default = resultDefault;
      }

      const ite_field = { ...item, default: resultDefault };
      ite_field.isEdit = !ite_field.isReadonly;
      ite_attr.fieldModelList.push(JSON.parse(JSON.stringify(ite_field)));
      ite_tree.fieldGroupModelList.push(ite_attr);

      if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
        nowCheckedMsg.treeNodes.push(ite_tree);
      }

      // 没找到属性组相当于属性组是第一次添加，就需要把属性组设置为选过的状态
      if (attrbutionGroup.value) {
        for (let i = 0; i < attrbutionGroup.value.length; i++) {
          if (attrbutionGroup.value[i].linkId === ite_attr.linkId) {
            // Vue3中不需要$set，直接赋值即可
            attrbutionGroup.value[i].isChoose = true;
            break;
          }
        }
      }
    }
  } else {
    // 取消某个字段
    if (!ite_tree?.fieldGroupModelList) return;

    let ite_attr = null;
    for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
      if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
        ite_attr = ite_tree.fieldGroupModelList[i];
        break;
      }
    }

    if (ite_attr?.fieldModelList) {
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName === item.fieldName) {
          // 找到了
          ite_attr.fieldModelList.splice(i, 1);
          break;
        }
      }

      if (ite_attr.fieldModelList.length === 0) {
        // 表示属性组下面没有字段了 需要把该属性组删除
        for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
          if (ite_tree.fieldGroupModelList[i].linkId === ite_attr.linkId) {
            ite_tree.fieldGroupModelList.splice(i, 1);
            break;
          }
        }
      }
    }

    if (ite_tree.fieldGroupModelList.length === 0 && !ite_tree.isEdit) {
      // 表示要素下面的属性组没有了 并且要素不是必须编辑的话 就需要把要素直接删除
      if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
        for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
          if (nowCheckedMsg.treeNodes[i].id === ite_tree.id) {
            nowCheckedMsg.treeNodes.splice(i, 1);
            break;
          }
        }
      }
    }
  }
};

/**
 * 必填项
 * @param item 数据
 * @param obj 对象
 */
const changeReq = (item: any, obj: any) => {
  //obj存在的时候就是表格或者身份证识别
  let required = 2; // 1必填,2选填,3不能填
  if (item.newRequired) {
    required = 1;
  }
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id == checkedYS.value.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }
  // 找属性组
  let ite_attr = null;
  if (ite_tree && ite_tree.fieldGroupModelList && Array.isArray(ite_tree.fieldGroupModelList)) {
    for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
      if (ite_tree.fieldGroupModelList[i].linkId == checkedAtt.value.linkId) {
        ite_attr = ite_tree.fieldGroupModelList[i];
        break;
      }
    }
  }
  if (item.checked && ite_attr && ite_attr.fieldModelList && Array.isArray(ite_attr.fieldModelList)) {
    //必填
    if (obj) {
      //特殊 表格 或 身份证
      if (obj.valueMethod == 'idCardScan' || obj.valueMethod === 'xtBankCard') {
        //身份证识别
        for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
          if (
            ite_attr.fieldModelList[i].fieldName == obj.fieldName &&
            ite_attr.fieldModelList[i].attribution &&
            ite_attr.fieldModelList[i].attribution.expendList &&
            ite_attr.fieldModelList[i].attribution.expendList[0] &&
            ite_attr.fieldModelList[i].attribution.expendList[0].enName == item.enName
          ) {
            // 安全地设置required属性
            if (ite_attr.fieldModelList[i].attribution && ite_attr.fieldModelList[i].attribution.expendList) {
              ite_attr.fieldModelList[i].attribution.expendList[0].required = required;
            }
            break;
          }
        }
      } else {
        for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
          if (ite_attr.fieldModelList[i].fieldName == obj.fieldName) {
            if (ite_attr.fieldModelList[i].attribution.children && Array.isArray(ite_attr.fieldModelList[i].attribution.children)) {
              for (let j = 0; j < ite_attr.fieldModelList[i].attribution.children.length; j++) {
                if (
                  ite_attr.fieldModelList[i].attribution.children[j] &&
                  ite_attr.fieldModelList[i].attribution.children[j].fieldName == item.fieldName
                ) {
                  ite_attr.fieldModelList[i].attribution.children[j].required = required;
                  break;
                }
              }
            }
            break;
          }
        }
      }
    } else {
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName == item.fieldName) {
          ite_attr.fieldModelList[i].required = required;
          break;
        }
      }
    }
  }

  // 添加这行来更新全必填状态
  nextTick(() => {
    updateChooseAllFieldStatus();
  });
};

/**
 * 只读
 * @param item 数据
 * @param obj 对象
 */
const changeReadonly = (item: any, obj: any) => {
  const isEdit = item.isReadonly;
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id == checkedYS.value.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }
  // 找属性组
  let ite_attr = null;
  if (ite_tree && ite_tree.fieldGroupModelList && Array.isArray(ite_tree.fieldGroupModelList)) {
    for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
      if (ite_tree.fieldGroupModelList[i].linkId == checkedAtt.value.linkId) {
        ite_attr = ite_tree.fieldGroupModelList[i];
        break;
      }
    }
  }
  if (item.checked && ite_attr && ite_attr.fieldModelList && Array.isArray(ite_attr.fieldModelList)) {
    if (obj) {
      //特殊 表格或身份证
      if (obj.valueMethod == 'idCardScan' || obj.valueMethod === 'xtBankCard') {
        //身份证识别
        for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
          if (ite_attr.fieldModelList[i].fieldName == obj.fieldName && ite_attr.fieldModelList[i].attribution.expendList[0].enName == item.enName) {
            ite_attr.fieldModelList[i].attribution.expendList[0].isEdit = !isEdit;
            break;
          }
        }
      } else {
        for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
          if (ite_attr.fieldModelList[i].fieldName == obj.fieldName) {
            for (let j = 0; j < ite_attr.fieldModelList[i].attribution.children.length; j++) {
              if (ite_attr.fieldModelList[i].attribution.children[j].fieldName == item.fieldName) {
                ite_attr.fieldModelList[i].attribution.children[j].isEdit = !isEdit;
                break;
              }
            }
            break;
          }
        }
      }
    } else {
      for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName == item.fieldName) {
          ite_attr.fieldModelList[i].isEdit = !isEdit;
          break;
        }
      }
    }
  }

  // 添加这行来更新全只读状态
  nextTick(() => {
    updateChooseAllFieldStatus();
  });
};
/**
 * 处理输入
 * @param val 值
 * @param item 数据项
 * @param obj 上级对象（如身份证、表格等）
 */
const handleInput = (val: any, item: any, obj: any) => {
  item.default = val;
  // 查找当前节点
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id === checkedYS.value?.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }
  // 处理不同输入场景
  if (obj) {
    // 身份证识别/表格场景
    if (item.checked && ite_tree) {
      // 查找属性组
      let ite_attr = null;
      for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
        if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value?.linkId) {
          ite_attr = ite_tree.fieldGroupModelList[i];
          break;
        }
      }

      // 查找字段
      let ite_field = null;
      if (obj.valueMethod === 'idCardScan' || obj.valueMethod === 'xtBankCard') {
        for (let i = 0; i < ite_attr?.fieldModelList.length; i++) {
          if (
            ite_attr.fieldModelList[i].fieldName === obj.fieldName &&
            ite_attr.fieldModelList[i].attribution?.expendList[0]?.enName === item.enName
          ) {
            ite_field = ite_attr.fieldModelList[i];
            break;
          }
        }
      } else {
        for (let i = 0; i < ite_attr?.fieldModelList.length; i++) {
          if (ite_attr.fieldModelList[i].fieldName === obj.fieldName) {
            ite_field = ite_attr.fieldModelList[i];
            break;
          }
        }
      }
      // 处理字段值更新
      if (ite_field) {
        if (ite_field.attribution?.expendList?.length > 0) {
          for (let i = 0; i < ite_field.attribution.expendList.length; i++) {
            if (ite_field.attribution.expendList[i].enName === item.enName) {
              const isSpecial = ['checkbox', 'date-range'].includes(ite_field.attribution.expendList[i].valueMethod);

              if (isSpecial && Array.isArray(val)) {
                ite_field.attribution.expendList[i].default = val.join(',');
              } else {
                ite_field.attribution.expendList[i].default = val;
              }
              break;
            }
          }
        } else if (ite_field.attribution?.children?.length > 0) {
          // 处理表格字段
          for (let i = 0; i < ite_field.attribution.children.length; i++) {
            if (ite_field.attribution.children[i].fieldName === item.fieldName) {
              const isSpecial = ['checkbox', 'date-range'].includes(ite_field.attribution.children[i].valueMethod);

              if (isSpecial && Array.isArray(val)) {
                ite_field.attribution.children[i].default = val.join(',');
              } else {
                ite_field.attribution.children[i].default = val;
              }
              break;
            }
          }
        }
      }
    }
  } else {
    // 常规字段处理
    if (item.checked && ite_tree) {
      // 查找属性组
      let ite_attr = null;
      for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
        if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value?.linkId) {
          ite_attr = ite_tree.fieldGroupModelList[i];
          break;
        }
      }

      // 查找字段
      let ite_field = null;
      for (let i = 0; i < ite_attr?.fieldModelList.length; i++) {
        if (ite_attr.fieldModelList[i].fieldName === item.fieldName) {
          ite_field = ite_attr.fieldModelList[i];
          break;
        }
      }
      if (ite_field) {
        if (['checkbox', 'date-range'].includes(ite_field.valueMethod)) {
          // 处理特殊字段类型
          if (Array.isArray(val)) {
            ite_field.default = val.join(',');
          }
        } else {
          // 常规字段
          ite_field.default = val;
        }
      }
    }
  }
};

/**
 * 处理select显示变化
 * @param val 值
 * @param item 数据
 */
const handleChangeSelectShow = (val: any, item: any) => {
  if (item.valueMethod == 'checkbox') {
    if (val) {
      const result = Array.isArray(item.default) ? item.default : item.default ? item.default.split(',') : [];
      item.default = result;
    } else {
      // let result = !Array.isArray(item.default) && (typeof item.default == 'string') ? item.default : item.default.join(",")
      // item.default = result
    }
  }
};

// 在script setup部分添加新的函数
const updateChooseAllFieldStatus = () => {
  if (!fieldList.value || fieldList.value.length === 0) {
    chooseAllField.value = false;
    chooseAllRequired.value = false;
    chooseAllReadonly.value = false;
    return;
  }

  let allChecked = true;
  let allRequired = true;
  let allReadonly = true;

  for (const field of fieldList.value) {
    if (field.valueMethod === 'idCardScan' || field.valueMethod === 'xtBankCard') {
      if (!field.attribution?.expendList?.every((k: any) => k.checked)) {
        allChecked = false;
      }
      if (!field.attribution?.expendList?.every((k: any) => k.newRequired)) {
        allRequired = false;
      }
      if (!field.attribution?.expendList?.every((k: any) => k.isReadonly)) {
        allReadonly = false;
      }
    } else if (field.valueMethod === 'xttable') {
      if (!field.attribution?.children?.every((k: any) => k.checked)) {
        allChecked = false;
      }
      if (!field.attribution?.children?.every((k: any) => k.newRequired)) {
        allRequired = false;
      }
      if (!field.attribution?.children?.every((k: any) => k.isReadonly)) {
        allReadonly = false;
      }
    } else {
      if (!field.checked) {
        allChecked = false;
      }
      if (!field.newRequired) {
        allRequired = false;
      }
      if (!field.isReadonly) {
        allReadonly = false;
      }
    }
  }

  chooseAllField.value = allChecked;
  chooseAllRequired.value = allRequired;
  chooseAllReadonly.value = allReadonly;
};

/**
 * 全必填/全不必填
 * @param val 值
 */
const changeAllRequired = (val: boolean) => {
  try {
    if (!fieldList.value || fieldList.value.length === 0) {
      chooseAllRequired.value = false;
      return;
    }

    fieldList.value.forEach((v: any) => {
      if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && Array.isArray(v.attribution?.expendList)) {
        v.attribution.expendList.forEach((k: any) => {
          k.newRequired = val;
          // 同步更新到treeNodes中
          updateFieldInTreeNodes(v, k, 'required', val ? 1 : 2);
        });
      } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
        v.attribution.children.forEach((k: any) => {
          k.newRequired = val;
          // 同步更新到treeNodes中
          updateFieldInTreeNodes(v, k, 'required', val ? 1 : 2);
        });
      } else {
        v.newRequired = val;
        // 同步更新到treeNodes中
        updateFieldInTreeNodes(v, null, 'required', val ? 1 : 2);
      }
    });

    emit('updateItem', props.item);
  } catch (err) {
    console.error('Error in changeAllRequired:', err);
    ElMessage.error('全必填/全不必填操作失败');
  }
};

/**
 * 全只读/全不只读
 * @param val 值
 */
const changeAllReadonly = (val: boolean) => {
  try {
    if (!fieldList.value || fieldList.value.length === 0) {
      chooseAllReadonly.value = false;
      return;
    }

    fieldList.value.forEach((v: any) => {
      if ((v.valueMethod === 'idCardScan' || v.valueMethod === 'xtBankCard') && Array.isArray(v.attribution?.expendList)) {
        v.attribution.expendList.forEach((k: any) => {
          k.isReadonly = val;
          // 同步更新到treeNodes中
          updateFieldInTreeNodes(v, k, 'isEdit', !val);
        });
      } else if (v.valueMethod === 'xttable' && v.attribution?.children) {
        v.attribution.children.forEach((k: any) => {
          k.isReadonly = val;
          // 同步更新到treeNodes中
          updateFieldInTreeNodes(v, k, 'isEdit', !val);
        });
      } else {
        v.isReadonly = val;
        // 同步更新到treeNodes中
        updateFieldInTreeNodes(v, null, 'isEdit', !val);
      }
    });

    emit('updateItem', props.item);
  } catch (err) {
    console.error('Error in changeAllReadonly:', err);
    ElMessage.error('全只读/全不只读操作失败');
  }
};

/**
 * 更新treeNodes中的字段属性
 * @param item 字段项
 * @param childItem 子字段项
 * @param property 属性名
 * @param value 属性值
 */
const updateFieldInTreeNodes = (item: any, childItem: any, property: string, value: any) => {
  let ite_tree = null;
  if (nowCheckedMsg.treeNodes && Array.isArray(nowCheckedMsg.treeNodes)) {
    for (let i = 0; i < nowCheckedMsg.treeNodes.length; i++) {
      if (nowCheckedMsg.treeNodes[i].id === checkedYS.value.id) {
        ite_tree = nowCheckedMsg.treeNodes[i];
        break;
      }
    }
  }

  if (!ite_tree?.fieldGroupModelList) return;

  let ite_attr = null;
  for (let i = 0; i < ite_tree.fieldGroupModelList.length; i++) {
    if (ite_tree.fieldGroupModelList[i].linkId === checkedAtt.value.linkId) {
      ite_attr = ite_tree.fieldGroupModelList[i];
      break;
    }
  }

  if (!ite_attr?.fieldModelList) return;

  for (let i = 0; i < ite_attr.fieldModelList.length; i++) {
    const field = ite_attr.fieldModelList[i];

    if (childItem) {
      // 处理特殊字段（身份证、表格等）
      if (
        (item.valueMethod === 'idCardScan' || item.valueMethod === 'xtBankCard') &&
        field.fieldName === item.fieldName &&
        field.attribution?.expendList?.[0]?.enName === childItem.enName
      ) {
        field.attribution.expendList[0][property] = value;
        break;
      } else if (item.valueMethod === 'xttable' && field.fieldName === item.fieldName) {
        if (field.attribution?.children) {
          for (let j = 0; j < field.attribution.children.length; j++) {
            if (field.attribution.children[j].fieldName === childItem.fieldName) {
              field.attribution.children[j][property] = value;
              break;
            }
          }
        }
        break;
      }
    } else {
      // 处理普通字段
      if (field.fieldName === item.fieldName) {
        field[property] = value;
        break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer.rtl) {
  width: 800px !important;
}

.waring-color {
  background: rgb(255, 148, 62) !important;
}

.drawer-content {
  height: calc(100% - 40px);
  overflow: auto;
  padding: 12px;
}

.drawer-footer {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
}

.spe-hr-child {
  background-color: #cacaca;
  height: 100%;
  position: absolute;
  left: 50%;
  width: 2px;
  top: 0;
}

.flow-main {
  position: relative;
  display: flex;
  flex-direction: column;

  .rest-height {
    flex: 1;
    display: flex;
    justify-content: center;

    .hr {
      width: 2px;
      height: 100%;
      background-color: #cacaca;
    }
  }

  .display-left-top-hr {
    width: calc(50% - 1px);
    background: #f0f2f5;
    height: 2px;
    position: absolute;
    top: -2px;
  }

  .display-left-bottom-hr {
    width: calc(50% - 1px);
    background: #f0f2f5;
    height: 2px;
    position: absolute;
    bottom: -2px;
  }

  .display-right-top-hr {
    width: calc(50% - 1px);
    background: #f0f2f5;
    height: 2px;
    position: absolute;
    top: -2px;
    right: 0;
  }

  .display-right-bottom-hr {
    width: calc(50% - 1px);
    background: #f0f2f5;
    height: 2px;
    position: absolute;
    bottom: -2px;
    right: 0;
  }

  .flow-top-box {
    width: 220px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    .hr {
      width: 2px;
      height: 100%;
      background: #cccccc;
    }
  }

  .flow-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .flow-content {
      width: 220px;
      height: 72px;
      box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      border: transparent solid 1px;
      z-index: 2;

      .title-box {
        height: 28px;
        padding: 0px 24px;
        background: rgb(50, 150, 250);
        line-height: 28px;
        color: #fff;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        position: relative;
        font-size: 14px;

        .close-btn {
          color: #fff;
          font-size: 18px;
          position: absolute;
          right: 0;
          top: 0;
        }
      }

      .item-content {
        height: calc(100% - 28px);
        display: flex;
        align-items: center;
        padding: 0px 12px;
        font-size: 14px;

        .con-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          width: 100%;
          height: 100%;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          /* 这里设置超出几行省略 */
          line-clamp: 2;
          overflow: hidden;
          position: relative;

          .right-con {
            position: absolute;
            right: 0px;
            top: 10px;
            width: 16px;
            height: 16px;
            font-size: 14px;
          }
        }
      }
    }

    .flow-content:hover {
      border: rgb(50, 150, 250) solid 1px;
    }

    .add-node-box {
      width: 240px;
      height: 82px;
      position: relative;

      .add-node-btn {
        user-select: none;
        width: 240px;
        padding: 20px 0px 32px;
        display: flex;
        -webkit-box-pack: center;
        justify-content: center;
        flex-shrink: 0;
        -webkit-box-flex: 1;
        flex-grow: 1;

        .add-btn {
          outline: none;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
          width: 30px;
          height: 30px;
          background: #3296fa;
          border-radius: 50%;
          position: relative;
          border: none;
          line-height: 30px;
          -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          padding: 0;
          z-index: 1;
          color: #fff;
          cursor: pointer;
        }

        .add-btn:hover {
          transform: scale(1.3);
          box-shadow: 0 13px 27px 0 rgba(0, 0, 0, 0.1);
        }
      }

      .handle-box {
        z-index: 3;
        position: absolute;
        left: calc(50% + 20px);
        top: calc(50% - 20px);
        background: #fff;
        padding: 15px;
        border-radius: 8px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;

        .handle-item {
          width: 160px;
          height: 50px;
          background: rgba(17, 31, 44, 0.02);
          display: flex;
          align-items: center;
          cursor: pointer;

          .ico-image {
            width: 32px;
            height: 32px;
            margin: 0px 12px;
          }
        }

        .handle-item:hover {
          background: #ffffff;
          border: 1px solid #ecedef;
          box-shadow: 0 2px 8px 0 rgba(17, 31, 44, 0.08);
        }
      }
    }

    .add-node-box::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      margin: auto;
      width: 2px;
      height: 100%;
      background-color: #cacaca;
      z-index: 1;
    }
  }

  .flow-Branch-box {
    display: flex;
    flex-direction: column;
    align-items: center;

    .flow-Branch {
      display: flex;
      flex-direction: row;
      border-top: 2px solid #cccccc;
      border-bottom: 2px solid #cccccc;
      position: relative;

      .add-condition-btn {
        position: absolute;
        left: calc(50% - 61px);
        top: -15px;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        color: #0089ff;
        border-radius: 15px;
        background: #fff;
        padding: 5px 10px;
        z-index: 1;
        cursor: pointer;
      }

      .add-condition-btn:hover {
        transform: scale(1.1);
      }
    }

    .add-node-box {
      width: 240px;
      height: 82px;
      position: relative;

      .add-node-btn {
        user-select: none;
        width: 240px;
        padding: 20px 0px 32px;
        display: flex;
        -webkit-box-pack: center;
        justify-content: center;
        flex-shrink: 0;
        -webkit-box-flex: 1;
        flex-grow: 1;

        .add-btn {
          outline: none;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
          width: 30px;
          height: 30px;
          background: #3296fa;
          border-radius: 50%;
          position: relative;
          border: none;
          line-height: 30px;
          -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          padding: 0;
          z-index: 1;
          color: #fff;
          cursor: pointer;
        }

        .add-btn:hover {
          transform: scale(1.3);
          box-shadow: 0 13px 27px 0 rgba(0, 0, 0, 0.1);
        }
      }

      .handle-box {
        z-index: 3;
        position: absolute;
        left: calc(50% + 20px);
        top: calc(50% - 20px);
        background: #fff;
        padding: 15px;
        border-radius: 8px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;

        .handle-item {
          width: 160px;
          height: 50px;
          background: rgba(17, 31, 44, 0.02);
          display: flex;
          align-items: center;
          cursor: pointer;

          .ico-image {
            width: 32px;
            height: 32px;
            margin: 0px 12px;
          }
        }

        .handle-item:hover {
          background: #ffffff;
          border: 1px solid #ecedef;
          box-shadow: 0 2px 8px 0 rgba(17, 31, 44, 0.08);
        }
      }
    }

    .add-node-box::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      margin: auto;
      width: 2px;
      height: 100%;
      background-color: #cacaca;
      z-index: 1;
    }
  }
}

.group-item {
  margin-bottom: 28px;
  border: 1px solid #ededed;

  .group-title {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    background: #f8f8f8;
    justify-content: space-between;
    cursor: pointer;

    .title {
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
      font-weight: 600;
    }

    .icon {
      i {
        font-size: 18px;
        color: #999;
      }
    }
  }

  .group-content {
    padding: 16px;
    overflow: auto;

    .flex-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      .flex-one {
        flex: 100%;
        margin-right: 16px;
      }

      .flex-item {
        flex: 0 0 calc(33.33% - 16px);
        /* 25% width minus the gap */
        margin-right: 16px;
        /* Right margin for the gap */
        // margin-bottom: 16px; /* Bottom margin for the gap */
        box-sizing: border-box;
        /* Include padding and border in the width */
      }

      .flex-row {
        width: calc(100% - 16px);
      }
    }
  }

  /*滚动条样式*/
  .group-content::-webkit-scrollbar {
    width: 4px;
  }

  .group-content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }

  .group-content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}

.tree-div {
  height: calc(100% - 49px);
  // overflow: auto;
  width: 100%;

  .tree-row {
    height: 44px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    width: 100%;

    // padding: 0px 16px;
    .tree-row-left {
      display: flex;
      flex-direction: row;
      align-items: center;

      .tree-img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        background: #fff;
      }

      .svg-item {
        width: 16px;
        height: 16px;
        // margin-right: 8px;
        // color: #333;
        vertical-align: middle;
      }
    }

    .tree-row-handle {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 2px;
    }

    .tree-row-handle:hover {
      background: var(--current-color);
      color: #fff;
    }
  }

  .active-tree {
    background: var(--current-color);
    color: #fff;
  }
}

.empty-span {
  color: #909399;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.gry-span {
  color: #909399 !important;
}

.flex-row {
  min-height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  align-content: center;
  cursor: pointer;
  font-size: 12px;
  height: 40px;

  :deep(.el-checkbox:last-of-type) {
    padding: 5px 0px;
  }

  :deep(.el-checkbox__label) {
    display: inline-grid;
    white-space: pre-line;
    word-wrap: break-word;
    overflow: hidden;
    font-size: 12px;
  }

  .label {
    color: rgba(22, 29, 38, 1);
    font-size: 12px;
    padding-left: 12px;
    flex: 1;
    min-height: 32px;
    line-height: 32px;
  }

  .flex-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    align-content: center;
    width: 60%;

    .ico {
      min-height: 32px;
      width: 70px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      justify-items: center;
      padding-right: 8px;
      text-align: center;
    }

    .input {
      min-height: 30px;
      min-width: 150px;
      width: 100%;
      flex: 1;

      :deep(.el-input) {
        // 输入框主体样式
        .el-input__inner {
          min-width: 150px;
          width: 100%;
          flex: 1;
          height: 30px;

          // 现代浏览器标准placeholder
          &::placeholder {
            font-size: 12px;
          }

          // 浏览器兼容性处理
          &::-webkit-input-placeholder {
            /* Chrome/Safari/Edge */
            font-size: 12px;
          }

          &::-moz-placeholder {
            /* Firefox */
            font-size: 12px;
            opacity: 1; // 修复Firefox默认透明度问题
          }

          &::-ms-input-placeholder {
            /* IE10-11 */
            font-size: 12px;
          }
        }

        // 后缀图标区域
        .el-input__suffix {
          .el-select__caret {
            width: 16px;
            height: 30px;
            line-height: 30px;
          }
        }

        // 前缀图标区域
        .el-input__prefix {
          width: 16px;
          height: 30px;
          line-height: 30px;

          .el-input__icon {
            width: 16px;
            height: 30px;
            line-height: 30px;
          }
        }
      }

      .el-date-editor {
        .el-range-separator {
          height: 30px;
          line-height: 30px;
        }

        .el-input__inner {
          height: 30px;

          :deep(.el-range__icon) {
            height: 30px;
            line-height: 30px;
          }
        }
      }
    }
  }
}

.flex-row:hover {
  background-color: #f5f7fa;
}

.flex-active {
  background: #edf4fb;
}

.check-item {
  height: auto;
  min-height: 32px;
  align-items: flex-start;
}

.flex-all {
  min-height: 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  font-size: 12px;

  .big-title {
    font-size: 12px;
    margin-left: 10px;
  }

  .flex-row-all {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    height: 40px;

    :deep(.el-checkbox) {
      // 最后一个复选框的特殊间距
      &:last-of-type {
        padding: 5px 0; // 移除0值单位
      }

      // 标签样式（合并为一个声明块）
      .el-checkbox__label {
        display: inline-grid;
        white-space: pre-line;
        word-wrap: break-word;
        overflow: hidden;
        font-size: 12px;
      }
    }

    .label {
      color: rgba(22, 29, 38, 1);
      font-size: 12px;
      padding-left: 12px;
      flex: 1;
      min-height: 32px;
      line-height: 32px;
      width: 40%;
    }

    .flex-right {
      display: flex;
      justify-content: space-between;
      width: 60%;

      .ico {
        min-height: 32px;
        width: 70px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        justify-items: center;
        padding-right: 8px;
        text-align: center;
      }

      .input {
        min-height: 30px;
        min-width: 150px;
        width: 100%;
        flex: 1;

        :deep(.el-input) {
          // 输入框主体样式
          .el-input__inner {
            min-width: 150px;
            width: 100%;
            flex: 1;
            height: 30px;

            // 现代浏览器标准placeholder
            &::placeholder {
              font-size: 12px;
            }

            // 浏览器兼容性处理
            &::-webkit-input-placeholder {
              /* Chrome/Safari/Edge */
              font-size: 12px;
            }

            &::-moz-placeholder {
              /* Firefox */
              font-size: 12px;
              opacity: 1; // 修复Firefox默认透明度问题
            }

            &::-ms-input-placeholder {
              /* IE10-11 */
              font-size: 12px;
            }
          }

          // 后缀图标区域
          .el-input__suffix {
            .el-select__caret {
              width: 16px;
              height: 30px;
              line-height: 30px;
            }
          }

          // 前缀图标区域
          .el-input__prefix {
            width: 16px;
            height: 30px;
            line-height: 30px;

            .el-input__icon {
              width: 16px;
              height: 30px;
              line-height: 30px;
            }
          }
        }

        .el-date-editor {
          .el-range-separator {
            height: 30px;
            line-height: 30px;
          }

          .el-input__inner {
            height: 30px;

            :deep(.el-range__icon) {
              height: 30px;
              line-height: 30px;
            }
          }
        }
      }
    }

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.setting-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 0 20px 20px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}

.dialog-content {
  .setting-content {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px 0 20px 20px;
    background-color: #fff;
    width: 100%;
  }
}

.group-item {
  height: 450px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  .group-title {
    padding: 12px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .title {
      font-weight: bold;
      color: #606266;
    }
  }

  .group-content {
    padding: 12px;
  }
}
</style>
