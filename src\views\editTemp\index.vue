<!-- 编辑文档 -->
<template>
  <div class="editTemp-main">
    <div class="handle">
      <div class="item" @click="showFast">快捷表达式</div>
      <div @click="handleCopy" class="item">自定义表达式</div>
    </div>
    <iframe
      :src="`/office.html?downloadUrl=${url}&templateName=${templateName}&tempId=${tempId}&baseUrl=${baseUrl}&token=${token}`"
      style="width: 100%; height: 100%"
    ></iframe>

    <!-- TODO -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="expression"
      :is-copy="true"
      @close-formula-edit="handleCloseFormulation"
      @submit-formulation="handleSubmitFormulation"
      :app-type="appType"
    />

    <!-- 快捷表达式 -->
    <fastExpression
      :mapFieldDialog="mapFieldDialog"
      @submitFastExp="submitFastExp"
      @handleCloseFast="handleCloseFast"
      :moduleId="moduleId"
    ></fastExpression>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import fastExpression from '@/components/fastExpression/index.vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter } from 'vue-router';

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();

// 响应式数据
const url = ref<string>((router.currentRoute.value.query.url as string) || '');
const templateName = ref<string>((router.currentRoute.value.query.templateName as string) || '');
const tempId = ref<string>((router.currentRoute.value.query.tempId as string) || '');
const baseUrl = ref<string>((router.currentRoute.value.query.baseUrl as string) || '');
const token = ref<string>((router.currentRoute.value.query.token as string) || '');
const moduleId = ref<number>(parseInt(router.currentRoute.value.query.moduleId as string) || 0);
const formulaVisible = ref<boolean>(false);
const expression = ref<string>('');
const mapFieldDialog = ref<boolean>(false);
const appType = ref<string>('#word');

// 方法
const handleCloseFormulation = () => {
  modalStore.setIsAllGroup(false);
  formulaVisible.value = false;
};

const handleSubmitFormulation = (exp: string) => {
  expression.value = exp;
  copyFun(exp);
};

const copyFun = (exp: string) => {
  const tag = document.createElement('textarea');
  document.body.appendChild(tag);
  tag.value = exp;
  tag.select();
  document.execCommand('copy', false);
  ElMessage.success('复制成功');
  tag.remove();
};

const handleCopy = () => {
  modalStore.setIsHasAcquition(false);
  modalStore.setIsAllGroup(true);
  expression.value = '';
  formulaVisible.value = true;
};

const showFast = () => {
  mapFieldDialog.value = true;
};
const submitFastExp = (exp: string) => {
  copyFun(exp);
  mapFieldDialog.value = false;
};
const handleCloseFast = () => {
  mapFieldDialog.value = false;
};

// 生命周期
onMounted(() => {
  // 初始化逻辑...
});
</script>
<style lang="scss" scoped>
.editTemp-main {
  width: 100%;
  height: 100%;
  .handle {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #fff;
    display: flex;
    align-items: center;
    .item {
      padding: 10px;
      cursor: pointer;
    }
  }
}
</style>
