import axios, { AxiosResponse } from 'axios';

/**
 * 加载文件
 * @param path 文件路径
 * @returns 文件内容
 */
const loadFile = async function (path: string): Promise<AxiosResponse> {
  let text: AxiosResponse;
  await axios.get(path).then((res: AxiosResponse) => {
    text = res;
  });
  return text;
};

/**
 * 文件下载
 * @param downloadUrl 文件URL
 * @param fileName 文件名
 */
const fileDownload = function (downloadUrl: string, fileName: string): void {
  const aLink = document.createElement('a');
  aLink.style.display = 'none';
  aLink.href = downloadUrl;
  aLink.download = fileName;
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
};

/**
 * Base64转文件
 * @param base64 Base64编码的字符串
 * @param fileName 文件名
 * @returns 文件对象
 */
const base64toFile = function (base64: string, fileName: string): File {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], fileName, { type: mime });
};

export { fileDownload, base64toFile, loadFile };
