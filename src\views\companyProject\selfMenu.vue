<template>
  <div class="main">
    <container-card>
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch">
        <el-form-item label="菜单名称" prop="menuName">
          <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="菜单状态" clearable>
            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" size="default" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" size="default" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain :icon="Plus" size="default" @click="handleAdd" v-hasPermi="['system:menu:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain :icon="Sort" size="default" @click="toggleExpandAll">展开/折叠</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="menuList"
        row-key="menuId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="160"></el-table-column>
        <el-table-column prop="icon" label="图标" align="center" width="100">
          <template #default="scope">
            <svg-icon :icon-class="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
        <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link :icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:menu:edit']">修改</el-button>
            <el-button link :icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:menu:add']">新增</el-button>
            <el-button link :icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:menu:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加或修改菜单对话框 -->
      <el-dialog :title="title" v-model="open" width="680px" append-to-body :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="上级菜单" prop="parentId">
                <!-- <treeselect v-model="form.parentId" :options="menuOptions" :normalizer="normalizer" :show-count="true" placeholder="选择上级菜单" /> -->
                <el-tree-select
                  v-model="form.parentId"
                  :data="menuOptions"
                  :props="{ value: 'menuId', label: 'menuName', children: 'children' } as any"
                  value-key="menuId"
                  placeholder="选择上级菜单"
                  check-strictly
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="菜单类型" prop="menuType">
                <el-radio-group v-model="form.menuType">
                  <el-radio value="M">目录</el-radio>
                  <el-radio value="C">菜单</el-radio>
                  <el-radio value="F">按钮</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="form.menuType != 'F'">
              <el-form-item label="菜单图标" prop="icon">
                <el-popover placement="bottom-start" width="460" trigger="click">
                  <template #default>
                    <!-- <icon-select ref="iconSelectRef" @selected="selected" /> -->
                    <IconSelect v-model="form.icon" />
                  </template>
                  <template #reference>
                    <el-input v-model="form.icon" placeholder="点击选择图标" readonly>
                      <template #prefix>
                        <svg-icon v-if="form.icon" :icon-class="form.icon" class="el-input__icon" style="height: 32px; width: 16px" />
                        <el-icon v-else><Search /></el-icon>
                      </template>
                    </el-input>
                  </template>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="菜单名称" prop="menuName">
                <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示排序" prop="orderNum">
                <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType != 'F'">
              <el-form-item prop="isFrame">
                <template #label>
                  <span>
                    <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    是否外链
                  </span>
                </template>
                <el-radio-group v-model="form.isFrame">
                  <el-radio value="0">是</el-radio>
                  <el-radio value="1">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType != 'F'">
              <el-form-item prop="path">
                <template #label>
                  <span>
                    <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    路由地址
                  </span>
                </template>
                <el-input v-model="form.path" placeholder="请输入路由地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType == 'C'">
              <el-form-item prop="component">
                <template #label>
                  <span>
                    <el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    组件路径
                  </span>
                </template>
                <el-input v-model="form.component" placeholder="请输入组件路径" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType != 'M'">
              <el-form-item prop="perms">
                <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
                <template #label>
                  <span>
                    <el-tooltip content="控制器中定义的角色英文名，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    角色英文名
                  </span>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType == 'C'">
              <el-form-item prop="query">
                <el-input v-model="form.query" placeholder="请输入路由参数" maxlength="255" />
                <template #label>
                  <span>
                    <el-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    路由参数
                  </span>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType == 'C'">
              <el-form-item prop="isCache">
                <template #label>
                  <span>
                    <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    是否缓存
                  </span>
                </template>
                <el-radio-group v-model="form.isCache">
                  <el-radio value="0">缓存</el-radio>
                  <el-radio value="1">不缓存</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType != 'F'">
              <el-form-item prop="visible">
                <template #label>
                  <span>
                    <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    显示状态
                  </span>
                </template>
                <el-radio-group v-model="form.visible">
                  <el-radio v-for="dict in sys_show_hide" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType != 'F'">
              <el-form-item prop="status">
                <template #label>
                  <span>
                    <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    菜单状态
                  </span>
                </template>
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </container-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from '@/api/system/menu';
import IconSelect from '@/components/IconSelect/index.vue';
import { Delete, Edit, Search, Share, Upload, Refresh, Plus, Sort } from '@element-plus/icons-vue';
// 获取路由参数
const route = useRoute();

// 字典数据
const { proxy } = getCurrentInstance() as any;
const { sys_show_hide, sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_show_hide', 'sys_normal_disable'));

// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 菜单表格树数据
const menuList = ref([]);
// 菜单树选项
const menuOptions = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 是否展开，默认全部折叠
const isExpandAll = ref(false);
// 重新渲染表格状态
const refreshTable = ref(true);
// 表单参数
const form = reactive({
  menuId: undefined,
  parentId: 0,
  menuName: '',
  icon: '',
  menuType: 'M',
  orderNum: undefined,
  isFrame: '1',
  isCache: '0',
  visible: '0',
  status: '0',
  perms: '',
  component: '',
  query: '',
  path: '',
  companyId: undefined,
  vipType: 1 // 权限级别 默认个人
});
// 是否是控制台专属
const spesal = ref('2');
// 表单校验
const rules = {
  menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
  orderNum: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
  path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }]
};
// 查询参数
const queryParams = reactive({
  menuName: undefined,
  visible: undefined,
  companyId: ''
});
// VIP级别列表
const vipList = [
  { label: '个人版', value: 1 },
  { label: '专业版', value: 2 },
  { label: '企业版', value: 3 }
];

// 表单ref
const formRef = ref();
const queryFormRef = ref();
const iconSelectRef = ref();

// 用户信息
const roles = computed(() => userStore.roles);
const user = computed(() => userStore.user);

onMounted(() => {
  const companyId = route.query.companyId;
  if (companyId) {
    getList();
  }
});

// 选择图标
function selected(name: string) {
  form.icon = name;
}

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  const params = {
    companyId: route.query.companyId,
    menuName: queryParams.menuName,
    status: queryParams.status
  };
  listMenu(params).then((response: any) => {
    menuList.value = proxy?.handleTree(response.data, 'menuId');
    loading.value = false;
  });
}

/** 转换菜单数据结构 */
function normalizer(node: any) {
  if (node.children && !node.children.length) {
    delete node.children;
  }
  return {
    id: node.menuId,
    label: node.menuName,
    children: node.children
  };
}

/** 查询菜单下拉树结构 */
function getTreeselect() {
  const params = {
    companyId: route.query.companyId
  };
  listMenu(params).then((response: any) => {
    menuOptions.value = [];
    const menu = { menuId: 0, menuName: '主类目', children: [] };
    menu.children = proxy?.handleTree(response.data, 'menuId');
    menuOptions.value.push(menu);
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.menuId = undefined;
  form.parentId = 0;
  form.menuName = '';
  form.icon = '';
  form.menuType = 'M';
  form.orderNum = undefined;
  form.isFrame = '1';
  form.isCache = '0';
  form.visible = '0';
  form.status = '0';
  form.perms = '';
  form.component = '';
  form.query = '';
  form.path = '';
  form.companyId = undefined;
  form.vipType = 1;

  spesal.value = '2';

  // 重置表单校验
  nextTick(() => {
    formRef.value?.resetFields();
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row?: any) {
  reset();
  getTreeselect();
  if (row?.menuId) {
    form.parentId = row.menuId;
  } else {
    form.parentId = 0;
  }
  open.value = true;
  title.value = '添加菜单';
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
  reset();
  getTreeselect();
  getMenu(row.menuId).then((response: any) => {
    Object.assign(form, response.data);
    spesal.value = response.data.companyId == '1' ? '1' : '2';
    open.value = true;
    title.value = '修改菜单';
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      const companyId = route.query.companyId as string;
      if (companyId) {
        form.companyId = companyId;
      }

      if (form.menuId !== undefined) {
        updateMenu(form).then((response: any) => {
          ElMessage.success('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addMenu(form).then((response: any) => {
          if (response.code == 200) {
            ElMessage.success('新增成功');
            open.value = false;
            getList();
          } else {
            ElMessage.error(response.msg);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
  ElMessageBox.confirm(`是否确认删除名称为"${row.menuName}"的数据项？`)
    .then(() => {
      return delMenu(row.menuId);
    })
    .then(() => {
      getList();
      ElMessage.success('删除成功');
    })
    .catch(() => {});
}

// 是否控制台专属
function radioChange(e: string) {
  if (e == '1') {
    // 控制台专属
    form.companyId = '1';
  } else {
    form.companyId = null;
  }
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  .mb8 {
    margin-bottom: 8px;
  }

  .small-padding {
    padding-left: 5px;
    padding-right: 5px;
  }

  .fixed-width {
    width: 120px;
  }

  .tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #ffffff none;
    border-radius: 4px;
    width: 100%;
  }

  .el-button {
    margin-right: 5px;
  }
}
</style>
