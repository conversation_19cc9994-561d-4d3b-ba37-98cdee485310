<template>
  <!-- 权属人设置 -->
  <div class="owner-main">
    <div v-show="!isFieldForm" class="owner-list">
      <el-row style="margin: 16px">
        <el-button size="small" :icon="Plus" type="primary" @click="handleOpenOwnerDialog" plain>新增属性</el-button>
      </el-row>
      <el-row v-for="item in ownerList" :key="item.id">
        <owner-item
          @reflectField="reflectField"
          @openField="handleOpenField"
          :item="item"
          :ownerTypeList="ownerTypeList"
          @updateOwner="handleUpdateOwnerList"
          @openFieldFormpenFieldForm="handleOpenFieldForm"
        ></owner-item>
      </el-row>
      <el-row v-if="ownerList.length == 0" class="owner-no-data">
        <el-image :src="onDataOwnering" style="width: 168px; height: 168px" fit="cover"> </el-image>
        <div class="text">暂无数据</div>
      </el-row>
    </div>

    <div class="btn-next-step">
      <!-- <el-button type="success" @click="handlePrev" size="mini"><i class="el-icon-arrow-left el-icon-left" style="margin-right: 5px;"></i>上一步</el-button> -->
      <el-button type="primary" @click="handleNext" size="small">
        下一步<el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <!-- 新增属性的弹框 -->
    <add-owner-type-dialog
      :ownerVisible="ownerVisible"
      @closeOwner="handleCloseOwner"
      @updateOwner="handleUpdateOwnerList"
      @openFieldForm="handleOpenFieldForm"
    ></add-owner-type-dialog>

    <!-- 新增字段表单 -->
    <dynamic-from
      :class="{ 'dynamic-form-class': true }"
      v-show="isFieldForm"
      @goBack="handleGoBack"
      @submitField="handleSubmitField"
      :confProp="formData"
      :isFieldFormProp="isFieldForm"
      :isTypeProp="''"
      :typeProp="1"
      @updateGroup="handleUpdateOwnerList"
    ></dynamic-from>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, defineProps, defineEmits } from 'vue';
import addOwnerTypeDialog from './addOwnerTypeDialog.vue';
import ownerItem from './ownerItem.vue';
import DynamicFrom from '@/components/DynamicForm/index.vue';
import onDataOwnering from '@/assets/images/nodata-owning.png';
import { useModalStore } from '@/store/modules/modal';
import { Plus, ArrowRight } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps<{
  ownerList: Array<{ id: number }>;
  ownerTypeList: Array<any>;
}>();

// 定义 emits
const emit = defineEmits<{
  (event: 'updateOwnerList', moduleId: number): void;
  (event: 'nextStep', step: number): void;
}>();

// 定义响应式数据
const isFieldForm = ref(false);
const ownerVisible = ref(false);
const formData = ref<Record<string, any>>({});

// 计算属性
const moduleId = computed(() => useModalStore().moduleId);

// 方法
const handleOpenOwnerDialog = () => {
  ownerVisible.value = true;
};

const handleCloseOwner = () => {
  ownerVisible.value = false;
};

const handleUpdateOwnerList = () => {
  emit('updateOwnerList', moduleId.value);
};

const handleOpenFieldForm = (groupId: number) => {
  formData.value = {};
  isFieldForm.value = true;
};

const handleGoBack = () => {
  formData.value = {};
  isFieldForm.value = false;
};

const handleSubmitField = () => {
  isFieldForm.value = false;
};

const reflectField = (formDataParam: Record<string, any>) => {
  formData.value = formDataParam;
  isFieldForm.value = true;
};

const handleOpenField = () => {
  isFieldForm.value = true;
};

const handleNext = () => {
  emit('nextStep', 3);
};

const handlePrev = () => {
  emit('nextStep', 1);
};
</script>

<style lang="scss" scoped>
.owner-main {
  position: relative;
  .owner-list {
    width: 70%;
    margin: 16px 15% 16px;
    background-color: #fff;
    min-width: 400px;
    height: calc(100vh - 220px);
    overflow: auto;
    border-radius: 8px;
    box-shadow: 0px 4px 10px 0px rgba(130, 145, 169, 0.12);
    .owner-no-data {
      text-align: center;
      margin-top: 150px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .text {
        height: 22px;
        font-size: 14px;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        font-weight: 400;
        color: #8291a9;
        line-height: 22px;
        margin-top: -16px;
      }
    }
  }
  .btn-next-step {
    position: absolute;
    right: 16%;
    left: 16%;
    top: calc(100vh - 300px);
    display: flex;
    justify-content: flex-end;
  }
}

.dynamic-form-class {
  width: 100%;
  background-color: #fff;
}
</style>
