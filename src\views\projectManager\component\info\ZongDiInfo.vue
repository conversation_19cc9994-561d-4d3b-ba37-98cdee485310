<!-- 宗地信息的详情展示页面 -->
<template>
  <div class="zong-di-info-contianer">
    <div v-for="(zditem, zindex) in zdDetialTitleList" :key="zditem.title" class="info-row" :class="zindex % 2 === 1 ? 'one' : 'two'">
      <div class="label">{{ zditem[zditem.title] }}</div>
      <div class="content" :title="currentParceItem.attribution[zditem.title]">{{ currentParceItem.attribution[zditem.title] }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import { getFieldList } from '@/api/project';

interface TitleItem {
  [key: string]: string;
  title: string;
}

interface Props {
  parceInfoItem: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  parceInfoItem: () => ({})
});

const store = useProjectStore();
const zdDetialTitleList = ref<TitleItem[]>([]);

const currentParceItem = computed(() => store.parcelInfo);

// 获取数据字典中的数据
const getAllFieldList = async () => {
  const params = {
    pageNum: 1,
    pageSize: 1000,
    appType: 1
  };

  try {
    const res = await getFieldList(params);
    if (res.code === 200) {
      const arr: TitleItem[] = [];
      const titleArr: string[] = [];

      res.data.rows.forEach((item: any) => {
        // fieldCate  1  宗地  2  房产  3 楼层  4  附属设施
        if (item.fieldCate === 1) {
          const obj: TitleItem = {
            [item.fieldName]: item.fieldCn,
            title: item.fieldName
          };
          const titleObj = { title: item.fieldName };
          arr.push(obj);
          titleArr.push(item.fieldName);
        }
      });

      if (currentParceItem.value.attribution != null) {
        const keyNameList = Object.keys(currentParceItem.value.attribution);
        zdDetialTitleList.value = [];

        keyNameList.forEach((kItem) => {
          arr.forEach((item) => {
            if (titleArr.includes(kItem) && item.title === kItem) {
              const obj: TitleItem = {
                [kItem]: item[kItem],
                title: kItem
              };
              zdDetialTitleList.value.push(obj);
            }
          });

          if (!titleArr.includes(kItem)) {
            const obj: TitleItem = {
              [kItem]: '未知',
              title: kItem
            };
            zdDetialTitleList.value.push(obj);
          }
        });
      }
    }
  } catch (error) {
    console.error('获取数据字典失败:', error);
  }
};

onMounted(() => {
  getAllFieldList();
});
</script>

<style lang="scss" scoped>
.zong-di-info-contianer {
  width: 512;
  height: calc(100vh - 300px);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  .info-row {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    flex-direction: row;
    height: 40px;
    padding: 16px;
    &:nth-child(odd) {
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
    }
    .label {
      width: 40%;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
    }
    .content {
      width: 60%;
      max-width: 350px;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-input {
      :deep(.el-input__inner) {
        width: 100%;
        height: 32px;
        background-color: #ffffff;
        border-radius: 4px;
        border-color: none;
        opacity: 0.2;
        border: none;
        z-index: 8;
        color: #ce0c0c;
        &::placeholder {
          color: #ffffff;
          opacity: 1;
          z-index: 10;
        }
        /* 谷歌 */
        &::-webkit-input-placeholder {
          color: #ffffff;
          opacity: 0.5;
          z-index: 99;
        }
        /* 火狐 */
        &::-moz-placeholder {
          color: #ffffff;
          opacity: 1;
          z-index: 10;
        }
        /*ie*/
        &::-ms-input-placeholder {
          color: #ffffff;
          opacity: 1;
          z-index: 10;
        }
      }
      :deep(.el-input__prefix) {
        height: 32px;
        line-height: 32px;
        left: 12px;
        color: #ffffff;
      }
    }
  }
}
</style>
