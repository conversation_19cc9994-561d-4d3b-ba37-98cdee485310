<!-- 新增安置房 -->
<template>
  <div>
    <el-dialog title="新增执行" v-model="afzDialogCopy" width="90%" :close-on-click-modal="false" :before-close="handleClose">
      <div class="content" :style="{ height: height }">
        <div class="handle-div">
          <div class="flex-item">
            <div class="label">{{ activeMenu.fieldList[0].label }}</div>
            <div class="right">
              <el-select v-model="addMsg.AZLX" placeholder="请选择" clearable>
                <el-option
                  v-for="item in activeMenu.fieldList[0].field.attribution.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="flex-item">
            <div class="label">{{ activeMenu.fieldList[1].label }}</div>
            <div class="right">
              <el-input v-model="addMsg.XJBCJE" :placeholder="activeMenu.fieldList[1].field.inputHint"></el-input>
            </div>
          </div>
          <div class="flex-item">
            <div class="label">{{ activeMenu.fieldList[2].label }}</div>
            <div class="right">
              <el-input v-model="addMsg.AZFFW" :placeholder="activeMenu.fieldList[2].field.inputHint" readonly></el-input>
            </div>
          </div>
          <div class="flex-item">
            <div class="label">{{ activeMenu.fieldList[3].label }}</div>
            <div class="right">
              <!-- 附件上传 -->
              <el-upload
                class="upload-demo"
                :headers="headers"
                :action="`${baseUrl}/qjt/file/multi/upload`"
                :on-success="(response) => handleSuccessFJ(response, activeMenu.fieldList[3].field)"
                :on-remove="(file) => handleRemoveFJ(file, activeMenu.fieldList[3].field)"
                multiple
                name="files"
                :before-upload="(file) => beforeAvatarUpload(file, activeMenu.fieldList[3].field)"
                :limit="activeMenu.fieldList[3].field.attribution.picNum"
                :on-exceed="(files, fileList) => handleExceed(files, fileList, activeMenu.fieldList[3].field)"
                :file-list="addMsg.AZBCHTSHYW"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip" style="color: red">只能上传{{ onlyAccpt }}文件</div>
                </template>
                <template #file="{ file }">
                  <div class="fj-row">
                    {{ file.name }}
                    <div style="cursor: pointer" @click="handleRemove(file)">×</div>
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </div>
        <div class="table-box">
          <iframe
            v-if="afzDialog"
            :src="azfSrc"
            frameborder="0"
            width="100%"
            height="100%"
            @load="onIframeLoad"
            ref="myIframe"
            id="myIframe"
          ></iframe>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitAddAfzDialog">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getToken } from '@/utils/auth';
import { isArray } from '@/utils/validate';
import { saveSimple } from '@/api/project';
// ---Props---
interface Props {
  // 打开弹框
  afzDialog: boolean;
  activeMenu?: any;
  nowQLR?: any;
  nowNodeMsg?: any;
}

const props = withDefaults(defineProps<Props>(), {
  afzDialog: false
});

const afzDialogCopy = computed(() => props.afzDialog);

watch(
  afzDialogCopy,
  (newVal) => {
    if (newVal) {
      // 再次进来需要初始化
      addMsg = {
        AZLX: '', //安置类型
        XJBCJE: '', //现金补偿金额
        AZFFW: '', //安置房房屋
        AZBCHTSHYW: [], //安置补偿合同审核业务协议书
        AZXQ: '' //安置小区
      };
      onlyAccpt.value = props.activeMenu.fieldList[4].field.attribution.acceptType.join(',');
      azfSrc.value = `${import.meta.env.VITE_APP_URL_BASE}/iframeAutoProject?isAZF=true`;
      localStorage.setItem('nowQLR', JSON.stringify(props.nowQLR));
    }
  },
  { deep: true }
);
//  ---定义emit---
const emit = defineEmits<{
  (e: 'handleCloseAfzDialog'): void;
  (e: 'handleSubmitAddAzf', zxAzf: any): void;
}>();

// ---定义变量 ---
let addMsg = reactive({
  AZLX: '', //安置类型
  XJBCJE: '', //现金补偿金额
  AZFFW: '', //安置房房屋
  AZBCHTSHYW: [], //安置补偿合同审核业务协议书
  AZXQ: '' //安置小区
});
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const headers = reactive({
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
});
const fit = ref('cover');
const token = ref(getToken());
const ruleIds = ref([]);
const moduleId = ref(0);
const azfSrc = ref('');
const width = ref(window.innerWidth * 0.9 + 'px');
const height = ref(window.innerHeight - 250 + 'px');
const chooseAZFMsg = reactive({});
const onlyAccpt = ref([]); //只允许上传的文件类型 安置补偿合同审核业务协议书
const azfQlrNode = ref(null); //安置房对应的节点信息

const onIframeLoad = () => {
  window.addEventListener('message', handleMessage, false);
};

const handleMessage = (event) => {
  // 假设消息的数据结构为 { method: 'methodName', args: [...] }
  const { method, args } = event.data;
  if (method === 'callChooseAzf') {
    parentMethod(...args);
  }
};

/**
 * 选中某个安置房返回来的数据
 * @param {Object} obj 安置房对应的节点信息
 * @param {String} mainName 安置小区
 */
const parentMethod = (obj, mainName) => {
  // 判断已安置的不允许选择
  let flg = false;
  for (let i = 0; i < obj.fieldInstanceModels.length; i++) {
    if (obj.fieldInstanceModels[i].groupName == '安置房户的基本信息') {
      if (
        !obj.fieldInstanceModels[i].attribution.FYZT ||
        (obj.fieldInstanceModels[i].attribution.FYZT && obj.fieldInstanceModels[i].attribution.FYZT != '已安置')
      ) {
        flg = true;
        break;
      }
    }
  }
  if (!flg) {
    ElMessage.error('该安置房已分配，请重新选择！！！');
    return;
  }
  azfQlrNode.value = obj; //安置房对应的节点信息
  addMsg.AZFFW = obj.parcelName;
  addMsg.AZXQ = mainName;
};

const handleClose = () => {
  emit('handleCloseAfzDialog');
};

const submitAddAfzDialog = async () => {
  if (!addMsg.AZLX) {
    ElMessage.error('请选择安置类型！！！');
    return;
  }
  if (addMsg.AZLX == '房屋' && !azfQlrNode.value) {
    ElMessage.error('请选择安置的房间！！！');
    return;
  }
  // 需要做两件事 1、给执行属性组增加一条安置数据 2、给安置房那边对应的数据增加该权利人
  ElMessageBox.confirm('确定要新增执行吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const item = props.nowNodeMsg;
      const fieldInstance: any = {};
      for (let i = 0; i < item.fieldGroupModels.length; i++) {
        if (item.fieldGroupModels[i].linkId == props.activeMenu.linkId) {
          fieldInstance.attribution = {};
          fieldInstance.groupId = item.fieldGroupModels[i].id;

          // 新增
          fieldInstance.parcelLinkId = 0;
          fieldInstance.linkId = item.fieldGroupModels[i].linkId;
          fieldInstance.timeStamp = 0;
          fieldInstance.appId = 0;
          break;
        }
      }
      fieldInstance.attribution = addMsg;
      item.fieldInstanceModels = [fieldInstance];
      delete item.fieldGroupModels;
      let zxAzf: any = {};
      await saveSimple([item]).then((res) => {
        // 新增成功了之后需要把执行表格数据也加上
        if (res.code == 200) {
          zxAzf = {
            linkId: props.activeMenu.linkId,
            parcelLinkId: res.data[0].fieldInstanceModels[0].parcelLinkId,
            timeStamp: res.data[0].fieldInstanceModels[0].timeStamp,
            typeName: props.activeMenu.typeName,
            fieldList: []
          };
          const list = JSON.parse(JSON.stringify(props.activeMenu.fieldList));
          list[0].value = addMsg.AZLX;
          list[1].value = addMsg.XJBCJE;
          list[2].value = addMsg.AZFFW;
          list[3].value = addMsg.AZBCHTSHYW;
          zxAzf.fieldList = list;
        } else {
          ElMessage.error(res.msg);
        }
      });
      // 第二步 给安置房增加当前拆迁权利人
      const oldItem = JSON.parse(JSON.stringify(azfQlrNode.value));
      // 这里是为了保存安置房的基本信息
      const oldAzfBasice = JSON.parse(JSON.stringify(azfQlrNode.value));
      const qlrList = [];
      if (props.nowQLR.isChild) {
        const qlrItem = {
          appType: 2,
          parcelName: oldItem.parcelName,
          id: oldItem.id,
          ruleId: oldItem.ruleId,
          fieldInstanceModels: [],
          ruleAttribution: oldItem.ruleAttribution || null,
          fieldGroupModels: oldItem.fieldGroupModels
        };
        //代表有多个权利人
        props.nowQLR.list.forEach((q) => {
          const fieldInstanceQlr: any = {};
          for (let i = 0; i < qlrItem.fieldGroupModels.length; i++) {
            if (qlrItem.fieldGroupModels[i].typeName == '权利人') {
              fieldInstanceQlr.attribution = {};
              fieldInstanceQlr.groupId = qlrItem.fieldGroupModels[i].id;

              // 新增
              fieldInstanceQlr.parcelLinkId = 0;
              fieldInstanceQlr.linkId = qlrItem.fieldGroupModels[i].linkId;
              fieldInstanceQlr.timeStamp = 0;
              fieldInstanceQlr.appId = 0;
              break;
            }
          }
          q.fieldList.forEach((v) => {
            if (v.field.valueMethod == 'upload' && Array.isArray(v.value)) {
              //图片需要处理方位角等信息
              const value = [];
              v.value.forEach((k) => {
                const ite: any = { url: k.url };
                if (k.fwj) {
                  ite.fwj = k.fwj.value;
                }
                if (k.psdz) {
                  ite.psdz = k.psdz.value;
                }
                if (k.psry) {
                  ite.psry = k.psry.value;
                }
                if (k.pssb) {
                  ite.pssb = k.pssb.value;
                }
                if (k.pssj) {
                  ite.pssj = k.pssj.value;
                }
                if (k.wzxx) {
                  ite.wzxx = k.wzxx.value;
                }
                value.push(ite);
              });
              fieldInstanceQlr.attribution[v.field.fieldName] = value;
            } else if (v.field.valueMethod == 'idCardScan') {
              //处理权利人
              if (v.field_index == '8' || v.field_index == '9') {
                //身份证正反面需要处理
                if (Array.isArray(v.value) && v.value.length != 0) {
                  fieldInstanceQlr.attribution[`${v.field.fieldName}`] = v.value[0].url;
                }
              } else {
                fieldInstanceQlr.attribution[`${v.field.fieldName}`] = v.value;
              }
            } else if (v.field.valueMethod == 'select') {
              //下拉的需要反编译为value传给数据库
              // value 可能是vlaue也可能是label
              let ite_value = v.value;
              for (let i = 0; i < v.field.attribution.options.length; i++) {
                if (v.field.attribution.options[i].label == v.value) {
                  ite_value = v.field.attribution.options[i].value;
                  break;
                }
              }
              fieldInstanceQlr.attribution[v.field.fieldName] = ite_value ? ite_value : undefined;
            } else if (v.field.valueMethod == 'xtqm' || v.field.valueMethod == 'xtzw' || v.field.valueMethod == 'xtsjjt') {
              //签名指纹 特殊处理
              let value = undefined;
              if (v.value.length != 0) {
                value = v.value[0].url;
              }
              fieldInstanceQlr.attribution[v.field.fieldName] = value;
            } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
              //多选处理
              let ite_value = undefined;
              if (v.value.length != 0) {
                ite_value = v.value.join(',');
                v.value = v.value.join(',');
              }
              fieldInstanceQlr.attribution[v.field.fieldName] = ite_value;
            } else if (v.field.valueMethod == 'xttable') {
              //表格处理
              fieldInstanceQlr.attribution[v.field.fieldName] = JSON.stringify(v.value.tableTr);
            } else {
              fieldInstanceQlr.attribution[v.field.fieldName] = v.value ? v.value : undefined;
            }
          });
          qlrItem.fieldInstanceModels.push(fieldInstanceQlr);
        });
        delete qlrItem.fieldGroupModels;
        qlrList.push(qlrItem);
      } else {
        const qlrItem = {
          appType: 2,
          parcelName: oldItem.parcelName,
          id: oldItem.id,
          ruleId: oldItem.ruleId,
          fieldInstanceModels: [],
          ruleAttribution: oldItem.ruleAttribution || null,
          fieldGroupModels: oldItem.fieldGroupModels
        };
        const fieldInstanceQlr: any = {};
        for (let i = 0; i < qlrItem.fieldGroupModels.length; i++) {
          if (qlrItem.fieldGroupModels[i].typeName == '权利人') {
            fieldInstanceQlr.attribution = {};
            fieldInstanceQlr.groupId = qlrItem.fieldGroupModels[i].id;

            // 新增
            fieldInstanceQlr.parcelLinkId = 0;
            fieldInstanceQlr.linkId = qlrItem.fieldGroupModels[i].linkId;
            fieldInstanceQlr.appId = 0;
            break;
          }
        }
        props.nowQLR.fieldList.forEach((v) => {
          if (v.field.valueMethod == 'upload' && Array.isArray(v.value)) {
            //图片需要处理方位角等信息
            const value = [];
            v.value.forEach((k) => {
              const ite: any = { url: k.url };
              if (k.fwj) {
                ite.fwj = k.fwj.value;
              }
              if (k.psdz) {
                ite.psdz = k.psdz.value;
              }
              if (k.psry) {
                ite.psry = k.psry.value;
              }
              if (k.pssb) {
                ite.pssb = k.pssb.value;
              }
              if (k.pssj) {
                ite.pssj = k.pssj.value;
              }
              if (k.wzxx) {
                ite.wzxx = k.wzxx.value;
              }
              value.push(ite);
            });
            fieldInstanceQlr.attribution[v.field.fieldName] = value;
          } else if (v.field.valueMethod == 'idCardScan') {
            //处理权利人
            if (v.field_index == '8' || v.field_index == '9') {
              //身份证正反面需要处理
              if (Array.isArray(v.value) && v.value.length != 0) {
                fieldInstanceQlr.attribution[`${v.field.fieldName}`] = v.value[0].url;
              }
            } else {
              fieldInstanceQlr.attribution[`${v.field.fieldName}`] = v.value;
            }
          } else if (v.field.valueMethod == 'select') {
            //下拉的需要反编译为value传给数据库
            // value 可能是vlaue也可能是label
            let ite_value = v.value;
            for (let i = 0; i < v.field.attribution.options.length; i++) {
              if (v.field.attribution.options[i].label == v.value) {
                ite_value = v.field.attribution.options[i].value;
                break;
              }
            }
            fieldInstanceQlr.attribution[v.field.fieldName] = ite_value ? ite_value : undefined;
          } else if (v.field.valueMethod == 'xtqm' || v.field.valueMethod == 'xtzw' || v.field.valueMethod == 'xtsjjt') {
            //签名指纹 特殊处理
            let value = undefined;
            if (v.value.length != 0) {
              value = v.value[0].url;
            }
            fieldInstanceQlr.attribution[v.field.fieldName] = value;
          } else if (v.field.valueMethod == 'checkbox' || v.field.valueMethod == 'cascader') {
            //多选处理
            let ite_value = undefined;
            if (v.value.length != 0) {
              ite_value = v.value.join(',');
              v.value = v.value.join(',');
            }
            fieldInstanceQlr.attribution[v.field.fieldName] = ite_value;
          } else if (v.field.valueMethod == 'xttable') {
            //表格处理
            fieldInstanceQlr.attribution[v.field.fieldName] = JSON.stringify(v.value.tableTr);
          } else {
            fieldInstanceQlr.attribution[v.field.fieldName] = v.value ? v.value : undefined;
          }
        });
        qlrItem.fieldInstanceModels = [fieldInstanceQlr];
        delete qlrItem.fieldGroupModels;
        qlrList.push(qlrItem);
      }
      await saveSimple(qlrList).then((res) => {
        if (res.code == 200) {
        } else {
          ElMessage.error(res.msg);
        }
      });
      // 第三步需要把安置房的基本信息的房源状态改为已安置
      const azfBasice = {
        appType: 2,
        parcelName: oldAzfBasice.parcelName,
        id: oldAzfBasice.id,
        ruleId: oldAzfBasice.ruleId,
        fieldInstanceModels: [],
        ruleAttribution: oldAzfBasice.ruleAttribution || null,
        fieldGroupModels: oldAzfBasice.fieldGroupModels
      };
      let azfBasiceFieldInstanceModels: any = {};
      for (let i = 0; i < oldAzfBasice.fieldInstanceModels.length; i++) {
        if (oldAzfBasice.fieldInstanceModels[i].groupName == '安置房户的基本信息') {
          azfBasiceFieldInstanceModels = {};
          azfBasiceFieldInstanceModels.attribution = oldAzfBasice.fieldInstanceModels[i].attribution;
          azfBasiceFieldInstanceModels.groupId = oldAzfBasice.fieldInstanceModels[i].groupId;
          // 修改
          azfBasiceFieldInstanceModels.parcelLinkId = oldAzfBasice.fieldInstanceModels[i].parcelLinkId;
          azfBasiceFieldInstanceModels.linkId = oldAzfBasice.fieldInstanceModels[i].linkId;
          azfBasiceFieldInstanceModels.timeStamp = oldAzfBasice.fieldInstanceModels[i].timeStamp;
          azfBasiceFieldInstanceModels.appId = 0;
          // 主要就是为了改这里 关键
          azfBasiceFieldInstanceModels.attribution.FYZT = '已安置';
          break;
        }
      }
      azfBasice.fieldInstanceModels = [azfBasiceFieldInstanceModels];
      delete azfBasice.fieldGroupModels;
      await saveSimple([azfBasice]).then((res) => {
        if (res.code == 200) {
        } else {
          ElMessage.error(res.msg);
        }
      });
      emit('handleSubmitAddAzf', zxAzf);
    })
    .catch(() => {});
};

/**
 * 附件上传成功
 * @param response
 * @param item
 */
const handleSuccessFJ = (response: any, item: any) => {
  if (response.data && isArray(response.data)) {
    const obj: any = {};
    obj[`${item.fieldName}_0`] = response.data[0].path;
    obj[`${item.fieldName}_1`] = response.data[0].name;
    obj.url = response.data[0].path;
    obj.name = response.data[0].name;
    addMsg.AZBCHTSHYW.push(obj);
  }
};

/**
 * 附件移除
 * @param file
 * @param item
 */
const handleRemoveFJ = (file: any, item: any) => {
  let num = 0;
  if (item.content && isArray(item.content)) {
    for (let index = 0; index < item.content.length; index++) {
      if (file.response.data[0].path == item.content[index].url) {
        num = index;
        break;
      }
    }
    item.content.splice(num, 1);
  }
};
/**
 * 上传前验证
 * @param file
 * @param item
 */
const beforeAvatarUpload = (file: any, item: any) => {
  let flg = false;
  for (let index = 0; index < item.attribution.acceptType.length; index++) {
    if (file.type.includes(item.attribution.acceptType[index])) {
      flg = true;
      break;
    }
  }
  if (!flg) {
    ElMessage.error(`不支持上传${file.type}格式！！！`);
  }
  return flg;
};

/**
 * 图片上传超过最大限制提示
 * @param files
 * @param fileList
 * @param item
 */
const handleExceed = (files: any, fileList: any, item: any) => {
  if (fileList.length >= item.attribution.picNum) {
    ElMessage.error(`${item.fieldCn}最多允许上传${item.attribution.picNum}`);
  }
};

/**
 * 移除照片
 * @param file
 * @param item
 */
const handleRemove = (file: any, item?: any) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      for (let i = 0; i < item.length; i++) {
        if (item[i].uid == file.uid) {
          item.splice(i, 1);
          break;
        }
      }
    })
    .catch(() => {});
};
</script>
<style lang="scss" scoped>
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
}
.fj-row :hover {
  color: #409eff;
}
.content {
  display: flex;
  flex-direction: column;
  .handle-div {
    display: flex;
    align-items: center;
    .flex-item {
      flex: 1;
      display: flex;
      align-items: center;
      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }
      .right {
        flex: 1;
      }
    }
  }
  .table-box {
    flex: 1;
  }
}
</style>
