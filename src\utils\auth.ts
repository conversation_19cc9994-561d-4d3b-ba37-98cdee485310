import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token';

export function getToken(): string | undefined {
  return Cookies.get(TokenKey);
}
// ,{ SameSite:'None',Secure:true }
export function setToken(token: string) {
  if (import.meta.env.PROD) {
    return Cookies.set(Token<PERSON><PERSON>, token, { SameSite: 'None', Secure: true });
  } else {
    return Cookies.set(Token<PERSON><PERSON>, token);
  }
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey);
}
