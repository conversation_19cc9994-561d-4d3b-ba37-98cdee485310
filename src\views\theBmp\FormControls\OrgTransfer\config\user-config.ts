import { getDpts, getUsers } from '@/api/process';
import { ElMessage } from 'element-plus';

interface TreeNode {
  level: number;
  data: {
    deptId?: string | number;
    userId?: string | number;
    [key: string]: any;
  };
}

interface DeptNode {
  deptId: string | number;
  deptName: string;
  label: string;
  nodeId: string | number;
  memberType: 'dept';
}

interface UserNode {
  userId: string | number;
  userName: string;
  label: string;
  nodeId: string | number;
  memberType: 'user';
}

type NodeData = DeptNode | UserNode;

let searchResultType = '用户';

const toHump = (name: string): string => name.replace(/\_(\w)/g, (_, letter) => letter.toUpperCase());

async function getRootDept(): Promise<DeptNode | null> {
  let rootDepts: DeptNode[] = [];
  try {
    const res = await getOrgTree();
    rootDepts = res.data.map((v: any) => ({
      deptId: v.deptId,
      deptName: v.deptName,
      label: v.deptName,
      nodeId: v.deptId,
      memberType: 'dept'
    }));
  } catch (err) {}
  return rootDepts[0] || null;
}

async function getDepChildNode(orgId: string | number): Promise<DeptNode[]> {
  let promises: any[] = [];
  try {
    const treeRes = await getOrgTree(orgId);
    promises = treeRes.data[0].child;

    const childRes = await Promise.all(promises);
    return childRes.map((v: any) => ({
      deptId: v.deptId,
      deptName: v.deptName,
      label: v.deptName,
      nodeId: v.deptId,
      memberType: 'dept'
    }));
  } catch (error) {
    return [];
  }
}

async function loadDepOrUser(node: TreeNode | null, loadDep = true): Promise<NodeData[]> {
  let nodeData: NodeData[] = [];

  if (!node || node.level === 0) {
    try {
      const res = await getDpts({});
      if (res.code === 200) {
        return res.data
          .filter((item: any) => item.parentId === 0)
          .map((v: any) => ({
            deptId: v.deptId,
            deptName: v.deptName,
            label: v.deptName,
            nodeId: v.deptId,
            memberType: 'dept'
          }));
      } else {
        ElMessage.error(res.msg);
        return [];
      }
    } catch (error) {
      return [];
    }
  }

  if (node.data.deptId) {
    try {
      // 获取下级部门
      const deptRes = await getDpts({ parentId: node.data.deptId });
      if (deptRes.code === 200) {
        nodeData = deptRes.data.map((v: any) => ({
          deptId: v.deptId,
          deptName: v.deptName,
          label: v.deptName,
          nodeId: v.deptId,
          memberType: 'dept'
        }));
      }

      // 获取部门下的成员
      const userRes = await getUsers({
        deptId: node.data.deptId,
        pageNum: 1,
        pageSize: 1000
      });

      if (userRes.code === 200) {
        const userData = userRes.rows.map((v: any) => ({
          userId: v.userId,
          userName: v.nickName,
          label: v.nickName,
          nodeId: v.userId,
          memberType: 'user'
        }));

        nodeData = nodeData.length > 0 ? [...nodeData, ...userData] : userData;
      }
    } catch (error) {}
  }

  return nodeData;
}

export const USER_CONFIG = {
  tabName: '指定人员',
  type: 'user',
  children: 'children',

  nodeId: (data: NodeData): string | number => {
    return 'userId' in data ? data.userId : data.deptId;
  },

  label: 'label',

  isLeaf: (data: NodeData): boolean => {
    return 'userId' in data;
  },

  searchResTip: (data: NodeData): string => {
    return searchResultType;
  },

  disabled: (data: NodeData): boolean => {
    return !('userId' in data);
  },

  onload: (node: TreeNode) => {
    return loadDepOrUser(node, false);
  },

  onsearch: async (searchString: string, resolve: (data: NodeData[]) => void): Promise<void> => {
    try {
      const res = await getUsers({
        custName: searchString,
        pageNum: 1,
        pageSize: 1000
      });

      if (res.code === 200) {
        const list = res.rows.map((v: any) => ({
          userId: v.userId,
          userName: v.nickName,
          label: v.nickName,
          nodeId: v.userId,
          memberType: 'user'
        }));

        searchResultType = '用户';
        resolve(list);
      } else {
        ElMessage.error(res.msg);
        resolve([]);
      }
    } catch (error) {
      resolve([]);
    }
  }
};
