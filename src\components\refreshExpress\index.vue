<!--  -->
<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="刷新表达式"
      v-model="refreshExpressDialogCopy"
      width="45%"
      class="refresh-express-dialog"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
      border
    >
      <div class="dialog-search">
        <el-button type="primary" size="small" @click="searchBtn">筛选数据</el-button>
        <div class="selected-count" v-if="selectedIds.length > 0">
          已选择 {{ selectedIds.length }} 条数据
          <el-button type="text" @click="clearSelection">清空选择</el-button>
        </div>
      </div>
      <div class="table-container">
        <el-table-v2
          :columns="columns"
          :data="refreshMsg.zdList"
          :width="tableWidth"
          :height="485"
          fixed
          :row-class="getRowClass"
          @row-click="handleRowClick"
          @select="handleSelectTable"
          @select-all="handleSeletionAll"
        />
      </div>
      <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="dialogSearch.pageNum"
          :page-sizes="[20, 50, 100, 500, 1000, 2000, 5000]"
          :page-size="dialogSearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitRef">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      :moduleId="moduleIdPop"
      @editCondition="editCondition"
    ></dataSearch>
    <!-- 刷新进度 -->
    <el-dialog
      title="数据刷新进度"
      v-model="publicPlanDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      width="300px"
      @closed="handlePublickClose"
    >
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center">
        <el-progress type="circle" :stroke-width="16" :percentage="publicPrecentage" :key="publicPrecentage"> </el-progress>
        <div style="margin-top: 10px">
          {{ publicMsg }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import searchData from '@/views/autoProject/components/searchData/index.vue';
import { getPlaceDetail, updateParcelNew, updateParcelFromMain, getPlaceList } from '@/api/modal';
import { fieldOrderList } from '@/api/fieldManagement';
import { h } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import dataSearch from '@/components/dataSearch/index.vue';
import { getSearchTask } from '@/api/task';
import type { AnyColumn, RowClassNameGetter } from '@element-plus/table-v2';

// ---Props---
interface Props {
  // 打开弹框
  refreshExpressDialog: boolean;
  moduleIdPop: number;
  ruleTree: any[];
}

const props = withDefaults(defineProps<Props>(), {
  refreshExpressDialog: false
});

const refreshExpressDialogCopy = computed(() => props.refreshExpressDialog);
// 定义emit
const emit = defineEmits<{
  (e: 'closeRefreshExpress'): void;
}>();

// ---定义变量---
const refreshMsg = ref({
  zdList: [] as any[], // 表格数据列表
  zdListNames: '' // 选中的数据名称字符串
});
const refreshMsgRule = ref({
  zdList: [{ required: true, message: '请选择刷新的数据', trigger: 'change' }]
});
const searchDialog = ref(false);
const expressList = ref([]); //表达式列表
const treeExpressList = ref([]); //树的表达式列表
const publicPlanDialog = ref(false); //刷新表达式进度弹窗
const publicMsg = ref(''); // 公共进度弹框的描述
const publicPrecentage = ref(0); // 公共弹框的进度值
const publicExpressList = ref([]); //通过后台得到的最终表达式顺序 所有数据都需要安装该顺序进行排序
const uploadSuccessCount = ref(0);
const isPower = ref(true); //是否强制刷新
const errorMsgList = ref([]); //刷新表达式
const refreshMsgRef = ref();
const multipleTableRef = ref();
const multipleSelection = ref<any[]>([]); // 表格选择的数据
const selectedIds = ref<(string | number)[]>([]); // 存储所有已选择的数据ID
const defaultProps = reactive({
  children: 'list',
  label: 'parcelName'
});
const checkTreeList = ref([]);
const treeRef = ref();
const childNode = ref([]);

const dialogSearch = ref({
  pageNum: 1,
  pageSize: 20,
  moduleId: props.moduleIdPop,
  conditionFields: []
});

// 监听 moduleIdPop 的变化
watch(
  () => props.moduleIdPop,
  (newVal) => {
    dialogSearch.value.moduleId = newVal;
    if (props.refreshExpressDialog) {
      getData(dialogSearch.value);
    }
  }
);

const total = ref(0);
const fullscreenLoading = ref(false);

// 获取表格数据
const getData = (params) => {
  if (params.taskId) {
    //选择了任务id 就要改变这个值
    params.allocation = true;
  } else if (!params.taskId) {
    params.allocation = '';
  }
  fullscreenLoading.value = true;
  getPlaceList(params).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      refreshMsg.value.zdList = res.data.list;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 监听弹窗显示
watch(
  () => props.refreshExpressDialog,
  (val) => {
    if (val) {
      dialogSearch.value.moduleId = props.moduleIdPop;
      getData(dialogSearch.value);
    }
  }
);

// 分页大小改变
const handleSizeChange = (val) => {
  dialogSearch.value.pageSize = val;
  dialogSearch.value.pageNum = 1;
  getData(dialogSearch.value);
};

// 分页页码改变
const handleCurrentChange = (val) => {
  dialogSearch.value.pageNum = val;
  getData(dialogSearch.value);
};

// ---方法---

const handleClose = () => {
  emit('closeRefreshExpress');
  //清空选中数据
  multipleSelection.value = [];
};

// 提交
const submitRef = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.error('请选择需要刷新的数据');
    return;
  }

  ElMessageBox({
    title: '提示',
    message: h('p', null, [
      h('span', null, `确定要刷新选中的 ${selectedIds.value.length} 条数据吗？`),
      h('i', { style: 'color: red' }, '(不刷新子要素)')
    ]),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true;
        instance.confirmButtonText = '执行中...';
        // 点击之后马上打开等待的弹框
        publicPrecentage.value = 0;
        publicPlanDialog.value = true;
        publicMsg.value = `已成功刷新表达式0条`;
        // 获取选中数据的 id 列表
        const selectedIds = multipleSelection.value.map((item) => item.id);
        try {
          const resultNum = await newSubsectionSubmit(selectedIds, props.moduleIdPop);
          if (resultNum.publicPrecentage >= 100) {
            done();
            instance.confirmButtonLoading = false;
            // 当数据完成后关闭弹框
            publicPlanDialog.value = false;
            if (errorMsgList.value.length != 0) {
              //表示有错误的
              // 将数据列表转换为带有换行符的字符串
              const textData = errorMsgList.value.map((item) => JSON.stringify(item)).join('\n');

              // 创建 Blob 对象
              const blob = new Blob([textData], { type: 'text/plain' });

              // 创建下载链接
              const url = URL.createObjectURL(blob);

              // 创建 a 标签
              const a = document.createElement('a');
              a.href = url;
              a.download = '刷新表达式错误日志.txt';

              // 模拟点击下载
              a.click();

              // 释放 URL 对象
              URL.revokeObjectURL(url);
              ElMessage.warning('有错误日志，日志已下载到本地，请前往查看！！！');
              setTimeout(() => {
                sessionStorage.setItem('qiehuan_company', false);
                location.reload();
              }, 1000);
            } else {
              if (resultNum.type == 2) {
                ElMessage.error('发送刷新请求失败！！！');
              } else {
                ElMessage.success('发送刷新请求成功！！！');
              }
              setTimeout(() => {
                sessionStorage.setItem('qiehuan_company', false);
                location.reload();
              }, 1000);
            }
          }
        } catch (error) {
          console.error('更新失败:', error);
          ElMessage.error(error);
          instance.confirmButtonLoading = false;
          done();
        }
      } else {
        instance.confirmButtonLoading = false;
        instance.confirmButtonText = '确定';
        done();
      }
    }
  }).then((action) => {});
};

/**
 * 新版刷新表达式 只需要传入需要刷新的id和模块id即可
 * @param ids 需要刷新的id列表
 * @param moduleId 模块id
 * @returns
 */
const newSubsectionSubmit = async (ids: (string | number)[], moduleId: number) => {
  return new Promise((resolve, reject) => {
    if (publicPrecentage.value < 100) {
      updateParcelFromMain(ids, moduleId).then((res) => {
        publicPrecentage.value = Number((ids.length / ids.length) * 100 > 100 ? 100 : ((ids.length / ids.length) * 100).toFixed(2));
        if (res.code == 200) {
          setTimeout(() => {
            uploadSuccessCount.value = uploadSuccessCount.value + ids.length;
            publicMsg.value = `已成功刷新表达式${uploadSuccessCount.value}条`;
            if (res.data && res.data.length != 0) {
              errorMsgList.value.push(...res.data);
            }
            resolve({ publicPrecentage: publicPrecentage.value, type: 1 });
          }, 500);
        } else {
          ElMessage.error(res.msg);
          publicPrecentage.value = 100;
          resolve({ publicPrecentage: publicPrecentage.value, type: 2 });
          publicPlanDialog.value = false;
        }
      });
    }
  });
};

/**
 * 迭代原始树表达式
 * @param list
 * @param mainId
 */
const getTreeExpress = (list, mainId) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              ruleId: v.id,
              groupId: k.id
            };
            treeExpressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getTreeExpress(v.list, mainId);
    }
  });
};

/**
 * 迭代获取树的所有表达式
 * @param list
 * @param mainId
 */
const getAllFiledExpressFromOne = (list, mainId) => {
  list.forEach((v) => {
    v.fieldGroupModels.forEach((k) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression && !q.attribution.isOnceRefeshExpression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              groupId: k.id
            };
            expressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getAllFiledExpressFromOne(v.list, mainId);
    }
  });
};

/**
 * 验证表达式
 * @param list
 */
const formulaOrder = async (list) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在验证表达式...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  const data = {
    moduleId: useProjectStore().proModuleId
  };
  fieldOrderList(data).then(async (res) => {
    loading.close();
    if (res.code == 200) {
      publicExpressList.value = [];
      let num = 0;
      let flgNum = 0; //看是否允许 刷新表达式
      const list = []; //需要忽略 子要素
      res.data.forEach((v) => {
        // [1,2,3,4,7]
        if ([1].includes(v.linkType)) {
          list.push(v);
        }
      });
      list.forEach((v) => {
        publicExpressList.value.push({
          fieldModel: v.fieldModel,
          groupName: v.groupName,
          id: num,
          linkId: v.linkId,
          ruleId: v.ruleId,
          groupId: v.groupId
        });
        num++;
        if (v.seq == 0) {
          //未排序
          flgNum++;
        }
      });
      if (flgNum > 0) {
        //不允许刷新表达式
        ElMessageBox.alert('检测到您的表达式未进行先后顺序排序，请联系系统管理员排序后再操作刷新表达式！！！', '提示', {
          confirmButtonText: '确定',
          callback: (action) => {}
        });
        loading.close();
        return;
      }
      // 点击之后马上打开等待的弹框
      publicPrecentage.value = 0;
      publicPlanDialog.value = true;
      publicMsg.value = `已成功刷新表达式0条`;
      // 得到了验证表达式，就需要真正组装数据了
      const chunksData: any = await handleChunkData(refreshMsg.value.zdList, 5);
      for (let index = 0; index < chunksData.chunks.length; index++) {
        try {
          const resultNum = await subsectionSubmit(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
          if (resultNum == 100) {
            // 当数据完成后关闭弹框
            publicPlanDialog.value = false;
            if (errorMsgList.value.length != 0) {
              //表示有错误的
              // 将数据列表转换为带有换行符的字符串
              const textData = errorMsgList.value.map((item) => JSON.stringify(item)).join('\n');

              // 创建 Blob 对象
              const blob = new Blob([textData], { type: 'text/plain' });

              // 创建下载链接
              const url = URL.createObjectURL(blob);

              // 创建 a 标签
              const a = document.createElement('a');
              a.href = url;
              a.download = '刷新表达式错误日志.txt';

              // 模拟点击下载
              a.click();

              // 释放 URL 对象
              URL.revokeObjectURL(url);
              const str = `有错误日志，日志已下载到本地，请前往查看！！！`;
              ElMessageBox.alert(str, `刷新成功`, {
                confirmButtonText: '确定',
                callback: (action) => {
                  // 在这添加是否切换公司的标识。
                  // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                  // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                  sessionStorage.setItem('qiehuan_company', 'false');
                  location.reload();
                }
              });
            } else {
              const str = `刷新成功`;
              ElMessageBox.alert(str, `刷新成功`, {
                confirmButtonText: '确定',
                callback: (action) => {
                  // 在这添加是否切换公司的标识。
                  // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                  // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                  sessionStorage.setItem('qiehuan_company', 'false');
                  location.reload();
                }
              });
            }
          }
        } catch (error) {
          console.error('上传失败:', error);
          ElMessage.error(error);
          // 处理错误，比如跳过当前子数组或中断整个上传过程
          continue;
        }
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const subsectionSubmit = async (list, num, count, chunkSize) => {
  const item_list = []; //二维数组，把选择的数据拆分为表达式列表
  list.forEach((v) => {
    const one_list = [];
    getEndOneExpressData([v], v.id, one_list);
    one_list.sort((a, b) => a.id - b.id);

    item_list.push(one_list);
  });
  const endList = [];
  item_list.forEach((v) => {
    endList.push(...v);
  });
  // 使用 reduce 方法进行分组，并将字段值作为键
  const groupList = endList.reduce((acc, item) => {
    // 使用字段值作为键，如果不存在则初始化为空数组
    acc[item.mainId] = (acc[item.mainId] || []).concat(item);
    return acc;
  }, {});
  return new Promise((resolve, reject) => {
    updateParcelNew(groupList).then((res) => {
      publicPrecentage.value = Number(+(((num * chunkSize) / count) * 100).toFixed(2) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2));
      if (res.code == 200) {
        setTimeout(() => {
          // this.uploadSuccessCount = this.uploadSuccessCount + list.length
          // this.publicMsg = `已成功刷新表达式${this.uploadSuccessCount}条`
          if (res.data && res.data.length != 0) {
            errorMsgList.value.push(...res.data);
          }
          resolve(publicPrecentage.value);
        }, 500);
      } else {
        ElMessage.error(res.msg);
        reject(res.msg);
        if (publicPrecentage.value == 100) {
          publicPlanDialog.value = false;
        }
      }
    });
  });
};

/**
 * 最终通过数据组装表达式集合
 * @param item
 * @param mainId
 * @param list
 */
const getEndOneExpressData = (item, mainId, list) => {
  item.forEach((v) => {
    publicExpressList.value.forEach((k) => {
      if (v.geomArcgis && k.ruleId == v.ruleId) {
        //当前数据的ruleid和公共表达式列表相等 需要先判断有图形的才加入
        const obj = JSON.parse(JSON.stringify(k));
        obj.parcelId = v.id;
        obj.mainId = mainId;
        list.push(obj);
      }
    });
    if (v.list && v.list.length != 0) {
      getEndOneExpressData(v.list, mainId, list);
    }
  });
};
/**
 * 关闭公共数据处理的弹框
 */
const handlePublickClose = () => {
  publicPrecentage.value = 0;
  publicMsg.value = '';
};

/**
 * 公共方法抽取数据 分段截取数据
 * @param list
 * @param num
 */
const handleChunkData = async (list, num) => {
  const count = list.length;
  const chunkSize = num || 10;
  const chunks = [];
  // 拆分数组
  for (let i = 0; i < list.length; i += chunkSize) {
    // 这里创建二维数组内容，分 10 个 截成一个二维数组
    chunks.push(list.slice(i, i + chunkSize));
  }
  const resultItem = {
    count: count,
    chunkSize: chunkSize,
    chunks: chunks
  };
  return new Promise((resolve) => {
    resolve(resultItem);
  });
};

// 处理表格全选
const handleSeletionAll = (selection: any[]) => {
  multipleSelection.value = selection;
  // 更新selectedIds
  const currentPageIds = selection.map((item) => item.id);
  // 移除当前页所有ID
  selectedIds.value = selectedIds.value.filter((id) => !refreshMsg.value.zdList.some((item) => item.id === id));
  // 添加新选中的ID
  selectedIds.value.push(...currentPageIds);
};

// 处理表格行选择
const handleSelectTable = (selection: any[], row: any) => {
  multipleSelection.value = selection;
  const rowId = row.id;
  const index = selectedIds.value.indexOf(rowId);

  if (selection.some((item) => item.id === rowId)) {
    // 如果是选中，且不在selectedIds中，则添加
    if (index === -1) {
      selectedIds.value.push(rowId);
    }
  } else {
    // 如果是取消选中，且在selectedIds中，则移除
    if (index > -1) {
      selectedIds.value.splice(index, 1);
    }
  }
};

// 清空选择
const clearSelection = () => {
  selectedIds.value = [];
  multipleSelection.value = [];
};

// 监听页面变化时，更新表格选中状态
watch(
  () => refreshMsg.value.zdList,
  () => {
    // 根据selectedIds设置表格选中状态
    multipleSelection.value = refreshMsg.value.zdList.filter((item) => selectedIds.value.includes(item.id));
  },
  { deep: true }
);

const handleRowClick = (row: any, column: any, event: any) => {
  multipleTableRef.value?.toggleRowSelection(row);
};

const formatDateType = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

const handleNodeClick = (data: any, flg: boolean) => {
  if (flg) {
    //选中
    const isHas = checkTreeList.value.indexOf(data.id) == -1;
    if (isHas) {
      //  不存在才推送
      checkTreeList.value.push(data.id);
      childNode.value.push({ id: data.id, ruleId: data.ruleId });
    }

    if (data.list && data.list.length > 0) {
      //  遍历选中所有节点
      data.list.forEach((item: any) => {
        handleNodeClick(item, true);
      });
    }
    treeRef.value?.setCheckedKeys(checkTreeList.value);
  } else {
    // 取消选中
    const index = childNode.value.findIndex((item) => item.id === data.id);
    if (index > -1) {
      childNode.value.splice(index, 1);
    }
    data.delFlag = 0;
    const treeIndex = checkTreeList.value.indexOf(data.id);
    if (treeIndex > -1) {
      checkTreeList.value.splice(treeIndex, 1);
    }
    if (data.list && data.list.length > 0) {
      data.list.forEach((item: any) => {
        handleNodeClick(item, false);
      });
    }
    treeRef.value?.setCheckedKeys(checkTreeList.value);
  }
};

// 在变量定义部分添加
const shaixuanDialog = ref(false); // 筛选弹窗
const taskList = ref([]); // 任务列表

// 添加方法
const searchBtn = async () => {
  shaixuanDialog.value = true;
  await getSearchTaskList();
};

const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

const submitSearch = (isMapping?: number) => {
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  getData(dialogSearch.value);
  shaixuanDialog.value = false;
};

const clearUser = (type: number) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 20,
    parcelName: '',
    moduleId: props.moduleIdPop,
    conditionFields: [], //字段查询
    express: false //是否查询表达式异常的数据
  };
  getData(dialogSearch.value);
  shaixuanDialog.value = false;
};

const editCondition = (type: number, idx?: number) => {
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else if (idx !== undefined) {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: dialogSearch.value.moduleId
  };
  if (props.moduleIdPop) {
    params.moduleId = props.moduleIdPop;
  }
  const res = await getSearchTask(params);
  if (res.code == 200) {
    taskList.value = res.data.list;
  } else {
    ElMessage.error(res.msg);
  }
};

// 在关闭弹窗时清空选择
watch(
  () => props.refreshExpressDialog,
  (val) => {
    if (!val) {
      multipleSelection.value = [];
    }
  }
);

// 添加表格配置
const tableWidth = ref<number>(800);

// 定义列配置
interface TableRowData {
  id: string | number;
  parcelName: string;
  createTime: string;
  [key: string]: any;
}

const columns = ref<AnyColumn[]>([
  {
    key: 'selection',
    dataKey: 'selection',
    title: '',
    width: 55,
    headerCellRenderer: () => {
      return h(ElCheckbox, {
        modelValue: isAllSelected.value,
        onChange: (val: boolean) => handleSelectAll(val)
      });
    },
    cellRenderer: ({ rowData }: { rowData: TableRowData }) => {
      return h(ElCheckbox, {
        modelValue: multipleSelection.value.includes(rowData),
        onChange: (val: boolean) => {
          const selection = val ? [...multipleSelection.value, rowData] : multipleSelection.value.filter((item) => item.id !== rowData.id);
          handleSelectTable(selection, rowData);
        }
      });
    }
  },
  {
    key: 'index',
    dataKey: 'index',
    title: '序号',
    width: 70,
    cellRenderer: ({ rowIndex }: { rowIndex: number }) => {
      return rowIndex + 1;
    }
  },
  {
    key: 'parcelName',
    dataKey: 'parcelName',
    title: '数据名称',
    width: 300
  },
  {
    key: 'createTime',
    dataKey: 'createTime',
    title: '创建时间',
    width: 200,
    cellRenderer: ({ rowData }: { rowData: TableRowData }) => {
      return formatDateType(rowData.createTime);
    }
  }
]);

// 获取行样式
const getRowClass: RowClassNameGetter<TableRowData> = ({ rowData }) => {
  return multipleSelection.value.includes(rowData) ? 'selected-row' : '';
};

// 是否全选的计算属性
const isAllSelected = computed(() => {
  return refreshMsg.value.zdList.length > 0 && multipleSelection.value.length === refreshMsg.value.zdList.length;
});

// 处理全选/取消全选
const handleSelectAll = (val: boolean) => {
  if (val) {
    // 全选
    multipleSelection.value = [...refreshMsg.value.zdList];
  } else {
    // 取消全选
    multipleSelection.value = [];
  }
  handleSeletionAll(multipleSelection.value);
};
</script>
<style lang="scss" scoped>
.refresh-express-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 60vh;

  .dialog-content {
    flex: 1;
    overflow: auto;
    padding: 20px;

    .table-content {
      margin-bottom: 0;
    }
  }

  .dialog-footer {
    padding: 10px 20px;
    border-top: 1px solid #dcdfe6;
    background-color: #fff;
    display: flex;
    justify-content: flex-end;
  }
}
.dialog-search {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .selected-count {
    font-size: 14px;
    color: #606266;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
.dialog-content {
  height: 485px;
  width: 100%;
  overflow: auto;
  /*滚动条样式*/
  &::-webkit-scrollbar {
    width: 10px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.table-container {
  height: 485px;
  width: 100%;
}

.selected-row {
  background-color: var(--el-table-row-hover-bg-color);
}
</style>
