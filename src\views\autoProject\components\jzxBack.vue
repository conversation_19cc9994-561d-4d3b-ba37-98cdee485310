<!-- 界址线找回 -->
<template>
  <div>
    <el-dialog
      title="界址线找回"
      v-model="jzxBackDialogCopy"
      width="30%"
      :close-on-click-modal="false"
      @opened="initDataBack"
      :before-close="handleClose"
    >
      <div class="notice">
        <span
          >注意事项：该功能是针对用户使用shp更新数据的时候同时更新了子要素的情况（界址线、界址点）,但您并不想更新，想把之前的子要素内容找回来！！！选择的属性组必须是线类型子要素，并且只能选择有默认值的字段！！！</span
        >
      </div>
      <el-form :model="form" ref="formRef" :rules="formRule" label-width="100px" label-position="top">
        <el-form-item label="数据选择" prop="zdList">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.zdListNames" @click.native="chooseData" readonly> </el-input>
        </el-form-item>
        <el-form-item label="属性组" prop="linkId">
          <el-select v-model="form.linkId" placeholder="请选择" style="width: 100%" clearable @change="chooseAttr">
            <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段" prop="fieldName">
          <el-select v-model="form.fieldName" placeholder="请选择" style="width: 100%" clearable>
            <el-option v-for="item in filedList" :key="item.fieldName" :label="item.fieldCn" :disabled="item.disabled" :value="item.fieldName">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="form.zdList"
      :isManager="false"
      :ifTree="false"
      :isKJ="false"
      :ruleTree="ruleTree"
      :ruleIds="[]"
    ></searchData>
  </div>
</template>

<script setup lang="ts">
import searchData from './searchData/index.vue';
import { jzxRetrieval } from '@/api/project';

// --- 定义props ---
interface Props {
  // 打开弹框
  jzxBackDialog: boolean;
  ruleTree?: any;
}

const props = withDefaults(defineProps<Props>(), {
  jzxBackDialog: false
});

const jzxBackDialogCopy = computed(() => props.jzxBackDialog);
// ---定义变量 ---
const form = ref({
  zdList: [], //需要回退的数据
  linkId: '', //属性组linkid
  fieldName: '', //字段名
  zdListNames: []
});
const formRule = ref({
  zdList: [{ required: true, message: '请选择数据', trigger: 'change' }],
  linkId: [{ required: true, message: '请选择属性组', trigger: 'change' }],
  fieldName: [{ required: true, message: '请选择字段', trigger: 'change' }]
});
const ifTree = ref(false);
const searchDialog = ref(false);
const attrList = ref([]); //所有属性组
const filedList = ref([]); //所有字段
const formRef = ref();

//  ---定义emit---
const emit = defineEmits<{
  (e: 'handleCloseJzxBack'): void;
}>();
// --- 定义方法 ---
const initDataBack = () => {
  attrList.value = [];
  disposeAttr(JSON.parse(JSON.stringify(props.ruleTree)));
};

// 组装所有属性组
const disposeAttr = (list: any) => {
  list.forEach((v: any) => {
    v.fieldGroupModelList.forEach((k: any) => {
      k.label = `${k.typeName}(${v.typeName})`;
      if (k.linkType == 5) {
        //只有子要素的界址线才允许选择
        attrList.value.push(k);
      }
    });
    if (v.list.length != 0) {
      disposeAttr(v.list);
    }
  });
};

const handleClose = () => {
  emit('handleCloseJzxBack');
};
/**
 * 选择数据
 */
const chooseData = () => {
  ifTree.value = false;
  searchDialog.value = true;
};

/**
 * 关闭筛选数据弹窗
 */
const closeSearchDialog = () => {
  form.zdList = [];
  form.zdListNames = '';
  searchDialog.value = false;
};

/**
 * 得到筛选要素数据
 */
const getChooseData = (list: any) => {
  form.zdList = list;
  const names = [];
  list.forEach((v: any) => {
    names.push(v.parcelName);
  });
  form.value.zdListNames = names.join(',');
  searchDialog.value = false;
};

/**
 * 选择属性组
 */
const chooseAttr = (e: any) => {
  //必须是有默认值并有选项的数据才可以选择
  filedList.value = [];
  for (let i = 0; i < attrList.value.length; i++) {
    if (attrList.value[i].linkId == e) {
      attrList.value[i].fieldModelList.forEach((v: any) => {
        if (v.attribution.defaultValue && v.fieldName == 'JZXLB') {
          //只有有默认值的字段才可以选择 只找界址线类别 后台是写死的
          filedList.value.push(v);
        }
      });
      break;
    }
  }
  if (filedList.value.length != 0) {
    filedList.value.forEach((v: any) => {
      if (['idCardScan', 'xttable'].includes(v.valueMethod) || (v.attribution && v.attribution.expression)) {
        //只有普通属性组才可以选择
        v.disabled = true;
      }
    });
  }
};
/**
 * 提交数据
 */
const submit = () => {
  ElMessageBox.confirm('确定要执行界址线找回操作吗？该操作会改变界址线的数据，请谨慎操作！！！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formRef.value.validate((valid) => {
        if (valid) {
          const parmas = {
            ids: [],
            linkId: form.value.linkId,
            fieldName: form.value.fieldName
          };
          const ids = [];
          form.value.zdList.forEach((v) => {
            ids.push(v.id);
          });
          parmas.ids = ids;
          jzxRetrieval(parmas).then((res) => {
            if (res.code == 200) {
              ElMessageBox.alert('找回成功', '提示', {
                confirmButtonText: '确定',
                callback: (action) => {
                  // 在这添加是否切换公司的标识。
                  // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                  // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                  sessionStorage.setItem('qiehuan_company', false);
                  location.reload();
                }
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.notice {
  color: red;
}
</style>
