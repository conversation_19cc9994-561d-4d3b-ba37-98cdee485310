<template>
  <div class="">
    <el-dialog title="表格扩展配置" v-model="expendDialog" width="90%" :close-on-click-modal="false" :before-close="handleCloseExpend">
      <div class="form-group" :style="{ height: tableHeight }">
        <!-- <div class="form-h">商品规格</div> -->
        <div class="form-item" v-for="(attr, index) in attrs" :key="index">
          <div class="form-title">
            <input type="text" name="" value="" v-model="attr.pName" placeholder="规格名" />
            <span class="delete" @click="toDelete(index)">×</span>
          </div>
          <ul class="form-list">
            <li v-for="(item, index2) in attr.spec" :key="index2">
              <el-tag style="margin-right: 5px">{{ item.cName }}</el-tag>
              <!-- <input class="spec-item" @change="verdictChange=false" type="text" name="" value="" v-model="item.cName"> -->
            </li>
          </ul>
          <!-- <span class="add-sku-key" @click="addSkuKey(attr)">+添加规格值</span> -->
          <span style="margin-right: 10px">选择字段</span>
          <el-select class="end" v-model="attr.fieldList" multiple placeholder="请选择字段" @change="(e) => changeSel(e, attr)" style="width: 90%">
            <el-option v-for="item in activeData.children" :key="item.vModel" :label="item.label" :value="item.vModel"> </el-option>
          </el-select>
        </div>
        <div class="form-btn-group"><button class="btn" type="button" name="" @click="addItem">添加规格项目</button></div>
        <div class="form-table" v-show="tableData">
          <!-- <div class="stock-title">商品库存</div> -->
          <table class="table-sku" border="1px solid #ccc">
            <thead>
              <tr>
                <td v-for="(list, index) in tableData" :key="index">{{ list['pName'] }}</td>
                <td>价格</td>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in rows" :key="index">
                <td v-for="(item, index2) in tableData" v-show="!((row - 1) % item['rowspan'])" :rowspan="item['rowspan']" :key="index2">
                  {{ getName(row, item) }}
                </td>
                <td><input type="number" v-model="tableList[row - 1]['price']" /></td>
              </tr>
            </tbody>
          </table>
          <!-- <div class="form-btn-group"><button class="btn" type="button" name="" @click="toConfirm">确认</button></div> -->
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseExpend">取 消</el-button>
          <el-button type="primary" @click="handleSubmitExpend">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, defineProps, defineEmits } from 'vue';

// 定义 props
const props = defineProps<{
  expendDialogProp: boolean;
  activeData: {
    children: Array<{
      vModel: string;
      label: string;
      tagIcon?: string;
      options?: Array<{
        label: string;
      }>;
    }>;
  };
}>();

// 定义 emits
const emit = defineEmits<{
  (event: 'closeExpend'): void;
  (event: 'submitExpend', tableList: any[], tableData: any[], rows: number, attrs: any[]): void;
}>();

// 定义响应式数据
const attrs = ref<any[]>([]);
const verdictChange = ref(false);
const tableHeight = ref(`${window.innerHeight - 200}px`);
const tableList = ref<any[]>([]);
const expendDialog = ref(props.expendDialogProp);

// 定义过滤器
const getName = (obj: any, index: number) => {
  if (obj) {
    const r = Math.floor((index - 1) / obj['rowspan']);
    const l = obj['specLen'] || 1;
    const key = r % l;
    return obj['spec'] && obj['spec'][key] && obj['spec'][key]['cName'];
  }
};

// 定义计算属性
const tableData = computed(() => {
  const attrsValue = attrs.value;
  const len = attrsValue.length;
  if (len === 0) {
    return;
  }
  const tData: any[] = [];
  // 初始化 tableData
  for (let i = 0; i < len; i++) {
    const row: any = {};
    row['pName'] = attrsValue[i]['pName'];
    row['spec'] = [];
    row['price'] = {};
    const len2 = attrsValue[i]['spec'].length;
    let specLen = 0;
    for (let j = 0; j < len2; j++) {
      const spe: any = {};
      const cName = attrsValue[i]['spec'][j]['cName'];
      if (!cName) {
        continue;
      }
      ++specLen;
      spe['cName'] = cName;
      row['spec'].push(spe);
    }
    row['specLen'] = specLen;
    tData.push(row);
  }
  // 获取 rowspan
  for (let k = 0, len3 = tData.length; k < len3; k++) {
    let rowspan = 1;
    for (let k1 = k + 1; k1 < len3; k1++) {
      const kSpecLen = tData[k1]['specLen'] || 1;
      rowspan *= kSpecLen;
    }
    tData[k].rowspan = rowspan;
  }
  return tData;
});

const rows = computed(() => {
  if (!tableData.value) {
    return;
  }
  let rows = 1;
  const tableDataValue = tableData.value;
  const len = tableDataValue.length;
  for (let i = 0; i < len; i++) {
    const specLen = tableDataValue[i]['specLen'] || 1;
    rows *= specLen;
  }
  // 每条 rowspan 都为 1 情况
  if (rows === 1) {
    return tableDataValue[0]['spec'].length;
  }
  return rows;
});

// 监听 attrs 变化
watch(
  attrs,
  (val) => {
    const rowsValue = rows.value;
    if (!rowsValue) return;
    const tList: any[] = [];
    const srcData = tableData.value;
    if (!srcData) return;
    for (let i = 0; i < rowsValue; i++) {
      const listItem: any = {};
      // 构建动态项
      for (let j = 0; j < srcData.length; j++) {
        const key = srcData[j]['pName'];
        const rowspan = srcData[j]['rowspan'];
        const len = srcData[j]['specLen'];
        if (!len) {
          continue;
        }
        const spec = srcData[j]['spec'];
        const index = Math.floor(i / rowspan) % len;
        listItem[key] = spec[index]['cName'];
      }
      // 构建固定项(price,number)
      if (verdictChange.value) {
        listItem['price'] = 0;
      }
      tList.push(listItem);
    }
    tableList.value = tList;
  },
  { deep: true }
);

// 定义方法
const addItem = () => {
  if (attrs.value) {
    for (let i = 0; i < attrs.value.length; i++) {
      if (attrs.value[i].pName === '') {
        alert('请填写规格名');
        return;
      }
      if (attrs.value[i].spec.length === 0) {
        alert('有未添加规格值');
        return;
      }
      for (let j = 0; j < attrs.value[i].spec.length; j++) {
        if (attrs.value[i].spec[j].cName === '') {
          alert('有未添加规格值');
          return;
        }
      }
    }
    const obj = {
      pName: '',
      rowspan: 1,
      spec: [],
      fieldList: []
    };
    attrs.value.push(obj);
    verdictChange.value = true;
  }
};

const toDelete = (index: number) => {
  attrs.value.splice(index, 1);
};

const toConfirm = () => {};

const addSkuKey = (obj: any) => {
  if (obj.pName === '') {
    alert('请填写规格名');
    return;
  }
  const item = { cName: '' };
  obj.spec.push(item);
  verdictChange.value = true;
};

const handleCloseExpend = () => {
  emit('closeExpend');
};

const changeSel = (el: any[], item: any) => {
  const list: any[] = [];
  el.forEach((v) => {
    for (let i = 0; i < props.activeData.children.length; i++) {
      if (props.activeData.children[i].vModel === v) {
        if (props.activeData.children[i].tagIcon === 'select') {
          props.activeData.children[i].options?.forEach((q) => {
            list.push({
              cName: q.label
            });
          });
        } else {
          list.push({
            cName: props.activeData.children[i].label
          });
        }
        break;
      }
    }
  });
  item.spec = list;
};

const handleSubmitExpend = () => {
  emit('submitExpend', tableList.value, tableData.value, rows.value, attrs.value);
};

const init = (obj: any) => {
  attrs.value = obj.attrExpend.attrs;
  tableList.value = obj.attrExpend.tableList;
};
</script>

<style lang="scss" scope>
/**reset*/
button,
input {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  outline: none;
}
.btn {
  padding: 4px 12px;
  margin-bottom: 0;
  font-size: 14px;
  color: #333;
  vertical-align: middle;
  cursor: pointer;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.btn.active,
.btn:active,
.btn:focus,
.btn:hover {
  text-decoration: none;
  color: #333;
  background-color: #fcfcfc;
  border-color: #ccc;
}
/*table*/
table {
  border: 0;
}
table.table-sku {
  width: 100%;
  background-color: #fff;
  text-align: left;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
table.table-sku td {
  border: 1px solid #e5e5e5;
  padding: 8px;
}
table.table-sku td input {
  padding: 10px;
  border: 1px solid #ccc;
}
/**/
.form-title {
  background: #f8f8f8;
  padding: 10px;
  position: relative;
}
.form-title .label {
  color: #999;
}
.form-title .delete {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border: 1px solid #ccc;
  border-radius: 50%;
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -10px;
  text-align: center;
  color: #fff;
  background: #ccc;
  cursor: pointer;
}
.form-title input {
  background: #fff;
  border: 1px solid #ccc;
  padding: 10px;
}
.form-list {
  padding: 10px;
  margin-top: 0;
}
.form-list li {
  display: inline-block;
  margin-top: 10px;
}
.spec-item {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.form-list,
.form-title {
  text-align: left;
}
.form-list input {
  background: #fff;
  margin-right: 10px;
  border: 1px solid #ccc;
  padding: 10px;
}
.form-group {
  //   border: 1px solid #ccc;
  padding: 10px;
}
.form-table {
  margin-top: 10px;
}
.form-btn-group {
  margin-bottom: 10px;
  background: #f8f8f8;
  padding: 10px;
  margin-top: 10px;
}
.stock-title,
.form-h {
  height: 40px;
  line-height: 40px;
}
.add-sku-key {
  cursor: pointer;
}
</style>
