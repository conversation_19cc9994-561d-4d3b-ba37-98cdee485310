<template>
  <el-image
    ref="imgRef"
    fit="cover"
    :src="img"
    :style="{ width: props.width, height: props.height, borderRadius: props.radius, background: '#fff' }"
    :preview-src-list="bigImg"
  >
    <template #error>
      <div class="image-slot">
        <img src="@/assets/images/img-error.png" alt="" :style="{ width: props.width, height: props.height }" />
      </div>
    </template>
  </el-image>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, defineProps } from 'vue';
import { getToken } from '@/utils/auth';
import Axios from 'axios';
// 定义props
const props = defineProps({
  authSrc: {
    type: String,
    required: false,
    default: ''
  },
  width: {
    type: String,
    default: '300px'
  },
  height: {
    type: String,
    default: '200px'
  },
  radius: {
    type: String,
    default: '0'
  }
});

// 响应式数据
const imgRef = ref(null);
const bigImg = ref<string[]>([]);
const img = ref('');

// 计算属性

// 方法
const getImg = async () => {
  if (!props.authSrc) return;

  try {
    const res = await Axios({
      method: 'get',
      url: props.authSrc,
      headers: {
        'Authorization': 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    });

    const blob = res.data;
    const reader = new FileReader();
    reader.readAsDataURL(blob); // 转换为base64
    reader.onload = function () {
      img.value = reader.result as string;
    };
  } catch (error) {
    console.error('获取图片失败:', error);
  }
};

// 监听
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  },
  { immediate: true, deep: true }
);

// 生命周期钩子
onMounted(() => {
  getImg();
});
</script>

<style scoped>
.img-item {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 300px;
  height: 200px;
}
.samll-img {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 50px;
  height: 40px;
}
.el-image {
  margin: 5px 0px 5px 5px;
}
:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd dashed 1px;
  background: rgba(0, 0, 0, 0.1);
}
</style>
