<!-- 论坛首页的列表 -->
<template>
  <container-card>
    <select-tab :tab-list="tabList" @clickValue="searchForumList"></select-tab>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="plus" @click="handleAddForum">发表新帖</el-button>
      </el-col>
      <right-toolbar :search="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <div class="table-contianer">
      <el-table :data="forumList" style="width: 100%; margin-top: 12px">
        <el-table-column label="帖子" prop="templateName">
          <template #default="scope">
            <span v-if="scope.row.titleType == 1" style="color: var(--current-color)">[系统建议]</span>
            <span v-if="scope.row.titleType == 2" style="color: #ff3d57">[BUG反馈]</span>
            <span v-if="scope.row.titleType == 3" style="color: #09b66d">[交流互动]</span>
            <span v-if="scope.row.titleType == 4" style="color: #fb00ff">[行业资讯]</span>
            <span style="padding-left: 8px" @click="handleViewForumDetial(scope.row)">
              {{ spanRule(scope.row.titleInfo, 50) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="作者" prop="createBy" width="120"></el-table-column>
        <el-table-column label="时间" width="150">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="查看" width="120">
          <template #default>
            <span
              ><el-icon><View /></el-icon
            ></span>
            <span>1000</span>
          </template>
        </el-table-column>
        <el-table-column label="回复" width="120">
          <template #default>
            <span
              ><el-icon><ChatDotSquare /></el-icon
            ></span>
            <span>500</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 我要发言--发表主题评论 -->
    <el-dialog title="发表新帖" v-model="forumDialogFormVisible" @closed="handleCloseForumForm" :close-on-click-modal="false">
      <el-form :model="forumForm" ref="forumFormRef" :rules="forumRules">
        <el-form-item label="主题类别" prop="titleType">
          <el-select v-model="forumForm.titleType" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in forumTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主题题目" prop="titleInfo">
          <el-input v-model="forumForm.titleInfo" autocomplete="off" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="主题内容" prop="contents">
          <el-input
            type="textarea"
            v-model="forumForm.contents"
            autocomplete="off"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="1000"
          ></el-input>
        </el-form-item>
        <div class="text-label">主题图片/视频</div>
        <div class="upload-card">
          <el-upload
            list-type="picture-card"
            :on-change="handleChangeUploadImg"
            :on-remove="handleRemoveUploadImg"
            :action="`${base}/system/option/multi/upload`"
            :auto-upload="false"
            :file-list="forumImgFileList"
            accept=".jpg,.png,.mp4"
            :headers="headers"
          >
            <svg-icon icon-class="tjtp" style="width: 32px; height: 32px" />
          </el-upload>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="forumDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitForum">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import type { UploadFile, UploadFiles, UploadUserFile } from 'element-plus';
import selectTab from '../../components/selectTab/selectTab.vue';
import { addForum, getForumCheckList, uploadImgae } from '@/api/forum';
import { getToken } from '@/utils/auth';

// 定义类型
interface TabItem {
  label: string;
  value: string | number;
  isClick: boolean;
}

interface ForumItem {
  titleId: string | number;
  titleType: number;
  titleInfo: string;
  createBy: string;
  createTime: string;
  contents?: string;
  [key: string]: any;
}

interface ForumForm {
  picUrl: string;
  contents: string;
  titleInfo: string;
  titleType: string | number;
}

interface ForumTypeOption {
  label: string;
  value: number;
}

const router = useRouter();
const forumFormRef = ref<FormInstance>();

// 切换选项
const tabList = ref<TabItem[]>([
  { label: '全部', value: '', isClick: true },
  { label: '行业资讯', value: 4, isClick: false },
  { label: '系统建议', value: 1, isClick: false },
  { label: 'BUG反馈', value: 2, isClick: false },
  { label: '交流互动', value: 3, isClick: false }
]);

const titleType = ref<string | number>('');
const forumList = ref<ForumItem[]>([]);
const forumDialogFormVisible = ref<boolean>(false);
const showSearch = ref<boolean>(false);

// 分页参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
});

// 添加的主题对象
const forumForm = reactive<ForumForm>({
  picUrl: '',
  contents: '',
  titleInfo: '',
  titleType: ''
});

// 论坛校验
const forumRules = reactive({
  titleInfo: [{ required: true, message: '请填写主题题目', trigger: 'blur' }],
  titleType: [{ required: true, message: '请选择主题类型', trigger: 'change' }],
  contents: [{ required: true, message: '请填写主题内容', trigger: 'blur' }]
});

// 主题的类型选择
const forumTypeOptions = ref<ForumTypeOption[]>([
  { label: '系统建议', value: 1 },
  { label: 'bug反馈', value: 2 },
  { label: '交流互动', value: 3 },
  { label: '行业资讯', value: 4 }
]);

// 上传的图片数组
const forumImgFileList = ref<UploadUserFile[]>([]);
const base = ref<string>(import.meta.env.VUE_APP_BASE_API || '');

const headers = computed(() => {
  return {
    'Authorization': 'Bearer ' + getToken(),
    'Access-Control-Allow-Origin': '*'
  };
});

// 生命周期
onMounted(() => {
  getList();
});

// 获取论坛列表
const getList = () => {
  const params = {
    seq: 0,
    checkStatus: 1,
    titleType: titleType.value,
    pageSize: queryParams.pageSize,
    pageNum: queryParams.pageNum
  };
  getForumCheckList(params).then((res) => {
    if (res.code === 200) {
      forumList.value = res.data.rows;
      queryParams.total = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * tab 筛选
 * @param val 筛选值
 */
const searchForumList = (val: string | number) => {
  titleType.value = val;
  getList();
};

// 发表新帖
const handleAddForum = () => {
  forumDialogFormVisible.value = true;
};

// 提交主题
const handleSubmitForum = () => {
  forumFormRef.value?.validate((valid) => {
    if (valid) {
      handleUploadFileServe().then((resUrl) => {
        const params = {
          picUrl: resUrl,
          contents: forumForm.contents,
          titleInfo: forumForm.titleInfo,
          seq: 0,
          titleType: forumForm.titleType
        };
        addForum(params).then((res) => {
          if (res.code === 200) {
            forumDialogFormVisible.value = false;
            ElMessageBox.confirm('内容待管理员审核', '发表成功', {
              confirmButtonText: '确定',
              type: 'warning'
            })
              .then(() => {})
              .catch(() => {});
          } else {
            ElMessage.success(res.msg);
          }
        });
      });
    }
  });
};

/**
 * 上传图片
 * @param file 文件
 * @param fileList 文件列表
 */
const handleChangeUploadImg = (file: UploadFile, fileList: UploadFiles) => {
  if (!file.raw) return false;

  const whiteList = ['image/png', 'image/jpeg', 'video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb'];
  const isFlag = whiteList.includes(file.raw.type);
  const isSize = file.size ? file.size / 1024 / 1024 : 0;

  if (isSize > 10) {
    const index = fileList.findIndex((e) => e.size && e.size / 1024 / 1024 === isSize);
    if (index !== -1) {
      fileList.splice(index, 1);
    }
    ElMessage.warning('上传的文件不能超过10MB');
    return false;
  }

  if (!isFlag) {
    ElMessage.error('只能上传图片和视频文件');
    return false;
  }

  if (file.raw.type === 'video/mp4') {
    const video = document.createElement('video');
    video.src = file.url || '';
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    video.crossOrigin = 'anonymous';
    video.currentTime = 1;

    video.oncanplay = () => {
      canvas.width = video.clientWidth ? video.clientWidth : 320;
      canvas.height = video.clientHeight ? video.clientHeight : 320;
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const _videoFirstimgsrc = canvas.toDataURL('image/png');
        file.url = _videoFirstimgsrc;
      }
      video.remove();
      canvas.remove();
    };

    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration;
      if (Math.floor(duration) > 30) {
        ElMessage.error('上传的视频不能超过30S');
        return false;
      }
    });
  }

  if (isSize < 5 && isFlag) {
    forumImgFileList.value = fileList;
  }

  return isFlag && isSize < 5;
};

/**
 * 删除图片
 * @param file 文件
 * @param fileList 文件列表
 */
const handleRemoveUploadImg = (file: UploadFile, fileList: UploadFiles) => {
  const index = forumImgFileList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    forumImgFileList.value.splice(index, 1);
  }
};

// 上传文件
const handleUploadFileServe = () => {
  const formData = new FormData();
  forumImgFileList.value.forEach((file) => {
    if (file.raw) {
      formData.append('files', file.raw);
    }
  });

  return new Promise<string>((resolve, reject) => {
    uploadImgae(formData).then((res) => {
      if (res.code === 200) {
        forumForm.picUrl = '';
        res.data.forEach((item: { path: string }) => {
          forumForm.picUrl += `${item.path};`;
        });
        resolve(forumForm.picUrl);
      } else {
        ElMessage.error(res.msg);
        reject();
      }
    });
  });
};

/**
 * 关闭添加论坛对话框时的内容 清楚数据
 */
const handleCloseForumForm = () => {
  forumDialogFormVisible.value = false;
  Object.assign(forumForm, {
    picUrl: '',
    contents: '',
    titleInfo: '',
    titleType: ''
  });
  forumImgFileList.value = [];
};

/**
 * 点击主题查看详细
 * @param row 主题数据
 */
const handleViewForumDetial = (row: ForumItem) => {
  router.push({ path: '/forum/detial', query: { titleId: row.titleId, titleType: row.titleType } });
};

/**
 * 自定义过滤器函数
 * @param value 字符串
 * @param length 长度
 * @returns 截取后的字符串
 */
const spanRule = (value: string, length: number) => {
  if (!value) return '';
  if (value.length > length) {
    return value.substring(0, length) + '...';
  }
  return value;
};
</script>

<style lang="scss" scoped></style>
