<template>
  <div class="fc-org-select">
    <div class="tags">
      <el-button v-if="buttonType === 'button'" size="small" type="primary" @click="show = true" style="margin-bottom: 6px">
        <el-icon><Plus /></el-icon>
        添加{{ title }}
      </el-button>
      <div class="input-box" :class="{ 'as-input': buttonType === 'input', 'no-change': noChange }" @click="changeDialog">
        <el-tag v-for="item in selectedData" :key="item.key" v-bind="tagConfig" class="org-tag" @close="removeCheckedItem(item)">
          {{ item.label }}
        </el-tag>
      </div>
    </div>
    <OrgTransfer
      ref="transfer"
      :value="innerValue"
      :title="title"
      :searchable="searchable"
      :max-num="maxNum"
      :type="type"
      v-model:show="show"
      @confirm="onConfirm"
    ></OrgTransfer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, defineExpose } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import type { TagProps } from 'element-plus';
import OrgTransfer from '../OrgTransfer/index.vue';
interface SelectedItem {
  key: string;
  label: string;
}

interface Props {
  modelValue: any[];
  type?: string;
  title?: string;
  buttonType?: 'button' | 'input';
  searchable?: boolean;
  maxNum?: number;
  tagConfig?: Partial<TagProps>;
  noChange?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  type: 'dep',
  title: '组织机构',
  buttonType: 'input',
  searchable: true,
  maxNum: 99,
  tagConfig: () => ({
    type: 'info',
    closable: true,
    disableTransitions: false,
    hit: false,
    color: undefined,
    size: 'small',
    effect: 'light'
  }),
  noChange: false
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: any[]): void;
}>();

const show = ref(false);
const innerValue = ref<any[]>([]);
const selectedData = ref<SelectedItem[]>([]);
const transfer = ref();
// const treeRef = ref();

const selectedLabels = computed(() => {
  return selectedData.value.map((item) => item.label).join(',');
});

const reloadCmpData = () => {
  innerValue.value = JSON.parse(JSON.stringify(props.modelValue));
  nextTick(() => {
    initSelectedData();
  });
};

const initSelectedData = () => {
  const arr: SelectedItem[] = [];
  for (const item of innerValue.value) {
    arr.push({
      key: getKey(item, props.type),
      label: getLabel(item, props.type)
    });
    // arr.push({
    //   key: item.nodeId,
    //   label: item.label
    // });
  }
  selectedData.value = arr;
};

const getPropByKey = (data: any, type: string, key: string): string => {
  if (transfer.value) {
    return transfer.value.getNodeProp(data, key, type);
  }
  return '';
};

const getKey = (data: any, type: string): string => {
  return getPropByKey(data, type, 'nodeId');
};

const getLabel = (data: any, type: string): string => {
  return getPropByKey(data, type, 'label');
};

const removeCheckedItem = (item: SelectedItem) => {
  if (!props.noChange) {
    const index = innerValue.value.findIndex((t) => getKey(t, props.type) === item.key);

    if (index > -1) {
      innerValue.value.splice(index, 1);
    }

    initSelectedData();
    emit('update:modelValue', innerValue.value);
  }
};

const onConfirm = (data: any[]) => {

  innerValue.value = data;
  initSelectedData();
  emit('update:modelValue', innerValue.value);
};

const changeDialog = () => {
  if (!props.noChange) {
    show.value = true;
  }
};

watch(
  () => props.modelValue,
  (val) => {
    if (!val) return;
    reloadCmpData();
  },
  { immediate: true, deep: true }
);
defineExpose({
  selectedLabels
});
</script>

<style lang="scss" scoped>
.tags {
  .input-box {
    &.as-input {
      border: 1px solid #dcdfe6;
      padding-left: 6px;
      font-size: 12px;
      min-height: 32px;
      line-height: 30px;
      border-radius: 4px;
      background: white;
      color: #606266;
      cursor: pointer;
    }

    &.no-change {
      cursor: not-allowed !important;
      background: rgb(245, 245, 245) !important;
    }
  }

  .org-tag {
    margin-right: 6px;
    max-width: 6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding-right: 1rem;
    vertical-align: middle;

    :deep(.el-tag__close) {
      position: absolute;
      right: 2px;
      top: 50%;
      margin-top: -7px;
    }
  }
}
</style>
