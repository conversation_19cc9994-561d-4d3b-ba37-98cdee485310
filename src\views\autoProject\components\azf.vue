<!-- 安置房特别页面 -->
<template>
  <div class="azf-main">
    <div class="flex-row" v-for="(item, index) in azfList" :key="index">
      <div class="main-item">{{ item.parcelName }}</div>
      <div class="end-content">
        <div
          class="item"
          v-for="(ite, idx) in item.list"
          :key="idx"
          @click="showAttr(ite)"
          :class="{ 'gree-item': getClass(ite), 'active': ite.id == active }"
        >
          <div>{{ ite.parcelName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';

interface AzfItem {
  id: string | number;
  parcelName: string;
  list: AzfItem[];
  levelNum?: number;
  fieldInstanceModels?: {
    groupName: string;
    attribution: {
      FYZT: string;
      [key: string]: any;
    };
  }[];
  [key: string]: any;
}

const props = defineProps<{
  isIfrem: boolean;
  mainParcelName: string;
}>();

const emit = defineEmits(['centerToRightAttr']);

// 数据列表
const azfList = ref<AzfItem[]>([]);
// 当前选择的户
const active = ref<string | number | null>(null);

/**
 * 初始化数据
 */
const initData = (list: AzfItem[]) => {
  azfList.value = list;
};

/**
 * 展示当前点击的属性
 */
const showAttr = (ite: AzfItem) => {
  active.value = ite.id;
  emit('centerToRightAttr', ite);
  // 发送消息到父页面，数据可以是任何可序列化的对象
  if (props.isIfrem) {
    window.parent.postMessage(
      {
        method: 'callChooseAzf',
        args: [ite, props.mainParcelName] // 替换为实际的参数
      },
      '*' // 注意：'*' 表示接受所有源的消息，实际应替换为父页面的确切源以提高安全性
    );
  }
};

/**
 * 获取样式类
 */
const getClass = (ite: AzfItem) => {
  let isflg = false;
  if (ite.levelNum == 5 && ite.fieldInstanceModels && ite.fieldInstanceModels.length != 0) {
    // 户才需要显示 是否已经安置 已安置需要设置为绿色 其他是黄色
    // 注意这里是写死的字段 后期可以考虑配置
    for (let i = 0; i < ite.fieldInstanceModels.length; i++) {
      if (ite.fieldInstanceModels[i].groupName == '安置房户的基本信息') {
        if (ite.fieldInstanceModels[i].attribution.FYZT == '已安置') {
          isflg = true;
        }
        break;
      }
    }
  }
  return isflg;
};

// 暴露方法给父组件使用
defineExpose({
  initData
});
</script>

<style lang="scss" scoped>
.azf-main {
  width: 100%;
  height: 100%;
  border: #ededed solid 1px;
  overflow: auto;
  padding: 10px;
  .flex-row {
    display: flex;
    flex-direction: row;
    .main-item {
      height: 80px;
      width: 80px;
      border: rgba(0, 0, 0, 0.2) solid 1px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
    }
    .end-content {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      .item {
        width: 210px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: rgba(0, 0, 0, 0.2) solid 1px;
        margin-right: 20px;
        margin-bottom: 20px;
        cursor: pointer;
      }
      .item:hover {
        background: #409eff;
        color: #fff;
        border: #409eff 1px solid;
      }
      .active {
        background: #409eff !important;
        color: #fff;
        border: #409eff 1px solid !important;
      }
      .gree-item {
        background: green;
        color: #fff;
      }
    }
  }
}
</style>
