<template>
  <div class="right-board">
    <el-tabs v-model="currentTab" class="center-tabs">
      <el-tab-pane name="field">
        <template #label>组件属性{{ getActiveTag }}</template>
      </el-tab-pane>
    </el-tabs>
    <el-scrollbar class="right-scrollbar">
      <!-- 组件属性 -->
      <el-form v-show="currentTab === 'field' && showField" label-width="90px" :model="localActiveData">
        <el-form-item
          v-if="localActiveData.label !== undefined"
          label="标题"
          prop="label"
          :rules="[
            { required: true, message: '请输入标题', trigger: 'blur' },
            {
              min: 1,
              max: 200,
              message: '长度在 1 到 200 个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input v-model="localActiveData.label" placeholder="请输入标题" @input="handleLabelInput(localActiveData)" />
        </el-form-item>
        <el-form-item v-if="localActiveData.placeholder !== undefined" label="占位提示">
          <el-input v-model="localActiveData.placeholder" placeholder="请输入占位提示" @input="handleInputPlaceholder(localActiveData)" />
        </el-form-item>
        <el-form-item v-if="localActiveData['start-placeholder'] !== undefined" label="开始占位">
          <el-input v-model="localActiveData['start-placeholder']" placeholder="请输入占位提示" />
        </el-form-item>
        <el-form-item v-if="localActiveData['end-placeholder'] !== undefined" label="结束占位">
          <el-input v-model="localActiveData['end-placeholder']" placeholder="请输入占位提示" />
        </el-form-item>
        <el-form-item v-if="localActiveData.showDivider !== undefined" label="显示分割线">
          <el-switch v-model="localActiveData.showDivider" />
        </el-form-item>
        <el-form-item v-if="localActiveData['show-summary'] !== undefined" label="显示合计">
          <el-switch v-model="localActiveData['show-summary']" />
        </el-form-item>
        <el-form-item v-if="localActiveData.justify !== undefined && localActiveData.type === 'flex'" label="水平排列">
          <el-select v-model="localActiveData.justify" placeholder="请选择水平排列" :style="{ width: '100%' }">
            <el-option v-for="(item, index) in justifyOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="localActiveData.min !== undefined" label="最小值">
          <el-input-number v-model="localActiveData.min" placeholder="最小值" />
        </el-form-item>
        <el-form-item v-if="localActiveData.max !== undefined" label="最大值">
          <el-input-number v-model="localActiveData.max" placeholder="最大值" />
        </el-form-item>
        <el-form-item v-if="localActiveData.step !== undefined" label="步长">
          <el-input-number v-model="localActiveData.step" placeholder="步数" />
        </el-form-item>
        <el-form-item v-if="['el-input-number', 'fc-amount'].includes(localActiveData.tag)" label="精度">
          <el-input-number v-model="localActiveData.precision" :min="0" placeholder="精度" />
        </el-form-item>
        <el-form-item v-if="localActiveData.maxlength !== undefined && !['phoneIcon', 'idCardIcon'].includes(localActiveData.icon)" label="最多输入">
          <el-input v-model="localActiveData.maxlength" placeholder="请输入字符长度">
            <template #append>个字符</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="localActiveData.actionText !== undefined" label="动作文字">
          <el-input v-model="localActiveData.actionText" placeholder="请输入动作文字" />
        </el-form-item>
        <el-form-item v-if="localActiveData.tag === 'fc-input-table'" label="类型">
          <el-select size="small" v-model="localActiveData.type">
            <el-option label="列表" value="list" />
            <el-option label="表格" value="table" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="localActiveData.accept !== undefined" label="文件类型">
          <el-select v-model="localActiveData.accept" placeholder="请选择文件类型" :style="{ width: '100%' }" clearable>
            <el-option label="图片" value="image" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="localActiveData['range-separator'] !== undefined" label="分隔符">
          <el-input v-model="localActiveData['range-separator']" placeholder="请输入分隔符" />
        </el-form-item>
        <template v-if="['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(localActiveData.tag) > -1">
          <el-divider>选项</el-divider>
          <draggable :list="localActiveData.options" :animation="340" group="selectItem" handle=".option-drag">
            <template #item="{ element, index }">
              <div class="select-item">
                <div class="select-line-icon option-drag">
                  <el-icon><Rank /></el-icon>
                </div>
                <el-input placeholder="选项值" size="small" :value="element.label" @input="setOptionValue(element, index, $event)" />
                <div class="close-btn select-line-icon" @click="localActiveData.options.splice(index, 1)">
                  <el-icon><DeleteFilled /></el-icon>
                </div>
              </div>
            </template>
          </draggable>
          <div style="margin-left: 20px">
            <el-button style="padding-bottom: 0" icon="el-icon-circle-plus-outline" type="text" @click="addSelectItem"> 添加选项 </el-button>
          </div>
          <el-divider />
        </template>
        <el-form-item v-if="localActiveData['active-color'] !== undefined" label="开启颜色">
          <el-color-picker v-model="localActiveData['active-color']" />
        </el-form-item>
        <el-form-item v-if="localActiveData['inactive-color'] !== undefined" label="关闭颜色">
          <el-color-picker v-model="localActiveData['inactive-color']" />
        </el-form-item>

        <el-form-item v-if="localActiveData['allow-half'] !== undefined" label="允许半选">
          <el-switch v-model="localActiveData['allow-half']" />
        </el-form-item>
        <el-form-item v-if="localActiveData['show-text'] !== undefined" label="辅助文字">
          <el-switch v-model="localActiveData['show-text']" @change="rateTextChange" />
        </el-form-item>
        <el-form-item v-if="localActiveData['show-score'] !== undefined" label="显示分数">
          <el-switch v-model="localActiveData['show-score']" @change="rateScoreChange" />
        </el-form-item>
        <el-form-item v-if="localActiveData['show-stops'] !== undefined" label="显示间断点">
          <el-switch v-model="localActiveData['show-stops']" />
        </el-form-item>
        <el-form-item v-if="localActiveData.range !== undefined" label="范围选择">
          <el-switch v-model="localActiveData.range" @change="rangeChange" />
        </el-form-item>
        <el-form-item v-if="localActiveData.tag === 'el-color-picker'" label="颜色格式">
          <el-select v-model="localActiveData['color-format']" placeholder="请选择颜色格式" :style="{ width: '100%' }" @change="colorFormatChange">
            <el-option v-for="(item, index) in colorFormatOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <template v-if="localActiveData.tag === 'fc-org-select'">
          <el-form-item label="弹框名称" v-if="localActiveData.title !== undefined">
            <el-input
              :value="localActiveData.title"
              @input="$emit('update:activeData', { ...localActiveData, title: $event })"
              placeholder="请输入弹框名称"
            />
          </el-form-item>
          <el-form-item label="可选数量" v-if="localActiveData.maxNum !== undefined">
            <el-input-number
              :value="localActiveData.maxNum"
              @input="$emit('update:activeData', { ...localActiveData, maxNum: $event })"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="按钮类型">
            <el-select v-model="localActiveData.buttonType" size="small">
              <el-option label="Button" value="button" />
              <el-option label="Input" value="input" />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item v-if="localActiveData.tag === 'el-cascader'" label="是否多选">
          <el-switch v-model="localActiveData.props.props.multiple" />
        </el-form-item>
        <el-form-item v-if="localActiveData.tag === 'el-cascader'" label="可否筛选">
          <el-switch v-model="localActiveData.filterable" />
        </el-form-item>
        <el-form-item v-if="localActiveData.required !== undefined" label="是否必填">
          <el-switch v-model="localActiveData.required" @change="requireChange" :disabled="!couldChangeRequire" />
          <el-tooltip class="item" effect="dark" placement="top-start">
            <template #content>
              流程条件：流程设计里可用于区分流程走向
              <br />
              例如：金额大于500需要主管+经理审批；小于500只需要主管审批。
            </template>
            <span v-show="localActiveData.proCondition" style="font-size: 12px; color: #aaa"> &nbsp;(勾选后可作为流程条件) </span>
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="localActiveData.showChinese !== undefined" label="显示大写">
          <el-switch v-model="localActiveData.showChinese" />
        </el-form-item>
        <el-form-item v-if="localActiveData.cmpType === 'custom'" label="组件说明">
          <el-input type="textarea" v-model="localActiveData.explain" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <tree-node-dialog-bmp v-model:visible="dialogVisible" title="添加选项" @commit="addNode"></tree-node-dialog-bmp>
    <icons-dialog-bmp v-model:visible="iconsVisible" :current="localActiveData[currentIconModel]" @select="setIcon"></icons-dialog-bmp>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useFlowStore } from '@/store/modules/flow';
// import { isArray } from 'util';
import TreeNodeDialogBmp from './TreeNodeDialogBmp.vue';
import { isNumberStr } from './utils/index';
import IconsDialogBmp from './IconsDialogBmp.vue';
import { inputComponents, selectComponents, layoutComponents } from './components/generator/config';
import { saveFormConf } from './utils/db';
import draggable from 'vuedraggable';
// import { mergeNumberOfExps, validExp, toRPN, calcRPN } from '@/utils/index.js';

interface FormItem {
  tag: string;
  icon?: string;
  label?: string;
  placeholder?: string;
  'start-placeholder'?: string;
  'end-placeholder'?: string;
  showDivider?: boolean;
  'show-summary'?: boolean;
  justify?: string;
  align?: string;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  maxlength?: number;
  actionText?: string;
  type?: string;
  accept?: string;
  'range-separator'?: string;
  options?: Array<{
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  dataType?: string;
  labelKey?: string;
  valueKey?: string;
  childrenKey?: string;
  'active-color'?: string;
  'inactive-color'?: string;
  'allow-half'?: boolean;
  'show-text'?: boolean;
  'show-score'?: boolean;
  'show-stops'?: boolean;
  range?: boolean;
  'color-format'?: string;
  title?: string;
  maxNum?: number;
  buttonType?: string;
  tagConfig?: {
    size?: string;
    effect?: string;
    type?: string;
    'disable-transitions'?: boolean;
    hit?: boolean;
  };
  size?: string;
  props?: {
    props: {
      multiple?: boolean;
    };
  };
  filterable?: boolean;
  expression?: any[];
  required?: boolean;
  proCondition?: boolean;
  showChinese?: boolean;
  cmpType?: string;
  explain?: string;
  [key: string]: any;
}

interface FormConf {
  formRef: string;
  size: string;
  labelPosition: string;
}

const props = defineProps<{
  showField: boolean;
  activeData: FormItem;
  formConf: FormConf;
  couldChangeRequire: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:activeData', data: FormItem): void;
  (e: 'updateLabel', label: string): void;
  (e: 'tag-change', target: any): void;
}>();

const flowStore = useFlowStore();

const expressionTemp = ref<any[]>([]);
const proConditionCmp = ['el-input-number', 'el-select', 'el-radio-group'];
const currentTab = ref('field');
const currentNode = ref(null);
const dialogVisible = ref(false);
const iconsVisible = ref(false);
const currentIconModel = ref(null);
const expValid = ref(true);

const themeOptions = [
  { value: 'dark', label: '深色' },
  { value: 'light', label: '亮色' },
  { value: 'plain', label: '扁平' }
];

const typeOptions = [
  { value: 'success', label: 'success' },
  { value: 'info', label: 'info' },
  { value: 'warning', label: 'warning' },
  { value: 'danger', label: 'danger' }
];

const dateTypeOptions = [
  { label: '日(date)', value: 'date' },
  { label: '周(week)', value: 'week' },
  { label: '月(month)', value: 'month' },
  { label: '年(year)', value: 'year' },
  { label: '日期时间(datetime)', value: 'datetime' }
];

const dateRangeTypeOptions = [
  { label: '日期范围(daterange)', value: 'daterange' },
  { label: '月范围(monthrange)', value: 'monthrange' },
  { label: '日期时间范围(datetimerange)', value: 'datetimerange' }
];

const colorFormatOptions = [
  { label: 'hex', value: 'hex' },
  { label: 'rgb', value: 'rgb' },
  { label: 'rgba', value: 'rgba' },
  { label: 'hsv', value: 'hsv' },
  { label: 'hsl', value: 'hsl' }
];

const justifyOptions = [
  { label: 'start', value: 'start' },
  { label: 'end', value: 'end' },
  { label: 'center', value: 'center' },
  { label: 'space-around', value: 'space-around' },
  { label: 'space-between', value: 'space-between' }
];

const layoutTreeProps = {
  label(data: any, node: any) {
    return data.componentName || `${data.label}: ${data.vModel}`;
  }
};

const phonePattern = /^1[3-9]\d{9}$/;
const idcardPattern = /\d{17}[0-9Xx]|\d{15}/;

const dateOptions = computed(() => {
  if (props.activeData.type !== undefined && ['el-date-picker', 'fc-date-duration'].includes(props.activeData.tag)) {
    if (props.activeData['start-placeholder'] === undefined) {
      return dateTypeOptions;
    }
    return dateRangeTypeOptions;
  }
  return [];
});

const tagList = computed(() => {
  return [
    {
      label: '输入型组件',
      options: inputComponents
    },
    {
      label: '选择型组件',
      options: selectComponents
    }
  ];
});

const calculateCmps = computed(() => {
  const calcList = [];
  const loop = (data: any, parent: any) => {
    if (!data) return;
    if (Array.isArray(data.children)) {
      loop(data.children, data);
    }
    if (Array.isArray(data)) data.forEach((d) => loop(d, parent));
    if (['el-input-number', 'fc-amount'].includes(data.tag)) {
      const isTableChild = parent && parent.rowType === 'table';
      calcList.push({
        vModel: isTableChild ? parent.vModel + '.' + data.vModel : data.vModel,
        label: isTableChild ? parent.label + '.' + data.label : data.label
      });
    }
  };
  loop(flowStore.formItemList);
  return calcList;
});

const phoneValidationRule = computed(() => {
  const phoneflag = props.activeData.icon === 'phoneIcon';
  const idflag = props.activeData.icon === 'idCardIcon';
  const pattern = /1[2-9]\d{9}/;
  const idPattern = /\d{17}[0-9Xx]|\d{15}/;
  if (phoneflag) {
    return {
      required: false,
      trigger: 'blur',
      validator: (rule: any, value: string, callback: Function) => {
        if (!phonePattern.test(value)) {
          callback(new Error('请输入有效的手机号'));
        } else {
          callback();
        }
      }
    };
  } else if (idflag) {
    return {
      required: false,
      trigger: 'blur',
      validator: (rule: any, value: string, callback: Function) => {
        if (!idcardPattern.test(value)) {
          callback(new Error('身份证输入错误'));
        } else {
          callback();
        }
      }
    };
  }
});

const getActiveTag = computed(() => {
  const tag = props?.activeData?.icon;
  let result = '';
  switch (tag) {
    case 'input':
      result = '(单行输入框)';
      break;
    case 'textarea':
      result = '(多行输入框)';
      break;
    case 'number':
      result = '(数字输入框)';
      break;
    case 'select':
      result = '(下拉选择)';
      break;
    case 'radio':
      result = '(单选框组)';
      break;
    case 'checkbox':
      result = '(多选框组)';
      break;
    case 'time':
      result = '(时间选择)';
      break;
    case 'time-range':
      result = '(时间范围)';
      break;
    case 'date':
      result = '(日期选择)';
      break;
    case 'date-range':
      result = '(日期范围)';
      break;
    case 'upload':
      result = '(附件)';
      break;
    case 'phoneIcon':
      result = '(手机号)';
      break;
    case 'idCardIcon':
      result = '(身份证号码)';
      break;
    default:
      result = '';
      break;
  }
  return result;
});

watch(
  () => props.formConf,
  (val) => {
    saveFormConf(val);
  },
  { deep: true }
);

watch(
  () => props.activeData,
  (val) => {
    if (val.tag !== 'fc-calculate') return;
    reloadExpressionTemp();
  }
);

const localActiveData = ref<FormItem>({ ...props.activeData });

// 监听 props 变化
watch(
  () => props.activeData,
  (newVal) => {
    localActiveData.value = { ...newVal };
  },
  { deep: true }
);

// 监听本地数据变化
watch(
  localActiveData,
  (newVal) => {
    emit('update:activeData', { ...newVal });
  },
  { deep: true }
);

const handleLabelInput = (item: FormItem) => {
  emit('updateLabel', item.label);
};
// 设置提示语是否变化的标识
const handleInputPlaceholder = (item: FormItem) => {
  emit('updatePlaceholder', item.placeholder);
};
const notObject = (val: any) => {
  return val === null || val === undefined || Object(val) !== val;
};

const reloadExpressionTemp = () => {
  const isValid = (d: any) => {
    const target = calculateCmps.value.find((cmp) => cmp.vModel === d.vModel && cmp.label === d.label);
    return target ? true : false;
  };
  expressionTemp.value = props.activeData.expression.map((t) => {
    return typeof t === 'string' || isValid(t) ? t : { vModel: t.vModel, label: '无效的值' };
  });
  props.activeData.expression = expressionTemp.value;
};

const requireChange = (required: boolean) => {
  if (!props.activeData.proCondition) return;
  if (required && !props.activeData.multiple) {
    flowStore.addPCondition(props.activeData);
  } else {
    flowStore.delPCondition(props.activeData.formId);
  }
};

const addSelectItem = () => {
  const leng = props.activeData.options.length;
  props.activeData.options.push({
    label: `选项${leng + 1}`,
    value: leng + 1
  });
};

const addTreeItem = () => {
  ++idGlobal;
  dialogVisible.value = true;
  currentNode.value = props.activeData.options;
};

const renderContent = (h: any, { node, data, store }: any) => {
  return h('div', { class: 'custom-tree-node' }, [
    h('span', node.label),
    h('span', { class: 'node-operation' }, [
      h('i', {
        class: 'el-icon-plus',
        title: '添加',
        onClick: () => append(data)
      }),
      h('i', {
        class: 'el-icon-delete',
        title: '删除',
        onClick: () => remove(node, data)
      })
    ])
  ]);
};

const append = (data: any) => {
  if (!data.children) {
    data.children = [];
  }
  dialogVisible.value = true;
  currentNode.value = data.children;
};

const remove = (node: any, data: any) => {
  const { parent } = node;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d: any) => d.id === data.id);
  children.splice(index, 1);
};

const addNode = (data: any) => {
  currentNode.value.push(data);
};

const setOptionValue = (item: any, index: number, val: string) => {
  item.label = val;
  item.value = index + 1;
};

const setDefaultValue = (val: any) => {
  if (Array.isArray(val)) {
    return val.join(',');
  }
  if (['string', 'number'].indexOf(typeof val) > -1) {
    return val;
  }
  if (typeof val === 'boolean') {
    return `${val}`;
  }
  return val;
};

const onDefaultValueInput = (str: string) => {
  if (isArray(props.activeData.defaultValue)) {
    props.activeData.defaultValue = str.split(',').map((val) => (isNumberStr(val) ? +val : val));
  } else if (['true', 'false'].indexOf(str) > -1) {
    props.activeData.defaultValue = JSON.parse(str);
  } else {
    props.activeData.defaultValue = isNumberStr(str) ? +str : str;
  }
};

const onSwitchValueInput = (val: string, name: string) => {
  if (['true', 'false'].indexOf(val) > -1) {
    props.activeData[name] = JSON.parse(val);
  } else {
    props.activeData[name] = isNumberStr(val) ? +val : val;
  }
};

const setTimeValue = (val: string, type: string) => {
  const valueFormat = type === 'week' ? dateTimeFormat.date : val;
  props.activeData.defaultValue = null;
  props.activeData['value-format'] = valueFormat;
  props.activeData.format = val;
};

const spanChange = (val: number) => {
  // props.formConf.span = val
};

const multipleChange = (val: boolean) => {
  props.activeData.defaultValue = val ? [] : '';
};

const dateTypeChange = (val: string) => {
  setTimeValue(dateTimeFormat[val], val);
};

const rangeChange = (val: boolean) => {
  props.activeData.defaultValue = val ? [props.activeData.min, props.activeData.max] : props.activeData.min;
};

const rateTextChange = (val: boolean) => {
  if (val) props.activeData['show-score'] = false;
};

const rateScoreChange = (val: boolean) => {
  if (val) props.activeData['show-text'] = false;
};

const colorFormatChange = (val: string) => {
  props.activeData.defaultValue = null;
  props.activeData['show-alpha'] = val.indexOf('a') > -1;
  props.activeData.renderKey = props.activeData.formId + new Date().getTime();
};

const openIconsDialog = (model: string) => {
  iconsVisible.value = true;
  currentIconModel.value = model;
};

const setIcon = (val: string) => {
  props.activeData[currentIconModel.value] = val;
};

const tagChange = (tagIcon: string) => {
  let target = inputComponents.find((item) => item.tagIcon === tagIcon);
  if (!target) target = selectComponents.find((item) => item.tagIcon === tagIcon);
  emit('tag-change', target);
};
</script>

<style lang="scss" scoped>
.right-board {
  border-left: 1px solid #f1e8e8;
  flex: 1;
  padding-top: 3px;
  height: 100%;
  .el-scrollbar {
    height: calc(100% - 42px);
    padding-right: 20px;
  }
}

.select-item {
  display: flex;
  border: 1px dashed #fff;
  box-sizing: border-box;

  & .close-btn {
    cursor: pointer;
    color: #f56c6c;
  }

  & .el-input + .el-input {
    margin-left: 4px;
  }
}

.select-item + .select-item {
  margin-top: 4px;
}

.select-item.sortable-chosen {
  border: 1px dashed #409eff;
}

.select-line-icon {
  line-height: 30px;
  font-size: 16px;
  padding: 0 4px;
  color: #777;
}

.option-drag {
  cursor: move;
}

.time-range {
  .el-date-editor {
    width: 227px;
  }

  :deep(.el-icon-time) {
    display: none;
  }
}

.document-link {
  position: absolute;
  display: block;
  width: 26px;
  height: 26px;
  top: 0;
  left: 0;
  cursor: pointer;
  background: #409eff;
  z-index: 1;
  border-radius: 0 0 6px 0;
  text-align: center;
  line-height: 26px;
  color: #fff;
  font-size: 18px;
}

.node-label {
  font-size: 14px;
}

.node-icon {
  color: #bebfc3;
}

.calc-btn {
  padding: 4px 8px;
  margin: 0 6px;
  background: #e5e5e5;
  cursor: pointer;

  &.error {
    background: #f56c6c;
    color: white;
  }

  &:hover {
    background: #f5f5f5;
  }
}

.pane-calc-preview {
  padding: 0 10px;
  cursor: pointer;
  min-height: 32px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  font-size: 12px;
}

.calc-dialog {
  :deep(.el-dialog__body) {
    padding-top: 0;
  }

  .calc-box {
    font-size: 12px;
    line-height: 2;

    .calc-tip {
      margin: 10px 0;
      font-size: 12px;
      color: #aaa;
    }

    .calc-preview {
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      min-height: 60px;
      padding: 4px 10px;
      position: relative;

      &.error {
        border: 1px solid red;
      }

      .preview-actions {
        position: absolute;
        bottom: 0;
        right: 0;

        > i {
          font-size: 14px;
          margin-right: 8px;
          cursor: pointer;

          &:hover {
            color: red;
          }
        }
      }
    }
  }
}

.calc-dialog :deep(.el-dialog__body) {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
