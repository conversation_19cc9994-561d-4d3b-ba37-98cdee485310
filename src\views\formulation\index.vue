<template>
  <div class="main">
    <container-card class="card">
      <el-row :gutter="10" class="mb8">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="类型">
            <el-select v-model="queryParams.type" placeholder="请选择" style="width: 200px" clearable @clear="getDefaultFormulationList">
              <el-option v-for="item in typeOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryParams.appType" placeholder="请选择" style="width: 200px">
              <el-option v-for="item in appTypeOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getDefaultFormulationList">查询</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row :gutter="10" class="mb8" v-if="userStore.user.companyId === '1'">
        <!-- <el-col :span="1.5">
          <el-button type="success" plain :icon="Plus" @click="handleFormulaitionOpen">打开表达式</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button type="success" plain :icon="Plus" @click="handleOpen">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain :icon="Check" :disabled="formulationList.length > 0" @click="handleSaveFormulation">保存全部</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain :icon="Delete" :disabled="formulationList.length !== 0" @click="handleDeleteAll">删除全部</el-button>
        </el-col>
        <!-- <right-toolbar @queryTable="getDefaultFormulationList"></right-toolbar> -->
      </el-row>
      <div class="formutation-app">
        <div class="forumation-table">
          <el-table v-loading="loading" :data="paginatedFormulationList" :height="tableHeight">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="公式名" align="center" prop="name" />
            <el-table-column label="适配类型" align="center" prop="type">
              <template #default="{ row }">
                <div v-if="row.mateBigScreen && row.mateField && row.mateWord">
                  <el-tag type="warning">适配全部</el-tag>
                </div>
                <div v-else>
                  <span v-if="row.mateBigScreen"><el-tag>适配大屏</el-tag></span>
                  <span v-if="row.mateField"><el-tag>适配字段</el-tag></span>
                  <span v-if="row.mateWord"><el-tag>适配导出</el-tag></span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="类型" align="center" prop="type">
              <template #default="{ row }">
                <span>{{ getFormulationType(row.type) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="参数" align="center" prop="param">
              <template #default="{ row }">
                <span>{{ spanRule(row.param) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="调用者" align="center" prop="parentVal">
              <template #default="{ row }">
                <span v-if="row.parentVal">{{ row.parentVal }}</span>
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column label="返回值" align="center" prop="returnVal" />
            <el-table-column label="示例" align="center" prop="remark">
              <template #default="{ row }">
                <span :title="row.example">{{ row.example }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ row }">
                <el-button type="primary" link :icon="Edit" @click="handleEdit(row)" v-if="userStore.user.companyId === '1'">编辑</el-button>
                <el-button type="danger" link :icon="Delete" @click="handleDeleteOne(row)" v-if="userStore.user.companyId === '1'">删除</el-button>
                <el-button v-if="userStore.user.companyId !== '1'" type="primary" link :icon="View" @click="handleEdit(row, true)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="pagination-container"
        />
      </div>
    </container-card>
    <el-dialog v-model="open" :title="addTitle" append-to-body :close-on-click-modal="false" @closed="handleClearFormations">
      <div style="height: 400px; overflow: auto" class="form-aliag">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="11">
              <el-form-item label="公式名" prop="name">
                <el-input v-model="form.name" placeholder="请输入公式名" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择" style="width: 100%">
                  <el-option v-for="item in typeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="返回值" prop="returnVal">
                <el-input v-model="form.returnVal" placeholder="请输入返回值" />
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="示例" prop="example">
                <el-input v-model="form.example" placeholder="请输入示例" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="适配类型">
                <el-select v-model="form.appType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="item in addAppTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="要素值">
                <el-input v-model="form.childElement" placeholder="请输入要素值" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="调用者">
                <el-input v-model="form.parentVal" placeholder="请输入调用者" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item
              v-for="(param, index) in form.paramList"
              :label="'参数' + (index + 1)"
              :key="param.key"
              :prop="'param' + (index + 1)"
              label-width="120px"
            >
              <el-col :span="userStore.user.companyId == '1' ? 14 : 23">
                <el-input v-model="param.value" placeholder="请输入参数"></el-input>
              </el-col>
              <el-col :span="6" :offset="1" style="display: flex" v-if="userStore.user.companyId == '1'">
                <el-button type="primary" plain @click="handleAddParams">新增参数</el-button>
                <el-button type="danger" plain @click="handleDeleteParams(param)">删除</el-button>
              </el-col>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              v-for="(paramDesc, index) in form.paramDescList"
              :label="'参数描述' + (index + 1)"
              :key="paramDesc.key"
              :prop="'paramDesc' + (index + 1)"
              label-width="120px"
            >
              <el-col :span="userStore.user.companyId == '1' ? 12 : 23">
                <el-input v-model="paramDesc.value" placeholder="请输入参数描述"></el-input>
              </el-col>
              <el-col :span="7" :offset="1" style="display: flex" v-if="userStore.user.companyId == '1'">
                <el-button type="primary" plain @click="handelAddParamsDesc">新增参数</el-button>
                <el-button type="danger" plain @click="handleDeleteParamDesc(paramDesc)">删除</el-button>
              </el-col>
            </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="23">
              <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="实例图片">
              <div class="upload-card">
                <el-upload
                  list-type="picture-card"
                  :on-change="handleChangeUploadImg"
                  :on-remove="handleRemoveUploadImg"
                  :action="`${base}/system/option/multi/upload`"
                  :auto-upload="false"
                  :file-list="forumImgFileList"
                  :limit="1"
                  ref="uploadRef"
                  accept=".jpg,.png"
                  :headers="headers"
                  fill="cover"
                >
                  <svg-icon icon-class="tjtp" style="width: 32px; height: 32px" />
                </el-upload>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="!isCheck">
          <el-button type="primary" @click="handleAddOne">确 定</el-button>
          <el-button @click="handleClearFormations">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <FormulaEditingDialog
      v-model="dialogVisible"
      :expression="expression"
      @closeFormulaEdit="handleCloseDialog"
      @submitFormulation="handleSubmitFormulation"
    />
    <!--  :is-copy="isCopy"
      :input-type="inputType"
      :app-type="appType"
      :is-yscj="isYSCJ" -->
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Check, Delete, Edit, View } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { getForumuList, saveForumulation, getForumuListByDataScreen } from '@/api/forumulation';
import { uploadImgae } from '@/api/forum/index';
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';
import { formatDateType, spanRule } from '@/utils/filters';
import { getToken } from '@/utils/auth';
import FormulaEditingDialog from '@/components/formulaEditingDialog/index.vue';

interface QueryParams {
  pageSize: number;
  pageNum: number;
  type?: number;
  appType: string;
}

interface FormData {
  name: string;
  remark: string;
  type?: number;
  delFlag: number;
  param: Record<string, any>;
  paramDesc: Record<string, any>;
  returnVal?: string;
  example: string;
  paramList: Array<{ key: number; value: string }>;
  paramDescList: Array<{ key: number; value: string }>;
  appType: string[] | string;
  id?: string;
  childElement?: string;
  parentVal?: string;
  createTime?: string;
  examplePic?: string;
}

interface FormulaItem {
  name: string;
  remark: string;
  type: number;
  delFlag: number;
  param?: Record<string, string>;
  paramDesc?: Record<string, string>;
  returnVal: string;
  appType: number;
  example?: string;
  parentVal?: string;
}

interface RequestHeaders {
  'Authorization': string;
  'Access-Control-Allow-Origin': string;
}

const headers: RequestHeaders = {
  'Authorization': `Bearer ${getToken()}`,
  'Access-Control-Allow-Origin': '*'
};

const base = import.meta.env.VITE_APP_BASE_API;
const tableHeight = window.innerHeight - 320;

const userStore = useUserStore();
const loading = ref(true);
const open = ref(false);
const expression = ref('');
const isCheck = ref(false);
const addTitle = ref('新增公式');
const formRef = ref<FormInstance>();
const formulaVisible = ref(false);
const queryParams = reactive<QueryParams>({
  pageSize: 10,
  pageNum: 1,
  type: undefined,
  appType: '#word'
});
//  创建打开表达式弹框
const dialogVisible = ref(false);
const form = reactive<FormData>({
  name: '',
  remark: '',
  type: undefined,
  delFlag: 0,
  param: {},
  paramDesc: {},
  returnVal: undefined,
  example: '',
  paramList: [{ key: Date.now(), value: '' }],
  paramDescList: [{ key: Date.now(), value: '' }],
  appType: '#field',
  childElement: '',
  parentVal: '',
  createTime: '',
  examplePic: ''
});

const formulationList = ref<any[]>([]);
const total = computed(() => formulationList.value.length);

const paginatedFormulationList = computed(() => {
  const start = (queryParams.pageNum - 1) * queryParams.pageSize;
  const end = queryParams.pageNum * queryParams.pageSize;
  return formulationList.value.slice(start, end);
});

const typeOptions = [
  { id: 1, label: '数字函数' },
  { id: 2, label: '字符串函数' },
  { id: 3, label: '系统函数' },
  { id: 4, label: '图形函数' },
  { id: 5, label: '属性函数' },
  { id: 6, label: '定制函数' },
  { id: 7, label: '快捷函数' },
  { id: 8, label: '时间函数' },
  { id: 9, label: '表格函数' },
  { id: 10, label: '数据大屏函数' },
  { id: 11, label: '勘界专属' },
  { id: 98, label: '全局常量' },
  { id: 99, label: '变量' }
];

const appTypeOptions = [
  { id: '#word', label: '适配导出' },
  { id: '#field', label: '适配字段' },
  { id: '#console', label: '适配大屏' }
];

// 2025年5月6日，由于添加其他俩种的表达式 列表中不显示，特此后端兰刚说适配导出和适配大屏是根据代码中 映射的，页面上目前只需要添加APP 的适配字段的内容
const addAppTypeOptions = [
  // { id: '#word', label: '适配导出' },
  { id: '#field', label: '适配字段' }
  // { id: '#console', label: '适配大屏' }
];

const rules: FormRules = {
  name: [
    { required: true, message: '名称不能为空', trigger: 'blur' },
    {
      pattern: /^[$a-zA-Z]/,
      message: '公式名必须以"$"或字母开头',
      trigger: 'blur'
    }
  ],
  remark: [{ required: true, message: '公式描述不能为空', trigger: 'blur' }],
  returnVal: [{ required: true, message: '返回值不能为空', trigger: ['blur', 'change'] }],
  example: [{ required: true, message: '示例不能为空', trigger: ['blur', 'change'] }],
  type: [{ required: true, message: '请选择类型', trigger: ['blur', 'change'] }]
};

const getFormulationType = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '数字函数',
    2: '字符串函数',
    3: '系统函数',
    4: '图形函数',
    5: '属性函数',
    55: '线要素函数',
    6: '定制函数',
    7: '快捷函数',
    56: '属性组函数',
    0: '操作符',
    8: '时间函数',
    9: '表格函数',
    10: '数据大屏',
    11: '勘界专属',
    98: '全局常量',
    99: '变量'
  };
  return typeMap[type] || '变量';
};

const forumImgFileList = ref<any[]>([]);

const truncateText = (text: string, length: number): string => {
  if (!text) return '';
  return text.length > length ? text.slice(0, length) + '...' : text;
};

const getDefaultFormulationList = async () => {
  try {
    const params = {
      type: queryParams.type || 0,
      appType: queryParams.appType
    };

    // 当选择适配大屏时使用getForumuListByDataScreen接口
    const res = queryParams.appType === '#console' ? await getForumuListByDataScreen(params) : await getForumuList(params);

    if (res.code === 200) {
      formulationList.value = res.data;
      loading.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Failed to fetch formulation list:', error);
    ElMessage.error('获取公式列表失败');
  }
};

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  getDefaultFormulationList();
};

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val;
  getDefaultFormulationList();
};

const handleOpen = () => {
  addTitle.value = '新增公式';
  form.appType = '#field';
  open.value = true;
};
const handleFormulaitionOpen = () => {
  dialogVisible.value = true;
};
const handleClearFormations = () => {
  Object.assign(form, {
    name: '',
    remark: '',
    type: undefined,
    delFlag: 0,
    param: {},
    paramDesc: {},
    returnVal: undefined,
    example: '',
    paramList: [{ key: Date.now(), value: '' }],
    paramDescList: [{ key: Date.now(), value: '' }],
    appType: '',
    parentVal: undefined
  });
  if (formRef.value) {
    formRef.value.resetFields();
  }
  open.value = false;
};

const handleSaveFormulation = async () => {
  const NumList: FormulaItem[] = [
    {
      name: 'max',
      remark: '计算数组中的最大值',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'max(3,4)  ——>  4.0'
    },
    {
      name: 'min',
      remark: '计算数组中的最小值',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'min(0,4)  ——>  0.0'
    },
    {
      name: 'sum',
      remark: '求俩个数之和',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'sum(1,4)  ——>  5.0'
    },
    {
      name: 'subtract',
      remark: '求俩个数之差',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'subtract(5,4)  ——>  1.0'
    },
    {
      name: 'multiply',
      remark: '求俩个数之积',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'multiply(5,2)  ——>  10.0'
    },
    {
      name: 'divide',
      remark: '求俩个数之商',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'divide(6,2)  ——>  3.0'
    },
    {
      name: 'floor',
      remark: '向下取整，返回一个整数',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)'
      },
      example: 'floor(3.5) ——>3',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: 'round',
      remark: '把一个数字舍入为最接近的整数',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Int',
        param3: 'Int'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '保留几位小数',
        desc3: `0 直接进位;1 舍弃多余的小数;2 往大取值;3 往小取值;4 四舍五入;5 五舍六入`
      },
      appType: 3,
      example: 'round(5.5,6,1) ——>6.0 | round(2.5,6,1) ——>2.0 |round(5.55,6,1)——>6.0',
      returnVal: 'Int'
    }
  ];

  const StrList: FormulaItem[] = [
    {
      name: 'toString',
      remark: '把数字类型改成字符串',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'Double'
      },
      paramDesc: {
        desc1: '要抓换的数字'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toString(1)  ——>  "1"'
    },
    {
      name: 'substring',
      remark: '截取字符串的长度',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'Int',
        param3: 'Int'
      },
      paramDesc: {
        desc1: '要截取字符串的内容，内容为文本',
        desc2: '要截取字符串内容的开始角标，角标0开始',
        desc3: '要截取字符串内容的结束角标，'
      },
      returnVal: 'String',
      appType: 3,
      example: 'substring("你好，中国！",0,2)  ——>  你好'
    },
    {
      name: 'concat',
      remark: '将多个字符串连接成一个字符串。空值被转换为空字符串。其它类型的值(如数字)将被转换为字符串。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'String'
      },
      paramDesc: {
        desc1: '要拼接的第一个字符串',
        desc2: '要拼接的第二个字符串'
      },
      returnVal: 'String',
      appType: 3,
      example: 'concat("你好，","中国！")  ——> 你好，中国！'
    },
    {
      name: 'toUpperCase',
      remark: '将字符串转换为大写字母。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要转换的英文小写字母的字符串，如果为其他类型的字符串，则会输出输入的字符'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toUpperCase("abc")  ——> ABC'
    },
    {
      name: 'toLowerCase',
      remark: '将字符串转换为小写字母。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要转换的英文大写写字母的字符串，如果为其他类型的字符串，则会输出输入的字符'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toUpperCase("ABC")  ——> abc'
    },
    {
      name: 'replace',
      remark: '替换字符串，返回一个字符串、数组或字符串映射替换后的字符串。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'String',
        param3: 'String'
      },
      paramDesc: {
        desc1: '要替换的字符串',
        desc2: '将要替换的字符串',
        desc3: '替换的内容'
      },
      returnVal: 'String',
      appType: 3,
      example: 'replace("你好，你好吗？你好不好","好","ok")  ——> 你ok，你ok吗？你ok不ok'
    },
    {
      name: 'length',
      remark: '获取字符串的长度',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要获取字符串的长度'
      },
      returnVal: 'Int',
      appType: 3,
      example: 'length("你好") ——> 2'
    },
    {
      name: 'reverse',
      remark: '翻转字符串',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要翻转的字符串'
      },
      returnVal: 'String',
      appType: 3,
      example: 'reverse("你好") ——> 好你'
    }
  ];

  const GEOList: FormulaItem[] = [
    {
      name: 'Geometry',
      remark: '返回一个要素的几何图形',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '采集节点名'
      },
      appType: 3,
      returnVal: 'Feature'
    },
    {
      name: 'Point',
      remark: '返回保证位于几何图形上的表面上的点。',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'Geometry'
      },
      paramDesc: {
        desc1: '一个几何图形'
      },
      example: 'Point(Geometry)',
      appType: 3,
      returnVal: 'List'
    },
    {
      name: 'PointN',
      remark: '返回几何图形的某个特定的结点',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'Geometry',
        param2: 'Int'
      },
      paramDesc: {
        desc1: '几何图形对象',
        desc2: '要返回的节点的索引，其中1是第一个节点'
      },
      example: 'PointN(Geometry,Int)',
      appType: 3,
      returnVal: 'Geometry'
    },
    {
      name: 'GeometryLength',
      remark: '返回几何图形线串的长度',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'Geometry'
      },
      paramDesc: {
        desc1: '一个几何图形'
      },
      example: 'GeometryLength($Geometry) ——>  1 ',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: 'StringLength',
      remark: '返回字符串中的字符数',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '待计算长度的字符串'
      },
      example: 'StingLength("HELLO") ——>  5',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: 'Area',
      remark: '返回几何图形多边形对象的面积。',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'Geometry',
        param2: 'Int'
      },
      paramDesc: {
        desc1: '多边形对象几何图形',
        desc2: '要保留的小数位数'
      },
      example: 'Area(Geometry,2)',
      appType: 3,
      returnVal: 'Double'
    },
    {
      name: 'CommonAttr',
      remark: '获取不可复制的节点的普通组的属性结果值。',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'String',
        param3: 'String',
        param4: 'String'
      },
      paramDesc: {
        desc1: '当前要素的几何图形',
        desc2: '当前要素的树节点',
        desc3: '当前要素的树节点的属性',
        desc4: '当前要素的坐标系'
      },
      appType: 3,
      example: 'CommonAttr($Geometry,"房产1","宗地信息","zbx")',
      returnVal: 'String'
    },
    {
      name: 'Picture',
      remark: '获取不可复制的节点的普通组的图片结果值。',
      type: 4,
      delFlag: 0,
      param: {
        param1: 'Geometry',
        param2: 'String',
        param3: 'String',
        param4: 'String',
        param5: 'Int',
        param6: 'Int'
      },
      paramDesc: {
        desc1: '当前要素的几何图形',
        desc2: '当前要素的树节点',
        desc3: '当前要素的树节点的属性',
        desc4: '当前要素的坐标系',
        desc5: '图片的缩放比例',
        desc6: '当前图片的角标'
      },
      example: 'Picture($Geometry,"宗地","宗地信息","hkb",200,1)',
      returnVal: 'String',
      appType: 3
    }
  ];

  const CLList: FormulaItem[] = [
    {
      name: '$Version',
      remark: '当前版本号',
      type: 98,
      delFlag: 0,
      appType: 3,
      returnVal: 'String'
    }
  ];

  const BLList: FormulaItem[] = [
    {
      name: '$Area',
      remark: '返回当前要素的面积。此函数计算的面积既要考虑当前工程的椭球参数设置，又要遵守面积单位设置。',
      type: 99,
      delFlag: 0,
      example: '$Area  ——> 18',
      returnVal: 'Double',
      appType: 3
    },
    {
      name: '$Geometry',
      remark: '返回当前要素的几何图形，可用于其他功能的处理',
      type: 99,
      delFlag: 0,
      example: '$Geometry  ——> Point(6 50)',
      appType: 3,
      returnVal: 'Geometry'
    }
  ];

  const JZDHList: FormulaItem[] = [
    {
      name: '$PointIndex',
      remark: '取界址点号',
      type: 99,
      delFlag: 0,
      param: {},
      paramDesc: {},
      example: '$PointIndex ——> J1',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: '$LineIndexLength',
      remark: '取界址线的长度(单位：米)',
      type: 99,
      delFlag: 0,
      param: {},
      paramDesc: {},
      appType: 3,
      example: '$LineIndexLength ——> 3米',
      returnVal: 'Double'
    }
  ];

  const SYList: FormulaItem[] = [
    {
      name: '$PointIndex',
      remark: '取界址点号',
      type: 99,
      delFlag: 0,
      param: {},
      paramDesc: {},
      example: '$PointIndex ——> J1',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: 'max',
      remark: '计算数组中的最大值',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'max(3,4)  ——>  4.0'
    },
    {
      name: 'min',
      remark: '计算数组中的最小值',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'min(0,4)  ——>  0.0'
    },
    {
      name: 'sum',
      remark: '求俩个数之和',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'sum(1,4)  ——>  5.0'
    },
    {
      name: 'subtract',
      remark: '求俩个数之差',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'subtract(5,4)  ——>  1.0'
    },
    {
      name: 'multiply',
      remark: '求俩个数之积',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'multiply(5,2)  ——>  10.0'
    },
    {
      name: 'divide',
      remark: '求俩个数之商',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double',
        param2: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '数字类型(小数/整数)'
      },
      returnVal: 'Double',
      appType: 3,
      example: 'divide(6,2)  ——>  3.0'
    },
    {
      name: 'floor',
      remark: '向下取整，返回一个整数',
      type: 1,
      delFlag: 0,
      param: {
        param1: 'Double'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)'
      },
      example: 'floor(3.5) ——>3',
      appType: 3,
      returnVal: 'Int'
    },
    {
      name: 'round',
      remark: '把一个数字舍入为最接近的整数',
      type: 1,
      delFlag: 0,
      appType: 3,
      param: {
        param1: 'Double',
        param2: 'Int',
        param3: 'Int'
      },
      paramDesc: {
        desc1: '数字类型(小数/整数)',
        desc2: '保留几位小数',
        desc3: `0 直接进位;1 舍弃多余的小数;2 往大取值;3 往小取值;4 四舍五入;5 五舍六入`
      },
      example: 'round(5.5,6,1) ——>6.0 | round(2.5,6,1) ——>2.0 |round(5.55,6,1)——>6.0',
      returnVal: 'Int'
    },
    {
      name: 'substring',
      remark: '截取字符串的长度',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'Int',
        param3: 'Int'
      },
      paramDesc: {
        desc1: '要截取字符串的内容，内容为文本',
        desc2: '要截取字符串内容的开始角标，角标0开始',
        desc3: '要截取字符串内容的结束角标，'
      },
      returnVal: 'String',
      appType: 3,
      example: 'substring("你好，中国！",0,2)  ——>  你好'
    },
    {
      name: 'concat',
      remark: '将多个字符串连接成一个字符串。空值被转换为空字符串。其它类型的值(如数字)将被转换为字符串。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'String'
      },
      paramDesc: {
        desc1: '要拼接的第一个字符串',
        desc2: '要拼接的第二个字符串'
      },
      returnVal: 'String',
      appType: 3,
      example: 'concat("你好，","中国！")  ——> 你好，中国！'
    },
    {
      name: 'toUpperCase',
      remark: '将字符串转换为大写字母。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要转换的英文小写字母的字符串，如果为其他类型的字符串，则会输出输入的字符'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toUpperCase("abc")  ——> ABC'
    },
    {
      name: 'toLowerCase',
      remark: '将字符串转换为小写字母。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要转换的英文大写写字母的字符串，如果为其他类型的字符串，则会输出输入的字符'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toUpperCase("ABC")  ——> abc'
    },
    {
      name: 'replace',
      remark: '替换字符串，返回一个字符串、数组或字符串映射替换后的字符串。',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String',
        param2: 'String',
        param3: 'String'
      },
      paramDesc: {
        desc1: '要替换的字符串',
        desc2: '将要替换的字符串',
        desc3: '替换的内容'
      },
      returnVal: 'String',
      appType: 3,
      example: 'replace("你好，你好吗？你好不好","好","ok")  ——> 你ok，你ok吗？你ok不ok'
    },
    {
      name: 'toString',
      remark: '把数字类型改成字符串',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'Double'
      },
      paramDesc: {
        desc1: '要抓换的数字'
      },
      returnVal: 'String',
      appType: 3,
      example: 'toString(1)  ——>  "1"'
    },
    {
      name: 'length',
      remark: '获取字符串的长度',
      type: 2,
      delFlag: 0,
      param: {
        param1: 'String'
      },
      paramDesc: {
        desc1: '要获取字符串的长度'
      },
      returnVal: 'Int',
      example: 'length("你好") ——> 2',
      appType: 3
    },
    {
      name: '$LineIndexLength',
      remark: '取界址线的长度(单位：米)',
      type: 99,
      delFlag: 0,
      param: {},
      paramDesc: {},
      example: '$LineIndexLength ——> 3米',
      returnVal: 'Double',
      appType: 3
    }
  ];

  try {
    const res = await saveForumulation(SYList);
    if (res.code === 200) {
      getDefaultFormulationList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Save formulation failed:', error);
    ElMessage.error('保存公式失败');
  }
};

const handleDeleteAll = async () => {
  const list = formulationList.value.map((item: FormulaItem) => ({
    ...item,
    delFlag: 1
  }));

  try {
    const res = await saveForumulation(list);
    if (res.code === 200) {
      getDefaultFormulationList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Delete all failed:', error);
    ElMessage.error('删除全部失败');
  }
};

const handleDeleteOne = (item: FormData) => {
  const str = `确认删除【${item.name}】公式吗？`;
  ElMessageBox.confirm(str, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const updatedItem = { ...item, delFlag: 1 };
      try {
        const res = await saveForumulation([updatedItem]);
        if (res.code === 200) {
          getDefaultFormulationList();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('Delete failed:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
};

const handleEdit = (item: FormData, type?: boolean) => {
  isCheck.value = !!type;

  Object.assign(form, JSON.parse(JSON.stringify(item)));

  if (form.appType && typeof form.appType === 'string') {
    form.appType = form.appType.split(',');
  }

  form.paramList = [];
  if (item.param && JSON.stringify(item.param) !== '{}') {
    const paramKeys = Object.keys(item.param).sort();
    paramKeys.forEach((key) => {
      form.paramList.push({
        key: Date.now(),
        value: item.param[key]
      });
    });
  }

  form.paramDescList = [];
  if (item.paramDesc && JSON.stringify(item.paramDesc) !== '{}') {
    const paramDescKeys = Object.keys(item.paramDesc).sort();
    paramDescKeys.forEach((key) => {
      form.paramDescList.push({
        key: Date.now(),
        value: item.paramDesc[key]
      });
    });
  }

  addTitle.value = '修改公式';
  open.value = true;
};

const handleAddOne = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (valid) {
      const resUrl = await handleUploadFileServe();
      const paramObj: Record<string, string> = {};
      form.paramList.forEach((item: { key: number; value: string }, index: number) => {
        if (item.value && item.value !== '') {
          const p = `param${index + 1}`;
          paramObj[p] = item.value;
        }
      });
      const paramDescObj: Record<string, string> = {};
      form.paramDescList.forEach((item: { key: number; value: string }, index: number) => {
        if (item.value && item.value !== '') {
          const p = `paramDesc${index + 1}`;
          paramDescObj[p] = item.value;
        }
      });

      const params: FormData = {
        name: form.name,
        remark: form.remark,
        type: form.type,
        delFlag: form.delFlag,
        param: paramObj,
        paramDesc: paramDescObj,
        returnVal: form.returnVal,
        example: form.example,
        childElement: form.childElement,
        examplePic: resUrl,
        appType: Array.isArray(form.appType) ? form.appType.join(',') : form.appType,
        parentVal: form.parentVal
      };

      if (form.id) {
        params.id = form.id;
      }

      const res = await saveForumulation([params]);
      if (res.code === 200) {
        getDefaultFormulationList();
        console.log(res);
        open.value = false;
      } else {
        ElMessage.error(res.msg);
      }
    }
  } catch (error) {
    console.error('Validation or save failed:', error);
    return false;
  }
};

const handleUploadFileServe = async (): Promise<string | null> => {
  if (forumImgFileList.value.length === 0) {
    return null;
  }

  const formData = new FormData();
  forumImgFileList.value.forEach((file) => {
    if (file.raw) {
      formData.append('files', file.raw);
    }
  });

  try {
    const res = await uploadImgae(formData);
    if (res.code === 200) {
      return res.data[0].path;
    } else {
      ElMessage.error(res.msg);
      throw new Error(res.msg);
    }
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};

const handleAddParams = () => {
  form.paramList.push({
    value: '',
    key: Date.now()
  });
};

const handelAddParamsDesc = () => {
  form.paramDescList.push({
    value: '',
    key: Date.now()
  });
};

const handleDeleteParams = (item: { key: number; value: string }) => {
  const index = form.paramList.indexOf(item);
  if (index !== -1) {
    form.paramList.splice(index, 1);
  }
};

const handleDeleteParamDesc = (item: { key: number; value: string }) => {
  const index = form.paramDescList.indexOf(item);
  if (index !== -1) {
    form.paramDescList.splice(index, 1);
  }
};

const handleChangeUploadImg = (uploadFile: UploadUserFile, uploadFiles: UploadUserFile[]): boolean => {
  const whiteList = ['image/png', 'image/jpeg'];
  const isFlag = whiteList.includes(uploadFile.raw?.type || '');
  const isSize = uploadFile.size / 1024 / 1024;

  if (isSize > 10) {
    const index = uploadFiles.findIndex((e) => e.size / 1024 / 1024 === isSize);
    uploadFiles.splice(index, 1);
    ElMessage.warning('上传的文件不能超过10MB');
    return false;
  }

  if (!isFlag) {
    ElMessage.error('只能上传文件类型为.png/.jpg的图片');
    return false;
  }

  if (isSize < 5 && isFlag) {
    forumImgFileList.value = uploadFiles;
  }

  return isFlag && isSize < 5;
};

const handleRemoveUploadImg = (uploadFile: UploadUserFile, uploadFiles: UploadUserFile[]): void => {
  const index = forumImgFileList.value.findIndex((item: UploadUserFile) => item.uid === uploadFile.uid);
  if (index !== -1) {
    forumImgFileList.value.splice(index, 1);
  }
};
const handleCloseDialog = () => {
  dialogVisible.value = false;
};

const handleSubmitFormulation = (expression: string) => {
  expression.value = expression;
  dialogVisible.value = false;
};

onMounted(() => {
  getDefaultFormulationList();
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .card {
    :deep(.el-card__body) {
      overflow: hidden;
    }
  }
}

.upload-card {
  display: flex;
  height: 71px;
  width: 71px;
  line-height: 80px;
  :deep(.el-upload-list--picture-card) {
    height: 71px;
    width: 71px;
    line-height: 80px;
    display: flex;
    .el-upload--picture-card {
      height: 71px;
      width: 71px;
      line-height: 80px;
    }
    .el-upload-list__item {
      margin-top: 4%;
      height: 71px;
      width: 71px;
      line-height: 80px;
    }
  }
}
.app-container {
  overflow: hidden !important;
  .formutation-app {
    height: calc(100% - 70px);
    overflow: hidden;
    .forumation-table {
      height: calc(100% - 70px);
      overflow: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgba(176, 175, 175, 0.5);
      }
      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        background: rgba(248, 248, 248, 0.1);
      }
    }
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 0px;
  }
}

.form-aliag {
  height: 400px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
</style>
