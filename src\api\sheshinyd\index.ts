import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import JSONbig from 'json-bigint'; // 解决超过 16 位数字精度丢失问题
import { NYParcelParams, NYParcelDetailParams, PCParams, ExportParams, SHPParams } from '@/api/sheshinyd/types';

/**
 * 农转用勘界地块列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getNYList(params: NYParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/parcel/search/parcel',
    method: 'get',
    params: params
  });
}

/**
 * 农转用勘界地块详情
 * @param id 地块ID
 * @param dataScope 数据范围
 * @returns {AxiosPromise}
 */
export function getNYParcelInfoById(id: string | number, dataScope: string | number): AxiosPromise<any> {
  return request({
    url: `qjt/xz/parcel/search/detail/${id}/${dataScope}`,
    method: 'get',
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 获取批次--现状
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPCNY(params: PCParams): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/export/xz/list',
    method: 'get',
    params: params
  });
}

/**
 * 获取批次--同口径
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPC(params: PCParams): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/export/list',
    method: 'get',
    params: params
  });
}

/**
 * 导出勘界报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function exportWordNYBG(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: '/qjt/xz/export/word/djdcb',
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 全打包导出勘界结果
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @returns {AxiosPromise}
 */
export function exportNYAll(batchName: string, folderName: string): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/export/all/tkj/20230512?batchName=' + batchName + '&folderName=' + folderName,
    method: 'post'
  });
}

/**
 * 全打包导出勘界结果(新)
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @param xmmc 项目名称
 * @param xz 标识
 * @returns {AxiosPromise}
 */
export function exportNYNewAll(batchName: string, folderName: string, xmmc: string, xz: string): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/export/all/tkj/20230612?batchName=' + batchName + '&folderName=' + folderName + '&xmmc=' + xmmc + '&xz=' + xz,
    method: 'post'
  });
}

/**
 * 全打包导出勘界现状结果
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @returns {AxiosPromise}
 */
export function exportNYAllXz(batchName: string, folderName: string): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/export/all/xz/20230512?batchName=' + batchName + '&folderName=' + folderName,
    method: 'post'
  });
}

/**
 * 导出设施农用shp
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadNYSHP(params: SHPParams): AxiosPromise<any> {
  return request({
    url: '/qjt/xz/project/shp',
    method: 'post',
    data: params
  });
}
