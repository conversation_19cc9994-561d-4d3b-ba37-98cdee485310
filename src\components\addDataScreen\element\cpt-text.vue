<template>
  <div
    style="width: 100%; height: 100%"
    :style="{
      textAlign: option.attribute.textAlign,
      color: option.attribute.textColor,
      fontSize: option.attribute.textSize + 'px',
      fontStyle: option.attribute.fontStyle,
      fontWeight: option.attribute.fontWeight,
      lineHeight: option.attribute.textLineHeight + 'px',
      backgroundColor: option.attribute.bgColor,
      fontFamily: option.attribute.textFamily,
      textDecoration: option.attribute.textDecoration
    }"
    @click="redirect"
  >
    {{ cptData.value }}
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();

// ---定义变量
const cptData = ref({
  value: ''
});
const uuid = ref('');
const taskId = ref('');
//  ---定义emit---
const emit = defineEmits<{
  (e: 'reload'): void;
}>();

// --- 定义方法
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});

const loadData = (id) => {
  if (id != '') {
    taskId.value = id;
  }
  if (props.option.cptDataForm.dataSource == 2) {
    // 表达式必填
    if (!props.option.cptDataForm.apiUrl) {
      ElMessage.warning('表达式不能为空');
      return;
    }
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId.value) {
      parmas.taskId = taskId.value;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        cptData.value.value = res.data;
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    cptData.value.value = JSON.parse(props.option.cptDataForm.dataText).value;
  }
};

const redirect = () => {
  if (props.option.attribute.url) {
    if (props.option.attribute.url.startsWith('view')) {
      router.push(props.option.attribute.url);
      emit('reload');
    } else {
      window.open(props.option.attribute.url);
    }
  }
};

onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});
defineOptions({
  name: 'cpt-text'
});
</script>
