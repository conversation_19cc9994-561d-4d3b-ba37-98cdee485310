{"id": "bdbb048e708754d2017d6fa92b73d0fd", "title": "任务数据大屏", "simpleDesc": "", "bgImg": "", "bgColor": "rgba(2, 7, 35, 1)", "scaleX": 1920, "scaleY": 3000, "components": [{"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-12", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(0, 0, 0, 0)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 675, "cptY": 48, "cptZ": 100, "cptWidth": 572, "cptHeight": 80, "id": "3ae0dae0-132f-11ef-89e4-415f78b0779e"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"任务数据大屏\"}", "dataSource": 2, "pollTime": 0, "apiUrl": "concat($TaskName,\"任务数据大屏\")", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 40, "fontWeight": "bold", "textLineHeight": 80, "textFamily": "微软雅黑", "textAlign": "center", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 679, "cptY": 48, "cptZ": 100, "cptWidth": 570, "cptHeight": 80, "id": "19240150-1331-11ef-89e4-415f78b0779e"}, {"cptTitle": "进度池", "icon": "percent-pond", "cptKey": "cpt-dataV-percentPond", "cptOptionKey": "cpt-dataV-percentPond-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":80}", "dataSource": 2, "pollTime": 0, "apiUrl": "$TaskSchedule", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"borderWidth": 2, "borderRadius": 4, "borderGap": 3, "lineWidth": 1, "lineSpace": 1, "localGradient": true, "colors": ["#01c4f9", "#c135ff"]}}, "cptX": 73, "cptY": 523, "cptZ": 100, "cptWidth": 457, "cptHeight": 39, "id": "3ca19650-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "数值框", "icon": "rect-num", "cptKey": "cpt-rect-num", "cptOptionKey": "cpt-rect-num-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"value\":\"1000\"}", "apiUrl": "$TaskSum", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"fontSize": 40, "padding": 8, "borderColor": "#00FFF4", "bgColor": "rgba(0, 255, 244, 0.3)", "color": "#00FFF4"}}, "cptX": 65, "cptY": 264, "cptZ": 100, "cptWidth": 252, "cptHeight": 66, "id": "3f261e50-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "滚动列表", "icon": "scroll-list", "cptKey": "cpt-dataV-scrollList", "cptOptionKey": "cpt-dataV-scrollList-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "[{\"name\":\"张三\",\"value\":55},{\"name\":\"李四\",\"value\":120},{\"name\":\"王五\",\"value\":78},{\"name\":\"陈小六\",\"value\":66},{\"name\":\"陈龙\",\"value\":80},{\"name\":\"张三丰\",\"value\":45},{\"name\":\"龙啸天\",\"value\":29}]", "apiUrl": "getGroupData(\"cust_name\",\"\",\"count(distinct main_id)\")", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"data": [], "rowNum": 5, "waitTime": 2000, "carousel": "single", "unit": "宗", "sort": true, "valueFormatter": null}}, "cptX": 1384, "cptY": 229, "cptZ": 100, "cptWidth": 462, "cptHeight": 349, "id": "4fa80a40-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "渐变地图", "icon": "map-gc", "cptKey": "cpt-chart-map-gc", "cptOptionKey": "cpt-chart-map-gc-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "[{\"name\":\"南海诸岛\",\"value\":0},{\"name\":\"北京111\",\"value\":0},{\"name\":\"天津\",\"value\":0.5},{\"name\":\"上海\",\"value\":0},{\"name\":\"重庆\",\"value\":0.5},{\"name\":\"河北\",\"value\":8},{\"name\":\"河南\",\"value\":500},{\"name\":\"云南\",\"value\":15},{\"name\":\"辽宁\",\"value\":0.5},{\"name\":\"黑龙江\",\"value\":5},{\"name\":\"湖南\",\"value\":50},{\"name\":\"安徽\",\"value\":0},{\"name\":\"山东\",\"value\":39},{\"name\":\"新疆\",\"value\":4},{\"name\":\"江苏\",\"value\":31},{\"name\":\"浙江\",\"value\":0.5},{\"name\":\"江西\",\"value\":36},{\"name\":\"湖北\",\"value\":1052},{\"name\":\"广西\",\"value\":33},{\"name\":\"甘肃\",\"value\":7},{\"name\":\"山西\",\"value\":9},{\"name\":\"内蒙古\",\"value\":0.5},{\"name\":\"陕西\",\"value\":22},{\"name\":\"吉林\",\"value\":4},{\"name\":\"福建\",\"value\":18},{\"name\":\"贵州\",\"value\":1080},{\"name\":\"广东\",\"value\":300},{\"name\":\"青海\",\"value\":0},{\"name\":\"西藏\",\"value\":0},{\"name\":\"四川\",\"value\":4},{\"name\":\"宁夏\",\"value\":0},{\"name\":\"海南\",\"value\":0.5},{\"name\":\"台湾\",\"value\":0},{\"name\":\"香港\",\"value\":0},{\"name\":\"澳门\",\"value\":0}]", "apiUrl": "getTaskMap()", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"map": "china", "roam": false, "titleText": "数据分布", "titleLeft": "center", "titleTop": 10, "subtext": "", "titleFontSize": 20, "titleColor": "rgba(0, 255, 244, 1)", "subTitleColor": "#aaa", "subTitleFontSize": 13, "seriesName": "", "geoLabelColor": "rgba(0, 0, 0, 1)", "geoLabelSize": 12, "piecesLabel1": "＞100", "piecesColor1": "rgba(255, 194, 95, 1)", "piecesLabel2": "10-100", "piecesColor2": "rgba(251, 170, 255, 1)", "piecesLabel3": "2-9", "piecesColor3": "rgba(249, 255, 117, 1)", "piecesLabel4": "0-1", "piecesColor4": "rgba(105, 240, 255, 1)", "piecesLabel5": "0", "piecesColor5": "rgba(255, 255, 255, 1)"}}, "cptX": 590, "cptY": 179, "cptZ": 100, "cptWidth": 737, "cptHeight": 412, "id": "579b1ad0-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "饼图", "icon": "pie", "cptKey": "cpt-chart-pie", "cptOptionKey": "cpt-chart-pie-option", "cptOption": {"attribute": {"theme": "light", "chartTitle": "", "titleX": 40, "titleY": "top", "titleTextColor": "#ccc", "subtext": "", "subtextColor": "#aaa", "titleFontSize": 18, "orient": "horizontal", "legendTextColor": "#ddd", "legendX": "center", "legendY": "bottom", "borderRadius": 0, "radiusInside": 0, "radiusOutside": 60, "roseType": "false", "legendShow": true, "labelFontSize": 13, "labelColor": "#DDDDDD", "legendFontSize": 12, "labelPosition": "outside", "pieColor": ["rgba(255, 0, 0, 1)", "rgba(85, 255, 0, 1)", "rgba(25, 103, 176, 1)", "rgba(226, 23, 237, 1)"]}, "cptDataForm": {"dataText": "[{\"value\":1048,\"name\":\"＜100㎡\"},{\"value\":735,\"name\":\"101㎡-200㎡\"},{\"value\":580,\"name\":\"201㎡-300㎡\"},{\"value\":484,\"name\":\"＞300㎡\"}]", "dataSource": 2, "pollTime": 0, "apiUrl": "getTaskArea(\"100,200,300\")", "sql": "", "code": "房地一体1700641228438"}}, "cptX": 595, "cptY": 685, "cptZ": 100, "cptWidth": 717, "cptHeight": 355, "id": "6f1a93c0-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "炫酷装饰", "icon": "decoration", "cptKey": "cpt-dataV-decoration", "cptOptionKey": "cpt-dataV-decoration-option", "cptOption": {"attribute": {"decorationType": "dv-decoration-3", "color1": "rgba(0, 255, 244, 0.8)", "color2": "rgba(0, 106, 255, 1)", "text": "若比伤春意未多", "textColor": "#ff0"}}, "cptX": 36, "cptY": 60, "cptZ": 100, "cptWidth": 615, "cptHeight": 61, "id": "ae7c9ef0-1332-11ef-89e4-415f78b0779e"}, {"cptTitle": "炫酷装饰", "icon": "decoration", "cptKey": "cpt-dataV-decoration", "cptOptionKey": "cpt-dataV-decoration-option", "cptOption": {"attribute": {"decorationType": "dv-decoration-3", "color1": "rgba(0, 255, 244, 0.8)", "color2": "rgba(0, 106, 255, 1)", "text": "若比伤春意未多", "textColor": "#ff0"}}, "cptX": 1266, "cptY": 62, "cptZ": 100, "cptWidth": 615, "cptHeight": 61, "id": "9fd76cd0-1333-11ef-89e4-415f78b0779e"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 49, "cptY": 165, "cptZ": 99, "cptWidth": 508, "cptHeight": 436, "id": "ea10bcc0-1333-11ef-89e4-415f78b0779e"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-8", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 580, "cptY": 165, "cptZ": 99, "cptWidth": 761, "cptHeight": 436, "id": "ca694920-134a-11ef-89e4-415f78b0779e"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"每人采集图形总量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 1386, "cptY": 186, "cptZ": 100, "cptWidth": 150, "cptHeight": 40, "id": "66ca8560-134e-11ef-89e4-415f78b0779e"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"预计总量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 16, "fontWeight": "normal", "textLineHeight": 20, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 74, "cptY": 242, "cptZ": 100, "cptWidth": 150, "cptHeight": 20, "id": "9ef893d0-135a-11ef-943a-bbbb2fbc73f8"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"已完成\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(251, 255, 0, 1)", "textSize": 16, "fontWeight": "normal", "textLineHeight": 20, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 74, "cptY": 360, "cptZ": 100, "cptWidth": 150, "cptHeight": 20, "id": "4a543900-135b-11ef-9086-912030ca57ef"}, {"cptTitle": "数值框", "icon": "rect-num", "cptKey": "cpt-rect-num", "cptOptionKey": "cpt-rect-num-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"value\":\"800\"}", "apiUrl": "$TaskComplete", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"fontSize": 40, "padding": 8, "borderColor": "#FBFF00", "bgColor": "rgba(251, 255, 0, 0.3)", "color": "#FBFF00"}}, "cptX": 65, "cptY": 390, "cptZ": 100, "cptWidth": 186, "cptHeight": 66, "id": "5fbe6680-135b-11ef-9086-912030ca57ef"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"未完成\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(255, 115, 0, 1)", "textSize": 16, "fontWeight": "normal", "textLineHeight": 20, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 281, "cptY": 360, "cptZ": 100, "cptWidth": 150, "cptHeight": 20, "id": "bc610000-135b-11ef-9086-912030ca57ef"}, {"cptTitle": "数值框", "icon": "rect-num", "cptKey": "cpt-rect-num", "cptOptionKey": "cpt-rect-num-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"value\":\"200\"}", "apiUrl": "$TaskNoComplete", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"fontSize": 40, "padding": 8, "borderColor": "#FF7300", "bgColor": "rgba(255, 115, 0, 0.3)", "color": "#FF7300"}}, "cptX": 272, "cptY": 390, "cptZ": 100, "cptWidth": 186, "cptHeight": 66, "id": "bfb29c00-135b-11ef-9086-912030ca57ef"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"任务进度\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(38, 150, 255, 1)", "textSize": 16, "fontWeight": "normal", "textLineHeight": 20, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 74, "cptY": 488, "cptZ": 100, "cptWidth": 150, "cptHeight": 20, "id": "f56eb680-135b-11ef-9086-912030ca57ef"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 1360, "cptY": 165, "cptZ": 99, "cptWidth": 508, "cptHeight": 436, "id": "b929e980-135e-11ef-9086-912030ca57ef"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 50, "cptY": 627, "cptZ": 99, "cptWidth": 508, "cptHeight": 436, "id": "9bd03870-135f-11ef-9086-912030ca57ef"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 581, "cptY": 628, "cptZ": 99, "cptWidth": 761, "cptHeight": 436, "id": "69f90e60-1361-11ef-9086-912030ca57ef"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 1361, "cptY": 626, "cptZ": 99, "cptWidth": 508, "cptHeight": 436, "id": "d5e9f5d0-1361-11ef-9086-912030ca57ef"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"任务数据\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 74, "cptY": 185, "cptZ": 100, "cptWidth": 109, "cptHeight": 40, "id": "7954ad50-1362-11ef-9086-912030ca57ef"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"当日图形新增量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 1382, "cptY": 647, "cptZ": 100, "cptWidth": 149, "cptHeight": 40, "id": "c64973c0-1362-11ef-9086-912030ca57ef"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"图形面积统计\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 607, "cptY": 657, "cptZ": 100, "cptWidth": 109, "cptHeight": 40, "id": "7a3a1c90-1368-11ef-bebf-35a043c6a510"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"近7日完成量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 76, "cptY": 1575, "cptZ": 100, "cptWidth": 109, "cptHeight": 40, "id": "ac64d7f0-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 48, "cptY": 1546, "cptZ": 99, "cptWidth": 1817, "cptHeight": 436, "id": "adc2ba40-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "折线图", "icon": "line", "cptKey": "cpt-chart-line", "cptOptionKey": "cpt-chart-line-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"xData\":\"周一,周二,周三,周四,周五,周六,周天\",\"yData\":\"120,200,150,80,70,110,130\"}", "apiUrl": "getTaskReport(\"日\",7)", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"title": "", "subtext": "", "titleLeft": "center", "titleTop": 10, "subtextColor": "#aaa", "yTickShow": true, "yGridLineShow": false, "xLineShow": true, "yLineShow": true, "xTickShow": false, "xLabelShow": true, "yLabelShow": true, "lineColor": "#409eff", "xLabelColor": "rgba(255, 255, 255, 1)", "xLineColor": "rgba(218, 218, 218, 1)", "titleTextColor": "#ccc", "yLabelColor": "rgba(238, 238, 238, 1)", "yLineColor": "rgba(238, 238, 238, 1)", "smooth": false, "areaColor1": "rgba(0, 157, 255, 0.8)", "areaColor2": "rgba(0, 157, 255, 0.4)", "areaColor3": "rgba(0, 157, 255, 0.1)"}}, "cptX": 84, "cptY": 1618, "cptZ": 100, "cptWidth": 1757, "cptHeight": 340, "id": "b9446fd0-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 50, "cptY": 2009, "cptZ": 99, "cptWidth": 1817, "cptHeight": 436, "id": "d02f9800-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "折线图", "icon": "line", "cptKey": "cpt-chart-line", "cptOptionKey": "cpt-chart-line-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"xData\":\"周一,周二,周三,周四,周五,周六,周天\",\"yData\":\"120,200,150,80,70,110,130\"}", "apiUrl": "getTaskReport(\"日\",15)", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"title": "", "subtext": "", "titleLeft": "center", "titleTop": 10, "subtextColor": "#aaa", "yTickShow": true, "yGridLineShow": false, "xLineShow": true, "yLineShow": true, "xTickShow": false, "xLabelShow": true, "yLabelShow": true, "lineColor": "#409eff", "xLabelColor": "rgba(255, 255, 255, 1)", "xLineColor": "rgba(218, 218, 218, 1)", "titleTextColor": "#ccc", "yLabelColor": "rgba(238, 238, 238, 1)", "yLineColor": "rgba(238, 238, 238, 1)", "smooth": false, "areaColor1": "rgba(0, 157, 255, 0.8)", "areaColor2": "rgba(0, 157, 255, 0.4)", "areaColor3": "rgba(0, 157, 255, 0.1)"}}, "cptX": 87, "cptY": 2071, "cptZ": 100, "cptWidth": 1757, "cptHeight": 340, "id": "d4591d70-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"近15日完成量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 78, "cptY": 2030, "cptZ": 100, "cptWidth": 109, "cptHeight": 40, "id": "d73a6b70-1813-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 49, "cptY": 1089, "cptZ": 99, "cptWidth": 1817, "cptHeight": 436, "id": "b87591a0-1814-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"人员采集图形总量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 71, "cptY": 1109, "cptZ": 100, "cptWidth": 147, "cptHeight": 40, "id": "d27f37e0-1814-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "滚动表格", "icon": "scroll-table", "cptKey": "cpt-dataV-scrollTable", "cptOptionKey": "cpt-dataV-scrollTable-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "[[\"张三\",24,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四\",14,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四2\",14,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四3\",19,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四5\",19,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四6\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四8\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四9\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四10\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四11\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"]]", "apiUrl": "getCustRuleInfo()", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"columns": "[{\"title\":\"姓名\",\"width\":100},{\"title\":\"采集总量\",\"width\":100},{\"title\":\"图形\",\"width\":null}]", "rowNum": 4, "headerBGC": "rgba(0, 255, 244, 0.5)", "oddRowBGC": "rgba(0, 255, 244, 0.1)", "evenRowBGC": "rgba(255, 255, 255, 0)", "waitTime": 2000, "headerHeight": 35, "indexHeader": "序号", "carousel": "single", "hoverPause": true, "index": true}}, "cptX": 85, "cptY": 1159, "cptZ": 100, "cptWidth": 1765, "cptHeight": 349, "id": "d70cfd60-1814-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "滚动表格", "icon": "scroll-table", "cptKey": "cpt-dataV-scrollTable", "cptOptionKey": "cpt-dataV-scrollTable-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "[[\"宗地信息\",24,\"20\"],[\"权利人\",14,\"20\"],[\"属性组1\",14,\"20\"],[\"属性组2\",19,\"20\"],[\"属性组2\",19,\"20\"],[\"属性组3\",29,\"20\"],[\"属性组4\",29,\"0\"],[\"属性组5\",29,\"20\"],[\"属性组6\",29,\"20\"],[\"属性组7\",29,\"20\"]]", "apiUrl": "getTaskGroupInfo()", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"columns": "[{\"title\":\"属性组名称\"},{\"title\":\"属性组应采集量\"},{\"title\":\"属性组未采集量\"}]", "rowNum": 4, "headerBGC": "rgba(0, 255, 244, 0.5)", "oddRowBGC": "rgba(0, 255, 244, 0.1)", "evenRowBGC": "rgba(255, 255, 255, 0)", "waitTime": 2000, "headerHeight": 35, "indexHeader": "序号", "carousel": "single", "hoverPause": true, "index": true}}, "cptX": 67, "cptY": 695, "cptZ": 100, "cptWidth": 471, "cptHeight": 349, "id": "60d534e0-1815-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"属性完成情况统计\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 73, "cptY": 648, "cptZ": 100, "cptWidth": 147, "cptHeight": 40, "id": "65fbb7f0-1815-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "边框", "icon": "border", "cptKey": "cpt-dataV-border", "cptOptionKey": "cpt-dataV-border-option", "cptOption": {"attribute": {"borderType": "dv-border-box-13", "borderColor1": "rgba(0, 255, 244, 1)", "borderColor2": "rgba(0, 255, 244, 1)", "backgroundColor": "rgba(1, 26, 24, 0.5)", "borderTitle": "标题1", "titleWidth": 250, "dur": 3, "reverse": false}}, "cptX": 52, "cptY": 2470, "cptZ": 99, "cptWidth": 1817, "cptHeight": 436, "id": "a6c04e70-1817-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "折线图", "icon": "line", "cptKey": "cpt-chart-line", "cptOptionKey": "cpt-chart-line-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "{\"xData\":\"周一,周二,周三,周四,周五,周六,周天\",\"yData\":\"120,200,150,80,70,110,130\"}", "apiUrl": "getTaskReport(\"日\",30)", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"title": "", "subtext": "", "titleLeft": "center", "titleTop": 10, "subtextColor": "#aaa", "yTickShow": true, "yGridLineShow": false, "xLineShow": true, "yLineShow": true, "xTickShow": false, "xLabelShow": true, "yLabelShow": true, "lineColor": "#409eff", "xLabelColor": "rgba(255, 255, 255, 1)", "xLineColor": "rgba(218, 218, 218, 1)", "titleTextColor": "#ccc", "yLabelColor": "rgba(238, 238, 238, 1)", "yLineColor": "rgba(238, 238, 238, 1)", "smooth": false, "areaColor1": "rgba(0, 157, 255, 0.8)", "areaColor2": "rgba(0, 157, 255, 0.4)", "areaColor3": "rgba(0, 157, 255, 0.1)"}}, "cptX": 84, "cptY": 2540, "cptZ": 100, "cptWidth": 1757, "cptHeight": 340, "id": "aa511650-1817-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "文字框", "icon": "text", "cptKey": "cpt-text", "cptOptionKey": "cpt-text-option", "cptOption": {"cptDataForm": {"dataText": "{\"value\":\"近30日完成量\"}", "dataSource": 1, "pollTime": 0, "moduleId": "", "apiUrl": "", "sql": ""}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "bold", "textLineHeight": 40, "textFamily": "黑体", "textAlign": "left", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 77, "cptY": 2501, "cptZ": 100, "cptWidth": 109, "cptHeight": 40, "id": "ad0b5450-1817-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "滚动表格", "icon": "scroll-table", "cptKey": "cpt-dataV-scrollTable", "cptOptionKey": "cpt-dataV-scrollTable-option", "cptOption": {"cptDataForm": {"dataSource": 2, "pollTime": 0, "dataText": "[[\"人员1\",24,\"20\"],[\"人员2\",14,\"20\"],[\"人员3\",14,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"人员4\",19,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"人员5\",19,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四6\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四8\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四9\",29,\"\"],[\"李四10\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"],[\"李四11\",29,\"宗地10，房产20，楼层25，阳台18，楼梯12，滴水线10，四至40，附属设施20\"]]", "apiUrl": "getGroupData(\"cust_name\",\"1日\",\"count(distinct main_id)\")", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"columns": "[{\"title\":\"人员名称\",\"width\":200},{\"title\":\"新增量\",\"width\":200}]", "rowNum": 4, "headerBGC": "rgba(0, 255, 244, 0.5)", "oddRowBGC": "rgba(0, 255, 244, 0.1)", "evenRowBGC": "rgba(255, 255, 255, 0)", "waitTime": 2000, "headerHeight": 35, "indexHeader": "序号", "carousel": "single", "hoverPause": true, "index": true}}, "cptX": 1382, "cptY": 699, "cptZ": 100, "cptWidth": 471, "cptHeight": 349, "id": "e4dae580-1817-11ef-b0e7-21cd3f2cb9ba"}, {"cptTitle": "任务切换", "icon": "", "cptKey": "cpt-select", "cptOptionKey": "cpt-select-option", "cptOption": {"cptDataForm": {"dataText": "[{\"value\":\"选项1\",\"label\":\"黄金糕\"},{\"value\":\"选项2\",\"label\":\"双皮奶\"},{\"value\":\"选项3\",\"label\":\"蚵仔煎\"},{\"value\":\"选项4\",\"label\":\"龙须面\"},{\"value\":\"选项5\",\"label\":\"北京烤鸭\"}]", "dataSource": 2, "pollTime": 0, "apiUrl": "$TaskSum", "sql": "", "code": "房地一体1700641228438"}, "attribute": {"url": "", "textColor": "rgba(0, 255, 244, 1)", "textSize": 18, "fontWeight": "normal", "textLineHeight": 40, "textFamily": "微软雅黑", "textAlign": "right", "fontStyle": "normal", "textDecoration": "none", "bgColor": "rgba(255, 255, 255, 0)"}}, "cptX": 1726, "cptY": 19, "cptZ": 100, "cptWidth": 138, "cptHeight": 40, "id": "4bfc7820-1cda-11ef-ae70-eb3fcbcec62a"}], "designImgPath": null, "state": 1, "viewCode": "", "countView": null, "createUser": null, "moduleId": 588, "companyId": "9bd5578788ae4f8f9577b56ab8baf088", "createTime": null, "updateTime": 0, "version": "0.9.0"}