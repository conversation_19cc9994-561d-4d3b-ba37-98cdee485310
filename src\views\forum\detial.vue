<!-- 论坛审核详情页 -->
<template>
  <container-card>
    <el-button type="success" plain icon="ChatDotSquare" size="small" @click="handleAddForum('评论')">评论</el-button>
    <div class="forum-detial-info-one">
      <div class="one-left">
        <span class="text">查看：1000</span>
        <span style="padding: 8px; color: #8291a9">|</span>
        <span class="text">回复：500</span>
      </div>
      <div class="one-right">
        <span class="title" v-if="titleType === 1" style="color: var(--current-color)">[系统建议]</span>
        <span class="title" v-if="titleType === 2" style="color: #ff3d57">[BUG反馈]</span>
        <span class="title" v-if="titleType === 3" style="color: #09b66d">[交流互动]</span>
        <span class="title" v-if="titleType === 4" style="color: #fb00ff">[行业资讯]</span>
        <span class="text" @click="() => {}">
          {{ forumList[0]?.titleInfo || '' }}
        </span>
      </div>
    </div>
    <!-- 第二部分 主要内容 -->
    <div class="forum-detial-info-two clearfix" :class="{ 'is-reply': item.quoteId }" v-for="(item, index) in forumList" :key="item.id">
      <div class="two-left">
        <authImg
          v-if="item.avatar != null && item.avatar !== ''"
          :authSrc="item.avatar.startsWith('http') ? item.avatar : `${baseUrl}${item.avatar}?att=1`"
          width="100px"
          height="100px"
          radios="50%"
          style="margin-top: 24px"
        />
        <img :src="defaultAvatar" class="user-avatar" v-else />
      </div>
      <div class="two-right">
        <div class="two-title">
          <div class="title-text">
            <span class="title">发表于</span>
            <span class="text">{{ formatDateYmdhm(item.createTime) }}</span>
          </div>
          <div class="title-ceng">{{ item.seq + 1 }}层</div>
        </div>
        <!-- 引用部分 -->
        <div class="quote-main" v-if="item.quoteName && item.quoteCreateTime">
          <div class="quote-content">
            <div class="quote-content-title">
              <span>{{ item.quoteName }}</span>
              <span>：发表于 </span>
              <span>{{ formatDateYmdhm(item.quoteCreateTime) }}</span>
            </div>
            <div class="content" @click="handlePosition(item, index)">
              <div class="text-main">
                {{ item.quoteContents }}
              </div>
            </div>
          </div>
        </div>
        <!-- 评论部分 -->
        <div class="comment-main" :ref="(el) => (contentRefs[index] = el)">
          <div class="text">{{ item.contents }}</div>
          <div class="img" v-if="item.picList">
            <div v-for="ite in item.picList" :key="ite.path" style="margin: 0 8px">
              <div v-if="ite.type === 'png'">
                <authImg
                  :authSrc="ite.path.startsWith('http') ? ite.path : `${baseUrl}${ite.path}?att=1`"
                  :width="'128px'"
                  :height="'128px'"
                  :radios="'4px'"
                >
                </authImg>
              </div>
              <div v-if="ite.type === 'mp4'">
                <the-forum-video-play :videoPath="ite.path" />
              </div>
            </div>
          </div>
          <!-- 评论底部回复部分 -->
          <div class="comment-repaly">
            <div class="comment-font">
              <div class="icon">
                <el-icon><ChatRound /></el-icon>
              </div>
              <div class="text" @click="handleAddForum('回复', item)">回复</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <el-pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        @current-change="getForumTableList"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
    <!-- 评论数据 -->
    <el-dialog v-model="forumDialogFormVisible" :title="fourmTitle" @closed="handleCloseForumForm" :close-on-click-modal="false" width="500px">
      <el-form :model="forumForm" ref="forumFormRef" :rules="forumRules">
        <el-form-item label="评论内容" :label-width="formLabelWidth" prop="contents">
          <el-input
            type="textarea"
            v-model="forumForm.contents"
            autocomplete="off"
            :autosize="{ minRows: 6, maxRows: 20 }"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="评论图片" :label-width="formLabelWidth">
          <el-upload
            list-type="picture-card"
            :on-change="handleChangeUploadImg"
            :on-remove="handleRemoveUploadImg"
            :action="`${base}/system/option/multi/upload`"
            :auto-upload="false"
            :file-list="forumImgFileList"
            accept=".jpg,.png,.mp4"
            :headers="headers"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="forumDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitForum">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </container-card>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeUnmount, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, type FormInstance, type UploadFile, type UploadUserFile } from 'element-plus';
import { Plus, ChatDotSquare, ChatRound } from '@element-plus/icons-vue';
import { getForumCheckList, editForum, addForum, uploadImgae } from '@/api/forum';
import { ForumData, ForumQuery } from '@/api/forum/types';
import authImg from '@/components/authImg/index.vue';
import { getToken } from '@/utils/auth';
import defaultAvatar from '@/assets/images/default-avatar2.png';
import TheForumVideoPlay from './components/TheForumVideoPlay.vue';

// 接口定义
interface ForumItem {
  id: string | number;
  titleInfo: string;
  titleType: number;
  contents: string;
  createTime: string;
  createBy: string;
  seq: number;
  picUrls?: string[];
  picUrl?: string;
  avatar?: string;
  picList?: PicListItem[];
  quoteName?: string;
  quoteCreateTime?: string;
  quoteContents?: string;
  quoteId?: string | number;
  quoteSeq?: number;
}

interface PicListItem {
  path: string;
  type: string;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  total: number;
}

interface ForumFormData {
  picUrl: string;
  contents: string;
  titleInfo: string;
  titleType: string | number;
}

// 定义响应式数据
const route = useRoute();
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
const base = import.meta.env.VITE_APP_BASE_API;
const isRelay = ref(false);
const currentRelayItem = ref<ForumItem | null>(null);
const forumList = ref<ForumItem[]>([{ id: '', titleInfo: '', titleType: 1, contents: '', createTime: '', createBy: '', seq: 0 }]);
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0
});
const picList = ref<PicListItem[]>([]);
const forumDialogFormVisible = ref(false);
const formLabelWidth = '120px';
const forumForm = reactive<ForumFormData>({
  picUrl: '',
  contents: '',
  titleInfo: '',
  titleType: ''
});
const forumImgFileList = ref<UploadUserFile[]>([]);
const headers = computed(() => ({
  'Authorization': 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
}));
const forumRules = reactive({
  contents: [{ required: true, message: '请填写评论内容', trigger: 'blur' }]
});
const fourmTitle = ref('');
const countdown = ref(5);
const timer = ref<number | null>(null);
const titleType = ref(1);
const contentRefs = ref<any[]>([]);
const forumFormRef = ref<FormInstance>();

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
const formatDateYmdhm = (dateString?: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 处理字符串截断
const spanRule = (str: string, len: number): string => {
  if (!str) return '';
  if (str.length <= len) return str;
  return str.substring(0, len) + '...';
};

/**
 * 获取主题评论列表
 */
const getForumTableList = async () => {
  try {
    const titleId = route.query.titleId as string;
    titleType.value = Number(route.query.titleType || 1);
    const params: ForumQuery = {
      titleId,
      checkStatus: 1,
      pageSize: queryParams.pageSize,
      pageNum: queryParams.pageNum
    };

    const res = await getForumCheckList(params);
    if (res.code === 200) {
      forumList.value = res.data.rows;
      forumList.value.forEach((item) => {
        const piclist: PicListItem[] = [];
        if (item.picUrls && item.picUrls.length > 0) {
          item.picUrls.forEach((ite) => {
            // 检查文件扩展名
            const str = ite.substring(ite.lastIndexOf('.') + 1).toLowerCase();
            const obj: PicListItem = { path: '', type: '' };
            if (str === 'jpg' || str === 'jpeg' || str === 'png') {
              obj.path = ite;
              obj.type = 'png';
              piclist.push(obj);
            } else if (str === 'mp4') {
              obj.path = ite;
              obj.type = 'mp4';
              piclist.push(obj);
            }
          });
        }
        item.picList = piclist;
      });
      queryParams.total = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取论坛列表失败', error);
  }
};

/**
 * 点击评论或者回复的按钮
 * @param str 按钮文字
 * @param item 评论数据
 */
const handleAddForum = (str: string, item?: ForumItem) => {
  if (str === '回复' && item) {
    isRelay.value = true;
    currentRelayItem.value = item;
    fourmTitle.value = `回复${item.createBy}`;
    forumDialogFormVisible.value = true;
  } else {
    fourmTitle.value = '评论主题';
    forumDialogFormVisible.value = true;
  }
};

/**
 * 提交评论的内容
 */
const handleSubmitForum = async () => {
  if (!forumFormRef.value) return;

  await forumFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const resUrl = await handleUploadFileServe();
        let params: ForumData = {};

        if (isRelay.value && currentRelayItem.value) {
          // 回复的参数
          params = {
            picUrl: resUrl,
            contents: forumForm.contents,
            titleId: route.query.titleId,
            titleInfo: currentRelayItem.value.titleInfo,
            titleType: currentRelayItem.value.titleType,
            quoteId: currentRelayItem.value.id
          };
        } else {
          // 评论的参数
          params = {
            picUrl: resUrl,
            contents: forumForm.contents,
            titleId: route.query.titleId,
            titleInfo: forumList.value[0]?.titleInfo || '',
            titleType: forumList.value[0]?.titleType || 1
          };
        }

        const res = await addForum(params);
        if (res.code === 200) {
          ElMessage.success('评论成功');
          forumDialogFormVisible.value = false;
          getForumTableList(); // 刷新列表
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('提交评论失败', error);
      }
    }
  });
};

/**
 * 上传图片
 * @param file 文件
 * @param fileList 文件列表
 * @returns 是否上传成功
 */
const handleChangeUploadImg = (file: UploadFile, fileList: UploadUserFile[]) => {
  const whiteList = ['image/png', 'image/jpeg', 'video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb'];
  const isFlag = whiteList.includes(file.raw?.type || '');
  const isSize = (file.size || 0) / 1024 / 1024;

  if (isSize > 10) {
    const index = fileList.findIndex((e) => (e.size || 0) / 1024 / 1024 === isSize);
    if (index !== -1) {
      fileList.splice(index, 1);
    }
    ElMessage.warning('上传的文件不能超过10MB');
    return false;
  }

  if (!isFlag) {
    ElMessage.error('只能上传图片和视频文件');
    return false;
  }

  if (file.raw?.type === 'video/mp4') {
    const video = document.createElement('video');
    video.src = file.url || '';

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    video.crossOrigin = 'anonymous';
    video.currentTime = 1; // 第一帧

    video.oncanplay = () => {
      canvas.width = video.clientWidth || 320;
      canvas.height = video.clientHeight || 320;

      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const videoFirstimgsrc = canvas.toDataURL('image/png');

        if (file.url !== undefined) {
          file.url = videoFirstimgsrc;
        }

        video.remove();
        canvas.remove();
      }
    };

    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration;
      if (Math.floor(duration) > 30) {
        ElMessage.error('上传的视频不能超过30S');
        return false;
      }
    });
  }

  if (isSize < 5 && isFlag) {
    forumImgFileList.value = fileList;
  }

  return isFlag && isSize < 5;
};

/**
 * 删除图片
 * @param file 文件
 */
const handleRemoveUploadImg = (file: UploadFile) => {
  const index = forumImgFileList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    forumImgFileList.value.splice(index, 1);
  }
};

// 上传文件到服务器
const handleUploadFileServe = async (): Promise<string> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    forumImgFileList.value.forEach((file) => {
      if (file.raw) {
        formData.append('files', file.raw);
      }
    });

    uploadImgae(formData)
      .then((res) => {
        if (res.code === 200) {
          forumForm.picUrl = '';
          res.data.forEach((item: any) => {
            forumForm.picUrl += `${item.path};`;
          });
          resolve(forumForm.picUrl);
        } else {
          ElMessage.error(res.msg);
          reject();
        }
      })
      .catch(reject);
  });
};

// 关闭添加论坛对话框时的内容清除数据
const handleCloseForumForm = () => {
  forumDialogFormVisible.value = false;
  Object.assign(forumForm, {
    picUrl: '',
    contents: '',
    titleInfo: '',
    titleType: ''
  });
  forumImgFileList.value = [];
  isRelay.value = false;
  currentRelayItem.value = null;
};

// 再次请求
const getForumList = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    const titleId = route.query.titleId as string;
    const params = {
      titleId,
      checkStatus: 1,
      pageSize: queryParams.pageSize,
      pageNum: queryParams.pageNum
    };

    getForumCheckList(params)
      .then((res) => {
        if (res.code === 200) {
          forumList.value = res.data.rows;
          forumList.value.forEach((item) => {
            const piclist: PicListItem[] = [];
            if (item.picUrls && item.picUrls.length > 0) {
              item.picUrls.forEach((ite) => {
                // 检查文件扩展名
                const str = ite.substring(ite.lastIndexOf('.') + 1).toLowerCase();
                const obj: PicListItem = { path: '', type: '' };
                if (str === 'jpg' || str === 'jpeg' || str === 'png') {
                  obj.path = ite;
                  obj.type = 'png';
                  piclist.push(obj);
                } else if (str === 'mp4') {
                  obj.path = ite;
                  obj.type = 'mp4';
                  piclist.push(obj);
                }
              });
            }
            item.picList = piclist;
          });
          queryParams.total = res.data.total;
          resolve();
        } else {
          ElMessage.error(res.msg);
          reject();
        }
      })
      .catch(reject);
  });
};

// 点击文字处理跳转到对应的位置锚点
const handlePosition = async (item: ForumItem, index: number) => {
  if (!item.quoteSeq) return;

  // 计算当前数据在10条数据中的位置
  const currentPosition = item.quoteSeq % queryParams.pageSize;
  let contentRef = 0;

  if (item.quoteSeq != null) {
    contentRef = currentPosition;
  } else if (item.quoteSeq === 0) {
    contentRef = 0;
  }

  // 计算当前的页数
  const currentPage = Math.floor(item.quoteSeq / queryParams.pageSize) + 1;
  queryParams.pageNum = currentPage;

  await getForumList();

  if (contentRefs.value[contentRef] && contentRefs.value[contentRef].innerText.replace(/[\r\n]/g, '') === `${item.quoteContents}回复`) {
    contentRefs.value[contentRef].scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest'
    });

    let count = countdown.value;

    if (timer.value) {
      clearInterval(timer.value);
    }

    timer.value = setInterval(() => {
      if (count === 1) {
        if (timer.value) {
          clearInterval(timer.value);
          timer.value = null;
        }
        if (contentRefs.value[contentRef]) {
          contentRefs.value[contentRef].style.backgroundColor = '#ffffff';
        }
      } else {
        if (contentRefs.value[contentRef]) {
          contentRefs.value[contentRef].style.backgroundColor = '#e8f4ff';
        }
        count--;
      }
    }, 500);
  }
};

// 生命周期钩子
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});

// 初始化
getForumTableList();
</script>

<style lang="scss" scoped>
.forum-detial-info-one {
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;
  height: 44px;
  border-top: 1px solid #dbe7ee;
  border-bottom: 4px solid #edf4fb;
  margin-top: 16px;
  width: 100%;
  .one-left {
    width: 15%;
    height: 44px;
    min-width: 120px;
    line-height: 40px;
    text-align: center;
    flex: 1;
    background: #f6f7f8;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .text {
      width: 73px;
      height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular-Regular, PingFangSC-Regular;
      font-weight: 400;
      color: #8291a9;
      line-height: 22px;
    }
  }
  .one-right {
    width: 85%;
    flex: 7;
    height: 44px;
    line-height: 44px;
    display: inline-flex;
    // justify-content: center;
    align-content: center;

    .title {
      padding-left: 24px;
      width: 100px;
      line-height: 44px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .text {
      font-size: 16px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #161d26;
      line-height: 22px;
      padding-left: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 600px;
      overflow-wrap: break-word;
      line-height: 44px;
    }
  }
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}
.clearfix:after {
  clear: both;
}
.clearfix {
  *zoom: 1;
}
.forum-detial-info-two {
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: stretch;
  border-bottom: 4px solid #edf4fb;
  width: 100%;
  margin: 16px 0;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  // 根据层级设置不同的样式
  &:first-child {
    // 原帖样式
    background: #fff;
    margin-top: 24px;
    border: 1px solid #e4e7ed;
  }

  &:not(:first-child) {
    // 评论和回复的样式
    background: #fafafa;
    margin-left: 24px;
    width: calc(100% - 24px);
  }

  .two-left {
    width: 15%;
    line-height: 40px;
    text-align: center;
    flex: 1;
    display: flex;
    min-width: 120px;
    align-items: flex-start;
    justify-content: center;
    background: #f6f7f8;
    border-radius: 8px 0 0 8px;
    padding: 16px 0;

    .user-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-top: 24px;
      border: 2px solid #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .user-name {
      margin-top: 8px;
      font-size: 14px;
      font-family: PingFangSC-Regular-Regular, PingFangSC-Regular;
      font-weight: 400;
      color: #8291a9;
    }
  }

  .two-right {
    width: 85%;
    height: auto;
    flex: 7;
    background: #fff;
    border-radius: 0 8px 8px 0;

    .two-title {
      display: flex;
      height: 40px;
      justify-content: flex-start;
      align-content: center;
      align-items: center;
      border-bottom: 1px solid #edf4fb;
      padding: 8px 0;

      .title-text {
        width: 90%;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #8291a9;
        line-height: 20px;

        .title {
          padding-left: 24px;
        }

        .text {
          padding-left: 8px;
        }
      }

      .title-ceng {
        width: 10%;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #161d26;
        line-height: 20px;
        display: inline-flex;
        justify-content: flex-end;
        padding-right: 16px;
      }
    }

    .quote-main {
      .quote-content {
        margin: 12px 24px;
        background-color: #f6f7f8;
        border-radius: 8px;
        border-left: 4px solid var(--current-color);

        .quote-content-title {
          margin: 8px 16px;
          padding-top: 8px;
          font-size: 12px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #8291a9;
          line-height: 22px;
        }

        .content {
          width: 100%;
          padding: 0 16px 16px;
          font-size: 14px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #161d26;
          line-height: 22px;

          .text-main {
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            padding-right: 10px;
          }
        }
      }
    }

    .comment-main {
      margin: 12px 24px;
      overflow: hidden;

      .text {
        font-size: 16px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #161d26;
        margin-bottom: 8px;
        line-height: 1.6;
      }

      .img {
        margin-top: 16px;
        margin-bottom: 20px;
        display: flex;
        align-content: center;
        flex-wrap: wrap;
        gap: 12px;
      }
    }

    .comment-repaly {
      border-top: 1px dashed #dbe7ee;
      padding: 12px 24px;
      background: #fafbfc;
      border-radius: 0 0 8px 0;

      .comment-font {
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.3s;

        .icon {
          width: 20px;
          height: 20px;
          overflow: hidden;
          color: var(--current-color);
          margin-right: 4px;

          &:hover {
            &::deep {
              .svg-icon {
                transform: translateX(-30px);
                z-index: 999;
              }
            }
          }
        }

        .text {
          font-size: 14px;
          font-family: PingFangSC-Regular-Regular, PingFangSC-Regular;
          font-weight: 400;
          color: var(--current-color);
          line-height: 22px;
        }

        &:hover {
          opacity: 1;
        }
      }
    }
  }
}

// 添加一个新的样式类用于回复的样式
.forum-detial-info-two.is-reply {
  margin-left: 48px;
  width: calc(100% - 48px);
  background: #fcfcfc;

  .two-right {
    background: #fcfcfc;
  }
}
</style>
