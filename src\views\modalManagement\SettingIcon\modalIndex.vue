<!-- 设置模块图标 -->
<template>
  <div class="">
    <el-dialog title="设置图标" v-model="dialogVisible" width="40%" :before-close="handleClose" @opened="handleIconOpen">
      <div class="icon-contianer">
        <div class="icon-custom-contianer" @click="handleOpenCropperImage">
          <span class="custom-text">自定义</span>
        </div>
        <div @click="handleCurrentSelect(customPicItem)">
          <div
            class="kuang"
            v-if="cropperImageUrl != null && cropperImageUrl !== ''"
            :style="{ 'background-color': customSelected ? '#f6f7f8' : '' }"
          >
            <custom-img :authSrc="`${baseUrl}${cropperImageUrl}?att=1`" :width="'36px'" :height="'36px'" :radios="'4px'" />
          </div>
        </div>
        <div v-for="item in iconList" :key="item.name" @click="handleCurrentSelect(item)">
          <div class="kuang" :style="{ 'background-color': item.selected ? '#f6f7f8' : '' }">
            <svg-icon-show
              class-name="svg-item"
              :icon-class="item.name"
              :style="{ color: item.selected && isShow ? 'var(--current-color)' : '#333' }"
            />
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 自定义照片的剪裁照片 -->
    <cropper-image ref="cropperImageRef" :iconName="iconName"></cropper-image>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, defineProps, defineEmits, Ref, nextTick } from 'vue';
import { useUserStore } from '@/store/modules/user';
import cropperImage from './cropperImage.vue';
import customImg from './customImg.vue';
import svgIconShow from '../svgIcon/index.vue';
import { useModalStore } from '@/store/modules/modal';

const userStore = useUserStore();
const modalStore = useModalStore();

// 定义 props
const props = defineProps<{
  iconVisible: boolean;
  defaultList: { name: string; selected: boolean; iconName?: string }[];
  iconSelected: string;
  isShow: boolean;
  iconName: string;
}>();
const dialogVisible = computed({
  get() {
    return props.iconVisible;
  },
  set(value) {
    // 触发关闭事件
    emits('closeSettingIcon');
  }
});

// 定义 emits
const emits = defineEmits<{
  (e: 'closeSettingIcon'): void;
  (e: 'submitIcon', icon: string): void;
}>();

// 回显图片的路径
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/downloadone/');
// 存在 session 中的图标地址
const cropperImageUrl = ref<string | null>(sessionStorage.getItem('cropperIcon'));
// 当前选中的图标，传递给父组件的值
const currentIcon = ref<string>('');
// 当前自定义的整个数据的内容
const customPicItem = ref<{ name: string } | null>(JSON.parse(sessionStorage.getItem('customPic') || 'null'));
// 自定义选择项目
const customSelected = ref<boolean>(false);

// 引用 cropperImage 组件
const cropperImageRef = ref<InstanceType<typeof cropperImage> | null>(null);

// 计算属性
const customPic = computed(() => modalStore.customPic);

const iconList = ref(props.defaultList);

// 监听 customPic 变化
watch(
  customPic,
  (val) => {
    if (props.iconName === '自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('customPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '模块自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('modalCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '属性组自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('GroupCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    } else if (props.iconName === '树图标自定义照片') {
      const item = JSON.parse(sessionStorage.getItem('TreeCustomPic') || 'null');
      if (item) {
        customPicItem.value = { ...item };
        cropperImageUrl.value = item.name;
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听 iconSelected 变化
watch(
  () => props.iconSelected,
  (val) => {
    props.defaultList.forEach((item) => {
      item.selected = item.name === val;
    });
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  handleGetCropperIamge();
});

// 方法定义
const handleIconOpen = () => {
  if (props.iconName === '自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('customPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '模块自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('modalCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '属性组自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('GroupCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  } else if (props.iconName === '树图标自定义照片') {
    const item = JSON.parse(sessionStorage.getItem('TreeCustomPic') || 'null');
    if (item) {
      customPicItem.value = { ...item };
      cropperImageUrl.value = item.name;
    }
  }
};

// 将 session 中存的图片取出，展示到数据中
const handleGetCropperIamge = () => {
  const url = sessionStorage.getItem('cropperIcon');
  cropperImageUrl.value = url;
  const customPicItemData = sessionStorage.getItem('customPic');
  customPicItem.value = JSON.parse(customPicItemData || 'null');
};

const handleClose = () => {
  emits('closeSettingIcon');
  props.defaultList.forEach((defaul) => {
    defaul.selected = false;
  });
};

// 打开自定义剪裁图片框
const handleOpenCropperImage = () => {
  if (cropperImageRef.value) {
    cropperImageRef.value.editCropper();
  }
};

// 当前选中的模块图标
const handleCurrentSelect = (item: any) => {
  if (item.iconName === props.iconName) {
    customSelected.value = true;
    iconList.value.forEach((defaul) => {
      defaul.selected = false;
    });
  } else {
    iconList.value.forEach((defaul) => {
      defaul.selected = false;
    });
    item.selected = true;
    customSelected.value = false;
  }

  currentIcon.value = item.name;
};

// 选中图片的提交按钮
const handleSubmit = () => {
  emits('submitIcon', currentIcon.value);
  // 关闭弹框
  handleClose();
};
</script>

<style lang="scss" scoped>
.icon-contianer {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .icon-custom-contianer {
    width: 56px;
    height: 56px;
    line-height: 20px;
    border-radius: 4px;
    background-color: #ffffff;
    text-align: center;
    border: 1px dashed #8291a980;
    margin: 8px 0 8px 8px;
    .custom-text {
      height: 56px;
      line-height: 56px;
      color: rgba(130, 145, 169, 1);
      font-size: 12px;
      text-align: center;
      font-family: PingFangSC-regular;
    }
  }
}

.kuang {
  width: 56px;
  height: 56px;
  margin: 8px 0 8px 8px;
  padding: 5px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: #f6f7f8;
    :deep(&) {
      .svg-icon {
        // transform: translateX(-50px);
        z-index: 999;
      }
    }
  }
  .svg-item {
    width: 36px;
    height: 36px;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    // filter:drop-shadow(50px 0 #0081ff);
  }
}
</style>
