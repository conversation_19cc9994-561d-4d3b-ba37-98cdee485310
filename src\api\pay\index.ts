import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PayParams, OrderQuery, RenewParams, PayBackParams } from '@/api/pay/types';

/**
 * 生成二维码接口
 * @param params 请求参数
 * @returns {AxiosPromise}
 */
export function getCreateQRCode(params: PayParams): AxiosPromise<any> {
  return request({
    url: '/system/sm/pay/create/wx/native/payinfo',
    method: 'post',
    data: params
  });
}

/**
 * 定时请求支付结果
 * @param orderNum 订单编号
 * @returns {AxiosPromise}
 */
export function getOrederStatus(orderNum: string): AxiosPromise<any> {
  return request({
    url: `/system/sm/pay/search/state/${orderNum}`,
    method: 'get'
  });
}

/**
 * 获取订单的单价
 * @returns {AxiosPromise}
 */
export function getPrice(): AxiosPromise<any> {
  return request({
    url: '/system/sm/pay/get/price',
    method: 'get'
  });
}

/**
 * 获取订单
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getOreders(params: OrderQuery): AxiosPromise<any> {
  return request({
    url: '/system/sm/pay/my/orders',
    method: 'get',
    params: params
  });
}

/**
 * 个人公司的单价为0元的时候直接修改价格
 * @param appType 应用类型
 * @param dataId 数据ID
 * @returns {AxiosPromise}
 */
export function getUpdatePayState(appType: string, dataId: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/remote/update/pay/state/${appType}/${dataId}`,
    method: 'get'
  });
}

/**
 * 通过业务端数据实例ID和订单字段名和金额字段,操作完成后更新字段
 * @param params 请求参数
 * @returns {AxiosPromise}
 */
export function addInstanceFee(params: RenewParams): AxiosPromise<any> {
  return request({
    url: '/system/sm/pay/renew/addInstanceFee',
    method: 'post',
    params: params
  });
}

/**
 * 退款操作
 * @param params 请求参数
 * @returns {AxiosPromise}
 */
export function payBack(params: PayBackParams): AxiosPromise<any> {
  return request({
    url: '/system/sm/pay/weixin/cba/payBack',
    method: 'post',
    params: params
  });
}
