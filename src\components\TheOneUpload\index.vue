<!-- 上传的文件内容 -->
<template>
  <div>
    <!-- 	
			:before-upload="(file) => beforeUpload(file)" 
			:on-remove="handleRemove" 
			:action="`${baseUrl}/qjt/file/multi/upload`"-->
    <el-upload
      :action="`${baseUrl}/qjt/file/multi/upload`"
      name="files"
      list-type="picture-card"
      :limit="9"
      multiple
      :headers="headers"
      :file-list="attachFileList"
      :on-change="handleChangeFile"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :accept="accept"
    >
      <template #default>
        <el-icon><Plus /></el-icon>
      </template>

      <template #file="{ file }">
        <img class="el-upload-list__item-thumbnail" fit="cover" :src="file.url" alt="" />
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <el-icon><ZoomIn /></el-icon>
          </span>
          <span class="el-upload-list__item-delete" @click="handleDownload(file)">
            <el-icon><Download /></el-icon>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <el-icon><Delete /></el-icon>
          </span>
        </span>
      </template>
    </el-upload>
    <el-dialog v-model="dialogVisible" :modal="false" :modal-append-to-body="false" :append-to-body="true" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import { Plus, ZoomIn, Download, Delete } from '@element-plus/icons-vue';
import type { UploadFile, UploadFiles, UploadUserFile } from 'element-plus';
import { getToken } from '@/utils/auth';
import { downLoadFile } from '@/utils/publicFun';

// Props 定义
defineProps({
  accept: {
    type: String,
    default: '.jpg, .jpeg, .png'
  }
});

// 定义 emit
const emit = defineEmits(['updateFileList']);

// 响应式数据
const attachFileList = ref<UploadUserFile[]>([]);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API || process.env.VUE_APP_BASE_API);
const headers = ref({
  Authorization: getToken(),
  'Access-Control-Allow-Origin': '*'
});
const dialogVisible = ref(false);
const dialogImageUrl = ref('');

// 监听上传文件变化
watch(
  attachFileList,
  (val) => {
    if (val.length > 0) {
      // 在这里分发事件，把值分发到需要的组件中
      emit('updateFileList', val);
    }
  },
  { deep: true }
);

// 上传成功
const handleSuccess = (res: any, file: UploadFile, fileList: UploadFiles) => {
  attachFileList.value = fileList;
};

// 文件校验
const handleChangeFile = (file: UploadFile, fileList: UploadFiles) => {
  const isJPG = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png';
  const isLt5M = file.size ? file.size / 1024 / 1024 < 5 : false;

  if (!isJPG) {
    // Element Plus 中的消息提示
    ElMessage.error('上传图片只能是JPG或PNG 格式!');
    const index = fileList.findIndex((f) => file.name === f.name && file.uid === f.uid);
    if (index !== -1) {
      fileList.splice(index, 1);
    }
  }

  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!');
    const index = fileList.findIndex((f) => file.name === f.name && file.uid === f.uid);
    if (index !== -1) {
      fileList.splice(index, 1);
    }
  }

  return isJPG && isLt5M;
};

// 移除照片
const handleRemove = (file: UploadFile) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const index = attachFileList.value.findIndex((f) => file.name === f.name && file.uid === f.uid);
      if (index !== -1) {
        attachFileList.value.splice(index, 1);
      }
    })
    .catch(() => {});
};

// 超出文件数量限制
const handleExceed = (files: File[], fileList: UploadUserFile[]) => {
  const message = `<p>当前限制选择<strong style="color:red">9</strong> 个文件,
    本次选择了<strong style="color:blue"> ${files.length}</strong> 个文件,
    共选择了<strong style="color:blue"> ${files.length + fileList.length} </strong>个文件,请<strong>重新选择</strong>！</p>
  `;

  ElMessageBox.alert(message, '提示', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定',
    showClose: false,
    callback: () => {}
  });
};

// 图片预览
const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url || '';
  dialogVisible.value = true;
};

// 下载文件
const handleDownload = (file: UploadFile) => {
  if (file.response) {
    const response = file.response as any;
    if (response.data && response.data[0]) {
      const url = response.data[0].path;
      downLoadFile(url, file.name);
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px !important;
  border: 1px solid #ededed;
  line-height: 100px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px !important;
}
// 改变上传进度转圈的大小
:deep(.el-upload-list--picture-card .el-progress) {
  width: 100px !important;
}
:deep(.el-progress-circle) {
  width: 100px !important;
  height: 100px !important;
}
:deep(.el-upload-list--picture-card .el-upload-list__item .el-upload-list__item-thumbnail) {
  width: 100px;
  height: 100px !important;
}
</style>
