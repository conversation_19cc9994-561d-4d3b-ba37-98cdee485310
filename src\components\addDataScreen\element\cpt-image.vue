<template>
  <div style="width: 100%; height: 100%">
    <el-image
      style="width: 100%; height: 100%"
      :preview-src-list="option.attribute.preview ? [`${fileUrl}/qjt/file/otherDownload/${option.attribute.url}?token=${token}`] : []"
      :src="option.attribute.url ? `${fileUrl}/qjt/file/otherDownload/${option.attribute.url}?token=${token}` : logod"
      :fit="option.attribute.fit"
    />
  </div>
</template>

<script lang="ts" setup>
import logod from '@/assets/logo/logod.png';
import { getToken } from '@/utils/auth';
// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const fileUrl = import.meta.env.VITE_APP_BASE_API;
const token = getToken();

defineOptions({
  name: 'cpt-image'
});
</script>

<style scoped></style>
