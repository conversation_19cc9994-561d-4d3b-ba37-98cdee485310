<template>
  <div class="flow-container">
    <div class="scale-slider">
      <el-button class="btn" :icon="Minus" circle size="small" @click="changeScale(-step)" />
      <span style="font-size: 14px">{{ scaleVal }}%</span>
      <el-button class="btn" :icon="Plus" circle size="small" @click="changeScale(step)" />
    </div>

    <FlowCard :verifyMode="verifyMode" :key="updateId" :data="data" @emits="eventReciver" :style="{ transform: `scale(${scaleVal / 100})` }" />

    <PropPanel :value="activeData" :processData="data" :isOnlineForm="isOnlineForm" @confirm="onPropEditConfirm" @cancel="onClosePanel" />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import FlowCard from './FlowCard/index.vue';
import PropPanel from './PropPanel/index.vue';
import { NodeUtils, getMockData } from './FlowCard/util.js';
import { Plus, Minus } from '@element-plus/icons-vue';
interface ProcessProps {
  tabName: string;
  conf: any;
  isOnlineForm: boolean;
}

interface ProcessEmits {
  (e: 'startNodeChange', data: any): void;
}

const props = defineProps<ProcessProps>();
const emit = defineEmits<ProcessEmits>();

// Data
const data = ref(getMockData());
const scaleVal = ref(100); // 流程图缩放比例 100%
const step = 5; // 缩放步长
const updateId = ref(0); // key值 用于模拟强制更新
const activeData = ref<any>(null); // 被激活的流程卡片数据，用于属性面板编辑
const isProcessCmp = ref(true);
const verifyMode = ref(false);

// Watchers
watch(
  () => props.conf,
  (val) => {
    if (val) {
      // 使用传过来的配置数据覆盖默认填充的数据
      if (typeof val === 'object' && val !== null) {
        Object.assign(data.value, val);
      }
    }
  },
  { immediate: true, deep: true }
);

// Methods
const getData = () => {
  verifyMode.value = true;
  // 检查节点数据
  if (NodeUtils.checkAllNode(data.value)) {
    return Promise.resolve({ formData: data.value });
  } else {
    // 返回一个带有拒绝原因的Promise对象
    return Promise.reject({ target: props.tabName });
  }
};

const getDataNoValidate = () => {
  return Promise.resolve({ formData: data.value });
};

const eventReciver = ({ event, args }: { event: string; args: any[] }) => {
  if (event === 'edit') {
    // 因为这里会给 activeData 赋值   监听这个值打开弹框  但是添加的节点我感觉应该是 这里
    activeData.value = args[0]; // 打开属性面板
    return;
  }
  // 其他方法只执行事件
  (NodeUtils as any)[event](...args);
  forceUpdate();
};

const forceUpdate = async () => {
  updateId.value += 1;
};

const changeScale = (val: number) => {
  const v = scaleVal.value + val;
  if (v > 0 && v <= 200) {
    // 缩放介于0%~200%
    scaleVal.value = v;
  }
};

const onPropEditConfirm = async (value: any, content?: string) => {
  try {
    if (!activeData.value) return;

    activeData.value.content = content || '请设置条件';
    const oldProp = { ...activeData.value.properties };
    activeData.value.properties = value;

    // 修改优先级
    if (NodeUtils.isConditionNode(activeData.value) && value.priority !== oldProp.priority) {
      NodeUtils.resortPrioByCNode(activeData.value, oldProp.priority, data.value);
      NodeUtils.setDefaultCondition(activeData.value, data.value);
    }

    if (NodeUtils.isStartNode(activeData.value)) {
      emit('startNodeChange', data.value);
    }

    await onClosePanel();
    await forceUpdate();
  } catch (error) {}
};

const onClosePanel = async () => {
  // 清除所有相关绑定的值
  activeData.value = null;
  data.value = JSON.parse(JSON.stringify(data.value));
  forceUpdate();
};

const isFilledPCon = (formIds?: string[]) => {
  let res = false;

  const loopChild = (parent: any, callback: () => void) => {
    parent.childNode && loop(parent.childNode, callback);
  };

  const loop = (nodeData: any, callback: () => void) => {
    if (res || !nodeData) return;

    if (Array.isArray(nodeData.conditionNodes)) {
      const used = nodeData.conditionNodes.some((c: any) => {
        const cons = c.properties.conditions || [];
        return Array.isArray(formIds) ? cons.some((item: any) => formIds.includes(item.formId)) : cons.length > 0;
      });

      if (used) {
        callback();
      } else {
        nodeData.conditionNodes.forEach((t: any) => loopChild(t, callback));
      }
    }

    loopChild(nodeData, callback);
  };

  loop(data.value, () => (res = true));
  return res;
};

const resetForm = () => {
  data.value = {
    type: 'start',
    content: '所有人',
    properties: {
      title: '发起人',
      initiator: 'all'
    },
    nodeId: 'START',
    icon: 'applyStart',
    time: ''
  };
  // 如果有其他需要重置的状态，也在这里重置
};

// Expose methods to parent component
defineExpose({
  getData,
  getDataNoValidate,
  resetForm,
  isFilledPCon,
  forceUpdate
});

// Update global ID before render
NodeUtils.globalID = NodeUtils.getMaxNodeId(data.value);
</script>

<style lang="scss" scoped>
$bg-color: #f5f5f7;

.flow-container {
  display: inline-block;
  background: $bg-color;
  padding: 20px;
  width: 100%;
  // height: 100%;
  box-sizing: border-box;
  text-align: center;
  overflow: auto;
}

.scale-slider {
  position: fixed;
  right: 66px;
  z-index: 99;
  display: flex;
  align-items: center;
  // gap: 10px;
  padding: 0 10px;
  // background: #fff;
  border-radius: 4px;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .btn {
    margin: 0;
  }
}
</style>
