<!-- 指界人 -->
<template>
  <div>
    <div class="obligee-info-contianer" v-for="item in currentParceItem.refereeList" :key="item.id">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <div class="obligee-name-phone">
            <div class="name">
              <span>指界人姓名:</span><span style="padding-left: 8px">{{ item.name }}</span>
            </div>
            <div class="name">
              <span>指界人电话:</span><span style="padding-left: 8px">{{ item.phone }}</span>
            </div>
          </div>

          <div class="obligee-name-phone">
            <div class="name">
              <span>起始界址点:</span><span style="padding-left: 8px">J{{ item.startPointSerial }}</span>
            </div>
            <div class="name">
              <span>结束界址点:</span><span style="padding-left: 8px">J{{ item.endPointSerial }}</span>
            </div>
          </div>
          <div v-show="item.show">
            <div class="obligee-name-phone">
              <div class="name" style="width: 100%">
                <span>界址点说明:</span><span style="padding-left: 8px">{{ item.pointExplain }}</span>
              </div>
            </div>
            <div class="obligee-img">
              <div style="padding-left: 12px">
                <div class="title" style="margin-bottom: 10px">签名</div>
                <authImg :authSrc="`${baseUrl}/${item.signature}?att=1`" :width="'218px'" :height="'138px'" :radios="'4px'" v-if="item.signature">
                </authImg>
                <div v-else class="no-img">暂无数据</div>
              </div>
              <div style="padding-left: 12px">
                <div class="title" style="margin-bottom: 10px">指纹</div>
                <authImg :authSrc="`${baseUrl}/${item.fingerprint}?att=1`" :width="'218px'" :height="'138px'" :radios="'4px'" v-if="item.fingerprint">
                </authImg>
                <div v-else class="no-img">暂无数据</div>
              </div>
            </div>
          </div>

          <div class="obligee-detial" v-if="!item.show" @click="handleChange(item, 1)">
            <span>详情<i class="el-icon-arrow-down"></i></span>
          </div>
          <div class="obligee-detial" v-else @click="handleChange(item, 2)">
            <span>收起<i class="el-icon-arrow-up"></i></span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import authImg from '../../../../components/authImg/index.vue';

interface Props {
  parceInfoItem: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  parceInfoItem: () => ({})
});

const store = useProjectStore();
const baseUrl = import.meta.env.VUE_APP_BASE_API + '/qjt/file/downloadone';

const currentParceItem = computed(() => store.parcelInfo);

interface Referee {
  id: string | number;
  name: string;
  phone: string;
  startPointSerial: string | number;
  endPointSerial: string | number;
  pointExplain: string;
  signature?: string;
  fingerprint?: string;
  show?: boolean;
}
/**
 * 改变
 * @param item 类型
 * @param type 类型
 */
const handleChange = (item: Referee, type: number) => {
  if (type === 1) {
    item.show = true;
  } else {
    item.show = false;
  }
};
</script>

<style lang="scss" scoped>
.no-img {
  width: 218px;
  height: 138px;
  border-radius: 6px;
  background: rgba(248, 248, 248, 0.1);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: dashed 1px #ffffff;
  margin-top: 15px;
}
.obligee-info-contianer {
  width: 483px;
  height: auto;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  margin: 0 12px 12px 12px;
  display: flex;
  .obligee-title {
    width: 100px;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 17px;
    padding-top: 12px;
    padding-left: 12px;
  }
  .obligee-name-phone {
    display: flex;
    justify-content: space-between;
    width: 483px;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 17px;
    margin-top: 12px;
    padding-left: 12px;
    margin-bottom: 10px;
    .name {
      width: 50%;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      margin-top: 12px;
    }
  }
  .obligee-idcard {
    width: 483px;
    height: 20px;
    font-size: 14px;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 20px;
    padding-top: 12px;
    padding-left: 12px;
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .obligee-img {
    display: flex;
    margin-top: 20px;

    .title {
      width: 70px;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      line-height: 20px;
    }
  }
  .obligee-detial {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: var(--current-color);
    line-height: 17px;
    padding-right: 20px;
    margin-bottom: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
}
</style>
