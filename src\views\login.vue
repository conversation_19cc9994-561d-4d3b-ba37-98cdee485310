<!--  -->
<template>
  <div class="login-main">
    <div class="login-bgcontent">
      <div class="left-content" v-show="!isMobile">
        <!-- <img class="logo-img" src="../assets/images/logo.png" alt=""> -->
        <!-- <div class="left-big-span">
                <span>Welcome  to</span>
                <span>Shenma Investigation!</span>
            </div>
            <div class="left-small-span">
                <span>Professional geographic information</span>
                <span>survey software.</span>
            </div> -->
        <div class="erm-div">
          <img src="../assets/images/big-erweima5.0.png" alt="" class="erweima" />
        </div>
      </div>
      <div class="right-content">
        <!-- 登录 -->
        <template v-if="showType == 1">
          <div class="big-title">
            <span v-show="companyType == '1'">欢迎使用，</span>
            <span v-show="companyType == '2'">欢迎使用「智图资产管理系统」</span>
          </div>
          <div class="small-title-big">
            <span v-show="companyType == '1'">神马调查管理端(新版)</span>
            <span v-show="companyType == '2'">智图资产管理系统管理端</span>
          </div>
          <div class="log-tab-div">
            <div class="tab-item" v-for="item in tabList" :key="item.title" :class="{ 'active-tab': item.checked }" @click="changeTab(item)">
              {{ item.title }}
              <div class="hr" v-if="item.checked"></div>
            </div>
          </div>
          <!-- 账号密码登录 -->
          <el-form
            :model="loginForPwd"
            :rules="loginForPwdRule"
            ref="loginForPwdRef"
            label-width="0px"
            class="demo-ruleForm"
            v-if="tabList[0].checked"
          >
            <el-form-item prop="username" key="loginForPwd-username">
              <el-input v-model="loginForPwd.username" placeholder="请输入账号"></el-input>
            </el-form-item>
            <el-form-item prop="password" key="loginForPwd-password">
              <el-input placeholder="请输入密码" v-model="loginForPwd.password" show-password> </el-input>
            </el-form-item>
          </el-form>
          <!-- 短信验证登录 -->
          <el-form :model="lgoinForSms" :rules="lgoinForSmsRule" ref="lgoinForSmsRef" label-width="0px" class="demo-ruleForm" v-else>
            <el-form-item prop="username" key="lgoinForSms-password">
              <el-input v-model="lgoinForSms.username" placeholder="请输入手机号"></el-input>
            </el-form-item>
            <el-form-item prop="captcha" key="lgoinForSms-captcha">
              <el-input v-model="lgoinForSms.captcha" maxlength="6" placeholder="请输入验证码">
                <template #suffix>
                  <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode(1)" style="line-height: 44px">{{
                    sendCodeText
                  }}</el-link>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <div class="login-handle">
            <!-- 记住密码待开发 -->
            <!-- <el-checkbox v-model="loginForPwd.rememberMe">记住密码</el-checkbox> -->
            <div></div>
            <el-link type="primary" @click="showType = 4">忘记密码?</el-link>
          </div>
          <el-button type="primary" class="login-btn" @click="firstLogin">登录</el-button>
          <div class="footer-msg">还没有账号？<el-link type="primary" @click="handleShowRegister">立即注册</el-link></div>
        </template>
        <!-- 选择组织 -->
        <template v-if="showType == 3">
          <div class="big-title">选择登录组织</div>
          <div class="small-title">你存在以下组织</div>
          <div class="org-div">
            <div class="org-item" v-for="item in orgList" :key="item.companyId" @click="chooseOrg(item)">
              <img src="../assets/images/org-ico.png" alt="" class="org-ico" />
              <span v-if="item.companyType == 1">{{ item.companyName }}</span>
              <span v-else-if="item.companyType == 2">我的组织</span>
              <span v-else>{{ item.companyName }}</span>
            </div>
          </div>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"
              ><el-icon><ArrowLeft /></el-icon> 返回登录页</el-link
            >
          </div>
        </template>
        <!-- 忘记密码 -->
        <template v-if="showType == 4">
          <div class="big-title">忘记密码</div>
          <div class="big-hr"></div>
          <el-form :model="forgetPwdMsg" :rules="forgetPwdMsgRule" ref="forgetPwdMsgRef" label-width="0px" class="demo-ruleForm">
            <el-form-item prop="username">
              <el-input v-model="forgetPwdMsg.username" placeholder="请输入手机号"></el-input>
            </el-form-item>
            <el-form-item prop="smscode">
              <el-input v-model="forgetPwdMsg.smscode" maxlength="6" placeholder="请输入验证码">
                <template #suffix>
                  <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode(2)" style="line-height: 44px">{{
                    sendCodeText
                  }}</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="forgetPwdMsg.password" placeholder="请输入新密码"></el-input>
            </el-form-item>
            <el-form-item prop="alginPassword">
              <el-input v-model="forgetPwdMsg.alginPassword" placeholder="再次输入新密码"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" class="login-btn" @click="forgetPwd">确定</el-button>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"
              ><el-icon><ArrowLeft /></el-icon> 返回登录页</el-link
            >
          </div>
        </template>
        <!-- 个人注册 -->
        <template v-if="showType == 5">
          <div class="big-title">个人注册</div>
          <div class="big-hr"></div>
          <el-form :model="registerMsg" :rules="registerMsgRlue" ref="registerMsgRef" label-width="0px" class="demo-ruleForm">
            <el-form-item prop="custName" key="registerMsg-custName">
              <el-input v-model="registerMsg.custName" placeholder="请输入姓名"></el-input>
            </el-form-item>
            <el-form-item prop="username" key="registerMsg-username">
              <el-input v-model="registerMsg.username" placeholder="请输入手机号"></el-input>
            </el-form-item>
            <el-form-item prop="captcha" key="registerMsg-captcha">
              <el-input v-model="registerMsg.captcha" maxlength="6" placeholder="请输入验证码">
                <template #suffix>
                  <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode(3)" style="line-height: 44px">{{
                    sendCodeText
                  }}</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" key="registerMsg-password">
              <el-input placeholder="请设置登录密码" v-model="registerMsg.password"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" class="login-btn" @click="register">注册</el-button>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"
              ><el-icon><ArrowLeft /></el-icon> 返回登录页</el-link
            >
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { getMessageCode, loginFirst, forgetPassword, register as registerApi } from '@/api/login/index';
import { isPhone } from '@/utils/validate';
import { useUserStore } from '@/store/modules/user';

// 路由
const router = useRouter();
const route = useRoute();

// 用户store
const userStore = useUserStore();

// 表单ref
const loginForPwdRef = ref();
const lgoinForSmsRef = ref();
const forgetPwdMsgRef = ref();
const registerMsgRef = ref();

// 重定向地址
const redirect = ref<string | undefined>(undefined);

// 显示类型 1登录 2注册 3选择组织 4忘记密码 5个人注册
const showType = ref(1);

// 标签列表
const tabList = ref([
  { title: '账号登录', checked: true },
  { title: '验证码登录', checked: false }
]);

// 验证码URL
const codeUrl = ref('');

// 账号密码登录表单
const loginForPwd = reactive({
  uuid: '',
  password: '',
  username: '',
  rememberMe: false,
  from: 'web'
});

// 账号密码登录表单验证规则
const loginForPwdRule = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
};

// 短信验证登录表单
const lgoinForSms = reactive({
  username: '',
  captcha: '',
  uuid: '',
  from: 'web'
});

// 短信验证登录表单验证规则
const lgoinForSmsRule = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
};

// 组织列表
const orgList = ref([]);

// 上一次选择的组织
const oldChooseCompanyId = ref(null);

// 倒计时
const time = ref(60);

// 发送验证码文本
const sendCodeText = ref('获取验证码');

// 发送验证码按钮类型
const showTypeTitle = ref<'primary' | 'default' | 'success' | 'warning' | 'info' | 'danger'>('primary');

// 发送验证码按钮是否禁用
const showDisabled = ref(false);

// 忘记密码表单
const forgetPwdMsg = reactive({
  username: '',
  password: '',
  smscode: '',
  alginPassword: ''
});

// 忘记密码表单验证规则
const forgetPwdMsgRule = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/, message: '密码需为8-20位数字和字母组合', trigger: ['blur', 'change'] }
  ],
  smscode: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
  alginPassword: [{ validator: validatePassRegister, trigger: 'blur' }]
};

// 注册表单
let registerMsg = reactive({
  username: '',
  mobile: '',
  password: '',
  companyType: 2, // 1组织 2个人
  captcha: '',
  custName: '' // 用户名
});

// 注册表单验证规则
const registerMsgRlue = {
  custName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  password: [
    { required: true, message: '请设置登录密码', trigger: 'blur' },
    { pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/, message: '密码需为8-20位数字和字母组合', trigger: ['blur', 'change'] }
  ]
};

// 是否显示app二维码
const isShowErweima = ref(false);
const isShowForget = ref(false);

// 公司类型
const companyType = ref(import.meta.env.VITE_APP_COMPANY_TYPE);

// 是否是小屏
const isMobile = ref(false);

/**
 * 手机号验证
 */
function validatePhone(rule: any, value: string, callback: any) {
  if (!isPhone(value)) {
    callback(new Error('请输入正确的手机号'));
  } else {
    callback();
  }
}

/**
 * 密码确认验证
 */
function validatePassRegister(rule: any, value: string, callback: any) {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== forgetPwdMsg.password) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
}

/**
 * 监听路由变化
 */
watch(
  () => route.query,
  (query) => {
    redirect.value = query.redirect as string;
  },
  { immediate: true }
);

/**
 * 组件挂载
 */
onMounted(() => {
  if (window.innerWidth < 1150) {
    isMobile.value = true;
  }
});

/**
 * 点击个人注册情况其他所有的表单校验
 */
const handleShowRegister = () => {
  if (showType.value == 1) {
    tabList.value.forEach((v) => {
      if (v.title == '账号登录' && v.checked) {
        loginForPwdRef.value?.clearValidate();
      } else if (v.title == '验证码登录' && v.checked) {
        lgoinForSmsRef.value?.clearValidate();
      }
    });
  }
  showType.value = 5;
};

/**
 *  切换标签
 */
const changeTab = (item: any) => {
  tabList.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  loginForPwd.password = '';
  lgoinForSms.captcha = '';
};

/**
 * 首次登录
 */
const firstLogin = () => {
  if (tabList.value[0].checked) {
    // 账号密码登录
    loginForPwdRef.value?.validate((valid: boolean) => {
      if (valid) {
        loginFirst(loginForPwd).then((res: any) => {
          if (res.code == 200) {
            if (res.data.sysCompanyList.length == 0) {
              ElMessage.error('您没有登录权限,请联系管理员！！！');
              return;
            }
            if (res.data.sysCompanyList.length < 2) {
              // 代表只有一个组织或者个人用户 直接默认选中第一个组织
              // if (res.data.sysCompanyList[0].companyType == 2) {
              //   // 个人用户提示不能登录
              //   ElMessageBox.alert('<span style="color:red">个人账号不允许登录网页端，请在app升级为专业版或企业版！！！</span>', '错误提示', {
              //     dangerouslyUseHTMLString: true
              //   });
              //   return;
              // } else {
              if (res.data.sysCompanyList[0].vipType == 1) {
                // 是企业账号，但是vip等级是个人 不允许登录
                ElMessageBox.alert(
                  '<span style="color:red">您的账号是个人账号，不允许登录网页端，请在app升级为专业版或企业版！！！</span>',
                  '错误提示',
                  {
                    dangerouslyUseHTMLString: true
                  }
                );
                return;
              }
              const companyId = res.data.sysCompanyList[0].companyId;
              const params = {
                username: loginForPwd.username,
                companyId: companyId,
                password: loginForPwd.password,
                from: 'web'
              };
              endLogin(params);
              // }
            } else {
              // 代表多个组织 需要先选组织
              oldChooseCompanyId.value = res.data.companyId;
              showType.value = 3;
              const list: any[] = [];
              res.data.sysCompanyList.forEach((v: any) => {
                if (v.companyType != 2) {
                  // 筛掉个人用户
                  list.push(v);
                }
              });
              orgList.value = list;
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        return false;
      }
    });
  } else {
    // 短信登录
    lgoinForSmsRef.value?.validate((valid: boolean) => {
      if (valid) {
        loginFirst(lgoinForSms).then((res: any) => {
          if (res.code == 200) {
            if (res.data.sysCompanyList.length == 0) {
              ElMessage.error('您没有登录权限,请联系管理员！！！');
              return;
            }
            if (res.data.sysCompanyList.length < 2) {
              // 代表只有一个组织或者个人用户 直接默认选中第一个组织
              const companyId = res.data.sysCompanyList[0].companyId;
              const params = {
                username: lgoinForSms.username,
                companyId: companyId,
                captcha: lgoinForSms.captcha,
                from: 'web'
              };
              endLoginMessage(params);
            } else {
              // 代表多个组织 需要先选组织
              oldChooseCompanyId.value = res.data.companyId;
              showType.value = 3;
              orgList.value = res.data.sysCompanyList;
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        return false;
      }
    });
  }
};

/**
 * 最终登录
 * @param {object} params 参数
 */
const endLogin = (params: any) => {
  userStore
    .login(params)
    .then(() => {
      router.push({ path: '/index' }).catch(() => {});
    })
    .catch(() => {});
};

/**
 * 短信登录
 * @param params
 */
const endLoginMessage = (params: any) => {
  userStore
    .loginMessage(params)
    .then(() => {
      router.push({ path: '/index' }).catch(() => {});
    })
    .catch(() => {});
};

/**
 * 选中某个组织
 * @param item
 */
const chooseOrg = (item: any) => {
  if (item.vipType == 1) {
    // 权限是个人权限不允许登录
    ElMessageBox.alert('<span style="color:red">个人账号不允许登录网页端，请在app升级为专业版或企业版！！！</span>', '错误提示', {
      dangerouslyUseHTMLString: true
    });
    return;
  }
  if (tabList.value[0].checked) {
    const params = {
      username: loginForPwd.username,
      companyId: item.companyId,
      password: loginForPwd.password,
      from: 'web'
    };
    endLogin(params);
  } else {
    // 短信登录
    const params = {
      username: lgoinForSms.username,
      companyId: item.companyId,
      captcha: lgoinForSms.captcha,
      from: 'web'
    };
    endLoginMessage(params);
  }
};

/**
 * 返回登录页
 */
const goLogin = () => {
  showType.value = 1;
};

/**
 * 获取验证码
 * @param type
 */
const getCode = (type: number) => {
  if (type == 1) {
    if (!lgoinForSms.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  } else if (type == 2) {
    if (!forgetPwdMsg.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  } else if (type == 3) {
    if (!registerMsg.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  }

  const param = {
    username: lgoinForSms.username
  };

  if (type == 2) {
    param.username = forgetPwdMsg.username;
  }
  if (type == 3) {
    param.username = registerMsg.username;
  }

  getMessageCode(param).then((res: any) => {
    if (res.code === 200) {
      setTime(time.value);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 设置倒计时
 * @param {number} val 时间
 */
const setTime = (val: number) => {
  if (time.value == 0) {
    sendCodeText.value = '获取验证码';
    time.value = 60;
    showTypeTitle.value = 'primary';
    showDisabled.value = false;
  } else {
    showTypeTitle.value = 'info';
    showDisabled.value = true;
    time.value--;
    sendCodeText.value = `重新发送${time.value}`;
    setTimeout(() => {
      setTime(time.value);
    }, 1000);
  }
};

/**
 * 忘记密码
 */
const forgetPwd = () => {
  forgetPwdMsgRef.value?.validate((valid: boolean) => {
    if (valid) {
      forgetPassword(forgetPwdMsg).then((res: any) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '密码重置成功'
          });
          forgetPwdMsg.username = '';
          forgetPwdMsg.password = '';
          forgetPwdMsg.smscode = '';
          forgetPwdMsg.alginPassword = '';
          showType.value = 1;
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      return false;
    }
  });
};

/**
 * 注册
 */
const register = () => {
  registerMsgRef.value?.validate((valid: boolean) => {
    if (valid) {
      registerMsg.mobile = registerMsg.username;
      registerApi(registerMsg).then((res) => {
        if (res.code == 200) {
          const username = registerMsg.username;
          ElMessageBox.alert("<font color='red'>恭喜您，您的账号 " + username + ' 注册成功！</font>', '系统提示', {
            dangerouslyUseHTMLString: true,
            type: 'success'
          })
            .then(() => {
              registerMsg = {
                username: '',
                mobile: '',
                password: '',
                companyType: 2, //1组织 2个人
                captcha: '',
                custName: '' //用户名
              };
              showType.value = 1;
            })
            .catch(() => {});
        } else {
          const errorText = res.msg;
          ElMessageBox.alert("<font color='red'>" + errorText + ' </font>', '系统提示', {
            dangerouslyUseHTMLString: true,
            type: 'error'
          })
            .then(() => {
              showType.value = 5;
            })
            .catch(() => {});
        }
      });
    } else {
      return false;
    }
  });
};
</script>

<style lang="scss" scoped>
.erm-div {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .erweima {
    width: 175px;
    height: 175px;
  }
}

:deep(.el-input__wrapper) {
  background-color: #edf4fb;
}

:deep(.el-input__inner) {
  height: 44px;
}

.login-btn {
  width: 100%;
  height: 44px;
}
.login-main {
  width: 100%;
  height: 100%;
  background: #dbe7ee;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .login-bgcontent {
    background-image: url('../assets/images/login-bg.png');
    background-size: cover;
    background-attachment: fixed;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .company2 {
      background: linear-gradient(135deg, #ff3d57 0%, #ff8a48 100%) !important;
    }
    .left-content {
      width: 704px;
      height: 674px;
      // background: linear-gradient(135deg, #0081FF 0%, #22CCE2 100%);
      background-image: url('../assets/images/left-bg.png');
      background-size: cover;
      // background-attachment: fixed;
      opacity: 1;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      .logo-img {
        width: 148px;
        height: 40px;
        position: absolute;
        top: 24px;
        left: 32px;
      }
      .left-big-span {
        width: 394px;
        height: 84px;
        font-size: 30px;
        font-family: Poppins-Bold, Poppins;
        font-weight: 700;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 120px;
      }
      .left-small-span {
        width: 294px;
        height: 48px;
        font-size: 14px;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 22px;
      }
    }
    .right-content {
      width: 496px;
      height: 674px;
      opacity: 1;
      background: #fff;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      padding: 0px 38px;
      position: relative;
      .big-title {
        font-size: 32px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: 600;
        color: #161d26;
        margin-top: 72px;
      }
      .big-hr {
        width: 48px;
        height: 4px;
        background: var(--current-color);
        border-radius: 4px 4px 4px 4px;
        margin-top: 2px;
        margin-bottom: 40px;
      }
      .small-title {
        font-size: 14px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: normal;
        color: #8291a9;
        margin-top: 4px;
      }
      .small-title-big {
        color: #1890ff;
        font-size: 32px;
        font-weight: bold;
        margin-top: 30px;
      }
      .log-tab-div {
        margin-top: 43px;
        margin-bottom: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .tab-item {
          height: 28px;
          font-size: 20px;
          color: #8291a9;
          margin-right: 32px;
          cursor: pointer;
          font-family:
            PingFangSC-328080,
            PingFang SC;
          .hr {
            width: 48px;
            height: 4px;
            background: var(--current-color);
            border-radius: 4px 4px 4px 4px;
            margin-top: 2px;
          }
        }
        .active-tab {
          font-size: 24px;
          color: #161d26;
          font-weight: 600;
        }
      }
      .login-handle {
        margin: 20px 0px 32px 0px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .org-div {
        margin-top: 40px;
        height: 425px;
        overflow: auto;
        /*滚动条样式*/
        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: rgba(176, 175, 175, 0.5);
        }
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          border-radius: 0;
          background: rgba(248, 248, 248, 0.1);
        }
        .org-item {
          width: 100%;
          height: 72px;
          background: #edf4fb;
          border-radius: 8px 8px 8px 8px;
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 18px;
          font-family:
            PingFang SC-Medium,
            PingFang SC;
          font-weight: 600;
          color: #161d26;
          margin-bottom: 20px;
          cursor: pointer;
          position: relative;
          .org-ico {
            width: 32px;
            height: 32px;
            margin-left: 16px;
            margin-right: 20px;
          }
        }
        .org-item:hover {
          box-shadow: 0px 4px 12px 0px rgba(0, 54, 106, 0.2);
          color: var(--current-color);
        }
      }
      .footer-msg {
        margin-top: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #8291a9;
      }
      .go-login {
        position: absolute;
        bottom: 40px;
      }
      .footer {
        position: absolute;
        bottom: 48px;
        right: 95px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .erweima {
          width: 16px;
          height: 16px;
          margin-right: 5px;
        }
      }
      .erweima-box {
        width: 200px;
        height: 200px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 4px 10px 0px rgba(27, 154, 247, 0.16);
        position: absolute;
        bottom: 84px;
        right: 39px;
        display: flex;
        align-items: center;
        justify-content: center;
        .erweima {
          width: 168px;
          height: 168px;
        }
      }
    }
  }
}
</style>
