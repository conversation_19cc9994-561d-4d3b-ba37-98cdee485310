<template>
  <div v-if="isExternalComputed" :style="styleExternalIcon" class="svg-external-icon svg-icon" />
  <svg v-else :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconName" />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { isExternal } from '@/utils/validate';

// 定义 props 类型
interface SvgIconProps {
  iconClass: string;
  className?: string;
  rotateDeg?: string;
}

const props = defineProps<SvgIconProps>();

// 计算属性
const isExternalComputed = computed(() => isExternal(props.iconClass));
const iconName = computed(() => `#icon-${props.iconClass}`);
const svgClass = computed(() => {
  if (props.className) {
    return 'svg-icon ' + props.className;
  } else {
    return 'svg-icon';
  }
});
const styleExternalIcon = computed(() => {
  return {
    mask: `url(${props.iconClass}) no-repeat 50% 50%`,
    '-webkit-mask': `url(${props.iconClass}) no-repeat 50% 50%`
  };
});
</script>

<style scoped>
.svg-icon {
  width: 36px;
  height: 36px;
  fill: currentColor;
  overflow: hidden;
}
/* .svg-icon:hover{  } */
.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
