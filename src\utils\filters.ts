/**
 * 格式化日期为 YYYY-MM-DD
 * @param value 日期字符串
 * @returns {string}
 */
export function formatDate(value: string | number): string {
  if (value) {
    value = value.toString().substring(0, 10);
    return value;
  }
  return '';
}

/**
 * 格式化到年月日时分秒
 * @param dlogTime 日期时间
 * @returns {string}
 */
export function formatDateYmdhm(dlogTime: string | number | Date): string {
  if (dlogTime) {
    const date = new Date(dlogTime);
    const Y = date.getFullYear() + '-';
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
    const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
    const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    const strDate = Y + M + D + '' + h + m + s;
    return strDate;
  } else {
    return '--';
  }
}

/**
 * 格式化时间为 HH:MM:SS
 * @param value 时间戳
 * @returns {string}
 */
export function formatTime(value: string | number): string {
  if (value) {
    const date = new Date(Number(value));
    const hh: string = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString();
    const mm: string = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString();
    const ss: string = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString();
    return `${hh}:${mm}:${ss}`;
  }
  return '';
}

/**
 * 字符串超长截断处理
 * @param value 字符串
 * @param number 截断长度
 * @returns {string}
 */
export function spanRule(value: string, number = 10): string {
  if (value) {
    if (value.length > number) {
      value = value.substring(0, number) + '...';
      return value;
    } else {
      return value;
    }
  }
  return '';
}

/**
 * 字符串居中截断处理
 * @param value 字符串
 * @param number 截断长度
 * @returns {string}
 */
export function spanRuleCenter(value: string, number = 10): string {
  if (value) {
    if (value.length > number) {
      const startIndex = number / 2;
      const endIndex = value.length - startIndex;
      value = value.substring(0, startIndex) + '...' + value.slice(endIndex);
      return value;
    } else {
      return value;
    }
  }
  return '';
}

/**
 * 手机号中间4位模糊处理
 * @param value 手机号
 * @returns {string}
 */
export function formatPhone(value: string): string {
  const reg = /^(\d{3})\d*(\d{4})$/;
  return value.replace(reg, '$1****$2');
}

/**
 * 时间戳转yyyy-MM-dd格式
 * @param value 时间戳
 * @returns {string}
 */
export function formatDateType(value: string | number): string {
  if (!value) return '';
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month: string | number = date.getMonth() + 1;
  let day: string | number = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  return `${year}-${month}-${day}`;
}

/**
 * 时间戳转yyyy-MM格式
 * @param value 时间戳
 * @returns {string}
 */
export function formatDateTypeMonth(value: string | number): string {
  if (!value) return '';
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month: string | number = date.getMonth() + 1;
  month = month < 10 ? '0' + month : month;
  return `${year}-${month}`;
}

/**
 * 时间戳转yyyy格式
 * @param value 时间戳
 * @returns {string}
 */
export function formatDateTypeYear(value: string | number): string {
  if (!value) return '';
  const date = new Date(Number(value));
  const year = date.getFullYear();
  return year.toString();
}

/**
 * 时间戳转yyyy-MM-dd HH:mm:ss格式
 * @param value 时间戳
 * @returns {string}
 */
export function formatDateAndTimeType(value: string | number): string {
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month: string | number = date.getMonth() + 1;
  let day: string | number = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  const hh: string = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString();
  const mm: string = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString();
  const ss: string = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString();
  return `${year}-${month}-${day} ${hh}:${mm}:${ss}`;
}

/**
 * 根据身份证获取年龄
 * @param identityCard 身份证号码
 * @returns {number | string}
 */
export function getAgeForCardId(identityCard: string): number | string {
  const len = (identityCard + '').length;
  if (len == 0) {
    return '';
  } else {
    if (len != 15 && len != 18) {
      // 身份证号码只能为15位或18位其它不合法
      return '';
    }
  }
  let strBirthday = '';
  if (len == 18) {
    // 处理18位的身份证号码从号码中得到生日和性别代码
    strBirthday = identityCard.slice(6, 10) + '/' + identityCard.slice(10, 12) + '/' + identityCard.slice(12, 14);
  }
  if (len == 15) {
    strBirthday = '19' + identityCard.slice(6, 8) + '/' + identityCard.slice(8, 10) + '/' + identityCard.slice(10, 12);
  }
  // 时间字符串里，必须是"/"
  const birthDate = new Date(strBirthday);
  const nowDateTime = new Date();
  let age = nowDateTime.getFullYear() - birthDate.getFullYear();
  // 再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
  if (
    nowDateTime.getMonth() < birthDate.getMonth() ||
    (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
}

/**
 * 根据身份证获取生日
 * @param idCard 身份证号码
 * @returns {string}
 */
export function getBirthdayForCardId(idCard: string): string {
  let birthday = '';
  if (idCard != null && idCard != '') {
    if (idCard.length == 15) {
      birthday = '19' + idCard.slice(6, 12);
    } else if (idCard.length == 18) {
      birthday = idCard.slice(6, 14);
    }

    birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
  }
  return birthday;
}

/**
 * 根据方位角度数得到具体方位
 * @param number 角度数
 * @returns {string}
 */
export function getDirection(number: number): string {
  let direction = '';
  if (number <= 22.5 || number >= 337.5) {
    direction = '北';
  } else if (number > 22.5 && number < 67.5) {
    direction = '东偏北';
  } else if (number >= 67.5 && number <= 112.5) {
    direction = '东';
  } else if (number > 112.5 && number < 157.5) {
    direction = '东偏南';
  } else if (number >= 157.5 && number <= 202.5) {
    direction = '南';
  } else if (number > 202.5 && number < 247.5) {
    direction = '西偏南';
  } else if (number >= 247.5 && number <= 292.5) {
    direction = '西';
  } else if (number > 292.5 && number < 337.5) {
    direction = '西偏北';
  }
  return direction;
}

/**
 * 处理位置信息经纬度保留5位小数 加上经纬度标志
 * @param value 经纬度字符串
 * @returns {string}
 */
export function getLonAndLat(value: string): string {
  if (value) {
    const list = value.split(',');
    const Longitude = parseFloat(list[1]).toFixed(5);
    const Latitude = parseFloat(list[0]).toFixed(5);
    return `Lon:${Longitude} Lat:${Latitude}`;
  }
  return '';
}

/**
 * 根据给的list和值得到label
 * @param value 值
 * @param list 选项列表
 * @returns {string}
 */
export function filterOption(value: string | number, list: Array<{ value: string | number; label: string }>): string {
  let result = '';
  if (value != '' && value != null && value != undefined) {
    result = `未匹配到正确的数据【${value}】`;
  }
  for (let i = 0; i < list.length; i++) {
    if (list[i].value == value) {
      result = list[i].label;
      break;
    }
  }
  return result;
}
