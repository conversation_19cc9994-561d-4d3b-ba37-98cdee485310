<template>
  <div class="avatar-img" @mouseenter="isshow = true" @mouseleave="isshow = false">
    <el-image
      ref="img"
      fit="cover"
      :src="img"
      class="image-img"
      :style="{ width: props.width, height: props.height, borderRadius: props.radios }"
      style="margin: 5px 0px 5px 5px"
    >
      <template #error>
        <div class="image-slot">
          <img src="@/assets/images/img-error.png" alt="" style="width: 100%; height: 100%" />
        </div>
      </template>
    </el-image>
    <div v-show="isshow" class="image-img-hover" :style="{ width: props.width, height: props.height, borderRadius: props.radios }">
      <div class="txet">修改头像</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { getToken } from '@/utils/auth';
import Axios from 'axios';

interface Props {
  authSrc?: string;
  width?: string;
  height?: string;
  radios?: string;
}

const props = withDefaults(defineProps<Props>(), {
  authSrc: '',
  width: '300px',
  height: '200px',
  radios: '0'
});

const img = ref<string>('');
const srcList = ref<string[]>([]);
const isshow = ref<boolean>(false);

const getImg = () => {
  if (!props.authSrc) {
    return Promise.resolve('');
  }

  return new Promise<string>((resolve, reject) => {
    try {
      Axios({
        method: 'get',
        url: props.authSrc,
        headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' },
        responseType: 'blob'
      })
        .then((res) => {
          try {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob); // 转换为base64
            reader.onload = function () {
              try {
                img.value = reader.result as string;
                resolve(img.value);
              } catch (error) {
                console.error('读取图片数据出错:', error);
                img.value = '';
                resolve('');
              }
            };
            reader.onerror = function () {
              console.error('FileReader读取出错');
              img.value = '';
              resolve('');
            };
          } catch (error) {
            console.error('处理响应数据出错:', error);
            img.value = '';
            resolve('');
          }
        })
        .catch((error) => {
          console.error('获取图片出错:', error);
          img.value = '';
          resolve('');
        });
    } catch (error) {
      console.error('请求图片过程出错:', error);
      img.value = '';
      resolve('');
    }
  });
};

// 监听
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  },
  { deep: true }
);

onMounted(() => {
  getImg();
});
</script>

<style lang="scss" scoped>
.avatar-img {
  position: relative;
  cursor: pointer;
  .image-img {
    position: absolute;
    top: 0;
    left: 0;
  }
  .image-img-hover {
    position: absolute;
    top: 4px;
    left: 5px;
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    .txet {
      width: 48px;
      height: 17px;
      font-size: 12px;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 17px;
      z-index: 99;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      /* margin-top: 12px; */
      position: absolute;
      top: 24px;
      left: 7px;
      text-align: center;
    }
  }
}

:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd solid 1px;
}
</style>
