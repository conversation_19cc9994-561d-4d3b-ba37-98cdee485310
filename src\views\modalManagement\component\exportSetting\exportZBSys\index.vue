<template>
  <div class="exportZBSys-main">
    <div class="title">坐标系统</div>
    <div class="normal-title">该项是否显示</div>
    <el-select :modelValue="disable" @update:modelValue="(v) => (disable = v)" placeholder="请选择" style="width: 100%">
      <el-option label="显示" :value="0" />
      <el-option label="不显示" :value="1" />
    </el-select>
    <div class="normal-title">提示文字</div>
    <el-input :modelValue="placeholder" @update:modelValue="(v) => (placeholder = v)" placeholder="请输入提示文字" maxlength="10" />
    <div class="normal-title">默认值</div>
    <el-cascader
      :modelValue="defaultValue"
      @update:modelValue="(v) => (defaultValue = v)"
      :options="wkidList"
      :show-all-levels="false"
      placeholder="请设置默认值"
      style="width: 100%"
    />
    <div class="normal-title">是否必填</div>
    <el-select :modelValue="must" @update:modelValue="(v) => (must = v)" placeholder="请选择" style="width: 100%">
      <el-option label="必填" :value="1" />
      <el-option label="不必填" :value="0" />
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';

interface WkidItem {
  value: string | number;
  label: string;
  children?: WkidItem[];
}

const props = defineProps<{
  zbsys: {
    disable: number;
    placeholder: string;
    must: number;
    defaultValue: string;
  };
}>();

// 响应式数据
const wkidList = ref<WkidItem[]>([
  {
    value: 'CGCS_2000',
    label: 'CGCS_2000',
    children: [
      { value: 'cgcs2000_3', label: '三度带', children: [] },
      { value: 'cgcs2000_6', label: '六度带', children: [] }
    ]
  }
]);

const disable = ref(0);
const placeholder = ref('请选择坐标系统');
const must = ref(1);
const defaultValue = ref('');

// 监听props变化
watch(
  () => props.zbsys,
  (val) => {
    disable.value = val.disable;
    placeholder.value = val.placeholder;
    must.value = val.must;
    defaultValue.value = val.defaultValue;
  },
  { deep: true, immediate: true }
);

// 初始化选项
onMounted(() => {
  const list3 = Array.from({ length: 21 }, (_, i) => ({
    value: i + 25,
    label: `${i + 25}`
  }));

  const list6 = Array.from({ length: 11 }, (_, i) => ({
    value: i + 13,
    label: `${i + 13}`
  }));

  wkidList.value[0].children![0].children = list3;
  wkidList.value[0].children![1].children = list6;
});

// 提交方法
const sumbit = () => ({
  disable: disable.value,
  placeholder: placeholder.value,
  must: must.value,
  defaultValue: defaultValue.value
});

defineExpose({ sumbit });
</script>
<style lang="scss" scoped>
.exportZBSys-main {
  width: 100%;
  height: 100%;
  padding: 20px 16px;
  color: #161d26;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .normal-title {
    font-size: 14px;
    margin: 16px 0px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-btn {
      color: var(--current-color);
      cursor: pointer;
    }
  }
}
</style>
