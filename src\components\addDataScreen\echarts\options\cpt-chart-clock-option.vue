<template>
  <el-form label-width="100px">
    <el-form-item label="表盘宽度">
      <el-input-number v-model="attributeCopy.axisLineWidth" :min="1" :max="200" />
    </el-form-item>
    <el-form-item label="表盘字体">
      <el-input-number v-model="attributeCopy.axisLabelSize" :min="5" :max="200" />
    </el-form-item>
    <el-form-item label="字体边距">
      <el-input-number v-model="attributeCopy.axisLabelDistance" :min="1" :max="100" />
    </el-form-item>
    <el-form-item label="刻度边距">
      <el-input-number v-model="attributeCopy.splitLineDistance" :min="1" :max="100" />
    </el-form-item>
    <el-form-item label="时针颜色">
      <el-color-picker v-model="attributeCopy.hourPointerColor" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-clock-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);
</script>

<style scoped></style>
