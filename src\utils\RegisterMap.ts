import * as echarts from 'echarts';

//注册echarts地图
import chinaGeoJson from '@/data/map/china.json';
import bijie from '@/data/map/bijie.json';
import changZhou from '@/data/map/city/changzhou.json';
import guizhou from '@/data/map/city/guizhou.json';
import sihui from '@/data/map/city/sihui.json';
import sihuidijizq from '@/data/map/city/sihuidijizq.json';
import areaGuiYang from '@/data/map/echarGlJson/guiyang.json';
import areaChina from '@/data/map/echarGlJson/china.json';
import areaGuizhou from '@/data/map/echarGlJson/guizhou.json';
import areaYunYanQu from '@/data/map/echarGlJson/yunyanqu.json';
import areaYanChuan from '@/data/map/echarGlJson/yanchuan.json';
import areaBiJie from '@/data/map/echarGlJson/bijie.json';
import bijiePart from '@/data/map/bijiePart.json';

echarts.registerMap('china', chinaGeoJson as any);
echarts.registerMap('bijie', bijie as any);
echarts.registerMap('changZhou', changZhou as any);
echarts.registerMap('guizhou', guizhou as any);
echarts.registerMap('sihui', sihui as any);
echarts.registerMap('sihuidijizq', sihuidijizq as any);
echarts.registerMap('areaGuiYang', areaGuiYang as any);
echarts.registerMap('areaChina', areaChina as any);
echarts.registerMap('areaGuizhou', areaGuizhou as any);
echarts.registerMap('areaYunYanQu', areaYunYanQu as any);
echarts.registerMap('areaYanChuan', areaYanChuan as any);
echarts.registerMap('areaBiJie', areaBiJie as any);
echarts.registerMap('bijiePart', bijiePart as any);
