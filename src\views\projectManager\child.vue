<!--  -->
<template>
  <div class="child-main" @click.stop="closeMenu">
    <div v-for="item in parcelList" :key="item.userId">
      <!-- 成员 -->
      <div class="sub-div">
        <div class="sub-first">
          <i class="el-icon-caret-right tree-ico" v-show="!item.checked"></i>
          <i class="el-icon-caret-bottom tree-ico" v-show="item.checked"></i>
          <div class="tree-label" @click="getParcelInfo(item)">{{ item.parcelName }}</div>
        </div>
        <child
          :parcelList="item.subUser"
          v-if="item.subUser && item.subUser.length != 0"
          v-show="item.checked"
          @getParcelInfo="(item) => emit('getParcelInfo', item)"
          @changeCheckedProject="(item) => emit('changeCheckedProject', item)"
          @getUserwithpts="() => emit('getUserwithpts')"
        ></child>
      </div>
      <!-- 项目 -->
      <div class="sub-div" v-show="item.checked" style="margin-left: 20px">
        <div class="sub-first" v-for="(pin, pidx) in item.pinfo" :key="pidx" @contextmenu.prevent="openMenu($event, pin)">
          <el-checkbox v-model="pin.checked" @change="changeProject(pin)" class="check-box-item">
            <el-tooltip class="item" effect="dark" :content="pin.projectName" placement="bottom">
              <span class="tree-label" :title="pin.projectName">{{ truncateText(pin.projectName, 8) }}</span>
            </el-tooltip>
            <span class="tree-end-label"> 状态:({{ pin.expandTwo == '2' ? '已完成' : '进行中' }}) &nbsp;&nbsp;wkid:({{ pin.wkid }}) </span>
          </el-checkbox>
        </div>
      </div>
    </div>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="endProject">结束项目</li>
      <li @click="editProjectName">修改项目名称</li>
    </ul>
    <ul v-show="visibleStart" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="startProject">开启项目</li>
      <li @click="editProjectName">修改项目名称</li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { endProjectForId, modifyProjectName } from '../../api/projectData';

interface ProjectInfo {
  projectId: string;
  projectName: string;
  wkid: string | number;
  expandTwo: string;
  checked: boolean;
}

interface ParcelItem {
  userId: string | number;
  parcelName: string;
  checked: boolean;
  subUser?: ParcelItem[];
  pinfo?: ProjectInfo[];
}

interface Props {
  parcelList: ParcelItem[];
  checkedWkid?: number;
}

const props = withDefaults(defineProps<Props>(), {
  parcelList: () => [],
  checkedWkid: undefined
});

const emit = defineEmits<{
  (e: 'getParcelInfo', item: ParcelItem): void;
  (e: 'changeCheckedProject', item: ProjectInfo): void;
  (e: 'getUserwithpts'): void;
}>();

// 状态变量
const visible = ref(false);
const visibleStart = ref(false);
const top = ref(0);
const left = ref(0);
const checkedProjectId = ref<string | number>('');
const rightClickItem = ref<ProjectInfo | null>(null);

/**
 * 获取宗地信息
 * @param item 宗地信息
 */
const getParcelInfo = (item: ParcelItem) => {
  emit('getParcelInfo', item);
};

/**
 * 截取文本
 * @param text 文本
 * @param length 长度
 * @returns 截取后的文本
 */
const truncateText = (text: string, length: number): string => {
  return text.length > length ? text.slice(0, length) + '...' : text;
};

/**
 * 改变项目
 * @param item 项目信息
 */
const changeProject = (item: ProjectInfo) => {
  if (props.checkedWkid && item.wkid != props.checkedWkid) {
    ElMessage.error('该项目与选中wkid不一致，不能选中');
    item.checked = false;
    return;
  }
  emit('changeCheckedProject', item);
};

/**
 * 打开菜单
 * @param e 事件
 * @param item 项目信息
 */
const openMenu = (e: MouseEvent, item: ProjectInfo) => {
  if (item.expandTwo != '2') {
    checkedProjectId.value = item.projectId;
    rightClickItem.value = item;
    const x = e.clientX;
    const y = e.clientY;

    top.value = y;
    left.value = x;
    visible.value = true;
  } else if (item.expandTwo == '2') {
    checkedProjectId.value = item.projectId;
    rightClickItem.value = item;
    const x = e.clientX;
    const y = e.clientY;

    top.value = y;
    left.value = x;
    visibleStart.value = true;
  }
};

const closeMenu = () => {
  visible.value = false;
  visibleStart.value = false;
};

/**
 * 结束项目
 */
const endProject = async () => {
  try {
    await ElMessageBox.confirm('请先确认该项目已全部完成，如未完成强行结束该项目则无法继续操作该项目！确定要结束该项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await endProjectForId({ type: '2', projectId: String(checkedProjectId.value) });
    if (res.code == 200) {
      if (rightClickItem.value) {
        rightClickItem.value.expandTwo = '2';
      }
      ElMessage.success('操作成功!');
    } else {
      ElMessage.error(res.msg);
    }
  } catch {
    // 用户取消操作
  }
};

/**
 * 开启项目
 */
const startProject = async () => {
  try {
    await ElMessageBox.confirm('确认要重新开启该项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await endProjectForId({ type: '1', projectId: String(checkedProjectId.value) });
    if (res.code == 200) {
      if (rightClickItem.value) {
        rightClickItem.value.expandTwo = '1';
      }
      ElMessage.success('操作成功!');
    } else {
      ElMessage.error(res.msg);
    }
  } catch {
    // 用户取消操作
  }
};

/**
 * 修改项目名称
 */
const editProjectName = async () => {
  try {
    const { value } = await ElMessageBox.prompt('请输入新的项目名称', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^([a-zA-Z0-9\u4E00-\u9FA5_\\(\\)-])+$/,
      inputErrorMessage: '请输入项目名称'
    });

    const params = {
      id: checkedProjectId.value,
      title: value
    };

    const res = await modifyProjectName(params);
    if (res.code == 200) {
      ElMessage.success('修改成功');
      emit('getUserwithpts');
    } else {
      ElMessage.error(res.msg);
    }
  } catch {
    // 用户取消操作
  }
};
</script>

<style lang="scss" scoped>
.child-main {
  .sub-div {
    margin-left: 10px;

    .sub-first {
      height: 36px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .check-box-item {
        // color: red;
        display: inline-flex;
        justify-content: center;
        align-items: center;
      }

      .tree-ico {
        margin-left: 0px;
        // margin-top: 3px;
      }

      .tree-label {
        margin-left: 5px;
      }

      .tree-end-label {
        margin-left: 10px;
        color: #d3d3d3;
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: fixed;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  }

  .contextmenu li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
  }

  .contextmenu li:hover {
    background-color: rgb(3, 125, 243);
    color: white;
  }
}
</style>
