# 神马调查管理系统

## 项目简介

神马调查管理系统是一个基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目，提供完整的调查管理功能。

## 技术栈

### 核心框架

- Vue 3.3.4 - 渐进式 JavaScript 框架
- TypeScript 5.0.2 - JavaScript 的超集，提供类型系统
- Vite 4.4.9 - 下一代前端构建工具
- Vue Router 4.2.4 - Vue.js 的官方路由管理器

### UI 框架

- Element Plus 2.8.8 - 基于 Vue 3 的组件库
- UnoCSS - 原子化 CSS 引擎
- Animate.css 4.1.1 - CSS 动画库

### 状态管理

- Pinia 2.1.6 - Vue 3 的状态管理库

### 工具库

- Axios 1.7.8 - 基于 Promise 的 HTTP 客户端
- Lodash-es - 实用工具库
- Crypto-js 4.2.0 - 加密库
- File-saver 2.0.5 - 文件保存工具
- XLSX - Excel 文件处理
- ECharts 5.6.0 - 数据可视化图表库
- Monaco Editor - 代码编辑器
- Vue-Quill - 富文本编辑器
- Vue-Esign - 电子签名组件
- Vue-Cropper - 图片裁剪组件

### 地图相关

- @amap/amap-jsapi-loader - 高德地图 JS API
- Esri-loader - ArcGIS API for JavaScript
- Proj4 - 坐标转换库

### 开发工具

- ESLint - 代码检查工具
- Prettier - 代码格式化工具
- Sass - CSS 预处理器
- Vitest - 单元测试框架

### 构建优化

- Vite-plugin-compression - Gzip 压缩
- Vite-plugin-svg-icons-ng - SVG 图标处理
- Unplugin-auto-import - 自动导入 API
- Unplugin-vue-components - 组件自动导入

### 浏览器支持

- Chrome >= 87
- Edge >= 88
- Safari >= 14
- Firefox >= 78

## 快速开始

### 环境要求

- Node.js >= 18.18.0
- npm >= 8.9.0

### 安装

```bash
# 克隆项目
git clone https://gitea.smgis.com/Front-End-Development-Team/sm-survey-manage-sys.git

# 进入项目目录
cd shenma-survey-manage-sys

# 安装依赖
pnpm install
```

### 开发

```bash
# 启动开发服务器
pnpm  dev

# 启动测试环境
pnpm  test
```

### 构建

```bash
# 构建生产环境
pnpm  build:prod

# 构建开发环境
pnpm  build:dev

# 构建测试环境
pnpm  build:test
```

## 部署脚本说明

项目提供了自动化部署脚本，位于 `scripts` 目录下：

- `scripts/deploy-test.sh`：测试环境一键部署脚本
- `scripts/deploy-prod.sh`：生产环境一键部署脚本

**使用方法：**

1. 请根据实际情况修改脚本中的服务器地址、目录等配置。
2. 执行前请确保已完成项目构建（或让脚本自动构建）。
3. 在命令行中运行对应脚本，例如：

```bash
bash scripts/deploy-test.sh
# 或
bash scripts/deploy-prod.sh
```

脚本会自动完成：
- 构建前端产物
- 打包并上传到远程服务器
- 远程备份旧文件并自动清理旧备份
- 解压新包并清理临时文件

如需自定义部署流程，请参考脚本内容进行修改。

## 项目结构

```
sm-survey-manage-sys/           # 项目根目录
├── src/                       # 源代码目录
│   ├── api/                   # API 接口定义
│   │   ├── project/          # 项目管理相关接口
│   │   ├── modal/            # 模态框相关接口
│   │   ├── dataScreen/       # 数据大屏相关接口
│   │   ├── home/             # 首页相关接口
│   │   ├── templateManager/  # 模板管理相关接口
│   │   ├── task/             # 任务管理相关接口
│   │   ├── question/         # 问题管理相关接口
│   │   ├── workflow/         # 工作流相关接口
│   │   ├── system/           # 系统管理相关接口
│   │   ├── shopping/         # 商城相关接口
│   │   ├── sheshinyd/        # 设施相关接口
│   │   ├── projectData/      # 项目数据相关接口
│   │   ├── shareCode/        # 分享码相关接口
│   │   ├── process/          # 流程相关接口
│   │   ├── pay/              # 支付相关接口
│   │   ├── menu/             # 菜单相关接口
│   │   ├── notice/           # 通知相关接口
│   │   ├── login/            # 登录相关接口
│   │   ├── issueManager/     # 问题管理相关接口
│   │   ├── esign/            # 电子签名相关接口
│   │   ├── control/          # 控制相关接口
│   │   ├── client/           # 客户端相关接口
│   │   ├── archives/         # 档案相关接口
│   │   ├── apply/            # 申请相关接口
│   │   ├── forumulation/     # 公式相关接口
│   │   ├── forum/            # 论坛相关接口
│   │   ├── fieldManagement/  # 字段管理相关接口
│   │   ├── tool/             # 工具相关接口
│   │   ├── monitor/          # 监控相关接口
│   │   ├── question.ts       # 问题接口定义
│   │   ├── types.ts          # API 类型定义
│   │   ├── login.ts          # 登录接口定义
│   │   └── menu.ts           # 菜单接口定义
│   │
│   ├── assets/               # 静态资源
│   │   ├── images/          # 图片资源
│   │   ├── logo/            # Logo 资源
│   │   ├── 401_images/      # 401 错误页面图片
│   │   ├── 404_images/      # 404 错误页面图片
│   │   ├── styles/          # 样式文件
│   │   │   ├── index.scss           # 主样式文件
│   │   │   ├── sidebar.scss         # 侧边栏样式
│   │   │   ├── btn.scss             # 按钮样式
│   │   │   ├── element-ui.scss      # Element Plus 样式覆盖
│   │   │   ├── mixin.scss           # 样式混入
│   │   │   ├── ruoyi.scss           # 若依框架样式
│   │   │   ├── transition.scss      # 过渡动画样式
│   │   │   └── variables.module.scss # 样式变量
│   │   ├── font/            # 字体文件
│   │   └── icons/           # 图标资源
│   │
│   ├── components/           # 公共组件
│   │   ├── DynamicForm/           # 动态表单组件
│   │   ├── logList/              # 日志列表组件
│   │   ├── taskDataSearch/       # 任务数据搜索组件
│   │   ├── RightToolbar/         # 右侧工具栏组件
│   │   ├── updateSHP/            # SHP 文件更新组件
│   │   ├── allotData/            # 数据分配组件
│   │   ├── taskFlow/             # 任务流程组件
│   │   ├── formulaEditingDialog/ # 公式编辑对话框
│   │   ├── dataSearch/           # 数据搜索组件
│   │   ├── addDataScreen/        # 添加数据大屏组件
│   │   ├── updateSpe/            # 规格更新组件
│   │   ├── forum/                # 论坛相关组件
│   │   ├── bigDataInteraction/   # 大数据交互组件
│   │   ├── completeness/         # 完整性检查组件
│   │   ├── fastExpression/       # 快速表达式组件
│   │   ├── dialogDraggableSP/    # 可拖拽对话框组件
│   │   ├── updateTPFile/         # 模板文件更新组件
│   │   ├── updateExcel/          # Excel 更新组件
│   │   ├── refreshExpress/       # 刷新表达式组件
│   │   ├── webSignature/         # 网页签名组件
│   │   ├── videoTemp/            # 视频模板组件
│   │   ├── videoDefault/         # 默认视频组件
│   │   ├── updateShpChild/       # SHP 子文件更新组件
│   │   ├── tuopuCheck/           # 拓扑检查组件
│   │   ├── smDraggable/          # 可拖拽组件
│   │   ├── skuMange/             # SKU 管理组件
│   │   ├── oldKJDialog/          # 旧版对话框组件
│   │   ├── flowTaskManager/      # 流程任务管理组件
│   │   ├── editAttr/             # 属性编辑组件
│   │   ├── chooseUserForDept/    # 部门用户选择组件
│   │   ├── addCQGDF/             # 添加 CQGDF 组件
│   │   ├── addAZF/               # 添加 AZF 组件
│   │   ├── TheOneUpload/         # 单文件上传组件
│   │   ├── TheMonacoEditor/      # Monaco 编辑器组件
│   │   ├── selectAreaCode/       # 区域代码选择组件
│   │   ├── authImg/              # 授权图片组件
│   │   ├── ContainerCard/        # 容器卡片组件
│   │   ├── IconSelect/           # 图标选择组件
│   │   ├── autoImage/            # 自动图片组件
│   │   ├── areaCodeTemp/         # 区域代码模板组件
│   │   ├── Pagination/           # 分页组件
│   │   ├── showTreeSetting/      # 树形设置显示组件
│   │   ├── commonLayout/         # 通用布局组件
│   │   ├── TopNav/               # 顶部导航组件
│   │   ├── selectTab/            # 标签选择组件
│   │   ├── iFrame/               # iframe 组件
│   │   ├── TreeSelect/           # 树形选择组件
│   │   ├── UserSelect/           # 用户选择组件
│   │   ├── Editor/               # 编辑器组件
│   │   ├── FileUpload/           # 文件上传组件
│   │   ├── Hamburger/            # 汉堡菜单组件
│   │   ├── ImagePreview/         # 图片预览组件
│   │   ├── ImageUpload/          # 图片上传组件
│   │   ├── ParentView/           # 父视图组件
│   │   ├── Process/              # 流程组件
│   │   ├── RoleSelect/           # 角色选择组件
│   │   ├── Screenfull/           # 全屏组件
│   │   ├── SizeSelect/           # 尺寸选择组件
│   │   ├── SvgIcon/              # SVG 图标组件
│   │   ├── Breadcrumb/           # 面包屑组件
│   │   └── DictTag/              # 字典标签组件
│   │
│   ├── constants/           # 常量定义
│   ├── data/               # 静态数据
│   ├── directive/          # 自定义指令
│   ├── enums/             # 枚举定义
│   ├── hooks/             # 自定义 Hooks
│   ├── lang/              # 国际化文件
│   │   ├── zh-CN/        # 中文语言包
│   │   └── en-US/        # 英文语言包
│   │
│   ├── layout/            # 布局组件
│   │   ├── components/    # 布局相关组件
│   │   └── index.vue      # 主布局文件
│   │
│   ├── plugins/           # 插件配置
│   ├── router/            # 路由配置
│   │   └── index.ts      # 路由主文件
│   ├── store/             # 状态管理
│   │   ├── modules/      # 状态模块
│   │   └── index.ts      # 状态管理入口
│   │
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   │   ├── request.ts           # Axios 请求封装
│   │   ├── auth.ts             # 权限相关工具
│   │   ├── validate.ts         # 表单验证工具
│   │   ├── filters.ts          # 全局过滤器
│   │   ├── format.ts           # 格式化工具
│   │   ├── crypto.ts           # 加密解密工具
│   │   ├── dict.ts             # 字典工具
│   │   ├── errorCode.ts        # 错误码定义
│   │   ├── jsencrypt.ts        # RSA 加密工具
│   │   ├── permission.ts       # 权限控制工具
│   │   ├── propTypes.ts        # 属性类型定义
│   │   ├── ruoyi.ts            # 若依框架工具
│   │   ├── scroll-to.ts        # 滚动工具
│   │   ├── theme.ts            # 主题相关工具
│   │   ├── websocket.ts        # WebSocket 工具
│   │   ├── expressionParser.ts # 表达式解析器
│   │   ├── publicFun.ts        # 公共函数
│   │   ├── RegisterMap.ts      # 注册映射工具
│   │   ├── refreshCptData.ts   # 组件数据刷新工具
│   │   ├── FileUtil.ts         # 文件处理工具
│   │   ├── createCustomNameComponent.tsx # 自定义组件创建工具
│   │   ├── dynamicTitle.ts     # 动态标题工具
│   │   └── index.ts            # 工具函数入口文件
│   │
│   ├── views/             # 页面组件
│   │   ├── login.vue              # 登录页面
│   │   ├── index.vue              # 首页
│   │   ├── error/                 # 错误页面
│   │   │   ├── 401.vue           # 401 未授权页面
│   │   │   └── 404.vue           # 404 未找到页面
│   │   │
│   │   ├── projectManager/        # 项目管理
│   │   ├── questionnaire/         # 问卷调查
│   │   ├── layerMap/             # 图层地图
│   │   ├── issueManager/         # 问题管理
│   │   ├── theBmp/               # 底图管理
│   │   ├── shareCode/            # 分享码管理
│   │   ├── formulation/          # 公式管理
│   │   ├── autoProject/          # 自动项目
│   │   ├── speRegister/          # 规格注册
│   │   ├── preview/              # 预览功能
│   │   ├── companyProject/       # 公司项目
│   │   ├── addProject/           # 添加项目
│   │   ├── forum/                # 论坛管理
│   │   ├── applyForm/            # 申请表
│   │   ├── modalManagement/      # 模态框管理
│   │   ├── profileManagement/    # 个人中心管理
│   │   ├── editTemp/             # 模板编辑
│   │   ├── monitor/              # 监控管理
│   │   ├── upLoadApk/            # APK 上传
│   │   ├── bigData/              # 大数据
│   │   ├── cqgdfData/            # CQGDF 数据
│   │   ├── bigIndexMap/          # 大数据索引地图
│   │   ├── iframeAutoProject/    # iframe 自动项目
│   │   ├── externalDataManager/  # 外部数据管理
│   │   ├── cqgdfList/            # CQGDF 列表
│   │   ├── clientManage/         # 客户端管理
│   │   ├── shareQuestion/        # 分享问题
│   │   ├── system/               # 系统管理
│   │   ├── archivesManagement/   # 档案管理
│   │   ├── redirect/             # 重定向页面
│   │   ├── tool/                 # 工具页面
│   │   └── PrivacyAgreement/     # 隐私协议
│   │
│   ├── App.vue           # 根组件
│   ├── main.ts           # 入口文件
│   ├── permission.ts     # 权限控制
│   ├── settings.ts       # 项目配置
│   └── animate.ts        # 动画配置
│
├── public/               # 静态资源目录
├── dist/                # 构建输出目录
├── node_modules/        # 依赖包目录
├── .vscode/            # VSCode 配置目录
├── vite/               # Vite 配置目录
├── html/               # HTML 模板目录
├── bin/                # 脚本文件目录
├── .cursor/            # Cursor IDE 配置目录
│
├── .env                # 基础环境变量
├── .env.development    # 开发环境变量
├── .env.production     # 生产环境变量
├── .env.test          # 测试环境变量
│
├── .gitignore          # Git 忽略文件
├── .editorconfig       # 编辑器配置
├── .prettierrc         # Prettier 配置
├── .prettierignore     # Prettier 忽略配置
├── .eslintrc-auto-import.json  # ESLint 自动导入配置
├── eslint.config.ts    # ESLint 配置
├── uno.config.ts       # UnoCSS 配置
├── tsconfig.json       # TypeScript 配置
├── vite.config.ts      # Vite 配置
├── vite.config.ts.timestamp-1748416107976-ab84d3b351385.mjs  # Vite 配置备份
├── package.json        # 项目依赖配置
├── pnpm-lock.yaml      # pnpm 依赖锁定文件
├── index.html          # HTML 入口文件
├── office.html         # Office 预览页面
├── LICENSE             # 开源协议
└── README.md           # 项目说明文档
```

## 开发规范

- 使用 TypeScript 进行开发
- 遵循 ESLint 和 Prettier 代码规范
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 使用 Composition API 和 `<script setup>`
- 遵循 Vue 3 最佳实践

## 环境变量

项目使用 `.env` 文件管理环境变量，支持以下环境：

- development
- test
- production

## 代码质量

- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 使用 TypeScript 进行类型检查

## 构建优化

- 使用 Vite 进行快速构建
- 支持代码分割
- 支持资源压缩
- 支持 Gzip 压缩

## 版本历史

- v1.0.0 (2024-03-20)
  - 初始版本发布
  - 基础功能实现

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情
