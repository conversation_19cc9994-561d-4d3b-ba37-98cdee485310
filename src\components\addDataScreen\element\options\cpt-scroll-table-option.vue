<template>
  <div>
    <el-form labelWidth="100px">
      <el-form-item label="显示序号">
        <el-switch v-model="attributeCopy.showIndex" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="表头高度">
        <el-input-number v-model="attributeCopy.theadHeight" :min="1" :max="800" />
      </el-form-item>
      <el-form-item label="表头背景">
        <el-color-picker v-model="attributeCopy.theadBg[0]" />
        <el-color-picker v-model="attributeCopy.theadBg[1]" />
      </el-form-item>
      <el-form-item label="表头字色">
        <el-color-picker v-model="attributeCopy.theadColor" />
      </el-form-item>
      <el-form-item label="表头字号">
        <el-input-number v-model="attributeCopy.theadSize" :min="1" :max="200" />
      </el-form-item>
      <el-form-item label="表格字色">
        <el-color-picker v-model="attributeCopy.tbodyColor" />
      </el-form-item>
      <el-form-item label="表格字号">
        <el-input-number v-model="attributeCopy.tbodySize" :min="1" :max="200" />
      </el-form-item>
      <el-form-item label="奇数行背景">
        <el-color-picker v-model="attributeCopy.oddRowBg" show-alpha />
      </el-form-item>
      <el-form-item label="偶数行背景">
        <el-color-picker v-model="attributeCopy.evenRowBg" show-alpha />
      </el-form-item>
      <el-form-item label="显示行数">
        <el-input-number v-model="attributeCopy.showLine" :min="1" :max="200" />
      </el-form-item>

      <div style="text-indent: 1em">表格列设置：<el-button theme="primary" @click="addCol">新增列</el-button></div>
      <el-table style="font-size: 12px; margin-top: 10px" :data="attribute.columns" @row-click="editRow">
        <el-table-column label="标识" prop="colKey" />
        <el-table-column label="名称" prop="title" />
        <el-table-column label="宽度" prop="width" />
      </el-table>
    </el-form>
    <el-dialog :title="currentRow.id ? '编辑' : '添加'" v-model="modelShow" :append-to-body="true" width="400px">
      <el-form labelWidth="100px">
        <el-form-item label="字段标识">
          <el-input v-model="currentRow.colKey" />
        </el-form-item>
        <el-form-item label="字段名">
          <el-input v-model="currentRow.title" />
        </el-form-item>
        <el-form-item label="字段类型">
          <el-select v-model="currentRow.type">
            <el-option label="文本" value="text" />
            <el-option label="图片" value="img" />
          </el-select>
        </el-form-item>
        <el-form-item label="宽度">
          <el-input-number v-model="currentRow.width" :min="0" :max="1000" />
        </el-form-item>
        <el-form-item v-show="currentRow.id">
          <el-button theme="danger" @click="delCol">删除此列</el-button>
        </el-form-item>
        <el-button v-show="!currentRow.id" @click="confirmRow">添加</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-scroll-table-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);

const modelShow = ref(false);
const currentIndex = ref(0);
const currentRow: any = ref({});

// --- 定义方法 ---
const confirmRow = () => {
  if (!currentRow.value?.colKey || !currentRow.value?.title) {
    ElMessage.error('请输入字段标识或字段名称');
    return;
  }
  const id = new Date().getTime();
  attributeCopy.columns.push(Object.assign(currentRow.value, { id: id }));
  modelShow.value = false;
};
const addCol = () => {
  currentRow.value = { width: 0 };
  modelShow.value = true;
};
const editRow = (row: any) => {
  currentRow.value = row;
  currentIndex.value = attributeCopy.columns.indexOf(row);
  modelShow.value = true;
};
const delCol = () => {
  attributeCopy.columns.splice(currentIndex.value, 1);
  modelShow.value = false;
};
</script>

<style scoped></style>
