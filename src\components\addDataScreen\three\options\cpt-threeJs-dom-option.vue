<template>
  <el-form label-width="100px">
    <el-form-item label="相机X">
      <el-input-number v-model="attributeCopy.cameraX" :min="-180" :max="180" />
    </el-form-item>
    <el-form-item label="相机Y">
      <el-input-number v-model="attributeCopy.cameraY" :min="-180" :max="180" />
    </el-form-item>
    <el-form-item label="相机Z">
      <el-input-number v-model="attributeCopy.cameraZ" :min="-180" :max="180" />
    </el-form-item>
    <el-form-item label="背景颜色">
      <el-color-picker v-model="attributeCopy.bgColor" />
    </el-form-item>
    <el-form-item label="背景透明度">
      <el-input-number v-model="attributeCopy.bgAlpha" :step="0.1" :min="0" :max="1" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-three-dom-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>
