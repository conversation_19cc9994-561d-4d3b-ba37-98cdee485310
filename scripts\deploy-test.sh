#!/bin/bash

# 自动切换到项目根目录
cd "$(dirname "$0")/.."

# === 配置项 ===
REMOTE_USER="root"
REMOTE_HOST="**************"
REMOTE_DIR="/opt/shenma/smdcvue3"                 # 远程部署目录
REMOTE_BACKUP_DIR="/opt/shenma/backup"            # 远程备份目录
DIST_DIR="./dist"                                 # 本地构建目录
ZIP_NAME="dist.zip"                               # 本地压缩包
SSH_PORT=22

# === 构建项目 ===
echo "开始构建 Vue 项目..."
pnpm build:test || { echo "❌ 构建失败"; exit 1; }

# === 压缩本地构建目录 ===
echo "压缩构建产物..."
powershell.exe -Command "Compress-Archive -Path '${DIST_DIR}\\*' -DestinationPath '$ZIP_NAME'" || { echo "❌ 压缩失败"; exit 1; }

# === 备份远程部署目录并保留最新5个 ===
echo "备份远程线上文件..."
BACKUP_FILE="smdcvue3-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
ssh -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST << EOF
  mkdir -p $REMOTE_BACKUP_DIR
  if [ -d "$REMOTE_DIR" ] && [ "\$(ls -A $REMOTE_DIR)" ]; then
    tar -czf $REMOTE_BACKUP_DIR/$BACKUP_FILE -C $REMOTE_DIR .
    echo "✅ 已创建远程备份: $REMOTE_BACKUP_DIR/$BACKUP_FILE"

    # 删除多余的备份文件，只保留最新5个
    cd $REMOTE_BACKUP_DIR
    ls -tp smdcvue3-backup-*.tar.gz | grep -v '/$' | tail -n +6 | xargs -r rm -f
    echo "🧹 已清理旧备份文件，只保留最新5个"
  else
    echo "⚠️ 远程部署目录为空或不存在，跳过备份"
  fi
EOF

# === 清理远程旧文件 ===
echo "清理远程旧文件..."
ssh -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "rm -rf $REMOTE_DIR/*"

# === 上传压缩包 ===
echo "上传压缩包..."
scp -P $SSH_PORT $ZIP_NAME $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/ || { echo "❌ 上传失败"; exit 1; }

# === 解压并清理压缩包 ===
echo "远程解压压缩包..."
ssh -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST << EOF
  cd $REMOTE_DIR
  unzip -o $ZIP_NAME
  rm -f $ZIP_NAME
EOF

# === 本地清理 ===
echo "清理本地压缩包..."
rm -f $ZIP_NAME

# === 完成提示 ===
echo "✅ 部署完成！已备份并自动清理旧文件，请检查 Nginx 状态。"
