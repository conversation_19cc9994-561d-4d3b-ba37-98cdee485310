// 定义节点属性接口
interface NodeProperties {
  title: string;
  initiator?: string | null;
  approvals?: any[];
  userOptional?: boolean;
  actType?: string;
  conditions?: any[];
  isdefault?: boolean;
}

// 定义节点配置接口
interface NodeConfig {
  type: string;
  content: string;
  properties?: NodeProperties;
  isdefault?: boolean;
}

// 定义流程配置接口
interface ProcessConfig {
  start: NodeConfig;
  approver: NodeConfig;
  notifier: NodeConfig;
  audit: NodeConfig;
  condition: NodeConfig;
  route: NodeConfig;
  defaultRoute: NodeConfig;
  branch: NodeConfig;
  empty: NodeConfig;
}

// 导出流程配置
const config: ProcessConfig = {
  start: {
    type: 'start',
    content: '所有人',
    properties: { title: '发起人', initiator: 'ALL' }
  },
  approver: {
    type: 'approver',
    content: '请设置审批人',
    properties: {
      title: '审批人',
      approvals: []
    }
  },
  notifier: {
    type: 'notifier',
    content: '发起人自选',
    properties: {
      title: '抄送人',
      approvals: [],
      userOptional: true,
      actType: 'and'
    }
  },
  audit: {
    type: 'audit',
    content: '办理人自选',
    properties: {
      title: '办理人',
      approvals: [],
      userOptional: true,
      actType: 'normal'
    }
  },
  condition: {
    type: 'condition',
    content: '请设置条件',
    properties: { title: '条件', conditions: [], initiator: null }
  },
  // 这里是条件分支外层包裹的一个childNode节点
  route: {
    type: 'route',
    content: '请设置条件'
    // properties: { title: '条件', conditions: [], initiator: null }
  },
  defaultRoute: {
    type: 'condition',
    content: '其他条件进入此流程',
    properties: { title: '默认条件', conditions: [], initiator: null },
    // 默认条件需要添加一个默认的标识内容
    isdefault: true
  },
  branch: { type: 'branch', content: '', properties: {} },
  empty: { type: 'empty', content: '', properties: {} }
};

export default config;
