<template>
  <table :style="{ width: width + 'px', height: height + 'px' }" style="border-collapse: collapse; color: #fff; text-align: center">
    <colgroup v-if="option.attribute.showIndex" style="width: 60px" />
    <colgroup v-for="item in option.attribute.columns" :key="item.colKey" :style="{ width: item.width > 0 ? item.width + 'px' : '' }" />
    <thead
      style="background-color: #001a59"
      :style="{
        backgroundImage: 'linear-gradient(' + option.attribute.theadBg.toString() + ')',
        height: option.attribute.theadHeight + 'px',
        color: option.attribute.theadColor,
        fontSize: option.attribute.theadSize + 'px'
      }"
    >
      <tr>
        <td v-if="option.attribute.showIndex">序号</td>
        <td v-for="column in option.attribute.columns" :key="column.colKey" :ref="column.colKey">{{ column.title }}</td>
      </tr>
    </thead>
    <tbody name="bounce" appear>
      <tr
        style="position: absolute"
        v-for="(temp, index) in rollData"
        :key="temp.rowNum"
        :style="{
          width: width + 'px',
          height: rowHeight + 'px',
          marginTop: index * rowHeight + 'px',
          backgroundColor: temp.rowNum % 2 === 0 ? option.attribute.oddRowBg : option.attribute.evenRowBg,
          color: option.attribute.tbodyColor,
          fontSize: option.attribute.tbodySize + 'px'
        }"
      >
        <td v-if="option.attribute.showIndex" style="width: 60px" :style="{ lineHeight: rowHeight + 'px' }">{{ temp.rowNum + 1 }}</td>
        <td v-for="column in option.attribute.columns" :key="column.colKey" :style="{ width: colWidths[column.colKey] + 'px' }">
          <div style="position: absolute; top: 50%; transform: translateY(-50%)" :style="{ width: colWidths[column.colKey] + 'px' }">
            <img
              v-if="column.type === 'img'"
              style="width: 60px; height: 60px"
              :src="temp[column.colKey].indexOf('http') === 0 ? temp[column.colKey] : fileUrl + temp[column.colKey]"
            />
            <span v-else>{{ temp[column.colKey] }}</span>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { v1 as uuidv1 } from 'uuid';
const proxy = getCurrentInstance()?.proxy as any;
// --- props ---
const props = defineProps<{
  option: object;
  width: number;
  height: number;
  show: boolean;
}>();

// --- watch ---
watch(
  () => props.option.attribute.showLine,
  () => {
    computeRowHeight();
    scrollTable();
  },
  { deep: true }
);
watch(
  () => props.width,
  () => {
    computeColWidth();
  }
);
watch(
  () => props.height,
  () => {
    computeRowHeight();
  }
);
watch(
  () => props.show,
  (val) => {
    if (val) {
      computeColWidth();
    }
  }
);

// --- 定义变量 ---
const fileUrl = import.meta.env.VITE_APP_BASE_API + '/qjt/file/multi/upload';
const colWidths = ref({});
const rowHeight = ref(10);
const tableData = ref([]);
const rollData = ref([]);
const cptData = ref('');
const uuid = ref(null);
const timer = ref(null);

// --- 定义方法 ---
const computeRowHeight = () => {
  rowHeight.value = (props.height - props.option.attribute.theadHeight) / props.option.attribute.showLine;
};
const computeColWidth = () => {
  colWidths.value = {};
  props.option.attribute.columns.forEach((item) => {
    colWidths.value[item.colKey] = proxy.$refs[item.colKey][0].clientWidth;
  });
};
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  const url = window.location.href;
  const list = url.split('/');
  const moduleId = list[list.length - 2];
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      const parse = res;
      for (let i = 0; i < parse.length; i++) {
        parse[i].rowNum = i;
      }
      tableData.value = parse;
      scrollTable();
    });
  }
};
const scrollTable = () => {
  if (timer.value) {
    clearInterval(timer.value);
  }
  rollData.value = tableData.value.slice(0, props.option.attribute.showLine);
  if (props.option.attribute.showLine >= tableData.value.length) {
    return;
  }
  let i = props.option.attribute.showLine;
  timer.value = setInterval(() => {
    rollData.value.shift();
    rollData.value.push(tableData.value[i]);
    i++;
    if (i >= tableData.value.length) {
      i = 0;
    }
  }, 2000);
};
// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
  computeColWidth();
  computeRowHeight();
});
onBeforeUnmount(() => {
  clearInterval(timer.value);
});
defineOptions({
  name: 'cpt-scroll-table'
});
</script>

<style scoped>
.bounce-enter {
  transform: scaleY(0.5);
  transform-origin: 100% 100%;
}
.bounce-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}
.bounce-enter-active,
.bounce-leave-active {
  transition: all 0.5s;
}
.bounce-move {
  transition: transform 0.5s;
}
</style>
