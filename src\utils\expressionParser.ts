// 解析表达式
import { loadModules } from 'esri-loader';
import { create, all } from 'mathjs';

// 配置接口
interface Config {
  css: string;
  url: string;
}

const config: Config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

/**
 * 不同属性组之间数据获取 最后提交的时候再处理
 * @param value 需要处理的数据
 * @returns 处理后的数据
 */
interface CommonFieldResult {
  expression: string[];
}

const mathFun = create(all);

mathFun.import(
  {
    /**
     * 模拟concat函数，用于拼接字符串
     * @param args 需要拼接的字符串
     * @returns 拼接后的字符串
     */
    concat: function (...args: string[]) {
      return args.join('');
    },
    /**
     * 计算，返回小数点几位
     * @param num 需要处理的数字
     * @param digit 保留位数
     * @returns 处理后的数字
     */
    round: function (num: number, digit: number): string {
      //num 需要处理的数字  digit 保留位数
      return num.toFixed(digit);
    },
    /**
     * toString函数在JavaScript中通常是隐式的，但为了保持一致性，我们仍然定义它
     * @param value 需要转换的字符串
     * @returns 转换后的字符串
     */
    toStringFun: function (value: any): string {
      return String(value);
    },
    /**
     * 不需要处理的数据
     * @param value 需要处理的数据
     * @returns 处理后的数据
     */
    getCommonField: function (value: string): CommonFieldResult {
      const obj: CommonFieldResult = {
        expression: value.split('.')
      };
      return obj;
    },
    /**
     * 将十进制度数转换为度分秒格式
     * @param decimal 需要转换的十进制度数
     * @returns 转换后的度分秒格式
     */
    decimalToDMS: function (decimal: number): string {
      const absDecimal = Math.abs(decimal);

      const degrees = Math.floor(absDecimal);
      const minutes = Math.floor((absDecimal - degrees) * 60);
      let seconds = ((absDecimal - degrees) * 60 - minutes) * 60;

      // 格式化秒数为四位小数
      seconds = parseFloat(seconds.toFixed(4));

      return `${degrees}° ${minutes}' ${seconds}"`;
    },
    /**
     * 得到纬度
     * @param x 需要计算的x坐标
     * @param y 需要计算的y坐标
     * @param shpWkid 需要计算的shpWkid
     * @returns 计算后的纬度
     */
    getLatitude: function (x: number, y: number, shpWkid?: number): Promise<string> {
      return new Promise((resolve, reject) => {
        loadModules(
          ['esri/geometry/Point', 'esri/geometry/geometryEngine', 'esri/Graphic', 'esri/geometry/SpatialReference', 'esri/geometry/projection'],
          config
        ).then(([Point, geometryEngine, Graphic, SpatialReference, projection]: any[]) => {
          projection.load().then(() => {
            const outSR = new SpatialReference({ wkid: 4326 }); // WKID为3857代表WGS 1984 Web Mercator
            const point = new Point({
              x: x,
              y: y,
              spatialReference: {
                wkid: shpWkid || 3857
              }
            });
            const projectedPoints = projection.project(point, outSR);
            // 得到纬度数据
            const absDecimal = Math.abs(projectedPoints.latitude);
            const degrees = Math.floor(absDecimal);
            const minutes = Math.floor((absDecimal - degrees) * 60);
            let seconds = ((absDecimal - degrees) * 60 - minutes) * 60;
            // 格式化秒数为四位小数
            seconds = parseFloat(seconds.toFixed(4));
            resolve(`${degrees}° ${minutes}' ${seconds}"`);
          });
        });
      });
    },
    /**
     * 得到经度
     * @param x 需要计算的x坐标
     * @param y 需要计算的y坐标
     * @param shpWkid 需要计算的shpWkid
     * @returns 计算后的经度
     */
    getLongitude: function (x: number, y: number, shpWkid?: number): Promise<string> {
      return new Promise((resolve, reject) => {
        loadModules(
          ['esri/geometry/Point', 'esri/geometry/geometryEngine', 'esri/Graphic', 'esri/geometry/SpatialReference', 'esri/geometry/projection'],
          config
        ).then(([Point, geometryEngine, Graphic, SpatialReference, projection]: any[]) => {
          projection.load().then(() => {
            const outSR = new SpatialReference({ wkid: 4326 }); // WKID为3857代表WGS 1984 Web Mercator
            const point = new Point({
              x: x,
              y: y,
              spatialReference: {
                wkid: shpWkid || 3857
              }
            });
            const projectedPoints = projection.project(point, outSR);
            // 得到纬度数据
            const absDecimal = Math.abs(projectedPoints.longitude);
            const degrees = Math.floor(absDecimal);
            const minutes = Math.floor((absDecimal - degrees) * 60);
            let seconds = ((absDecimal - degrees) * 60 - minutes) * 60;
            // 格式化秒数为四位小数
            seconds = parseFloat(seconds.toFixed(4));
            resolve(`${degrees}° ${minutes}' ${seconds}"`);
          });
        });
      });
    },
    /**
     * 根据坐标得到两点之间的长度
     * @param p1 需要计算的p1坐标
     * @param p2 需要计算的p2坐标
     * @param p3 需要计算的p3坐标
     * @param p4 需要计算的p4坐标
     * @returns 计算后的长度
     */
    getLineLength: function (p1: number, p2: number, p3: number, p4: number): Promise<string> {
      return new Promise((resolve, reject) => {
        loadModules(
          ['esri/geometry/Point', 'esri/geometry/geometryEngine', 'esri/Graphic', 'esri/geometry/SpatialReference', 'esri/geometry/Polyline'],
          config
        ).then(([Point, geometryEngine, Graphic, SpatialReference, Polyline]: any[]) => {
          const polylinePaths = [
            [p1, p2],
            [p3, p4]
          ];
          // 创建Polyline对象
          const polyline = new Polyline({
            paths: polylinePaths,
            spatialReference: { wkid: 3857 } // 使用WGS84坐标系
          });
          const length = geometryEngine.geodesicLength(polyline, 'meters').toFixed(2);
          resolve(length);
        });
      });
    }
  },
  { override: true }
);

/**
 * 解析表达式的函数
 * @param expression 需要解析的表达式
 * @param index 当前下标
 * @param rings 图形数据集合
 * @param kdx 当前rings循环的第几个图形数据
 * @param transRings 需要计算的transRings
 * @param shpWkid 需要计算的shpWkid
 */
export function parseExpression(expression: string, index: number, rings: number[][][], kdx: number, transRings: any[][], shpWkid?: number) {
  return new Promise(async (resolve, reject) => {
    let begin = 0;
    let endIndex = 0;
    for (let n = 0; n < kdx; n++) {
      begin = begin + rings[n].length;
    }
    const $PointIndex = begin + index + 1; //当前节点下标
    if (rings[kdx].length - 1 == index) {
      //如果index是最后一个节点
      endIndex = 1;
    } else {
      endIndex = begin + index + 2;
    }
    // 把$PointIndex+1替换成计算出来的值
    expression = expression.replace(/\$PointIndex\+1/g, String(endIndex));
    expression = expression.replace(/\$PointIndex/g, String(begin + index + 1));
    // 把$LineIndexLength 替换成 getLineLength 函数
    if (expression.includes('$LineIndexLength')) {
      //首先判断是否有$LineIndexLength常量，才去执行 避免浪费资源
      let funName = '';
      if (index != transRings[kdx].length - 1) {
        //如果不是最后一个点
        funName = `getLineLength(${transRings[kdx][index]},${transRings[kdx][index + 1]})`;
      } else {
        //最后一个点需要回到第一个点去
        funName = `getLineLength(${transRings[kdx][index]},${transRings[kdx][0]})`;
      }
      expression = expression.replace(/\$LineIndexLength/g, funName);
    }
    // 把toString 替换成toStringFun
    if (expression.includes('toString')) {
      expression = expression.replace(/toString/g, 'toStringFun');
    }
    // 把$Geometry.getGeoPoints().get($ChildPoIndex).getX()替换
    if (expression.includes('$Geometry.getGeoPoints().get($ChildPoIndex).getX()')) {
      const x = rings[kdx][index][0];
      expression = expression.replace('$Geometry.getGeoPoints().get($ChildPoIndex).getX()', `round(${x},3)`);
    }
    // 把$Geometry.getGeoPoints().get($ChildPoIndex).getY()替换
    if (expression.includes('$Geometry.getGeoPoints().get($ChildPoIndex).getY()')) {
      const x = rings[kdx][index][1];
      expression = expression.replace('$Geometry.getGeoPoints().get($ChildPoIndex).getY()', `round(${x},3)`);
    }
    // 把$Geometry.getGeoPoints().get($ChildPoIndex).getX()替换
    if (expression.includes('$Geometry.getGeoFirstPoint().getX()')) {
      const x = rings[kdx][index][0];
      expression = expression.replace('$Geometry.getGeoFirstPoint().getX()', `round(${x},3)`);
    }
    // 把$Geometry.getGeoPoints().get($ChildPoIndex).getY()替换
    if (expression.includes('$Geometry.getGeoFirstPoint().getY()')) {
      const x = rings[kdx][index][1];
      expression = expression.replace('$Geometry.getGeoFirstPoint().getY()', `round(${x},3)`);
    }
    // 把wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getY()) //纬度 替换
    if (expression.includes('wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getY())')) {
      const x = rings[kdx][index][0];
      const y = rings[kdx][index][1];
      expression = expression.replace('wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getY())', `getLongitude(${x},${y},${shpWkid})`);
    }
    // 把wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getX()) //经度 替换
    if (expression.includes('wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getX())')) {
      const x = rings[kdx][index][0];
      const y = rings[kdx][index][1];
      expression = expression.replace('wgs84ToDMS($GeoWgs84.getGeoPoints().get($PointIndex).getX())', `getLatitude(${x},${y},${shpWkid})`);
    }
    // 把toStr() 替换成toStr 把toDb() 替换成toDb
    expression = expression.replace(/\.toStr\(\)/g, '');
    expression = expression.replace(/\.toDb\(\)/g, '');
    try {
      const result = mathFun.evaluate(expression);
      resolve(result);
    } catch (e) {
      

      reject();
    }
  });
}
