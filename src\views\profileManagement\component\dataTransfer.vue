<!-- 数据迁移 -->
<template>
  <div class="dataTransfer-main">
    <div class="handle">
      <el-input v-model="form.id" placeholder="请输入ID" style="margin-right: 10px; width: 240px" clearable @clear="getData"></el-input>
      <!-- <areaCodeTemp
            style="widht: 100%"
            :defautCode="form.areaCode"
            @changeCityCode="changeCityCode"
            @changeCodeGetName="changeCodeGetName"
            ref="areaCodeRef"
        ></areaCodeTemp> -->
      <el-button type="primary" style="margin-left: 10px" @click="getData">查询</el-button>
      <el-button type="primary" @click="addTransport">新增迁移</el-button>
    </div>
    <el-table :data="tableData" style="width: 100%; margin-top: 20px" :height="tableHeight" border>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="iDV5" prop="idV5"></el-table-column>
      <el-table-column label="iDV6" prop="idV6"></el-table-column>
      <el-table-column label="宗地名称" prop="parcelName"></el-table-column>
      <el-table-column label="地址" prop="address"></el-table-column>
      <el-table-column label="迁移的状态" prop="transportState">
        <template #default="scope">
          <el-tag size="small" v-show="scope.row.transportState == 1">正常</el-tag>
          <el-tag size="small" type="warning" v-show="scope.row.transportState == -1">数据不存在</el-tag>
          <el-tag size="small" type="danger" v-show="scope.row.transportState == 0">失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="迁移结果描述" prop="transportDesc"></el-table-column>
      <el-table-column label="最后一次迁移时间" prop="lastTime">
        <template #default="scope">
          {{ formatDateAndTimeType(scope.row.lastTime) }}
        </template>
      </el-table-column>
      <el-table-column label="迁移结果json" prop="transportResult">
        <template #default="scope">
          <span v-for="(item, index) in Object.keys(scope.row.transportResult)" :key="index">
            {{ item }}:{{ Object.values(scope.row.transportResult)[index] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 数据迁移dialog -->
    <el-dialog title="数据迁移" v-model="dialogVisible" width="600px" :close-on-click-modal="false" :before-close="handleClose">
      <el-form
        :model="addTransportFrom"
        :rules="addTransportFromRules"
        ref="addTransportFromRef"
        label-width="70px"
        class="demo-ruleForm"
        label-position="top"
      >
        <el-form-item label="行政区划（选中区域的数据将会被迁移）">
          <areaCodeTemp
            style="width: 100%"
            @changeCityCode="changeCityCodeAdd"
            @changeCodeGetName="changeCodeGetName"
            ref="areaCodeRef"
          ></areaCodeTemp>
        </el-form-item>
        <el-form-item label="数据ID（用英文逗号分隔）">
          <el-input type="textarea" :rows="10" placeholder="请输入id,用英文逗号分割" v-model="addTransportFrom.ids"> </el-input>
        </el-form-item>
        <el-form-item label="是否删除老的数据">
          <el-radio-group v-model="addTransportFrom.deleteOld">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 进度弹窗 -->
    <el-dialog
      title="迁移进度"
      v-model="speedDialog"
      width="300px"
      :show-close="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleCloseSpeed"
    >
      <div class="deep-div">
        <el-progress type="circle" :percentage="progressNum"></el-progress>
        <div style="margin-top: 10px" v-show="progressMsg">总数：{{ progressMsg }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import areaCodeTemp from '@/components/areaCodeTemp/index.vue';
import { transportList, doTransport, transportSpeed } from '@/api/home';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';

// 定义接口和类型
interface Form {
  id: string;
  areaCode: string;
  areaCodeName: string;
}

interface AddTransportForm {
  areaCode: string;
  ids: string;
  deleteOld: number;
}

interface TableItem {
  idV5: string;
  idV6: string;
  parcelName: string;
  address: string;
  transportState: number;
  transportDesc: string;
  lastTime: string;
  transportResult: Record<string, any>;
}

interface TransportParams {
  areaCode: string;
  ids: string[];
  deleteOld: number;
}

interface TransportSpeedResponse {
  rate: number | null;
  total: string;
}

// 格式化日期时间函数
const formatDateAndTimeType = (date: string | Date): string => {
  const d = new Date(date);
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  const seconds = d.getSeconds().toString().padStart(2, '0');

  return `${d.getFullYear()}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// ref定义
const form = ref<Form>({
  id: '',
  areaCode: '',
  areaCodeName: ''
});

const tableData = ref<TableItem[]>([]);
const tableHeight = computed(() => `${window.innerHeight - 300}px`);
const dialogVisible = ref(false);
const speedDialog = ref(false);
const progressNum = ref(0);
const errorNum = ref(0);
const progressMsg = ref('');
const areaCodeRef = ref();
const addTransportFromRef = ref<FormInstance>();

const addTransportFrom = ref<AddTransportForm>({
  areaCode: '',
  ids: '',
  deleteOld: 0
});

const addTransportFromRules = ref({});

// 方法定义
const getData = () => {
  transportList(form.value).then((res: any) => {
    if (res.code === 200) {
      tableData.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const changeCityCode = (code: string) => {
  form.value.areaCode = code;
};

const changeCodeGetName = (name: string) => {
  form.value.areaCodeName = name;
};

const addTransport = () => {
  dialogVisible.value = true;
};

const handleClose = () => {
  addTransportFrom.value = {
    areaCode: '',
    ids: '',
    deleteOld: 0
  };
  progressNum.value = 0;
  errorNum.value = 0;
  progressMsg.value = '';

  if (areaCodeRef.value) {
    areaCodeRef.value.clearAreaCode();
  }

  dialogVisible.value = false;
};

const changeCityCodeAdd = (code: number | string) => {
  addTransportFrom.value.areaCode = code.toString();
};

const submit = () => {
  if (addTransportFrom.value.ids.length === 0 && !addTransportFrom.value.areaCode) {
    ElMessage.error('请至少输入一个内容！！！');
    return;
  }

  const params: TransportParams = {
    areaCode: addTransportFrom.value.areaCode,
    ids: [],
    deleteOld: addTransportFrom.value.deleteOld
  };

  if (addTransportFrom.value.ids) {
    params.ids = addTransportFrom.value.ids.split(',');
  }

  if (params.ids.length > 3000) {
    ElMessage.error('建议一次性迁移的数据不要大于3000条数据，如果大于3000条也只会取前3000条数据！！！');
    params.ids = params.ids.slice(0, 3000);
  }

  doTransport(params).then((res: any) => {
    if (res.code === 200) {
      dialogVisible.value = false;
      addTransportFrom.value = {
        areaCode: '',
        ids: '',
        deleteOld: 0
      };

      if (Object.keys(res.data).length !== 0) {
        speedDialog.value = true;
        errorNum.value = 0;
        transportSpeedHandler(res.data);
      } else {
        ElMessage.error('迁移异常');
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const transportSpeedHandler = (id: string) => {
  transportSpeed({ key: id }).then((res: any) => {
    if (res.code === 200) {
      if (res.data.rate !== undefined) {
        progressNum.value = res.data.rate || 0;
        progressMsg.value = res.data.total;
      }

      if (res.data.rate === null) {
        errorNum.value++;
      }

      if (errorNum.value >= 3) {
        ElMessage.error('迁移异常');
        speedDialog.value = false;
        getData();
      }

      if (res.data.rate !== null && res.data.rate < 100) {
        setTimeout(() => {
          transportSpeedHandler(id);
        }, 3000);
      } else if (res.data.rate !== null && res.data.rate >= 100) {
        speedDialog.value = false;
        ElMessage.success('迁移完成');
        getData();
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleCloseSpeed = () => {
  speedDialog.value = false;
};

// 暴露给父组件的方法
defineExpose({
  getData
});
</script>

<style lang="scss" scoped>
.dataTransfer-main {
  font-size: 14px;
  .handle {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}
.deep-div {
  padding: 10px 40px;
  text-align: center;
}
</style>
