<!-- 老的勘界内容 -->
<template>
  <div class="oldKJDialog-main">
    <el-dialog
      title="导出成果"
      v-model="kjDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1100px"
      :before-close="handleClose"
    >
      <el-table :data="tableData" style="width: 100%" height="400" border>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="批次号" prop="batch"></el-table-column>
        <el-table-column label="项目名称" prop="xmmc"></el-table-column>
        <el-table-column label="户数" prop="num"></el-table-column>
        <el-table-column label="上传时间">
          <template v-slot="scope">{{ formatDateYmdhm(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="scope">
            <el-link type="primary" @click="downLoadKJ(scope.row)">导出</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 林业导出设置新内容 -->
    <el-dialog
      title="导出设置"
      v-model="exportSettingDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="500px"
      @open="onExportSettingDialogOpen"
    >
      <div class="dialog-other-content">
        <div class="dialog-other-row">
          <div style="margin-bottom: 10px">案例： 乡镇名称：某某镇 <span style="margin-left: 20px"></span> 批次号：某某镇2023年度第八批次</div>
          <el-form :model="settingExportForm" :rules="settingExportFormRules" ref="settingExportFormRef" label-width="120px" class="demo-ruleForm">
            <el-form-item label="乡镇名称" prop="areaCode">
              <!-- <area-code-temp style="widht: 100%" :selectAreaCode="settingExportForm.areaCode" @changeCityCode="changeCityCode"></area-code-temp> -->
              <areaCodeTemp
                style="width: 100%"
                @changeCityCode="changeCityCodeAdd"
                @changeCodeGetName="changeCodeGetName"
                ref="areaCodeRef"
              ></areaCodeTemp>
            </el-form-item>
            <el-form-item label="填报单位名称" prop="tbdwmc">
              <el-input v-model="settingExportForm.tbdwmc" placeholder="请输入填报单位名称" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="单位负责人" prop="dwfzr">
              <el-input v-model="settingExportForm.dwfzr" placeholder="请输入单位负责人" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="资料复审人" prop="zlfsr">
              <el-input v-model="settingExportForm.zlfsr" placeholder="请输入资料复审人" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="资料审核人" prop="zlshr">
              <el-input v-model="settingExportForm.zlshr" placeholder="请输入资料审核人" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="项目负责人" prop="xmfzr">
              <el-input v-model="settingExportForm.xmfzr" placeholder="请输入项目负责人" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="带号" prop="dh">
              <el-input v-model="settingExportForm.dh" placeholder="请输入带号" class="dialog-other-end"></el-input>
            </el-form-item>
            <el-form-item label="公司信息" prop="companyName">
              <el-select v-model="settingExportForm.companyName" placeholder="请选择" style="width: 100%" @change="changeCompany">
                <el-option v-for="item in companyList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="简易导出">
              <el-radio v-model="settingExportForm.simple" :label="true">是</el-radio>
              <el-radio v-model="settingExportForm.simple" :label="false">否</el-radio>
            </el-form-item>
            <el-form-item label="导出坐标系">
              <el-radio v-model="settingExportForm.wkType" :label="1">投影坐标系</el-radio>
              <el-radio v-model="settingExportForm.wkType" :label="2">大地坐标系</el-radio>
            </el-form-item>
            <el-checkbox v-model="isNoDel" v-if="userStore['userName'] === '18285070490'">不删除服务器数据</el-checkbox>
          </el-form>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="exportSettingDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitExportSetting">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { kjList, exportPcNew as exportPcNewApi } from '@/api/project';
// import AreaCodeTemp from './areaCodeTemp.vue';
import areaCodeTemp from '@/components/areaCodeTemp/index.vue';
import { useUserStore } from '@/store/modules/user';
import { formatDateYmdhm } from '@/utils/filters';
const userStore = useUserStore();

// --- props ---
interface Props {
  kjDialog: boolean;
  companyData: Record<string, any> | unknown;
}

const props = withDefaults(defineProps<Props>(), {
  kjDialog: false
});
const kjDialogCopy = computed(() => props.kjDialog);

// --- 定义emit ---

const emit = defineEmits(['closeKJDialog', 'sumbitDownloadKJ']);

// --- 声明变量 ---
const tableData = ref([]);
const exportSettingDialog = ref(false);
const xzName = ref(''); //乡镇名称
const pcH = ref(''); //批次号
// 导出设置的对象
const settingExportForm = reactive({
  areaCode: undefined,
  batchName: undefined,
  dh: undefined,
  dwfzr: undefined,
  folderName: undefined,
  sbdw: undefined,
  tbdwmc: undefined,
  xmfzr: undefined,
  xmmc: undefined,
  xz: undefined,
  zlfsr: undefined,
  zlshr: undefined,
  num: undefined,
  companyName: '',
  simple: false,
  wkType: 2
});
const settingExportFormRules = reactive({
  areaCode: [{ required: true, message: '请选择行政区域', trigger: ['change', 'blur'] }],
  companyName: [{ required: true, message: '请选择公司信息', trigger: ['change', 'blur'] }],
  batchName: [{ required: true, message: '请输入批次号', trigger: ['change', 'blur'] }],
  dh: [{ required: true, message: '请输入带号', trigger: ['change', 'blur'] }],
  dwfzr: [{ required: true, message: '请输入单位负责人', trigger: ['change', 'blur'] }],
  folderName: [{ required: true, message: '请输入文件名', trigger: ['change', 'blur'] }],
  sbdw: [{ required: true, message: '请输入申报单位', trigger: ['change', 'blur'] }],
  tbdwmc: [{ required: true, message: '请输入填报单位名称', trigger: ['change', 'blur'] }],
  xmfzr: [{ required: true, message: '请输入项目负责人', trigger: ['change', 'blur'] }],
  xmmc: [{ required: true, message: '请输入项目名称', trigger: ['change', 'blur'] }],
  zlfsr: [{ required: true, message: '请输入资料复审人', trigger: ['change', 'blur'] }],
  zlshr: [{ required: true, message: '请输入资料审核人', trigger: ['change', 'blur'] }]
});
const companyList = ref<Array<{ label: string; value: string }>>([]); //公司列表
const isNoDel = ref(false); //是否删除服务器zip包 默认都是删除的
// 导出设置表单ref
const settingExportFormRef = ref();

// --- 监听 ---
watch(kjDialogCopy, (val) => {
  if (val) {
    isNoDel.value = false;
    getData();
  }
});

// --- 方法 ---

const changeCompany = (val) => {
  settingExportForm.companyName = val;
};

/**
 * 导出设置弹窗打开回调
 */
const onExportSettingDialogOpen = () => {
  const companyData = props.companyData as any;
  companyList.value = [];
  companyList.value.push({ label: `${companyData.companyName}(主公司)`, value: companyData.companyName });
  companyData.sysCompanyChildList.forEach((v) => {
    companyList.value.push({ label: `${v.companyName}(关联公司)`, value: v.companyName });
  });
  settingExportForm.companyName = companyData.companyName;
};

/**
 * 获取数据
 */
const getData = () => {
  kjList().then((res) => {
    if (res.code == 200) {
      tableData.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 关闭
 */
const handleClose = () => {
  emit('closeKJDialog');
};

/**
 * 导出设置
 */
const exportSetting = (row) => {
  ElMessageBox.confirm('确认要下载吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('sumbitDownloadKJ', row.batchName);
    })
    .catch(() => {});
};

/**
 * 导出勘界
 */
const downLoadKJ = (row) => {
  settingExportForm.batchName = row.batch;
  settingExportForm.xmmc = row.xmmc;
  settingExportForm.num = row.num;
  exportSettingDialog.value = true;
};

/**
 * 提交设置林业导出
 */
const submitExportSetting = () => {
  settingExportFormRef.value.validate((valid) => {
    if (valid) {
      exportPcNew();
      exportSettingDialog.value = false;
    } else {
      return false;
    }
  });
};

// /**
//  * 选择城市code变化
//  */
// const changeCityCode = (code, codeList, address) => {
//   settingExportForm.areaCode = code;
//   settingExportForm.xz = address;
// };

const changeCityCodeAdd = (code: number | string) => {
  settingExportForm.areaCode = code.toString();
  settingExportFormRef.value.clearValidate('areaCode');
};

const changeCodeGetName = (name: string) => {
  settingExportForm.xz = name;
};

/**
 * 10月23日版本导出报告
 */
const exportPcNew = () => {
  ElMessageBox.confirm('确定要导出此条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('sumbitDownloadKJ', settingExportForm, isNoDel.value);
    })
    .catch(() => {});
};
</script>
<style lang="scss" scoped>
.dialog-other-content {
  width: 100%;
  .dialog-other-row {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .dialog-other-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 10px;
      .dialog-other-label {
        width: 70px;
        text-align: right;
      }
      .dialog-other-end {
        flex: 1;
      }
    }
  }
}
</style>
