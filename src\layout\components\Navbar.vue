<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggle-click="toggleSideBar" />
    <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu flex align-center">
      <template v-if="appStore.device !== 'mobile'">
        <search-menu ref="searchMenuRef" />
        <el-tooltip content="搜索" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect" @click="openSearchMenu">
            <svg-icon class-name="search-icon" icon-class="search" />
          </div>
        </el-tooltip>
        <!-- 消息 -->
        <!-- <el-tooltip content="消息" effect="dark" placement="bottom">
          <div>
            <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
              <template #reference>
                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                  <svg-icon icon-class="message" />
                </el-badge>
              </template>
              <template #default>
                <notice></notice>
              </template>
            </el-popover>
          </div>
        </el-tooltip> -->
        <el-tooltip content="全屏" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip>

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>
      <div class="avatar-container">
        <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
          <div class="avatar-wrapper">
            <user-img
              v-if="userStore.avatar"
              :authSrc="`${AVATAR_PREFIX_URL}${userStore.avatar}?att=1`"
              :width="'30px'"
              :height="'30px'"
              style="margin: 0"
              radius="6px"
            ></user-img>
            <img :src="defAva" class="user-avatar" v-else />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <div class="profile-card">
                <div>
                  <user-img
                    v-if="userStore.avatar"
                    class="img"
                    :authSrc="`${AVATAR_PREFIX_URL}${userStore.avatar}?att=1`"
                    :width="'32px'"
                    :height="'32px'"
                    radius="6px"
                  />
                  <img v-else :src="defAva" class="user-avatar" />
                  <div class="user-name">{{ userStore.nickname as string }}</div>
                  <div class="user-dept-name">{{ user?.companyName }}</div>
                </div>
              </div>
              <router-link to="/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <router-link :to="{ path: '/profile', query: { activeName: 'first' } }">
                <el-dropdown-item>基本信息</el-dropdown-item>
              </router-link>
              <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item command="changeCompany">
                <span>切换组织</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span style="color: #ff3d57">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <!-- 切换组织 -->
    <el-dialog v-model="dialogVisible" title="切换组织" append-to-body :close-on-click-modal="false" width="400px" :before-close="handleClose">
      <div class="dialog-content">
        <div
          v-for="(item, index) in companies"
          :key="index"
          class="dialog-item"
          :class="{ 'active': item.companyId == user?.companyId }"
          @click="qiehuan(item)"
        >
          <div>{{ item.companyName }}</div>
          <div v-show="item.companyId == user?.companyId"><i class="el-icon-check" /></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import defAva from '@/assets/images/profile.png';
import SearchMenu from './TopBar/search.vue';
import { useAppStore } from '@/store/modules/app';
import { useUserStore } from '@/store/modules/user';
import { useSettingsStore } from '@/store/modules/settings';
import { useNoticeStore } from '@/store/modules/notice';
import notice from './notice/index.vue';
import router from '@/router';
import { ElMessageBoxOptions } from 'element-plus/es/components/message-box/src/message-box.type';
import { AVATAR_PREFIX_URL } from '@/constants/index';
import UserImg from './UserImg/index.vue';
import { companyList as getCompanyList, switchCompany } from '@/api/login/index';
import { setToken } from '@/utils/auth';
import { getMineNum } from '@/api/notice';
import { useTagsViewStore } from '@/store/modules/tagsView';

// 公司类型接口
interface Company {
  companyId: number;
  companyName: string;
  [key: string]: any;
}

// 用户类型接口
interface User {
  phonenumber?: string;
  companyId?: number;
  companyName?: string;
  [key: string]: any;
}

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const noticeStore = storeToRefs(useNoticeStore());
const newNotice = ref<number>(0);

// 组织切换相关
const dialogVisible = ref(false);
const companies = ref<Company[]>([]);
const user = ref<User>({});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// 搜索菜单
const searchMenuRef = ref<InstanceType<typeof SearchMenu>>();

const tagsViewStore = useTagsViewStore();

// 初始化获取通知数量
onMounted(() => {
  getNoticeNum();
  user.value = userStore.user;
});

// 获取通知数量
const getNoticeNum = () => {
  getMineNum({ status: 0 }).then((res: any) => {
    if (res.code === 200) {
      newNotice.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const openSearchMenu = () => {
  searchMenuRef.value?.openSearch();
};

const toggleSideBar = () => {
  appStore.toggleSideBar(false);
};

// 切换组织
const changeCompany = () => {
  if (userStore.user && (userStore.user as any).phonenumber) {
    getCompanyList((userStore.user as any).phonenumber).then((res: any) => {
      if (res.code === 200) {
        companies.value = res.data;
        dialogVisible.value = true;
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 切换到指定组织
const qiehuan = (item: Company) => {
  if (item.companyId !== user.value?.companyId) {
    ElMessageBox.confirm('是否要切换组织?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        switchCompany({ companyId: item.companyId, from: 'web' }).then((res: any) => {
          if (res.code === 200) {
            setToken(res.data['access_token']);
            // 在这添加是否切换公司的标识
            sessionStorage.setItem('qiehuan_company', 'true');
            location.href = '/profile';
          } else {
            ElMessage.error(res.msg);
          }
        });
      })
      .catch(() => {
        // 用户取消
      });
  }
};

const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  } as ElMessageBoxOptions);
  userStore.logout().then(() => {
    // 当退出登录的时候，删除该账号存储在sessionstorage中的数据
    sessionStorage.removeItem('filterMsg');
    // 清空 tagview
    tagsViewStore.delAllViews();
    router.replace({
      path: '/login',
      query: {
        redirect: encodeURIComponent(router.currentRoute.value.fullPath || '/')
      }
    });
  });
};

const emits = defineEmits(['setLayout']);
const setLayout = () => {
  emits('setLayout');
};
// 定义Command方法对象 通过key直接调用方法
const commandMap: { [key: string]: any } = {
  setLayout,
  logout,
  changeCompany
};
const handleCommand = (command: string) => {
  // 判断是否存在该方法
  if (commandMap[command]) {
    commandMap[command]();
  }
};
//用深度监听 消息
watch(
  () => noticeStore.state.value.notices,
  (newVal) => {
    newNotice.value = newVal.filter((item: any) => !item.read).length;
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
:deep(.el-select .el-input__wrapper) {
  height: 30px;
}

:deep(.el-badge__content.is-fixed) {
  top: 12px;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  //background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        .user-avatar {
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.dialog-content {
  height: 400px;
  overflow: auto;
}

.dialog-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  background: #f6f7f8;
  border-radius: 8px;
  cursor: pointer;
  justify-content: space-between;
}

.dialog-item:hover {
  color: #1890ff;
  background: #edf6ff;
}

.active {
  color: #1890ff;
  background: #edf6ff;
  cursor: not-allowed;
}

.profile-card {
  width: 223px;
  height: 120px;
  background: linear-gradient(135deg, #d0e7ff 0%, #d9fbff 100%);
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
  text-align: center;
  transform: translateY(-5px);

  .user-name {
    font-size: 14px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #161d26;
    line-height: 20px;
  }

  .user-dept-name {
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #8291a9;
    line-height: 17px;
  }
}

:deep(.el-dropdown-menu--medium) {
  padding: 0 !important;
}
</style>
