import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TaskSearchParams, TaskParams, TaskDeleteParams, StepParams, StepUpdateParams } from '@/api/task/types';

/**
 * 查询创建和收到的任务
 * @param params 查询参数
 * @param type 类型
 * @returns {AxiosPromise}
 */
export function getSearchTask(params: TaskSearchParams, type: number = 0): AxiosPromise<any> {
  return request({
    url: `/qjt/task/select?type=${type}`,
    method: 'post',
    data: params
  });
}

/**
 * 新建任务
 * @param params 任务参数
 * @returns {AxiosPromise}
 */
export function addTask(params: TaskParams): AxiosPromise<any> {
  return request({
    url: '/qjt/task/save',
    method: 'post',
    data: params
  });
}

/**
 * 修改任务
 * @param params 任务参数
 * @returns {AxiosPromise}
 */
export function updateTask(params: TaskParams): AxiosPromise<any> {
  return request({
    url: '/qjt/qjttask/update/task',
    method: 'post',
    data: params
  });
}

/**
 * 删除任务
 * @param type 类型
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function deleteTask(type: string | number, params: TaskDeleteParams): AxiosPromise<any> {
  return request({
    url: `/qjt/qjttask/opt/task/${type}`,
    method: 'post',
    data: params
  });
}

/**
 * 根据任务id查询任务
 * @param taskId 任务ID
 * @returns {AxiosPromise}
 */
export function selectTaskByTaskId(taskId: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/qjttask/detail/${taskId}`,
    method: 'post'
  });
}

/**
 * 查询我的任务所有
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getAllTask(params: TaskSearchParams): AxiosPromise<any> {
  return request({
    url: `/qjt/qjttask/search/taskList`,
    method: 'post',
    data: params
  });
}

/**
 * 查询具体任务详情
 * @param id 任务ID
 * @returns {AxiosPromise}
 */
export function getTaskDetail(id: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/task/selectById?Id=${id}`,
    method: 'post'
  });
}

/**
 * 根据地块id查询步骤列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getSelectStep(params: StepParams): AxiosPromise<any> {
  return request({
    url: `/qjt/task/selectStepList`,
    method: 'post',
    params: params
  });
}

/**
 * 更新任务步骤
 * @param params 更新参数
 * @returns {AxiosPromise}
 */
export function taskStepUpdate(params: StepUpdateParams): AxiosPromise<any> {
  return request({
    url: `/qjt/task/updateStep`,
    method: 'post',
    data: params
  });
}
