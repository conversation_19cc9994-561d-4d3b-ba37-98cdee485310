import { to as tos } from 'await-to-js';
import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import { isHttp, isPathMatch } from '@/utils/validate';
import { isRelogin } from '@/utils/request';
import { useUserStore } from '@/store/modules/user';
import { useSettingsStore } from '@/store/modules/settings';
import { usePermissionStore } from '@/store/modules/permission';
import { ElMessage } from 'element-plus/es';

NProgress.configure({ showSpinner: false });
const whiteList = ['/login', '/register', '/forgetPassword', '/externalDataManager', '/upLoadApk'];

const isWhiteList = (path: string) => {
  return whiteList.some((pattern) => isPathMatch(pattern, path));
};

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title as string);
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' });
    } else if (isWhiteList(to.path)) {
      next();
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        const [err, res] = await tos(useUserStore().getInfo());
        if (err) {
          await useUserStore().logout();
          ElMessage.error(err);
          next({ path: '/' });
        } else {
          isRelogin.show = false;
          const accessRoutes = await usePermissionStore().generateRoutes();
          // 根据roles权限生成可访问的路由表
          accessRoutes.forEach((route) => {
            if (!isHttp(route.path)) {
              router.addRoute(route); // 动态添加可访问路由表
            }
          });

          //企业用户有没有数据大屏权限
          const isQyIndex = accessRoutes.some((route) => route.path === '/' && route.children.some((child) => child.path === 'index'));

          if (to.path === '/index') {
            // 进入index需要判断
            if (res.vipType === 2) {
              // 专业版用户，重定向到个人中心
              next(`/profile?activeName=first`);
            } else if (res.vipType === 3) {
              // 企业用户
              if (isQyIndex) {
                // 企业用户并且有数据大屏权限
                // @ts-expect-error hack方法 确保addRoutes已完成
                next({ path: to.path, replace: true, params: to.params, query: to.query, hash: to.hash, name: to.name as string });
              } else {
                next(`/profile?activeName=first`);
              }
            }
          } else {
            // @ts-expect-error hack方法 确保addRoutes已完成
            next({ path: to.path, replace: true, params: to.params, query: to.query, hash: to.hash, name: to.name as string });
          }
        }
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (isWhiteList(to.path)) {
      // 在免登录白名单，直接进入
      next();
    } else if (to.params.moduleId) {
      next();
    } else {
      const redirect = encodeURIComponent(to.fullPath || '/');
      next(`/login?redirect=${redirect}`); // 否则全部重定向到登录页
    }
  }
  NProgress.done(); // 在每个导航路径的末尾调用
});

router.afterEach(() => {
  NProgress.done();
});
