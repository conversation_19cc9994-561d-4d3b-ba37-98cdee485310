<template>
  <div style="height: 100%">
    <button
      style="width: 100%; height: 100%; border: none"
      @click="redirect()"
      :style="{ background: option.attribute.bgColor, color: option.attribute.textColor, borderRadius: option.attribute.radius + 'px' }"
    >
      {{ cptData.value }}
    </button>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { v1 as uuidv1 } from 'uuid';
import { useRouter } from 'vue-router';

const router = useRouter();

// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();

// --- 定义emit ---
const emit = defineEmits<{
  (e: 'reload'): void;
}>();

// --- 定义变量 ---
const cptData = ref({});
const uuid = ref(null);

// --- 定义方法
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData);
};
defineExpose({
  refreshCptData
});
const loadData = () => {
  getDataJson(props.option.cptDataForm).then((res) => {
    cptData.value = res;
  });
};

const redirect = () => {
  if (props.option.attribute.url) {
    if (props.option.attribute.url.startsWith('view')) {
      router.push(props.option.attribute.url);
      emit('reload');
    } else {
      window.open(props.option.attribute.url);
    }
  }
};

// --- onMounted ---
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});

defineOptions({
  name: 'cpt-button'
});
</script>

<style scoped></style>
