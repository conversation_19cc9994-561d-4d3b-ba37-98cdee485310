<template>
  <div style="font-family: hooge; height: 100%" @click="showInteraction">
    <table style="width: 100%; height: 100%; text-align: center">
      <tbody>
        <tr style="height: 100%">
          <td
            v-for="item in cptData.value.length"
            :key="item"
            :style="{ width: (1 / cptData.value.length) * 100 + '%', padding: option.attribute.padding + 'px' }"
          >
            <div
              style="border: 1px solid; width: 100%; height: 100%"
              :style="{
                lineHeight: height - option.attribute.padding * 2 + 'px',
                backgroundColor: option.attribute.bgColor,
                borderColor: option.attribute.borderColor,
                fontSize: option.attribute.fontSize + 'px',
                color: option.attribute.color
              }"
            >
              {{ cptData.value.substr(item - 1, 1) }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <bigDataInteraction
      ref="bigDataInteractionRef"
      v-model:bigDataDialog="dialogVisible"
      @handleCloseBigDataInteraction="handleCloseBigDataInteraction"
    ></bigDataInteraction>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import bigDataInteraction from '@/components/bigDataInteraction/index.vue';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
// --- props ---
const props = defineProps<{
  option: Record<string, any>;
  height: number;
}>();

// --- 定义变量 ---
const cptData = ref({ value: '12345' });
const uuid = ref(null);
const bigDataDialog = ref(false);
const bigDataInteractionRef = ref(null);

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId: any) => {
  if (props.option.cptDataForm.dataSource == 2) {
    // 表达式必填
    if (!props.option.cptDataForm.apiUrl) {
      ElMessage.warning('表达式不能为空');
      return;
    }
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      taskId: taskId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        cptData.value = {
          value: res.data + '' || '0'
        };
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
    });
  }
};
/**
 * 展示交互
 */
const showInteraction = () => {
  if (props.option.interaction) {
    //代表配置了交互
    bigDataDialog.value = true;
    bigDataInteractionRef.value.init(props.option.interaction);
  }
};
const handleCloseBigDataInteraction = () => {
  bigDataDialog.value = false;
};
// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});

defineOptions({
  name: 'cpt-rect-num'
});
</script>

<style scoped></style>
