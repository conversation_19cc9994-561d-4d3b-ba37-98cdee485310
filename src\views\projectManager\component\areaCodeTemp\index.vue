<!-- 反显省市区并可选择省市区 用于宗地管理 -->
<template>
  <div class="areaCodeTemp-main">
    <el-cascader
      :props="cascaderProps"
      :size="size"
      v-model="selectCityCode"
      clearable
      @change="handleChangeCity"
      ref="cascaderHandle"
      style="width: 100%"
    ></el-cascader>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, onMounted } from 'vue';
import { getAreaCode } from '@/api/project';

interface CascaderProps {
  lazy: boolean;
  lazyLoad: (node: any, resolve: any) => void;
  value: string;
  label: string;
  expandTrigger: 'click' | 'hover';
  children: string;
  checkStrictly: boolean;
}

interface AreaNode {
  level: number;
  data: {
    areaCode: number;
  };
}

const props = defineProps({
  selectAreaCode: {
    type: String,
    default: ''
  },
  size: {
    type: String as () => '' | 'small' | 'default' | 'large',
    default: 'medium'
  }
});

const emit = defineEmits(['changeCityCode']);

/**
 * 加载节点
 * @param node 节点
 * @param resolve 解析
 */
const loadNode = async (node: AreaNode, resolve: (data: any[]) => void) => {
  if (node.level === 0) {
    getAreaCode('86').then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        // 假设 $message 是全局注入的
        (window as any).$message.error(res.msg);
      }
    });
  } else {
    const areaCode = node.data.areaCode;
    getAreaCode(String(areaCode)).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        // 假设 $message 是全局注入的
        (window as any).$message.error(res.msg);
      }
    });
  }
};

/**
 * 级联选择器属性
 */
const cascaderProps = reactive<CascaderProps>({
  lazy: true,
  lazyLoad: loadNode,
  value: 'areaCode',
  label: 'areaName',
  expandTrigger: 'click',
  children: 'children',
  checkStrictly: true
});

const selectCityCode = ref<number[]>([Number(sessionStorage.getItem('areaCode'))]);
const cascaderHandle = ref();

/**
 * 选择城市的改变
 * @param val 值
 */
const handleChangeCity = (val: any) => {
  cascaderHandle.value.dropDownVisible = false; // 监听值发生变化就关闭它
  emit('changeCityCode', val[val.length - 1]);
};
</script>

<style lang="scss" scoped></style>
