import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import JSONbig from 'json-bigint'; // 解决超过 16 位数字精度丢失问题
import { NoticeData, NoticeQuery, JobData, JobQuery } from '@/api/notice/types';

/**
 * 获取当前用户消息
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getMine(params: NoticeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/smMessage/mine/list',
    method: 'get',
    params: params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 获取当前用户消息数量
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getMineNum(params: NoticeQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/smMessage/mine/count',
    method: 'get',
    params: params
  });
}

/**
 * 已读单条数据
 * @param id 消息ID
 * @returns {AxiosPromise}
 */
export function readNotice(id: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/smMessage/read/${id}`,
    method: 'post'
  });
}

/**
 * 全部已读
 * @returns {AxiosPromise}
 */
export function readNoticeAll(): AxiosPromise<any> {
  return request({
    url: `/qjt/smMessage/readAll`,
    method: 'post'
  });
}

/**
 * 删除所有
 * @returns {AxiosPromise}
 */
export function deleteAll(): AxiosPromise<any> {
  return request({
    url: `/qjt/smMessage/deleteAll`,
    method: 'post'
  });
}

/**
 * 读消息(多条)
 * @param params 消息ID数组
 * @returns {AxiosPromise}
 */
export function readMulti(params: NoticeData): AxiosPromise<any> {
  return request({
    url: `/qjt/smMessage/readMulti`,
    method: 'post',
    data: params
  });
}

/**
 * 删除多条
 * @param params 消息ID数组
 * @returns {AxiosPromise}
 */
export function deleteNotice(params: NoticeData): AxiosPromise<any> {
  return request({
    url: `/qjt/smMessage/delete`,
    method: 'post',
    data: params
  });
}

/**
 * 新增定时任务
 * @param data 任务数据
 * @returns {AxiosPromise}
 */
export function addJob(data: JobData): AxiosPromise<any> {
  return request({
    url: `/qjt/job/add1`,
    method: 'post',
    data: data
  });
}

/**
 * 查询用户创建的定时任务的列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getJobList(params: JobQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/job/list`,
    method: 'get',
    params: params
  });
}

/**
 * 删除定时任务
 * @param data 任务ID
 * @returns {AxiosPromise}
 */
export function deleteJob(data: JobData): AxiosPromise<any> {
  return request({
    url: `/qjt/job/delete`,
    method: 'post',
    data: data
  });
}

/**
 * 定时任务状态修改
 * @param data 任务数据
 * @returns {AxiosPromise}
 */
export function editjob(data: JobData): AxiosPromise<any> {
  return request({
    url: `/qjt/job/changeStatus`,
    method: 'post',
    data: data
  });
}

/**
 * 修改定时任务
 * @param data 任务数据
 * @returns {AxiosPromise}
 */
export function modifyJob(data: JobData): AxiosPromise<any> {
  return request({
    url: `/qjt//job/modify`,
    method: 'post',
    data: data
  });
}

/**
 * 获取定时任务的详细信息
 * @param jobId 任务ID
 * @returns {AxiosPromise}
 */
export function getJobDetial(jobId: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/job/detail/${jobId}`,
    method: 'get'
  });
}
