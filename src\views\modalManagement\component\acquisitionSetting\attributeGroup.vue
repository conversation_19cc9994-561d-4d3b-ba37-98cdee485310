<template>
  <!-- 属性组页面 -->
  <div class="attribute-group-main">
    <div class="item-title">
      <div class="handle-title">
        <span class="text">属性设置</span>
      </div>
    </div>
    <el-row style="margin: 24px 16px 4px">
      <el-col>
        <el-button type="primary" plain :icon="Plus" size="small" @click="handleAddGroup('新增属性')">新增属性</el-button>
      </el-col>
    </el-row>
    <div class="attribute-main">
      <div class="attribite-list">
        <el-row
          class="attribite-item"
          v-show="item.groupScope === 2 && item.ruleAttribution === null"
          v-for="(item, index) in checkedTreeMsg.fieldGroupModelList"
          :key="index"
        >
          <el-col :span="7" style="display: flex; align-items: center">
            <div
              v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) === 'blob'"
              style="display: flex; align-items: center"
            >
              <el-image style="width: 20px; height: 20px; margin-right: 8px" :src="`${baseUrl}${item.iconUrl}?token=${token}`" :fit="'cover'" />
            </div>
            <div v-else>
              <svg-icon class-name="svg-item" :icon-class="item.iconUrl + ''" style="margin-top: 10px" />
            </div>
            <div class="item-type-name">{{ item.typeName }}</div>
          </el-col>
          <el-col :span="8">
            <div class="item-remark">{{ item.remark }}</div>
          </el-col>
          <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end">
            <el-tooltip class="item" effect="dark" content="字段" placement="top">
              <div class="text-btn" @click="handleOpenInfoField(item)">
                <svg-icon class-name="svg-item" icon-class="more_field" />
              </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="修改" placement="top">
              <div class="text-btn" @click="handleAddGroup('修改属性', item)"><svg-icon class-name="svg-item" icon-class="more_edit" /></div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="删除" placement="top">
              <div v-if="[0, 8, 9].includes(modal_state)" class="text-btn" @click="handleDeleteGroup(item)">
                <svg-icon class-name="svg-item" icon-class="more_delete" />
              </div>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 新增属性的弹框 -->
    <add-group
      :addGroupVisible="groupVisible"
      :addGroupTitle="grouptitle"
      :addGroupItem="addGroupForm"
      :groupFilterList="groupFilterList"
      @closeAddGroup="handleCloseAddGroup"
      @submitGroup="handleSubmitGroup"
    ></add-group>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import authImg from '@/components/authImg/index.vue';
import { saveFieldGroup, getOwnerListByModuleId } from '@/api/modal/index';
import addGroup from '../addGroup/index.vue';
import svgIcon from '../../svgIcon/index.vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();
import { getToken } from '@/utils/auth';
const token = getToken();

// 定义 props 类型
interface Props {
  checkedLevel: number;
  checkedNodeId: number;
  checkedTreeMsg: {
    fieldGroupModelList: Array<{
      groupScope: number;
      ruleAttribution: any;
      iconUrl: string;
      typeName: string;
      remark: string;
      id?: number;
      attribution?: {
        formData: any;
      };
      fieldModelList: Array<{
        fieldName: string;
        id: number;
      }>;
    }>;
  };
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'saveRule', type?: string): void;
  (e: 'openFieldForm', id?: number): void;
  (e: 'showField', formData: any): void;
}>();

const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const grouptitle = ref<string>('新增属性');
const groupVisible = ref<boolean>(false);
const infoFieldVisible = ref<boolean>(false);
const groupInfo = ref<Record<string, any>>({});
const addGroupForm = ref<Record<string, any>>({});
const groupFilterList = ref<Array<any>>([]);
const ownerTypeList = ref<Array<any>>([]);

const moduleId = computed(() => modalStore.moduleId);
const modal_state = Number(route.query.status);

// 监听 checkedTreeMsg 变化
watch(
  () => props.checkedTreeMsg,
  (val) => {},
  {
    immediate: true,
    deep: true
  }
);

// 打开新增属性的弹框
const handleAddGroup = (str: string, item?: any) => {
  grouptitle.value = str;
  if (str === '修改属性') {
    addGroupForm.value = item;
  }
  modalStore.setElementType('');
  modalStore.setIsGroupForm({});
  emit('saveRule', 'group');
};

// 父组件保存了树再调用打开弹窗
const groupCenterFun = () => {
  groupFilterList.value = JSON.parse(JSON.stringify(ownerTypeList.value));
  const list = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  if (list && list.length > 0) {
    list.forEach((i: any) => {
      groupFilterList.value.push(i);
    });
  }
  groupVisible.value = true;
};

const handleCloseAddGroup = () => {
  groupVisible.value = false;
  groupFilterList.value = [];
};

// 新建属性的提交按钮
const handleSubmitGroup = (item: any) => {
  item.groupScope = 2;
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    item.companyId = companyId;
  }
  if (!item.id) {
    // 新增
    saveFieldGroup(item).then((res) => {
      if (res.code === 200) {
        // 保存成功之后将数据存到当前树规则下的属性组的字段中
        item.id = res.data.id;
        props.checkedTreeMsg.fieldGroupModelList.push(item);
        // 将组id 存到vuex 中 供后续使用
        modalStore.setGroupId(res.data.id);
        // 修改属性的时候不用打开字段名字
        emit('openFieldForm', res.data.id);
        // 将数据存到session中
        // sessionStorage.setItem('groupItem',JSON.stringify(res.data) )
        modalStore.setCurrentGroupItem(res.data);
        groupVisible.value = false;
        // 需要把 数据内容 清空自定义表达式的选择的属性组字段
        modalStore.setIsAllGroup(false);
        // 判断是权属人设置要点及字段 还是采集要素设置字段
        modalStore.setIsHasAcquition(true);
        // 设置当前树节点下的某一个节点的内容
        modalStore.setCheckedNodeItem(props.checkedTreeMsg);
        // 修改之后要先调用保存树的接口
        emit('saveRule');
      } else {
        (this as any).$message.error(res.msg);
      }
    });
  } else {
    saveFieldGroup(item).then((res) => {
      if (res.code === 200) {
        // 修改
        // 只有保存成功之后才删除这个属性组然后再加入进去
        const index = props.checkedTreeMsg.fieldGroupModelList.findIndex((f: any) => f.id === item.id);
        props.checkedTreeMsg.fieldGroupModelList.splice(index, 1);
        // 保存成功之后将数据存到当前树规则下的属性组的字段中
        item.id = res.data.id;
        props.checkedTreeMsg.fieldGroupModelList.push(item);
        // 将组id 存到vuex 中 供后续使用
        modalStore.setGroupId(res.data.id);
        // 将数据存到session中
        // sessionStorage.setItem('groupItem',JSON.stringify(res.data) )
        modalStore.setCurrentGroupItem(res.data);
        // 需要把 数据内容 清空自定义表达式的选择的属性组字段
        modalStore.setIsAllGroup(false);
        // 判断是权属人设置要点及字段 还是采集要素设置字段
        modalStore.setIsHasAcquition(true);
        // 设置当前树节点下的某一个节点的内容
        modalStore.setCheckedNodeItem(props.checkedTreeMsg);
        groupVisible.value = false;
      } else {
        (this as any).$message.error(res.msg);
      }
    });
  }
};

// 关闭新增属性对话框
const handleClose = () => {
  groupVisible.value = false;
  addGroupForm.value = {};
};

// 打开或者关闭宗地信息字段的内容
const handleOpenInfoField = (item: any) => {
  // 在这里点击清空点和线要素的内容
  modalStore.setElementType('');
  // 设置groupId
  modalStore.setGroupId(item.id);
  // 需要把 数据内容 清空自定义表达式的选择的属性组字段
  modalStore.setIsAllGroup(false);
  // 判断是权属人设置要点及字段 还是采集要素设置字段
  modalStore.setIsHasAcquition(true);
  // sessionStorage.setItem('groupItem',JSON.stringify(item) )
  modalStore.setCurrentGroupItem(item);
  // 设置当前树节点下的某一个节点的内容
  modalStore.setCheckedNodeItem(props.checkedTreeMsg);
  // 有时候修改字段之后点击保存之后会打开子要素的弹框的bug
  // 在这里把值 isShowGroup== false,不让他在打开
  const elementObj = {
    isShowGroup: false
    // title:this.settingTitle
  };
  modalStore.setIsGroupForm(elementObj);
  if (item.attribution !== null) {
    const formData = JSON.parse(JSON.stringify(item.attribution.formData));
    item.fieldModelList.find((i: any) => {
      formData.fields.filter((e: any) => {
        // 回显字段时  对联系人进行特殊处理
        if (e.tagIcon === 'xtlxr' && e.tag === 'el-xtlxr') {
          e.childrenList.forEach((child: any) => {
            if (i.fieldName === child.vModel) {
              return (child.id = i.id);
            }
          });
        } else {
          if (i.fieldName === e.vModel) {
            return (e.id = i.id);
          }
        }
      });
    });
    emit('showField', formData);
  } else {
    emit('openFieldForm');
  }
};

// 行布局 属性的反显
const handleRowShowField = (e: any, item: any) => {
  e.children.forEach((it: any) => {
    if (it.layout === 'rowFormItem') {
      handleRowShowField(it, item);
    } else {
      item.fieldModelList.find((i: any) => {
        if (i.fieldName === it.vModel) {
          return (it.id = i.id);
        }
      });
    }
  });
  return e;
};

// 删除属性组
const handleDeleteGroup = (item: any) => {
  ElMessageBox.confirm(`你确定删除【${item.typeName}】组吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      item.delFlag = 1;
      saveFieldGroup(item).then((res) => {
        if (res.code === 200) {
          const index = props.checkedTreeMsg.fieldGroupModelList.findIndex((f: any) => f.id === item.id);
          props.checkedTreeMsg.fieldGroupModelList.splice(index, 1);
          ElMessage.success('删除成功!');
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      (this as any).$message({
        type: 'info',
        message: '已取消'
      });
    });
};

// 根据ModuleId 获取权属类型列表
const getOwnerList = async () => {
  const params = {
    moduleId: moduleId.value,
    groupScope: 1
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  ownerTypeList.value = modalStore.speGroups;
};

onMounted(() => {
  getOwnerList();
});

defineExpose({
  groupCenterFun
});
</script>

<style lang="scss" scoped>
// 样式部分保持不变
.attribute-group-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  min-height: 80px;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;
    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }
  &:hover {
    border: 1px solid var(--current-color);
    // color: #0081ff;
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .attribute-main {
    // margin:24px 0 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    .attribite-list {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      // align-content: center;
      // align-items: center;
      flex: 1;
      margin: 0px 16px 12px;
      .attribite-item {
        margin: 12px 0;
        padding: 0px 12px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        background-color: rgba(246, 247, 248, 1);
        // text-align: center;
        display: flex;
        align-items: center;
        .svg-item {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          color: #333;
        }
        .item-type-name {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
        }
        .item-remark {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .text-btn {
          margin: 0 8px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .svg-item {
            width: 16px;
            height: 16px;
            margin: auto;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
          &:hover .svg-item {
            width: 16px;
            height: 16px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
        }
      }
    }
  }
}

.el-dialog {
  .item-content {
    flex: 1;
    .choose-img-div {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      cursor: pointer;
      .choose-img {
        width: 20px;
        height: 20px;
        margin-left: 16px;
      }
      .choose-btn {
        width: 52px;
        height: 100%;
        border-left: 1px solid #dcdfe6;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 20px;
      }
    }
  }
}
.dynamic-form-class {
  width: 100%;
}
</style>
