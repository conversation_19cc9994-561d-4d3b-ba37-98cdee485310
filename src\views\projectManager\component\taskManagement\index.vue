<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="任务状态">
            <el-col :span="6">
              <el-select v-model="queryParams.status" clearable placeholder="请输入任务状态" class="form-select">
                <el-option
                  v-for="item in taskStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @keyup.enter="handleQuery"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>
          <!-- <el-form-item label="任务类型">
            <el-col :span="6">
              <el-select v-model="queryParams.scope" clearable placeholder="请选择任务类型" class="form-select">
                <el-option v-for="item in scopeList" :key="item.value" :label="item.label" :value="item.value" @keyup.enter="handleQuery"></el-option>
              </el-select>
            </el-col>
          </el-form-item> -->
          <el-form-item label="任务权限">
            <el-col :span="6">
              <el-select v-model="queryParams.type" clearable placeholder="请选择任务权限" class="form-select">
                <el-option
                  v-for="item in taskOptTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @keyup.enter="handleQuery"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="任务名称">
            <el-col :span="6">
              <el-input v-model="queryParams.name" placeholder="请输入任务名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-col>
          </el-form-item>
          <el-form-item label="业务模块" prop="moduleId">
            <el-col :span="6">
              <el-select v-model="queryParams.moduleId" placeholder="请选择业务模块" class="form-select" @keyup.enter="handleQuery">
                <el-option v-for="item in appTypeOptions" :key="item.id" :label="getAddTaskLable(item)" :value="item.id"></el-option>
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="任务人员">
            <el-col :span="6">
              <el-select
                v-model="queryParams.taskReceivers"
                filterable
                clearable
                remote
                :remote-method="remoteMethod"
                v-loadmore="getMoreUser"
                placeholder="请选择任务人员"
                class="form-select"
              >
                <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.custName" :value="item.userId"></el-option>
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-col :span="6">
              <el-date-picker
                v-model="queryParams.dateRange"
                style="width: 240px"
                type="datetimerange"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="x"
                format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon>搜索</el-button
            >
            <el-button @click="resetQuery"
              ><el-icon><RefreshRight /></el-icon>重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain @click="handleAddTask" v-hasPermi="['system:user:add']"
              ><el-icon><Plus /></el-icon>新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain :disabled="batchEndDisabled" @click="handleBatchEnd"
              ><el-icon><TurnOff /></el-icon>批量结束</el-button
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" v-model:columns="columns" @queryTable="getSearchTaskList"></right-toolbar>
        </el-row>
        <div class="table-contianer">
          <el-table
            v-loading="loading"
            :data="taskList"
            :height="tableHeight"
            :cell-class-name="tableCellClassName"
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" fixed></el-table-column>
            <el-table-column label="序号" fixed width="60" align="center">
              <template #default="scope">
                {{ scope.row.index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="任务名称" align="center" key="name" prop="name" min-width="240" fixed v-if="columns[1].visible">
              <template #default="scope">
                <div
                  class="task-name"
                  :class="{ 'row-currout': scope.row.checked }"
                  :style="{
                    height: currentRowIndex === scope.row.index ? taskHeight : '23px'
                  }"
                  :title="scope.row.name"
                >
                  {{ scope.row.name }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="任务状态" width="100" align="center" key="status" prop="status" v-if="columns[7].visible">
              <template #default="scope">
                <el-tag type="success" v-if="scope.row.status == 1">进行中</el-tag>
                <el-tag type="info" v-else>已结束</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="业务模块" align="center" key="moduleId" prop="moduleId" v-if="columns[8].visible" min-width="340">
              <template #default="scope">
                <span>{{ getModule(scope.row.moduleId) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="任务类型" align="center" width="100" key="scope" v-if="columns[11].visible">
              <template #default="scope">
                <span v-if="scope.row.flowType == 0">普通任务</span>
                <span v-else-if="scope.row.flowType == 1">流程任务</span>
              </template>
            </el-table-column>
            <el-table-column label="任务权限" align="center" width="100" key="type" v-if="columns[0].visible">
              <template #default="scope">
                <span v-if="scope.row.type == 0">只读</span>
                <span v-if="scope.row.type == 1">新增</span>
                <span v-if="scope.row.type == 2">修改</span>
              </template>
            </el-table-column>
            <el-table-column label="任务描述" align="center" key="descs" prop="descs" min-width="220" v-if="columns[3].visible">
              <template #default="scope">
                <div
                  class="task-name"
                  :class="{ 'row-currout': scope.row.checked }"
                  :style="{
                    height: currentRowIndex === scope.row.index ? taskHeight : '23px'
                  }"
                  :title="scope.row.descs"
                >
                  {{ scope.row.descs }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="任务人员" align="center" key="receiverNames" prop="receiverNames" v-if="columns[4].visible" width="280">
              <template #default="scope">
                <div
                  class="task-name"
                  :class="{ 'row-currout': scope.row.checked }"
                  :style="{
                    height: currentRowIndex === scope.row.index ? taskHeight : '23px'
                  }"
                  :title="scope.row.receiverNames"
                >
                  {{ scope.row.receiverNames }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="项目负责人" align="center" prop="chargeUserName" width="100" v-if="columns[9].visible"></el-table-column>
            <el-table-column label="任务备注" align="center" key="remark" prop="remark" v-if="columns[5].visible" min-width="220">
              <template #default="scope">
                <div
                  class="task-name"
                  :class="{ 'row-currout': scope.row.checked }"
                  :style="{
                    height: currentRowIndex === scope.row.index ? taskHeight : '23px'
                  }"
                  :title="scope.row.remark"
                >
                  {{ scope.row.remark }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="创建者" align="center" width="100" prop="createUserName" v-if="columns[6].visible"></el-table-column>
            <el-table-column label="创建时间" align="center" width="120" v-if="columns[8].visible">
              <template #default="scope">
                {{ formatDateType(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" fixed="right" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-link
                  type="primary"
                  style="margin-right: 5px"
                  @click="handleUpdate(scope.row)"
                  v-if="(loginUserId == scope.row.createUserId || isAdmin) && scope.row.status != -1"
                  ><el-icon><Edit /></el-icon> 修改</el-link
                >
                <el-link
                  type="danger"
                  @click="handleDelete(1, scope.row)"
                  style="margin-right: 5px"
                  v-if="(loginUserId == scope.row.createUserId || isAdmin) && scope.row.status != -1"
                  ><el-icon><Lock /></el-icon> 结束</el-link
                >
                <el-link
                  type="success"
                  style="margin-right: 5px"
                  @click="handleDelete(2, scope.row)"
                  v-if="(loginUserId == scope.row.createUserId || isAdmin) && scope.row.status == -1"
                  ><el-icon><Unlock /></el-icon> 开始</el-link
                >
                <el-link
                  type="danger"
                  style="margin-right: 5px"
                  @click="handleDelete(3, scope.row)"
                  v-if="(loginUserId == scope.row.createUserId || isAdmin) && scope.row.status == -1"
                  ><el-icon><Delete /></el-icon> 删除</el-link
                >
                <el-checkbox
                  style="margin-right: 5px"
                  v-hasPermi="['task:temp:edit']"
                  v-model="scope.row.defaultFlag"
                  v-if="scope.row.flowType == 1"
                  @change="handleDelete(4, scope.row, $event)"
                  ><span
                    :style="{
                      color: scope.row.defaultFlag ? '#13ce66' : '#8291a9'
                    }"
                    >模板</span
                  ></el-checkbox
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getSearchTaskList(order)"
          />
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      class="showAll_dialog"
      :title="title"
      v-model="addTaskDialogVisible"
      width="600px"
      @close="handleCloseTaskForm"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      height=""
    >
      <el-form ref="addTaskFormRef" :model="addTaskForm" :rules="addTaskFormRules" v-if="addTaskDialogVisible">
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务模块" prop="moduleId">
              <el-select
                v-model="addTaskForm.moduleId"
                :disabled="title == '修改任务'"
                filterable
                clearable
                placeholder="请选择业务模块"
                style="width: 100%"
                @change="changeModal"
              >
                <el-option v-for="item in appTypeOptions" :key="item.id" :label="getAddTaskLable(item)" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="任务类型" prop="scope">
              <el-select v-model="addTaskForm.scope" placeholder="请选择" style="width: 100%" @change="handleChangeScope">
                <el-option v-for="item in scopeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="addTaskForm.name" placeholder="请输入任务名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="任务字段" prop="taskGroupModels">
              <el-input
                placeholder="请选择任务字段"
                readonly
                @focus="handleOpenField"
                v-if="!addTaskForm.taskGroupModels || (addTaskForm.taskGroupModels && addTaskForm.taskGroupModels.length == 0)"
              />
              <el-input value="已选择任务字段" v-else readonly @focus="handleOpenField"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="任务权限" prop="type">
              <el-select
                v-model="addTaskForm.type"
                @focus="handleFocusTaskOptType"
                :disabled="title == '修改任务'"
                placeholder="请选择任务权限"
                style="width: 100%"
              >
                <template v-if="addTaskForm.scope == 0">
                  <el-option v-for="item in taskOptTypeComputedOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
                <template v-else>
                  <el-option v-for="item in taskOptTypeComputedOptionsOut" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="addTaskForm.scope === 5 || addTaskForm.type === 2">
          <el-col :span="24">
            <el-form-item :label="taskName" prop="parcelNames">
              <el-input v-model="addTaskForm.parcelNames" multiple :placeholder="taskPlacehlder" @focus="handleOpenZDManagementDialog"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="addTaskForm.scope == 0">
          <el-col :span="24">
            <el-form-item label="任务人员" :rules="[{ required: true, message: '请选择任务人员', trigger: 'blur' }]">
              <el-input v-model="addTaskForm.receiverNames" placeholder="请选择任务人员" readonly @focus="handleOpenUser"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="addTaskForm.scope == 0 && addTaskForm.parcelNames && addTaskForm.receiverNames">
          <el-col :span="24">
            <el-form-item label="分配任务数据">
              <el-radio-group v-model="addTaskForm.allocationType">
                <el-radio :value="item.value" v-for="(item, index) in allocationTypeList" :key="index">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-link v-show="addTaskForm.allocationType != 0" type="primary" @click="goAllot" style="margin-left: 20px">去分配</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="addTaskForm.scope == 0">
          <el-col :span="12">
            <el-form-item
              label="预计工作量"
              :rules="[
                {
                  required: true,
                  message: '请输入预计工作量',
                  trigger: 'blur'
                }
              ]"
            >
              <el-input v-model="addTaskForm.workload" placeholder="请输入预计工作量" maxlength="100" @input="handleInput"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item label="任务工期" :rules="[{ required: true, message: '请输入任务工期', trigger: 'blur' }]">
              <el-date-picker
                @input="handleDateRank"
                v-model="addTaskForm.dateRange"
                style="width: 100%"
                value-format="timestamp"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目负责人" prop="chargeUserId">
              <el-select v-model="addTaskForm.chargeUserId" filterable clearable remote placeholder="请选择项目负责人" style="width: 100%">
                <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.custName" :value="item.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="任务描述" prop="descs">
              <el-input v-model="addTaskForm.descs" type="textarea" placeholder="请输入任务描述" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务备注">
              <el-input v-model="addTaskForm.remark" type="textarea" placeholder="请输入任务备注" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitAddTask">确 定</el-button>
          <el-button @click="handleCancleAddTask">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="selectedList"
      :moduleIdPop="String(addTaskForm.moduleId)"
      :isManager="false"
      :taskId="addTaskForm.id || ''"
      :taskName="addTaskForm.name"
    ></searchData>

    <!-- 选择字段 -->
    <select-field
      ref="selectFieldRef"
      :appType="addTaskForm.appType ?? 0"
      :fieldCate="addTaskForm.fieldCate ?? 0"
      :fieldList="addTaskForm.taskFieldModelList"
      :selectFielsVisibleDialog="selectFieldVisible"
      @selectedField="handleSelectedField"
      @closeField="handleCloseField"
    ></select-field>

    <!-- 选择人员 -->
    <chooseUser
      :userDialog="userDialog"
      @chageUserDialog="chageUserDialog"
      @getChooseUser="getChooseUser"
      :users="addTaskForm.receivers"
      :flowList="[]"
      :isShowDisable="false"
    ></chooseUser>

    <!-- 选择任务字段 -->
    <chooseField
      :moduleId="addTaskForm.moduleId || 0"
      :chooseFieldDialog="chooseFieldDialog"
      :taskGroupModels="addTaskForm.taskGroupModels"
      @closeFieldDialog="closeFieldDialog"
      @submitField="submitField"
      :title="title"
    ></chooseField>

    <!-- 任务错误弹窗 -->
    <el-dialog title="错误提示" v-model="errorDialog" :close-on-click-modal="false" width="400px" :before-close="handleCloseErrorDialog">
      <div class="dialog-row" style="color: red">以下数据在其他任务存在(共{{ errorList.length }}条)：</div>
      <div class="dialog-box">
        <div class="dialog-row" v-for="(item, index) in errorList" :key="index">
          <div class="item">数据名称：{{ item.parcelName }}</div>
          <div class="item">任务名称：{{ item.taskName }}</div>
        </div>
      </div>
    </el-dialog>

    <el-dialog :title="ewmTitle" v-model="erwDialog" width="600px" :close-on-click-modal="false" :before-close="handleCloseEWM">
      <div class="handle-row">
        <div class="row">大小：<el-input type="number" class="row-input" v-model="QRImgUrlW"></el-input></div>
        <div class="row">
          <!-- <el-button  type="primary" @click="editEWM">确定</el-button> -->
        </div>
      </div>
      <img :src="QRImgUrl" />
    </el-dialog>

    <!-- 分配任务数据 -->
    <allotData
      @closeAllot="closeAllot"
      :allotDialog="allotDialog"
      :users="addTaskForm.receivers"
      :selectedDataItems="selectedList"
      @submitAllotData="submitAllotData"
    ></allotData>

    <!-- 选择任务类型 -->
    <el-dialog title="任务类型" v-model="chooseTaskTypeDialog" width="30%" :before-close="handleCloseChooseTask">
      <el-radio-group v-model="taskType">
        <el-radio :value="0">普通任务</el-radio>
        <el-radio :value="1">流程任务</el-radio>
      </el-radio-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseChooseTask">取 消</el-button>
          <el-button type="primary" @click="submitTaskType">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onActivated } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listUser } from '@/api/system/user';
import { getSearchTask, addTask, deleteTask } from '@/api/task';
import { getModuleList } from '@/api/modal';
import SelectField from '../SelectField/index.vue';
import chooseUser from './chooseUser.vue';
import chooseField from './chooseField.vue';
import allotData from '@/components/allotData/index.vue';
import searchData from '@/components/taskDataSearch/index.vue';
import { UserVO } from '@/api/system/user/types';
import type { FormInstance } from 'element-plus';

// 注册 v-loadmore 指令
const vLoadmore = {
  mounted(el: HTMLElement, binding: any) {
    const selectWrap = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
    if (selectWrap) {
      selectWrap.addEventListener('scroll', function (this: HTMLElement) {
        const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
        if (condition) {
          binding.value();
        }
      });
    }
  }
};

// 类型定义
interface TaskStatusOption {
  label: string;
  value: number;
}

interface TaskOptTypeOption {
  label: string;
  value: number;
}

interface TaskReceiver {
  userId: number;
  custName: string;
  userName: string;
  nickName: string;
  deptId: number;
  deptName: string;
  checked: boolean;
  isContains: any[];
}

interface AppTypeOption {
  id: number;
  moduleName: string;
  status: number;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  name: string;
  type: number | undefined;
  moduleId: number | undefined;
  status: number | undefined;
  dateRange: string[];
  taskReceivers: string;
  scope: number | undefined;
}

interface TaskForm {
  moduleId: number | undefined;
  name: string;
  type: number | undefined;
  remark: string;
  descs: string;
  startTime: string;
  endTime: string;
  workload: string;
  chargeUserName: string;
  chargeUserId: string | undefined;
  receiverNames: string;
  receivers: TaskReceiver[];
  dateRange: string[];
  parcelIds: string[];
  id: string | undefined;
  timeTamp: string;
  taskGroupModels: any[];
  scope: number | undefined;
  allocationType: number;
  parcelNames: string;
  taskParcelModels: any[];
  appType: number | undefined;
  fieldCate: number | undefined;
  taskFieldModelList: any[];
  taskFieldModelNames: string;
  status?: number;
  delFlag?: string;
  defaultFlag?: boolean;
  users: UserVO[];
}

interface TaskParams {
  pageSize: number;
  pageNum: number;
  type: number;
  name: string;
  moduleId: number;
  status: number;
  createTimeStart: string;
  createTimeEnd: string;
  userId: string;
  scope: number;
  pageType?: number;
  parcelIds?: string[];
  id?: string;
  remark?: string;
  descs?: string;
  startTime?: string;
  endTime?: string;
  workload?: string;
  chargeUserName?: string;
  chargeUserId?: string;
  receiverNames?: string;
  receivers?: TaskReceiver[];
  taskGroupModels?: any[];
  taskParcelModels?: any[];
  allocationType?: number;
  timeTamp?: string;
}

interface ParcelModel {
  geomArcgis?: any;
  custName?: string;
  lockNum?: number;
  parcelId: any;
  parcelName: string;
  taskId?: any;
  userId?: any;
  id?: any;
}

interface ErrorItem {
  parcelName: string;
  taskName: string;
}

// 状态管理
const userStore = useUserStore();
const router = useRouter();

// 响应式数据
const fullscreenLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const addTaskDialogVisible = ref(false);
const selectFieldVisible = ref(false);
const userDialog = ref(false);
const chooseFieldDialog = ref(false);
const errorDialog = ref(false);
const erwDialog = ref(false);
const searchDialog = ref(false);
const allotDialog = ref(false);
const chooseTaskTypeDialog = ref(false);
const selectedTasks = ref<any[]>([]);
const batchEndDisabled = ref(true);

const taskList = ref([]);
const total = ref(0);
const taskReceiversOptions = ref<TaskReceiver[]>([]);
const appTypeOptions = ref<AppTypeOption[]>([]);
const appTypeAllOptions = ref<AppTypeOption[]>([]);
const errorList = ref<ErrorItem[]>([]);
const selectedList = ref<ParcelModel[]>([]);
const taskType = ref(0);

const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  name: '',
  type: undefined,
  moduleId: undefined,
  status: undefined,
  dateRange: [],
  taskReceivers: '',
  scope: undefined
});

const addTaskForm = reactive<TaskForm>({
  moduleId: undefined,
  name: '',
  type: undefined,
  receivers: [],
  workload: '',
  dateRange: [],
  chargeUserId: undefined,
  descs: '',
  remark: '',
  taskGroupModels: [],
  scope: undefined,
  allocationType: 0,
  startTime: '',
  endTime: '',
  chargeUserName: '',
  receiverNames: '',
  timeTamp: '',
  parcelIds: [],
  id: undefined,
  parcelNames: '',
  taskParcelModels: [],
  appType: undefined,
  fieldCate: undefined,
  taskFieldModelList: [],
  taskFieldModelNames: '',
  users: []
});

// 表单引用
const addTaskFormRef = ref<FormInstance>();

// 常量定义
const taskStatusOptions: TaskStatusOption[] = [
  { label: '进行中', value: 1 },
  { label: '已结束', value: -1 }
];

const taskOptTypeOptions: TaskOptTypeOption[] = [
  { label: '新增', value: 1 },
  { label: '修改', value: 2 }
  // { label: '只读', value: 0 }
];

const taskOptTypeComputedOptions: TaskOptTypeOption[] = [
  { label: '新增', value: 1 },
  { label: '修改', value: 2 }
];

const taskOptTypeComputedOptionsOut: TaskOptTypeOption[] = [{ label: '只读', value: 0 }];

const scopeList = [
  { label: '内部使用', value: 0 },
  { label: '外部使用', value: 5 }
];

/**
 * 分配类型列表
 */
const allocationTypeList = [
  { label: '共享任务', value: 0 },
  { label: '自定义分配', value: 1 }
];
/**
 * 任务列表列
 */
const columns = ref([
  { key: '0', label: '任务权限', visible: true },
  { key: '1', label: '任务名称', visible: true },
  { key: '2', label: '任务位置', visible: true },
  { key: '3', label: '任务描述', visible: true },
  { key: '4', label: '任务人员', visible: true },
  { key: '5', label: '任务备注', visible: true },
  { key: '6', label: '创建时间', visible: true },
  { key: '7', label: '任务状态', visible: true },
  { key: '8', label: '业务模块', visible: true },
  { key: '9', label: '创建者', visible: true },
  { key: '10', label: '项目创建人', visible: true },
  { key: '11', label: '任务类型', visible: true }
]);

// 计算属性
const loginUserId = computed(() => userStore.user.userId);
const isAdmin = computed(() => userStore.isAdmin);
const tableHeight = computed(() => window.innerHeight - 440 + 'px');
const taskHeight = ref('23px');
const currentRowIndex = ref(1000);
const QRImgUrl = ref('');
const QRImgUrlW = ref(128);
const ewmTitle = ref('');
const taskName = ref('任务数据');
const taskPlacehlder = ref('请选择任务数据');

const userPages = ref(1);
const searchUser = reactive({
  pageNum: 1,
  pageSize: 10
});

const order = ref(3);

/**
 * 验证宗地名称
 * @param rule 规则
 * @param value 值
 * @param callback 回调
 */
const validateParcelNames = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请选择宗地'));
  } else {
    callback();
  }
};

/**
 * 验证字段名称
 * @param rule 规则
 * @param value 值
 * @param callback 回调
 */
const validateFieldNames = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请选择字段'));
  } else {
    callback();
  }
};

/**
 * 验证业务模块
 * @param rule 规则
 * @param value 值
 * @param callback 回调
 */
const validateModule = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '' || value === '0') {
    callback(new Error('请选择业务模块'));
  } else {
    callback();
  }
};

/**
 * 添加任务表单规则
 */
const addTaskFormRules = {
  moduleId: [{ required: true, validator: validateModule, trigger: 'change' }],
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  scope: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  type: [{ required: true, message: '请选择任务权限', trigger: 'change' }],
  receivers: [{ required: true, message: '请选择任务人员', trigger: 'blur' }],
  remark: [{ required: true, message: '请输入任务备注', trigger: 'blur' }],
  descs: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
  parcelNames: [{ required: true, validator: validateParcelNames, trigger: 'blur' }],
  workload: [{ required: true, message: '请输入工作量', trigger: 'blur' }],
  dateRange: [{ required: true, message: '请选择任务工期', trigger: 'blur' }],
  chargeUserId: [{ required: true, message: '请选择项目负责人', trigger: 'blur' }],
  taskGroupModels: [{ required: true, message: '请选择任务字段', trigger: 'change' }]
};

// 生命周期钩子
onMounted(() => {
  getModelList();
  getUserList();
  getModelAllList();
});
// 修改onActivated钩子，避免重复调用查询
onActivated(() => {
  // 重置查询条件但不立即查询
  // Object.assign(queryParams, {
  //   pageNum: 1,
  //   pageSize: 10,
  //   name: '',
  //   type: undefined,
  //   moduleId: undefined,
  //   status: undefined,
  //   dateRange: [],
  //   taskReceivers: '',
  //   scope: undefined
  // });

  // 只执行一次查询
  getSearchTaskList(3);

  // 重置各弹窗状态
  chooseTaskTypeDialog.value = false;
  addTaskDialogVisible.value = false;
  errorDialog.value = false;
  searchDialog.value = false;
  allotDialog.value = false;
});

/**
 * 获取任务标签
 * @param item 业务模块选项
 * @returns 任务标签
 */
const getAddTaskLable = (item: AppTypeOption) => {
  return item.status == 8 ? `${item.moduleName}(测试)` : item.moduleName;
};

/**
 * 获取业务模块
 * @param val 业务模块ID
 * @returns 业务模块名称
 */
const getModule = (val: string) => {
  let moduleName = '';
  if (val) {
    for (let index = 0; index < appTypeAllOptions.value.length; index++) {
      if (appTypeAllOptions.value[index].id === Number(val) && appTypeAllOptions.value[index].status === 1) {
        moduleName = appTypeAllOptions.value[index].moduleName;
        break;
      } else if (appTypeAllOptions.value[index].id === Number(val) && appTypeAllOptions.value[index].status === 8) {
        moduleName = `${appTypeAllOptions.value[index].moduleName}(测试)`;
        break;
      } else if (appTypeAllOptions.value[index].id === Number(val) && appTypeAllOptions.value[index].status === -1) {
        moduleName = `${appTypeAllOptions.value[index].moduleName}(已停用)`;
        break;
      }
    }
  }
  return moduleName;
};

/**
 * 获取模型列表
 */
const getModelList = async () => {
  try {
    const res = await getModuleList([1, 8], undefined, {});
    if (res.code == 200) {
      appTypeOptions.value = res.data;
      // 移除此处的查询调用
      // getSearchTaskList(3);
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

/**
 * 获取模型列表
 */
const getModelAllList = async () => {
  try {
    const res = await getModuleList([1, 8, 9, -1], undefined, {});
    if (res.code == 200) {
      appTypeAllOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

/**
 * 获取用户列表
 */
const getUserList = async () => {
  try {
    const response = await listUser({ pageSize: 10000000, pageNum: 1 });
    if (response.code === 200) {
      taskReceiversOptions.value = handleUserList(response.rows);
    } else {
      ElMessage.error(response.msg);
    }
  } catch (error) {}
};

/**
 * 获取任务列表
 * @param order 排序
 */
const getSearchTaskList = async (order: number) => {
  const type = 1;
  let timeStart = '';
  let timeEnd = '';
  if (queryParams.dateRange && queryParams.dateRange.length !== 0) {
    timeStart = queryParams.dateRange[0] + '000';
    timeEnd = queryParams.dateRange[1] + '000';
  }

  const params: any = {
    pageSize: queryParams.pageSize,
    pageNum: queryParams.pageNum,
    name: queryParams.name,
    createTimeStart: timeStart,
    createTimeEnd: timeEnd,
    userId: queryParams.taskReceivers
  };

  if (queryParams.type !== undefined) {
    params.type = queryParams.type;
  }

  if (queryParams.moduleId !== undefined) {
    params.moduleId = queryParams.moduleId;
  }

  if (queryParams.status !== undefined) {
    params.status = queryParams.status;
  }

  if (queryParams.scope !== undefined) {
    params.scope = queryParams.scope;
  }

  if (!order || order == 3) {
    params.pageType = 3;
  } else if (order == 2) {
    params.pageType = 2;
  } else if (order == 1) {
    params.pageType = 1;
  }

  loading.value = true;
  try {
    const res = await getSearchTask(params, 1);
    if (res.code == 200) {
      // 处理项目负责人名称
      if (res.data.list && res.data.list.length > 0) {
        res.data.list.forEach((item: any) => {
          // 如果chargeUserName是电话号码格式，从用户列表中查找对应的名称
          if (item.chargeUserId) {
            const user = taskReceiversOptions.value.find((u) => u.userId === Number(item.chargeUserId));
            if (user) {
              item.chargeUserName = user.custName || user.nickName || user.userName;
            }
          }
        });
      }
      taskList.value = res.data.list;
      total.value = res.data.total;
      queryParams.pageSize = res.data.pageSize;
      queryParams.pageNum = res.data.pageNum;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

/**
 * 添加任务
 */
const handleAddTask = () => {
  chooseTaskTypeDialog.value = true;
};

/**
 * 提交添加任务
 */
const handleSubmitAddTask = async () => {
  try {
    // 查找并设置项目负责人的名称
    taskReceiversOptions.value.forEach((i) => {
      if (i.userId === Number(addTaskForm.chargeUserId)) {
        addTaskForm.chargeUserName = i.custName;
      }
    });

    // 构建请求参数
    const params: TaskParams = {
      pageSize: 10,
      pageNum: 1,
      type: addTaskForm.type || 0,
      status: 0,
      userId: '',
      scope: addTaskForm.scope || 0,
      createTimeStart: '',
      createTimeEnd: '',
      moduleId: addTaskForm.moduleId || 0,
      name: addTaskForm.name,
      remark: addTaskForm.remark,
      descs: addTaskForm.descs,
      startTime: addTaskForm.dateRange[0],
      endTime: addTaskForm.dateRange[1],
      workload: addTaskForm.workload,
      chargeUserName: addTaskForm.chargeUserName,
      chargeUserId: addTaskForm.chargeUserId,
      receiverNames: addTaskForm.receiverNames,
      receivers: addTaskForm.receivers,
      taskGroupModels: addTaskForm.taskGroupModels,
      taskParcelModels: addTaskForm.taskParcelModels,
      allocationType: addTaskForm.allocationType,
      timeTamp: addTaskForm.timeTamp
    };

    // 如果是修改或只读类型，添加宗地信息
    if (addTaskForm.type === 2 || addTaskForm.type === 0) {
      params.parcelIds = addTaskForm.parcelIds;
    }

    // 如果有ID，说明是更新任务
    if (addTaskForm.id) {
      params.id = addTaskForm.id;
    }

    // 表单验证
    await addTaskFormRef.value?.validate();

    // 如果是外部使用，去除部分验证
    if (addTaskForm.scope === 5) {
      addTaskFormRef.value?.clearValidate(['receivers', 'workload', 'dateRange']);
    }

    // 提交任务
    const res = await addTask(params);

    if (res.code === 200) {
      ElMessage.success('操作成功');
      getSearchTaskList(order.value);
      addTaskDialogVisible.value = false;
    } else if (res.code === 500) {
      if (res.msg) {
        ElMessage.error(res.msg);
      } else {
        errorDialog.value = true;
        errorList.value = res.data.errors;
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

/**
 * 处理任务更新
 * @param row 任务行数据
 */
const handleUpdate = (row: any) => {
  if (row.flowType == 0) {
    router.push(`/taskManager/editTask/${row.id}`);
  } else if (row.flowType == 1) {
    router.push(`/taskManager/editTaskFlow/${row.id}`);
  }
};

/**
 * 处理任务删除/状态变更
 * @param type 操作类型: 1-失效 2-生效 3-删除 4-设置模板
 * @param row 任务行数据
 * @param event 事件数据(用于模板操作)
 */
const handleDelete = async (type: number, row: any, event?: any) => {
  // 根据操作类型设置相关参数
  let str = '';
  let status = 1;
  let defaultFlag = false;

  if (type === 1) {
    status = -1;
    str = `确定要失效【${row.name}】任务吗?`;
  } else if (type === 2) {
    status = 1;
    str = `确定要生效【${row.name}】任务吗?`;
  } else if (type === 3) {
    status = -1;
    str = `确定要删除【${row.name}】任务吗?`;
  } else if (type === 4) {
    const modalName = getModule(row.moduleId);
    // defaultFlag

    if (!event) {
      str = `确认将【${row.name}】从任务模板中移除?`;
      defaultFlag = false;
    } else {
      str = `确认将【${row.name}】设置为任务模板？`;
      defaultFlag = true;
    }
  }

  try {
    // 使用Element Plus的确认框API
    await ElMessageBox.confirm(str, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 构建请求参数
    const params: any = {
      status: status,
      id: row.id,
      timeTamp: row.timeTamp,
      defaultFlag: defaultFlag
    };

    // 如果是删除操作，添加delFlag参数
    if (type === 3) {
      params.delFlag = 1;
    }

    // 调用API接口
    const res = await addTask(params);

    if (res.code === 200) {
      ElMessage.success('操作成功');
      getSearchTaskList(order.value);
    } else {
      if (res.msg) {
        ElMessage.error(res.msg);
      } else {
        errorList.value = res.data.errors;
        errorDialog.value = true;
      }
    }
  } catch (error) {
    // 用户取消操作或发生其他错误时，刷新列表
    getSearchTaskList(order.value);
  }
};

/**
 * 处理查询
 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getSearchTaskList(order.value);
};

/**
 * 重置查询
 */
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: undefined,
    moduleId: undefined,
    status: undefined,
    dateRange: [],
    taskReceivers: '',
    scope: undefined
  });
  handleQuery();
};

/**
 * 格式化日期类型
 * @param date 日期
 * @returns 格式化后的日期
 */
const formatDateType = (date: number) => {
  // 实现日期格式化逻辑
  return new Date(date).toLocaleString();
};

/**
 * 处理范围变更
 */
const handleChangeScope = () => {
  addTaskForm.type = 0;
};

/**
 * 打开字段选择
 */
const handleOpenField = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  } else {
    chooseFieldDialog.value = true;
  }
};

/**
 * 处理任务类型变更
 */
const handleFocusTaskOptType = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  }
};

/**
 * 打开宗地管理对话框
 */
const handleOpenZDManagementDialog = () => {
  if (!addTaskForm.moduleId) {
    addTaskFormRef.value?.validateField('moduleId');
    return false;
  } else {
    searchDialog.value = true;
  }
};

/**
 * 打开用户对话框
 */
const handleOpenUser = () => {
  userDialog.value = true;
};

/**
 * 改变用户对话框
 * @param flg 对话框状态
 */
const chageUserDialog = (flg: boolean) => {
  userDialog.value = flg;
};

/**
 * 获取选择用户
 * @param list 用户列表
 */
const getChooseUser = (list: UserVO[]) => {
  const receivers = handleUserList(list);
  addTaskForm.receivers = receivers;
  addTaskForm.users = list;
  addTaskForm.receiverNames = receivers.map((v) => v.custName).join(',');
  addTaskFormRef.value?.clearValidate('receivers');
  userDialog.value = false;
};

/**
 * 关闭字段对话框
 */
const closeFieldDialog = () => {
  if (!addTaskForm.id) {
    addTaskForm.taskGroupModels = [];
  }
  chooseFieldDialog.value = false;
};

/**
 * 提交字段
 * @param list 字段列表
 */
const submitField = (list: any[]) => {
  addTaskForm.taskGroupModels = list;
  addTaskFormRef.value?.clearValidate('taskGroupModels');
  chooseFieldDialog.value = false;
};

/**
 * 改变模块
 * @param val 模块
 */
const changeModal = (val: string) => {
  addTaskForm.taskGroupModels = [];
};

/**
 * 关闭错误对话框
 */
const handleCloseErrorDialog = () => {
  errorDialog.value = false;
};

/**
 * 关闭二维码对话框
 */
const handleCloseEWM = () => {
  erwDialog.value = false;
};

/**
 * 处理输入
 * @param value 值
 */
const handleInput = (value: string) => {
  addTaskForm.workload = value.replace(/^0+([1-9]\d*)/, '$1').replace(/\D/g, '');
};

/**
 * 关闭搜索对话框
 */
const closeSearchDialog = () => {
  searchDialog.value = false;
};

/**
 * 获取选择数据
 * @param list 数据列表
 */
const getChooseData = (list: any[]) => {
  selectedList.value = [];

  // 检查list是否存在且是数组
  if (!list || !Array.isArray(list)) {
    return;
  }

  list.forEach((v) => {
    const obj = {
      geomArcgis: v.geomArcgis,
      custName: v.custName || '',
      lockNum: v.lockNum || 0,
      parcelId: v.id,
      parcelName: v.parcelName,
      taskId: v.taskId || null,
      userId: v.userId || null,
      id: v.relationId || v.id || null
    };
    if (v.geomWkb || v.isNew) {
      obj.id = v.relationId || null;
    } else {
      obj.id = v.id;
    }
    selectedList.value.push(obj);
  });

  addTaskForm.taskParcelModels = selectedList.value;

  // 只有当list有数据时才执行map操作
  if (list.length > 0) {
    const parcelNames = list.map((item) => item.parcelName);
    const parcelIds = list.map((item) => item.id);
    addTaskForm.parcelNames = parcelNames.join(',');
    addTaskForm.parcelIds = parcelIds;
    if (addTaskForm.parcelNames && addTaskForm.parcelNames !== '') {
      addTaskFormRef.value?.clearValidate('parcelNames');
    }
  } else {
    // 如果list为空，清空相关字段
    addTaskForm.parcelNames = '';
    addTaskForm.parcelIds = [];
  }

  searchDialog.value = false;
};

const goAllot = () => {
  // 确保有数据可以分配
  // if (!selectedList.value || selectedList.value.length === 0) {
  //   ElMessage.warning('没有可分配的数据，请先选择任务数据');
  //   return;
  // }

  // // 确保任务人员已设置
  // if (!addTaskForm.receivers || addTaskForm.receivers.length === 0) {
  //   ElMessage.warning('请先选择任务人员');
  //   return;
  // }

  // // 确保数据已正确设置到任务表单中
  // addTaskForm.taskParcelModels = JSON.parse(JSON.stringify(selectedList.value));
  allotDialog.value = true;
};

/**
 * 关闭分配
 */
const closeAllot = () => {
  allotDialog.value = false;
};

/**
 * 提交分配数据
 * @param list 数据列表
 */
const submitAllotData = (list: any[]) => {
  if (!list || !Array.isArray(list)) {
    return;
  }

  addTaskForm.taskParcelModels = list;
  for (let i = 0; i < list.length; i++) {
    if (list[i].userId) {
      addTaskForm.allocationType = 1;
      break;
    }
  }
  allotDialog.value = false;
};

/**
 * 关闭选择任务类型
 */
const handleCloseChooseTask = () => {
  chooseTaskTypeDialog.value = false;
};

/**
 * 提交任务类型
 */
const submitTaskType = () => {
  if (taskType.value === 0) {
    router.push('/taskManager/editTask/0');
  } else if (taskType.value === 1) {
    router.push('/taskManager/editTaskFlow/0');
  }
};

/**
 * 关闭任务表单
 */
const handleCloseTaskForm = () => {
  addTaskFormRef.value?.resetFields();
  Object.keys(addTaskForm).forEach((key) => {
    if (key !== 'allocationType') {
      (addTaskForm as any)[key] =
        typeof (addTaskForm as any)[key] === 'boolean'
          ? false
          : Array.isArray((addTaskForm as any)[key])
            ? []
            : typeof (addTaskForm as any)[key] === 'number'
              ? undefined
              : '';
    } else {
      (addTaskForm as any)[key] = 0;
    }
  });
  addTaskDialogVisible.value = false;
  searchDialog.value = false; // 关闭宗地管理对话框
};

/**
 * 取消添加任务
 */
const handleCancleAddTask = () => {
  addTaskDialogVisible.value = false;
};

/**
 * 远程方法
 * @param query 查询
 */
const remoteMethod = async (query: string) => {
  if (query !== '') {
    const params = {
      pageNum: 1,
      pageSize: 10,
      custName: query
    };
    try {
      const response = await listUser(params);
      if (response.code === 200) {
        taskReceiversOptions.value = handleUserList(response.rows);
      } else {
        ElMessage.error(response.msg);
      }
    } catch (error) {}
  } else {
    try {
      const response = await listUser({ pageSize: 10000000, pageNum: 1 });
      if (response.code === 200) {
        taskReceiversOptions.value = handleUserList(response.rows);
      } else {
        ElMessage.error(response.msg);
      }
    } catch (error) {}
  }
};

/**
 * 获取更多用户
 */
const getMoreUser = async () => {
  if (userPages.value > searchUser.pageNum) {
    searchUser.pageNum = searchUser.pageNum + 1;
    try {
      const res = await listUser(searchUser);
      if (res.code === 200) {
        taskReceiversOptions.value.push(...handleUserList(res.rows));
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {}
  }
};

/**
 * 表格单元格类名
 * @param row 行
 * @param column 列
 * @param rowIndex 行索引
 * @param columnIndex 列索引
 */
const tableCellClassName = ({ row, column, rowIndex, columnIndex }: any) => {
  //解构---利用单元格的 className 的回调方法，给行列索引赋值
  row.index = rowIndex;
  column.index = columnIndex;
  return '';
};

/**
 * 处理日期排序
 * @param val 日期范围
 */
const handleDateRank = (val: string[]) => {
  addTaskForm.dateRange = val;
  Object.assign(addTaskForm, { ...addTaskForm, dateRange: val });
};

/**
 * 处理选择字段
 * @param list 字段列表
 */
const handleSelectedField = (list: any[]) => {
  selectFieldVisible.value = false;
  const ids = list.map((item) => item.id);
  addTaskForm.taskFieldModelList = ids;
  const names = list.map((item) => item.fieldCn);
  addTaskForm.taskFieldModelNames = names.join('  , ');
};

/**
 * 关闭字段选择
 */
const handleCloseField = () => {
  selectFieldVisible.value = false;
};

/**
 * 处理用户列表
 * @param users 用户列表
 * @returns 任务接收者列表
 */
const handleUserList = (users: UserVO[]): TaskReceiver[] => {
  return users.map((user) => ({
    userId: Number(user.userId),
    custName: user.nickName || user.userName,
    userName: user.userName || user.nickName || '',
    nickName: user.nickName || user.userName || '',
    deptId: user.deptId,
    deptName: user.deptName || '',
    checked: false,
    isContains: []
  }));
};

// 修改类型比较的地方
const handleCompare = (value: string | number, compareValue: string | number) => {
  return String(value) === String(compareValue);
};

const handleTaskReceiverName = (val: TaskReceiver[]) => {
  const name: string[] = [];
  if (val.length > 0) {
    val.forEach((item) => {
      name.push(item.custName);
    });
    addTaskForm.receivers = val;
    addTaskForm.receiverNames = name.join(',');
  }
  Object.assign(addTaskForm, { ...addTaskForm });
};

/**
 * 处理表格选择变更
 * @param selection 选中的行
 */
const handleSelectionChange = (selection: any[]) => {
  // 存储选中的任务行
  selectedTasks.value = selection;
  // 启用或禁用批量结束按钮
  batchEndDisabled.value = selection.length === 0;
};

/**
 * 处理单个任务删除
 * 从选中行获取任务ID和名称，确认后调用删除API
 */
const handleDeleteSingle = async () => {
  // 当前不实现多选功能，这个方法暂时不使用
  // 但保留代码框架以备后续添加
  // 获取选中的任务ID和名称
  const taskId = '';
  const taskName = '';

  // 确认删除提示
  const confirmMessage = `是否确认删除【${taskName}】?`;

  try {
    // 使用Element Plus的确认框API
    await ElMessageBox.confirm(confirmMessage, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 调用删除API
    const res = await deleteTask(2, [taskId]);

    if (res.code === 200) {
      ElMessage.success(res.data);
      getSearchTaskList(order.value);
      addTaskDialogVisible.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    // 用户取消操作
    ElMessage({
      type: 'info',
      message: '已取消删除'
    });
  }
};

/**
 * 处理批量结束任务
 */
const handleBatchEnd = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要结束的任务');
    return;
  }

  // 获取选中任务的名称列表和ID列表
  const taskNames = selectedTasks.value.map((task) => task.name).join('、');
  const taskIds = selectedTasks.value.map((task) => task.id);

  try {
    // 确认结束提示
    await ElMessageBox.confirm(`确定要批量结束以下任务吗？\n${taskNames}`, '批量结束任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 构建批量请求参数
    const batchPromises = selectedTasks.value.map((task) => {
      return addTask({
        id: task.id,
        status: -1,
        timeTamp: task.timeTamp
      });
    });

    // 并行执行所有请求
    const results = await Promise.all(batchPromises);

    // 检查是否所有请求都成功
    const allSuccess = results.every((res) => res.code === 200);

    if (allSuccess) {
      ElMessage.success('批量结束任务成功');
      // 刷新任务列表
      getSearchTaskList(order.value);
    } else {
      // 如果有失败的请求，显示错误信息
      const errorMsgs = results.filter((res) => res.code !== 200).map((res) => res.msg);
      ElMessage.error(`部分任务结束失败: ${errorMsgs.join(', ')}`);
      // 仍然刷新列表以显示成功的更改
      getSearchTaskList(order.value);
    }
  } catch (error) {
    // 用户取消操作
    ElMessage({
      type: 'info',
      message: '已取消批量结束操作'
    });
  }
};

// 在script标签结束前添加defineExpose语句，导出getSearchTaskList方法
defineExpose({
  getSearchTaskList
});
</script>

<style lang="scss" scoped>
:deep(.el-checkbox__label) {
  padding-left: 2px;
}
.dialog-box {
  max-height: 400px;
  overflow: auto;
}
.dialog-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .item {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
}
// 修改对话框高度
.showAll_dialog {
  overflow: hidden;
  :deep(.el-dialog) {
    height: 70%;
    overflow: hidden;
  }
}
.task-name {
  height: 23px;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.row-currout {
  text-overflow: inherit;
  overflow: visible;
  white-space: pre-line;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: center;
}
.table-contianer {
  height: calc(100vh - 380px);
  overflow-y: hidden;
  // display: flex;
  // flex-direction: column;
  // align-items: stretch;
  // .el-table{
  //   flex-grow: 1;
  //   overflow-y: auto;
  // }
  // .pagination-container{
  //   height: 50px;
  // }
}
.form-select {
  width: 240px;
  :deep(.el-input) {
    .el-input__inner {
      width: 100%;
    }
  }
}
// 滚动条
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 4px;
}
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.handle-row {
  display: flex;
  align-items: center;
  .row {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .row-input {
    width: 100px;
  }
}
</style>
