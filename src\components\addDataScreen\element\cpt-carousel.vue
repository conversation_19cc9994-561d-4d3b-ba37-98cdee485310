<template>
  <div style="width: 100%; height: 100%; text-align: center">
    <el-carousel :height="height + 'px'" :trigger="option.attribute.trigger">
      <el-carousel-item v-for="item in option.attribute.imgUrls" :key="item">
        <authImg :authSrc="`${baseUrl}${item}?att=1`" :width="'100%'" :height="'100%'" :fit="option.attribute.fit" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script lang="ts" setup>
import authImg from '@/components/authImg/index.vue';
// ---Props---
interface Props {
  option: Record<string, any>;
  height: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 168
});

// ---定义变量---
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
defineOptions({
  name: 'cpt-carousel'
});
</script>

<style scoped></style>
