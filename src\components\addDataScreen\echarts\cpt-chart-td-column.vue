<template>
  <div :id="uuid" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-td-column'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData = ref();
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart.value?.resize();
  }
);
// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId: any) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        cptData.value = {
          xData: res.data.xdata.join(','),
          yData: res.data.ydata.join(','),
          yData2: '',
          yData3: '',
          yData4: ''
        };
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;

      loadChart(props.option.attribute);
    });
  }
};
const loadChart = (attribute) => {
  let columnColor = attribute.barColor;
  if (attribute.gradualColor) {
    columnColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: attribute.barColor1 },
      { offset: 0.5, color: attribute.barColor2 },
      { offset: 1, color: attribute.barColor3 }
    ]);
  }
  chartOption.value = {
    color: columnColor,
    title: {
      text: attribute.chartTitle,
      textStyle: {
        color: attribute.titleTextColor
      },
      left: attribute.titleLeft,
      top: attribute.titleTop
    },
    tooltip: {},
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true
    },
    xAxis: {
      show: attribute.xAxisShow,
      type: 'category',
      data: cptData.value.xData.split(','),
      axisLabel: {
        color: attribute.xLabelColor,
        rotate: attribute.xFontRotate //倾斜角度-180~180
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor
        }
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow
      }
    },
    yAxis: {
      show: attribute.yAxisShow,
      type: 'value',
      axisLabel: {
        color: attribute.yLabelColor
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor
        }
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow
      }
    },
    series: [
      {
        name: '新增用户数量',
        type: 'bar', //pictorialBar || bar
        showBackground: attribute.barBgShow,
        stack: 'account',
        barWidth: attribute.barWidth,
        data: cptData.value.yData.split(',')
      },
      {
        name: '邀请新用户数量',
        type: 'bar',
        stack: 'account',
        barWidth: attribute.barWidth,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffae88' },
            { offset: 1, color: '#ff7388' }
          ])
        },
        data: cptData.value.yData2.split(',')
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [attribute.barWidth, 10],
        symbolRotate: 0,
        itemStyle: {
          borderWidth: 0,
          color: '#10e6ff'
        },
        data: cptData.value.yData3.split(',')
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [attribute.barWidth, 10],
        itemStyle: {
          borderWidth: 0,
          color: '#ffcf90'
        },
        data: cptData.value.yData4.split(',')
      }
    ]
  };
  chart?.setOption(chartOption.value);
};
// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
