<!-- 图层数据列表 -->
<template>
  <div class="zong-di-list-continer">
    <div class="continer-content">
      <el-row :gutter="20" v-if="isUnfold">
        <el-col :span="24" :xs="24">
          <div class="list-title">
            <span class="title">{{ title }}</span>
            <span @click="handleUnfold">
              <el-image class="icon" :src="sqIcon" style="cursor: pointer"></el-image>
            </span>
          </div>
          <div class="list-search">
            <el-input v-model="dialogSearch.parcelName" :placeholder="`请输入${title}名称`" clearable>
              <template #prefix>
                <i class="el-icon-search"></i>
              </template>
            </el-input>
            <el-button type="primary" size="small" class="search-button" @click="searchZD">搜索</el-button>
          </div>
          <div class="list-content" :style="{ height: tableHeight + 'px' }">
            <div v-for="item in paginatedList" :key="item.id">
              <div class="content-item" @click="handleParceItem(item)" :class="{ 'list-content-active': nowCheckedZD == item.id }">
                <span class="item-name" :class="{ 'active-span': nowCheckedZD == item.id }">
                  {{ item.properties[tcName] }}
                </span>
                <i class="el-icon-arrow-right icon-right" v-show="nowCheckedZD != item.id"></i>
                <i class="el-icon-arrow-up icon-right" style="font-size: 16px" v-show="nowCheckedZD == item.id && item.checked"></i>
                <i class="el-icon-arrow-down icon-right" style="font-size: 16px" v-show="nowCheckedZD == item.id && !item.checked"></i>
              </div>
              <div v-show="nowCheckedZD == item.id && item.checked" class="child-tree">
                <el-tree
                  :data="graphicalList"
                  node-key="id"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  default-expand-all
                  highlight-current
                  :current-node-key="defaultExpand"
                  @node-click="checkedNode"
                >
                  <template #default="{ data }">
                    <span class="custom-tree-node">
                      <authImg
                        :authSrc="`${baseUrl}${data.iconUrl}?att=1`"
                        :width="'20px'"
                        :height="'20px'"
                        v-if="data.iconUrl && data.iconUrl.includes('_blob')"
                      />
                      <svg-icon
                        v-else
                        class="svg-ico"
                        :class="{
                          'no-span': !data.geomArcgis,
                          'checked-span': data.id == defaultExpand
                        }"
                        :icon-class="data.iconUrl"
                      />
                      <div
                        class="tree-span"
                        :class="{
                          'no-span': !data.geomArcgis,
                          'checked-span': data.id == defaultExpand
                        }"
                      >
                        {{ data.parcelName }}
                      </div>
                    </span>
                  </template>
                </el-tree>
              </div>
            </div>
            <div class="list-empty" v-if="paginatedList.length == 0">
              <el-empty :image="emptyImg"></el-empty>
            </div>
          </div>
          <div class="list-pagination">
            <pagination
              :total="dataTotal"
              v-model:page="dialogSearch.pageNum"
              v-model:limit="dialogSearch.pageSize"
              :pager-count="pagerCount"
              layout="jumper,sizes,pager"
              :page-sizes="[10, 50, 100, 200]"
              @pagination="getParceListByPage"
            />
          </div>
          <div class="page-div" style="margin-bottom: 20px">
            <div class="page-total">共{{ dataTotal }}条</div>
            <div class="page-right">
              <el-button type="primary" size="small" :disabled="btnType == 'noUp' || nowCheckedZD == 0" @click="btnUPandDown(1)">上一个</el-button>
              <el-button type="primary" size="small" :disabled="btnType == 'noDown' || nowCheckedZD == 0" @click="btnUPandDown(2)">下一个</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row v-else>
        <div class="zongdi-icon" @click="handleUnfoldZongDi">
          <el-image class="icon" :src="zdIcon"></el-image>
        </div>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
import shouqiIcon from '@/assets/images/shouqi.png';
import zhankaizongdi from '@/assets/images/zhankaizongdi.png';
import emptyImgTemp from '@/assets/images/empty.png';
// api部分
import { findAsyncMsg as findAsyncMsgApi, findAsyncFileBrowser } from '@/api/project';
import { exportSettingList, getExportDetail, downLoadPublic } from '@/api/modal';
import { listUser } from '@/api/system/user';
import { getSearchTask } from '@/api/task';
// 组件部分
import authImg from '@/components/authImg/index.vue';
import { useProjectStore } from '@/store/modules/project';
import { useRoute } from 'vue-router';
const route = useRoute();
// --- props
interface QueryParams {
  pageNum: number;
  pageSize: number;
  parcelName: string;
  moduleId: number;
  areaCode: string;
}

const props = withDefaults(
  defineProps<{
    tcData?: any[];
    tcName?: string;
    total?: number;
    queryParams?: QueryParams;
    pageCount?: number;
    title?: string;
    ruleTree?: any[];
    mainHeight?: number;
  }>(),
  {
    tcData: () => [],
    tcName: '',
    total: 0,
    queryParams: () => ({
      pageNum: 1,
      pageSize: 10,
      parcelName: '',
      moduleId: 0,
      areaCode: ''
    }),
    pageCount: 0,
    title: '',
    ruleTree: () => [],
    mainHeight: 0
  }
);

// --- 定义变量 ---
const sqIcon = ref(shouqiIcon); //  展开收起图标
const zdIcon = ref(zhankaizongdi); // 收起的宗地Icon
const nowCheckedZD = ref(0); // 当前选择的一项
const paraceName = ref(''); //  搜索的宗地名
const isUnfold = ref(true); // 是否展开
const fullscreenLoading = ref(false); // 全屏loading
const shpDialog = ref(false); //导出shp弹窗
const wkidList = ref([
  {
    value: 'CGCS_2000',
    label: 'CGCS_2000',
    children: [
      {
        value: 'cgcs2000_3',
        label: '三度带',
        children: []
      },
      {
        value: 'cgcs2000_6',
        label: '六度带',
        children: []
      }
    ]
  }
]);
const pagerCount = ref(5); // 分页器数量
const exportBtn = ref([]); // 导出按钮
const publicDownDialoig = ref(false); // 公共导出弹窗
//选中的导出设置内容
const nowDownMsg: any = reactive({
  coordinate: {
    placeholder: '',
    must: 1
  },
  dataList: {
    placeholder: ''
  }
});
// 导出设置
let downLoadMsg: any = reactive({
  zdList: []
});
let downLoadMsgRule = reactive({}); // 导出设置校验
const searchDialog = ref(false); // 数据筛选弹窗
// 导出的树结构迭代 整理出来shp和gdb列表
const shpGdbTree = ref({
  shp: [],
  gdb: [],
  pic: [],
  word: [],
  excel: []
});
const progress = ref(0); // 进度
const progressDialog = ref(false); // 进度条弹窗
const downLoadFileName = ref(''); // 导出的文件名
const graphicalList = ref([]); // 图形信息 树
const defaultProps = ref({
  children: 'list',
  label: 'parcelName'
});
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
const itemGraphicalList = ref([]); // 把得到详情的树整理成列表
const defaultExpand = ref(0); // 默认展开
const shaixuanDialog = ref(false); // 筛选弹窗
// 弹窗筛选
let dialogSearch: any = reactive({
  areaCode: '', // 行政区划
  createDate: '', // 创建时间
  updateDate: '', // 最后修改时间
  createUserId: '', // 采集人
  updateUserId: '', // 最后修改人
  taskId: '', // 任务id
  allocation: '', //查询是否分配任务 如果选择了任务设置为true
  pageNum: 1,
  pageSize: 10,
  parcelName: '',
  ruleIds: [],
  parcelCode: null
});
const userPages = ref(0); // 用户分页
const searchUser = ref({
  pageNum: 1,
  pageSize: 10
});
const userList = ref([]); // 用户列表
const taskList = ref([]); // 任务列表
const chooseUserType = ref(1); // 选择人员的类型 1采集人 2最后修改人
const chooseUserDialog = ref(false); // 选择人员弹窗
const chooseUserId = ref(0); // 当前需要反显的人员id
const chooseUserTitle = ref(''); // 选择人员的title
const emptyImg = ref(emptyImgTemp); // 空图片
const showSearchMsg = ref(false); // 是否展示筛选结构内容
const showDetail = ref(true); // 展示详细筛选条件
const shpUploadDialog = ref(false); // 导入或者更新shp
const currentPercent = ref(0); // 当前下载进度
const loadingProgess = ref(null);
const managerDialog = ref(false); // 数据管理
const dataTotal = ref(0);
const downLoadRef = ref(null); // 导出表单ref

// ---computed ---
const tableHeight = computed(() => {
  return props.mainHeight - 460;
});

const queryParamsCopy = computed(() => {
  return props.queryParams;
});

const paginatedList = computed(() => {
  // 分页的其实下标
  const startIndex = (queryParamsCopy.value.pageNum - 1) * queryParamsCopy.value.pageSize;
  // 分页的末尾下标
  const endIndex = queryParamsCopy.value.pageNum * queryParamsCopy.value.pageSize;
  if (props.queryParams.parcelName) {
    queryParamsCopy.value.pageSize = 10;
    queryParamsCopy.value.pageNum = 1;
    const list = [];
    props.tcData.forEach((v) => {
      if (v.properties[props.tcName].includes(queryParamsCopy.value.parcelName)) {
        list.push(v);
      }
    });
    dataTotal.value = list.length;
    if (list.length > 10) {
      return list.slice(startIndex, endIndex);
    } else {
      return list;
    }
  } else {
    dataTotal.value = props.tcData.length;
    // 返回切割后的数据
    return props.tcData.slice(startIndex, endIndex);
  }
});

const btnType = computed(() => {
  if (!nowCheckedZD.value) {
    return 'normal';
  } else {
    let index;
    paginatedList.value.forEach((v, idx) => {
      if (v.id == nowCheckedZD.value) {
        index = idx;
      }
    });
    if (index == 0 && queryParamsCopy.value.pageNum == 1) {
      return 'noUp';
    } else if (index == paginatedList.value.length - 1 && props.pageCount == queryParamsCopy.value.pageNum) {
      return 'noDown';
    } else {
      return 'normal';
    }
  }
});

// --- watch ---
watch(
  dialogSearch,
  (val: any) => {
    if (val.areaCode || val.createDate || val.optUserId || val.createUserId || val.updateUserId || val.taskId || val.parcelName) {
      showSearchMsg.value = true;
    } else {
      showSearchMsg.value = false;
    }
  },
  { deep: true }
);

watch(
  currentPercent,
  (val: any) => {
    const percent = val.toFixed(2);
    loadingProgess.value.setText(`数据下载中${percent}MB`);
  },
  { deep: true }
);

//  ---定义emit---
const emit = defineEmits<{
  (e: 'selectTc', item: any): void;
  (e: 'changeGraph', node: any): void;
  (e: 'getParmas', parmas: any): void;
}>();

// ---methods---

/**
 * 搜索宗地
 */
const searchZD = () => {};
/**
 * 分页查询宗地
 */
const getParceListByPage = () => {};
/**
 * 收起宗地列表
 */
const handleUnfold = () => {
  isUnfold.value = false;
};
/**
 * 展开宗地列表
 */
const handleUnfoldZongDi = () => {
  isUnfold.value = true;
};
/**
 * 点击某一项宗地
 */
const handleParceItem = (item: any) => {
  nowCheckedZD.value = item.id;
  // 选中某一项的时候高亮图层
  emit('selectTc', item);
};
/**
 * 上一个宗地、下一个宗地
 */
const btnUPandDown = (type: number) => {
  let index;
  paginatedList.value.forEach((v, idx) => {
    if (nowCheckedZD.value == v.id) {
      index = idx;
    }
  });
  if (type == 1) {
    //上一个
    if (index == 0 && queryParamsCopy.value.pageNum != 1) {
      //代表不是第一页的第一个，回到上一页并选中最后一个
      queryParamsCopy.value.pageNum--;
      nowCheckedZD.value = paginatedList.value[paginatedList.value.length - 1].id;
      emit('selectTc', paginatedList.value[paginatedList.value.length - 1]);
    } else {
      //不换页 选中上一个
      nowCheckedZD.value = paginatedList.value[index - 1].id;
      emit('selectTc', paginatedList.value[index - 1]);
    }
  } else {
    //下一个
    if (index == paginatedList.value.length - 1 && queryParamsCopy.value.pageNum != props.pageCount) {
      //代表不是最后一页的最后一个，点下一个的时候要翻页并选中第一个
      queryParamsCopy.value.pageNum++;
      nowCheckedZD.value = paginatedList.value[0].id;
      emit('selectTc', paginatedList.value[0]);
    } else {
      nowCheckedZD.value = paginatedList.value[index + 1].id;
      emit('selectTc', paginatedList.value[index + 1]);
    }
  }
};
/**
 * 返回新的command对象
 * @param item
 */
const beforeHandleCommand = (item: any) => {
  //index我这里是遍历的角标，即你需要传递的额外参数
  return item;
};
/**
 * 选中某个导出按钮
 */
const handleCommand = (data: any) => {
  getExportDetail(data.id).then((res) => {
    if (res.code == 200) {
      nowDownMsg.value = res.data;
      // 先初始化树
      shpGdbTree.value = {
        shp: [],
        gdb: [],
        pic: [],
        word: [],
        excel: []
      }; //导出的树结构迭代 整理出来shp和gdb列表
      downLoadFileName.value = nowDownMsg.value.detail.fileName;
      getShpGdb([nowDownMsg.value.detail]);
      initDownMsg();
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 初始化导出设置内容
 */
const initDownMsg = () => {
  const downLoadMsgTemp: any = {
    zdList: [], //选中的宗地
    zdListNames: '', //选择的宗地中文名
    onWkid: '' //坐标系
  }; //导出设置
  const downLoadMsgRuleTemp: any = {
    zdList: [{ required: true, message: '请选择数据', trigger: 'change' }]
  }; //导出设置校验
  if (nowDownMsg.coordinate.disable == 0) {
    //坐标系
    if (nowDownMsg.coordinate.defaultValue) {
      downLoadMsgTemp.onWkid = nowDownMsg.coordinate.defaultValue;
    } else {
      downLoadMsgTemp.onWkid = '';
    }
    if (nowDownMsg.coordinate.must == 1) {
      //必填
      const rules = [{ required: true, message: '请选择坐标系', trigger: 'change' }];
      downLoadMsgRuleTemp.onWkid = rules;
    }
  }
  if (nowDownMsg.dataList.must == '1') {
    //如果数据列表必填 需要加验证
  }
  downLoadMsg = downLoadMsgTemp;
  downLoadMsgRule = downLoadMsgRuleTemp;
  publicDownDialoig.value = true;
};
/**
 * 关闭公共导出弹窗
 */
const handleClose = () => {
  downLoadMsg.value = {
    zdList: []
  };
  publicDownDialoig.value = false;
};
/**
 * 关闭筛选数据弹窗
 */
const closeSearchDialog = () => {
  downLoadMsg.value.zdList = [];
  downLoadMsg.value.zdListNames = '';
  searchDialog.value = false;
};
/**
 * 选择数据
 */
const chooseData = () => {
  managerDialog.value = false;
  searchDialog.value = true;
};

/**
 * 得到筛选要素数据
 */
const getChooseData = (list: any) => {
  downLoadMsg.value.zdList = list;
  const names = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  downLoadMsg.value.zdListNames = names.join(',');
  searchDialog.value = false;
};

/**
 * 真正导出
 */
const submitDown = () => {
  downLoadRef.value.validate((valid) => {
    if (valid) {
      const wkId = '3857';
      const parmas: any = {
        exportId: nowDownMsg.value.id,
        moduleId: nowDownMsg.value.moduleId,
        wkId: wkId
      };
      if (downLoadMsg.value.onWkid) {
        if (downLoadMsg.value.onWkid[2] < 25) {
          //六度带
          parmas.wkId = 4478 + downLoadMsg.value.onWkid[2];
        } else if (downLoadMsg.value.onWkid[2] >= 25) {
          //三度带
          parmas.wkId = 4513 + downLoadMsg.value.onWkid[2] - 25;
        }
      }
      const zdId = [];
      downLoadMsg.value.zdList.forEach((v) => {
        zdId.push(v.id);
      });
      const gdbMap = [];
      const shpMap = [];
      const picMap = [];
      const wordMap = [];
      const excelMap = [];
      shpGdbTree.value.shp.forEach((v) => {
        const obj = {
          exportDetailId: v,
          zdId: zdId
        };
        shpMap.push(obj);
      });
      shpGdbTree.value.gdb.forEach((v) => {
        const obj = {
          exportDetailId: v,
          zdId: zdId
        };
        gdbMap.push(obj);
      });
      shpGdbTree.value.pic.forEach((v) => {
        const obj = {
          exportDetailId: v,
          zdId: zdId
        };
        picMap.push(obj);
      });
      shpGdbTree.value.word.forEach((v) => {
        const obj = {
          exportDetailId: v,
          zdId: zdId
        };
        wordMap.push(obj);
      });
      shpGdbTree.value.excel.forEach((v) => {
        const obj = {
          exportDetailId: v,
          zdId: zdId
        };
        excelMap.push(obj);
      });
      parmas.gdbMap = gdbMap;
      parmas.shpMap = shpMap;
      parmas.picMap = picMap;
      parmas.wordMap = wordMap;
      parmas.excelMap = excelMap;
      downLoadMsg.value = {
        zdList: [], //选中的宗地
        zdListNames: '', //选择的宗地中文名
        onWkid: ''
      };
      downLoadPublic(parmas).then((res) => {
        if (res.code == 200) {
          progress.value = res.data.progress;
          progressDialog.value = true;
          findAsyncMsg(res.data.id);
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      return false;
    }
  });
};

/**
 * 把导出里面的文件结构迭代 整理出来代表gdb和shp的树id
 * @param list
 */
const getShpGdb = (list: any) => {
  list.forEach((v: any) => {
    if (v.fileType == 3) {
      // shp
      shpGdbTree.value.shp.push(v.id);
    } else if (v.fileType == 4) {
      // gdb
      shpGdbTree.value.gdb.push(v.id);
    } else if (v.fileType == 5) {
      // 图片
      shpGdbTree.value.pic.push(v.id);
    } else if (v.fileType == 1) {
      // word
      shpGdbTree.value.word.push(v.id);
    } else if (v.fileType == 2) {
      //excel
      shpGdbTree.value.excel.push(v.id);
    }
    if (v.list.length != 0) {
      getShpGdb(v.list);
    }
  });
};

/**
 * 循环获取进度条，当进度条==1的时候停止调用
 */
const findAsyncMsg = (id: any) => {
  const parmas = {
    id: id
  };
  findAsyncMsgApi(parmas).then((res) => {
    if (res.code == 200) {
      if (parseFloat(res.data.progress) != 1) {
        progress.value = parseFloat(res.data.progress) * 100;
        if (res.data.status == -1) {
          ElMessage.error('导出出错，请联系管理员');
          publicDownDialoig.value = false;
          progressDialog.value = false;
        } else {
          setTimeout(() => {
            findAsyncMsg(id);
          }, 1000);
        }
      } else if (parseFloat(res.data.progress) == 1) {
        progress.value = parseFloat(res.data.progress) * 100;
        publicDownDialoig.value = false;
        generalDown(id, downLoadFileName.value);
        setTimeout(() => {
          progressDialog.value = false;
        }, 1000);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 通用下载
 */
const generalDown = (id: any, fileName: any) => {
  const parmas = {
    id: id,
    fileName: fileName
  };
  findAsyncFileBrowser(parmas, showProgess).then((res) => {
    if (res.data.type == 'application/json') {
      loadingProgess.value.close();
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data) => {
        bugMsg = JSON.parse(data.currentTarget['result']).msg;
        ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      const endFileName = name.substring(index + 1, name.length) || fileName;
      const blob = new Blob([res.data], { type: 'application/zip' });
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, endFileName);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = endFileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
      setTimeout(() => {
        loadingProgess.value.close();
      }, 3000);
    }
  });
};
/**
 * 获取当前下载进度
 * @param progress
 */
const showProgess = (progress: any) => {
  const currentPercent = progress.loaded / 1024 / 1024;
  const percent = currentPercent.toFixed(2);
  loadingProgess.value = ElLoading.service({
    lock: true,
    text: `数据下载中${percent}MB`,
    spinner: 'el-icon-loading',
    background: 'rgba(255, 255, 255, 0.9)'
  });
};
const checkedNode = (node: any, data: any, data1: any) => {
  defaultExpand.value = node.id;
  emit('changeGraph', node);
};

/**
 * 通过id高亮选中某个节点
 * @param id
 */
const heightNodeForId = (id: any) => {
  defaultExpand.value = id;
};

// 打开筛选弹窗
const shaixuan = async () => {
  shaixuanDialog.value = true;
  await getSearchTaskList();
  searchUser.value.pageNum = 1;
  listUser(searchUser.value).then((response) => {
    if (response.code) {
      userList.value = response.rows;
      let userPagesTemp = parseInt(String(response.total / 10));
      if (response.total % 10 > 0) {
        userPagesTemp = userPagesTemp + 1;
      }
      userPages.value = userPagesTemp;
    } else {
      ElMessage.error(response.msg);
    }
  });
};
/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 重置筛选条件
 */
const handleResetSearch = () => {
  dialogSearch = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 10,
    parcelName: '',
    moduleId: queryParamsCopy.value.moduleId
  };
  emit('getParmas', dialogSearch);
  shaixuanDialog.value = false;
};
/**
 * 获取更多用户  分页
 */
const getMoreUser = () => {
  if (userPages.value > searchUser.value.pageNum) {
    searchUser.value.pageNum = searchUser.value.pageNum + 1;
    listUser(searchUser.value).then((res) => {
      if (res.code == 200) {
        res.rows.forEach((v) => {
          userList.value.push(v);
        });
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

/**
 * 远程方法
 */
const remoteMethod = (query: any) => {
  if (query !== '') {
    const parmas = {
      pageNum: 1,
      pageSize: 10,
      custName: query
    };
    listUser(parmas).then((response) => {
      if (response.code) {
        userList.value = response.rows;
      } else {
        ElMessage.error(response.msg);
      }
    });
  } else {
    searchUser.value.pageNum = 1;
    listUser(searchUser.value).then((response) => {
      if (response.code) {
        userList.value = response.rows;
      } else {
        ElMessage.error(response.msg);
      }
    });
  }
};

/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: queryParamsCopy.value.moduleId
  };
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 显示选择人员弹窗
 * @param type
 */
const showChooseUser = (type: any) => {
  // type 1采集人 2最后修改人
  chooseUserType.value = type;
  if (type == 1) {
    chooseUserTitle.value = '采集人选择';
    chooseUserId.value = dialogSearch.value.createUserId;
  } else {
    chooseUserTitle.value = '最后修改人选择';
    chooseUserId.value = dialogSearch.value.optUserId;
  }
  chooseUserDialog.value = true;
};

/**
 * 提交选择的人员
 */
const submitChooseUser = (obj: any) => {
  if (chooseUserType.value == 1) {
    //采集人
    dialogSearch.value.createUserId = obj.userId;
    dialogSearch.value.createUserName = obj.custName;
  } else if (chooseUserType.value == 2) {
    //最后更新人
    dialogSearch.value.optUserId = obj.userId;
    dialogSearch.value.optUserName = obj.custName;
  }
  chooseUserDialog.value = false;
};

/**
 * 关闭选择人员弹窗
 */
const closeChooseUser = () => {
  chooseUserDialog.value = false;
};

/**
 * 清除选中的用户 1采集人 2最后更新人
 */
const clearUser = (type: any) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

/**
 * 通过任务id得到任务名称
 */
const getTaskName = (id: any) => {
  let taskName = '';
  for (let index = 0; index < taskList.value.length; index++) {
    if (taskList.value[index].id == id) {
      taskName = taskList.value[index].name;
      break;
    }
  }
  return taskName;
};

/**
 * 导入或者更新shp 打开弹窗
 */
const handleUpdateShp = () => {
  shpUploadDialog.value = true;
};
/**
 * 导入或者更新shp  关闭弹框
 */
const handleCloseShpDialog = () => {
  shpUploadDialog.value = false;
};
/**
 * 管理数据
 */
const showManager = () => {
  managerDialog.value = true;
  searchDialog.value = true;
};

/**
 * 改变数据管理
 * @param flg
 */
const changeMange = (flg: any) => {
  managerDialog.value = flg;
};

// ---mounted ---
onMounted(() => {
  const list_3 = [];
  for (let i = 25; i < 46; i++) {
    list_3.push({ value: i, label: `${i}` });
  }
  const list_6 = [];
  for (let i = 13; i < 24; i++) {
    list_6.push({ value: i, label: `${i}` });
  }
  wkidList.value[0].children[0].children = list_3;
  wkidList.value[0].children[1].children = list_6;
});
</script>

<style lang="scss" scoped>
:deep(.el-empty__description) {
  margin-top: -20px;
}
@media screen and (max-width: 1366px) {
  .zong-di-list-continer {
    width: 260px;
    .tree-span {
      font-size: 12px !important;
    }
    .list-content {
      .content-item {
        .item-name {
          font-size: 12px !important;
        }
        .icon-right {
          font-size: 12px !important;
        }
      }
    }
    .list-pagination {
      width: 260px !important;
      .pagination-container {
        width: 250px !important;
      }
      :deep(.el-pagination) {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        height: 70px !important;
      }
    }
    .page-div {
      margin-top: 30px;
    }
  }
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .dialog-label {
    color: #161d26;
    margin-right: 10px;
  }
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0px;
}
.dialog-row-allChecked {
  position: absolute;
  right: 0px;
  top: -40px;
}
.zong-di-list-continer {
  .continer-content {
    height: auto;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 8px 8px;
    display: flex;
    flex-direction: column;
    z-index: 1;
    backdrop-filter: blur(5px);
    &:after {
      content: '';
      // width: 312px;
      height: auto;
      position: relative;
      left: 0;
      top: 0;
      background: inherit;
      z-index: 2;
      backdrop-filter: blur(10px);
    }
    .list-title {
      // width: 312px;
      height: 40px;
      line-height: 40px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 0px 0px;
      display: flex;
      justify-content: center;
      align-content: center;
      position: relative;
      border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
      z-index: 3;
      .shaixuan {
        position: absolute;
        left: 12px;
      }
      .title {
        // width: 56px;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        padding-top: 10px;
        padding-bottom: 10px;
      }
      .icon {
        width: 14px;
        height: 14px;
        position: absolute;
        right: 12px;
        top: 12px;
      }
    }
    .list-search {
      border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
      padding: 12px;
      // width: 312px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-content: center;
      position: relative;
      z-index: 3;
      .el-input {
        :deep(.el-input__inner) {
          color: #fff;
          width: 100%;
          height: 32px;
          background-color: rgb(255, 255, 255, 0.3);
          border-radius: 4px;
          border-color: none;
          border: none;
          z-index: 8;
          &::placeholder {
            color: rgb(255, 255, 255, 0.5);
          }
          /* 谷歌 */
          &::-webkit-input-placeholder {
            color: rgb(255, 255, 255, 0.5);
          }
          /* 火狐 */
          &::-moz-placeholder {
            color: rgb(255, 255, 255, 0.5);
          }
          /*ie*/
          &::-ms-input-placeholder {
            color: rgb(255, 255, 255, 0.5);
          }
        }
        .el-input__prefix {
          height: 32px;
          line-height: 32px;
          left: 12px;
          color: #ffffff;
        }
      }
      .search-button {
        width: 40px;
        height: 24px;
        background: var(--current-color);
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        margin-left: 8px;
        padding-left: 8px;
        margin-top: 4px;
      }
    }
    /*滚动条样式*/
    .list-content::-webkit-scrollbar {
      width: 4px;
    }
    .list-content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    .list-content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
    .list-content {
      // width: 312px;
      // height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
      z-index: 3;
      .list-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .content-item {
        width: 100%;
        min-height: 44px;
        border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        color: #ffffff;
        padding: 12px 16px;
        position: relative;
        z-index: 4;
        cursor: pointer;
        .item-name {
          width: 42px;
          height: 20px;
          font-size: 14px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 20px;
        }
        .icon-right {
          position: absolute;
          right: 16px;
        }
      }
      .child-tree {
        .el-tree {
          background: transparent;
          padding: 0px 10px 10px 10px;
          width: 100%;
          .el-icon svg {
            //原有的箭头 去掉
            display: none !important;
            height: 0;
            width: 0;
          }
          .tree-span {
            color: #fff;
            font-size: 14px;
            margin-left: 12px;
          }
          .no-span {
            color: #ff3333 !important;
          }
          .checked-span {
            color: var(--current-color) !important;
          }
          :deep(.el-tree-node__content) {
            width: 512;
            height: 44px;
            background: rgba(0, 0, 0, 0);
            border-radius: 0px 0px 0px 0px;
            .custom-tree-node {
              margin-left: 12px;
              display: flex;
              align-items: center;
              position: relative;
              width: 100%;
              height: 44px;
              border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
              .zd-icon {
                width: 20px;
                .el-image {
                  :deep(.el-image__inner) {
                    width: 20px;
                    height: 20px;
                    margin: 12px;
                    vertical-align: bottom;
                  }
                }
              }
              .zd-name {
                height: 20px;
                width: 300px;
                font-size: 14px;
                font-family:
                  PingFang SC-Medium,
                  PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 20px;
                margin-left: 12px;
              }
              .zd-leng {
                position: absolute;
                right: 40px;
                height: 20px;
                font-size: 14px;
                font-family:
                  PingFangSC-328080,
                  PingFang SC;
                font-weight: normal;
                color: rgba(255, 255, 255, 0.5);
                line-height: 20px;
              }
              .zd-arrow {
                position: absolute;
                right: 10px;
                height: 20px;
                font-size: 14px;
                font-family:
                  PingFangSC-328080,
                  PingFang SC;
                font-weight: normal;
                color: rgba(255, 255, 255, 0.5);
                line-height: 20px;
              }
            }
            &:hover {
              width: 512;
              height: 44px;
              background: rgba(0, 0, 0, 0.5);
              border-radius: 0px 0px 0px 0px;
              opacity: 1;
            }
          }
        }
      }
    }
    .list-content-active {
      background: rgb(0, 0, 0, 0.5);
      color: var(--current-color) !important;
    }
    .active-span {
      color: var(--current-color) !important;
    }
    .list-pagination {
      width: 340px;
      .pagination-container {
        z-index: 3;
        background-color: transparent !important;
        height: 40px;
        width: 350px;
        display: flex;
        :deep(.el-pagination) {
          top: 0;
          height: 40px;
          .el-pagination__total {
            color: #dbe7ee;
            width: 70px;
          }
          .btn-prev {
            background: transparent !important;
            background-color: transparent !important;
          }
          .btn-next {
            background: transparent !important;
            background-color: transparent !important;
          }
          .el-pager {
            width: 130px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 7px;
            .number {
              background-color: transparent;
              color: #dbe7ee;
              opacity: 1;
              width: 20px;
            }
            .active {
              width: 32px;
              // height: 32px;
              line-height: 32px;
              background: var(--current-color) !important;
              border-radius: 5px 5px 5px 5px;
              opacity: 1;
            }
            li {
              margin: 0;
              padding: 0;
            }
          }
          .el-pagination__jump {
            background-color: transparent;
            color: #dbe7ee;
            .el-pagination__editor.el-input {
              background-color: transparent !important;
              height: 20px;
              line-height: 20px;
              width: 30px;
            }
            .el-input {
              .el-input__inner {
                background-color: rgb(0, 0, 0, 0.4);
                border: none;
                height: 20px;
                line-height: 20px;
                width: 30px;
                color: #fff;
              }
            }
          }
        }
        :deep(.el-pagination .el-select .el-input .el-input__inner) {
          background-color: transparent;
          border-color: transparent;
          color: rgb(255, 255, 255, 0.7);
        }
        :deep(.el-pagination__sizes .el-input .el-input__inner:hover) {
          border-color: transparent;
        }
        :deep(.el-pagination.is-background .el-pager li) {
          background-color: transparent;
          color: #fff;
        }
      }
    }
    .page-total {
      color: #dbe7ee;
      margin-left: 17px;
      font-size: 12px;
    }
    .page-div {
      display: flex;
      flex-direction: row;
      align-items: center;
      .page-total {
        color: #dbe7ee;
        margin-left: 17px;
        font-size: 12px;
      }
      .page-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        margin-right: 10px;
      }
    }
    .list-btn {
      z-index: 3;
      display: flex;
      justify-content: flex-end;
      margin-top: 27px;
      margin-bottom: 24px;
      padding-right: 12px;
      .el-button {
        z-index: 4;
      }
    }
    .zongdi-icon {
      display: flex;
      justify-content: center;
      align-self: center;
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      cursor: pointer;
      .icon {
        width: 20px;
        height: 20px;
        margin: auto;
      }
    }
  }
  .search-div {
    position: absolute;
    top: 0px;
    left: 340px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 8px 8px;
    color: #fff;
    font-size: 12px;
    padding: 10px;
    cursor: pointer;
    margin-left: 10px;
    width: 240px;
    .search-title {
      font-size: 14px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
    .search-item {
      font-size: 12px;
      margin-bottom: 5px;
      display: flex;
      flex-wrap: wrap;
    }
  }
  .min-search {
    left: 40px;
  }
  .min-width {
    width: 100px;
  }
}
.svg-ico {
  color: #fff;
}
.close-ico {
  position: absolute;
  right: 10px;
  top: 0px;
  z-index: 1;
  cursor: pointer;
  font-size: 16px;
}
</style>
