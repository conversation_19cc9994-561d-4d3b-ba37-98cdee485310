<template>
  <div :style="{ backgroundColor: bgColor }" v-if="route && route.path">
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <transition :enter-active-class="proxyInstance?.animate.menuSearchAnimate.enter" mode="out-in">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :background-color="bgColor"
          :text-color="textColor"
          :unique-opened="true"
          :active-text-color="theme"
          :collapse-transition="false"
          mode="vertical"
          @click="handleMenuClick"
        >
          <sidebar-item :collapse="isCollapse" v-for="(r, index) in sidebarRouters" :key="r.path + index" :item="r" :base-path="r.path" />
        </el-menu>
      </transition>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import SidebarItem from './SidebarItem.vue';
import variables from '@/assets/styles/variables.module.scss';
import { useAppStore } from '@/store/modules/app';
import { useSettingsStore } from '@/store/modules/settings';
import { usePermissionStore } from '@/store/modules/permission';
import { RouteRecordRaw } from 'vue-router';
import { ComponentInternalInstance, ComponentPublicInstance, computed } from 'vue';
import { useRoute } from 'vue-router';
import { getCurrentInstance } from 'vue';

interface AnimateInstance {
  animate: {
    menuSearchAnimate: {
      enter: string;
    };
  };
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const proxyInstance = proxy as unknown as ComponentPublicInstance & AnimateInstance;

const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
const sidebarRouters = computed<RouteRecordRaw[]>(() => permissionStore.getSidebarRoutes());
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route || {};
  // if set path, the sidebar will highlight the path you set
  if (meta?.activeMenu) {
    return meta.activeMenu;
  }
  return path || '';
});

const bgColor = computed(() => (sideTheme.value === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground));
const textColor = computed(() => (sideTheme.value === 'theme-dark' ? variables.menuColor : variables.menuLightColor));

const handleMenuClick = (key: string, keyPath: string[]) => {
  try {
    // 这里不需要具体实现，只需要确保任何错误都被捕获
  } catch (err) {
    console.error('菜单点击处理错误:', err);
  }
};
</script>
