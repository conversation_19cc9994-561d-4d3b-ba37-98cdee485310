<!-- 拓扑检查 -->
<template>
  <div class="tuopuCheck-main">
    <el-timeline>
      <el-timeline-item :timestamp="filterTime(item.insertTime)" placement="top" v-for="(item, index) in list" :key="index">
        <el-card>
          <div class="card-body">
            <div class="flex-row"><span class="label">操作者：</span>{{ item.createName }}</div>
            <div class="flex-row">
              <span class="label">图形名称：</span>
              <span class="end-label">
                {{ item.parcelName }}
              </span>
            </div>
            <div class="flex-row">
              <span class="label">检查条件：</span>
              <span class="end-label">
                {{ item.checkInfo }}
              </span>
            </div>
            <div class="flex-row">
              <span class="label">检查结果：</span>
              <span class="end-label">
                {{ item.content }}
              </span>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    <div v-if="!list || list.length == 0">
      <el-empty description="暂无检查日志"></el-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { getTuopuLog } from '@/api/project';
import { ElMessage } from 'element-plus';

const list = ref([]);

// 获取日志
const getLog = (id: string | number) => {
  const params = {
    parcelId: id,
    dealStatus: 0,
    readStatus: 0
  };

  getTuopuLog([params]).then((res) => {
    if (res.code == 200) {
      list.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

defineExpose({
  getLog
});

// 格式化时间
const filterTime = (value: string | number) => {
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month: string | number = date.getMonth() + 1;
  let day: string | number = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  let hh: string | number = date.getHours();
  let mm: string | number = date.getMinutes();
  let ss: string | number = date.getSeconds();
  hh = hh < 10 ? '0' + hh : hh;
  mm = mm < 10 ? '0' + mm : mm;
  ss = ss < 10 ? '0' + ss : ss;
  return year + '-' + month + '-' + day + ' ' + hh + ':' + mm + ':' + ss;
};
</script>

<style lang="scss" scoped>
:deep(.el-timeline-item__content) {
  margin-right: 10px;
}
.tuopuCheck-main {
  width: 100%;
  height: calc(100% - 10px);
  margin-top: 10px;
  padding-left: 10px;
  .card-body {
    width: calc(100% - 20px);
    min-height: 116px;
    border-radius: 8px 8px 8px 8px;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .flex-row {
      display: flex;
      font-size: 14px;
      margin-bottom: 5px;
      .label {
        font-size: 14px;
      }
      .end-label {
        flex: 1;
      }
    }
  }
}
</style>
