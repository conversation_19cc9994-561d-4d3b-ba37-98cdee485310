<template>
  <div class="dynamic-form-container">
    <div class="left-board">
      <el-scrollbar class="left-scrollbar">
        <el-tabs v-model="activeTabName" :stretch="true">
          <el-tab-pane label="常用组件" name="common">
            <div class="components-list">
              <draggable
                class="components-draggable"
                :list="commonComponents"
                :group="{ name: 'componentsGroup', pull: 'clone', put: false }"
                :clone="cloneComponent"
                draggable=".components-item"
                :sort="false"
                @end="onEnd"
                itemKey="id"
              >
                <template #item="{ element, index }">
                  <div class="components-item" @click="addComponent(element)" v-show="!element.isHide">
                    <div class="components-body">
                      <el-icon><component :is="element.tagIcon" /></el-icon>
                      {{ element.label }}
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>

    <div class="center-board">
      <el-scrollbar class="center-scrollbar">
        <el-row class="center-board-row">
          <el-form label-position="top" :disabled="formConf.disabled" :label-width="formConf.labelWidth + 'px'">
            <draggable class="drawing-board" :list="drawingList" :animation="200" group="componentsGroup" @end="onMianDragEnd" itemKey="vModel">
              <template #item="{ element, index }">
                <draggable-item-bmp
                  :drawing-list="drawingList"
                  :element="element"
                  :index="index"
                  :active-id="activeId"
                  :form-conf="{ unFocusedComponentBorder: formConfig.disabled }"
                  :put="shouldClone"
                  @active-item="activeFormItem"
                  @copy-item="drawingItemCopy"
                  @delete-item="handleDrawingItemDelete"
                ></draggable-item-bmp>
              </template>
            </draggable>
            <div v-show="!drawingList.length" class="empty-info">从左侧拖入或点选组件进行表单设计</div>
          </el-form>
        </el-row>
      </el-scrollbar>
    </div>
    <right-panel-bmp
      :active-data="activeData"
      :form-conf="formConf"
      :show-field="!!drawingList.length"
      :could-change-require="!isProCondition(activeData)"
      @tag-change="tagChange"
      @update-label="handleupdateLabel"
      @updatePlaceholder="handleUpdatePlaceholder"
    ></right-panel-bmp>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, reactive, defineExpose, withDefaults, defineProps } from 'vue';
import { useRouter } from 'vue-router';
import draggable from 'vuedraggable';
import { Delete } from '@element-plus/icons-vue';
import RightPanelBmp from './RightPanelBmp.vue';
import DraggableItemBmp from './DraggableItemBmp.vue';
import { inputComponents, selectComponents, layoutComponents } from './components/generator/config';
import { commonComponents, formConf, trigger, getConfigByTag, copyConfigAsCustom } from './components/generator/config';
import { deepClone } from '@/utils';
import { useFlowStore } from '@/store/modules/flow';
import { ElMessage, ElMessageBox } from 'element-plus';
import { debounce } from '../utils/index';

interface ComponentItem {
  tagIcon: string;
  label: string;
  isHide?: boolean;
  formId?: number;
  renderKey?: string;
  layout?: string;
  vModel?: string;
  placeholder?: string;
  rowType?: string;
  componentName?: string;
  gutter?: number;
  children?: ComponentItem[];
}

interface FormData {
  formRef: string;
  size: string;
  labelPosition: string;
  labelWidth: number;
  gutter: number;
  disabled: boolean;
  span: number;
  formBtns: boolean;
  fields: ComponentItem[];
}

interface DragEndObj {
  from: HTMLElement;
  to: HTMLElement;
  oldIndex: number;
  newIndex: number;
}

interface FormResult {
  formData: FormData;
  target: string;
}

interface GenerateData {
  operationType: string;
  [key: string]: any;
}

interface ConfProps {
  conf: {
    fields?: ComponentItem[];
    [key: string]: any;
  };
}

const props = withDefaults(defineProps<ConfProps>(), {
  conf: () => ({})
});

const drawingList = ref([]);
const formConfig = ref(formConf);
const activeTabName = ref('common');
const activeData = ref<ComponentItem | null>({});
const components = ref<ComponentItem[]>(commonComponents);
const activeId = ref<number | null>(null);

const flowStore = useFlowStore();
const formData = ref<FormData>({
  formRef: 'elForm',
  size: 'small',
  labelPosition: 'right',
  labelWidth: 100,
  gutter: 15,
  disabled: false,
  span: 24,
  formBtns: true,
  fields: []
});

const tempActiveData = ref<ComponentItem | null>(null);

let oldActiveId: number | null = null;

watch(
  activeId,
  (val) => {
    oldActiveId = val;
  },
  { immediate: true }
);

let afterDrawingChange: ((val: any) => void) | null = null;

watch(
  drawingList,
  (val) => {
    if (!val) return;
    if (!afterDrawingChange) {
      afterDrawingChange = debounce(handlerListChange, 400);
    }
    afterDrawingChange(val);
  },
  { deep: true }
);

watch(
  () => props.conf,
  (val) => {
    if (val) {
      if (typeof val === 'object' && val !== null) {
        if (val.fields && val.fields.length !== 0) {
          drawingList.value = JSON.parse(JSON.stringify(val.fields));
          // 重要，请暂时不要删除这里的注释内容
          // Object.assign(formConf, props.conf)
        } else {
          drawingList.value = [];
        }
      }
      activeFormItem(drawingList.value[0]);
    }
  },
  { deep: true }
);
const inputList = ['input', 'textarea', 'number', 'xtsjy', 'phoneIcon', 'idCardIcon'];
const selectList = ['select', 'radio', 'checkbox', 'time', 'date', 'date-range', 'xtzsdl', 'cascader'];
const uploadList = ['upload', 'xtfj', 'xtsjjt', 'xtaudio', 'xtvideo'];

const placeholderValue = computed({
  get: () => activeData.value.placeholder,
  set: (val: string) => {
    let strValue = '';
    console.log('是否允许编辑---', activeData.value?.isPlaceholder, activeData.value.icon);
    if (activeData.value?.isPlaceholder) {
      strValue = activeData.value.placeholder;
    } else {
      if (inputList.includes(activeData.value.icon)) {
        strValue = `请输入${val}`;
      } else if (selectList.includes(activeData.value.icon)) {
        strValue = `请选择${val}`;
      } else if (uploadList.includes(activeData.value.icon)) {
        strValue = `请上传${val}`;
      } else {
        if (!(activeData.value.id && activeData.value.placeholder)) {
          strValue = `${val}`;
        }
      }
      activeData.value.placeholder = strValue;
    }
  }
});
const cloneChildrenOfRowFormItem = (rowFormItem: ComponentItem) => {
  if (rowFormItem.children && rowFormItem.children.length) {
    const children = rowFormItem.children;
    children.forEach((clone, index) => {
      clone.formId = rowFormItem.formId! + index + 1;
      clone.renderKey = clone.formId + new Date().getTime();

      if (!clone.layout) clone.layout = 'colFormItem';

      if (clone.layout === 'colFormItem') {
        clone.vModel = getUUid(8, 16);
        if (clone.placeholder !== undefined) {
          clone.placeholder += clone.label;
        }
      } else if (clone.layout === 'rowFormItem') {
        delete clone.label;
        clone.componentName = `row${clone.formId}`;
        clone.gutter = formConf.gutter;
        cloneChildrenOfRowFormItem(clone);
      }
    });
  }
};
/**
 * 克隆选中的组件
 * @param origin 克隆选中的数据源
 */
const cloneComponent = (origin: ComponentItem) => {
  const clone = JSON.parse(JSON.stringify(origin));
  clone.formId = getNextId();
  clone.renderKey = clone.formId + new Date().getTime();

  if (!clone.layout) clone.layout = 'colFormItem';

  if (clone.layout === 'colFormItem') {
    clone.label = createCmpLabel(clone);
    clone.vModel = getUUid(8, 16);
    if (clone.placeholder !== undefined) {
      clone.placeholder += clone.label;
    }
    tempActiveData.value = clone;
  } else if (clone.layout === 'rowFormItem') {
    if (clone.rowType === 'table') {
      clone.vModel = getUUid(8, 16);
    }
    clone.componentName = `row${clone.formId}`;
    clone.gutter = formConf.gutter;
    cloneChildrenOfRowFormItem(clone);
    tempActiveData.value = clone;
  }
  return tempActiveData.value;
};

/**
 * 拖拽结束后的数据  以下注释的代码之后 可能会使用 不要删除
 * @param obj
 */
const onEnd = (obj: DragEndObj) => {
  // if (obj.from !== obj.to) {
  activeId.value = tempActiveData.value?.formId;
  activeData.value = tempActiveData.value;
  // const clone = cloneComponent(tempActiveData.value);

  // drawingList.value.push(clone);
  // }
};

/**
 * 激活高亮选中的一项
 * @param element 选中的原生
 */
const activeFormItem = (element: ComponentItem) => {
  if (element) {
    activeData.value = element;
    activeId.value = element.formId;
    placeholderValue.value = element.label;
  }
};

/**
 * 绘制页面中选中的组件
 * @param item  当前选中的一项
 */
const drawingItemCopy = (item: ComponentItem) => {
  const clone = deepClone(item);
  clone.formId = getNextId();
  clone.renderKey = clone.formId + new Date().getTime();
  drawingList.value.push(clone);
};
/**
 * 根据选中的数据 处理列表中的数据
 * @param val 当前中间已经绘制的数据
 */
const handlerListChange = (val: any) => {
  flowStore.clearPCondition();
  const canUsedAsPCon = (conf: ComponentItem, parent: ComponentItem | null) => {
    const isRangeCmp = ['fc-date-duration', 'fc-time-duration'].includes(conf.tag || '');
    if (isRangeCmp && !conf.showDuration) return false;
    if (parent && parent.rowType === 'table') return false;
    if (!conf.proCondition || !conf.required) return false;
    if (conf.tag === 'el-select' && conf.multiple) return false;
    return true;
  };

  const loop = (data: ComponentItem | ComponentItem[], parent: ComponentItem | null = null) => {
    if (!data) return;
    if (Array.isArray(data)) {
      data.forEach((item) => loop(item, parent));
    } else {
      if (Array.isArray(data.children)) {
        data.children.forEach((child) => loop(child, data));
      }
      canUsedAsPCon(data, parent) ? flowStore.addPCondition(data) : flowStore.delPCondition(data.formId);
    }
  };

  loop(drawingList.value);
  flowStore.updateFormItemList(drawingList.value);
};
/**
 * 这是不确定是否在使用 暂时注释掉不删除代码
 */
// const isCommonCmp = (name: string) => {
//   return commonComponents.findIndex((t) => t.label === name) > -1;
// };

/**
 * 是否可以选中当前的组件到页面中
 * @param to 目标值
 * @param from 来源值
 * @param target 目标值
 * @param event 当前触发的本身事件
 * @param conf 组件的当前的数据
 */
const shouldClone = (to: any, from: any, target: any, event: any, conf: any) => {
  const targetConf = target._underlying_vm_;
  const isRowContainer = conf.cmpType === 'common' && conf.rowType === 'layout';
  if (isRowContainer) return true;
  if (conf.cmpType === 'custom') return false;
  if (conf.rowType === 'table') {
    if (targetConf.layout === 'rowFormItem') return false;
    if (isFilledPCon([targetConf.formId])) return false;
  }
  return true;
};

/**
 * 右侧输入的值，及时更新到页面中
 * @param val 更新值
 */
const handleupdateLabel = (val: string) => {
  console.log('标题更新计算属性的值--', val);

  if (activeData.value) {
    activeData.value.isPlaceholder = false;
    activeData.value.label = val;
    // 自动更新计算属性中的值
    placeholderValue.value = val;
  }
};
/**
 * 只是用右侧写入的提示语的值
 */
const handleUpdatePlaceholder = (val: string) => {
  console.log('提示语更新计算属性的值--', val);

  if (activeData.value) {
    activeData.value.isPlaceholder = true;
    // 自动更新计算属性中的值
    activeData.value.placeholder = val;
  }
};
/**
 * 拖拽之后的结果
 * @param obj 拖拽绑定的情况值
 */
const onMianDragEnd = (obj: any) => {
  if (obj.from !== obj.to) {
    // 确保拖拽后的数据被正确更新
    nextTick(() => {
      if (drawingList.value[obj.newIndex]) {
        activeFormItem(drawingList.value[obj.newIndex]);
        // 触发列表变化处理
        handlerListChange(drawingList.value);
      }
    });
  }
};
/**
 * 获取相同的组件，并且把label的组件名+1  例如：单行输入框/单行输入框1
 * @param tag 组件
 */
const getSameTagCmpNum = (tag: string) => {
  return drawingList.value.reduce((count, item) => {
    if (item.children) {
      return (
        count +
        item.children.reduce((c, t) => {
          return t.tag === tag ? c + 1 : c;
        }, 0)
      );
    }
    return item.tag === tag ? count + 1 : count;
  }, 0);
};
/**
 * 创建组件名称
 * @param cmp 组件值
 */
const createCmpLabel = (cmp: ComponentItem) => {
  const len = getSameTagCmpNum(cmp.tag || '');
  return len ? `${cmp.label}${len}` : cmp.label;
};
/**
 * 添加组件
 * @param item 当前要添加组件的内容
 */
const addComponent = (item: ComponentItem) => {
  const clone = cloneComponent(item);
  drawingList.value.push(clone);
  activeFormItem(clone);
  // 更新当前控件的提示语内容
  placeholderValue.value = item.label;
  item.placeholder = placeholderValue.value;
};

/**
 * 获取最大编号
 */
const getMaxId = () => {
  if (drawingList.value.length) {
    let maxId = 0;
    const loop = (data: ComponentItem | ComponentItem[]) => {
      if (!data) return;
      if (Array.isArray(data)) {
        data.forEach(loop);
      } else {
        if (Array.isArray(data.children)) {
          data.children.forEach((child) => loop(child));
        }
        maxId = Math.max(data.formId || 0, maxId);
      }
    };
    loop(drawingList.value);
    return maxId;
  }
  return 0;
};
/**
 * 获取下一个编号
 */
const getNextId = () => {
  return getMaxId() + 1;
};
/**
 * 随机生成uuid 作为vModel 的值
 * @param len 长度
 * @param radix 编码方式
 */
const getUUid = (len: number, radix: number = 16) => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const uuid: string[] = [];
  if (len) {
    for (let i = 0; i < len; i++) {
      uuid[i] = chars[Math.floor(Math.random() * radix)];
    }
  }
  return uuid.join('');
};
/**
 * 这个方法 可能暂时没有使用到的地方 之后会使用 不删除代码 只注释代码
 * @param item
 * @param parent
 */
// const handleDrawingItemCopy = (item: ComponentItem, parent: ComponentItem[]) => {
//   const clone = JSON.parse(JSON.stringify(item));
//   clone.formId = getNextId();
//   clone.renderKey = clone.formId + new Date().getTime();
//   if (clone.layout === 'colFormItem') {
//     clone.vModel = `field${clone.formId}`;
//   } else if (clone.layout === 'rowFormItem') {
//     clone.componentName = `row${clone.formId}`;
//   }
//   if (Array.isArray(clone.children)) {
//     clone.children = clone.children.map((childItem) => createIdAndKey(childItem));
//   }
//   parent.push(clone);
//   activeFormItem(clone);
// };

/**
 * 删除选中的元素控件
 * @param index 要删除的角标
 * @param parent 要删除的父级原生，因为可能行布局中会有子布局的数据
 */
const handleDrawingItemDelete = (index: number, parent: ComponentItem[]) => {
  parent.splice(index, 1);
  nextTick(() => {
    const len = drawingList.value.length;
    if (len) {
      activeFormItem(drawingList.value[len - 1]);
    }
  });
};
/**
 * 添加流程节点到表单中
 * @param formIds 表单id
 */
const isFilledPCon = (formIds?: number[]) => {
  const processCmp = parent?.children?.find((t) => t.isProcessCmp);
  return processCmp && processCmp.isFilledPCon(formIds);
};
/**
 * 检查是不是要添加的组件
 * @param cmp ：组件的数据
 */
const checkColItem = (cmp: ComponentItem) => {
  if (!cmp) return false;
  return isFilledPCon([cmp.formId || 0]);
};
/**
 * 如果是必填的流程节点项目 添加到流程审核中的校验
 * @param cmp 当前选中的节点数据
 */
const isProCondition = (cmp: ComponentItem) => {
  if (Array.isArray(cmp?.children) && cmp?.children?.length) {
    if (cmp.rowType === 'table') return false;
    let flag = false;
    const loop = (el: ComponentItem | ComponentItem[]) => {
      if (flag) return;
      if (Array.isArray(el)) {
        el.some((e) => {
          if (e.children) loop(e.children);
          return checkColItem(e);
        }) && (flag = true);
      }
    };
    loop(cmp.children);
    return flag;
  } else {
    return checkColItem(cmp);
  }
};
/**
 * 当前绘制的页面中的列表的数据
 * @param newTag 当前新选择的组件
 * @param list 当前页面中的数据
 */
const updateDrawingList = (newTag: ComponentItem, list: ComponentItem[]) => {
  const index = list.findIndex((item) => item.formId === activeId.value);
  if (index > -1) {
    list.splice(index, 1, newTag);
  } else {
    list.forEach((item) => {
      if (Array.isArray(item.children)) {
        updateDrawingList(newTag, item.children);
      }
    });
  }
};
/**
 * 切换选中的组件数据
 * @param newTag 当前新选择的组件
 */
const tagChange = (newTag: ComponentItem) => {
  const clone = cloneComponent(newTag);
  clone.vModel = activeData.value?.vModel;
  clone.formId = activeId.value;
  clone.span = activeData.value?.span;

  if (activeData.value) {
    delete activeData.value.tag;
    delete activeData.value.tagIcon;

    Object.keys(clone).forEach((key) => {
      if (activeData.value?.[key] !== undefined && typeof activeData.value[key] === typeof clone[key]) {
        clone[key] = activeData.value[key];
      }
    });

    activeData.value = clone;
    updateDrawingList(clone, drawingList.value);
  }
};
/**
 * 获取当前组件的数据，主要是在父组件中调用这个方法
 */
const getData = (): Promise<FormResult> => {
  return new Promise((resolve, reject) => {
    if (drawingList.value.length === 0) {
      resolve({ formData: {}, target: activeTabName.value });
      return;
    }

    if (isEmptyRowContainer()) {
      reject({ msg: '您的行容器中没有组件', target: activeTabName.value });
      return;
    }

    AssembleFormData();
    resolve({ formData: formData.value, target: activeTabName.value });
  });
};
/**
 * 校验当前组件的数据，主要是在父组件中调用这个方法
 */
const getDataNoValidate = (): Promise<FormResult> => {
  return new Promise((resolve) => {
    AssembleFormData();
    resolve({ formData: formData.value, target: activeTabName.value });
  });
};
/**
 * 重置当前组件的数据，主要是在父组件中调用这个方法
 */
const resetForm = () => {
  formData.value = {
    fields: []
  };
};
/**工具函数   这个方法以后会使用，暂时先注释掉 */
// const titleCase = (str: string): string => {
//   return str.charAt(0).toUpperCase() + str.slice(1);
// };

/**
 * 判断行布局中是否为空
 */
const isEmptyRowContainer = (): boolean => {
  const rowContainer = drawingList.value.find((t) => t.layout === 'rowFormItem');
  if (rowContainer) {
    return rowContainer.children?.length === 0;
  }
  return false;
};
/**
 * 拷贝新数据的方法
 */
const AssembleFormData = () => {
  formData.value = {
    ...formConf,
    fields: JSON.parse(JSON.stringify(drawingList.value))
  };
};

defineExpose({
  getData,
  getDataNoValidate,
  resetForm
});
</script>

<style lang="scss">
@import './styles/home.scss';
.el-radio-group {
  line-height: 2.5;
}

.el-date-editor {
  .el-range-separator {
    box-sizing: content-box;
  }
}
.svg-icon {
  float: right;
}
</style>
