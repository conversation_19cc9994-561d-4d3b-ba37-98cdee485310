import { createApp } from 'vue';
// global css
import 'virtual:uno.css';
import '@/assets/styles/index.scss';
import 'element-plus/theme-chalk/dark/css-vars.css';

// App、router、store
import App from './App.vue';
import store from './store';
import router from './router';

// 添加全局未捕获Promise异常处理器
window.addEventListener('unhandledrejection', (event) => {
  console.error('未捕获的Promise异常:', event.reason);
  // 可选：如果需要阻止默认处理（控制台错误），取消事件
  event.preventDefault();
});

// 自定义指令
import directive from './directive';

// 注册插件
import plugins from './plugins/index'; // plugins

// 高亮组件
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
import HighLight from '@highlightjs/vue-plugin';

// svg图标
import 'virtual:svg-icons-register';
import ElementIcons from '@/plugins/svgicon';

// permission control
import './permission';

// vxeTable
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
VXETable.config({
  zIndex: 999999
});

// 修改 el-dialog 默认点击遮照为不关闭
import { ElDialog } from 'element-plus';
ElDialog.props.closeOnClickModal.default = false;

import DataVVue3 from '@kjgl77/datav-vue3';
import registerOption from '@/components/addDataScreen/utils/register-option'; // 引入组件注册文件
import registerCpt from '@/components/addDataScreen/utils/register-cpt'; // 引入组件注册文件
import '@/assets/font/addDataScreen/custom-font.css';
import '@/utils/RegisterMap';
const app = createApp(App);

app.use(registerOption);
app.use(registerCpt);
app.use(HighLight);
app.use(ElementIcons);
app.use(router);
app.use(store);
app.use(VXETable);
app.use(plugins);
app.use(DataVVue3);
// 自定义指令
directive(app);

app.mount('#app');
