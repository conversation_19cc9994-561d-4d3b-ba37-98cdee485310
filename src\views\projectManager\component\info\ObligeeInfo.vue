<!-- 權利人信息權利人 -->
<template>
  <div>
    <div class="obligee-info-contianer" v-for="(item, index) in currentParceItem.obligeeList" :key="item.id">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <div class="obligee-title">
            <span>权利人</span>
            <span>{{ index + 1 }}</span>
          </div>
          <div class="obligee-name-phone">
            <div class="name">
              <span>姓名:</span><span>{{ item.qlrxm }}</span>
            </div>
            <div class="name">
              <span>手机号码:</span><span>{{ item.phone }}</span>
            </div>
          </div>
          <div class="obligee-idcard">
            <span>证件号码:</span><span>{{ item.zjhm }}</span>
          </div>
          <div class="obligee-img">
            <authImg :authSrc="`${baseUrl}/${item.idCardFront}?att=1`" :width="'107px'" :height="'68px'" :radios="'4px'" v-if="item.idCardFront">
            </authImg>
            <div v-if="!item.idCardFront" class="no-img">暂无数据</div>
            <authImg :authSrc="`${baseUrl}/${item.idCardBack}?att=1`" :width="'107px'" :height="'68px'" :radios="'4px'" v-if="item.idCardBack">
            </authImg>
            <div v-if="!item.idCardBack" class="no-img">暂无数据</div>
            <authImg :authSrc="`${baseUrl}/${item.signature}?att=1`" :width="'107px'" :height="'68px'" :radios="'4px'" v-if="item.signature">
            </authImg>
            <div v-if="!item.signature" class="no-img">暂无数据</div>
            <authImg :authSrc="`${baseUrl}/${item.fingerprint}?att=1`" :width="'107px'" :height="'68px'" :radios="'4px'" v-if="item.fingerprint">
            </authImg>
            <div v-if="!item.fingerprint" class="no-img">暂无数据</div>
          </div>
          <div class="obligee-detial" @click="checkDetail(item)">
            <span>查看详情<i class="el-icon-right"></i></span>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 权利人弹窗 -->
    <el-dialog
      :title="checdQRL.qlrxm"
      v-model="dialogVisible"
      width="864px"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <div class="dialog-span" style="margin-top: 0px">个人信息</div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">权利人类型</div>
            <div class="dialog-item-content">{{ checdQRL.qlrlx }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">姓名</div>
            <div class="dialog-item-content">{{ checdQRL.qlrxm }}</div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">手机号</div>
            <div class="dialog-item-content">{{ checdQRL.phone }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">性别</div>
            <div class="dialog-item-content">{{ checdQRL.qlrxb === '1' ? '男' : '女' }}</div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">出生日期</div>
            <div class="dialog-item-content">{{ formatDateType(checdQRL.csrq) }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">民族</div>
            <div class="dialog-item-content">{{ checdQRL.mz }}</div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">地址</div>
            <div class="dialog-item-content">{{ checdQRL.qlrdz }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">婚姻状况</div>
            <div class="dialog-item-content">{{ checdQRL.hyqk }}</div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">是否是本村人员</div>
            <div class="dialog-item-content">{{ checdQRL.cyqk }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">家庭人口</div>
            <div class="dialog-item-content">{{ checdQRL.jtrk }}</div>
          </div>
        </div>
        <div class="dialog-span">证件信息</div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">证件类型</div>
            <div class="dialog-item-content">{{ checdQRL.zjlx }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">证件号码</div>
            <div class="dialog-item-content">{{ checdQRL.zjhm }}</div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">有效期限</div>
            <div class="dialog-item-content">{{ checdQRL.yxq }}</div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">签发机关</div>
            <div class="dialog-item-content">{{ checdQRL.qfjg }}</div>
          </div>
        </div>
        <div class="dialog-span">附件</div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">身份证正面</div>
            <div class="dialog-item-content">
              <authImg
                v-if="checdQRL.idCardFront"
                :authSrc="`${baseUrl}/${checdQRL.idCardFront}?att=1`"
                :width="'120px'"
                :height="'76px'"
                :radios="'0px'"
              >
              </authImg>
              <div v-else class="no-img">暂无数据</div>
            </div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">身份证反面</div>
            <div class="dialog-item-content">
              <authImg
                v-if="checdQRL.idCardBack"
                :authSrc="`${baseUrl}/${checdQRL.idCardBack}?att=1`"
                :width="'120px'"
                :height="'76px'"
                :radios="'0px'"
              >
              </authImg>
              <div v-else class="no-img">暂无数据</div>
            </div>
          </div>
        </div>
        <div class="dialog-row">
          <div class="dialog-item">
            <div class="dialog-item-label">签名</div>
            <div class="dialog-item-content">
              <authImg
                v-if="checdQRL.signature"
                :authSrc="`${baseUrl}/${checdQRL.signature}?att=1`"
                :width="'120px'"
                :height="'76px'"
                :radios="'0px'"
              >
              </authImg>
              <div v-else class="no-img">暂无数据</div>
            </div>
          </div>
          <div class="dialog-item">
            <div class="dialog-item-label">签章</div>
            <div class="dialog-item-content">
              <authImg
                v-if="checdQRL.fingerprint"
                :authSrc="`${baseUrl}/${checdQRL.fingerprint}?att=1`"
                :width="'120px'"
                :height="'76px'"
                :radios="'0px'"
              >
              </authImg>
              <div v-else class="no-img">暂无数据</div>
            </div>
          </div>
        </div>
        <div class="dialog-span">家庭成员</div>
        <div v-for="(item, index) in checdQRL.memberList" :key="index">
          <div class="dialog-small-span">人员{{ index + 1 }}</div>
          <div class="dialog-row">
            <div class="dialog-item">
              <div class="dialog-item-label">姓名</div>
              <div class="dialog-item-content">{{ item.qlrxm }}</div>
            </div>
            <div class="dialog-item">
              <div class="dialog-item-label">性别</div>
              <div class="dialog-item-content">{{ item.qlrxb === 1 ? '男' : '女' }}</div>
            </div>
          </div>
          <div class="dialog-row">
            <div class="dialog-item">
              <div class="dialog-item-label">与权利人关系</div>
              <div class="dialog-item-content">{{ item.relation ? item.relation : '--' }}</div>
            </div>
            <div class="dialog-item">
              <div class="dialog-item-label">身份证号码</div>
              <div class="dialog-item-content">{{ item.zjhm }}</div>
            </div>
          </div>
          <div class="dialog-row">
            <div class="dialog-item">
              <div class="dialog-item-label">地址</div>
              <div class="dialog-item-content">{{ item.qlrdz }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import authImg from '../../../../components/authImg/index.vue';

interface Member {
  qlrxm: string;
  qlrxb: string | number;
  relation?: string;
  zjhm: string;
  qlrdz: string;
}

interface Obligee {
  id: string | number;
  qlrxm: string;
  phone: string;
  zjhm: string;
  idCardFront?: string;
  idCardBack?: string;
  signature?: string;
  fingerprint?: string;
  qlrlx: string;
  qlrxb: string;
  csrq: string;
  mz: string;
  qlrdz: string;
  hyqk: string;
  cyqk: string;
  jtrk: string;
  zjlx: string;
  yxq: string;
  qfjg: string;
  parentId: string | number;
  fid: string;
  memberList: Member[];
}

interface Props {
  parceInfoItem: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  parceInfoItem: () => ({})
});

const store = useProjectStore();
const baseUrl = import.meta.env.VUE_APP_BASE_API + '/qjt/file/downloadone';
const dialogVisible = ref(false);
const checdQRL = ref<Obligee>({} as Obligee);

const currentParceItem = computed(() => {
  const parcelInfo = store.parcelInfo;
  const list = JSON.parse(JSON.stringify(parcelInfo.obligeeList));
  const zdId = store.parcelInfo.id;
  const endList: Obligee[] = [];
  const itemList: Obligee[] = [];

  list.forEach((v: Obligee) => {
    if (v.parentId === zdId) {
      endList.push(v);
    } else {
      itemList.push(v);
    }
  });

  endList.forEach((v: Obligee) => {
    const memberList: Member[] = [];
    itemList.forEach((k: Obligee) => {
      if (k.fid === v.zjhm) {
        memberList.push(k as Member);
      }
    });
    v.memberList = memberList;
  });

  parcelInfo.obligeeList = endList;
  return parcelInfo;
});

// 查看权利人详情
const checkDetail = (item: Obligee) => {
  dialogVisible.value = true;
  checdQRL.value = item;
};

// 格式化日期
const formatDateType = (date: string) => {
  if (!date) return '--';
  return date;
};
</script>

<style lang="scss" scoped>
.no-img {
  width: 107px;
  height: 68px;
  border-radius: 6px;
  background: rgba(248, 248, 248, 0.1);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: dashed 1px rgb(255, 255, 255, 0.5);
  margin-top: 5px;
  font-size: 12px;
}
.obligee-info-contianer {
  width: 483px;
  height: auto;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  margin: 0 12px 12px 12px;
  display: flex;
  .obligee-title {
    width: 100px;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 17px;
    padding-top: 12px;
    padding-left: 12px;
  }
  .obligee-name-phone {
    display: flex;
    justify-content: space-between;
    width: 483px;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 17px;
    margin-top: 12px;
    padding-left: 12px;
    margin-bottom: 10px;
    .name {
      width: 50%;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      margin-top: 12px;
    }
  }
  .obligee-idcard {
    width: 483px;
    height: 20px;
    font-size: 14px;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 20px;
    padding-top: 12px;
    padding-left: 12px;
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .obligee-img {
    display: grid;
    grid-gap: 1px;
    grid-template-columns: auto auto auto auto;
    margin-top: 20px;
    margin-left: 10px;
  }
  .obligee-detial {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    height: 17px;
    font-size: 12px;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: var(--current-color);
    line-height: 17px;
    padding-right: 20px;
    margin-bottom: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
}
.dialog-content {
  height: 700px;
  overflow: auto;
}
.dialog-span {
  font-size: 14px;
  color: #161d26;
  font-weight: 600;
  margin-bottom: 12px;
  margin-top: 24px;
}
.dialog-row {
  margin-bottom: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .dialog-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    .dialog-item-label {
      font-size: 14px;
      width: 128px;
      color: #8291a9;
    }
    .dialog-item-content {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      .no-img {
        width: 120px;
        height: 76px;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.1);
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}
</style>
