<!-- 详情 -->
<template>
  <div class="detail-main">
    <div class="handle-title">
      <span style="cursor: pointer" @click="goBack">返回</span>
    </div>
    <div class="handle-search">
      <div class="item">
        <div class="label">项目名称</div>
        <div class="right">
          <el-select v-model="search.moduleName" placeholder="请选择项目">
            <el-option v-for="item in moduleList" :key="item.id" :label="item.moduleName" :value="item.id"> </el-option>
          </el-select>
        </div>
      </div>
      <div class="item">
        <div class="label">小区名称</div>
        <div class="right">
          <el-input readonly v-model="search.areaName" placeholder="请输入小区名称"></el-input>
        </div>
      </div>
      <div class="item">
        <div class="label">日期</div>
        <div class="right">
          <el-date-picker readonly v-model="search.rangMonth" type="month" placeholder="选择月" value-format="timestamp"> </el-date-picker>
        </div>
      </div>
      <div class="item"></div>
      <div class="item"></div>
    </div>
    <el-table :data="tableData" height="400px" style="width: 100%" ref="multipleTable" v-loading="loading" border>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="被征收人姓名" prop="qlrName"></el-table-column>
      <el-table-column label="金额" prop="ZFJE"></el-table-column>
      <el-table-column label="发放状态">
        <template #default="scope">{{ scope.row.status === 1 ? '已发放' : '未发放' }}</template>
      </el-table-column>
    </el-table>
    <div class="jf-box">
      <div class="fj-title">附件信息</div>
      <div class="fj-content">
        <div class="fj-item" v-for="(item, index) in FJList" :key="index">
          <div>
            <el-icon><Paperclip /></el-icon>
            <span style="font-size: 12px; margin-left: 16px">{{ item.FJ_1 }}</span>
          </div>
          <div>
            <el-link type="success" @click="showFjDialog(item)">预览</el-link>
            <el-link type="primary" style="margin-left: 40px; margin-right: 20px" @click="handleDownload(item)">下载</el-link>
          </div>
        </div>
      </div>
    </div>
    <!-- 查看附件 -->
    <el-dialog title="查看" v-model="fjDialog" :before-close="handleCloseFj" :close-on-click-modal="false" width="90%">
      <div v-show="fileType === 1" style="width: 100%" :style="{ height: windowHeight }" v-html="vHtml"></div>
      <div v-show="fileType === 2">
        <el-table
          :data="excelData"
          border
          stripe
          style="width: 100%; overflow: auto"
          :style="{ height: windowHeight }"
          :header-cell-style="{ background: '#F5F4F7' }"
        >
          <el-table-column type="index" label="序号" width="60" :resizable="false" align="center" />
          <el-table-column v-for="(value, key, index) in excelData[0]" :key="index" :prop="key" :label="key" />
        </el-table>
      </div>
      <div v-show="fileType === 3" style="width: 100%">
        <iframe :src="fjUrl" frameborder="0" width="100%" :height="windowHeight"></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, inject } from 'vue';
import { getModuleList, getPlaceList } from '@/api/modal';
import axios from 'axios';
import { getToken } from '@/utils/auth';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { downLoadFile } from '@/utils/publicFun';
import { ElLoading, ElMessage } from 'element-plus';
import { Paperclip } from '@element-plus/icons-vue';

interface SearchForm {
  moduleName: string | number;
  areaName: string;
  rangMonth: string | number;
}

interface ModuleItem {
  id: number;
  moduleName: string;
}

interface TableItem {
  status: number;
  qlrName?: string;
  ZFJE?: string | number;
  [key: string]: any;
}

interface FileItem {
  FJ_0?: string;
  FJ_1?: string;
  url?: string;
  type?: string;
  [key: string]: any;
}

interface ApiResponse {
  code: number;
  data: {
    list: Array<any>;
  };
  msg?: string;
}

interface BatchMsg {
  xmmc: string;
  xqmc: string;
  rq: string | number;
  FJ: FileItem[];
  batch: string;
}

const search = reactive<SearchForm>({
  moduleName: '',
  areaName: '',
  rangMonth: ''
});

const tableData = ref<TableItem[]>([]);
const moduleList = ref<ModuleItem[]>([]);
const loading = ref(false);
const batch = ref('');
const FJList = ref<FileItem[]>([]);
const windowHeight = ref('600px');
const fjDialog = ref(false);
const fjUrl = ref('');
const fileType = ref(1);
const vHtml = ref('');
const workbook = ref<XLSX.WorkBook>({} as XLSX.WorkBook);
const excelData = ref<Record<string, any>[]>([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API ? import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/' : '';
const multipleTable = ref();

// 注入父组件提供的方法
const changeType = inject<(type: number, obj?: any) => void>('changeType');
const getBtchMsg = inject<() => BatchMsg>('getBtchMsg');

const getData = () => {
  if (!getBtchMsg) {
    console.error('无法访问父组件方法 getBtchMsg');
    return;
  }

  const btchMsg: BatchMsg = getBtchMsg();
  search.moduleName = btchMsg.xmmc;
  search.areaName = btchMsg.xqmc;
  search.rangMonth = btchMsg.rq;
  FJList.value = btchMsg.FJ || [];

  getModuleList([1] as unknown as string[]).then(async (res: ApiResponse) => {
    if (res.code === 200) {
      moduleList.value = res.data.list;
      for (let i = 0; i < moduleList.value.length; i++) {
        if (moduleList.value[i].moduleName === btchMsg.xmmc) {
          search.moduleName = moduleList.value[i].id;
          break;
        }
      }
      // 如果没有查到项目名字 默认选择第一个
      if (!search.moduleName) {
        search.moduleName = moduleList.value[0]?.id || '';
      }

      // 根据模块id 配合查询条件查询对应拆迁数据 月份
      const params = {
        groupNames: ['权利人', '过渡费支付'],
        areaCode: '',
        ifCheck: false,
        pageNum: 1,
        pageSize: 100000,
        ruleName: '被征收户',
        conditionFields: [
          {
            groupName: '过渡费支付',
            name: 'batch',
            operator: '=',
            relation: 'and',
            type: 1,
            linkType: 2,
            value: [btchMsg.batch],
            top: false
          }
        ]
      };

      loading.value = true;
      getPlaceList(params).then((res: ApiResponse) => {
        loading.value = false;
        if (res.code === 200) {
          tableData.value = [];
          // 组装 权利人和超期过渡费支付账单数据
          res.data.list.forEach((v) => {
            const obj: TableItem = {
              status: 1 // 已发放
            };
            v.fieldInstanceModels.forEach((k: any) => {
              if (k.groupName === '权利人') {
                obj.qlrName = k.attribution.QLRXX_0;
              }
              if (k.groupName === '过渡费支付' && k.attribution.batch === btchMsg.batch) {
                obj.ZFJE = k.attribution.ZFJE;
              }
            });
            tableData.value.push(obj);
          });
        }
      });
    }
  });
};

const goBack = () => {
  if (changeType) {
    changeType(1);
  } else {
    console.error('无法访问父组件方法 changeType');
  }
};

// 查看附件dialog
const showFjDialog = (obj: FileItem) => {
  if (!obj.FJ_1) return;

  const index = obj.FJ_1.indexOf('.');
  obj.type = obj.FJ_1.substring(index + 1, obj.FJ_1.length);

  const loadingInstance = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  windowHeight.value = window.innerHeight - 150 + 'px';

  if (obj.type === 'txt' || obj.type === 'pdf') {
    axios({
      method: 'get',
      url: `${baseUrl}${obj.FJ_0}?att=1`,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    }).then((res) => {
      let type = '';
      switch (obj.type) {
        case 'txt':
          type = 'text/plain';
          break;
        case 'xls':
          type = 'application/vnd.ms-excel';
          break;
        case 'xlsx':
          type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'doc':
          type = 'application/msword';
          break;
        case 'docx':
          type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'pdf':
          type = 'application/pdf';
          break;
        default:
          break;
      }
      loadingInstance.close();
      fileType.value = 3;
      const blob = new Blob([res.data], { type: type });
      fjUrl.value = `${window.URL.createObjectURL(blob)}`;
      fjDialog.value = true;
    });
  } else {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url || obj.FJ_0}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = function () {
      if (xhr.status === 200) {
        if (obj.type === 'doc' || obj.type === 'docx') {
          // 使用Uint8Array的buffer属性获取底层的ArrayBuffer
          mammoth.convertToHtml({ arrayBuffer: xhr.response }).then(function (resultObject) {
            nextTick(() => {
              fileType.value = 1;
              loadingInstance.close();
              fjDialog.value = true;
              vHtml.value = resultObject.value;
            });
          });
        } else {
          fileType.value = 2;
          const data = new Uint8Array(xhr.response);
          try {
            const xWorkbook = XLSX.read(data, { type: 'array' });
            loadingInstance.close();
            const sheetNames = xWorkbook.SheetNames; // 工作表名称集合
            workbook.value = xWorkbook;
            getTable(sheetNames[0]);
          } catch (error) {
            loadingInstance.close();
            ElMessage.error('文件存在异常，无法打开');
          }
        }
      }
    };
    xhr.send();
  }
};

const getTable = (sheetName: string) => {
  const worksheet = workbook.value.Sheets[sheetName];
  excelData.value = XLSX.utils.sheet_to_json(worksheet);
  fjDialog.value = true;
};

const handleCloseFj = () => {
  fjDialog.value = false;
};

const handleDownload = (file: FileItem) => {
  if (file.FJ_0 && file.FJ_1) {
    downLoadFile(file.FJ_0, file.FJ_1);
  }
};

onMounted(() => {
  getData();
});

defineExpose({
  multipleTable
});
</script>

<style lang="scss" scoped>
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
}
.fj-row :hover {
  color: #409eff;
}
.detail-main {
  width: 100%;
  height: 100%;
  .handle-title {
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: rgba(0, 0, 0, 0.1) solid 1px;
  }
  .handle-search {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 10px;
    .item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }
      .right {
        flex: 1;
      }
    }
  }
  .jf-box {
    width: 100%;
    height: calc(100% - 500px);
    border: rgba(0, 0, 0, 0.1) solid 1px;
    margin: 10px 0px;
    display: flex;
    flex-direction: column;
    .fj-title {
      padding: 6px 10px;
      color: #409eff;
      background: rgba(0, 0, 0, 0.1);
    }
    .fj-content {
      flex: 1;
      padding: 20px 10px 10px 10px;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .fj-item {
        height: 40px;
        margin-bottom: 10px;
        border-radius: 4px;
        padding: 0px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: rgba(242, 245, 250, 1);
      }
    }
  }
}
</style>
