<!-- 评论列表 -->
<template>
  <div class="forum-content-contianer">
    <el-row v-for="item in props.commentsList" :key="item.id">
      <el-col :span="24" :xs="24">
        <!-- Original Comment/Reply Display Logic -->
        <div
          class="people-info"
          v-if="item.createBy && item.createTime && !(item.quoteName && item.quoteCreateTime)"
          @mouseenter="hoveredCommentId = item.id"
          @mouseleave="hoveredCommentId = null"
        >
          <auth-img class="img" :authSrc="`${baseUrl}${item.avatar}?att=1`" :width="'32px'" :height="'32px'" :radios="'50%'"></auth-img>
          <div class="title-info">
            <div class="name">
              <span>{{ item.createBy }}:</span>
              <span>{{ item.contents }}</span>
            </div>
            <div class="time">
              <span>{{ formatDateYmdhm(item.createTime) }}</span>
              <span class="reply-text" v-show="hoveredCommentId === item.id">回复</span>
            </div>
          </div>
        </div>

        <!-- Quoted Reply Display Logic -->
        <div
          class="people-info"
          v-if="item.createBy && item.quoteName && item.quoteCreateTime"
          @mouseenter="hoveredCommentId = item.id"
          @mouseleave="hoveredCommentId = null"
        >
          <!-- Assuming avatar is for the replier (item.createBy) -->
          <auth-img class="img" :authSrc="`${baseUrl}${item.avatar}?att=1`" :width="'32px'" :height="'32px'" :radios="'50%'"></auth-img>
          <div class="title-info">
            <div class="name">
              <span style="color: var(--current-color)">{{ item.createBy }}:</span>
              <span> 回复 </span>
              <span style="color: var(--current-color)">{{ item.quoteName }}:</span>
              <span>{{ item.contents }}</span>
            </div>
            <div class="time">
              <span>{{ formatDateYmdhm(item.createTime) }}</span>
              <span class="reply-text" v-show="hoveredCommentId === item.id">回复</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { PropType } from 'vue';
import authImg from '../../components/authImg/index.vue';

// Define basic interface for comment items (replace with actual structure if available)
interface CommentItem {
  id: string | number;
  createBy?: string;
  createTime?: string | number | Date;
  avatar?: string;
  contents?: string;
  quoteName?: string;
  quoteCreateTime?: string | number | Date;
  // Add other properties as needed
}

// Props definition
const props = defineProps({
  commentsList: {
    type: Array as PropType<CommentItem[]>,
    default: () => []
  }
});

// State
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/');
const hoveredCommentId = ref<string | number | null>(null);

// Utility function to format date (replace with a robust library like date-fns or dayjs if needed)
const formatDateYmdhm = (dateInput: string | number | Date | undefined): string => {
  if (!dateInput) return '';
  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return String(dateInput); // Return original if invalid date

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('Error formatting date:', dateInput, error);
    return String(dateInput); // Return original on error
  }
};
</script>

<style lang="scss" scoped>
.forum-content-contianer {
  display: flex;
  // justify-content: flex-start; // Not needed with flex-direction column
  // align-content: center; // Not needed with flex-direction column
  flex-direction: column;
  margin-top: 10px;
  // Removed bottom margin and border as it should likely be on the row or handled by parent

  .el-row {
    border-bottom: 1px solid #dbe7ee; // Apply border to each row instead
    padding-bottom: 10px; // Add padding below each comment row
    margin-bottom: 10px; // Add margin between comment rows
    &:last-child {
      border-bottom: none; // No border on the last item
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  .people-info {
    display: flex;
    // flex: 1; // Not needed here
    // justify-content: flex-start; // Default for flex
    align-items: flex-start; // Align items to top for multiline comments
    // height: 40px; // Allow height to adjust to content
    min-height: 40px;
    width: 100%;
    cursor: pointer;
    position: relative; // Needed for absolute positioning of reply text

    .img {
      flex-shrink: 0; // Prevent image from shrinking
      margin-right: 10px; // Add margin instead of using margin on title-info
    }

    .title-info {
      // height: 40px; // Allow height to adjust
      width: 100%;
      display: flex;
      flex-direction: column;
      // margin-left: 10px; // Handled by margin-right on img
      .name {
        // width: 100%;
        // height: auto; // Adjust height based on content
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #161d26;
        line-height: 1.4; // Improve line spacing for multiline
        margin-bottom: 4px; // Space between name/content and time
        word-break: break-word; // Allow long words to break

        span {
          vertical-align: middle; // Align spans better if needed
        }

        .sys-type {
          // Kept original styles, seems unused in current template
          width: 60px;
          height: 17px;
          font-size: 12px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          line-height: 17px;
          padding-left: 12px;
        }
      }

      .time {
        // width: 100%;
        // height: 20px;
        font-size: 12px; // Made time smaller
        font-family:
          PingFang SC-Regular,
          PingFang SC; // Regular weight for time
        font-weight: 400;
        color: #909399; // Grey color for time
        line-height: 1.4;
        display: flex; // Use flex to position reply text
        justify-content: space-between; // Pushes time left, reply right
        align-items: center;

        .reply-text {
          color: var(--current-color, #409eff); // Use CSS var or fallback
          font-weight: 500; // Make reply text bolder
          // Removed absolute positioning and width styles
          // display: inline-block; // Ensure it takes space
          padding: 0 5px; // Add some padding
          cursor: pointer;
        }
      }
    }

    .more-info {
      // Kept original styles, seems unused
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
  }
}
</style>
