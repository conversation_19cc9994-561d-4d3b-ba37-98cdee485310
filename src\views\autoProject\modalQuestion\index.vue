<!-- 新建问卷调查内容 -->
<template>
  <div class="modalQuestion-main" v-loading.fullscreen.lock="fullscreenLoading" :class="$attrs.class" v-bind="$attrs">
    <div class="main-con flex flex-col" v-show="!addQuestionDialog">
      <div class="handle flex-none">
        <div class="flex items-center">
          <el-button type="primary" @click="shaixuan">筛选</el-button>
          <el-button type="primary" @click="addNewQuestion">新增</el-button>
          <el-button type="danger" @click="delQuestion">删除</el-button>
          <el-button type="success" @click="batchReport">批量导入</el-button>
          <el-dropdown @command="handleCommand">
            <el-button type="warning" style="margin-left: 10px">
              导出<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="beforeHandleCommand(item)" v-for="(item, index) in exportBtn" :key="index" :disabled="!item.id">{{
                  item.exportName
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-checkbox v-model="isChecked" style="margin-left: 10px" v-if="user['userName'] == '18285070490'">检测</el-checkbox>
        </div>
        <el-button type="primary" @click="jumpScreen">数据大屏</el-button>
      </div>
      <div class="down-all flex-none">
        <el-checkbox v-model="checkAllData">全选所有数据</el-checkbox>
      </div>
      <div class="content flex-auto overflow-y-auto" id="main-table-js">
        <el-table
          :data="tableData"
          class="table-class"
          :row-class-name="tableRowClassName"
          ref="multipleTable"
          row-key="id"
          :key="tableKey"
          v-model:selection="multipleSelection"
          @select-all="handleSelectionChange"
          @select="handleSelectOne"
          v-loading="loading"
          border
          :height="mainTableHeight"
        >
          <el-table-column fixed type="selection" reserve-selection width="55" />
          <el-table-column label="序号" fixed>
            <template #default="scope">
              {{ scope.row.index }}
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in questionList" :key="index" :fixed="item.isLock" min-width="240">
            <template #header>
              {{ item.fieldCn }}
              <span style="color: red" v-show="item.required == 1">*</span>
              <el-tooltip effect="dark" content="解除固定" placement="top-start">
                <el-icon class="red" v-show="item.isLock" @click="changeLock(item, 1)"><lock /></el-icon>
              </el-tooltip>
              <el-tooltip effect="dark" content="固定在左侧" placement="top-start">
                <el-icon class="blue" v-show="!item.isLock" @click="changeLock(item, 2)"><unlock /></el-icon>
              </el-tooltip>
            </template>
            <template #default="scope">
              <!-- 附件 -->
              <template v-if="item.valueMethod == 'xtfj'">
                <div v-for="(ite, idx) in getFJ(scope.row.fieldInstanceModels[0].attribution[item.fieldName], item)" :key="idx" class="fj-box">
                  <div class="fj-item" @click="showFjDialog(ite)">
                    <el-icon><document /></el-icon>
                    <span style="margin-left: 5px">{{ spanRuleCenter(ite.title, 18) }}</span>
                  </div>
                </div>
              </template>
              <!-- 图片 -->
              <template v-else-if="item.valueMethod == 'upload'">
                <div v-for="(ite, idx) in scope.row.fieldInstanceModels[0].attribution[item.fieldName]" :key="idx" class="img-box">
                  <el-image
                    v-if="ite.url"
                    class="img-one"
                    :src="`${base}/qjt/file/otherDownload/${ite.url}?token=${token}`"
                    :fit="`cover`"
                    :preview-src-list="getPreview(scope.row.fieldInstanceModels[0].attribution[item.fieldName])"
                    :preview-teleported="true"
                  >
                  </el-image>
                </div>
              </template>
              <!-- 其他 -->
              <template v-else>
                {{ scope.row.fieldInstanceModels[0].attribution[item.fieldName] }}
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="100">
            <template #default="scope">
              <el-link type="primary" @click="editRow(scope.row)" style="margin-left: 10px">编辑</el-link>
              <el-link type="success" @click="saveOne(scope.row)" v-show="scope.row.isEdit" style="margin-left: 10px">保存</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page flex-none">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:current-page="dialogSearch.pageNum"
          v-model:page-size="dialogSearch.pageSize"
          :page-sizes="[20, 50, 100, 200, 500]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
    <!-- 新增问卷 -->
    <addQuestionComponent
      v-show="addQuestionDialog"
      @closeAddQuestion="closeAddQuestion"
      @saveOne="saveOne"
      :questionOne="questionOne"
      :editQuestionType="String(editQuestionType)"
      :questionOneRule="questionOneRule"
      :questionList="questionList"
    />
  </div>
  <!-- 公共导出 -->
  <el-dialog
    :title="nowDownMsg.exportName"
    v-model="publicDownDialoig"
    width="874px"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :append-to-body="true"
    @close="handleClose"
  >
    <el-form :model="downLoadMsg" :rules="downLoadMsgRule" ref="downLoadRef" class="demo-ruleForm" label-position="top">
      <el-form-item label="选择坐标系统" :prop="nowDownMsg.coordinate.must == 1 ? 'onWkid' : ''" v-show="nowDownMsg.coordinate.disable == 0">
        <el-cascader
          v-model="downLoadMsg.onWkid"
          :options="wkidList"
          :show-all-levels="false"
          style="width: 100%"
          :placeholder="nowDownMsg.coordinate.placeholder"
        />
      </el-form-item>
      <el-form-item label="数据列表" :prop="nowDownMsg.dataList.must == 1 ? 'zdList' : ''">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 6 }"
          :placeholder="nowDownMsg.dataList.placeholder"
          v-model="downLoadMsg.zdListNames"
          readonly
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitDown">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 进度条弹窗 -->
  <el-dialog
    title="数据下载中"
    v-model="progressDialog"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    @close="handleCloseProgress"
    width="300px"
  >
    <div class="down-dialog">
      <el-progress :stroke-width="16" type="circle" :percentage="progress" :status="downStatus as any" />
      <div style="margin-top: 10px">{{ downMsg }}</div>
    </div>
  </el-dialog>
  <!-- 二维码 -->
  <el-dialog :title="ewmTitle" v-model="erwDialog" width="600px" :close-on-click-modal="false" @close="handleCloseEWM">
    <div class="handle-row">
      <div class="row">大小：<el-input type="number" class="row-input" size="small" v-model="QRImgUrlW" /></div>
    </div>
    <img :src="QRImgUrl" />
  </el-dialog>
  <!-- 筛选弹窗 -->
  <dataSearch
    @handleCloseShaixuan="handleCloseShaixuan"
    @submitSearch="submitSearch"
    :dialogSearch="dialogSearch"
    :shaixuanDialog="shaixuanDialog"
    @clearUser="clearUser"
    :taskList="taskList"
    @handleResetSearch="handleResetSearch"
    @editCondition="editCondition"
    :isQuestion="true"
  />
  <!-- 批量导入 -->
  <batchReportDialog :batchDialog="batchDialog" @changeBatchDialog="changeBatchDialog" @submitUploadTemp="submitUploadTemp" />
  <!-- 数据上传进度 -->
  <el-dialog
    title="数据导入中"
    v-model="uploadLodingDialog"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="300px"
  >
    <div class="down-dialog">
      <el-progress :stroke-width="16" type="circle" :percentage="uploadProgress" :status="uploadStatus as any" />
      <div style="margin-top: 10px">{{ uploadMsg }}</div>
    </div>
  </el-dialog>

  <!-- 查看附件 -->
  <el-dialog title="查看" v-model="fjDialog" @close="handleCloseFj" :close-on-click-modal="false" width="90%">
    <div v-show="fileType == 1" style="width: 100%" :style="{ height: windowHeight }" v-html="vHtml"></div>
    <div v-show="fileType == 2">
      <el-table
        :data="excelData"
        border
        stripe
        style="width: 100%; overflow: auto"
        :style="{ height: windowHeight }"
        :header-cell-style="{ background: '#F5F4F7' }"
      >
        <el-table-column type="index" label="序号" width="60" :resizable="false" align="center" />
        <el-table-column v-for="(key, index) in excelData[0]" :key="index" :prop="key" :label="key" />
      </el-table>
    </div>
    <div v-show="fileType == 3" style="width: 100%">
      <iframe :src="fjUrl" frameborder="0" width="100%" :height="windowHeight"></iframe>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({
  inheritAttrs: false
});

import { selectRules, getPlaceList, exportSettingList, downLoadPublic, getExportDetail, downLoadPublicCheck } from '@/api/modal';
import { saveSimple, downLoadQuestion, findAsyncFile, findAsyncMsg as findAsyncMsgApi, downloadTemp, downDefaultTemp } from '@/api/project';
import { getSearchTask } from '@/api/task';
import { copyWord, isValidDateFormat, isValidYearFormat, isValidMonthFormat, deWeight, isArray } from '@/utils/validate';
import { listUser } from '@/api/system/user';
import { getScreenList } from '@/api/dataScreen';
import dataSearch from '@/components/dataSearch/index.vue';
import batchReportDialog from './batchReportDialog.vue';
import addQuestionComponent from './addQuestion.vue';
import Axios from 'axios';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { getToken } from '@/utils/auth';
import { ElMessage, ElMessageBox, ElLoading, ElNotification } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { useProjectStore } from '@/store/modules/project';
import { useRoute } from 'vue-router';
import { spanRuleCenter } from '@/utils/filters';
import { previewFile } from '@/api/esign';
const { proxy } = getCurrentInstance();

const userStore = useUserStore();
const projectStore = useProjectStore();
const route = useRoute();

// 定义变量部分

const tableKey = ref(Date.now()); // 表格 key
const questionList = ref([]); // 问卷内容
const fieldInstanceModels = ref([]); // 提交的数据 或反显的数据
const fieldGroupModel = ref({}); // 属性组内容
const moduleId = ref(0); // 模块 ID
const exportBtn = ref([]); // 导出按钮
const shpGdbTree = ref({ shp: [], gdb: [], pic: [], word: [], excel: [] }); // 导出的树结构迭代 整理出来shp和gdb列表
const nowDownMsg = ref({
  exportName: undefined,
  // 选中的导出设置内容
  coordinate: {
    placeholder: '',
    must: 1,
    disable: undefined,
    defaultValue: undefined
  },
  dataList: {
    placeholder: '',
    must: undefined
  },
  detail: {
    fileName: ''
  },
  id: undefined,
  moduleId: undefined
});
const downLoadFileName = ref(''); // 导出的文件名
const downLoadMsg = ref({ zdList: [], onWkid: undefined, zdListNames: undefined }); // 导出设置
const downLoadMsgRule = ref({}); // 导出设置校验
const publicDownDialoig = ref(false); // 公共导出弹窗
const wkidList = ref([
  // WKID 坐标系列表
  {
    value: 'CGCS_2000',
    label: 'CGCS_2000',
    children: [
      {
        value: 'cgcs2000_3',
        label: '三度带',
        children: []
      },
      {
        value: 'cgcs2000_6',
        label: '六度带',
        children: []
      }
    ]
  }
]);
const progressDialog = ref(false); // 进度条弹窗
const progress = ref(0); // 导出进度
const QRImgUrl = ref(''); // 二维码图片
const erwDialog = ref(false); // 二维码弹窗
const ewmTitle = ref(''); // 二维码标题
const QRImgUrlW = ref(128); // 二维码图片宽度

const base = import.meta.env.VITE_APP_BASE_API; // 接口地址
const baseUrl = import.meta.env.VITE_APP_URL_BASE; // 接口地址

const dialogSearch: any = ref({
  // 弹窗筛选
  areaCode: '', // 行政区划
  createDate: '', // 创建时间
  updateDate: '', // 最后修改时间
  createUserId: '', // 采集人
  updateUserId: '', // 最后修改人
  taskId: '', // 任务id
  allocation: false, // 查询是否分配任务 如果选择了任务设置为true
  pageNum: 1,
  pageSize: 20,
  parcelName: '',
  moduleId: 0,
  conditionFields: [], // 字段查询
  ifTree: true,
  ruleIds: [],
  parcelCode: null,
  downLoadId: undefined,
  createTimeStart: undefined,
  createTimeEnd: undefined,
  updateTimeStart: undefined,
  updateTimeEnd: undefined,
  createUserName: undefined,
  optUserId: undefined,
  optUserName: undefined,
  express: false //是否查询表达式异常的数据
});

const total = ref(0); // 数据总条数
const shaixuanDialog = ref(false); // 筛选弹窗
const userPages = ref(0); // 用户分页
const searchUser = reactive({ pageNum: 1, pageSize: 10 }); // 用户查询分页条件
const userList = ref([]); // 用户列表
const taskList = ref([]); // 任务列表
const batchDialog = ref(false); // 批量导入弹窗
const multipleSelection = ref([]); // 多选数据
const isChecked = ref(false); // 是否选中
const uploadLodingDialog = ref(false); // 数据上传进度弹窗
const uploadProgress = ref(0); // 上传进度
const uploadExcelNum = ref(0); // 上传的excel总条数
const uploadStatus = ref(''); // 上传状态
const uploadMsg = ref(''); // 上传成功条数信息
const uploadErrorMsg = ref(''); // 检查上传的excel错误数据
const tableData = ref([]); // 虚拟滚动加载显示的数据
const loading = ref(false); // 加载中标志
const isLoading = ref(false); // 加载中标志
const addNum = ref(0); // 添加的第几条
const addQuestionDialog = ref(false); // 新增问卷弹窗
const questionOne = ref({}); // 新增或编辑的问卷内容
const editQuestionType = ref(1); // 默认新增 2修改
let questionOneRule = reactive({}); // 验证规则
let itemAttribution = reactive({}); // 原始数据 组装数据
let editFieldGroupModel = reactive({}); // 编辑问卷的内容
const checkAllData = ref(false); // 全选所有数据
const fullscreenLoading = ref(false); // 页面全屏加载
const downMsg = ref(''); // 下载信息
const downStatus = ref(''); // 下载信息状态
const nowPageChooseAll = ref(false); // 当前页码是不是全选了 用于懒加载缓存的时候主动勾选
const exportCount = ref(0); // 页面请求三次内容
const exportTime = ref(null); // 页面回调时间
const isContinue = ref(false); // 是否继续下载 用于关闭了进度条就不继续下载
const fjDialog = ref(false); // 附件弹窗
const fjUrl = ref(''); // 附件 URL
const fileType = ref(1); // 文件类型：1doc 2excel 3pdf或者txt
const vHtml = ref(''); // 预览 HTML 内容
const windowHeight = ref('600px'); // 页面高度
const excelData = ref([]); // Excel 数据
let workbook = reactive({}); // Excel 工作簿对象
const token = getToken(); // token
const user = computed(() => userStore.user); // 用户信息
const multipleTable = ref(); // 表格实例
const mainTableHeight = ref(0); // 主table高度

onUpdated(() => {
  multipleTable.value?.doLayout(); // 更新后调用 doLayout 方法
});

/**
 * 获取导出设置
 */
const getExportSetting = () => {
  const moduleId = projectStore.proModuleId;
  const params = {
    moduleId: moduleId,
    companyId: undefined
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  exportSettingList(params).then((res) => {
    if (res.code == 200) {
      if (res.data.length != 0) {
        exportBtn.value = res.data;
      }
      exportBtn.value.unshift({ exportName: '默认导出', id: -1 });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取问卷内容
 */
const getData = () => {
  selectRules({ moduleId: moduleId.value }).then((res) => {
    if (res.code == 200) {
      questionList.value = [];
      // 给字段列表加个锁 是否固定到左侧
      res.data[0].fieldGroupModelList[0].fieldModelList.forEach((v) => {
        v.isLock = false;
        questionList.value.push(v);
      });
      const group = res.data[0].fieldGroupModelList[0];
      fieldGroupModel.value = {
        ruleAttribution: group.ruleAttribution,
        ruleId: res.data[0].id,
        appType: 2,
        fieldInstanceModels: [
          {
            attribution: {},
            groupId: group.id,
            linkId: group.linkId,
            appId: 0
          }
        ]
      };
      init();
      // 初始化保存的结构 树结构 方便赋值上传
      initEdit();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 初始化
 */
const init = () => {
  dialogSearch.value.moduleId = moduleId.value;
  loading.value = true;
  // 重新组装筛选条件
  const conditionFields = [];
  const itemParmas = JSON.parse(JSON.stringify(dialogSearch.value));
  if (itemParmas.conditionFields && itemParmas.conditionFields.length != 0) {
    itemParmas.conditionFields.forEach((v, idx) => {
      conditionFields.push(v);
      // 条件取下一个的条件 最后一个不进入/
      if (idx != itemParmas.conditionFields.length - 1) {
        const obj = { type: 2, value: itemParmas.conditionFields[idx + 1].relation };
        conditionFields.push(obj);
      }
    });
    if (conditionFields.length != 0 && conditionFields[conditionFields.length - 1].type == 2) {
      //如果最后一个是条件 删除
      conditionFields.pop();
    }
    itemParmas.conditionFields = conditionFields;
  }
  itemParmas.levelNum = 1;
  getPlaceList(itemParmas)
    .then((res) => {
      loading.value = false;
      fieldInstanceModels.value = [];
      tableData.value = [];
      addNum.value = 0;
      if (res.code == 200) {
        res.data.list.forEach((v, vdx) => {
          const item = JSON.parse(JSON.stringify(fieldGroupModel.value));
          item.fieldInstanceModels = [v.fieldInstanceModels[0]];
          item.parcelName = v.parcelName;
          item.id = v.id;
          item.index = vdx + 1;
          item.isEdit = false;
          fieldInstanceModels.value.push(item);
        });

        tableData.value = fieldInstanceModels.value;
        isLoading.value = false;

        total.value = res.data.total;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      loading.value = false;
    });
};
/**
 * 添加问卷
 */
const addQuestion = () => {
  const attribution = {};
  questionList.value.forEach((v) => {
    attribution[v.fieldName] = '';
  });
  const item = JSON.parse(JSON.stringify(fieldGroupModel.value));
  item.fieldInstanceModels[0].attribution = attribution;
  item.parcelName = `${new Date().getTime()}_${fieldInstanceModels.value.length}`;
  item.isEdit = true;
  item.index = total.value + 1 + addNum.value;
  tableData.value.unshift(item);
  addNum.value++;
};

/**
 * 保存
 */
const save = () => {
  if (fieldInstanceModels.value.length == 0) {
    ElMessage.error('没有填写内容！！！');
    return;
  }
  // 验证数据是否完整
  let emptyNum = 0;
  questionList.value.forEach((v, vdx) => {
    if (v.required == 1) {
      //需要必填
      fieldInstanceModels.value.forEach((k, kdx) => {
        if (!k.fieldInstanceModels[0].attribution[v.fieldName]) {
          emptyNum++;
          const ele = document.getElementById(`input_${kdx}_${vdx}`);
          ele.classList.add('error-input');
        } else if (k.fieldInstanceModels[0].attribution[v.fieldName]) {
          const ele = document.getElementById(`input_${kdx}_${vdx}`);
          ele.classList.remove('error-input');
        }
      });
    }
  });
  if (emptyNum > 0) {
    //有需要填写的未填写
    ElMessage.error('请检查内容是否填写完整!!!');
    return;
  }
  saveSimple(fieldInstanceModels.value).then((res) => {
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '保存成功'
      });
      init();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 导出
 */
const exportDefault = () => {
  downLoadQuestion(moduleId.value).then((res) => {
    if (res.data.type == 'application/json') {
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data: any) => {
        bugMsg = JSON.parse(data.currentTarget.result).msg;
        ElMessage.error(JSON.parse(data.currentTarget.result).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      ElMessage({
        type: 'success',
        message: '导出成功!'
      });
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      const endFileName = name.substring(index + 1, name.length) || '';
      const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, endFileName);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = endFileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
    }
  });
};

/**
 * 返回行背景样式
 */
const tableRowClassName = ({ row, rowIndex }) => {
  //为了实时判断是否有必填字段未填写
  let flg = false;
  for (let index = 0; index < questionList.value.length; index++) {
    if (questionList.value[index].required == 1 && !row.fieldInstanceModels[0].attribution[questionList.value[index].fieldName]) {
      //需要必填
      flg = true;
      break;
    }
  }
  if (flg) {
    return 'warning-row';
  }
  return '';
};

/**
 * 验证输入
 */
const verificationInput = (value, scopeIndex, index) => {
  if (questionList.value[index].required == 1) {
    const ele = document.getElementById(`input_${scopeIndex}_${index}`);
    if (value) {
      //有值
      ele.classList.remove('error-input');
    } else {
      ele.classList.add('error-input');
    }
  }
};

/**
 * 终止进度继续请求
 */
const handleCloseProgress = () => {
  progressDialog.value = false;
  isContinue.value = false;
  // 清除实时网速定时器
  // clearInterval(netWorkInterval);
};

/**
 * 选中某个导出按钮
 */
const handleCommand = async (data) => {
  if (!checkAllData.value && multipleSelection.value.length == 0) {
    //没有选中全部数据
    ElMessage.error('请选择您要导出的数据!!!');
    return;
  }
  // 每次再选中之前要先清除上一次的值
  downLoadFileName.value = '';
  if (data.id && data.id != -1) {
    getExportDetail(data.id).then((res) => {
      if (res.code == 200) {
        nowDownMsg.value = res.data;
        // 先初始化树
        shpGdbTree.value = {
          shp: [],
          gdb: [],
          pic: [],
          word: [],
          excel: []
        }; //导出的树结构迭代 整理出来shp和gdb列表
        downLoadFileName.value = nowDownMsg.value.detail.fileName;
        getShpGdb([nowDownMsg.value.detail]);
        initDownMsg();
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    const zdId = [];
    if (checkAllData.value) {
      //全选了数据
      const list: any = await getAllData();
      list.forEach((v) => {
        zdId.push(v.id);
      });
    } else {
      multipleSelection.value.forEach((v) => {
        zdId.push(v.id);
      });
    }
    const loading = ElLoading.service({
      lock: true,
      text: '数据下载中，请稍后！！！',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255, 255, .8)'
    });
    // 后端修改了接口，重新传参的方式
    const paramsData = {
      zdId: zdId,
      allData: false
    };
    if (checkAllData.value) {
      paramsData.allData = true;
    }

    downDefaultTemp(moduleId.value, paramsData).then((res) => {
      loading.close();
      if (res.code == 200) {
        multipleTable.value.clearSelection();
        multipleSelection.value = [];
        nowPageChooseAll.value = false;
        progress.value = parseInt(res.data.progress);
        progressDialog.value = true;
        downStatus.value = '';
        isContinue.value = true;
        findAsyncMsg(res.data.id);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};
/**
 * 返回新的command对象
 */
const beforeHandleCommand = (item) => {
  //index我这里是遍历的角标，即你需要传递的额外参数
  return item;
};

/**
 * 把导出里面的文件结构迭代 整理出来代表gdb和shp的树id
 */
const getShpGdb = (list) => {
  list.forEach((v) => {
    if (v.fileType == 3) {
      // shp
      shpGdbTree.value.shp.push(v.id);
    } else if (v.fileType == 4) {
      // gdb
      shpGdbTree.value.gdb.push(v.id);
    } else if (v.fileType == 5) {
      // 图片
      shpGdbTree.value.pic.push(v.id);
    } else if (v.fileType == 1) {
      // word
      shpGdbTree.value.word.push(v.id);
    } else if (v.fileType == 2) {
      //excel
      shpGdbTree.value.excel.push(v.id);
    }
    if (v.list.length != 0) {
      getShpGdb(v.list);
    }
  });
};

// 初始化导出设置内容
const initDownMsg = () => {
  const downLoadMsgTemp = {
    zdList: [], //选中的宗地
    zdListNames: '', //选择的宗地中文名
    onWkid: undefined
  }; //导出设置
  const downLoadMsgRuleTemp = {
    zdList: [{ required: true, message: '请选择数据', trigger: 'change' }],
    onWkid: []
  }; //导出设置校验
  if (nowDownMsg.value.coordinate.disable == 0) {
    //坐标系
    if (nowDownMsg.value.coordinate.defaultValue) {
      downLoadMsgTemp.onWkid = nowDownMsg.value.coordinate.defaultValue;
    } else {
      downLoadMsgTemp.onWkid = '';
    }
    if (nowDownMsg.value.coordinate.must == 1) {
      //必填
      const rules = [{ required: true, message: '请选择坐标系', trigger: 'change' }];
      downLoadMsgRuleTemp.onWkid = rules;
    }
  }
  if (nowDownMsg.value.dataList.must == '1') {
    //如果数据列表必填 需要加验证
    // let zdNumRule = {min: this.nowDownMsg.dataList.minNum, trigger: 'change'}
    // if (this.nowDownMsg.dataList.maxNum) {
    //   zdNumRule.max = this.nowDownMsg.dataList.maxNum
    //   zdNumRule.message = `选择数据应该在${this.nowDownMsg.dataList.minNum}和${this.nowDownMsg.dataList.maxNum}之间`
    // } else if(!this.nowDownMsg.dataList.maxNum) {
    //   zdNumRule.message = `选择数据应该大于${this.nowDownMsg.dataList.minNum}`
    // }
    // downLoadMsgRule.zdList.push(zdNumRule)
  }
  downLoadMsg.value = downLoadMsgTemp;
  downLoadMsgRule.value = downLoadMsgRuleTemp;
  submitDown();
};

/**
 * 真正导出
 */
const submitDown = () => {
  const wkId = '3857';
  const parmas: any = {
    exportId: nowDownMsg.value.id,
    moduleId: nowDownMsg.value.moduleId,
    wkId: wkId
  };
  if (nowDownMsg.value.coordinate.disable == 1) {
    //坐标系统不显示的时候需要主动去取默认值
    if (nowDownMsg.value.coordinate.defaultValue[2] < 25) {
      //六度带
      parmas.wkId = 4478 + nowDownMsg.value.coordinate.defaultValue[2];
    } else if (nowDownMsg.value.coordinate.defaultValue[2] >= 25) {
      //三度带
      parmas.wkId = String(4513 + nowDownMsg.value.coordinate.defaultValue[2] - 25);
    }
  }
  if (downLoadMsg.value.onWkid) {
    if (downLoadMsg.value.onWkid[2] < 25) {
      //六度带
      parmas.wkId = 4478 + downLoadMsg.value.onWkid[2];
    } else if (downLoadMsg.value.onWkid[2] >= 25) {
      //三度带
      parmas.wkId = String(4513 + downLoadMsg.value.onWkid[2] - 25);
    }
  }
  const zdId = [];
  if (checkAllData.value) {
    //全选了数据
    // let list = await this.getAllData()
    // list.forEach(v=>{
    //   zdId.push(v.id)
    // })
  } else {
    multipleSelection.value.forEach((v) => {
      zdId.push(v.id);
    });
  }
  const gdbMap = [];
  const shpMap = [];
  const picMap = [];
  const wordMap = [];
  const excelMap = [];
  shpGdbTree.value.shp.forEach((v) => {
    const obj = {
      exportDetailId: v,
      zdId: zdId
    };
    shpMap.push(obj);
  });
  shpGdbTree.value.gdb.forEach((v) => {
    const obj = {
      exportDetailId: v,
      zdId: zdId
    };
    gdbMap.push(obj);
  });
  shpGdbTree.value.pic.forEach((v) => {
    const obj = {
      exportDetailId: v,
      zdId: zdId
    };
    picMap.push(obj);
  });
  shpGdbTree.value.word.forEach((v) => {
    const obj = {
      exportDetailId: v,
      zdId: zdId
    };
    wordMap.push(obj);
  });
  shpGdbTree.value.excel.forEach((v) => {
    const obj = {
      exportDetailId: v,
      zdId: zdId
    };
    excelMap.push(obj);
  });
  parmas.gdbMap = gdbMap;
  parmas.shpMap = shpMap;
  parmas.picMap = picMap;
  parmas.wordMap = wordMap;
  parmas.excelMap = excelMap;
  parmas.zdId = zdId;
  if (checkAllData.value) {
    parmas.allData = true;
  }
  downLoadMsg.value = {
    zdList: [], //选中的宗地
    zdListNames: '', //选择的宗地中文名
    onWkid: ''
  };
  if (isChecked.value) {
    //检测用
    downLoadPublicCheck(parmas).then((res) => {
      if (res.code == 200) {
        ElNotification({
          title: '错误提示',
          message: res.data,
          duration: 0
        });
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    fullscreenLoading.value = true;
    downLoadPublic(parmas).then((res) => {
      fullscreenLoading.value = false;
      if (res.code == 200) {
        progress.value = parseInt(res.data.progress);
        progressDialog.value = true;
        downStatus.value = '';
        isContinue.value = true;
        findAsyncMsg(res.data.id);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 获取所有数据
const getAllData = () => {
  return new Promise((resolve, reject) => {
    const parmas = {
      moduleId: moduleId.value,
      pageSize: total.value,
      pageNum: 1,
      levelNum: undefined
    };
    fullscreenLoading.value = true;
    parmas.levelNum = 1;
    getPlaceList(parmas).then((res) => {
      fullscreenLoading.value = false;
      if (res.code == 200) {
        resolve(res.data.list);
      } else {
        ElMessage.error(res.msg);
        reject();
      }
    });
  });
};

//循环获取进度条，当进度条==1的时候停止调用
const findAsyncMsg = (id) => {
  if (isContinue.value) {
    const parmas = {
      id: id
    };
    findAsyncMsgApi(parmas).then((res) => {
      if (res.code == 200) {
        downMsg.value = res.data.remark;
        if (parseFloat(res.data.progress) != 1) {
          progress.value = Number((res.data.progress * 100).toFixed(2));
          if (res.data.status == -1) {
            ElMessage.error('导出出错，请联系管理员');
            downStatus.value = 'exception';
            publicDownDialoig.value = false;
            progressDialog.value = false;
            isContinue.value = false;
          } else {
            setTimeout(() => {
              findAsyncMsg(id);
            }, 1000);
          }
        } else if (parseFloat(res.data.progress) == 1) {
          downStatus.value = 'success';
          progress.value = parseFloat(res.data.progress) * 100;
          publicDownDialoig.value = false;
          multipleTable.value.clearSelection();
          nowPageChooseAll.value = false;
          multipleSelection.value = [];
          checkAllData.value = false;
          let text = '';
          if (res.data.result && res.data.result.data != '' && res.data.result.data != null) {
            text = res.data.result.data;
          } else {
            exportCount.value++;
            if (exportCount.value == 3) {
              clearTimeout(exportTime.value);
              ElMessage.error('导出出错，请稍后再试~~~');
              return;
            }
            exportTime.value = setTimeout(() => {
              findAsyncMsg(id);
            }, 1000);
          }
          generalDown(id, downLoadFileName.value, text);
          setTimeout(() => {
            progressDialog.value = false;
            isContinue.value = false;
          }, 1000);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

/**
 * 通用下载
 */
const generalDown = (id, fileName, text) => {
  const parmas = {
    id: id,
    fileName: fileName
  };
  findAsyncFile(parmas).then((res) => {
    if (res.data.type == 'application/json') {
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data: any) => {
        bugMsg = JSON.parse(data.currentTarget.result).msg;
        ElMessage.error(JSON.parse(data.currentTarget.result).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      let endFileName = name.substring(index + 1, name.length) || fileName;
      if (endFileName == '') {
        const index = text.lastIndexOf('/');
        endFileName = text.substring(index + 1, text.length);
      }
      let blob = null;
      if (text.endsWith('.zip')) {
        blob = new Blob([res.data], { type: 'application/zip' });
      } else if (text.endsWith('.xlsx')) {
        blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      } else if (text.endsWith('.xlsx')) {
        blob = new Blob([res.data], { type: '	application/vnd.ms-excel' });
      }
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, endFileName);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = endFileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
    }
  });
};

/**
 * 关闭公共导出弹窗
 */
const handleClose = () => {
  downLoadMsg.value.zdList = [];
  publicDownDialoig.value = false;
};

/**
 * 分享
 */
const share = () => {
  const url = `${baseUrl}/speRegister/${moduleId.value}/${encodeURIComponent(user.value['companyName'])}`;
  copyWord(url);
};

/**
 * 关闭二维码弹窗
 */
const handleCloseEWM = () => {
  erwDialog.value = false;
};

/**
 * 每页条数变化的时候需要先清除选择的数据
 */
const handleSizeChange = (val) => {
  //每页条数变化的时候需要先清除选择的数据
  multipleSelection.value = [];
  multipleTable.value.clearSelection();
  nowPageChooseAll.value = false;
  const scrollableElements = document.querySelector('.el-table__body-wrapper');
  scrollableElements.scrollTop = 0;
  dialogSearch.value.pageNum = 1;
  dialogSearch.value.pageSize = val;
  init();
};

/**
 * 每页条数变化的时候需要先清除选择的数据
 */
const handleCurrentChange = (val) => {
  const scrollableElements = document.querySelector('.el-table__body-wrapper');
  scrollableElements.scrollTop = 0;
  dialogSearch.value.pageNum = val;
  init();
};
/**
 * 打开筛选弹窗
 */
const shaixuan = async () => {
  shaixuanDialog.value = true;
  await getSearchTaskList();
  searchUser.pageNum = 1;
  listUser(searchUser).then((response: any) => {
    if (response.code) {
      userList.value = response.rows;
      let userPagesTemp = parseInt(String(response.total / 10));
      if (response.total % 10 > 0) {
        userPagesTemp = userPagesTemp + 1;
      }
      userPages.value = userPagesTemp;
    } else {
      ElMessage.error(response.msg);
    }
  });
};

/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: moduleId.value
  };
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
      loading.value = false;
    }
  });
};

/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 提交筛选条件
 */
const submitSearch = (isMapping) => {
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  if (dialogSearch.value.parcelCode && !isArray(dialogSearch.value.parcelCode)) {
    dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
  }
  if (dialogSearch.value.taskId) {
    //选择了任务id 就要改变这个值
    dialogSearch.value.allocation = true;
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.value.allocation = false;
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  if (dialogSearch.value.updateDate && dialogSearch.value.updateDate.length != 0) {
    dialogSearch.value.updateTimeStart = dialogSearch.value.updateDate[0];
    dialogSearch.value.updateTimeEnd = dialogSearch.value.updateDate[1];
  } else if (!dialogSearch.value.updateDate) {
    dialogSearch.value.updateTimeStart = '';
    dialogSearch.value.updateTimeEnd = '';
  }
  dialogSearch.value.moduleId = moduleId.value;
  init();
  shaixuanDialog.value = false;
};

/**
 * 清除用户
 */
const clearUser = (type) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

/**
 * 重置筛选条件
 */
const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    // qlrxm:'',//权利人姓名
    // qlrzjhm:'',//权利人证件号码
    // qlrPhone:'',//权利人手机号
    pageNum: 1,
    pageSize: 10,
    parcelName: '',
    moduleId: moduleId.value,
    conditionFields: [],
    ifTree: true,
    express: false //是否查询表达式异常的数据
  }; //弹窗筛选
  init();
  shaixuanDialog.value = false;
};

/**
 * 修改筛选条件
 */
const editCondition = (type, idx) => {
  //type 1新增 2删除 idx 删除的下标
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

/**
 * 批量导入
 */
const batchReport = () => {
  batchDialog.value = true;
};

/**
 * 修改批量导入弹窗状态
 */
const changeBatchDialog = (flg) => {
  batchDialog.value = flg;
};

/**
 * 验证导入的表头是否跟问卷字段对应上
 */
const verifyExcel = (excelThKey) => {
  let errorNum = 0;
  questionList.value.forEach((v, vdx) => {
    if (v.fieldCn.trim() != excelThKey[vdx].trim()) {
      errorNum++;
    }
  });
  if (errorNum == 0) {
    return true;
  } else {
    return false;
  }
};

/**
 * excel导入分段提交
 */
const subsectionSubmit = (list, num) => {
  const fieldInstanceModels = [];
  list.forEach((v) => {
    const attribution = {};
    questionList.value.forEach((k) => {
      if (k.valueMethod == 'date') {
        if (v[k.fieldCn]) {
          attribution[k.fieldName] = v[k.fieldCn] + '';
        } else {
          attribution[k.fieldName] = '';
        }
      } else {
        attribution[k.fieldName] = v[k.fieldCn];
      }
    });
    const item = JSON.parse(JSON.stringify(fieldGroupModel.value));
    item.fieldInstanceModels[0].attribution = attribution;
    item.parcelName = `${new Date().getTime()}_${fieldInstanceModels.length}`;
    fieldInstanceModels.push(item);
  });
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      saveSimple(fieldInstanceModels).then((res) => {
        if (res.code == 200) {
          uploadProgress.value =
            Number((((num * 100) / uploadExcelNum.value) * 100).toFixed(2)) > 100
              ? 100
              : Number((((num * 100) / uploadExcelNum.value) * 100).toFixed(2));
          uploadMsg.value = `已成功导入${num * 100 + list.length}条`;
          resolve(res);
        } else {
          reject(res.msg);
        }
      });
    }, 1000);
  });
};

/**
 * 整体验证是否允许提交 主要判断日期格式是否正确
 */
const verificationSumbit = (list) => {
  return new Promise((resolve, reject) => {
    // 先判断是否有日期
    let veriFlg = false;
    for (let index = 0; index < questionList.value.length; index++) {
      if (questionList.value[index].valueMethod == 'date') {
        veriFlg = true;
        break;
      }
    }
    if (veriFlg) {
      let errFlg = false;
      for (let i = 0; i < list.length; i++) {
        for (let j = 0; j < questionList.value.length; j++) {
          if (questionList.value[j].valueMethod == 'date' && list[i][questionList.value[j].fieldCn]) {
            if (questionList.value[j].attribution.type == 'year') {
              //年
              const flgy = isValidYearFormat(list[i][questionList.value[j].fieldCn]);
              if (!flgy) {
                //不满足年
                // this.uploadErrorMsg = list[i][this.questionList[j].fieldCn]
                uploadErrorMsg.value = `【${questionList.value[j].fieldCn}】是时间类型不能填写【${list[i][questionList.value[j].fieldCn]}】`;
                errFlg = true;
                break;
              }
            } else if (questionList.value[j].attribution.type == 'month') {
              //月
              const flgm = isValidMonthFormat(list[i][questionList.value[j].fieldCn]);
              if (!flgm) {
                //不满足月
                uploadErrorMsg.value = `【${questionList.value[j].fieldCn}】是时间类型不能填写【${list[i][questionList.value[j].fieldCn]}】`;
                errFlg = true;
                break;
              }
            } else if (questionList.value[j].attribution.type == 'date') {
              //日
              const flgd = isValidDateFormat(list[i][questionList.value[j].fieldCn]);
              if (!flgd) {
                //不满足日
                uploadErrorMsg.value = `【${questionList.value[j].fieldCn}】是时间类型不能填写【${list[i][questionList.value[j].fieldCn]}】`;
                errFlg = true;
                break;
              }
            }
          }
        }
      }
      if (errFlg) {
        //错误
        resolve(false);
      } else {
        resolve(true);
      }
    } else {
      resolve(true);
    }
  });
};

/**
 * 通过读取上传的excel导入数据
 */
const submitUploadTemp = async (list, headers) => {
  // 先判断表头是不是大于等于字段数量
  if (headers.length < questionList.value.length) {
    ElMessage.error('导入文档字段与当前字段不匹配！！！');
    return;
  }
  //第一步，需要先判断导入的数据是否正确 表头必须跟字段一一对应
  const flg = verifyExcel(headers);
  if (!flg) {
    ElMessage.error('导入的excel内容跟问卷内容不一致，请检查后再导入！！！');
    return;
  }
  if (list.length == 0) {
    ElMessage.error('选择的表格没有数据!!!');
    return;
  }
  // 验证内容是否准确 主要是日期类型判断
  const submitFlg = await verificationSumbit(list);
  if (!submitFlg) {
    // this.$message.error("请检查上传的excel，日期数据不符合标准，请修改后重新上传！！！")
    ElMessageBox.alert(
      `<span>日期数据不符合标准，请检查上传的excel，修改后重新上传！！！</span><br><span>如：2024年2月需要写成2024.02；2024年2月2日要写成2024.02.02</span><br>
        <span style="color:red">错误数据：${uploadErrorMsg.value}</span>`,
      '上传失败',
      {
        dangerouslyUseHTMLString: true
      }
    );
    return;
  }
  uploadExcelNum.value = list.length;
  const chunkSize = 100;
  const chunks = [];

  // 拆分数组
  for (let i = 0; i < list.length; i += chunkSize) {
    chunks.push(list.slice(i, i + chunkSize));
  }
  batchDialog.value = false;
  // 初始化上传进度弹窗
  uploadStatus.value = '';
  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  // 依次上传子数组
  for (let index = 0; index < chunks.length; index++) {
    try {
      await subsectionSubmit(chunks[index], index + 1);
    } catch (error) {
      console.error('上传失败:', error);
      // 上传失败继续上传
      // await this.subsectionSubmit(chunks[index],index+1);
      ElMessage.error(error);
      // 处理错误，比如跳过当前子数组或中断整个上传过程
    }
  }
  uploadStatus.value = 'success';
  uploadLodingDialog.value = false;
  init();
  ElMessage({
    type: 'success',
    message: '上传完成'
  });
};

/**
 * 选中某行
 */
const handleSelectOne = (selection, row) => {
  nowPageChooseAll.value = false;
  // 判断当前项目是否选中 true 选中 false 未选中
  const isChecked = selection.some((sel) => sel.id === row.id);
  // 判断当前数组中是否已经加入
  const fundIndex = multipleSelection.value.findIndex((obj) => obj.id === row.id);
  if (!isChecked) {
    //  未选中数据
    if (fundIndex > -1) {
      multipleSelection.value.splice(fundIndex, 1);
    }
  } else {
    // 选中
    if (fundIndex == -1) {
      // 把当前勾选的项目推送进入
      multipleSelection.value.push(row);
    }
  }
};

/**
 * 全选
 */
const handleSelectionChange = (val) => {
  if (val.length === 0) {
    //全不选
    multipleSelection.value = [];
    multipleTable.value.clearSelection();
    nowPageChooseAll.value = false;
  } else {
    //全选 全选直接把所有数据赋值给multipleSelection
    nowPageChooseAll.value = true;
    if (val.length >= 20 && dialogSearch.value.pageSize > 20) {
      //大于20条代表还有数据没显示出来 这时候全选就需要把没显示的数据也选上
      multipleSelection.value.push(...tableData.value, ...fieldInstanceModels.value);
      multipleSelection.value.forEach((item) => {
        multipleTable.value.toggleRowSelection(item, true);
      });
    } else {
      multipleSelection.value = [...val];
      multipleSelection.value.forEach((item) => {
        multipleTable.value.toggleRowSelection(item, true);
      });
    }
  }
};

/**
 * 返回日期类型
 */
const getFormat = (type) => {
  if (type == 'year') {
    return 'yyyy';
  } else if (type == 'month') {
    return 'yyyy-MM';
  } else if (type == 'date') {
    return 'yyyy-MM-dd';
  }
};

/**
 * 编辑某一行
 */
const edit = (row) => {
  row.isEdit = true;
};

/**
 * 编辑某一行
 */
const editRow = (row) => {
  editQuestionType.value = 2; //编辑
  editFieldGroupModel = row;
  questionOne.value = row.fieldInstanceModels[0].attribution;
  addQuestionDialog.value = true;
};

/**
 * 保存某条数据
 */
const saveOne = (row) => {
  addQuestionDialog.value = false;
  //组装数据
  let parmas: any = {};
  if (editQuestionType.value == 1) {
    //新增
    parmas = JSON.parse(JSON.stringify(fieldGroupModel.value));
    parmas.fieldInstanceModels[0].attribution = row;
    parmas.parcelName = `${new Date().getTime()}_${fieldInstanceModels.value.length}`;
  } else if (editQuestionType.value == 2) {
    //修改
    parmas = editFieldGroupModel;
    parmas.fieldInstanceModels[0].attribution = row;
  }
  proxy.$modal.loading();
  saveSimple([parmas])
    .then((res) => {
      proxy.$modal.closeLoading();

      if (res.code == 200) {
        ElMessage({
          type: 'success',
          message: '保存成功'
        });
        addQuestionDialog.value = false;
        init();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
};

/**
 * 删除数据
 */
const delQuestion = async () => {
  let allowFlg = false;
  if (checkAllData.value) {
    //选择了全部数据
    await ElMessageBox.confirm('您选择了全部数据，确认要删除全部数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        allowFlg = true;
      })
      .catch(() => {
        allowFlg = false;
      });
  } else {
    if (multipleSelection.value.length == 0) {
      ElMessage.error('请选择数据！！！');
      return;
    }
  }
  if (checkAllData.value && !allowFlg) {
    //选择了全部数据 并且选择了取消删除
    return;
  }
  ElMessageBox.confirm('确定要删除选择的数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      let list = [];
      if (checkAllData.value) {
        const itemList: any = await getAllData();
        itemList.forEach((v) => {
          v.delFlag = 1;
        });
        list = itemList;
      } else {
        multipleSelection.value.forEach((v, vdx) => {
          if (v.id) {
            v.delFlag = 1;
            list.push(v);
          } else {
            for (let index = 0; index < tableData.value.length; index++) {
              if (tableData.value[index].index == v.index) {
                tableData.value.splice(index, 1);
                break;
              }
            }
          }
        });
      }
      if (list.length != 0) {
        multipleTable.value.clearSelection();
        nowPageChooseAll.value = false;
        multipleSelection.value = [];
        saveSimple(list).then((res) => {
          if (res.code == 200) {
            ElMessage({
              type: 'success',
              message: '删除成功'
            });
            init();
          } else {
            ElMessage.error(res.msg);
          }
        });
      }
    })
    .catch(() => {});
};

/**
 * 是否固定到左侧
 */
const changeLock = (item, type) => {
  if (type == 1) {
    //解除
    item.isLock = false;
  } else {
    //固定
    item.isLock = true;
  }
  tableKey.value = new Date().getTime() + 1;
};

/**
 * 跳转数据大屏
 */
const jumpScreen = () => {
  const moduleId = projectStore.proModuleId;
  const searchMsg = {
    pageNo: 1,
    pageSize: 10000,
    moduleId: moduleId
  };
  if (moduleId) {
    getScreenList(searchMsg).then((res) => {
      if (res.code == 200) {
        if (res.data.records.length == 0) {
          ElMessage.error('未查询到数据大屏！！！');
          return;
        }
        let id = '';
        for (let index = 0; index < res.data.records.length; index++) {
          if (res.data.records[index].state == 1) {
            id = res.data.records[index].id;
            break;
          }
        }
        if (!id) {
          ElMessage.error('未查询到启用的数据大屏');
          return;
        }
        window.open(`/preview/${moduleId}/${id}`);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

/**
 * 表格更新
 */
const handleUpdate = () => {};

/**
 * 初始化上传结构 方便组装数据
 */
const initEdit = () => {
  const attribution = {};
  questionOneRule = {};
  questionList.value.forEach((v) => {
    attribution[v.fieldName] = '';
    questionOneRule[v.fieldName] = [];
    if (v.required == 1) {
      //必填
      if (v.valueMethod == 'date' || v.valueMethod == 'select') {
        questionOneRule[v.fieldName].push({
          required: true,
          message: `选择${v.fieldCn}`,
          trigger: 'change'
        });
      } else {
        questionOneRule[v.fieldName].push({
          required: true,
          message: `请输入${v.fieldCn}`,
          trigger: 'blur'
        });
      }
    }
  });
  itemAttribution = attribution;
};

/**
 * 新增问卷弹窗
 */
const addNewQuestion = () => {
  questionOne.value = JSON.parse(JSON.stringify(itemAttribution));
  editQuestionType.value = 1;
  addQuestionDialog.value = true;
};

/**
 * 关闭问卷弹窗
 */
const closeAddQuestion = () => {
  addQuestionDialog.value = false;
};

/**
 * 组装附件
 */
const getFJ = (item, file) => {
  const list = [];
  if (item.length != 0) {
    item.forEach((v) => {
      const obj = {
        title: v[`${file.fieldName}_1`],
        type: v[`${file.fieldName}_1`].substring(v[`${file.fieldName}_1`].indexOf('.') + 1, v[`${file.fieldName}_1`].length),
        url: v[`${file.fieldName}_0`]
      };
      list.push(obj);
    });
  }
  return list;
};

/**
 * 查看附件dialog
 * @param obj
 */
const showFjDialog = (obj: any) => {
  if (!obj.type) {
    //现新增的然后 查看就没有类型 需要把类型先得出来
    let type = '';
    const index = obj.title.indexOf('.');
    type = obj.title.substring(index + 1, obj.title.length);
    obj.type = type;
  }
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  const windowHeight = window.innerHeight - 150 + 'px';
  if (obj.type == 'txt' || obj.type == 'pdf') {
    Axios({
      method: 'get',
      url: `${baseUrl}${obj.url}?att=1`,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    }).then((res) => {
      let type = '';
      switch (obj.type) {
        case 'txt':
          type = 'text/plain';
          break;
        case 'xls':
          type = 'application/vnd.ms-excel';
          break;
        case 'xlsx':
          type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'doc':
          type = 'application/msword';
          break;
        case 'docx':
          type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'pdf':
          type = 'application/pdf';
          break;
        default:
          break;
      }
      loading.close();
      fileType.value = 3;
      const blob = new Blob([res.data], { type: type });
      fjUrl.value = `${window.URL.createObjectURL(blob)}`;
      fjDialog.value = true;
    });
  } else if (obj.type == 'doc') {
    // 使用 previewFile 接口获取预览地址
    previewFile(obj.url, 'pdf')
      .then((res) => {
        if (res.status === 200) {
          loading.close();
          fileType.value = 3; // 统一使用 iframe 预览
          const blob = new Blob([res.data], { type: 'application/pdf' });
          fjUrl.value = URL.createObjectURL(blob);
          fjDialog.value = true;
        } else {
          loading.close();
          ElMessage.error(res.msg || '预览失败');
        }
      })
      .catch((error) => {
        loading.close();
        ElMessage.error('预览失败：' + error.message);
      });
  } else if (obj.type == 'docx') {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = function () {
      if (xhr.status == 200) {
        let flg = true;
        if (xhr.response.byteLength == 0) {
          //空文件 word 不允许打开
          ElMessageBox.alert('您上传的word文件为空文件，不允许打开！！！', '错误信息', {
            confirmButtonText: '确定',
            callback: (action) => {
              loading.close();
              flg = false;
            }
          });
        }
        if (!flg) {
          return;
        }
        mammoth.convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) }).then(function (resultObject) {
          nextTick(() => {
            fileType.value = 1;
            loading.close();
            fjDialog.value = true;
            vHtml.value = resultObject.value;
          });
        });
      }
    };
    xhr.send();
  } else {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = function () {
      if (xhr.status == 200) {
        fileType.value = 2;
        const data = new Uint8Array(xhr.response);
        try {
          const workbookTemp = XLSX.read(data, { type: 'array' });
          loading.close();
          const sheetNames = workbookTemp.SheetNames; // 工作表名称集合
          workbook.value = workbookTemp;
          getTable(sheetNames[0]);
        } catch (error) {
          loading.close();
          ElMessageBox.alert('【文件存在异常，无法打开】', '错误信息', {
            confirmButtonText: '确定',
            callback: (action) => {}
          });
        }
      }
    };
    xhr.send();
  }
};

/**
 * 关闭附件dialog
 */
const handleCloseFj = () => {
  fjDialog.value = false;
};

/**
 * 获取预览
 */
const getPreview = (item) => {
  const list = [];
  item.forEach((v) => {
    list.push(`${base}/qjt/file/otherDownload/${v.url}?token=${token}`);
  });
  return list;
};

onMounted(() => {
  mainTableHeight.value = document.getElementById('main-table-js')?.clientHeight;
  const list_3 = [];
  for (let i = 25; i < 46; i++) {
    list_3.push({ value: i, label: `${i}` });
  }
  const list_6 = [];
  for (let i = 13; i < 24; i++) {
    list_6.push({ value: i, label: `${i}` });
  }
  wkidList.value[0].children[0].children = list_3;
  wkidList.value[0].children[1].children = list_6;

  moduleId.value = projectStore.proModuleId;
  getData();
  getExportSetting();
});
</script>
<style lang="scss" scoped>
.img-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .img-one {
    width: 45px;
    height: 45px;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.fj-box {
  display: flex;
  flex-direction: column;
  .fj-item {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
.nothing {
  background: #dbe7ed !important;
}
.red {
  color: #f56c6c;
  cursor: pointer;
  margin-left: 4px;
}
.blue {
  color: #409eff;
  cursor: pointer;
  margin-left: 4px;
}
.table-footer {
  height: 53px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.logding-msg {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
  color: rgb(0, 0, 0, 0.6);
  font-size: 14px;
  bottom: 180px;
  left: calc(50% - 100px);
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  margin-right: 10px;
}
:deep(.el-table .warning-row) {
  background: rgb(255, 0, 0, 0.1);
}
:deep(.error-input) {
  border: red solid 1px !important;
}
.modalQuestion-main {
  width: 100%;
  height: 100%;
  .main-con {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
  .handle {
    padding: 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .down-all {
    padding: 0px 16px 10px 16px;
  }
  .content {
    // height: calc(100% - 180px);
    width: 100%;
    .table-class {
      // width: calc(100% - 32px);
      // margin-left: 16px;
    }
  }
}
</style>
