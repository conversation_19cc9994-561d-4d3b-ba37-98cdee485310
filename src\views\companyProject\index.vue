<!-- 公司列表 -->
<template>
  <div class="main">
    <container-card>
      <div class="title-row">
        <span class="text">公司列表</span>
        <div>
          <el-link type="primary" @click="addCompany">注册公司</el-link>
          <el-link type="primary" @click="handleBatchPerson" style="margin-left: 10px">个人版更新</el-link>
        </div>
      </div>
      <div class="search-div">
        <div class="search-item">
          <div class="label">公司名称</div>
          <div class="search-content">
            <el-input v-model="search.companyName" clearable placeholder="请输入公司名称" @keyup.enter="getData"></el-input>
          </div>
        </div>
        <div class="search-item">
          <div class="label">公司类型</div>
          <div class="search-content">
            <el-select v-model="search.companyType" placeholder="请选择" @change="getData">
              <el-option :value="1" label="公司"></el-option>
              <el-option :value="2" label="个人"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-item search-end">
          <el-button type="primary" @click="getData">搜索</el-button>
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" :height="tableHeight" class="table-content" border>
        <el-table-column type="index" label="序号" width="60px"></el-table-column>
        <el-table-column label="公司名称" prop="companyName" min-width="200"></el-table-column>
        <el-table-column label="管理者" prop="custName" width="120"></el-table-column>
        <el-table-column label="管理账号" prop="adminPhone" width="120"></el-table-column>
        <el-table-column label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDateYmdhm(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" sortable prop="expireTime" width="160">
          <template #default="{ row }">
            <span v-if="row.expireTime >= 32505381325000">无限制</span>
            <span v-else>{{ formatDateAndTimeType(row.expireTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最大人数" width="100">
          <template #default="{ row }">
            <span v-if="row.maxUser === *********">无限制</span>
            <span v-else>{{ row.maxUser }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公司等级" width="100">
          <template #default="{ row }">
            <span v-if="row.vipType === 1">个人版</span>
            <span v-else-if="row.vipType === 2">专业版</span>
            <span v-else-if="row.vipType === 3">企业版</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="公司状态">
          <template #default="{ row }">
            <span v-show="row.status === 0">正常</span>
            <span v-show="row.status === -1">已过期</span>
            <span v-show="row.status === -2">暂停使用</span>
          </template>
        </el-table-column>
        <el-table-column label="默认模板数量" prop="defaultModule" width="110"></el-table-column>
        <el-table-column label="在线用户" prop="online" width="80"></el-table-column>
        <el-table-column label="业务员" prop="promoterUseName" width="120"></el-table-column>
        <el-table-column label="操作" width="360" fixed="right">
          <template #default="{ row }">
            <el-link type="primary" @click="handleEditCompany(row)" v-if="row.companyId !== '1'" v-hasPermi="['system:company:edit']">编辑</el-link>
            <el-link type="primary" @click="handleDetialUser(row)" style="margin-left: 10px">查看在线用户</el-link>
            <el-link type="primary" @click="handleOpenModal(row)" style="margin-left: 10px">添加模板</el-link>
            <el-link type="primary" @click="handleSettingModal(row)" style="margin-left: 10px">设置模块</el-link>
            <el-link type="primary" @click="handleSettingMenu(row)" style="margin-left: 10px" v-if="row.selfMenu === 1">设置菜单</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer-page">
        <el-pagination
          v-model:current-page="search.pageNum"
          v-model:page-size="search.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </container-card>

    <!-- 编辑公司 -->
    <EditCompanyDialog
      v-model:visible="dialogVisible"
      :company-data="currentCompany"
      :selected-company-type="selectedCompanyType"
      @success="getData"
    />
    <!-- 注册公司 -->
    <RegisterCompanyDialog v-model:visible="addCompanyDialog" @success="getData" />

    <!-- 选择模块库 -->
    <SelectModuleDialog v-model:visible="modelTypeDialog" :current="current" :base-url="baseUrl" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Action, ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { getCompanyList, updateFromControl, initAddModule, addModel } from '@/api/control/index';
import { register } from '@/api/login';
import { selectDefalutList } from '@/api/modal/index';
import { formatDateYmdhm, formatDateAndTimeType } from '@/utils/filters';
import EditCompanyDialog from './components/EditCompanyDialog.vue';
import RegisterCompanyDialog from './components/RegisterCompanyDialog.vue';
import SelectModuleDialog from './components/SelectModuleDialog.vue';

interface FormData {
  companyId: string;
  companyName: string;
  autoPublish: boolean;
  [key: string]: any;
}

interface CompanyData {
  companyId: string;
  companyName: string;
  [key: string]: any;
}

interface SearchParams {
  companyName: string;
  pageSize: number;
  pageNum: number;
  companyType: number;
}

interface CompanyItem {
  companyId: string;
  companyName: string;
  type: number;
  [key: string]: any;
}

interface ChunkData {
  count: number;
  chunkSize: number;
  chunks: CompanyItem[];
}

interface RegistCompany {
  companyName: string;
  companyType: number;
  custName: string;
  username: string;
  password: string;
  vipType: number;
}

const router = useRouter();
const userStore = useUserStore();

const search = reactive<SearchParams>({
  companyName: '',
  pageSize: 20,
  pageNum: 1,
  companyType: 1
});

const tableData = ref<any[]>([]);
const total = ref(0);
const tableHeight = computed(() => window.innerHeight - 280);

const currentCompany = reactive<CompanyData>({
  maxSelect: 1,
  dateSelect: 1,
  selfMenu: 0,
  companyId: '',
  appId: undefined,
  appSecret: undefined,
  loginSource: [],
  companyName: '',
  companyType: 1,
  maxUser: 0,
  expireTime: 0,
  status: 0,
  vipType: 1
});

const selectedCompanyType = ref<number>(2);
const dialogVisible = ref(false);
const addCompanyDialog = ref(false);
const modelTypeDialog = ref(false);
const defaultList = ref<any[]>([]);
const itemId = ref<string[]>([]);
const current = ref<any>({});
const isPay = ref(false);
const isAddModel = ref(false);
const loadingProgess = ref<any>(null);
const uploadResultLength = ref(0);
const uploadTotal = ref(0);
const successList = ref<any[]>([]);
const failList = ref<any[]>([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
let loadingInstance: any = null;

const loginSourceList = [
  { label: '网页', value: 'web' },
  { label: '小程序', value: 'wx' },
  { label: 'App', value: 'app' }
];

const currentCompanyRule = {
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  maxUser: [{ required: true, message: '请输入公司最大成员数', trigger: 'blur' }],
  expireTime: [{ required: true, message: '请选择公司过期时间', trigger: 'blur' }],
  companyType: [{ required: true, message: '请选择公司类型', trigger: 'blur' }]
};

const registCompany = reactive<RegistCompany>({
  companyName: '',
  companyType: 1,
  custName: '',
  username: '',
  password: '',
  vipType: 1
});

const registCompanyRule = {
  custName: [
    { required: true, trigger: 'blur', message: '请输入单位名称' },
    {
      min: 2,
      max: 20,
      message: '用户账号长度必须介于 2 和 20 之间',
      trigger: 'blur'
    }
  ],
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!/^(1[0-9])\d{9}$/i.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  companyName: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入公司名称'
    }
  ]
};
// 监听上传进度
watch(
  uploadResultLength,
  (newVal: number) => {
    if (newVal === uploadTotal.value) {
      setTimeout(() => {
        if (loadingInstance) {
          loadingInstance.close();
        }
        if (failList.value.length > 0) {
          handleDownLoadErrorLog();
        }
        // getData()
      }, 1000);
    } else {
      if (loadingInstance) {
        loadingInstance.setText(`本次上传的数据共:${uploadTotal.value}条，已上传${newVal}条`);
      }
    }
  },
  { deep: true }
);
const getData = async () => {
  try {
    const res = await getCompanyList(search);
    if (res.code === 200) {
      tableData.value = res.data.rows;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Failed to fetch company list:', error);
    ElMessage.error('获取公司列表失败');
  }
};

const handleSizeChange = (val: number) => {
  search.pageNum = 1;
  search.pageSize = val;
  getData();
};

const handleCurrentChange = (val: number) => {
  search.pageNum = val;
  getData();
};

const handleEditCompany = (row: any) => {
  Object.assign(currentCompany, {
    ...row,
    loginSource: row.loginSource && typeof row.loginSource === 'string' ? JSON.parse(JSON.stringify(row.loginSource.split(','))) : [],
    companyId: row.companyId
  });
  selectedCompanyType.value = JSON.parse(JSON.stringify(row.companyType));
  if (row.maxUser === *********) {
    currentCompany.maxSelect = 1;
  } else {
    currentCompany.maxSelect = 2;
  }
  if (row.expireTime >= 32505381325000) {
    currentCompany.dateSelect = 1;
  } else {
    currentCompany.dateSelect = 2;
  }
  dialogVisible.value = true;
};

const handleMaxSelectChange = (val: number) => {
  currentCompany.maxSelect = val;
  if (val === 1) {
    currentCompany.maxUser = *********;
  }
};

const handleDateSelectchange = (val: number) => {
  currentCompany.dateSelect = val;
  if (val === 1) {
    currentCompany.expireTime = 32505381325000;
    currentCompany.status = 0;
  }
};

// const handleClose = () => {
//   dialogVisible.value = false;
// };
/**
 * 注册公司
 */
const addCompany = () => {
  addCompanyDialog.value = true;
};
/**
 * 批量更新个人版的数据
 */
const handleBatchPerson = () => {
  const params = {
    pageSize: 10000,
    pageNum: 1,
    companyType: 2
  };
  ElMessageBox.confirm('确认批量更新个人版公司的过期时间吗？', '提醒', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const res = await getCompanyList(params);

        if (res.code === 200) {
          const tableList = res.data.rows;
          uploadTotal.value = res.data.total;
          const text = `本次上传的数据共:${uploadTotal.value}条，已上传:${uploadResultLength.value * 10}条`;
          loadingInstance = ElLoading.service({
            lock: true,
            text,
            spinner: 'el-icon-loading',
            background: 'rgba(255, 255, 255, 0.9)'
          });

          const chunksData = await handleChunkData(tableList, 1);
          await handleAllAddByOne(chunksData);
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('批量处理失败:', error);
        ElMessage.error('批量处理失败');
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消更新'
      });
    });
};
/**
 * 批量处理公司的数据
 * @param list  个人版列表的所有数据
 * @param num 一次分批多少条数据
 */
const handleChunkData = async (list: CompanyItem[], num: number): Promise<ChunkData> => {
  const count = list.length;
  const chunkSize = num || 100;

  return {
    count,
    chunkSize,
    chunks: list
  };
};
/**
 * 在分批次处理之后的每一个处理的数据
 * @param chunksData  分批处理之后的二维数组的数据
 */
const handleAllAddByOne = async (chunksData: ChunkData) => {
  for (let index = 0; index < chunksData.count; index++) {
    try {
      const type = chunksData.chunks[index].type;
      const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize, type);
      if (resultNum === uploadTotal.value) {
        // 当数据完成后关闭弹框
        // this.$emit("closeExcelAsset");
      }
    } catch (error) {
      console.error('处理异常:', error);
      ElMessage.error(error as string);
      continue;
    }
  }
};
/**
 * 上传前校验每一条数据是否正确
 * @param item 校验每一次的数据是否正确
 * @param num 当前分批的次数
 * @param count 总分批数字
 * @param chunkSize  每次分批次的长度
 */
const verificationOnece = async (item: CompanyItem, num: number, count: number, chunkSize: number): Promise<number> => {
  return new Promise((resolve) => {
    const params = { ...item };
    updateFromControl(params).then((res) => {
      if (res && res.code === 200) {
        setTimeout(() => {
          uploadResultLength.value = num * chunkSize > count ? count : num * chunkSize;
          uploadTotal.value = count;
          successList.value.push({
            companyId: item.companyId,
            companyName: item.companyName
          });
          resolve(uploadResultLength.value);
        }, 100);
      } else {
        uploadResultLength.value = num * chunkSize > count ? count : num * chunkSize;
        uploadTotal.value = count;
        failList.value.push({
          companyId: item.companyId,
          companyName: item.companyName
        });
        resolve(uploadResultLength.value);
      }
    });
  });
};
/***
 * 批量更新失败了之后 将失败的数据和失败原因导出为一个txt文件
 */
const handleDownLoadErrorLog = () => {
  const element = document.createElement('a');
  const endContent = `本次更新:成功了${successList.value.length}条，失败了${failList.value.length}条：\n ${failList.value.join('\n')}`;
  element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
  element.setAttribute('download', '更新失败数据');
  element.style.display = 'none';
  element.click();
  document.body.removeChild(element);
};

const getDefalutList = async () => {
  try {
    const res = await selectDefalutList();
    if (res.code === 200) {
      defaultList.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取默认列表失败:', error);
    ElMessage.error('获取默认列表失败');
  }
};

const handleDefaultModel = (item: any) => {
  const index = itemId.value.indexOf(item.id);
  if (index !== -1) {
    itemId.value.splice(index, 1);
  } else {
    itemId.value.push(item.id);
  }
};

const handleOpenModal = (row: any) => {
  current.value = row;
  modelTypeDialog.value = true;
};

const submitModelType = async () => {
  if (itemId.value.length === 0) {
    ElMessage.error('请选择模块！');
    return;
  }

  isAddModel.value = true;
  const params = {
    companyId: current.value.companyId,
    adminUserId: current.value.adminUserId
  };

  try {
    const res = await addModel(params, itemId.value);
    isAddModel.value = false;
    if (res.code === 200) {
      ElMessage.success(res.data);
      modelTypeDialog.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Submit model type failed:', error);
    ElMessage.error('提交模块类型失败');
  }
};
/**
 * 设置是有菜单
 * @param row 当前公司的设置信息
 */
const handleSettingMenu = (row: any) => {
  router.push({
    path: '/selfMenu',
    query: {
      companyId: row.companyId
    }
  });
};
/**
 * 设置私有模块
 * @param row 当前公司的信息
 */
const handleSettingModal = (row: any) => {
  router.push({
    path: '/modal',
    query: {
      companyId: row.companyId
    }
  });
};
/**
 * 查看在线人数
 * @param row 当前公司的信息
 */
const handleDetialUser = (row: any) => {
  router.push({
    path: '/system/online',
    query: {
      companyId: row.companyId
    }
  });
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0;
}

.main {
  width: 100%;
  height: 100%;
  .title-row {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dbe7ee;
    align-items: center;
    .text {
      height: 22px;
      font-size: 16px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
      line-height: 22px;
      margin-bottom: 4px;
    }
  }
  .search-div {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      .label {
        width: 100px;
        text-align: right;
      }
      .search-content {
        margin-left: 10px;
        flex: 1;
      }
    }
    .search-end {
      justify-content: flex-end;
    }
  }
  .table-content {
    margin-top: 10px;
    height: calc(100% - 115px);
    overflow: auto;
    /*滚动条样式*/
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
  .footer-page {
    margin-top: 10px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
  }
}

.label-main {
  display: flex;
  justify-content: space-between;
  .label {
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    font-weight: 600;
  }
}

.modal-item-contianer {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  .row-left {
    display: flex;
    align-items: center;
    border-radius: 6px;
    margin-right: 8px;
    margin-bottom: 10px;
    position: relative;
    .left-icon {
      min-width: 48px;
      text-align: left;
      margin-left: 10px;
      position: relative;
      .svg-item {
        width: 44px;
        height: 44px;
      }
      .modal-icon-svg {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 0px;
      }
      .modal-icon-pic {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 8px;
      }
    }
    .left-module {
      width: 30%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-info {
        min-width: 156px;
        .title {
          display: flex;
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 600;
          min-width: 156px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .stop {
            width: 48px;
            height: 20px;
            background: rgba(255, 61, 87, 0.1);
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #ff3d57;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .defalut {
            width: 60px;
            height: 20px;
            background: #e6ebf5;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #8291a9;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing {
            width: 48px;
            height: 20px;
            background: #f2752157;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #e53e07af;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing-end {
            width: 60px;
            height: 20px;
            background: #adffbf;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #089145;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
        }
        .remark {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 12px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &:hover {
      background-color: #f6f7f8;
      border-radius: 6px;
    }
    .pay-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 25px;
      background: linear-gradient(180deg, #ffa509 0%, #f1890a 100%);
      border-top-right-radius: 50%;
      border-bottom-left-radius: 50%;
      object-fit: cover;
      color: #fff;
      font-weight: 500;
      letter-spacing: 1px;
      line-height: 25px;
    }
  }
}
</style>
