<template>
  <dv-water-level-pond :key="refreshFlagKey" :config="config" :style="{ width: width + 'px', height: height + 'px' }" />
</template>

<script setup lang="ts">
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { v1 as uuidv1 } from 'uuid';
defineOptions({
  name: 'cpt-dataV-waterLevel'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const config = ref<any>({});
const uuid = ref(null);
const refreshFlagKey = ref(null);

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadData();
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
watch(
  () => props.height,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
  refreshFlagKey.value = uuidv1();
};
defineExpose({
  refreshCptData
});
const loadData = () => {
  getDataJson(props.option.cptDataForm).then((res) => {
    config.value = JSON.parse(JSON.stringify(props.option.attribute));
    config.value.data = res;
  });
};

onMounted(() => {
  uuid.value = uuidv1();
  refreshFlagKey.value = uuidv1();
  refreshCptData();
});
</script>

<style scoped></style>
