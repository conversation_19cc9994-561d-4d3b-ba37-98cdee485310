<!-- 新增类型的弹框 -->
<template>
  <div>
    <el-dialog :title="addGroupTitle" v-model="dialogVisible" width="600" @close="handleClose" @open="handleOpen" :close-on-click-modal="false">
      <el-form :model="addTypeForm" :rules="addTypeFormRules" ref="addTypeFormRef" class="demo-addTypeForm" label-width="100">
        <el-form-item label="属性名称" prop="typeName">
          <el-input v-model="addTypeForm.typeName" @input="handleChangeTypeName" placeholder="请输入属性名称" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="属性图标" prop="iconUrl">
          <div class="item-content" @click="handleOpenSettingIconDialog">
            <div class="choose-img-div">
              <div class="choose-img" v-if="addTypeForm.iconUrl && addTypeForm.iconUrl.substring(addTypeForm.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                <!-- 用户自定义的上传 -->
                <!-- <authImg :authSrc="`${baseUrl}${addTypeForm.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" /> -->
                <el-image
                  style="width: 20px; height: 20px; margin-right: 8px"
                  :src="`${baseUrl}${addTypeForm.iconUrl}?token=${token}`"
                  :fit="'cover'"
                />
              </div>
              <div class="choose-img" v-else>
                <!-- 写死的内容 -->
                <svg-icon class-name="svg-item" :icon-class="addTypeForm.iconUrl" />
              </div>
              <div class="choose-btn" @click="handleOpenSettingIconDialog">
                <el-icon><Setting /></el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="属性说明" prop="remark">
          <el-input v-model="addTypeForm.remark" @input="handleChangeRemark" placeholder="请输入属性说明"></el-input>
        </el-form-item>
        <el-form-item label="属性固定别名" prop="displayName">
          <el-input v-model="addTypeForm.displayName" @input="handleChangeDisplayName" placeholder="选填"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmitGroup">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 设置属性图标的组件 -->
    <setting-icon
      :iconVisible="iconVisible"
      :defaultList="groupIconList"
      :iconSelected="addTypeForm.iconUrl"
      :isShow="isShow"
      :iconName="iconName"
      @closeSettingIcon="handleCloseSettingIcon"
      @submitIcon="handleSubmitSettingIcon"
    ></setting-icon>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus/es';
import settingIcon from '../../SettingIcon/index.vue';
import authImg from '@/components/authImg/index.vue';
import svgIcon from '../../svgIcon/index.vue';
import groupIconList from '../../SettingIcon/groupIcon.json';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { Setting } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getToken } from '@/utils/auth';
const token = getToken();

// 定义 props
const props = defineProps<{
  addGroupTitle: string;
  addGroupVisible: boolean;
  addGroupItem: Record<string, any>;
  groupFilterList: any[];
}>();

// 定义 emits
const emit = defineEmits(['closeAddGroup', 'submitGroup']);

const modalStore = useModalStore();
const userStore = useUserStore();

const dialogVisible = computed({
  get() {
    return props.addGroupVisible;
  },
  set(value) {
    // 触发关闭事件
    emit('closeAddGroup');
  }
});

// 定义常量
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;

// 定义响应式数据
const addTypeForm = ref<{
  typeName?: string;
  iconUrl?: string;
  remark?: string;
  displayName?: string;
  id?: number;
  linkId?: number;
  attribution?: any;
  fieldModelList?: any[];
  ruleAttribution?: any;
}>({
  typeName: '',
  iconUrl: '',
  remark: '',
  displayName: ''
});

const addTypeFormRef = ref<FormInstance>();

const isShow = ref(true);
const iconVisible = ref(false);
const iconName = ref('属性组自定义照片');

// 定义校验规则
const validateIcon = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    return callback(new Error('请选择属性图标'));
  } else {
    callback();
  }
};

const addTypeFormRules = {
  typeName: [{ required: true, message: '属性名称不能为空', trigger: 'blur' }],
  iconUrl: [{ required: true, validator: validateIcon, trigger: ['blur', 'change'] }],
  remark: [{ required: true, message: '属性说明不能为空', trigger: 'blur' }]
};

const moduleId = computed(() => modalStore.moduleId);

// 监听 addTypeForm.iconUrl 的变化
watch(
  () => addTypeForm.value.iconUrl,
  (val) => {
    if (val && addTypeFormRef.value) {
      addTypeFormRef.value.clearValidate('iconUrl');
    }
  }
);

// 打开对话框
const handleOpen = () => {
  // 修改时的回显数据
  if (props.addGroupItem && props.addGroupTitle === '修改属性') {
    Object.assign(addTypeForm.value, props.addGroupItem);
  }
};

// 关闭对话框
const handleClose = () => {
  emit('closeAddGroup');
  addTypeForm.value = {};
};

// 打开属性图标的组件 dialog
const handleOpenSettingIconDialog = () => {
  iconVisible.value = true;
};

// 关闭设置属性图标的 dialog 弹框
const handleCloseSettingIcon = () => {
  iconVisible.value = false;
};

// 确定选中的属性图标，子组件中的传值
const handleSubmitSettingIcon = (name: string) => {
  addTypeForm.value.iconUrl = name;
};

// 新增属性的提交按钮
const handleSubmitGroup = () => {
  const params = {
    // 提交的全数组属于范围 1 模块 2 规则 可以采用复制方式到另一个模块即可实现公司共享
    groupScope: 1,
    iconUrl: addTypeForm.value.iconUrl,
    moduleId: moduleId.value,
    operaType: 1, // 操作类型 1 新增  2 删除
    typeName: addTypeForm.value.typeName,
    wordName: '',
    linkType: 1,
    attribution: addTypeForm.value.attribution,
    fieldModelList: addTypeForm.value.fieldModelList,
    remark: addTypeForm.value.remark,
    ruleAttribution: addTypeForm.value.ruleAttribution,
    displayName: addTypeForm.value.displayName,
    id: addTypeForm.value?.id,
    linkId: addTypeForm.value?.linkId
  };

  // if (props.addGroupTitle === '修改属性') {
  //   params.id = addTypeForm.value.id;
  //   params.linkId = addTypeForm.value.linkId;
  // }

  const isName = props.groupFilterList.some((i) => {
    return !addTypeForm.value.id && i.typeName === addTypeForm.value.typeName;
  });

  if (isName) {
    ElMessage.error('组名不能相等');
    return;
  }

  if (addTypeFormRef.value) {
    addTypeFormRef.value.validate((valid: boolean) => {
      if (valid) {
        emit('submitGroup', params);
        modalStore.setIsAllGroup(false);
        modalStore.setIsHasAcquition(true);
      }
    });
  }
};

// 修改创建的类型的名称
const handleChangeTypeName = (val: string) => {
  addTypeForm.value.typeName = val;
};

const handleChangeRemark = (val: string) => {
  addTypeForm.value.remark = val;
};

const handleChangeDisplayName = (val: string) => {
  addTypeForm.value.displayName = val;
};
</script>

<style lang="scss" scoped>
.item-content {
  flex: 1;
  .choose-img-div {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    cursor: pointer;

    .choose-img {
      width: 20px;
      height: 20px;
      margin-left: 12px;
      margin-top: -3px;
      .svg-item {
        width: 20px;
        height: 20px;
        color: #333;
      }
    }
    .choose-btn {
      width: 52px;
      height: 100%;
      border-left: 1px solid #e6e9ee;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      background: #edf4fb;
      border-radius: 0px 4px 4px 0px;
    }
  }
}
:deep(.el-form-item__label) {
  color: #161d26;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-size: 14px;
}
</style>
