<template>
  <!-- 拓扑检测 -->
  <div class="examineSetting-main">
    <div class="content">
      <el-row style="margin: 24px 16px 4px">
        <el-col>
          <el-button type="primary" plain :icon="Plus" size="small" @click="handleExamine">新增检查</el-button>
        </el-col>
      </el-row>
      <div class="examine-item" v-for="(item, index) in list" :key="index">
        <div class="examine-content">
          <div class="examine-left">
            <span class="spe-span" v-show="item.sourceType === 1">图形拓扑检查：</span>
            <span class="spe-span" v-show="item.sourceType === 2">字段条件检查：</span>
          </div>
          <div class="examine-center">
            <div class="examine-span" v-show="item.sourceType === 2">当</div>
            <div class="examine-span">
              <el-tag size="small">{{ getYSName(ruleTree, item.sourceRuleId) || '没有找到节点或者节点已经被删除' }}</el-tag>
            </div>
            <!-- 图形拓扑检查 -->
            <template v-if="item.sourceType === 1">
              <!-- 只有一个参数的时候 -->
              <div class="examine-span" v-if="!item.targetRuleId && !item.attribution">与外部同级节点</div>
              <div class="examine-span" v-if="item.attribution && item.attribution.GeometryGroup">
                的<el-tag size="small" style="margin: 0px 5px">{{
                  item.attribution.GeometryGroup.fieldGroupModelList.fieldModelList[0].fieldCn
                }}</el-tag
                >字段值相同的兄弟节点
              </div>
              <div class="examine-span">
                <el-tag size="small" type="success">
                  <span v-show="item.checkType === 1">不相交</span>
                  <span v-show="item.checkType === 2">相交</span>
                  <span v-show="item.checkType === 3">相邻</span>
                  <span v-show="item.checkType === 4">不包含</span>
                  <span v-show="item.checkType === 5">包含</span>
                  <span v-show="item.checkType === 6">自相交</span>
                </el-tag>
              </div>
              <template v-if="!item.attribution || (item.attribution && !item.attribution.GeometryGroup)">
                <div class="examine-span" v-if="item.targetRuleId && item.attribution && item.attribution.direct">直属</div>
                <div class="examine-span" v-if="item.targetRuleId && (!item.attribution || (item.attribution && !item.attribution.direct))">所有</div>
                <!-- 两个参数都在的时候 -->
                <div class="examine-span" v-show="item.targetRuleId">
                  <el-tag size="small">{{ getYSName(ruleTree, item.targetRuleId) || '没有找到节点或者节点已经被删除' }}</el-tag>
                </div>
              </template>
              <div class="examine-span">
                的 <el-tag size="small">{{ item.insideFlag === 1 ? '内部' : '外部' }}</el-tag> 检查规则
              </div>
            </template>
            <!-- 字段条件检查 -->
            <template v-else>
              <div class="examine-span">的</div>
              <div class="examine-span">
                <el-tag size="small">{{ getAttrName(ruleTree, item.sourceRuleId, item.attribution.attrGroup) }}</el-tag>
              </div>
              <div class="examine-span">
                <span v-show="item.attribution.condition === 1">等于</span>
                <span v-show="item.attribution.condition === 2">不等于</span>
              </div>
              <div class="examine-span">
                <el-tag size="small">{{ item.attribution.value }}</el-tag>
              </div>
              <div class="examine-span">时</div>
              <div class="examine-span">
                <el-tag size="small">{{ getJdName(ruleTree, item.sourceRuleId, item.attribution.targetIds) }}</el-tag>
              </div>
              <div class="examine-span">节点不用创建</div>
            </template>
          </div>
        </div>
        <div class="examine-handle">
          <el-link type="primary" @click="editExamine(item)">编辑</el-link>
          <el-link type="danger" style="margin-left: 10px" @click="delExamine(item)">删除</el-link>
        </div>
      </div>
      <!-- 完成 -->
      <!-- <div class="btn-next-left" @click="handleOpenOrderDialog">排序</div>  -->
      <div class="btn-next-step">
        <el-button type="primary" @click="handleFinsh" size="small"
          >完成
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    <!-- 检查弹窗 -->
    <el-dialog
      :title="examineType === 1 ? '新增拓扑检查' : '修改拓扑检查'"
      v-model="examineDialog"
      width="90%"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div style="padding: 6px">请选择检查类型</div>
      <div style="display: flex; flex-direction: row">
        <el-select v-model="examineMsg.sourceType" placeholder="请选择" @change="handleChangeType" style="width: 240px">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="examineMsg.checkLevel" placeholder="请选择检查级别" style="width: 240px; margin-left: 10px">
          <el-option v-for="item in checkLevelList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </div>

      <!-- 图形类型 -->
      <div v-show="examineMsg.sourceType === 1">
        <div class="dialog-notes">1、源节点代表当前节点 目标节点代表比对的节点</div>
        <div class="dialog-notes">2、目标节点不是必填项</div>
        <div class="dialog-notes">3、当目标节点不填的时候只允许做不相交检查</div>
        <div class="dialog-notes">4、内部检查指只检查同一条数据下的源节点跟目标节点的相交关系</div>
        <div class="dialog-notes">5、外部检查指检查所有数据的源节点跟目标节点的相交关系</div>
        <div class="dialog-content" style="align-items: center">
          <div class="item">
            <el-cascader
              clearable
              style="width: 100%"
              v-model="examineMsg.sourceRuleId"
              :options="ruleTree"
              placeholder="请选择源节点"
              :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
              @change="(response) => handleChange(response, 1, true)"
            ></el-cascader>
          </div>
          <div class="item" v-show="!examineMsg.targetRuleId">
            <el-cascader
              clearable
              style="width: 100%"
              v-model="examineMsg.attribution.attrGroup"
              :options="fieldGroupModelList"
              placeholder="请选择字段"
              :props="{ children: 'children', value: 'id', label: 'label' }"
              @change="(response) => handleChangeField(response, 1, 2)"
            ></el-cascader>
          </div>
          <div class="item" style="margin-left: 12px">
            <el-select v-model="examineMsg.checkType" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in checkTypeList" :key="item.value" :label="item.title" :disabled="item.disebale" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="item" v-show="!examineMsg.attribution.attrGroup || examineMsg.attribution.attrGroup.length === 0">
            <el-cascader
              clearable
              style="width: 100%"
              v-model="examineMsg.targetRuleId"
              :options="ruleTree"
              placeholder="请选择目标节点"
              :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
              @change="(response) => handleChange(response, 2)"
            ></el-cascader>
          </div>
          <div class="item">
            <el-select v-model="examineMsg.insideFlag" placeholder="请选择检查范围">
              <el-option v-for="item in insideFlagList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="item" v-show="examineMsg.targetRuleId">
            <el-select v-model="examineMsg.attribution.direct" placeholder="请选择匹配目标类型" clearable>
              <el-option v-for="item in directList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="label">的检查规则</div>
        </div>
      </div>
      <!-- 字段类型 -->
      <div v-show="examineMsg.sourceType === 2">
        <div class="dialog-notes">1、源节点指需要验证的节点</div>
        <div class="dialog-notes">2、选择字段指源节点的某个字段去进行条件判断</div>
        <div class="dialog-notes">3、目标节点指提示哪些节点不用创建</div>
        <div class="dialog-notes">
          4、例如：当【楼层】节点下的【楼层信息】属性组的【所在层】等于【1】时，下面的子要素【阳台、楼梯、滴水线】不用创建
        </div>
        <div class="dialog-content">
          <div class="item">
            <el-cascader
              clearable
              style="width: 100%"
              v-model="examineMsg.sourceRuleId"
              :options="ruleTree"
              placeholder="请选择源节点"
              :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
              @change="(response) => handleChangeGroup(response, 1)"
            ></el-cascader>
          </div>
          <div class="item">
            <el-cascader
              clearable
              style="width: 100%"
              v-model="examineMsg.attribution.attrGroup"
              :options="fieldGroupModelList"
              placeholder="请选择字段"
              :props="{ children: 'children', value: 'id', label: 'label' }"
              @change="(response) => handleChangeField(response, 1)"
            ></el-cascader>
          </div>
          <div class="item">
            <el-select v-model="examineMsg.attribution.condition" placeholder="请选择运算条件" style="width: 100%">
              <el-option v-for="item in operationCondition" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="item">
            <el-input v-model="examineMsg.attribution.value" placeholder="请输入匹配的值" maxlength="30"></el-input>
          </div>
          <div class="item">
            <el-select v-model="examineMsg.attribution.targetIds" placeholder="请选择目标节点" multiple clearable>
              <el-option v-for="item in targetList" :key="item.id" :label="item.typeName" :value="item.id"> </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitExamine">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 排序弹框 -->
    <!-- <the-expression-order :orderVisible="orderVisible" @closeOrder="handleOrderClose"></the-expression-order> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { selectRules, addExamine, examineList } from '@/api/modal/index';
// import theExpressionOrder from '@/components/theExpressionOrder/index.vue';
import { useModalStore } from '@/store/modules/modal';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowRight } from '@element-plus/icons-vue';
import { Plus } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { param } from '@/utils';
const route = useRoute();
const modalStore = useModalStore();
const router = useRouter();

// 定义接口
interface CheckTypeItem {
  title: string;
  value: number;
  disebale: boolean;
}

interface TypeListItem {
  label: string;
  value: number;
}

interface OperationConditionItem {
  label: string;
  value: number;
}

interface CheckLevelListItem {
  label: string;
  value: number;
}

interface DirectListItem {
  label: string;
  value: boolean;
  disebale?: boolean;
}

interface InsideFlagListItem {
  label: string;
  value: number;
}

interface Attribution {
  attrGroup: any[];
  condition: string | number;
  value: string;
  targetIds: any[];
  GeometryGroup?: any;
  direct?: boolean;
}

interface ExamineMsg {
  checkType: number;
  operaType: number;
  sourceRuleId: string | any[];
  targetRuleId: string | any[];
  insideFlag: number;
  sourceType: number;
  attribution: Attribution;
  checkLevel: string | number;
  id?: number;
}

interface ExamineItem {
  sourceType: number;
  sourceRuleId: number;
  targetRuleId?: number;
  attribution: Attribution;
  checkType: number;
  insideFlag: number;
  delFlag?: number;
}

// 定义 props
const props = defineProps<{
  ruleTreeProp: any[];
}>();

// 定义响应式数据
const examineDialog = ref(false);
const examineType = ref(1);
const checkTypeList = ref<CheckTypeItem[]>([
  { title: '不相交', value: 1, disebale: false },
  { title: '相交', value: 2, disebale: true },
  { title: '相邻', value: 3, disebale: true },
  { title: '不包含', value: 4, disebale: true },
  { title: '包含', value: 5, disebale: true },
  { title: '自相交', value: 6, disebale: true }
]);
const examineMsg = ref<ExamineMsg>({
  checkType: 1,
  operaType: 1,
  sourceRuleId: '',
  targetRuleId: '',
  insideFlag: 2,
  sourceType: 1,
  attribution: {
    attrGroup: [],
    condition: '',
    value: '',
    targetIds: []
  },
  checkLevel: ''
});
const list = ref<ExamineItem[]>([]);
const typeList = ref<TypeListItem[]>([
  { label: '图形拓扑检查', value: 1 },
  { label: '字段条件检查', value: 2 }
]);
const fieldGroupModelList = ref<any[]>([]);
const operationCondition = ref<OperationConditionItem[]>([
  { label: '=', value: 1 },
  { label: '!=', value: 2 }
]);
const targetList = ref<any[]>([]);
const checkLevelList = ref<CheckLevelListItem[]>([
  { label: '警告', value: 1 },
  { label: '禁止', value: 2 }
]);
const directList = ref<DirectListItem[]>([
  { label: '所有目标节点', value: false, disebale: true },
  { label: '直属目标节点', value: true }
]);
const insideFlagList = ref<InsideFlagListItem[]>([
  { label: '内部检查', value: 1 },
  { label: '外部检查', value: 2 }
]);
const orderVisible = ref(false);
const ruleTree = computed({
  get: () => props.ruleTreeProp,
  set: (val) => {}
});

// 计算 moduleId
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = Number(router.currentRoute.value.query.id);
  }
  return moduleId;
});

// 定义方法
const getData = async () => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  try {
    const res = await examineList(params);
    if (res.code === 200) {
      list.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

const getTree = async () => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  try {
    const res = await selectRules(params);
    if (res.code === 200) {
      ruleTree.value = res.data;
      examineType.value = 1;
      fieldGroupModelList.value = [];
      examineDialog.value = true;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

const handleExamine = () => {
  getTree();
};

const handleClose = () => {
  examineMsg.value = {
    checkType: 1,
    operaType: 1,
    sourceRuleId: '',
    targetRuleId: '',
    insideFlag: 2,
    sourceType: 1,
    attribution: {
      attrGroup: [],
      condition: '',
      value: '',
      targetIds: []
    },
    checkLevel: ''
  };
  examineDialog.value = false;
};

const handleChange = (el: any[], val: number, type?: boolean) => {
  if (val === 2) {
    checkTypeList.value.forEach((v) => {
      v.disebale = false;
    });
  } else if (val === 1) {
    if (!examineMsg.value.targetRuleId) {
      checkTypeList.value.forEach((v) => {
        v.disebale = true;
      });
      checkTypeList.value[0].disebale = false;
    }
  }
  if (val === 2 && el.length === 0) {
    checkTypeList.value.forEach((v) => {
      v.disebale = true;
    });
    checkTypeList.value[0].disebale = false;
    examineMsg.value.checkType = 1;
  }
  if (type) {
    fieldGroupModelList.value = getAttrGroup(ruleTree.value, el[el.length - 1]);
  }
};

const submitExamine = async () => {
  if (!examineMsg.value.checkLevel) {
    ElMessage.error('请选择检查级别！！！');
    return;
  }
  if (!examineMsg.value.sourceRuleId) {
    ElMessage.error('请选择源要素！！！');
    return;
  }
  if (examineMsg.value.sourceType === 2) {
    if (examineMsg.value.attribution.attrGroup.length === 0) {
      ElMessage.error('请选择条件字段！！！');
      return;
    }
    if (!examineMsg.value.attribution.condition) {
      ElMessage.error('请选择运算条件！！！');
      return;
    }
    if (!examineMsg.value.attribution.value) {
      ElMessage.error('请输入运算匹配的值！！！');
      return;
    }
    if (examineMsg.value.attribution.targetIds.length === 0) {
      ElMessage.error('请选择目标节点！！！');
      return;
    }
  }
  const parmas: any = {
    checkType: examineMsg.value.checkType,
    operaType: examineMsg.value.operaType,
    targetRuleId: Array.isArray(examineMsg.value.targetRuleId)
      ? examineMsg.value.targetRuleId[examineMsg.value.targetRuleId.length - 1] || 0
      : examineMsg.value.targetRuleId,
    insideFlag: examineMsg.value.insideFlag,
    checkLevel: examineMsg.value.checkLevel
  };
  if (Array.isArray(examineMsg.value.sourceRuleId)) {
    parmas.sourceRuleId = examineMsg.value.sourceRuleId[examineMsg.value.sourceRuleId.length - 1] || 0;
  } else {
    parmas.sourceRuleId = examineMsg.value.sourceRuleId;
  }
  if (examineMsg.value.attribution && examineMsg.value.attribution.GeometryGroup) {
    parmas.targetRuleId = parmas.sourceRuleId;
    parmas.insideFlag = 1;
  }
  if (examineMsg.value.id) {
    parmas.id = examineMsg.value.id;
  }
  if (examineMsg.value.sourceType === 2) {
    parmas.sourceType = 2;
    parmas.insideFlag = 1;
  }
  parmas.attribution = examineMsg.value.attribution;
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    parmas.companyId = companyId;
  }
  addExamine([parmas]).then((res) => {
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
      getData();
      handleClose();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const getYSName = (tree: any[], id: number): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      return tree[i].typeName;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getYSName(tree[i].list, id);
      if (result) {
        return result;
      }
    }
  }
  return;
};

const editExamine = (item: ExamineItem) => {
  examineType.value = 2;
  examineMsg.value = { ...item, operaType: 2 };
  if (examineMsg.value.sourceType === 2) {
    fieldGroupModelList.value = getAttrGroup(props.ruleTree, examineMsg.value.sourceRuleId as number);
  }
  if (examineMsg.value.sourceType === 1) {
    examineMsg.value.attribution = item.attribution;
    if (!examineMsg.value.attribution) {
      examineMsg.value.attribution = {
        attrGroup: [],
        condition: '',
        value: '',
        targetIds: []
      };
    }
    if (examineMsg.value.attribution.GeometryGroup) {
      fieldGroupModelList.value = getAttrGroup(props.ruleTree, examineMsg.value.sourceRuleId as number);
      examineMsg.value.targetRuleId = '';
    }
  }
  examineDialog.value = true;
};

const delExamine = (item: ExamineItem) => {
  ElMessageBox.confirm('确定要删除该拓扑检查规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      item.delFlag = 1;
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        item.companyId = companyId;
      }
      try {
        const res = await addExamine([item]);
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '删除成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {}
    })
    .catch(() => {});
};

const handleFinsh = () => {
  //设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  modalStore.setNodeTree([]);
  if (companyId && companyId !== undefined && companyId !== null) {
    router.push({
      path: '/modal',
      query: {
        companyId: companyId
      }
    });
  } else {
    router.push('/modal');
  }
};

const handleClearRule = () => {};

const handleChangeGroup = (ele: any[], val: number) => {
  fieldGroupModelList.value = getAttrGroup(props.ruleTree, ele[ele.length - 1]);
};

const getAttrGroup = (tree_item: any[], id: number): any[] | undefined => {
  for (let i = 0; i < tree_item.length; i++) {
    if (tree_item[i].id === id) {
      const targetList: any[] = [];
      tree_item[i].list.forEach((k: any) => {
        targetList.push(k);
      });
      targetList.value = targetList;
      const resultList: any[] = [];
      tree_item[i].fieldGroupModelList.forEach((v: any) => {
        const item = {
          label: v.typeName,
          id: v.linkId,
          children: []
        };
        v.fieldModelList.forEach((k: any) => {
          item.children.push({
            label: k.fieldCn,
            id: k.fieldName
          });
        });
        resultList.push(item);
      });
      return resultList;
    }
    if (tree_item[i].list && tree_item[i].list.length > 0) {
      const result = getAttrGroup(tree_item[i].list, id);
      if (result) {
        return result;
      }
    }
  }
  return;
};

const handleChangeField = (ele: any[], val: number, type?: number) => {
  if (type === 2) {
    let fieldCn = '';
    for (let i = 0; i < fieldGroupModelList.value.length; i++) {
      if (fieldGroupModelList.value[i].id === ele[0]) {
        for (let j = 0; j < fieldGroupModelList.value[i].children.length; j++) {
          if (fieldGroupModelList.value[i].children[j].id === ele[1]) {
            fieldCn = fieldGroupModelList.value[i].children[j].label;
            break;
          }
        }
        break;
      }
    }
    const GeometryGroup = {
      fieldGroupModelList: {
        linkId: ele[0],
        fieldModelList: [
          {
            fieldName: ele[1],
            fieldCn: fieldCn
          }
        ]
      }
    };
    examineMsg.value.attribution.GeometryGroup = GeometryGroup;
  }
};

const getAttrName = (tree: any[], ruleId: number, attrGroup: any[]): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === ruleId) {
      let group: any;
      let fieldCn: string | undefined;
      for (let j = 0; j < tree[i].fieldGroupModelList.length; j++) {
        if (tree[i].fieldGroupModelList[j].linkId === attrGroup[0]) {
          group = tree[i].fieldGroupModelList[j];
          break;
        }
      }
      if (group) {
        for (let k = 0; k < group.fieldModelList.length; k++) {
          if (group.fieldModelList[k].fieldName === attrGroup[1]) {
            fieldCn = group.fieldModelList[k].fieldCn;
            break;
          }
        }
      }
      return `【${group?.typeName}】属性组的【${fieldCn}】字段`;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getAttrName(tree[i].list, ruleId, attrGroup);
      if (result) {
        return result;
      }
    }
  }
  return;
};

const getJdName = (tree: any[], ruleId: number, ids: any[]): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === ruleId) {
      const names: string[] = [];
      tree[i].list.forEach((v: any) => {
        if (ids.includes(v.id)) {
          names.push(v.typeName);
        }
      });
      return names.join(',');
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getJdName(tree[i].list, ruleId, ids);
      if (result) {
        return result;
      }
    }
  }
  return;
};

const handleChangeType = () => {
  examineMsg.value.sourceRuleId = '';
};

const handleOpenOrderDialog = () => {
  orderVisible.value = true;
};

const handleOrderClose = () => {
  orderVisible.value = false;
};

// 挂载时执行
onMounted(() => {
  // 可以在这里添加初始化逻辑
});

// 暴露给模板
defineExpose({
  getData,
  getTree,
  handleExamine,
  handleClose,
  handleChange,
  submitExamine,
  getYSName,
  editExamine,
  delExamine,
  handleFinsh,
  handleClearRule,
  handleChangeGroup,
  getAttrGroup,
  handleChangeField,
  getAttrName,
  getJdName,
  handleChangeType,
  handleOpenOrderDialog,
  handleOrderClose
});
</script>

<style lang="scss" scoped>
/* 样式部分保持不变 */
/*滚动条样式*/
.order-content::-webkit-scrollbar {
  width: 4px;
}
.order-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.order-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.spe-span {
  font-weight: bold;
}
.dialog-notes {
  display: flex;
  color: #ff4343;
}
.dialog-content {
  display: flex;
  flex-direction: row;
  // align-items: center;
  margin-top: 10px;
  .item {
    flex: 1;
    padding: 6px;
    max-height: 400px;
    overflow: auto;
    border-radius: 8px;
  }
  .label {
    padding: 0px 10px;
  }
}
.examineSetting-main {
  width: 100%;
  display: flex;
  justify-content: center;
  .content {
    width: 723px;
    background: #fff;
    position: relative;
    .item-title {
      position: absolute;
      top: -12px;
      left: 24px;
      right: 32px;
      display: flex;
      justify-content: space-between;
      .handle-title {
        // width: 80px;
        height: 16px;
        color: #333333;
        background-color: #fff;
        .text {
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          padding: 8px;
          font-weight: 600;
          vertical-align: top;
          &:hover {
            color: var(--current-color);
          }
        }
      }
    }
    // &:hover{
    //     border: 1px solid var(--current-color);
    //     // color: #0081ff;
    // }
    // &:hover .handle-title .text{
    //     color: var(--current-color);
    // }
    .examine-item {
      display: flex;
      margin: 12px 0;
      padding: 8px 12px 3px 12px;
      // height: 40px;
      // line-height: 40px;
      border-radius: 4px;
      background-color: #f6f7f8;
      display: flex;
      flex-direction: row;
      // align-items: center;
      width: calc(100% - 32px);
      margin-left: 16px;
      .examine-content {
        flex: 1;
        display: flex;
        flex-direction: row;
        // align-items: center;
        font-size: 14px;
        .examine-left {
          width: 100px;
        }
        .examine-center {
          flex: 1;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          align-items: center;
          .examine-span {
            margin-right: 4px;
            margin-bottom: 5px;
          }
        }
      }
      .examine-handle {
        width: 80px;
        text-align: right;
      }
    }
    .btn-next-step {
      position: absolute;
      bottom: 100px;
      right: 52px;
    }
    .btn-next-left {
      position: absolute;
      bottom: 16px;
      left: 16px;
      color: #f5f7fa;
      width: 40px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      cursor: pointer;
      font-size: 12px;
      border: 0.5px dashed #f5f7fa;
    }
  }
}
</style>
