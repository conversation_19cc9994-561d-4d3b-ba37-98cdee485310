<!-- 首页 -->
<template>
  <div class="index-main" v-loading.fullscreen.lock="fullscreenLoading">
    <div class="setting" @click="jumpSetting">
      <el-icon><Setting /></el-icon>
    </div>
    <div class="full-screen" @click="fullScreen" v-if="url">
      <el-icon><FullScreen /></el-icon>
    </div>
    <iframe :src="url" frameborder="0" style="width: 100%; height: 100%" v-if="url"></iframe>
    <div v-else class="empty-main">
      <img src="../assets/images/index_bg.png" class="img-div" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Setting, FullScreen } from '@element-plus/icons-vue';
import { getScreenList } from '@/api/dataScreen';
import { useUserStore } from '@/store/modules/user'; // 引入用户状态store

// 响应式数据
const url = ref('');
const fullscreenLoading = ref(false);
const screenList = ref<Array<any>>([]);
const value = ref('');

// 使用路由和store
const router = useRouter();
const userStore = useUserStore(); // 使用pinia的用户store

// 获取数据方法
const getData = () => {
  const searchMsg = {
    pageNo: 1,
    pageSize: 10000,
    moduleId: 0
  };

  fullscreenLoading.value = true;
  getScreenList(searchMsg).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      if (res.data.records.length == 0) {
        ElMessage.warning('未查询到数据大屏！！！');
        return;
      }
      screenList.value = res.data.records;

      // 查找默认大屏
      for (let i = 0; i < screenList.value.length; i++) {
        if (screenList.value[i].isDefault == 1) {
          value.value = screenList.value[i].id;
          break;
        }
      }

      // 如果没有默认大屏，使用第一个
      if (!value.value) {
        value.value = res.data.records[0].id;
      }

      url.value = `/preview/0/${value.value}`;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 切换大屏
const changeScreen = (val: string) => {
  url.value = `/preview/0/${val}`;
};

// 跳转设置页面
const jumpSetting = () => {
  router.push('/bigData');
};

// 跳转单独大屏
const fullScreen = () => {
  window.open(`/preview/0/${value.value}`);
};

// 生命周期钩子
onMounted(() => {
  // 从pinia store中获取权限
  const permissions = userStore.permissions;
  const permValue = 'sjdp';
  const all_permission = '*:*:*';

  if (!permissions.includes(all_permission) && !permissions.includes(permValue)) {
    // 不是最高权限 并且没有数据大屏权限 直接跳转个人中心
    router.push({ path: '/profile?activeName=first' }).catch(() => {});
  } else {
    const copyFooter = document.getElementById('copyFooter');
    if (copyFooter) {
      copyFooter.classList.add('remove-footer');
    }
    getData();
  }
});

// 组件卸载时
onUnmounted(() => {
  const copyFooter = document.getElementById('copyFooter');
  if (copyFooter) {
    copyFooter.classList.remove('remove-footer');
  }
});
</script>

<style lang="scss" scoped>
.setting {
  position: absolute;
  right: 26px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: white;
  padding: 4px;
  background: rgb(43, 51, 64);
  border-radius: 4px;
  z-index: 1;
  display: grid;
  place-items: center;
}
.full-screen {
  position: absolute;
  right: 66px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: white;
  padding: 4px;
  background: rgb(43, 51, 64);
  border-radius: 4px;
  display: grid;
  place-items: center;
}
:deep(.el-input--medium .el-input__inner) {
  background: transparent;
  border: transparent;
}
.content-sel {
  position: absolute;
  z-index: 99;
  top: 10px;
  left: 10px;
  width: 120px;
}
.index-main {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #dbe7ed;
}
.empty-main {
  background: transparent !important;
  background: #ffffff;
}
.img-div {
  width: 100%;
  height: auto;
}
/*滚动条样式*/
.empty-main::-webkit-scrollbar {
  width: 4px;
}
.empty-main::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.empty-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
