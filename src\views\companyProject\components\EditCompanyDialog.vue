<template>
  <el-dialog :title="`修改公司【${currentCompany.companyName}】`" v-model="dialogVisible" width="700px">
    <el-form :model="currentCompany" :rules="currentCompanyRule" ref="currentCompanyRef" label-position="top" class="demo-ruleForm">
      <el-row :gutter="16">
        <el-col :span="selectedCompanyType === 2 ? 12 : 24">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="currentCompany.companyName" placeholder="请输入公司名称" maxlength="30"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司类型" prop="companyType" v-if="selectedCompanyType == 2">
            <el-select v-model="currentCompany.companyType" placeholder="请选择" style="width: 100%">
              <el-option :value="1" label="公司"></el-option>
              <el-option :value="2" label="个人"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item prop="maxUser">
            <div class="label-main">
              <div class="label">最大成员数</div>
              <div>
                <el-radio-group v-model="currentCompany.maxSelect" @change="handleMaxSelectChange">
                  <el-radio :value="1">无限制</el-radio>
                  <el-radio :value="2">限制</el-radio>
                </el-radio-group>
              </div>
            </div>
            <el-input
              v-if="currentCompany.maxSelect == 2"
              v-model="currentCompany.maxUser"
              type="number"
              placeholder="请输入公司最大成员数"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="expireTime">
            <div class="label-main">
              <div class="label">过期时间</div>
              <div>
                <el-radio-group v-model="currentCompany.dateSelect" @change="handleDateSelectchange">
                  <el-radio :value="1">无限制</el-radio>
                  <el-radio :value="2">限制</el-radio>
                </el-radio-group>
              </div>
            </div>
            <el-date-picker
              v-if="currentCompany.dateSelect == 2"
              v-model="currentCompany.expireTime"
              type="date"
              format="YYYY-MM-DD"
              value-format="x"
              placeholder="选择日期"
              style="width: 100%"
              :disabled-date="disabledDate"
              @change="handleChangeDate"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="小程序Id">
            <el-input v-model="currentCompany.appId" placeholder="请输入小程序Id" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小程序密钥">
            <el-input v-model="currentCompany.appSecret" placeholder="请输入小程序密钥" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="登录源头">
            <el-select v-model="currentCompany.loginSource" multiple placeholder="请选择" style="width: 100%" clearable>
              <el-option v-for="item in loginSourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="maxUser" label="公司等级">
            <el-radio-group v-model="currentCompany.vipType" style="width: 100%">
              <el-radio :value="1">个人版</el-radio>
              <el-radio :value="2">专业版</el-radio>
              <el-radio :value="3">企业版</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="公司状态">
            <el-radio-group v-model="currentCompany.status" style="width: 100%">
              <el-radio :value="0">正常</el-radio>
              <el-radio :value="-2">暂停使用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否菜单私有">
            <el-radio-group v-model="currentCompany.selfMenu" style="width: 100%">
              <el-radio :value="0">否</el-radio>
              <el-radio :value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancleUpdateCompany">取 消</el-button>
        <el-button type="primary" @click="handleSubmitCompany">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { updateFromControl } from '@/api/control';

interface CompanyData {
  companyId: string;
  companyName: string;
  [key: string]: any;
}

const props = defineProps<{
  visible: boolean;
  companyData: CompanyData;
  selectedCompanyType: number;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();

const dialogVisible = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val)
});
const currentCompany = reactive<CompanyData>({
  maxSelect: props.companyData?.maxSelect ?? 1,
  dateSelect: props.companyData?.dateSelect ?? 1,
  selfMenu: props.companyData?.selfMenu ?? 0,
  companyId: props.companyData?.companyId ?? '',
  appId: props.companyData?.appId,
  appSecret: props.companyData?.appSecret,
  loginSource: props.companyData?.loginSource ?? [],
  companyName: props.companyData?.companyName ?? '',
  companyType: props.companyData?.companyType ?? 1,
  maxUser: props.companyData?.maxUser ?? 0,
  expireTime: props.companyData?.expireTime ?? 0,
  status: props.companyData?.status ?? 0,
  vipType: props.companyData?.vipType ?? 1
});
/**
 * 选择时间只能大于当前时间
 * @param time 当前的时间
 */
const disabledDate = (time: Date) => {
  // 禁用今天之前的日期
  return time.getTime() < new Date(new Date().setHours(0, 0, 0, 0)).getTime();
};
const loginSourceList = [
  { label: '网页', value: 'web' },
  { label: '小程序', value: 'wx' },
  { label: 'App', value: 'app' }
];

const currentCompanyRule = {
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  maxUser: [{ required: true, message: '请输入公司最大成员数', trigger: 'blur' }],
  expireTime: [{ required: true, message: '请选择公司过期时间', trigger: 'blur' }],
  companyType: [{ required: true, message: '请选择公司类型', trigger: 'blur' }]
};

const handleMaxSelectChange = (val: number) => {
  currentCompany.maxSelect = val;
  if (val === 1) {
    currentCompany.maxUser = *********;
  }
};

const handleDateSelectchange = (val: number) => {
  currentCompany.dateSelect = val;
  if (val === 1) {
    currentCompany.expireTime = 32505381325000;
    currentCompany.status = 0;
  }
};

/**
 * 取消编辑公式的弹框
 */
const handleCancleUpdateCompany = () => {
  Object.assign(currentCompany, {
    maxSelect: 1,
    dateSelect: 1,
    selfMenu: 0,
    companyId: '',
    appId: undefined,
    appSecret: undefined,
    loginSource: []
  });
  dialogVisible.value = false;
};
/***
 * 提交保存的方法
 */
const handleSubmitCompany = async () => {
  const currentCompanyRef = document.querySelector('.demo-ruleForm');
  if (!currentCompanyRef) return;

  try {
    currentCompany.loginSource = currentCompany.loginSource.join(',');
    const res = await updateFromControl(currentCompany);
    if (res.code === 200) {
      ElMessage.success('操作成功');
      dialogVisible.value = false;
      emit('success');
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('Update company failed:', error);
    ElMessage.error('更新公司信息失败');
  }
};

watch(
  () => props.companyData,
  (newVal) => {
    if (newVal) {
      Object.assign(currentCompany, {
        maxSelect: newVal.maxSelect ?? 1,
        dateSelect: newVal.dateSelect ?? 1,
        selfMenu: newVal.selfMenu ?? 0,
        companyId: newVal.companyId ?? '',
        appId: newVal.appId,
        appSecret: newVal.appSecret,
        loginSource:
          typeof newVal.loginSource === 'string' ? newVal.loginSource.split(',') : Array.isArray(newVal.loginSource) ? newVal.loginSource : [],
        companyName: newVal.companyName ?? '',
        companyType: newVal.companyType ?? 1,
        maxUser: newVal.maxUser ?? 0,
        expireTime: newVal.expireTime ?? 0,
        status: newVal.status ?? 0,
        vipType: newVal.vipType ?? 1
      });

      // Handle special cases for maxUser and expireTime
      if (newVal.maxUser === *********) {
        currentCompany.maxSelect = 1;
      } else {
        currentCompany.maxSelect = 2;
      }

      if (newVal.expireTime >= 32505381325000) {
        currentCompany.dateSelect = 1;
      } else {
        currentCompany.dateSelect = 2;
      }
    }
  },
  { immediate: true, deep: true }
);
/***
 * 当选中公司的时间，将公司状态修改为正常  也就是公司状态值为正常的情况
 * status 0 正常 -1 已过期  -2 暂停使用
 */
const handleChangeDate = (val: Date) => {
  currentCompany.status = 0;
};
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.companyData) {
      Object.assign(currentCompany, {
        maxSelect: props.companyData.maxSelect ?? 1,
        dateSelect: props.companyData.dateSelect ?? 1,
        selfMenu: props.companyData.selfMenu ?? 0,
        companyId: props.companyData.companyId ?? '',
        appId: props.companyData.appId,
        appSecret: props.companyData.appSecret,
        loginSource:
          typeof props.companyData.loginSource === 'string'
            ? props.companyData.loginSource.split(',')
            : Array.isArray(props.companyData.loginSource)
              ? props.companyData.loginSource
              : [],
        companyName: props.companyData.companyName ?? '',
        companyType: props.companyData.companyType ?? 1,
        maxUser: props.companyData.maxUser ?? 0,
        expireTime: props.companyData.expireTime ?? 0,
        status: props.companyData.status ?? 0,
        vipType: props.companyData.vipType ?? 1
      });

      // Handle special cases for maxUser and expireTime
      if (props.companyData.maxUser === *********) {
        currentCompany.maxSelect = 1;
      } else {
        currentCompany.maxSelect = 2;
      }

      if (props.companyData.expireTime >= 32505381325000) {
        currentCompany.dateSelect = 1;
      } else {
        currentCompany.dateSelect = 2;
      }
    }
  }
);
</script>

<style lang="scss" scoped>
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0;
}

.label-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  width: 100%;
  .label {
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    font-weight: 600;
  }
}
</style>
