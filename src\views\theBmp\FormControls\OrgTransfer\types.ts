export interface ConfigItem {
  label: string;
  value: string;
  children?: ConfigItem[];
  [key: string]: any;
}

export interface Config {
  type: string;
  title: string;
  data: ConfigItem[];
}

export interface OrgTransferProps {
  value: any[];
  type: string;
  title: string;
  show: boolean;
  searchable: boolean;
  maxNum: number;
}

export interface OrgTransferEmits {
  (e: 'update:show', value: boolean): void;
  (e: 'confirm', data: any[]): void;
}

export interface TreeNode {
  nodeId: string | number;
  memberType?: string;
  [key: string]: any;
}
