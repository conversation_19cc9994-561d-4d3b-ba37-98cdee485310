<!--  -->
<template>
  <div class="company-main">
    <div class="left">
      <div class="title">本公司信息</div>
      <div class="flex-row">
        <div class="label">公司资质证书：</div>
        <div class="image-container">
          <auth-img
            v-if="companyQualificationCertificate"
            :auth-src="`${baseUrl}/system/user/profile/downloadone/${companyQualificationCertificate}?att=1`"
            width="178px"
            height="178px"
            class="preview-image"
          />
          <el-upload
            v-else
            class="avatar-uploader"
            :headers="headers"
            name="files"
            :action="`${baseUrl}/qjt/file/multi/upload`"
            :show-file-list="false"
            :on-success="handleSuccessZZ"
            :before-upload="beforeUpload"
          >
            <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div v-if="companyQualificationCertificate" class="upload-btn">
            <el-button type="primary" @click="triggerUploadZZ">更换资质证书</el-button>
            <input ref="uploadZZRef" type="file" class="hidden-input" accept="image/jpeg,image/png" @change="handleFileChangeZZ" />
          </div>
        </div>
      </div>
      <div class="flex-row">
        <div class="label">公司营业执照：</div>
        <div class="image-container">
          <auth-img
            v-if="companyBusinessLicense"
            :auth-src="`${baseUrl}/system/user/profile/downloadone/${companyBusinessLicense}?att=1`"
            width="178px"
            height="178px"
            class="preview-image"
          />
          <el-upload
            v-else
            class="avatar-uploader"
            :headers="headers"
            name="files"
            :action="`${baseUrl}/qjt/file/multi/upload`"
            :show-file-list="false"
            :on-success="handleSuccessYYZZ"
            :before-upload="beforeUpload"
          >
            <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div v-if="companyBusinessLicense" class="upload-btn">
            <el-button type="primary" @click="triggerUploadYYZZ">更换营业执照</el-button>
            <input ref="uploadYYZZRef" type="file" class="hidden-input" accept="image/jpeg,image/png" @change="handleFileChangeYYZZ" />
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="title">
        <span>关联公司列表</span>
        <el-link link type="primary" style="margin-left: 10px" @click="addCompany">添加关联公司</el-link>
      </div>
      <el-table :data="sysCompanyChildList" border style="width: 100%">
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="公司名称" prop="companyName"></el-table-column>
        <el-table-column label="公司类型">
          <template #default="{ row }">
            <span v-show="row.companyType == 1">公司</span>
            <span v-show="row.companyType == 2">个人</span>
          </template>
        </el-table-column>
        <el-table-column label="公司资质证书">
          <template #default="{ row }">
            <el-image
              preview-teleported
              style="width: 40px; height: 40px"
              :src="`${base}/qjt/file/otherDownload/${row.companyQualificationCertificate}?token=${token}`"
              :fit="`cover`"
              :preview-src-list="[`${base}/qjt/file/otherDownload/${row.companyQualificationCertificate}?token=${token}`]"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column label="公司营业执照">
          <template #default="{ row }">
            <el-image
              preview-teleported
              style="width: 40px; height: 40px"
              :src="`${base}/qjt/file/otherDownload/${row.companyBusinessLicense}?token=${token}`"
              :fit="`cover`"
              :preview-src-list="[`${base}/qjt/file/otherDownload/${row.companyBusinessLicense}?token=${token}`]"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row, $index }">
            <el-link type="primary" link @click="edit(row)">编辑</el-link>
            <el-link style="margin-left: 10px" type="danger" @click="del($index)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="footer-btn">
        <el-button type="primary" size="mini">提交</el-button>
    </div> -->
    <el-dialog v-model="editCompanyDialog" title="公司信息" width="600px" :before-close="handleClose">
      <el-form ref="companyFormRef" :model="company" :rules="companyRules" label-width="120px" class="demo-ruleForm">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="company.companyName" placeholder="请输入公司名称"></el-input>
        </el-form-item>
        <el-form-item label="公司资质证书" prop="companyQualificationCertificate">
          <div class="image-container">
            <auth-img
              v-if="company.companyQualificationCertificate"
              :auth-src="`${baseUrl}/system/user/profile/downloadone/${company.companyQualificationCertificate}?att=1`"
              width="178px"
              height="178px"
              class="preview-image"
            />
            <el-upload
              v-else
              class="avatar-uploader"
              :headers="headers"
              name="files"
              :action="`${baseUrl}/qjt/file/multi/upload`"
              :show-file-list="false"
              :on-success="(response) => handleSuccess(response, 1)"
              :before-upload="beforeUpload"
            >
              <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div v-if="company.companyQualificationCertificate" class="upload-btn">
              <el-button type="primary" @click="triggerUploadZZDialog">更换资质证书</el-button>
              <input ref="uploadZZDialogRef" type="file" class="hidden-input" accept="image/jpeg,image/png" @change="handleFileChangeZZDialog" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="公司营业执照" prop="companyBusinessLicense">
          <div class="image-container">
            <auth-img
              v-if="company.companyBusinessLicense"
              :auth-src="`${baseUrl}/system/user/profile/downloadone/${company.companyBusinessLicense}?att=1`"
              width="178px"
              height="178px"
              class="preview-image"
            />
            <el-upload
              v-else
              class="avatar-uploader"
              :headers="headers"
              name="files"
              :action="`${baseUrl}/qjt/file/multi/upload`"
              :show-file-list="false"
              :on-success="(response) => handleSuccess(response, 2)"
              :before-upload="beforeUpload"
            >
              <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div v-if="company.companyBusinessLicense" class="upload-btn">
              <el-button type="primary" @click="triggerUploadYYZZDialog">更换营业执照</el-button>
              <input ref="uploadYYZZDialogRef" type="file" class="hidden-input" accept="image/jpeg,image/png" @change="handleFileChangeYYZZDialog" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="公司类型">
          <el-select v-model="company.companyType" placeholder="请选择公司类型">
            <el-option v-for="item in companyTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editCompanyDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitCompany">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';
import { editMyCompany, getCompany } from '@/api/control';
import { useUserStore } from '@/store/modules/user';
import AuthImg from '@/components/authImg/index.vue';

// 添加防抖函数
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void {
  let timer: number | null = null;
  return function (this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

interface CompanyType {
  label: string;
  value: number;
}

interface Company {
  mainCompanyId: string;
  companyQualificationCertificate: string;
  companyBusinessLicense: string;
  companyName: string;
  companyType: number;
  isEdit?: boolean;
}

const userStore = useUserStore();
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const base = import.meta.env.VITE_APP_BASE_API;
const token = getToken();
const isLoading = ref(false);
const uploadZZRef = ref<HTMLInputElement | null>(null);
const uploadYYZZRef = ref<HTMLInputElement | null>(null);
const uploadZZDialogRef = ref<HTMLInputElement | null>(null);
const uploadYYZZDialogRef = ref<HTMLInputElement | null>(null);

const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};

const companyFormRef = ref<FormInstance>();
const editCompanyDialog = ref(false);
const companyQualificationCertificate = ref('');
const companyBusinessLicense = ref('');
const sysCompanyChildList = ref<Company[]>([]);
const mainCompanyId = ref('');
const allCompany = ref<any>({});

const companyTypeList: CompanyType[] = [
  { label: '企业', value: 1 },
  { label: '个人', value: 2 }
];

const company = reactive<Company>({
  mainCompanyId: '',
  companyQualificationCertificate: '',
  companyBusinessLicense: '',
  companyName: '',
  companyType: 1
});

const companyRules = {
  companyQualificationCertificate: [{ required: true, message: '请上传资质证书', trigger: 'change' }],
  companyBusinessLicense: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }]
};

const getData = debounce(async () => {
  if (isLoading.value) {
    ElMessage.warning('数据正在处理，请勿重复提交');
    return;
  }
  try {
    isLoading.value = true;
    const res = await getCompany();
    if (res.code === 200) {
      allCompany.value = res.data;
      companyQualificationCertificate.value = res.data.companyQualificationCertificate;
      companyBusinessLicense.value = res.data.companyBusinessLicense;
      sysCompanyChildList.value = res.data.sysCompanyChildList || [];
      mainCompanyId.value = res.data.companyId;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
}, 600);

const handleSuccess = (res: any, type: number) => {
  if (res.data) {
    if (type === 1) {
      company.companyQualificationCertificate = res.data[0].path;
    } else if (type === 2) {
      company.companyBusinessLicense = res.data[0].path;
    }
  } else {
    ElMessage.error(res.msg);
  }
};

const handleSuccessZZ = async (res: any) => {
  if (isLoading.value) {
    ElMessage.warning('数据正在处理，请勿重复提交');
    return;
  }
  isLoading.value = true;
  if (res.data) {
    try {
      const params = {
        companyQualificationCertificate: res.data[0].path,
        companyId: userStore.user.companyId
      };
      const result = await editMyCompany(params);
      if (result.code === 200) {
        companyQualificationCertificate.value = res.data[0].path;
        ElMessage.success('操作成功');
      } else {
        ElMessage.error(result.msg);
      }
    } catch (error) {
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  } else {
    ElMessage.error(res.msg);
    isLoading.value = false;
  }
};

const beforeUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 10;

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 10MB!');
  }
  return isJPG && isLt2M;
};

const handleSuccessYYZZ = async (res: any) => {
  if (isLoading.value) {
    ElMessage.warning('数据正在处理，请勿重复提交');
    return;
  }
  isLoading.value = true;
  if (res.data) {
    try {
      const params = {
        companyBusinessLicense: res.data[0].path,
        companyId: userStore.user.companyId
      };
      const result = await editMyCompany(params);
      if (result.code === 200) {
        companyBusinessLicense.value = res.data[0].path;
        ElMessage.success('操作成功');
      } else {
        ElMessage.error(result.msg);
      }
    } catch (error) {
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  } else {
    ElMessage.error(res.msg);
    isLoading.value = false;
  }
};

const handleClose = () => {
  editCompanyDialog.value = false;
};

const addCompany = () => {
  Object.assign(company, {
    mainCompanyId: mainCompanyId.value,
    companyQualificationCertificate: '',
    companyBusinessLicense: '',
    companyName: '',
    companyType: 1
  });
  editCompanyDialog.value = true;
};

const submitCompany = debounce(async () => {
  // 检查是否加载中或表单引用不存在
  if (isLoading.value) {
    ElMessage.warning('数据正在处理，请勿重复提交');
    return;
  }

  if (!companyFormRef.value) return;

  // 判断添加的关联公司不能跟主公司名字一样
  if (company.companyName === allCompany.value.companyName) {
    ElMessage.error('关联公司不能跟主公司名字一样!!!');
    return;
  }

  isLoading.value = true;
  try {
    const valid = await companyFormRef.value.validate();
    if (valid) {
      if (!company.isEdit) {
        sysCompanyChildList.value.push({ ...company });
      }
      allCompany.value.sysCompanyChildList = sysCompanyChildList.value;
      const res = await editMyCompany(allCompany.value);
      if (res.code === 200) {
        editCompanyDialog.value = false;
        ElMessage.success('操作成功');
        getData();
      } else {
        ElMessage.error(res.msg);
      }
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  } finally {
    isLoading.value = false;
  }
}, 700);

const edit = (row: Company) => {
  Object.assign(company, row);
  company.isEdit = true;
  editCompanyDialog.value = true;
};

const del = debounce(async (index: number) => {
  if (isLoading.value) {
    ElMessage.warning('数据正在处理，请勿重复提交');
    return;
  }

  // 检查是否至少有一个关联公司
  if (sysCompanyChildList.value.length <= 1) {
    ElMessage.error('关联公司不能少于1个!!!');
    return;
  }

  isLoading.value = true;
  try {
    await ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    allCompany.value.sysCompanyChildList.splice(index, 1);
    const res = await editMyCompany(allCompany.value);
    if (res.code === 200) {
      editCompanyDialog.value = false;
      ElMessage.success('删除成功!');
      getData();
    } else {
      ElMessage.error(res.msg);
    }
  } catch {
    // 用户取消删除操作
  } finally {
    isLoading.value = false;
  }
}, 600);

// 触发资质证书文件选择
const triggerUploadZZ = () => {
  if (uploadZZRef.value) {
    uploadZZRef.value.click();
  }
};

// 处理资质证书文件上传
const handleFileChangeZZ = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  if (beforeUpload(file)) {
    const formData = new FormData();
    formData.append('files', file);

    if (isLoading.value) {
      ElMessage.warning('数据正在处理，请勿重复提交');
      return;
    }

    isLoading.value = true;
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${baseUrl}/qjt/file/multi/upload`, true);

      // 添加headers
      xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`);
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*');

      xhr.onload = async () => {
        if (xhr.status === 200) {
          const res = JSON.parse(xhr.responseText);
          if (res.data) {
            const params = {
              companyQualificationCertificate: res.data[0].path,
              companyId: userStore.user.companyId
            };
            const result = await editMyCompany(params);
            if (result.code === 200) {
              companyQualificationCertificate.value = res.data[0].path;
              ElMessage.success('操作成功');
            } else {
              ElMessage.error(result.msg);
            }
          } else {
            ElMessage.error(res.msg);
          }
        } else {
          ElMessage.error('上传失败');
        }
        isLoading.value = false;
      };

      xhr.onerror = () => {
        ElMessage.error('上传失败');
        isLoading.value = false;
      };

      xhr.send(formData);
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  }

  // 清空文件选择，以便下次选择同一文件时也能触发change事件
  if (uploadZZRef.value) {
    uploadZZRef.value.value = '';
  }
};

// 触发营业执照文件选择
const triggerUploadYYZZ = () => {
  if (uploadYYZZRef.value) {
    uploadYYZZRef.value.click();
  }
};

// 处理营业执照文件上传
const handleFileChangeYYZZ = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  if (beforeUpload(file)) {
    const formData = new FormData();
    formData.append('files', file);

    if (isLoading.value) {
      ElMessage.warning('数据正在处理，请勿重复提交');
      return;
    }

    isLoading.value = true;
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${baseUrl}/qjt/file/multi/upload`, true);

      // 添加headers
      xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`);
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*');

      xhr.onload = async () => {
        if (xhr.status === 200) {
          const res = JSON.parse(xhr.responseText);
          if (res.data) {
            const params = {
              companyBusinessLicense: res.data[0].path,
              companyId: userStore.user.companyId
            };
            const result = await editMyCompany(params);
            if (result.code === 200) {
              companyBusinessLicense.value = res.data[0].path;
              ElMessage.success('操作成功');
            } else {
              ElMessage.error(result.msg);
            }
          } else {
            ElMessage.error(res.msg);
          }
        } else {
          ElMessage.error('上传失败');
        }
        isLoading.value = false;
      };

      xhr.onerror = () => {
        ElMessage.error('上传失败');
        isLoading.value = false;
      };

      xhr.send(formData);
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  }

  // 清空文件选择，以便下次选择同一文件时也能触发change事件
  if (uploadYYZZRef.value) {
    uploadYYZZRef.value.value = '';
  }
};

// 触发弹窗资质证书文件选择
const triggerUploadZZDialog = () => {
  if (uploadZZDialogRef.value) {
    uploadZZDialogRef.value.click();
  }
};

// 处理弹窗资质证书文件上传
const handleFileChangeZZDialog = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  if (beforeUpload(file)) {
    const formData = new FormData();
    formData.append('files', file);

    if (isLoading.value) {
      ElMessage.warning('数据正在处理，请勿重复提交');
      return;
    }

    isLoading.value = true;
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${baseUrl}/qjt/file/multi/upload`, true);

      // 添加headers
      xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`);
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*');

      xhr.onload = async () => {
        if (xhr.status === 200) {
          const res = JSON.parse(xhr.responseText);
          if (res.data) {
            company.companyQualificationCertificate = res.data[0].path;
            ElMessage.success('上传成功');
          } else {
            ElMessage.error(res.msg);
          }
        } else {
          ElMessage.error('上传失败');
        }
        isLoading.value = false;
      };

      xhr.onerror = () => {
        ElMessage.error('上传失败');
        isLoading.value = false;
      };

      xhr.send(formData);
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  }

  // 清空文件选择，以便下次选择同一文件时也能触发change事件
  if (uploadZZDialogRef.value) {
    uploadZZDialogRef.value.value = '';
  }
};

// 触发弹窗营业执照文件选择
const triggerUploadYYZZDialog = () => {
  if (uploadYYZZDialogRef.value) {
    uploadYYZZDialogRef.value.click();
  }
};

// 处理弹窗营业执照文件上传
const handleFileChangeYYZZDialog = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  if (beforeUpload(file)) {
    const formData = new FormData();
    formData.append('files', file);

    if (isLoading.value) {
      ElMessage.warning('数据正在处理，请勿重复提交');
      return;
    }

    isLoading.value = true;
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${baseUrl}/qjt/file/multi/upload`, true);

      // 添加headers
      xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`);
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*');

      xhr.onload = async () => {
        if (xhr.status === 200) {
          const res = JSON.parse(xhr.responseText);
          if (res.data) {
            company.companyBusinessLicense = res.data[0].path;
            ElMessage.success('上传成功');
          } else {
            ElMessage.error(res.msg);
          }
        } else {
          ElMessage.error('上传失败');
        }
        isLoading.value = false;
      };

      xhr.onerror = () => {
        ElMessage.error('上传失败');
        isLoading.value = false;
      };

      xhr.send(formData);
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  }

  // 清空文件选择，以便下次选择同一文件时也能触发change事件
  if (uploadYYZZDialogRef.value) {
    uploadYYZZDialogRef.value.value = '';
  }
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409eff;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: #d9d9d9 1px dashed;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.company-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  .left {
    width: 400px;
    display: flex;
    flex-direction: column;
    .title {
      font-size: 16px;
      color: #161d26;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .flex-row {
      margin-bottom: 10px;
      display: flex;
      flex-direction: row;
      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        color: #161d26;
      }
    }
  }
  .right {
    flex: 1;
    .title {
      color: #161d26;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
  .footer-btn {
    margin-left: 200px;
  }
}

.image-container {
  position: relative;
  .preview-image {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }
  .upload-btn {
    margin-top: 8px;
    text-align: center;
  }
}

.hidden-input {
  display: none;
}
</style>
