/**
 * 项目查询参数
 */
export interface ProjectParams {
  [key: string]: any;
}

/**
 * 日志查询参数
 */
export interface LogParams {
  [key: string]: any;
}

/**
 * 项目删除参数
 */
export interface DelProjectParams {
  [key: string]: any;
}

/**
 * 项目合并参数
 */
export interface CombineProjectParams {
  [key: string]: any;
}

/**
 * 项目查询参数
 */
export interface ProjectQueryParams {
  [key: string]: any;
}

/**
 * 项目状态参数
 */
export interface ProjectStatusParams {
  type: string;
  projectId: string | number;
}

/**
 * 项目ID参数
 */
export interface ProjectIdParams {
  pid: string | number;
}

/**
 * 项目用户参数
 */
export interface ProjectUserParams {
  [key: string]: any;
}

/**
 * 任务参数
 */
export interface TaskParams {
  [key: string]: any;
}

/**
 * 任务状态参数
 */
export interface TaskStatusParams {
  [key: string]: any;
}

/**
 * 项目SHP参数
 */
export interface ProjectSHPParams {
  [key: string]: any;
}

/**
 * 项目名称参数
 */
export interface ProjectNameParams {
  [key: string]: any;
}

/**
 * 指界参数
 */
export interface ZJParams {
  [key: string]: any;
}
