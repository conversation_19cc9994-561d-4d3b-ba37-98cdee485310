import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { getToken } from '@/utils/auth';
import { LoginParams, RegisterParams, CompanyParams } from '@/api/login/types';

/**
 * 登录方法
 * @param username 用户名
 * @param password 密码
 * @param captcha 验证码
 * @param uuid 验证码标识
 * @param companyId 公司ID
 * @returns {AxiosPromise}
 */
export function login(username: string, password: string, captcha: string, uuid: string, companyId?: string | number): AxiosPromise<any> {
  const data: LoginParams = {
    username,
    password,
    captcha,
    uuid,
    companyId,
    from: 'web'
  };
  return request({
    url: '/system/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  });
}

/**
 * 短信+验证码++图形验证码
 * @param captcha 验证码
 * @param uuid 验证码标识
 * @param companyId 公司ID
 * @param mobile 手机号
 * @returns {Promise<AxiosPromise>}
 */
export async function loginMessage(captcha: string, uuid: string, companyId: string | number, mobile: string): Promise<AxiosPromise<any>> {
  const data: LoginParams = {
    mobile: mobile,
    captcha: captcha,
    companyId: companyId,
    from: 'web'
  };
  return request({
    url: '/system/login/sms',
    method: 'post',
    data
  });
}

/**
 * 注册方法
 * @param data 注册数据
 * @returns {AxiosPromise}
 */
export function register(data: RegisterParams): AxiosPromise<any> {
  return request({
    url: '/system/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  });
}

/**
 * 管理后台注册公司接口
 * @param data 公司数据
 * @returns {AxiosPromise}
 */
export function addCompany(data: CompanyParams): AxiosPromise<any> {
  return request({
    url: '/system/company/addCompany',
    method: 'post',
    data: data
  });
}

/**
 * 获取用户详细信息
 * @returns {AxiosPromise}
 */
export function getInfo(): AxiosPromise<any> {
  return request({
    url: '/system/user/getInfo',
    method: 'get',
    headers: { 'Authorization': getToken() }
  });
}

/**
 * 退出方法
 * @returns {AxiosPromise}
 */
export function logout(): AxiosPromise<any> {
  return request({
    url: '/system/logout',
    method: 'delete'
  });
}

/**
 * 获取验证码
 * @returns {AxiosPromise}
 */
export function getCodeImg(): AxiosPromise<any> {
  return request({
    url: '/system/getCaptcha',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000
  });
}

/**
 * 通过手机号获取所在公司
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getCompanyList(params: CompanyParams): AxiosPromise<any> {
  return request({
    url: '/system/companyList',
    method: 'post',
    data: params
  });
}

/**
 * 通过手机号获取所在公司
 * @param phone 手机号
 * @returns {AxiosPromise}
 */
export function getOrgizationList(phone: string): AxiosPromise<any> {
  return request({
    url: `/system/user/companyListDetail/${phone}`,
    method: 'get'
  });
}

/**
 * 根据手机号获取短信验证码
 * @param params 参数
 * @returns {AxiosPromise}
 */
export function getMessageCode(params: LoginParams): AxiosPromise<any> {
  return request({
    url: '/system/smscode',
    method: 'post',
    data: params
  });
}

/**
 * 忘记密码
 * @param params 参数
 * @returns {AxiosPromise}
 */
export function forgetPassword(params: LoginParams): AxiosPromise<any> {
  return request({
    url: '/system/forgetpwd',
    method: 'post',
    data: params
  });
}

/**
 * 公共登录
 * @param params 参数
 * @returns {AxiosPromise}
 */
export function loginFirst(params: LoginParams): AxiosPromise<any> {
  return request({
    url: '/system/loginFirst',
    method: 'post',
    data: params
  });
}

/**
 * 通过手机号获取公司列表
 * @param params 手机号
 * @returns {AxiosPromise}
 */
export function companyList(params: string): AxiosPromise<any> {
  return request({
    url: '/system/user/companyListDetail/' + params,
    method: 'get'
  });
}

/**
 * 切换公司
 * @param params 参数
 * @returns {AxiosPromise}
 */
export function switchCompany(params: CompanyParams): AxiosPromise<any> {
  return request({
    url: '/system/switchCompany',
    method: 'post',
    data: params
  });
}

/**
 * 直接注册接口 适用于问卷调查模块
 * @param params 参数
 * @returns {AxiosPromise}
 */
export function directRegister(params: RegisterParams): AxiosPromise<any> {
  return request({
    url: '/system/register/direct',
    method: 'post',
    data: params
  });
}
