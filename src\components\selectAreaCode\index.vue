<!-- 省市区带反选 -->
<template>
  <div class="selectAreaCode-main">
    <el-select v-model="provinceCode" filterable placeholder="请选择省" style="width: 100px" @change="changePro">
      <el-option v-for="item in provinceList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
    </el-select>
    <el-select v-model="cityCode" filterable placeholder="请选择市" style="margin-left: 10px; width: 100px" @change="changeCity">
      <el-option v-for="item in cityList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
    </el-select>
    <el-select v-model="areaCode" filterable placeholder="请选择区" style="margin-left: 10px; width: 100px" @change="changeArea">
      <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getAreaCode } from '@/api/project';
import { ElMessage } from 'element-plus';

// 定义区域项接口
interface AreaItem {
  areaCode: string;
  areaName: string;
  fullName?: string;
  fullCode?: string;
  [key: string]: any;
}

// 定义props
interface Props {
  defaultCode?: string | null;
}

const props = defineProps<Props>();

// 定义emit
const emit = defineEmits<{
  (e: 'submitCode', code: string, fullName: string): void;
}>();

// 响应式数据
const provinceCode = ref<string | null>(null);
const provinceName = ref<string | null>(null);
const cityCode = ref<string | null>(null);
const cityName = ref<string | null>(null);
const areaCode = ref<string | null>(null);
const areaName = ref<string | null>(null);
const provinceList = ref<AreaItem[]>([]);
const cityList = ref<AreaItem[]>([]);
const areaList = ref<AreaItem[]>([]);
const fullName = ref<string | null>(null);

// 获取省份数据
const getProvince = async () => {
  try {
    const res = await getAreaCode('86');
    if (res.code === 200) {
      provinceList.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取省份数据失败', error);
  }
};

// 根据代码初始化选择
const init = async (code: string) => {
  if (!code) return;

  try {
    const res = await getAreaCode(code);
    if (res.code === 200) {
      const fullCode = res.data[0].fullCode.split(',');
      if (fullCode.length === 4) {
        provinceCode.value = fullCode[0];
        cityCode.value = fullCode[1];
        areaCode.value = fullCode[2];
        await changePro(provinceCode.value, true);
        await changeCity(cityCode.value, true);
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('初始化选择失败', error);
  }
};

// 当省份改变时
const changePro = async (val: string | null, isInit?: boolean) => {
  if (!val) return;

  try {
    const res = await getAreaCode(val);
    if (res.code === 200) {
      cityList.value = res.data;
      if (!isInit) {
        cityCode.value = null;
        areaCode.value = null;
        areaList.value = [];
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('省份变更处理失败', error);
  }
};

// 当城市改变时
const changeCity = async (val: string | null, isInit?: boolean) => {
  if (!val) return;

  try {
    const res = await getAreaCode(val);
    if (res.code === 200) {
      areaList.value = res.data;
      if (!isInit) {
        areaCode.value = null;
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('城市变更处理失败', error);
  }
};

// 当区域改变时
const changeArea = (val: string | null) => {
  if (!val) return;

  for (let i = 0; i < areaList.value.length; i++) {
    if (areaList.value[i].areaCode === val) {
      fullName.value = areaList.value[i].fullName || '';
      break;
    }
  }

  emit('submitCode', val, fullName.value || '');
};

// 初始化时获取省份列表
onMounted(() => {
  getProvince();
});

// 暴露方法给父组件
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.selectAreaCode-main {
  display: flex;
}
</style>
