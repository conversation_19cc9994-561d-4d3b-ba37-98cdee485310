import { createSvgIconsPlugin } from 'vite-plugin-svg-icons-ng';
import path from 'path';

// 定义 SVG 图标目录
const SVG_DIRS = ['common', 'approvalSvg', 'fieldSvg', 'groupSvg', 'modalSvg', 'treeSvg', 'dataScreen'];

export default () => {
  // 项目根目录
  const rootPath = path.resolve(__dirname, '../../');

  // 生成完整的图标目录路径
  const iconDirs = SVG_DIRS.map((dir) => path.resolve(rootPath, `src/assets/icons/svg/${dir}`));

  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs,
    // 指定symbolId格式
    symbolId: 'icon-[name]',
    // 是否压缩
    svgoOptions: {},
    // 是否在开发环境下启用
    inject: 'body-first',
    // 是否在开发环境下启用
    customDomId: '__svg__icons__dom__'
  });
};
