/**
 * 表单参数
 */
export interface FormParams {
  [key: string]: any;
}

/**
 * 流程定义参数
 */
export interface ProcessDefinitionParams {
  [key: string]: any;
}

/**
 * 流程实例参数
 */
export interface ProcessInstanceParams {
  [key: string]: any;
}

/**
 * 部门查询参数
 */
export interface DeptQuery {
  [key: string]: any;
}

/**
 * 用户查询参数
 */
export interface UserQuery {
  [key: string]: any;
}

/**
 * 任务查询参数
 */
export interface TaskQuery {
  [key: string]: any;
}

/**
 * 审批参数
 */
export interface ApproveParams {
  [key: string]: any;
}

/**
 * 流程分组参数
 */
export interface ProcessGroupParams {
  [key: string]: any;
}
