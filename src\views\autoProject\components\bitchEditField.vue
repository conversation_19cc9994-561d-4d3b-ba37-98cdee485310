<!-- 批量修改字段 -->
<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="批量修改字段"
      v-model="bitchUpdateFieldDialogCopy"
      width="90%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      append-to-body
    >
      <el-form :model="formMsg" ref="formMsgRef" :rules="formMsgRule" label-width="100px" label-position="top">
        <div class="dialog-row">
          <div class="dialog-left">
            <div class="notice">
              <span
                >注意事项：批量修改字段只适用于选择的该批数据的字段值都是一样的情况，且不允许修改表达式字段以及修改的字段如果影响其他字段（如：其他字段用表达式引用了）请自行刷新表达式！！！</span
              >
              <br />
              <span>如果您选择的数据不是同一个根节点，可能导致数据无法更新，请注意一次只能选择同一根节点的数据！！！</span>
              <span>如果您修改的属性组里面没有任何内容，则无法进行更新！！！</span>
            </div>
            <el-form-item label="数据选择" prop="zdList">
              <div class="dialog-search">
                <div class="left-section">
                  <el-button type="primary" size="small" @click="searchBtn">筛选数据</el-button>
                  <span v-if="selectedIds.length > 0" class="selected-info">
                    已选择 {{ selectedIds.length }} 条数据
                    <el-button type="text" @click="clearSelection">清空选择</el-button>
                  </span>
                </div>
              </div>
              <div class="table-container">
                <el-table-v2
                  :columns="columns"
                  :data="formMsg.zdList"
                  :width="tableWidth"
                  :height="405"
                  fixed
                  :row-class="getRowClass"
                  @row-click="handleRowClick"
                  @select="handleSelectTable"
                  @select-all="handleSeletionAll"
                />
              </div>
              <div class="page">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="dialogSearch.pageNum"
                  :page-sizes="[20, 50, 100, 500, 1000, 2000, 5000]"
                  :page-size="dialogSearch.pageSize"
                  layout="total, sizes,  pager"
                  :total="total"
                >
                </el-pagination>
              </div>
            </el-form-item>
          </div>
          <div class="dialog-right">
            <el-form-item label="属性组/字段选择（请先选择数据！！！）" prop="tableList">
              <div class="add-btn">
                <el-link type="danger" :disabled="multipleSelection.length == 0" @click="delTable" style="margin-right: 10px">删除</el-link>
                <el-link type="primary" :disabled="multipleSelection.length == 0" @click="addTable" style="margin-right: 10px">新增修改字段</el-link>
                <el-link type="success" :disabled="multipleSelection.length == 0" @click="bitchAdd">批量增加字段选择</el-link>
              </div>
              <el-table :data="formMsg.tableList" :height="tableHeight" style="width: 100%" @selection-change="handleSelectionChange" border>
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column type="index" width="60" label="序号"></el-table-column>
                <el-table-column prop="linkId" label="属性组">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.linkId"
                      placeholder="请选择属性组"
                      style="width: 100%"
                      clearable
                      @change="chooseAttr($event, scope.$index)"
                    >
                      <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="fieldName" label="字段">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.fieldName"
                      placeholder="请选择字段"
                      style="width: 100%"
                      clearable
                      @change="changeField($event, scope.$index)"
                    >
                      <el-option
                        v-for="item in scope.row.filedList"
                        :key="item.fieldName"
                        :label="item.fieldCn"
                        :disabled="item.disabled"
                        :value="item.fieldName"
                      >
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="fieldValue" label="修改的值">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.fieldValue"
                      placeholder="请选择"
                      v-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'select'"
                      clearable
                    >
                      <el-option v-for="item in scope.row.fieldObj.attribution.options" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                    <el-date-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'date'"
                      v-model="scope.row.fieldValue"
                      :type="scope.row.fieldObj.attribution.type"
                      value-format="x"
                      :placeholder="scope.row.fieldObj.inputHint"
                      style="width: 100%"
                    >
                    </el-date-picker>

                    <el-date-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'date-range'"
                      v-model="scope.row.fieldValue"
                      type="daterange"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 90%"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                    <el-time-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'time'"
                      v-model="scope.row.fieldValue"
                      :picker-options="{
                        selectableRange: '00:00:00 - 23:59:59'
                      }"
                      value-format="HH:mm:ss"
                      :placeholder="scope.row.fieldObj.inputHint"
                      style="width: 100%"
                    >
                    </el-time-picker>
                    <el-input
                      v-else-if="scope.row.fieldObj && ['input', 'textarea', 'number'].includes(scope.row.fieldObj.valueMethod)"
                      :type="scope.row.fieldObj.valueMethod === 'input' ? 'text' : scope.row.fieldObj.valueMethod"
                      v-model="scope.row.fieldValue"
                      style="width: 100%"
                      clearable
                    ></el-input>
                    <el-radio-group v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'radio'" v-model="scope.row.fieldValue">
                      <el-radio :value="item.value" v-for="(item, index) in scope.row.fieldObj.attribution.options" :key="index">{{
                        item.label
                      }}</el-radio>
                    </el-radio-group>

                    <el-checkbox-group v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'checkbox'" v-model="scope.row.fieldValue">
                      <el-checkbox
                        v-for="(item, index) in scope.row.fieldObj.attribution.options"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      />
                    </el-checkbox-group>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-link type="danger" @click="deleteRow(scope.$index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      @handleResetSearch="handleResetSearch"
      :moduleId="moduleId"
      @editCondition="editCondition"
    ></dataSearch>
    <!-- 批量选择字段弹窗 -->
    <el-dialog
      title="批量选择字段"
      v-model="bitchFieldDialog"
      width="800px"
      v-dialogDrag
      :close-on-click-modal="false"
      :before-close="handleCloseBitchFieldDialog"
      append-to-body
    >
      <div style="padding: 20px 0px">
        <el-select filterable v-model="chooseGroup" placeholder="请选择属性组" style="width: 100%" clearable @change="chooseAttr">
          <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId"> </el-option>
        </el-select>
        <el-select filterable v-model="chooseFields" multiple placeholder="请选择字段" style="width: 100%; margin-top: 10px" clearable>
          <el-option v-for="item in filedList" :key="item.fieldName" :label="item.fieldCn" :disabled="item.disabled" :value="item.fieldName">
          </el-option>
        </el-select>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBitchFieldDialog">取 消</el-button>
          <el-button type="primary" @click="submitBitchFields">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 进度弹窗 -->
    <el-dialog
      title="修改字段进度"
      v-model="progressDialog"
      width="300px"
      :close-on-click-modal="false"
      :before-close="handleProgressClose"
      append-to-body
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="progress" :status="editStatus"></el-progress>
        <div style="margin-top: 10px">{{ editMsg }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import searchData from '@/views/autoProject/components/searchData/index.vue';
import { updateField } from '@/api/project';
import { getPlaceList } from '@/api/modal';
import { h } from 'vue';
import type { AnyColumn, RowClassNameGetter } from '@element-plus/table-v2';
import dataSearch from '@/components/dataSearch/index.vue';

// ---Props---
interface Props {
  // 打开弹框
  bitchUpdateFieldDialog: boolean;
  // 模块id
  ruleTree?: any[];
  moduleId?: number;
}

interface DialogSearchType {
  pageNum: number;
  pageSize: number;
  moduleId: number;
  createUserId?: string;
  createUserName?: string;
  optUserId?: string;
  optUserName?: string;
  conditionFields?: Array<{
    name: string;
    operator: string;
    value: string;
    index: string | number;
    relation: string;
    type: number;
    linkId?: string | number;
  }>;
  areaCode?: string;
  createDate?: string;
  updateDate?: string;
  parcelName?: string;
  express?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  bitchUpdateFieldDialog: false,
  ruleTree: () => [],
  moduleId: 0
});

const bitchUpdateFieldDialogCopy = computed(() => props.bitchUpdateFieldDialog);

//--- 定义变量 ---
const formMsg: any = ref({
  zdList: [], //需要回退的数据
  zdListNames: [],
  tableList: [] //修改的属性组内容
});

const formMsgRule = ref({
  zdList: [{ required: true, message: '请选择数据', trigger: 'change' }],
  tableList: [{ required: true, message: '请选择修改的内容', trigger: 'change' }]
});

const ifTree = ref(false);
const shaixuanDialog = ref(false);
const attrList = ref([]); //所有属性组
const filedList = ref([]); //所有字段
const tableHeight = ref(window.innerHeight - 300); //表格高度
const bitchFieldDialog = ref(false); //批量选择字段弹窗
const chooseGroup = ref(''); //批量选择字段的属性组
const chooseFields = ref([]); //批量选择字段的字段
const multipleSelection = ref<any[]>([]); //表格选中的行
const progressDialog = ref(false); //进度弹窗
const progress = ref(0); //进度
const editMsg = ref(''); //进度信息
const editStatus = ref(''); //进度状态
const okUpdataNum = ref(0); //成功修改的数量
const formMsgRef = ref();
const nowChooseRuleId = ref(0); //当前选择的数据对应的跟节点id

// 分页相关
const dialogSearch = ref<DialogSearchType>({
  pageNum: 1,
  pageSize: 20,
  moduleId: props.moduleId
});
const total = ref(0);
const fullscreenLoading = ref(false); // 添加loading变量

// 表格配置
const tableWidth = ref<number>(500);

// 在 script 部分添加变量和方法
const selectedIds = ref<(string | number)[]>([]); // 存储所有已选择的数据ID

// 监听moduleId变化
watch(
  () => props.moduleId,
  (newVal) => {
    dialogSearch.value.moduleId = newVal;
    if (props.bitchUpdateFieldDialog) {
      getData();
    }
  }
);

// 监听当前选择的根节点
watch(
  () => nowChooseRuleId.value,
  (newVal) => {
    if (newVal !== 0) {
      initGroups(newVal);
    } else {
      // 如果重置为0的时候 需要把选择的字段全部删除
      formMsg.value.tableList = [];
    }
  }
);

// 监听弹窗显示
watch(
  () => props.bitchUpdateFieldDialog,
  (val) => {
    if (val) {
      dialogSearch.value.moduleId = props.moduleId;
      getData();
    }
  }
);

// 获取表格数据
const getData = () => {
  if (!dialogSearch.value.moduleId) {
    ElMessage.error('模块ID不能为空');
    return;
  }
  fullscreenLoading.value = true;
  getPlaceList(dialogSearch.value)
    .then((res) => {
      if (res.code == 200) {
        formMsg.value.zdList = res.data.list;
        total.value = res.data.total;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .finally(() => {
      fullscreenLoading.value = false;
    });
};

// 定义列配置
interface TableRowData {
  id: string | number;
  parcelName: string;
  createTime: string;
  [key: string]: any;
}

const columns = ref<AnyColumn[]>([
  {
    key: 'selection',
    dataKey: 'selection',
    title: '',
    width: 45,
    headerCellRenderer: () => {
      return h(ElCheckbox, {
        modelValue: formMsg.value.zdList.length > 0 && formMsg.value.zdList.every((item) => selectedIds.value.includes(item.id)),
        onChange: (val: boolean) => {
          if (val) {
            // 全选当前页
            const currentPageSelection = formMsg.value.zdList;
            handleSeletionAll(currentPageSelection);
          } else {
            // 取消当前页选择
            handleSeletionAll([]);
          }
        },
        disabled: getChooseAll()
      });
    },
    cellRenderer: ({ rowData }: { rowData: TableRowData }) => {
      return h(ElCheckbox, {
        modelValue: selectedIds.value.includes(rowData.id),
        onChange: (val: boolean) => {
          const selection = val ? [...multipleSelection.value, rowData] : multipleSelection.value.filter((item) => item.id !== rowData.id);
          handleSelectTable(selection, rowData);
        },
        disabled: getDisabled(rowData)
      });
    }
  },
  {
    key: 'index',
    dataKey: 'index',
    title: '序号',
    width: 70,
    cellRenderer: ({ rowIndex }: { rowIndex: number }) => {
      return rowIndex + 1;
    }
  },
  {
    key: 'parcelName',
    dataKey: 'parcelName',
    title: '数据名称',
    width: 100
  },
  {
    key: 'createTime',
    dataKey: 'createTime',
    title: '创建时间',
    width: 100,
    cellRenderer: ({ rowData }: { rowData: TableRowData }) => {
      return formatDateType(rowData.createTime);
    }
  }
]);

const getDisabled = (rowData: TableRowData) => {
  if (nowChooseRuleId.value !== 0 && rowData.ruleId !== nowChooseRuleId.value) {
    return true;
  }
  return false;
};

/**
 * 是否允许全选  当数据里面存在多个根节点的时候不允许全选
 */
const getChooseAll = () => {
  const rules = []; //根节点数量
  formMsg.value.zdList.forEach((item) => {
    if (!rules.includes(item.ruleId)) {
      rules.push(item.ruleId);
    }
  });
  if (rules.length > 1) {
    return true;
  }
  return false;
};

// 获取行样式
const getRowClass: RowClassNameGetter<TableRowData> = ({ rowData }) => {
  return selectedIds.value.includes(rowData.id) ? 'selected-row' : '';
};

// 处理表格全选
const handleSeletionAll = (selection: any[]) => {
  // 更新当前页选择状态
  multipleSelection.value = selection;

  // 移除当前页所有ID
  const currentPageIds = formMsg.value.zdList.map((item) => item.id);
  selectedIds.value = selectedIds.value.filter((id) => !currentPageIds.includes(id));

  // 添加新选中的ID
  const newSelectedIds = selection.map((item) => item.id);
  selectedIds.value.push(...newSelectedIds);

  // 更新选中的数据名称
  updateSelectedNames();
};

// 处理表格行选择
const handleSelectTable = (selection: any[], row: any) => {
  if (!nowChooseRuleId.value) {
    nowChooseRuleId.value = selection[0].ruleId;
  }
  // 未选择数据的时候需要初始化当前选择的根节点
  if (selection.length == 0) {
    nowChooseRuleId.value = 0;
  }
  multipleSelection.value = selection;

  const rowId = row.id;
  const index = selectedIds.value.indexOf(rowId);

  if (selection.includes(row)) {
    // 选中
    if (index === -1) {
      selectedIds.value.push(rowId);
    }
  } else {
    // 取消选中
    if (index > -1) {
      selectedIds.value.splice(index, 1);
    }
  }

  // 更新选中的数据名称
  updateSelectedNames();
};

// 更新选中的数据名称
const updateSelectedNames = () => {
  const names = formMsg.value.zdList.filter((item) => selectedIds.value.includes(item.id)).map((item) => item.parcelName);
  formMsg.value.zdListNames = names.join(',');
};

const handleRowClick = (row: any, column: any, event: any) => {
  multipleTableRef.value?.toggleRowSelection(row);
};

const formatDateType = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  dialogSearch.value.pageSize = val;
  dialogSearch.value.pageNum = 1;
  getData();
};

// 分页页码改变
const handleCurrentChange = (val: number) => {
  dialogSearch.value.pageNum = val;
  getData();
};

// 筛选按钮点击
const searchBtn = () => {
  shaixuanDialog.value = true;
};

// 定义emit
const emit = defineEmits<{
  (e: 'handleCloseBitchUpdateFieldDialog'): void;
}>();

// --- 定义方法 ---
/**
 * 根据选择的数据的根节点id 得到对应内容的属性组
 * @param ruleId 规则id
 */
const initGroups = (ruleId: number) => {
  attrList.value = [];
  for (let i = 0; i < props.ruleTree.length; i++) {
    if (props.ruleTree[i].id === ruleId) {
      disposeAttr(JSON.parse(JSON.stringify([props.ruleTree[i]])));
      break;
    }
  }
  // disposeAttr(JSON.parse(JSON.stringify(props.ruleTree)));
};

/**
 * 组装所有属性组
 * @param list
 */
const disposeAttr = (list) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      k.label = `${k.typeName}(${v.typeName})`;
      attrList.value.push(k);
    });
    if (v.list.length != 0) {
      disposeAttr(v.list);
    }
  });
};

const handleClose = () => {
  // 初始化
  formMsg.value = {
    zdList: [], //需要回退的数据
    zdListNames: [],
    tableList: [] //修改的属性组内容
  };
  filedList.value = []; //所有字段
  nowChooseRuleId.value = 0; //重置已选择的数据对应的根节点
  emit('handleCloseBitchUpdateFieldDialog');
  clearSelection();
};

/**
 * 选择数据
 */
const chooseData = () => {
  ifTree.value = false;
  shaixuanDialog.value = true;
};
/**
 * 关闭筛选数据弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 得到筛选要素数据
 */
const getChooseData = (list) => {
  formMsg.value.zdList = list;
  const names: any = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  formMsg.value.zdListNames = names.join(',');
  shaixuanDialog.value = false;
};

/**
 * @params {number} e
 * @param {number} index 没有index的代表是批量增加的
 */
const chooseAttr = (e, index) => {
  let filedListTemp = [];
  for (let i = 0; i < attrList.value.length; i++) {
    if (attrList.value[i].linkId == e) {
      filedListTemp = attrList.value[i].fieldModelList;
      break;
    }
  }
  if (filedListTemp.length != 0) {
    filedListTemp.forEach((v) => {
      if (
        [
          'idCardScan',
          'xtBankCard',
          'xttable',
          'xtzwsb',
          'xtdwsb',
          'xtvideo',
          'xtfj',
          'cascader',
          'upload',
          'xtzw',
          'xtqm',
          'area',
          'xtaudio',
          'xtsjjt',
          'xtsjy',
          'xtsjy',
          'xtzsdl',
          'xtcy',
          'xtpay'
        ].includes(v.valueMethod) ||
        (v.attribution && v.attribution.expression)
      ) {
        //只有普通属性组才可以选择
        v.disabled = true;
      }
    });
  }
  if ((index != undefined && index != null && index != '') || index == 0) {
    formMsg.value.tableList[index].filedList = filedListTemp;
  } else {
    //代表是批量增加的
    filedList.value = filedListTemp;
  }
};

// 提交数据
const submit = async () => {
  formMsgRef.value.validate((valid) => {
    if (valid) {
      //判断表格中是否有字段值为空的
      let flag = true;
      for (let i = 0; i < formMsg.value.tableList.length; i++) {
        if (!formMsg.value.tableList[i].linkId) {
          ElMessage({
            message: `第${i + 1}行的属性组不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
        if (!formMsg.value.tableList[i].fieldName) {
          ElMessage({
            message: `第${i + 1}行的字段不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
        if (
          formMsg.value.tableList[i].fieldValue == '' &&
          formMsg.value.tableList[i].fieldValue != 0 &&
          formMsg.value.tableList[i].fieldValue != false
        ) {
          ElMessage({
            message: `第${i + 1}行的字段值不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
      }
      if (!flag) {
        return;
      }
      ElMessageBox.confirm('确定要批量修改选择的数据的字段内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 检查是否有选中的数据
          if (selectedIds.value.length === 0) {
            ElMessage.error('请选择需要修改的数据');
            return;
          }

          // 按照 linkId 对 formMsg.tableList 进行分组
          const groups: any = []; //需要修改的属性组
          formMsg.value.tableList.forEach((item) => {
            if (!groups[item.linkId]) {
              groups[item.linkId] = [];
            }
            groups[item.linkId].push(item);
          });

          const endGroups = []; //最终需要修改的属性组
          groups.forEach((v) => {
            const obj: any = {
              linkId: v[0].linkId,
              attribution: {}
            };
            v.forEach((k) => {
              if (k.fieldObj.valueMethod === 'checkbox') {
                obj.attribution[k.fieldName] = k.fieldValue.join(',');
              } else if (k.fieldObj.valueMethod === 'date-range') {
                obj.attribution[k.fieldName] = k.fieldValue.join('至');
              } else {
                obj.attribution[k.fieldName] = k.fieldValue;
              }
            });
            endGroups.push(obj);
          });

          const submitList = [];
          // 使用选中的数据ID而不是当前页的所有数据
          const subIds = [...selectedIds.value];
          // 把subIds分割成10个一组的数据
          const subIdsArr = [];
          for (let i = 0; i < subIds.length; i += 10) {
            subIdsArr.push(subIds.slice(i, i + 10));
          }
          let submitCount = 0;
          subIdsArr.forEach((v) => {
            const ite_list = [];
            v.forEach((k) => {
              endGroups.forEach((q) => {
                ite_list.push({
                  linkId: q.linkId,
                  attribution: q.attribution,
                  parcelId: k
                });
                submitCount++;
              });
            });
            submitList.push(ite_list);
          });
          const batchSize = 10;
          const currentIndex = 0;
          progressDialog.value = true;
          progress.value = 0;
          editMsg.value = '数据处理中，当前更新0/0条';
          editStatus.value = '';
          okUpdataNum.value = 0;
          let bitch_index = 0;
          const processBatch = async () => {
            try {
              // 调用接口修改数据
              await editFiled(submitList[bitch_index]);
              okUpdataNum.value = okUpdataNum.value + submitList[bitch_index].length;
              // 更新进度
              progress.value = ((okUpdataNum.value / submitCount) * 100).toFixed(2);
              editMsg.value = `数据处理中，当前更新${okUpdataNum.value}/${submitCount}条`;
              bitch_index++;
              if (bitch_index < submitList.length) {
                // 继续处理下一批次
                await processBatch();
              } else {
                // 全部处理完成
                if (progress.value >= 100) {
                  ElMessageBox.alert('修改成功', '提示', {
                    confirmButtonText: '确定',
                    callback: (action) => {
                      // 在这添加是否切换公司的标识。
                      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                      sessionStorage.setItem('qiehuan_company', false);
                      location.reload();
                    }
                  });
                }
              }
            } catch (error) {
              ElMessage({
                message: '批量处理出错，请稍后重试',
                type: 'error'
              });
            }
          };
          // 开始处理第一批数据
          processBatch();
        })
        .catch(() => {});
    } else {
      return false;
    }
  });
};

/**
 * 分布调用接口修改
 * @param parmas
 */
const editFiled = async (parmas) => {
  return new Promise((resolve, reject) => {
    // 调用接口
    updateField(parmas).then((res) => {
      if (res.code == 200) {
        resolve(null);
      } else {
        reject(res.msg);
      }
    });
  });
};

/**
 * 删除
 * @param {number} index
 */
const deleteRow = (index) => {
  ElMessageBox.confirm('确定要删除该条内容吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formMsg.value.tableList.splice(index, 1);
    })
    .catch(() => {});
};

/**
 * 新增字段修改
 */
const addTable = () => {
  formMsg.value.tableList.push({
    linkId: '',
    fieldName: '',
    fieldValue: '',
    fieldObj: null,
    filedList: []
  });
};

/**
 * 选择字段
 */
const changeField = (e, index) => {
  for (let i = 0; i < formMsg.value.tableList[index].filedList.length; i++) {
    if (formMsg.value.tableList[index].filedList[i].fieldName == e) {
      formMsg.value.tableList[index].fieldObj = formMsg.value.tableList[index].filedList[i];
      formMsg.value.tableList[index].fieldValue = formMsg.value.tableList[index].filedList[i].valueMethod === 'checkbox' ? [] : '';

      break;
    }
  }
};

/**
 * 批量增加字段选择
 */
const bitchAdd = () => {
  bitchFieldDialog.value = true;
};
/**
 * 关闭批量增加字段选择弹窗
 */
const handleCloseBitchFieldDialog = () => {
  bitchFieldDialog.value = false;
};

/**
 * 提交批量增加字段选择
 */
const submitBitchFields = () => {
  chooseFields.value.forEach((v) => {
    for (let i = 0; i < filedList.value.length; i++) {
      if (filedList.value[i].fieldName == v) {
        formMsg.value.tableList.push({
          linkId: chooseGroup.value,
          fieldName: v,
          fieldValue: filedList.value[i].valueMethod === 'checkbox' ? [] : '',
          fieldObj: filedList.value[i],
          filedList: filedList.value
        });
        break;
      }
    }
  });
  bitchFieldDialog.value = false;
};

/**
 * 表格多选
 * @param val
 */
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

/**
 * 删除表格选中的行
 * @param val
 */
const delTable = () => {
  ElMessageBox.confirm('确定要删除所选择的字段吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      multipleSelection.value.forEach((v) => {
        const index = formMsg.value.tableList.indexOf(v);
        if (index != -1) {
          formMsg.value.tableList.splice(index, 1);
        }
      });
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {});
};

const handleProgressClose = () => {
  progressDialog.value = false;
};

// 提交筛选
const submitSearch = () => {
  getData();
  shaixuanDialog.value = false;
};

// 清除用户
const clearUser = (type: number) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

// 重置筛选
const handleResetSearch = () => {
  dialogSearch.value = {
    pageNum: 1,
    pageSize: 20,
    moduleId: props.moduleId,
    createUserId: '',
    createUserName: '',
    optUserId: '',
    optUserName: '',
    conditionFields: [],
    areaCode: '',
    createDate: '',
    updateDate: '',
    parcelName: '',
    express: false
  };
  getData();
  shaixuanDialog.value = false;
};

// 编辑条件
const editCondition = (type: number, idx?: number) => {
  if (type == 1) {
    dialogSearch.value.conditionFields = dialogSearch.value.conditionFields || [];
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else if (idx !== undefined) {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

// 清空选择
const clearSelection = () => {
  selectedIds.value = [];
  formMsg.value.zdListNames = '';
  multipleSelection.value = [];
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px 20px 0px 20px;
}
.notice {
  color: red;
}
.add-btn {
  position: absolute;
  top: -37px;
  left: 265px;
}
.dialog-row {
  display: flex;
  .dialog-left {
    flex: 1;
  }
  .dialog-right {
    flex: 3;
    margin-left: 20px;
  }
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.dialog-search {
  margin-bottom: 10px;

  .left-section {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .selected-info {
    font-size: 14px;
    color: #606266;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.table-container {
  height: 455px;
  width: 100%;
}

.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.selected-row {
  background-color: var(--el-table-row-hover-bg-color);
}
</style>
