<!-- 属性组排序 -->
<template>
  <div>
    <el-dialog
      title="属性组排序"
      v-model="dialogVisible"
      @opened="handleOpenDialog"
      width="90%"
      :before-close="handleOrderClose"
      :close-on-click-modal="false"
    >
      <div class="top">上下拖动进行排序 <span style="color: red; padding-left: 16px">注:数字越小代表排序在前，*代表未排序</span></div>
      <div class="handle-div">
        <div class="head-xh">优先级(从高到低)</div>
        <div class="item-title tree">节点名称</div>
        <div class="item-title group">属性组名称</div>
        <div class="item-title guanl">属性组类型</div>
      </div>
      <div class="order-content" :style="{ height: height }">
        <draggable :list="groupOrderList" @end="handleOrederEnd" v-if="groupOrderList.length > 0">
          <template #item="{ element, index }">
            <div class="flex-item" @click="handleClickItem(element, index)">
              <div class="flex-xh">{{ element.seq == 0 ? '*' : index + 1 }}</div>
              <div class="item tree">{{ typeName }}</div>
              <div class="item group">{{ element.typeName }}</div>
              <div class="item guanl">{{ filterLinkType(element.linkType) }}</div>
              <!-- <div class="item guanl" >{{ item.ruleAttribution.type | filterGroupType }}</div> -->
            </div>
          </template>
        </draggable>
        <div v-else style="width: 100%; margin-top: 100px; text-align: center">暂无属性组</div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleOrderClose">取 消</el-button>
          <el-button type="primary" @click="handleOrderSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import draggable from 'vuedraggable';
import { addRule } from '@/api/modal';
import type { PropType } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModalStore } from '@/store/modules/modal';

interface CheckedTreeMsgProp {
  typeName: string;
  fieldGroupModelList: Array<{
    seq: number;
    typeName: string;
    linkType: number;
  }>;
}

const props = defineProps({
  orderVisible: {
    type: Boolean,
    required: true
  },
  checkedTreeMsg: {
    type: Object as PropType<CheckedTreeMsgProp>,
    required: true,
    default: () => ({})
  }
});

const modalStore = useModalStore();

const dialogVisible = computed({
  get() {
    return props.orderVisible;
  },
  set(value) {
    // 触发关闭事件
    // emit('closeOrder');
  }
});

const emit = defineEmits(['update:modelValue', 'closeOrder']);

// 响应式数据
const height = ref<string>(`${window.innerHeight - 300}px`);
const typeName = ref<string>('');
const groupOrderList = ref<CheckedTreeMsgProp['fieldGroupModelList']>([]);

// 计算属性
const title = computed(() => '属性组排序');

// 监听checkedTreeMsg变化
// watch(
//   () => props.checkedTreeMsg,
//   (val) => {
//     typeName.value = val.typeName;
//     if (val.fieldGroupModelList?.length) {
//       groupOrderList.value = JSON.parse(JSON.stringify(val.fieldGroupModelList));
//     }
//   },
//   { immediate: true, deep: true }
// );

// 方法
const handleOrderClose = () => {
  emit('closeOrder');
};

const handleOrderSubmit = async () => {
  const list1 = groupOrderList.value.filter((item) => item.seq == 0);
  if (list1.length > 0) {
    ElMessage.error('排序未结束');
    return;
  }
  const list = groupOrderList.value.map((item, index) => {
    item.seq = index + 1;
    return item;
  });
  const text = `确定保存【${typeName.value}】节点下的属性组排序`;
  ElMessageBox.confirm(text, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const item_obj = JSON.parse(JSON.stringify(props.checkedTreeMsg));
      item_obj.fieldGroupModelList = JSON.parse(JSON.stringify(list));
      // props.checkedTreeMsg.fieldGroupModelList = JSON.parse(JSON.stringify(list));
      const resultList = [item_obj];
      addRule(resultList).then((res) => {
        if (res.code == 200) {
          ElMessage.success('保存成功');
          modalStore.setNodeTree(res.data);
          emit('closeOrder', res.data);
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

const handleOrederEnd = (event: { oldIndex: number; newIndex: number }) => {
  const oldIndex = event.oldIndex;
  const newIndex = event.newIndex;
  // let resultList = this.fieldOrderList.filter(item =>item.seq !== 0)
  if (oldIndex !== newIndex) {
    groupOrderList.value[newIndex].seq = newIndex + 1;
  }
};

// 过滤器转换为方法
const filterLinkType = (val: number) => {
  const map: { [key: number]: string } = {
    2: '面共享',
    3: '线共享',
    4: '点共享',
    5: '子要素-线',
    6: '子要素-点',
    7: '无图形'
  };
  return map[val] || '普通';
};

/**
 * 点击即排序 如果未排序的话
 * @param item 点击的内容
 */
const handleClickItem = (item: any, index: number) => {
  if (!item.seq) {
    item.seq = index + 1;
  }
};

/**
 * 对话框初始化方法
 */
const handleOpenDialog = () => {
  typeName.value = props.checkedTreeMsg.typeName;
  if (props.checkedTreeMsg.fieldGroupModelList?.length) {
    groupOrderList.value = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  }
  // 如果没有排序给个默认排序
  groupOrderList.value.forEach((v, index) => {
    if (!v.seq) {
      v.seq = index + 1;
    }
  });
};
</script>

<style lang="scss" scoped>
.top {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #edf4fb;
  margin-bottom: 10px;
  padding: 0px 16px;
  border-radius: 4px;
}
.handle-div {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #f5f7fa;
  padding: 0px 16px;
  .head-xh {
    width: 130px;
  }
  .item-title {
    padding: 0px 16px;
  }
  .tree {
    min-width: 120px;
  }
  .guanl {
    min-width: 120px;
  }
  .ziduan {
    min-width: 120px;
  }
  .group {
    min-width: 120px;
  }
  .expression {
    width: calc(100% - 650px);
  }
  margin-bottom: 10px;
}
.order-content {
  // height: 500px;
  overflow: auto;
  .flex-item {
    padding: 0px 16px;
    height: 32px;
    display: flex;
    align-items: center;
    cursor: move;
    .flex-xh {
      width: 130px;
    }
    .item {
      padding: 0px 16px;
    }
    .tree {
      min-width: 120px;
    }
    .guanl {
      min-width: 120px;
    }
    .ziduan {
      min-width: 120px;
    }
    .group {
      min-width: 120px;
    }
    .expression {
      width: calc(100% - 650px);
    }
    border-bottom: #edf4fb solid 1px;
  }
}
</style>
