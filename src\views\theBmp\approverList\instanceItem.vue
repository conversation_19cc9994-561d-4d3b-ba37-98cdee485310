<template>
  <div class="modal-item-main">
    <div class="row-left">
      <div class="left-icon">
        <img :src="getIcon" class="icon-item" />
      </div>
      <div class="left-module" :title="currentItem.processName">
        <div class="title">{{ currentItem.processName }}</div>
      </div>
    </div>
    <div class="row-right">
      <el-button class="div-btn" v-if="showType === 'apply'" @click="handleApplyInstance" type="primary" text>申请</el-button>
      <el-button class="div-btn" v-if="showType === 'edit'" @click="handleEdit" type="primary" text>编辑</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';

// Types
interface CurrentItem {
  processId: string | number;
  processName?: string;
  iconUrl?: string;
  [key: string]: any;
}

interface IconItem {
  src: string;
  id: string;
}

// Props
const props = defineProps({
  currentItem: {
    required: true,
    type: Object as () => CurrentItem,
    default: () => ({})
  },
  showType: {
    type: String,
    default: 'apply'
  }
});

// Router
const router = useRouter();

// 批量导入所有图标
const iconModules = import.meta.glob('@/assets/images/approverIcon/A (*).png', { eager: true });

const iconList: IconItem[] = Object.entries(iconModules)
  .map(([path, module]) => {
    // 从文件名中提取数字作为id
    const id = path.match(/\((\d+)\)/)?.[1] || '';
    return {
      src: (module as any).default,
      id
    };
  })
  .sort((a, b) => Number(a.id) - Number(b.id));

// Computed property to get the icon based on current item
const getIcon = computed(() => {
  const iconItem = iconList.find((item) => item.id === props.currentItem.iconUrl);

  if (iconItem && iconItem.id !== undefined && iconItem.src) {
    return iconItem.src;
  } else {
    return iconList.length > 0 ? iconList[0].src : '';
  }
});

/**
 * 编辑流程详情
 */
const handleEdit = () => {
  router.push({
    path: `/addBmp`,
    query: {
      id: props.currentItem.processId
    }
  });
};

/**
 * 申请流程详情
 */
const handleApplyInstance = () => {
  router.push({
    path: `/applyBmp`,
    query: {
      id: props.currentItem.processId,
      instanceId: '0'
    }
  });
};
</script>

<style lang="scss" scoped>
.handle-row {
  display: flex;
  align-items: center;
  .row {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .row-input {
    width: 100px;
  }
}
.modal-item-main {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  height: 50px;
  border-radius: 8px 8px 8px 8px;
  margin: 8px;
  border: 1px solid #ededed;
  width: 100%;

  .row-left {
    width: 70%;
    display: flex;
    align-items: center;

    .left-icon {
      width: 20%;
      min-width: 48px;
      padding: 8px;
      text-align: left;
      position: relative;

      .svg-item {
        width: 44px;
        height: 44px;
      }

      .icon-item {
        width: 30px;
        height: 30px;
      }
    }

    .left-module {
      width: 80%;
      text-align: left;

      .title {
        color: #161d26;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        font-size: 14px;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .row-right {
    width: 30%;
    margin-right: 8px;
    text-align: right;
  }

  &:hover {
    background-color: #f6f7f8;
    border-radius: 8px;
  }
}
</style>
