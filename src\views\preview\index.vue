<template>
  <div class="preview-main-container">
    <div
      class="preview-main"
      :style="{
        width: windowWidth + 'px',
        height: conHeight + 'px',
        backgroundColor: designCache?.bgColor,
        backgroundImage: designCache?.bgImg ? 'url(' + fileUrl + designCache?.bgImg + '?token=' + token + ')' : 'none'
      }"
    >
      <div class="content-sel">
        <el-select v-model="value" placeholder="切换大屏" @change="changeScreen">
          <el-option v-for="item in screenList" :key="item.id" :label="item.title" :value="item.id"> </el-option>
        </el-select>
      </div>
      <div
        style="position: relative; transform-origin: 0 0"
        :style="{ width: windowWidth + 'px', height: conHeight + 'px', transform: 'scale(' + containerScale + ')' }"
      >
        <transition-group appear name="bounce">
          <div
            v-for="(item, index) in designCache?.components"
            :key="item.id"
            style="position: absolute"
            :style="{
              width: Math.round(item.cptWidth) + 'px',
              height: Math.round(item.cptHeight) + 'px',
              top: Math.round(item.cptY) + 'px',
              left: Math.round(item.cptX) + 'px',
              zIndex: item.cptZ
            }"
          >
            <component
              :is="item.cptKey"
              :width="Math.round(item.cptWidth)"
              :ref="item.cptKey + index"
              @changeTaskId="changeTaskId"
              :height="Math.round(item.cptHeight)"
              @reload="loadCacheData"
              :option="item.cptOption"
            />
          </div>
        </transition-group>
      </div>

      <el-dialog
        title="请输入访问码"
        v-model="authCodeDialogVisible"
        width="30%"
        center
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form>
          <el-form-item label="访问码">
            <el-input v-model="viewCode" autocomplete="off" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="authCode">提 交</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getScreenById, getScreenList } from '@/api/dataScreen';
import { getToken } from '@/utils/auth';
import { useRouter } from 'vue-router';
const router = useRouter();
const props = defineProps({
  moduleId: {
    type: String,
    required: true
  },
  id: {
    type: String,
    required: true
  }
});
// --- 数据 ---
const fileUrl = import.meta.env.VITE_APP_BASE_API + '/qjt/file/otherDownload/';
const designCache: any = ref();
const windowWidth = ref(0);
const windowHeight = ref(0);
const conHeight = ref(0);
const containerScale = ref(1);
const authCodeDialogVisible = ref(false);
const viewCode = ref('');
const value = ref('');
const screenList = ref([]);
const taskId = ref('');
const token = getToken();
const refs = getCurrentInstance()?.proxy?.$refs;

onMounted(() => {
  loadCacheData(props.id);
  const searchMsg = {
    pageNo: 1,
    pageSize: 10000,
    moduleId: props.moduleId
  };
  getScreenList(searchMsg).then((res) => {
    if (res.code == 200) {
      screenList.value = res.data.records;
    } else {
      ElMessage.error(res.msg);
    }
  });
  window.onresize = () => {
    return (() => {
      loadSize();
    })();
  };
  document.getElementById('copyFooter').classList.add('remove-footer');
});

// --- 方法 ---
const loadCacheData = (id) => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  getScreenById(id, '0').then((res) => {
    if (res.code == 200) {
      loadDesign(res.data, true);
      loading.close();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const loadDesign = (design, componentPares) => {
  if (componentPares) {
    design.components = JSON.parse(design.components);
  }
  document.title = design.title;
  designCache.value = design;
  loadSize();
};

const authCode = () => {
  if (!viewCode.value) {
    ElMessage.error('请输入访问码');
    return;
  }
  const id = props.id;
};
const loadSize = () => {
  windowWidth.value = document.documentElement.clientWidth;
  windowHeight.value = document.documentElement.clientHeight;
  containerScale.value = Math.round((windowWidth.value / designCache.value?.scaleX) * 100) / 100;
  conHeight.value = designCache.value?.scaleY;
};
/**
 * 切换大屏
 * @param val 大屏id
 */
const changeScreen = (val) => {
  const strList = window.location.href.split('/');
  strList[strList.length - 1] = val;
  const newUrl = strList.join('/');
  window.history.replaceState(null, null, newUrl);
  location.reload();
  loadCacheData(val);
};
const jumpSetting = () => {
  router.push('/bigData');
};

/**
 * 任务下拉列表 改变
 * @param id 任务id
 */
const changeTaskId = (id) => {
  taskId.value = id;
  // 任务改变的时候主动触发所有组件刷新
  const loadList = [
    'cpt-text',
    'cpt-dataV-percentPond',
    'cpt-rect-num',
    'cpt-dataV-scrollList',
    'cpt-chart-map-gc',
    'cpt-chart-pie',
    'cpt-chart-line',
    'cpt-dataV-scrollTable'
  ]; //需要执行的组件key
  designCache.value?.components?.forEach((v, vdx) => {
    if (loadList.includes(v.cptKey)) {
      const ref = v.cptKey + vdx;
      refs[ref][0].loadData(id);
    }
  });
};
</script>

<style scoped>
.setting {
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: white;
  padding: 4px;
  background: rgb(43, 51, 64);
  border-radius: 4px;
}
:deep(.el-input--medium .el-input__inner) {
  background: transparent;
  border: transparent;
}
.content-sel {
  position: absolute;
  z-index: 99;
  top: 10px;
  left: 10px;
  width: 120px;
}
.bounce-enter-active {
  transition: all 1s;
  /*animation: bounce-in 1s;*/
}
.bounce-enter {
  opacity: 0;
  transform: scale(0.5);
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  25% {
    transform: scale(0.5);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}
.preview-main-container {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}
:deep(.el-select__wrapper) {
  background: none !important;
}
.preview-main {
  background-size: 100% 100%;
}

/*滚动条样式*/
.preview-main::-webkit-scrollbar {
  width: 2px;
}
.preview-main::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.preview-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
