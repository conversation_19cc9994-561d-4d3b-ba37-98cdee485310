<template>
  <el-dialog title="注册公司" v-model="dialogVisible" width="500px" :close-on-click-modal="false" :before-close="handleClose">
    <el-form :model="registCompany" :rules="registCompanyRule" ref="registCompanyRef" label-position="top" class="demo-ruleForm">
      <el-form-item label="注册类型">
        <el-radio-group v-model="registCompany.companyType" @change="changeCompanyType">
          <el-radio :value="1">公司</el-radio>
          <el-radio :value="2">个人</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName" v-if="registCompany.companyType == 1">
        <el-input v-model="registCompany.companyName" placeholder="请输入公司名称" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item label="客户姓名" prop="custName">
        <el-input v-model="registCompany.custName" placeholder="请输入客户姓名" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="username">
        <el-input v-model.trim="registCompany.username" placeholder="请输入手机号" maxlength="11" autocomplete="off" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="公司等级">
        <el-radio-group v-model="registCompany.vipType">
          <el-radio :value="1">个人版</el-radio>
          <el-radio :value="2">专业版</el-radio>
          <el-radio :value="3">企业版</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { register } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance, FormRules } from 'element-plus';
interface RegistCompany {
  companyName: string;
  companyType: number | string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  companyWebsite: string;
  companyDescription: string;
  ifCaptcha: boolean;
  // 当前添加的推广员id（当前创建人员的id）
  promoterUseId: string | number;
  // 当前页面的升级类型
  vipType: number | string;
  // 客户姓名
  custName: string;
  // 手机号
  username: string;
}

export default defineComponent({
  name: 'RegisterCompanyDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const userStore = useUserStore();
    const dialogVisible = computed({
      get: () => props.visible,
      set: (val) => emit('update:visible', val)
    });

    const registCompany = reactive<RegistCompany>({
      companyName: '',
      companyType: 1,
      companyAddress: '',
      companyPhone: '',
      companyEmail: '',
      companyWebsite: '',
      companyDescription: '',
      // 必须添加这个参数代表不需要图形验证码
      ifCaptcha: false,
      promoterUseId: userStore.user.userId,
      vipType: 1,
      // 客户姓名
      custName: '',
      // 手机号
      username: ''
    });
    const registCompanyRef = ref<FormInstance>();
    const registCompanyRule = reactive<FormRules>({
      companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
      companyType: [{ required: true, message: '请选择公司类型', trigger: 'change' }],
      custName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
      username: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
    });

    const handleSubmit = async () => {
      if (!registCompanyRef.value) return;
      try {
        // 执行表单校验
        await registCompanyRef.value.validate();
        // 校验通过后执行注册
        const res = await register({
          ...registCompany
          // password: `qjt${registCompany.username.substring(5, 11)}`
        });
        if (res.code === 200) {
          ElMessage.success('注册成功');
          handleClose();
        } else {
          ElMessage.error(res.msg || '注册失败');
        }
      } catch (error) {
        // 表单校验失败会进入这里
        console.error('表单校验失败:', error);
      }
    };
    const handleClose = () => {
      dialogVisible.value = false;
      // 移除表单校验，并且清空值
      Object.assign(registCompany, {
        companyName: '',
        companyType: 1,
        companyAddress: '',
        companyPhone: '',
        companyEmail: '',
        companyWebsite: '',
        companyDescription: '',
        // 必须添加这个参数代表不需要图形验证码
        ifCaptcha: false,
        promoterUseId: '',
        vipType: 1,
        // 客户姓名
        custName: '',
        // 手机号
        username: ''
      });
      if (registCompanyRef.value) {
        registCompanyRef.value.resetFields();
      }
    };

    const changeCompanyType = (val: string) => {
      registCompany.companyType = val;
    };

    return {
      dialogVisible,
      registCompany,
      registCompanyRule,
      registCompanyRef,
      handleClose,
      changeCompanyType,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0;
}
</style>
