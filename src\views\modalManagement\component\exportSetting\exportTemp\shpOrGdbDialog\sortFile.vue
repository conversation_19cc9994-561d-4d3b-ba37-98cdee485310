<!-- 给字段排序，然后按照属性组分类返回 -->
<template>
  <div class="main">
    <el-dialog
      title="字段排序"
      v-model="dialogVisible"
      width="611px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleClose"
    >
      <div class="top">上下拖动进行排序</div>
      <div class="handle-div">
        <div class="head-xh">序号</div>
        <div>字段名</div>
      </div>
      <div class="content">
        <draggable v-model="sortMsg.fiedList" itemKey="id">
          <template #item="{ element, index }">
            <div class="flex-item">
              <div class="flex-xh">{{ index + 1 }}</div>
              <div v-if="element.valueMethod === 'idCardScan'">
                {{ element.attribution.expendList[0].enName }}({{ element.attribution.expendList[0].cnName }})
              </div>
              <div v-else>{{ element.fieldName }}({{ element.fieldCn }})</div>
            </div>
          </template>
        </draggable>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch } from 'vue';
import draggable from 'vuedraggable';

// 定义 props 类型
interface SortMsg {
  fiedList: Array<{
    valueMethod?: string;
    attribution?: { expendList: Array<{ enName: string; cnName: string }> };
    fieldName: string;
    fieldCn: string;
    isDrive: boolean;
    groupId: string;
    fieldSort?: number;
  }>;
  driveGroup: Array<{ id: string; fieldModelList?: any[] }>;
  groupList: Array<{ id: string; fieldModelList?: any[] }>;
}

const props = defineProps({
  sortDialog: { type: Boolean, required: true },
  sortMsgProp: { type: Object as () => SortMsg, required: true }
});
const dialogVisible = computed({
  get() {
    return props.sortDialog;
  },
  set(value) {}
});
const sortMsg = computed(() => {
  return props.sortMsgProp;
});

// 定义 emits 事件
const emit = defineEmits<{
  (e: 'closeSortDialog'): void;
  (e: 'submitSortFiled', params: any): void;
}>();

// 关闭对话框
const handleClose = () => {
  emit('closeSortDialog');
};

// 提交排序结果
const submit = () => {
  const params = {
    driveFieldGroupModelList: sortMsg.value.driveGroup.map((v) => ({ ...v, fieldModelList: [] })),
    fieldGroupModelList: sortMsg.value.groupList.map((v) => ({ ...v, fieldModelList: [] }))
  };

  sortMsg.value.fiedList.forEach((v, vdx) => {
    v.fieldSort = vdx + 1;
    if (v.isDrive) {
      const targetGroup = params.driveFieldGroupModelList.find((g) => g.id === v.groupId);
      targetGroup?.fieldModelList.push(v);
    } else {
      const targetGroup = params.fieldGroupModelList.find((g) => g.id === v.groupId);
      targetGroup?.fieldModelList.push(v);
    }
  });

  emit('submitSortFiled', params);
};

// 监听对话框显示状态变化
watch(
  () => props.sortDialog,
  (val) => {
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
.top {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #edf4fb;
  margin-bottom: 10px;
  padding: 0px 16px;
  border-radius: 4px;
}
.handle-div {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #f5f7fa;
  padding: 0px 16px;
  .head-xh {
    width: 100px;
  }
  margin-bottom: 10px;
}
.content {
  height: 500px;
  overflow: auto;
  .flex-item {
    padding: 0px 16px;
    height: 32px;
    display: flex;
    align-items: center;
    cursor: move;
    .flex-xh {
      width: 100px;
    }
    border-bottom: #edf4fb solid 1px;
  }
}
/*滚动条样式*/
.content::-webkit-scrollbar {
  width: 4px;
}
.content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
