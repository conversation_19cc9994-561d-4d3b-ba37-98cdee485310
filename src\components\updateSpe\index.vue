<!-- 特殊excel更新 写死的 -->
<template>
  <div class="updateSpe-main" v-loading.fullscreen.lock="fullscreenLoading">
    <!-- 更新项目dialog -->
    <el-dialog
      title="更新项目"
      v-model="updateSpeDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      :before-close="handleClose"
    >
      <div class="title-label">上传excel</div>
      <el-upload
        class="upload-demo"
        :action="`${base}${sortUrl}`"
        :headers="headers"
        :show-file-list="false"
        :auto-upload="false"
        :multiple="false"
        accept=".xlsx,.xls"
        :file-list="fileList"
        ref="uploadTemp"
        :on-change="handleChangeFile"
      >
        <el-button size="small" v-show="!isUpload">点击上传</el-button>
        <template v-slot:tip>
          <div v-show="!isUpload" class="el-upload__tip" style="color: rgba(148, 155, 164, 1)">支持格式：.xls、.xlsx</div>
        </template>
        <div v-show="isUpload" style="margin-bottom: 12px">{{ fileMsg.name }} ({{ filterSize(fileMsg.size) }}kb)</div>
        <el-button size="small" v-show="isUpload">重新选择</el-button>
      </el-upload>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 进度弹窗 -->
    <el-dialog
      title="更新进度"
      v-model="submitProgressDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      :before-close="handleCloseProDia"
    >
      <div class="progress-label">【地块属性】更新条数：{{ dksxCount }}</div>
      <el-progress :percentage="dksxPlan"></el-progress>
      <div class="progress-label" style="margin-top: 40px">【承包方家庭成员】更新条数：{{ jtcyCount }}</div>
      <el-progress :percentage="jtcyPlan"></el-progress>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseProDia">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import * as xlsx from 'xlsx';
import { selectRules } from '@/api/modal';
import { updateInstance } from '@/api/project';

// ---Props---
interface Props {
  // 打开弹框
  updateSpeDialog: boolean;
  // 模块id
  moduleId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  updateSpeDialog: false,
  moduleId: ''
});

const updateSpeDialogCopy = computed(() => props.updateSpeDialog);

// 定义emit
const emit = defineEmits<{
  (e: 'closeSpeUpload'): void;
}>();

// --- 定义变量 ---
const base = import.meta.env.VITE_APP_BASE_API;
const headers = { Authorization: 'Bearer ' + getToken() }; //请求头
const fullscreenLoading = ref(false);
const fileList = ref([]);
const sortUrl = '/project/fund/excel/upload';
const isUpload = ref(false); //是否已经上传文件
const fileMsg = ref<File>();
const shpFields: any = ref([]);
const excleList = ref([]);
const submitList = ref([]); //整理的需要提交的数据
const ruleTree = ref([]);
const dksxGroupId = ref(''); //地块属性属性组id
const dksxLinkid = ref(''); //地块属性属性组linkid
const dksxruleAttribution = ref(''); //地块属性ruleAttribution
const cbfJtcyGroupId = ref(''); //承包方家庭成员属性组id
const cbfjtcyLinkid = ref(''); //承包方家庭成员linkId
const cbfjtcyruleAttribution = ref(''); //承包方家庭成员ruleAttribution
const submitProgressDialog = ref(false); //提交进度弹窗
const isAllow = ref(true); //是否允许提交  用于用户关闭弹窗的时候主动停止请求
const dksxTotal = ref(0); //地块属性总条数
const dksxCount = ref(0); //地块属性已经更新条数
const jtcyCount = ref(0); //家庭成员已更新条数
const jtcyTotal = ref(0); //家庭成员总条数
const dksxPlan = ref(0); //地块属性进度
const jtcyPlan = ref(0); //家庭成员进度
const uploadShpError = ref([]); //更新失败的数据
const dksxUploadError = ref([]); //地块属性更新失败数据
const jtcyUploadError = ref([]); //家庭成员更新失败数据
const YHZGXOptions = ref([]); //与户主关系的列表
// --- 方法部分 ---
const filterSize = (val: number) => {
  if (val) {
    return (val / 1024).toFixed(2);
  }
  return '';
};

// ---watch ---
watch(updateSpeDialogCopy, (val) => {
  if (val) {
    getTree();
  }
});

// --- 方法定义部分 ---
const getTree = () => {
  selectRules({ moduleId: props.moduleId }).then((res) => {
    if (res.code == 200) {
      ruleTree.value = res.data;
      res.data[0].fieldGroupModelList.forEach((v) => {
        if (v.typeName == '地块属性') {
          dksxGroupId.value = v.id;
          dksxLinkid.value = v.linkId;
          dksxruleAttribution.value = v.ruleAttribution;
        } else if (v.typeName == '承包方家庭成员') {
          cbfJtcyGroupId.value = v.id;
          cbfjtcyLinkid.value = v.linkId;
          cbfjtcyruleAttribution.value = v.ruleAttribution;
          for (let i = 0; i < v.fieldModelList.length; i++) {
            if (v.fieldModelList[i].fieldName == 'YHZGX') {
              YHZGXOptions.value = v.fieldModelList[i].attribution.options;
              break;
            }
          }
        }
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleClose = () => {
  emit('closeSpeUpload');
};

const handleChangeFile = (file, fileList) => {
  fileMsg.value = file;
  if (fileList.length > 1) {
    fileList.splice(0, 1);
  }
  fileList.value = [file];
  isUpload.value = true;
  fullscreenLoading.value = true;
  readExcel({ 0: file.raw });
};

const readExcel = (files) => {
  // 表格导入
  if (files.length <= 0) {
    // 如果没有文件名
    return false;
  } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
    ElMessage.error('上传格式不正确，请上传xls或者xlsx格式');
    fullscreenLoading.value = false;
    return false;
  }
  const fileReader = new FileReader();
  fileReader.onload = (files) => {
    try {
      const data = files.target.result;
      const workbook = xlsx.read(data, {
        type: 'binary'
      });
      const wsname = workbook.SheetNames[1]; // 取第一张表
      // 获取第一个工作表
      const worksheet = workbook.Sheets[wsname];

      // 将工作表转换为JSON对象
      const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

      // 获取表头
      const headers = jsonData[0];
      const ws = xlsx.utils.sheet_to_json(workbook.Sheets[wsname]); // 生成json表格内容
      fullscreenLoading.value = false;
      shpFields.value = headers;
      excleList.value = ws;
      initExcel();
    } catch (e) {
      return false;
    }
  };
  fileReader.readAsBinaryString(files[0]);
};
// 给表分组
const initExcel = () => {
  // this.excleList
  const groupList = []; //分完组的列表
  const startIndexList = []; //截取的开始下标集合
  const endIndexList = []; //截取的结束下标集合
  excleList.value.forEach((v, vdx) => {
    if (v[shpFields.value[0]] == '序号') {
      //表格开头
      const startIndex = vdx + 3;
      startIndexList.push(startIndex);
    } else if (v.__EMPTY && v.__EMPTY.includes('填表说明')) {
      //结束的地方
      const endIndex = vdx - 1;
      endIndexList.push(endIndex);
    }
  });
  // 组装成分组数据
  startIndexList.forEach((v, vdx) => {
    const list = []; //每条承包商对应的数据列表
    for (let i = v; i < endIndexList[vdx] + 1; i++) {
      list.push(excleList.value[i]);
    }
    groupList.push(list);
  });
  // 组装成真实数据
  submitList.value = [];
  groupList.forEach((v) => {
    const item = {
      cbfxm: v[0].__EMPTY, //承包方姓名
      cbtdzmj: v[0].__EMPTY_1, //承包土地总面积
      dkList: [], //地块列表
      jtcyList: [] //家庭成员列表
    };
    v.forEach((k) => {
      if (k.__EMPTY_2) {
        //代表有地块编号，需要把数据放入item.dkList里面
        item.dkList.push({ dkbm: k.__EMPTY_2, dkmc: k.__EMPTY_28, htmj: k.__EMPTY_29 });
      }
      if (k.__EMPTY_10) {
        //代表有家庭成员，需要把数据放入item.jtcyList里面
        item.jtcyList.push({ xm: k.__EMPTY_10, ycbfdmgx: k.__EMPTY_11, sfzhm: k.__EMPTY_12, cybz: k.__EMPTY_23 }); //ycbfdmgx:与承包方代表关系
      }
    });
    submitList.value.push(item);
  });
};

// 提交数据
const submit = async () => {
  const dksxParmas = []; //地块属性需要提交的列表
  const cbfjtcyParmas = []; //承包方家庭成员需要提交的列表
  submitList.value.forEach((v) => {
    v.dkList.forEach((k) => {
      // 组装地块属性对象
      const dksxItem = {
        appId: 0,
        attribution: {
          DKMC: k.dkmc,
          DKBM: k.dkbm
        },
        groupId: dksxGroupId.value,
        linkId: dksxLinkid.value,
        ruleAttribution: dksxruleAttribution.value,
        groupModel: {
          id: dksxGroupId.value,
          linkId: dksxLinkid.value,
          fieldModelList: [
            {
              fieldName: 'DKBM' //地块编码来对照
            }
          ]
        }
      };
      dksxParmas.push(dksxItem);
      v.jtcyList.forEach((q) => {
        // 组装承包方家庭成员
        const cbfjtcyItem = {
          appId: 0,
          attribution: {
            CYXM: q.xm,
            CYZJHM: q.sfzhm,
            YHZGX: getFieldCode(q.ycbfdmgx),
            CYBZSM: q.cybz,
            DKBM: k.dkbm
          },
          groupId: cbfJtcyGroupId.value,
          linkId: cbfjtcyLinkid.value,
          ruleAttribution: cbfjtcyruleAttribution.value,
          groupModel: {
            id: cbfJtcyGroupId.value,
            linkId: cbfjtcyLinkid.value,
            fieldModelList: [
              {
                fieldName: 'DKBM' //地块编码来对照
              }
            ]
          }
        };
        cbfjtcyParmas.push(cbfjtcyItem);
      });
    });
  });
  dksxTotal.value = dksxParmas.length;
  jtcyTotal.value = cbfjtcyParmas.length;
  const chunkSizeDksx = 10;
  const chunksDksx = [];
  const chunkSizeJtcy = 10;
  const chunksJtcy = [];

  // 拆分数组
  for (let i = 0; i < dksxParmas.length; i += chunkSizeDksx) {
    chunksDksx.push(dksxParmas.slice(i, i + chunkSizeDksx));
  }
  // 拆分数组
  for (let i = 0; i < cbfjtcyParmas.length; i += chunkSizeJtcy) {
    chunksJtcy.push(cbfjtcyParmas.slice(i, i + chunkSizeJtcy));
  }
  submitProgressDialog.value = true;
  for (let i = 0; i < chunksDksx.length; i++) {
    try {
      await subsectionSubmit(chunksDksx[i], i, 1);
    } catch (error) {
      // this.$message.error(error)
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  for (let i = 0; i < chunksJtcy.length; i++) {
    try {
      await subsectionSubmit(chunksJtcy[i], i, 2);
    } catch (error) {
      // this.$message.error(error)
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  // this.submitProgressDialog = false
  const str = `<span>成功更新【地块属性】：${dksxCount.value}条</span><span style="color:#ff4343;margin-left:10px">失败：${dksxUploadError.value.length}条</span><br>
      <span>成功更新【承包方家庭成员】：${jtcyCount.value}条</span><span style="color:#ff4343;margin-left:10px">失败：${jtcyUploadError.value.length}条</span>`;
  ElMessageBox.alert(str, `更新成功`, {
    confirmButtonText: '确定',
    dangerouslyUseHTMLString: true,
    callback: (action) => {
      // 在这添加是否切换公司的标识。
      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
      sessionStorage.setItem('qiehuan_company', 'false');
      location.reload();
    }
  });
};

/**
 * excel导入分段提交
 * @param list
 * @param num
 * @param type
 * @returns
 */
const subsectionSubmit = async (list, num, type) => {
  // list 列表 num第几次 type 1地块信息 2家庭成员
  return new Promise((resolve, reject) => {
    if (isAllow.value) {
      let moreFlag = 0;
      if (type == 2) {
        //一对多 同时更新多条
        moreFlag = 1;
      }
      updateInstance(list, moreFlag).then((res) => {
        if (res.code == 200) {
          if (type == 1) {
            dksxCount.value = dksxCount.value + list.length;
            dksxPlan.value = parseFloat((((num * 10 + list.length) / dksxTotal.value) * 100).toFixed(0));
          } else if (type == 2) {
            jtcyCount.value = jtcyCount.value + list.length;
            jtcyPlan.value = parseFloat((((num * 10 + list.length) / jtcyTotal.value) * 100).toFixed(0));
          }
          resolve(null);
        } else {
          if (type == 1) {
            dksxUploadError.value.push(...list);
          } else if (type == 2) {
            jtcyUploadError.value.push(...list);
          }
          reject(res.msg);
        }
      });
    } else {
      resolve(null);
    }
  });
};

/**
 * select 得到与户主关系的value值
 * @param label
 */
const getFieldCode = (label) => {
  let value = '';
  for (let i = 0; i < YHZGXOptions.value.length; i++) {
    if (YHZGXOptions.value[i].label == label) {
      value = YHZGXOptions.value[i].value;
      break;
    }
  }
  return value;
};

const handleCloseProDia = () => {
  submitProgressDialog.value = false;
  isAllow.value = false;
};
</script>
<style lang="scss" scoped>
:deep(.el-upload) {
  text-align: left;
}
.updateSpe-main {
}
.title-label {
  margin-bottom: 10px;
  margin-top: 10px;
}
.progress-label {
  display: flex;
  align-items: center;
  margin: 10px 0px;
}
</style>
