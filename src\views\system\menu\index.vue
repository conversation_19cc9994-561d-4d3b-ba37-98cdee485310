<template>
  <container-card>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch">
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="菜单状态" clearable>
          <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="default" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:menu:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      row-key="menuId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="160"></el-table-column>
      <el-table-column prop="icon" label="图标" align="center" width="100">
        <template #default="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
      <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <!-- TODO: 当升级到Element Plus 3.0.0后，将type="text"替换为type="link" -->
          <el-button size="small" type="primary" text icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:menu:edit']">修改</el-button>
          <el-button size="small" type="primary" text icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:menu:add']">新增</el-button>
          <el-button size="small" type="primary" text icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:menu:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="680px" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :multiple="false"
                :options="menuOptions"
                :show-count="true"
                placeholder="选择上级菜单"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="form.menuType">
                <el-radio value="M">目录</el-radio>
                <el-radio value="C">菜单</el-radio>
                <el-radio value="F">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.menuType != 'F'">
            <el-form-item label="菜单图标" prop="icon">
              <IconSelect v-model="form.icon" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="isFrame">
              <template #label>
                <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                是否外链
              </template>
              <el-radio-group v-model="form.isFrame">
                <el-radio value="0">是</el-radio>
                <el-radio value="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="path">
              <template #label>
                <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                路由地址
              </template>
              <el-input v-model="form.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="component">
              <template #label>
                <el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                组件路径
              </template>
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'M'">
            <el-form-item prop="perms">
              <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
              <template #label>
                <el-tooltip content="控制器中定义的角色英文名，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                角色英文名
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="query">
              <el-input v-model="form.query" placeholder="请输入路由参数" maxlength="255" />
              <template #label>
                <el-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                路由参数
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="isCache">
              <template #label>
                <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                是否缓存
              </template>
              <el-radio-group v-model="form.isCache">
                <el-radio value="0">缓存</el-radio>
                <el-radio value="1">不缓存</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="visible">
              <template #label>
                <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                显示状态
              </template>
              <el-radio-group v-model="form.visible">
                <el-radio v-for="dict in sys_show_hide" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="status">
              <template #label>
                <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
                菜单状态
              </template>
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="控制台专属">
              <el-radio-group v-model="spesal" @change="radioChange">
                <el-radio value="1">是</el-radio>
                <el-radio value="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限控制">
              <el-select v-model="form.vipType" placeholder="请选择">
                <el-option v-for="item in vipList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, onMounted, toRefs } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from '@/api/system/menu';
import IconSelect from '@/components/IconSelect/index.vue';
import { useUserStore } from '@/store/modules/user';
import Treeselect from '@zanmato/vue3-treeselect';
import '@zanmato/vue3-treeselect/dist/vue3-treeselect.min.css';

// 临时实现 useDict 钩子函数
const useDict = (...args: string[]) => {
  // 为每个字典类型创建一个响应式数组
  const result: Record<string, any[]> = {};
  args.forEach((dictType) => {
    if (dictType === 'sys_show_hide') {
      result[dictType] = [
        { label: '显示', value: '0' },
        { label: '隐藏', value: '1' }
      ];
    } else if (dictType === 'sys_normal_disable') {
      result[dictType] = [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ];
    } else {
      result[dictType] = [];
    }
  });
  return result;
};

interface MenuFormData {
  menuId?: number | string;
  parentId: number | string;
  menuName?: string;
  icon?: string;
  menuType: string;
  orderNum?: number | undefined;
  isFrame: string;
  isCache: string;
  visible: string;
  status: string;
  perms?: string;
  component?: string;
  path?: string;
  query?: string;
  vipType: number;
  companyId?: string | null;
}

interface MenuQueryParams {
  menuName?: string;
  visible?: string;
  status?: string;
  companyId?: string | number;
}

interface MenuItem {
  menuId: string | number;
  menuName: string;
  parentId?: string | number;
  icon?: string;
  perms?: string;
  component?: string;
  status?: string;
  createTime?: string;
  orderNum?: number;
  children?: MenuItem[];
}

interface VipOption {
  label: string;
  value: number;
}

// 字典数据
const { sys_show_hide, sys_normal_disable } = useDict('sys_show_hide', 'sys_normal_disable');

// 用户信息
const userStore = useUserStore();
const user = computed(() => userStore.user as any);

// 表单引用
const formRef = ref<FormInstance>();
const queryFormRef = ref<FormInstance>();

// 遮罩层
const loading = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 菜单表格树数据
const menuList = ref<MenuItem[]>([]);
// 菜单树选项
const menuOptions = ref<any[]>([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 是否展开，默认全部折叠
const isExpandAll = ref(false);
// 重新渲染表格状态
const refreshTable = ref(true);
// 是否是控制台专属
const spesal = ref('2');

// 查询参数
const queryParams = reactive<MenuQueryParams>({
  menuName: undefined,
  visible: undefined,
  status: undefined,
  companyId: user.value?.companyId
});

// 表单参数
const form = reactive<MenuFormData>({
  menuId: undefined,
  parentId: 0,
  menuName: undefined,
  icon: undefined,
  menuType: 'M',
  orderNum: undefined,
  isFrame: '1',
  isCache: '0',
  visible: '0',
  status: '0',
  vipType: 1,
  companyId: null
});

// 表单校验
const rules = reactive({
  menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
  orderNum: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
  path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }]
});

// 权限类型选项
const vipList = ref<VipOption[]>([
  { label: '个人版', value: 1 },
  { label: '专业版', value: 2 },
  { label: '企业版', value: 3 }
]);

/** 查询菜单列表 */
const getList = async () => {
  loading.value = true;
  try {
    const response = await listMenu(queryParams);
    menuList.value = handleTree(response.data, 'menuId');
  } finally {
    loading.value = false;
  }
};

/** 转换菜单数据结构 */
const normalizer = (node: any) => {
  if (node.children && !node.children.length) {
    delete node.children;
  }
  return {
    id: node.menuId,
    label: node.menuName,
    children: node.children
  };
};

/** 查询菜单下拉树结构 */
const getTreeselect = async () => {
  const params = {
    companyId: user.value.companyId
  } as any; // 使用类型断言
  const response = await listMenu(params);
  menuOptions.value = [];
  const menu = { menuId: 0, menuName: '主类目', children: [] };
  menu.children = handleTree(response.data, 'menuId');
  menuOptions.value.push(menu);
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 表单重置
const reset = () => {
  form.menuId = undefined;
  form.parentId = 0;
  form.menuName = undefined;
  form.icon = undefined;
  form.menuType = 'M';
  form.orderNum = undefined;
  form.isFrame = '1';
  form.isCache = '0';
  form.visible = '0';
  form.status = '0';
  form.vipType = 1;
  form.companyId = null;
  spesal.value = '2';
  formRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = (row?: MenuItem | MouseEvent) => {
  reset();
  getTreeselect();
  if (row && 'menuId' in row) {
    form.parentId = row.menuId;
  } else {
    form.parentId = 0;
  }
  open.value = true;
  title.value = '添加菜单';
};

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row: MenuItem) => {
  reset();
  await getTreeselect();
  const response = await getMenu(row.menuId);
  Object.assign(form, response.data);
  if (response.data.companyId === '1') {
    spesal.value = '1';
  } else {
    spesal.value = '2';
  }
  open.value = true;
  title.value = '修改菜单';
};

/** 提交按钮 */
const submitForm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.menuId !== undefined) {
        const response = await updateMenu(form as any);
        ElMessage.success('修改成功');
        open.value = false;
        getList();
      } else {
        const response = await addMenu(form as any);
        if (response.code === 200) {
          ElMessage.success('新增成功');
          open.value = false;
          getList();
        } else {
          ElMessage.error(response.msg);
        }
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = (row: MenuItem) => {
  ElMessageBox.confirm(`是否确认删除名称为"${row.menuName}"的数据项？`)
    .then(async () => {
      await delMenu(row.menuId);
      getList();
      ElMessage.success('删除成功');
    })
    .catch(() => {});
};

// 是否控制台专属
const radioChange = (e: string) => {
  if (e === '1') {
    // 控制台专属
    form.companyId = '1';
  } else {
    form.companyId = null;
  }
};

// 处理树形数据函数，假设已在项目中定义
const handleTree = (data: any[], id: string, parentId?: string, children?: string): any[] => {
  // 这里应该是从项目中的工具函数引入的，为了编译通过进行简单实现
  // 实际项目中请使用项目已定义的handleTree函数
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  const childrenListMap: Record<string, any[]> = {};
  const nodeIds: Record<string, boolean> = {};
  const tree: any[] = [];

  for (const d of data) {
    const parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = true;
    childrenListMap[parentId].push(d);
  }

  for (const d of data) {
    const parentId = d[config.parentId];
    if (!nodeIds[parentId]) {
      tree.push(d);
    }
  }

  const adaptToChildrenList = (o: any) => {
    if (childrenListMap[o[config.id]]) {
      o[config.childrenList] = childrenListMap[o[config.id]];
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  };

  for (const t of tree) {
    adaptToChildrenList(t);
  }

  return tree;
};

// 时间格式化函数，假设已在项目中定义
const parseTime = (time: string) => {
  // 实际项目中请使用项目已定义的parseTime函数
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleString();
};

// 生命周期钩子
onMounted(() => {
  getList();
});
</script>
