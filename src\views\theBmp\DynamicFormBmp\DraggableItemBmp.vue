<script lang="ts">
import draggable from 'vuedraggable';
import render from './components/render';
import { h, defineComponent, watch } from 'vue';
import { ElInput, ElFormItem, ElCol, ElRow, ElIcon } from 'element-plus';
import { Close } from '@element-plus/icons-vue';
interface FormItem {
  formId: string | number;
  span: number;
  label: string;
  labelWidth?: number;
  required?: boolean;
  'value-format'?: string;
  icon?: string;
  defaultValue?: any;
  renderKey?: string;
  layout?: string;
  type?: string;
  justify?: string;
  align?: string;
  gutter?: number;
  children?: FormItem[];
  cmpType?: string;
  rowType?: string;
  actionText?: string;
  tag?: string;
  showDuration?: boolean;
  proCondition?: boolean;
  multiple?: boolean;
}

interface FormConfig {
  unFocusedComponentBorder?: boolean;
}

interface ComponentContext {
  element: FormItem;
  index: number;
  drawingList: FormItem[];
  activeId: string | number;
  formConf: FormConfig;
  emit: (event: string, ...args: any[]) => void;
  handleDragEnd: (evt: any) => void;
}

const components = {
  itemBtns(this: ComponentContext, h: any, element: FormItem, index: number, parent: FormItem[], root?: FormItem) {
    const visibility = 'visibility:' + (root && root.cmpType === 'custom' ? 'hidden;' : 'visible;');
    return [
      h(
        'span',
        {
          class: 'drawing-item-delete',
          style: visibility,
          title: '删除',
          onClick: (event: Event) => {
            this.emit('deleteItem', index, parent);
            event.stopPropagation();
          }
        },
        [
          h(
            ElIcon,
            {
              style: {
                fontSize: '16px',
                color: '#666'
              }
            },
            () => h(Close)
          )
        ]
      )
    ];
  }
};

const layouts = {
  colFormItem(this: ComponentContext, h: any, element: FormItem, index: number, parent: FormItem[], root?: FormItem) {
    let className = this.activeId === element.formId ? 'drawing-item active-from-item' : 'drawing-item';
    if (this.formConf.unFocusedComponentBorder) className += ' unfocus-bordered';

    if (['time', 'time-range', 'date', 'date-range'].includes(element.icon)) {
      return h(
        ElCol,
        {
          span: element.span,
          class: className,
          'data-form-id': element.formId,
          onClick: (event: Event) => {
            this.emit('activeItem', element);
            event.stopPropagation();
          }
        },
        [
          h(
            ElFormItem,
            {
              // 'label-width': element.labelWidth ? `${element.labelWidth}px` : null,
              label: element.label,
              required: element.required,
              'value-format': element['value-format']
            },
            [
              h(render, {
                key: `${element.formId}_${element.label}_${index}`,
                conf: element,
                onInput: (event: any) => {
                  this.emit('update:element', { ...element, defaultValue: event });
                }
              })
            ]
          ),
          ...components.itemBtns.call(this, h, element, index, parent, root)
        ]
      );
    } else {
      return h(
        ElCol,
        {
          span: element.span,
          class: className,
          'data-form-id': element.formId,
          onClick: (event: Event) => {
            this.emit('activeItem', element);
            event.stopPropagation();
          }
        },
        [
          h(
            ElFormItem,
            {
              // 'label-width': element.labelWidth ? `${element.labelWidth}px` : null,
              label: element.label,
              required: element.required
            },
            [
              h(render, {
                key: `${element.formId}_${element.label}_${index}`,
                conf: element,
                onInput: (event: any) => {
                  if (element.icon === 'idCardIcon') {
                    const idRegex = / (^(\d{17}([0-9]|X|x))$)/;
                    let idResult: string,
                      str = '';
                    if (!idRegex.test(event)) {
                      idResult = event.replace(/[^0-9Xx]/g, '');
                      str = idResult.substring(0, 18);
                    }
                    this.emit('update:element', { ...element, defaultValue: str });
                  } else {
                    this.emit('update:element', { ...element, defaultValue: event });
                  }
                }
              })
            ]
          ),
          ...components.itemBtns.call(this, h, element, index, parent, root)
        ]
      );
    }
  },
  rowFormItem(this: ComponentContext, h: any, element: FormItem, index: number, parent: FormItem[]) {
    const className = this.activeId === element.formId ? 'drawing-row-item active-from-item' : 'drawing-row-item';

    let child = renderChildren.call(this, h, element, index, parent);
    if (element.type === 'flex') {
      child = h(
        ElRow,
        {
          type: element.type,
          justify: element.justify,
          align: element.align
        },
        () => child
      );
    }

    const group = { name: 'componentsGroup', put: (...arg: any[]) => this.emit('put', ...arg, element) };
    const isCustom = element.cmpType === 'custom';

    return h(ElCol, { span: element.span }, [
      h(
        ElRow,
        {
          gutter: element.gutter,
          class: className,
          'data-form-id': element.formId,
          style: 'margin-left:0;',
          onClick: (event: Event) => {
            this.emit('activeItem', element);
            event.stopPropagation();
          }
        },
        [
          h('span', { class: 'component-name' }, () => element.label),
          h(
            draggable,
            {
              props: {
                list: element.children,
                animation: 0,
                group: group,
                disabled: isCustom
              },
              class: 'drag-wrapper',
              onEnd: this.handleDragEnd
            },
            () => child
          ),
          ...components.itemBtns.call(this, h, element, index, parent),
          element.rowType === 'table' &&
            h(
              'div',
              {
                style: 'text-align: center;background: white;color: #4e79ff;padding: .4rem 1rem;'
              },
              [h('i', { class: 'el-icon-plus' }), () => element.actionText]
            )
        ]
      )
    ]);
  }
};

function renderChildren(this: ComponentContext, h: any, element: FormItem, index: number, parent: FormItem[]) {
  if (!Array.isArray(element.children)) return null;
  return element.children.map((el, i) => {
    const layout = layouts[el.layout];
    if (layout) {
      return layout.call(this, h, el, i, element.children, element);
    }
    return layoutIsNotFound.call(this);
  });
}

function layoutIsNotFound(this: ComponentContext) {
  throw new Error(`没有与${this.element.layout}匹配的layout`);
}

export default defineComponent({
  components: {
    render,
    draggable
  },
  props: {
    element: {
      type: Object as () => FormItem,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    drawingList: {
      type: Array as () => FormItem[],
      required: true
    },
    activeId: {
      type: [String, Number],
      required: true
    },
    formConf: {
      type: Object as () => FormConfig,
      required: true
    },
    put: {
      type: Function,
      required: true
    }
  },
  emits: ['activeItem', 'copyItem', 'deleteItem', 'update:element'],
  setup(props, { emit }) {
    const updateActiveClass = (element: FormItem) => {
      const items = document.querySelectorAll('.drawing-item, .drawing-row-item');
      items.forEach((item) => {
        const formId = item.getAttribute('data-form-id');
        if (formId && props.activeId === parseInt(formId, 10)) {
          item.classList.add('active-from-item');
        } else {
          item.classList.remove('active-from-item');
        }
      });
    };

    const handleDragEnd = (evt: any) => {
      if (evt.added) {
        // 处理新增元素
        const newElement = evt.added.element;
        emit('activeItem', newElement);
      } else if (evt.moved) {
        // 处理移动元素
        const movedElement = props.drawingList[evt.moved.newIndex];
        emit('activeItem', movedElement);
      }
    };

    // watch(
    //   () => props.drawingList,
    //   () => {
    //     if (props.element) {
    //       updateActiveClass(props.element);
    //     }
    //   },
    //   { deep: true }
    // );

    watch(
      () => props.activeId,
      () => {
        if (props.element) {
          updateActiveClass(props.element);
        }
      }
    );

    return {
      ...props,
      emit,
      handleDragEnd
    };
  },
  render() {
    const layout = layouts[this.element.layout];
    if (layout) {
      return layout.call(this, h, this.element, this.index, this.drawingList);
    }
    return layoutIsNotFound.call(this);
  }
});
</script>
