<template>
  <el-form label-width="100px">
    <el-form-item label="格式化">
      <el-input v-model="attributeCopy.content" />
    </el-form-item>
    <el-form-item label="小数位数">
      <el-input-number :min="0" :max="10" v-model="attributeCopy.toFixedNum" />
    </el-form-item>
    <el-form-item label="对齐方式">
      <el-select v-model="attributeCopy.textAlign">
        <el-option label="center" value="center" />
        <el-option label="left" value="left" />
        <el-option label="right" value="right" />
      </el-select>
    </el-form-item>
    <el-form-item label="行间距">
      <el-input-number :min="0" :max="300" v-model="attributeCopy.rowGap" />
    </el-form-item>
    <el-form-item label="字体大小">
      <el-input-number :min="0" :max="100" v-model="attributeCopy.style.fontSize" />
    </el-form-item>
    <el-form-item label="字体颜色">
      <el-color-picker v-model="attributeCopy.style.fill" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-dataV-digitalFlop-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();
const attributeCopy = computed(() => props.attribute);
</script>

<style scoped></style>
