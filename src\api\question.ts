import request from "@/utils/request"

// Define interfaces for request parameters and responses
interface QuestionParams {
  id?: string | number;
  title?: string;
  remark?: string;
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}

interface QuestionResponse {
  code: number;
  msg: string;
  data: any;
}

interface QuestionListResponse extends QuestionResponse {
  data: {
    list: Array<{
      id: string | number;
      title: string;
      remark: string;
      status: number;
      count: number;
      createTime: string;
      [key: string]: any;
    }>;
    total?: number;
  };
}

// 保存问卷
export function saveQuestion(params: QuestionParams): Promise<QuestionResponse> {
  return request({
    url: '/qjt/questionnaire/save',
    method: 'post',
    data: params
  })
}

// 问卷列表
export function questionList(params: QuestionParams): Promise<QuestionListResponse> {
  return request({
    url: '/qjt/questionnaire/selectList',
    method: 'post',
    data: params
  })
}

// 根据id查询详情 
export function getQuestionDetail(params: string | number): Promise<QuestionResponse> {
  return request({
    url: `/qjt/questionnaire/selectOne?id=${params}`,
    method: 'post',
  })
}

// 保存问卷调查结果
export function saveQuestionResult(params: QuestionParams): Promise<QuestionResponse> {
  return request({
    url: '/qjt/questionnaire/saveResult',
    method: 'post',
    data: params
  })
}

// 导出问卷报告
export function exportWord(params: string | number): Promise<Blob> {
  return request({
    url: `/qjt/questionnaire/downloadone/${params}`,
    method: 'get',
    responseType: 'blob', // 将文件流转成blob对象
  })
}

// 导出问卷详情
export function exportExcel(params: string | number): Promise<Blob> {
  return request({
    url: `/qjt/questionnaire/downloadoneExcel/${params}`,
    method: 'get',
    responseType: 'blob', // 将文件流转成blob对象
  })
}

// 查看文件详情
export function exportList(params: string | number): Promise<Blob> {
  return request({
    url: `/qjt/questionnaire/downloadoneList/${params}`,
    method: 'get',
    responseType: 'blob', // 将文件流转成blob对象
  })
} 