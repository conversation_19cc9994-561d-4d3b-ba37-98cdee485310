<template>
  <div class="forum-container">
    <div class="forum-icon" @click="handleOpenDrawer">
      <svg-icon-hover icon-class="forum-grey" style="margin: 8px"></svg-icon-hover>
      <!-- <el-image :src="forumIcon"></el-image> -->
    </div>
    <!-- 抽屉 -->
    <el-drawer
      :title="titleDrawer"
      v-model="isOpenforumDrawer"
      direction="rtl"
      :modal="false"
      style="margin-top: 85px"
      class="forum-drawer-contianer"
      :before-close="handleCloseForum"
    >
      <div class="forum-drawer-content">
        <select-tab :tab-list="tabList" @clickValue="searchForumList" style="margin-top: 12px"></select-tab>
        <div style="height: calc(100vh - 200px); margin-bottom: 130px">
          <div v-for="item in forumList" :key="item.id">
            <forum-content :forum-item="item" @detialInfo="handleForumDetial"></forum-content>
          </div>
        </div>
      </div>
      <div class="forum-drawer-footer">
        <el-button type="primary" icon="EditOutlined" size="small" class="forum-btn" @click="handleAddForum">我要发言</el-button>
      </div>
    </el-drawer>

    <!-- 我要发言--发表主题评论 -->
    <el-dialog title="我要发言" v-model="forumDialogFormVisible" @closed="handleCloseForumForm" :close-on-click-modal="false">
      <el-form :model="forumForm" ref="forumFormRef" :rules="forumRules">
        <el-form-item label="主题类别" prop="titleType">
          <el-select v-model="forumForm.titleType" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in forumTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主题题目" prop="titleInfo">
          <el-input v-model="forumForm.titleInfo" autocomplete="off" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="主题内容" prop="contents">
          <el-input
            type="textarea"
            v-model="forumForm.contents"
            autocomplete="off"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="1000"
          ></el-input>
        </el-form-item>
        <div class="text-label">主题图片</div>
        <div class="upload-card">
          <el-upload
            list-type="picture-card"
            :on-change="handleChangeUploadImg"
            :action="`${base}/system/option/multi/upload`"
            :auto-upload="false"
            :file-list="forumImgFileList"
            accept=".jpg,.png"
            :headers="headers"
          >
            <svg-icon icon-class="tjtp" style="width: 32px; height: 32px" />
          </el-upload>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="forumDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitForum">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 论坛详情页面抽屉 -->
    <forum-detial
      v-model:isOpenforumDetialDrawer="isOpenforumDetialDrawer"
      :detialItem="detialItem"
      @closeForum="handleCloseForumDetial"
      :commentsList="commentsList"
    ></forum-detial>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import forumIcon from '@/assets/images/forum.png';
import SelectTab from '@/components/selectTab/selectTab.vue';
import ForumContent from './forumContent.vue';
import forumDetial from './forumDetial.vue';
import { addForum, getForumCheckList, uploadImgae } from '@/api/forum';
import { getToken } from '@/utils/auth';
import svgIconHover from '../SvgIconHover/index';
import svgIcon from '../SvgIcon/index';
import type { FormInstance, UploadFile, UploadUserFile } from 'element-plus';

interface ForumItem {
  id: string | number;
  titleId?: string | number;
  picUrl?: string;
  contents?: string;
  titleInfo?: string;
  titleType?: number | string;
  [key: string]: any;
}

interface TabItem {
  label: string;
  value: string | number;
  isClick: boolean;
}

interface ForumTypeOption {
  label: string;
  value: number;
}

interface ForumFormType {
  picUrl: string;
  contents: string;
  titleInfo: string;
  titleType: number | string;
}

// 论坛图标
const forumIcon = forumIcon;
// 是否打开论坛抽屉框
const isOpenforumDrawer = ref(false);
const titleDrawer = ref('论坛');

// 切换选项
const tabList = ref<TabItem[]>([
  { label: '全部', value: '', isClick: true },
  { label: '系统建议', value: 1, isClick: false },
  { label: 'BUG反馈', value: 2, isClick: false },
  { label: '交流互动', value: 3, isClick: false }
]);

// 论坛列表
const forumList = ref<ForumItem[]>([]);
// tab 筛选
const titleType = ref<number | string>(0);
// 打开我要发言语列表
const forumDialogFormVisible = ref(false);

// 添加的主题对象
const forumForm = reactive<ForumFormType>({
  picUrl: '',
  contents: '',
  titleInfo: '',
  titleType: ''
});

const forumFormRef = ref<FormInstance>();

// 论坛校验
const forumRules = reactive({
  titleInfo: [{ required: true, message: '请填写主题题目', trigger: 'blur' }],
  titleType: [{ required: true, message: '请选择主题类型', trigger: 'change' }],
  contents: [{ required: true, message: '请填写主题内容', trigger: 'blur' }]
});

// 主题的类型选择
const forumTypeOptions = ref<ForumTypeOption[]>([
  { label: '系统建议', value: 1 },
  { label: 'bug反馈', value: 2 },
  { label: '交流互动', value: 3 }
]);

// 上传的图片数组
const forumImgFileList = ref<UploadUserFile[]>([]);
const base = process.env.VUE_APP_BASE_API;
const headers = {
  'Authorization': 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};

// 打开评论详情抽屉
const isOpenforumDetialDrawer = ref(false);
// 点击论坛的详情
const detialItem = ref<ForumItem>({} as ForumItem);
// 评论详情数据
const commentsList = ref<ForumItem[]>([]);

// 获取论坛列表
const getForumTableList = () => {
  const params = {
    seq: 0,
    checkStatus: 1,
    titleType: titleType.value
  };
  getForumCheckList(params).then((res) => {
    if (res.code == 200) {
      forumList.value = res.data.rows;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 打开抽屉框
const handleOpenDrawer = () => {
  isOpenforumDrawer.value = true;
  getForumTableList();
};

// 关闭论坛
const handleCloseForum = () => {
  isOpenforumDrawer.value = false;
};

// tab 筛选
const searchForumList = (val: string | number) => {
  titleType.value = val;
  getForumTableList();
};

// 我要发言弹框
const handleAddForum = () => {
  forumDialogFormVisible.value = true;
};

// 提交主题
const handleSubmitForum = async () => {
  if (!forumFormRef.value) return;

  await forumFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const resUrl = await handleUploadFileServe();
        const params = {
          picUrl: resUrl,
          contents: forumForm.contents,
          titleInfo: forumForm.titleInfo,
          seq: 0,
          titleType: forumForm.titleType
        };

        const res = await addForum(params);
        if (res.code == 200) {
          forumDialogFormVisible.value = false;
          ElMessageBox.confirm('内容待管理员审核', '发表成功', {
            confirmButtonText: '确定',
            type: 'warning'
          }).catch(() => {});
        } else {
          ElMessage.success(res.msg);
        }
      } catch (error) {
        console.error(error);
      }
    }
  });
};

// 上传图片
const handleChangeUploadImg = (file: UploadFile, fileList: UploadUserFile[]) => {
  if (!file.raw) return false;

  const isJPG = file.raw.type === 'image/png' || file.raw.type === 'image/jpeg';
  const isSize = file.size / 1024 / 1024;

  if (isSize > 5) {
    const index = fileList.findIndex((e) => e.size / 1024 / 1024 == isSize);
    if (index !== -1) {
      fileList.splice(index, 1);
    }
    ElMessage.warning('上传的文件不能超过5MB');
    return false;
  }

  if (!isJPG) {
    ElMessage.error('只能上传image/png格式图片');
    return false;
  }

  if (isSize < 5 && isJPG) {
    forumImgFileList.value = fileList;
  }

  return isJPG && isSize < 5;
};

// 上传文件
const handleUploadFileServe = () => {
  const formData = new FormData();
  forumImgFileList.value.forEach((file) => {
    if (file.raw) {
      formData.append('files', file.raw);
    }
  });

  return new Promise<string>((resolve, reject) => {
    uploadImgae(formData)
      .then((res) => {
        if (res.code == 200) {
          let picUrl = '';
          res.data.forEach((item: { path: string }) => {
            picUrl += `${item.path};`;
          });
          resolve(picUrl);
        } else {
          ElMessage.error(res.msg);
          reject();
        }
      })
      .catch(reject);
  });
};

// 关闭添加论坛对话框时的内容 清除数据
const handleCloseForumForm = () => {
  forumDialogFormVisible.value = false;
  Object.assign(forumForm, {
    picUrl: '',
    contents: '',
    titleInfo: '',
    titleType: ''
  });
  forumImgFileList.value = [];
};

// 查看论坛详细信息
const handleForumDetial = (item: ForumItem) => {
  isOpenforumDrawer.value = false;
  isOpenforumDetialDrawer.value = true;
  detialItem.value = item;
  getForumCommentsList(item);
};

// 关闭论坛详细信息
const handleCloseForumDetial = () => {
  isOpenforumDrawer.value = true;
  isOpenforumDetialDrawer.value = false;
};

// 获取评论信息
const getForumCommentsList = (item: ForumItem) => {
  const titleId = item.titleId;
  const params = {
    titleId: titleId,
    checkStatus: 1
  };
  getForumCheckList(params).then((res) => {
    if (res.code == 200) {
      commentsList.value = res.data.rows;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
</script>

<style lang="scss" scoped>
.forum-container {
  .forum-icon {
    width: 32px;
    height: 32px;
    background: #dbe7ee;
    border-radius: 50%;
    margin-top: 12px;
    overflow: hidden;
    cursor: pointer;
    &:hover {
      :deep(.svg-icon) {
        transform: translateX(-30px);
        z-index: 999;
      }
    }
    .el-image {
      cursor: pointer;
      font-size: 12px;
      width: 16px;
      height: 16px;
      margin-left: 8px;
      margin-bottom: 8px;
    }
  }
  .forum-drawer-contianer {
    :deep(.el-drawer__header) {
      height: 44px;
      background: #fff;
      border-radius: 0px 0px 0px 0px;
      border-bottom: 1px solid #dbe7ee;
      margin: 0;
      span {
        width: 100px;
        height: 44px;
        font-size: 16px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #161d26;
        line-height: 44px;
        padding: 0;
        margin-bottom: 20px;
      }
      .el-drawer__close-btn {
        font-size: 16px;
        margin-bottom: 20px;
      }
    }
    .forum-drawer-footer {
      position: absolute;
      height: 48px;
      background: #ffffff;
      border-radius: 0px 0px 0px 0px;
      bottom: 0;
      right: 0;
      left: 0;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #dbe7ee;
      .forum-btn {
        position: absolute;
        margin-top: 10px;
        margin-bottom: 10px;
        margin-right: 20px;
      }
    }
  }
}
.text-label {
  text-align: right;
  width: 78px;
  vertical-align: middle;
  font-size: 14px;
  color: #606266;
  font-weight: 700;
  line-height: 40px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.upload-card {
  display: flex;
}
.upload-card :deep(.el-upload--picture-card) {
  height: 71px;
  width: 71px;
  line-height: 80px;
}
.upload-card :deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin-top: 4%;
  height: 71px;
  width: 71px;
  line-height: 80px;
}
</style>
