<template>
  <div>
    <el-dialog title="图片素材库" v-model="modelShow" width="75%" :close-on-click-modal="false" append-to-body>
      <el-tabs v-model="activeGroup" @tab-click="tabClick" type="border-card" @tab-remove="removeTab">
        <el-tab-pane v-for="item in groupPanes" :key="item.id" :label="item.groupName" :name="item.id">
          <el-row :gutter="6">
            <el-col :span="4" v-for="item2 in imgData" :key="item2.id" @click="checkImg(item2)">
              <div style="height: 150px" :style="checkedItem.id === item2.id ? 'border: 2px solid #409eff;' : 'border: 1px solid #ccc;'">
                <el-image style="width: 100%; height: 100%" :src="imgUrl + item2.filePath" fit="fill" />
              </div>
              <div style="height: 20px; margin-bottom: 6px; overflow: hidden">{{ item2.imgName }}</div>
            </el-col>
          </el-row>
          <el-pagination
            style="margin-top: 16px"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageConfig.pageNo"
            :page-size="pageConfig.pageSize"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageConfig.total"
          >
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modelShow = false" size="small">取 消</el-button>
          <el-button type="primary" @click="confirmCheck" size="small">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
const imgUrl = import.meta.env.VITE_APP_BASE_API + '/system/option/multi/upload';
const groupPanes = ref([]);
const imgData = ref([]);
const modelShow = ref(false);
const activeGroup = ref('');

const pageConfig = ref({
  groupId: '',
  pageNo: 1,
  pageSize: 10,
  total: 0
});
const checkedItem: any = ref({});
const formGroup = ref({ name: '' });

//  ---定义emit---
const emit = defineEmits<{
  (e: 'confirmCheck', filePath: string): void;
}>();

// --- 定义方法 ---
const opened = () => {
  modelShow.value = true;
};
const loadData = () => {
  pageConfig.value.groupId = activeGroup.value;
};
const confirmCheck = () => {
  if (!checkedItem.value || !checkedItem.value?.id) {
    ElMessage.error('请选择图片');
  } else {
    emit('confirmCheck', checkedItem.value?.filePath);
    modelShow.value = false;
  }
};

const checkImg = (item) => {
  checkedItem.value = item;
};
/**
 * 询问处理、当前选中项处理、刷新处理
 */
const removeTab = (targetName) => {
  groupPanes.value = groupPanes.value.filter((tab) => tab.id !== targetName);
  activeGroup.value = groupPanes.value[0].id;
};
const tabClick = () => {
  pageConfig.value.pageNo = 1;
  loadData();
};
const handleSizeChange = (val) => {
  pageConfig.value.pageSize = val;
  loadData();
};
const handleCurrentChange = (val) => {
  pageConfig.value.pageNo = val;
  loadData();
};
</script>
