<!-- 映射字段弹窗 -->
<template>
  <div class="mapField-main">
    <el-dialog
      :title="mapFielType == 1 ? '选择映射字段' : '选择映射源'"
      :model-value="mapFieldDialog"
      @update:model-value="$emit('update:visible', $event)"
      width="720px"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">结构树</span></div>
          <div class="content">
            <el-tree
              ref="tree"
              :data="treeList"
              :props="defaultProps"
              highlight-current
              default-expand-all
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </div>
        </div>
        <div class="center">
          <div class="title-div"><span class="normal-sapn">属性组</span></div>
          <div class="content">
            <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
            <template v-else>
              <div
                class="flex-row"
                v-for="(item, index) in attrbutionGroup"
                :class="{ 'flex-active': item.checked }"
                :key="index"
                @click="changeAtt(item)"
              >
                <div class="label">{{ item.typeName }}</div>
                <div class="ico">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="right">
          <div class="title-div"><span class="normal-sapn">字段</span></div>
          <div class="content">
            <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
            <template v-else>
              <div class="flex-row" v-for="(item, index) in fieldList" :key="index">
                <div class="label">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                <div class="ico">
                  <el-checkbox v-model="item.checked" @change="changeField(item)"></el-checkbox>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitField">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { selectRules } from '@/api/modal';
export default {
  data() {
    return {
      treeList: [], //要素树
      defaultProps: {
        children: 'list',
        label: 'typeName'
      },
      attrbutionGroup: [], //属性组
      fieldList: [], //字段组
      checkedYS: '', //字段对应的要素编码
      checkedAtt: '', //字段对应的属性组编码
      fieldCn: '' //图片类型 顺带赋值映射字段中文名
    };
  },
  emits: ['update:visible', 'closeFieldDialog', 'submitField'],
  props: {
    mapFieldDialog: {
      type: Boolean,
      required: true
    },
    checked: {
      type: Object,
      required: true
    },
    mapFielType: {
      type: Number,
      required: true
    }
  },

  watch: {
    mapFieldDialog: {
      handler(val) {
        if (val) {
          this.getTree();
        }
      },
      deep: true
    }
  },

  components: {},

  computed: {
    checkedFiledList() {
      let list = [];
      if (this.checked.checkedFiledList) {
        list = this.checked.checkedFiledList;
      }
      return list;
    },
    checkedFiledSouseList() {
      let list = [];
      if (this.checked.checkedFiledSouseList) {
        list = this.checked.checkedFiledSouseList;
      }
      return list;
    },
    moduleId() {
      let moduleId = parseInt(this.$store.state.modal.moduleId);
      if (this.$route.query.id) {
        moduleId = this.$route.query.id;
      }
      return moduleId;
    }
  },

  mounted() {},

  methods: {
    getTree() {
      const params = {
        moduleId: moduleId.value
      };
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = this.$route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        params.companyId = companyId;
      }
      selectRules(params).then((res) => {
        if (res.code == 200) {
          this.treeList = res.data;
          this.attrbutionGroup = res.data[0].fieldGroupModelList;
          this.checkedYS = res.data[0].wordName;
          this.$nextTick(() => {
            // selectId：绑定的 node-key
            this.$refs.tree.setCurrentKey(res.data[0].id);
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleClose() {
      this.fieldList = [];
      this.attrbutionGroup = [];
      this.$emit('closeFieldDialog');
    },
    submitField() {
      this.$emit('submitField', this.checkedFiledList, this.fieldCn);
      this.fieldList = [];
      this.attrbutionGroup = [];
    },
    handleNodeClick(data) {
      this.attrbutionGroup = data.fieldGroupModelList;
      this.checkedYS = data.wordName;
      this.fieldList = [];
    },
    // 改变选中属性组
    changeAtt(item) {
      this.attrbutionGroup.forEach((v) => {
        this.$set(v, 'checked', false);
      });
      item.checked = true;
      this.checkedAtt = item.typeName;
      this.fieldList = item.fieldModelList;
      this.fieldList.forEach((v) => {
        this.$set(v, 'allWorkName', `#${this.checkedYS}.${this.checkedAtt}.${v.fieldName}#`);
        let flag = false; //用于判断在选中的字段列表有没有
        if (this.mapFielType == 1) {
          //反显映射字段
          this.checkedFiledList.forEach((k) => {
            if (k.id == v.id) {
              flag = true;
            }
          });
        } else if (this.mapFielType == 2) {
          //反显映射数据源
          this.checkedFiledSouseList.forEach((k) => {
            if (k.id == v.id) {
              flag = true;
            }
          });
        }
        if (flag) {
          this.$set(v, 'checked', true);
        } else {
          this.$set(v, 'checked', false);
        }
      });
    },
    // 改变选中某个字段
    changeField(item) {
      if (item.checked) {
        let flag = false;
        this.checkedFiledList.forEach((v) => {
          if (v.id == item.id) {
            flag = true;
          }
        });
        // 库里面没有的时候才加进去
        if (!flag) {
          if (this.mapFielType == 1) {
            //映射字段可以多选
            this.checkedFiledList.push(item);
          } else if (this.mapFielType == 2) {
            //映射源
            if (this.checkedFiledList.length == 0) {
              this.checkedFiledList.push(item);
              this.fieldCn = item.fieldCn;
            } else {
              this.$message.error('映射数据源的时候只能选择一个值!!!');
              item.checked = false;
              return;
            }
          }
        }
      } else {
        this.checkedFiledList.forEach((v) => {
          if (v.id == item.id) {
            this.checkedFiledList.splice(v, 1);
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.mapField-main {
  .dialog-box {
    height: 300px;
    border: 1px solid rgba(219, 231, 238, 1);
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    .left {
      flex: 2;
    }
    .center {
      flex: 2;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .right {
      flex: 3;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .title-div {
      width: 100%;
      height: 37px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      .normal-sapn {
        margin-left: 20px;
      }
    }
    .content {
      height: calc(100% - 37px);
      padding: 0px 8px;
      width: calc(100% - 16px);
      margin-left: 8px;
      overflow: auto;
      :deep(&) {
        .el-tree-node__content {
          height: 32px;
          font-size: 12px;
        }
      }
      .empty-span {
        color: #909399;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      .flex-row {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .label {
          color: rgba(22, 29, 38, 1);
          font-size: 12px;
          padding-left: 12px;
        }
        .ico {
          padding-right: 8px;
        }
      }
      .flex-row:hover {
        background-color: #f5f7fa;
      }
      .flex-active {
        background: #edf4fb;
      }
    }
    /*滚动条样式*/
    .content::-webkit-scrollbar {
      width: 4px;
    }
    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(255, 255, 255, 0.5);
    }
    .content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
