<template>
  <container-card>
    <div class="p-2">
      <el-row :gutter="20">
        <!-- 部门树 -->
        <el-col :lg="4" :xs="24" style="">
          <el-card shadow="hover">
            <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
            <el-tree
              ref="deptTreeRef"
              class="mt-2"
              node-key="id"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' } as any"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              highlight-current
              default-expand-all
              @node-click="handleNodeClick"
            />
          </el-card>
        </el-col>
        <el-col :lg="20" :xs="24">
          <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
              <el-card shadow="hover">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                  <el-form-item label="用户名称" prop="custName">
                    <el-input v-model="queryParams.custName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
                  </el-form-item>
                  <el-form-item label="手机号码" prop="phonenumber">
                    <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
                  </el-form-item>

                  <el-form-item label="状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="用户状态" clearable>
                      <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="创建时间" style="width: 308px">
                    <el-date-picker
                      v-model="dateRange"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
          </transition>

          <el-card shadow="hover">
            <template #header>
              <el-row :gutter="10">
                <el-col :span="1.5">
                  <el-button v-has-permi="['system:user:add']" type="primary" plain icon="Plus" @click="handleAdd()">新增</el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button v-has-permi="['system:user:edit']" type="success" plain :disabled="single" icon="Edit" @click="handleUpdate()">
                    修改
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button v-has-permi="['system:user:remove']" type="danger" plain :disabled="multiple" icon="Delete" @click="handleDelete()">
                    删除
                  </el-button>
                </el-col>

                <right-toolbar v-model:show-search="showSearch" :columns="columns" :search="true" @query-table="getList"></right-toolbar>
              </el-row>
            </template>

            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" :selectable="(row) => !isAdmin(row)" />
              <el-table-column v-if="columns[0].visible" key="userId" label="用户编号" align="center" prop="userId" />
              <el-table-column v-if="columns[1].visible" key="userName" label="用户编号" align="center" prop="userId" :show-overflow-tooltip="true" />
              <el-table-column
                v-if="columns[2].visible"
                key="nickName"
                label="用户名称"
                align="center"
                prop="custName"
                :show-overflow-tooltip="true"
              />
              <el-table-column v-if="columns[3].visible" key="deptNames" label="角色" align="center" prop="roleNames" :show-overflow-tooltip="true" />
              <el-table-column v-if="columns[4].visible" key="deptNames" label="部门" align="center" prop="deptNames" :show-overflow-tooltip="true" />
              <el-table-column v-if="columns[5].visible" key="phonenumber" label="手机号码" align="center" prop="phonenumber" width="120" />
              <el-table-column v-if="columns[6].visible" key="status" label="状态" align="center">
                <template #default="scope">
                  <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
                </template>
              </el-table-column>

              <el-table-column v-if="columns[6].visible" label="创建时间" align="center" prop="createTime" width="160">
                <template #default="scope">
                  <span>{{ scope.row.createTime }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" fixed="right" width="180" class-name="small-padding fixed-width">
                <template #default="scope">
                  <el-tooltip content="修改" placement="top" :disabled="scope.row.userId === 1 || isAdmin(scope.row)">
                    <el-button
                      v-hasPermi="['system:user:edit']"
                      link
                      type="primary"
                      icon="Edit"
                      @click="handleUpdate(scope.row)"
                      :disabled="scope.row.userId === 1 || isAdmin(scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top" :disabled="scope.row.userId === 1 || isAdmin(scope.row)">
                    <el-button
                      v-hasPermi="['system:user:remove']"
                      link
                      type="primary"
                      icon="Delete"
                      @click="handleDelete(scope.row)"
                      :disabled="scope.row.userId === 1 || isAdmin(scope.row)"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip content="重置密码" placement="top" :disabled="scope.row.userId === 1 || isAdmin(scope.row)">
                    <el-button
                      v-hasPermi="['system:user:resetPwd']"
                      link
                      type="primary"
                      icon="Key"
                      @click="handleResetPwd(scope.row)"
                      :disabled="scope.row.userId === 1 || isAdmin(scope.row)"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip content="分配角色" placement="top" :disabled="scope.row.userId === 1 || isAdmin(scope.row)">
                    <el-button
                      v-hasPermi="['system:user:edit']"
                      link
                      type="primary"
                      icon="CircleCheck"
                      @click="handleAuthRole(scope.row)"
                      :disabled="scope.row.userId === 1 || isAdmin(scope.row)"
                    ></el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :total="total"
              @pagination="getList"
            />
          </el-card>
        </el-col>
      </el-row>

      <!-- 添加或修改用户配置对话框 -->
      <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body @close="closeDialog">
        <el-form ref="userFormRef" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户昵称" prop="custName">
                <el-input v-model="form.custName" placeholder="请输入用户昵称" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色" prop="roleNames">
                <el-select v-model="form.roleIds" placeholder="请选择角色" style="width: 100%" multiple v-if="form.roleNames == '超级管理员'">
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.roleId"
                    :label="item.roleName"
                    :value="item.roleId"
                    :disabled="item.status === '1' || item.roleName !== '超级管理员'"
                  ></el-option>
                </el-select>
                <el-select v-model="form.roleIds" placeholder="请选择角色" style="width: 100%" multiple>
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.roleId"
                    :label="item.roleName"
                    :value="item.roleId"
                    :disabled="item.status == '1' || item.roleName == '超级管理员'"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="部门" prop="deptNames">
                <el-tree-select
                  v-model="form.deptId"
                  :data="enabledDeptOptions"
                  value-key="id"
                  placeholder="请选择归属部门"
                  check-strictly
                  @change="handleDeptChange"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
              </el-form-item>
            </el-col> -->
          </el-row>
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
                <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
                <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row>
            <!-- <el-col :span="12">
              <el-form-item label="用户性别">
                <el-select v-model="form.sex" placeholder="请选择">
                  <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="12">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="性别">
                <el-select v-model="form.sex" placeholder="请选择性别" style="width: 100%">
                  <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel()">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 用户导入对话框 -->
      <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
        <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload">
            <i-ep-upload-filled />
          </el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="text-center el-upload__tip">
              <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
          </template>
        </el-upload>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </container-card>
</template>

<script setup name="User" lang="ts">
import api from '@/api/system/user';
import { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import { DeptTreeVO, DeptVO } from '@/api/system/dept/types';
import { RoleVO } from '@/api/system/role/types';
import { PostVO } from '@/api/system/post/types';
import { globalHeaders } from '@/utils/request';
import { to } from 'await-to-js';
import { optionselect } from '@/api/system/post';
import { listRole } from '@/api/system/role';

const router = useRouter();
const { proxy } = getCurrentInstance() as any;
const { sys_normal_disable, sys_user_sex } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_user_sex'));
const userList = ref<UserVO[]>();
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const deptName = ref('');
const deptOptions = ref<DeptTreeVO[]>([]);
const enabledDeptOptions = ref<DeptTreeVO[]>([]);
const initPassword = ref<string>('');
const postOptions = ref<PostVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);
/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData'
});
// 列显隐信息
const columns = ref<any>([
  { key: '0', label: `用户编号`, visible: false, children: [] },
  { key: '1', label: `用户名称`, visible: true, children: [] },
  { key: '2', label: `用户昵称`, visible: true, children: [] },
  { key: '3', label: `部门`, visible: true, children: [] },
  { key: '4', label: `手机号码`, visible: true, children: [] },
  { key: '5', label: `状态`, visible: true, children: [] },
  { key: '6', label: `创建时间`, visible: true, children: [] }
]);

const deptTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();
const formDialogRef = ref<ElDialogInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  userName: '',
  nickName: undefined,
  password: '',
  phonenumber: undefined,
  email: undefined,
  sex: undefined,
  status: '0',
  remark: '',
  postIds: [],
  roleIds: [],
  roleNames: undefined,
  deptNames: undefined
};

const initData: PageData<UserForm, UserQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    phonenumber: '',
    status: '',
    deptId: '',
    roleId: ''
  },
  rules: {
    userName: [
      { required: true, message: '用户名称不能为空', trigger: 'blur' },
      {
        min: 2,
        max: 20,
        message: '用户名称长度必须介于 2 和 20 之间',
        trigger: 'blur'
      }
    ],
    custName: [
      { required: true, message: '用户昵称不能为空', trigger: 'blur' },
      {
        min: 2,
        max: 30,
        message: '用户昵称长度必须介于 2 和 30 之间',
        trigger: 'blur'
      }
    ],
    nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
    password: [
      { required: true, message: '用户密码不能为空', trigger: 'blur' },
      {
        min: 5,
        max: 20,
        message: '用户密码长度必须介于 5 和 20 之间',
        trigger: 'blur'
      },
      { pattern: /^[^<>"'|\\]+$/, message: '不能包含非法字符：< > " \' \\ |', trigger: 'blur' }
    ],
    email: [
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change']
      }
    ],
    phonenumber: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ],
    roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }]
  }
};
const data = reactive<PageData<UserForm, UserQuery>>(initData);

const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data);

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 根据名称筛选部门树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 查询用户列表 */
const getList = async () => {
  loading.value = true;
  const res = await api.listUser(proxy?.addDateRange(queryParams.value, dateRange.value));
  loading.value = false;
  userList.value = res.rows;
  total.value = res.total;
};

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  const params = {
    status: 0
  };
  const res = await api.deptTreeSelect(params);
  deptOptions.value = res.data;
  enabledDeptOptions.value = filterDisabledDept(res.data);
};

/** 过滤禁用的部门
 * @param deptList 部门列表
 */
const filterDisabledDept = (deptList: DeptTreeVO[]) => {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
};

/** 节点单击事件
 * @param data 部门数据
 */
const handleNodeClick = (data: DeptVO) => {
  queryParams.value.deptId = data.id;
  handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 删除按钮操作
 * @param row 用户数据
 */
const handleDelete = async (row?: UserVO) => {
  const userIds = row?.userId || ids.value;
  const [err] = await to(proxy?.$modal.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？') as any);
  if (!err) {
    await api.delUser(userIds);
    await getList();
    proxy?.$modal.msgSuccess('删除成功');
  }
};

/** 用户状态修改
 * @param row 用户数据
 */
const handleStatusChange = async (row: UserVO) => {
  const text = row.status === '0' ? '启用' : '停用';
  try {
    await proxy?.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗?');
    await api.changeUserStatus(row.userId, row.status);
    proxy?.$modal.msgSuccess(text + '成功');
  } catch (err) {
    row.status = row.status === '0' ? '1' : '0';
  }
};
/** 跳转角色分配
 * @param row 用户数据
 */
const handleAuthRole = (row: UserVO) => {
  const userId = row.userId;
  router.push('/system/user-auth/role/' + userId);
};

/** 重置密码按钮操作
 * @param row 用户数据
 */
const handleResetPwd = async (row: UserVO) => {
  const [err, res] = await to(
    ElMessageBox.prompt('请输入"' + row.userName + '"的新密码', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      inputPattern: /^.{5,20}$/,
      inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
      inputValidator: (value: string) => {
        if (/<|>|"|'|\||\\/.test(value)) {
          return false;
        }
        return true;
      }
    })
  );
  if (!err && res) {
    await api.resetUserPwd(row.userId, res.value);
    proxy?.$modal.msgSuccess('修改成功，新密码是：' + res.value);
  }
};

/** 选择条数
 * @param selection 用户数据
 */
const handleSelectionChange = (selection: UserVO[]) => {
  // 过滤掉超级管理员角色的用户
  const filteredSelection = selection.filter((item) => !isAdmin(item));
  ids.value = filteredSelection.map((item) => item.userId);
  single.value = filteredSelection.length != 1;
  multiple.value = !filteredSelection.length;
};

/**
 * 判断用户是否为超级管理员
 * @param user 用户对象
 */
function isAdmin(user: UserVO): boolean {
  if (!user || !user.roleNames) {
    return false;
  }
  return user.roleNames.includes('超级管理员');
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '用户导入';
  upload.open = true;
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/user/export',
    {
      ...queryParams.value
    },
    `user_${new Date().getTime()}.xlsx`
  );
};
/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData };
  userFormRef.value?.resetFields();
};
/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  reset();
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  try {
    const { data } = await api.getUser();
    dialog.visible = true;
    dialog.title = '新增用户';
    if (data && data.posts) {
      postOptions.value = data.posts;
    }
    // 如果 roles 存在且非空，直接赋值
    if (data && Array.isArray(data.roles) && data.roles.length > 0) {
      roleOptions.value = data.roles;
    } else {
      // 如果 roles 为空，尝试从其他接口获取角色列表
      try {
        const res = await listRole({
          pageNum: 1,
          pageSize: 999,
          roleName: '',
          roleKey: '',
          status: ''
        });
        if (res && res.rows) {
          roleOptions.value = res.rows;
        }
      } catch (error) {}
    }
    form.value.password = initPassword.value ? initPassword.value.toString() : '';
  } catch (error) {}
};

const handleUpdate = async (row?: UserVO) => {
  reset();
  const userId = row?.userId || ids.value[0];
  queryParams.value.userIds = String(userId);

  // 获取用户详细信息
  const { data } = await api.getUser(userId);

  // 获取角色列表
  try {
    const res = await listRole({
      pageNum: 1,
      pageSize: 999,
      roleName: '',
      roleKey: '',
      status: ''
    });
    if (res && res.rows) {
      roleOptions.value = res.rows;
    }
  } catch (error) {}

  if (data) {
    // 确保数据类型匹配
    form.value = {
      ...initFormData,
      ...data,
      status: data.status || '0',
      password: ''
    };

    if (data.posts) {
      postOptions.value = data.posts;
    }

    dialog.visible = true;
    dialog.title = '修改用户';
  }

  // 获取用户的角色IDs，不使用分页限制
  try {
    const userRes = await api.listUser({
      pageNum: 1,
      pageSize: 1000, // 使用更大的pageSize以确保能获取到所有数据
      userIds: String(data.userId)
    });

    if (userRes.rows?.length > 0) {
      const currentUser = userRes.rows.find((item) => item.userId === data.userId);
      if (currentUser) {
        form.value.roleIds = currentUser.roleIds;
        form.value.deptId = currentUser.deptIds[0];
      }
    }
  } catch (error) {
    console.error('获取用户角色数据失败:', error);
  }
};

/** 提交按钮 */
const submitForm = () => {
  userFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const loading = ref(false);
      try {
        // 将roleIds格式化为数组
        const formattedRoleIds = Array.isArray(form.value.roleIds) ? [...form.value.roleIds] : [form.value.roleIds];

        // 确保deptId不为null
        if (!form.value.deptId) {
          proxy?.$modal.msgError('请选择部门');
          return;
        }

        // 构建参数
        const params: any = {
          userName: form.value.phonenumber,
          nickName: form.value.nickName,
          custName: form.value.custName,
          phonenumber: form.value.phonenumber,
          roleIds: formattedRoleIds,
          postIds: form.value.postIds,
          deptIds: [form.value.deptId], // 确保deptId被正确传递
          remark: form.value.remark,
          sex: form.value.sex,
          type: 1,
          password: form.value.password || '',
          status: form.value.status
        };

        // 修改用户时需要添加userId
        if (form.value.userId) {
          Object.assign(params, { userId: form.value.userId });
          loading.value = true;
          const res = await api.updateUser(params);
          loading.value = false;
          if (res.code == 200) {
            proxy?.$modal.msgSuccess('修改成功');
            queryParams.value.userIds = undefined;
            getList();
          } else {
            proxy?.$message.error(res.msg);
          }
        } else {
          // 新增用户
          loading.value = true;
          const res = await api.addUser(params);
          loading.value = false;
          if (res.code == 200) {
            proxy?.$modal.msgSuccess(res.msg);
            getList();
          } else {
            ElMessage.error(res.msg);
          }
        }

        dialog.visible = false;
      } catch (error) {
        loading.value = false;
      }
    }
  });
};

/**
 * 关闭用户弹窗
 */
const closeDialog = () => {
  dialog.visible = false;
  resetForm();
};

/**
 * 重置表单
 */
const resetForm = () => {
  userFormRef.value?.resetFields();
  userFormRef.value?.clearValidate();

  form.value.id = undefined;
  form.value.status = '1';
};
onMounted(() => {
  getDeptTree(); // 初始化部门数据
  getList(); // 初始化列表数据
  proxy?.getConfigKey('sys.user.initPassword').then((response: any) => {
    initPassword.value = response.data;
  });
});

async function handleDeptChange(value: number | string) {
  const response = await optionselect(value);
  postOptions.value = response.data;
  form.value.postIds = [];
}
</script>
