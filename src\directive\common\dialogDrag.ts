import type { Directive, DirectiveBinding } from 'vue';

/**
 * v-dialogDrag 弹窗拖拽
 * 适用于 Element Plus 的 el-dialog 组件
 */
const dialogDrag: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 由于Element Plus v2+的el-dialog组件使用了teleport，
    // 所以实际的DOM结构会被移到body下，需要等待teleport完成
    setTimeout(() => {
      // 查找实际渲染的对话框元素
      const dialogs = document.querySelectorAll('.el-dialog');
      if (!dialogs.length) return;

      // 找到最近创建的dialog（通常是当前打开的）
      const dragDom = dialogs[dialogs.length - 1] as HTMLElement;
      const dialogHeaderEl = dragDom.querySelector('.el-dialog__header') as HTMLElement;

      if (!dialogHeaderEl || !dragDom) return;

      dialogHeaderEl.style.cursor = 'move';
      dialogHeaderEl.style.userSelect = 'none';

      // 获取原有属性
      const getStyle = (dom: HTMLElement, attr: string) => {
        return getComputedStyle(dom).getPropertyValue(attr);
      };

      dialogHeaderEl.onmousedown = (e) => {
        // 鼠标按下，计算当前元素距离可视区的距离
        const disX = e.clientX - dialogHeaderEl.offsetLeft;
        const disY = e.clientY - dialogHeaderEl.offsetTop;

        const screenWidth = document.body.clientWidth; // body当前宽度
        const screenHeight = document.documentElement.clientHeight; // 可见区域高度

        const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
        const dragDomHeight = dragDom.offsetHeight; // 对话框高度

        const minDragDomLeft = dragDom.offsetLeft;
        const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;

        const minDragDomTop = dragDom.offsetTop;
        const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight;

        // 获取到的值带px 正则匹配替换
        let styL: number = 0;
        let styT: number = 0;

        // 注意在谷歌浏览器中 translate属性会覆盖left和top属性
        if (getStyle(dragDom, 'transform') !== 'none') {
          const transform = getStyle(dragDom, 'transform').match(/\d+/g) || ['0', '0'];
          styL = parseInt(transform[0]);
          styT = parseInt(transform[1]);
        } else {
          // 如果没有transform，尝试获取left和top
          if (getStyle(dragDom, 'left') !== 'auto') {
            styL = parseInt(getStyle(dragDom, 'left'));
          }
          if (getStyle(dragDom, 'top') !== 'auto') {
            styT = parseInt(getStyle(dragDom, 'top'));
          }
        }

        document.onmousemove = function (e) {
          // 通过事件委托，计算移动的距离
          let left = e.clientX - disX;
          let top = e.clientY - disY;

          // 边界处理
          if (-left > minDragDomLeft) {
            left = -minDragDomLeft;
          } else if (left > maxDragDomLeft) {
            left = maxDragDomLeft;
          }

          if (-top > minDragDomTop) {
            top = -minDragDomTop;
          } else if (top > maxDragDomTop) {
            top = maxDragDomTop;
          }

          // 移动当前元素
          dragDom.style.transform = `translate(${left + styL}px, ${top + styT}px)`;
        };

        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };

        // 阻止默认事件
        e.preventDefault();
        return false;
      };
    }, 100); // 等待teleport完成
  }
};

export default dialogDrag;
