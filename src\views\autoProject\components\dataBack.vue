<!-- 数据回退 -->
<template>
  <div>
    <el-dialog title="字段找回" v-model="dialogVisible" width="30%" :close-on-click-modal="false" @opened="initDataBack" :before-close="handleClose">
      <div class="notice">
        <!-- <el-tooltip class="item" effect="dark" content="数据回退功能可让用户针对某一个字段（字段为空）进行批量找回上一个版本有效数据(只允许找回普通属性组的非表达式字段)" placement="top-start">
                    <i class="el-icon-question"></i>
                </el-tooltip> -->
        <span>注意事项：字段找回功能可让用户针对某一个字段（字段为空）进行批量找回上一个版本有效数据(只允许找回普通属性组的非表达式字段)！！！</span>
      </div>
      <el-form :model="form" ref="formRef" :rules="formRule" label-width="100px" label-position="top">
        <el-form-item label="数据选择" prop="zdList">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.zdListNames" @click="chooseData" readonly> </el-input>
        </el-form-item>
        <el-form-item label="属性组" prop="linkId">
          <el-select v-model="form.linkId" placeholder="请选择" style="width: 100%" clearable @change="chooseAttr">
            <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段" prop="fieldName">
          <el-select v-model="form.fieldName" placeholder="请选择" style="width: 100%" clearable>
            <el-option v-for="item in filedList" :key="item.fieldName" :label="item.fieldCn" :disabled="item.disabled" :value="item.fieldName">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <search-data
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="form.zdList"
      :isManager="false"
      :ifTree="false"
      :isKJ="false"
      :ruleTree="ruleTree"
      :ruleIds="[]"
    ></search-data>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits, watchEffect } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import SearchData from './searchData/index.vue';
import { retrieval } from '@/api/project';

interface FormData {
  zdList: any[];
  linkId: string;
  fieldName: string;
  zdListNames: string;
}

interface AttrItem {
  linkId: string;
  typeName?: string;
  label?: string;
  linkType?: number;
  disabled?: boolean;
  fieldModelList?: FieldItem[];
  [key: string]: any;
}

interface FieldItem {
  fieldName: string;
  fieldCn: string;
  valueMethod?: string;
  attribution?: {
    expression?: any;
    [key: string]: any;
  };
  disabled?: boolean;
  [key: string]: any;
}

interface RuleTreeItem {
  typeName: string;
  list: RuleTreeItem[];
  fieldGroupModelList: AttrItem[];
  [key: string]: any;
}

const props = defineProps<{
  dataBackDialog: boolean;
  ruleTree: RuleTreeItem[];
}>();

const emit = defineEmits(['handleCloseDataBack']);

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const searchDialog = ref(false);
const ifTree = ref(false);
const attrList = ref<AttrItem[]>([]);
const filedList = ref<FieldItem[]>([]);
const managerDialog = ref(false);

const form = reactive<FormData>({
  zdList: [], // 需要回退的数据
  linkId: '', // 属性组linkid
  fieldName: '', // 字段名
  zdListNames: ''
});

const formRule = {
  zdList: [{ required: true, message: '请选择数据', trigger: 'change' }],
  linkId: [{ required: true, message: '请选择属性组', trigger: 'change' }],
  fieldName: [{ required: true, message: '请选择字段', trigger: 'change' }]
};

// 监听dataBackDialog变化
watchEffect(() => {
  dialogVisible.value = props.dataBackDialog;
});

// 初始化数据
const initDataBack = () => {
  attrList.value = [];
  disposeAttr(JSON.parse(JSON.stringify(props.ruleTree)));
};

// 组装所有属性组
const disposeAttr = (list: RuleTreeItem[]) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      // 使用Vue3的方式设置属性
      k.label = `${k.typeName}(${v.typeName})`;
      if (k.linkType != 1) {
        // 只有普通属性组才可以选择
        k.disabled = true;
      }
      attrList.value.push(k);
    });
    if (v.list.length != 0) {
      disposeAttr(v.list);
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  emit('handleCloseDataBack');
};

// 选择数据
const chooseData = () => {
  ifTree.value = false;
  searchDialog.value = true;
};

// 关闭筛选数据弹窗
const closeSearchDialog = () => {
  // 需要重新请求下列表 因为可能管理删除了某些数据
  if (managerDialog.value) {
    // 管理才进行重新查询
    // 注意：在Vue3中，不推荐使用$parent，这里需要用其他方式处理
    // 可能需要通过emit事件告知父组件刷新数据
    emit('refreshData', true);
  }
  form.zdList = [];
  form.zdListNames = '';
  searchDialog.value = false;
};

// 得到筛选要素数据
const getChooseData = (list: any[]) => {
  form.zdList = list;
  const names: string[] = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  form.zdListNames = names.join(',');
  searchDialog.value = false;
};

// 选择属性组
const chooseAttr = (e: string) => {
  for (let i = 0; i < attrList.value.length; i++) {
    if (attrList.value[i].linkId == e) {
      filedList.value = attrList.value[i].fieldModelList || [];
      break;
    }
  }
  if (filedList.value.length != 0) {
    filedList.value.forEach((v) => {
      if (['idCardScan', 'xttable'].includes(v.valueMethod || '') || (v.attribution && v.attribution.expression)) {
        // 只有普通属性组才可以选择
        v.disabled = true;
      }
    });
  }
};

// 提交数据
const submit = () => {
  ElMessageBox.confirm('确定要执行字段找回操作吗？该操作会改变界址线的数据，请谨慎操作！！！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formRef.value?.validate((valid) => {
        if (valid) {
          const parmas = {
            ids: [] as any[],
            linkId: form.linkId,
            fieldName: form.fieldName
          };
          const ids: any[] = [];
          form.zdList.forEach((v) => {
            ids.push(v.id);
          });
          parmas.ids = ids;
          retrieval(parmas).then((res) => {
            if (res.code == 200) {
              ElMessageBox.alert('回退成功', '提示', {
                confirmButtonText: '确定',
                callback: (action) => {
                  // 在这添加是否切换公司的标识。
                  // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                  // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                  sessionStorage.setItem('qiehuan_company', 'false');
                  location.reload();
                }
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    })
    .catch(() => {
      // 用户取消操作
    });
};
</script>

<style lang="scss" scoped>
.notice {
  color: red;
}
</style>
