<!-- 邀请列表 -->
<template>
  <container-card>
    <!-- <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddClient"
          >新增</el-button> -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="受邀人">
        <el-input v-model="queryParams.custName" placeholder="请输入受邀人" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码">
        <el-input v-model="queryParams.custPhone" placeholder="请输入手机号" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="邀请时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" s @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="danger" plain icon="CircleClose" size="small" :disabled="multiple" @click="handleCancleInvate">取消邀请</el-button>
      </el-col>

      <right-toolbar v-model:show-search="showSearch" @query-table="getClientTableList" />
    </el-row>
    <el-table v-loading="loading" :data="clientList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司名称" align="center" prop="companyName" />

      <el-table-column label="受邀人" align="center" prop="custName" />
      <el-table-column label="手机号码" align="center" prop="custPhone" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="邀请时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button size="small" type="text" icon="CircleClose" @click="handleAddClient(row)">取消邀请</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getClientTableList"
    />

    <!-- 添加客户 -->
    <el-dialog v-model="clientDialogFormVisible" title="邀请客户" :close-on-click-modal="false">
      <el-form :model="addClientForm">
        <el-form-item label="客户姓名" label-width="120px">
          <el-input v-model="addClientForm.custName" autocomplete="off" />
        </el-form-item>
        <el-form-item label="客户手机号" label-width="120px">
          <el-input v-model="addClientForm.custPhone" autocomplete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clientDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitClientForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { getClientList, addClient, updateClient } from '@/api/client';
import { parseTime } from '@/utils/ruoyi';
import { formatDateType } from '@/utils/filters';

interface ClientItem {
  id: number | string;
  companyId: number | string;
  custName: string;
  custPhone: string;
  remark?: string;
  createTime: string | number;
  companyName?: string;
  [key: string]: any;
}

interface QueryParams {
  custPhone: string;
  custName: string;
  dateRange: string[];
  pageSize: number;
  pageNum: number;
}

interface ClientForm {
  custName: string;
  custPhone: string;
  direction?: number;
  status?: number;
  id?: number | string;
  companyId?: number | string;
}

// 查询表单ref
const queryFormRef = ref<FormInstance>();

// 遮罩层
const loading = ref(true);
// 选中数组
const ids = ref<Array<string | number>>([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// table 的数组
const clientList = ref<ClientItem[]>([]);
// 邀请客户
const clientDialogFormVisible = ref(false);
// 邀请客户的表单
const addClientForm = reactive<ClientForm>({
  custName: '',
  custPhone: ''
});
// 显示搜索条件
const showSearch = ref(true);
// 当前编辑项的id
const currentId = ref<number | string>(0);
// 查询参数
const queryParams = reactive<QueryParams>({
  custPhone: '',
  custName: '',
  dateRange: [],
  pageSize: 10,
  pageNum: 1
});

// 总条数
const total = computed(() => clientList.value.length);

// 格式化时间
const formatTime = (time: string | number) => {
  return formatDateType(time);
};

// 获取邀请列表
const getClientTableList = () => {
  const params = {
    direction: 2,
    status: 0,
    type: 1,
    custName: queryParams.custName,
    custPhone: queryParams.custPhone
  };

  loading.value = true;
  getClientList(params)
    .then((res) => {
      if (res.code === 200) {
        clientList.value = res.data;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

// 取消邀请客户
const handleAddClient = (row: ClientItem) => {
  ElMessageBox.confirm(`是否确定放弃邀请【${row.custName}】？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = {
        status: -1, // 取消-1 同意是1 拒绝2
        id: row.id,
        companyId: row.companyId,
        custPhone: row.custPhone
      };
      updateClient(params).then((res) => {
        if (res.code === 200) {
          ElMessage.success(res.msg);
          getClientTableList();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消操作'
      });
    });
};

// 邀请客户的提交按钮
const handleSubmitClientForm = () => {
  const params = {
    custName: addClientForm.custName,
    custPhone: addClientForm.custPhone,
    direction: 2,
    status: 0
  };

  if (currentId.value) {
    // 修改--邀请客户
    updateClient(params).then((res) => {
      if (res.code === 200) {
        clientList.value = res.data;
        clientDialogFormVisible.value = false;
        ElMessage.success('修改成功');
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    // 新建-邀请客户
    addClient(params).then((res) => {
      if (res.code === 200) {
        clientList.value = res.data;
        clientDialogFormVisible.value = false;
        ElMessage.success('添加成功');
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 多选框选中数据
const handleSelectionChange = (selection: ClientItem[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 批量取消邀请
const handleCancleInvate = () => {
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getClientTableList();
};

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.custName = '';
  queryParams.custPhone = '';
  queryParams.dateRange = [];
  handleQuery();
};

onMounted(() => {
  getClientTableList();
});
</script>

<style lang="scss" scoped></style>
