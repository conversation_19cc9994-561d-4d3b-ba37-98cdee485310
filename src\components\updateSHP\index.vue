<!-- 更新shp -->
<template>
  <div class="updateSHP-main" v-loading.fullscreen.lock="fullscreenLoading">
    <!-- 更新项目dialog -->
    <el-dialog
      title="更新项目"
      v-model="uploadProjectDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1200px"
      :before-close="handleClose"
      @opened="getData"
    >
      <div class="dialog-hint">
        <div class="title">温馨提示：</div>
        <div class="content">(1)更新根节点数据时，SHP属性中需要有个唯一ID字段，来当做数据的唯一标识</div>
        <div class="content">(2)更新子节点数据时，SHP属性中除本身唯一ID外，还需要标识父级节点的唯一ID，用于寻找父节点</div>
        <div class="content">(3)更新图形的时候如果该图形有子要素，会同时生成一份默认的子要素数据，会覆盖之前的子要素内容！！！</div>
      </div>
      <div class="operation-area">
        <div class="top-area">
          <div class="operation-item">
            <div>
              <div class="dialog-row">1、操作方式</div>
              <div class="dialog-row">
                <el-radio-group v-model="operaType">
                  <el-radio :label="1">新增</el-radio>
                  <el-radio :label="2">修改</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <div class="dialog-row">2、选择编码方式</div>
              <div class="dialog-row">
                <el-radio-group v-model="bmType">
                  <el-radio label="utf-8">utf-8</el-radio>
                  <el-radio label="gbk">gbk</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <div class="dialog-row">4、选择文件 <span style="color: red">(请选择.shp,.dbf,.prj,.shx文件)</span></div>
              <form enctype="multipart/form-data" method="post" id="uploadForm" ref="uploadForm">
                <div class="file-div">
                  <label class="file-upload">
                    <input ref="file" type="file" multiple accept=".shp,.dbf,.prj,.shx" @change="updateFileList" class="file-input" />
                    <span class="upload-button">
                      <i class="el-icon-upload"></i>
                      <span>选择文件</span>
                    </span>
                    <ul id="fileList" class="file-list"></ul>
                  </label>
                </div>
              </form>
            </div>
            <div>
              <div class="dialog-row">5、读取文件</div>
              <div class="dialog-row">
                <el-button type="primary" @click="submitUploadProject">读取</el-button>
                <span style="margin-left: 20px; color: #67c23a" v-show="submitList.length != 0"
                  >总共读取数量【{{ submitList.length }}】条，请点击提交进行上传！！！</span
                >
              </div>
            </div>
          </div>
        </div>

        <div class="content-area">
          <div class="left-area">
            <div class="dialog-row">3、选择更新节点</div>
            <div class="tree-container">
              <el-tree
                :data="treeOptions"
                :props="{
                  label: 'typeName',
                  children: 'list'
                }"
                @node-click="handleTreeNodeClick"
                node-key="id"
                highlight-current
                default-expand-all
                :expand-on-click-node="false"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <span>{{ node.label }}</span>
                    <span v-if="updateType.includes(data.id)">✓</span>
                  </span>
                </template>
              </el-tree>
            </div>
          </div>

          <div class="right-area">
            <div class="dialog-row">读取的数据</div>
            <div class="data-list-container">
              <div v-if="shpList.length === 0" class="no-data">暂无数据</div>
              <div v-else class="data-list">
                <div v-for="(item, index) in shpList" :key="index" class="data-item">
                  <span>{{ item.properties[selectFrom.currentParcelName] || `数据${index + 1}` }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bottom-area">
          <div class="operation-item"></div>
          <div class="operation-item"></div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitUpdateShp">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="映射字段（表达式字段不允许映射改变）"
      v-model="dialogVisible"
      width="700px"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleDialogVisible"
    >
      <el-tooltip
        class="item"
        effect="dark"
        content="会展示第一个图形的属性数据，如果发现乱码就需要返回选择另一个编码方式重新读取！！！"
        placement="top-start"
      >
        <el-link type="primary" @click="verifyCoding">校验编码格式</el-link>
      </el-tooltip>
      <el-form :model="selectFrom" :rules="selectFromRules" ref="ruleForm" label-position="left" label-width="180px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="`${currentTreeItem.typeName}名称`" prop="currentParcelName">
              <el-select
                v-model="selectFrom.currentParcelName"
                style="width: 100%"
                filterable
                clearable
                placeholder="请选择当前节点名称"
                @change="handleParcelName"
              >
                <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- 修改才需要 -->
          <el-col :span="12">
            <el-form-item label="shp字段" prop="currentNodeId" v-if="operaType == 2">
              <el-select
                v-model="selectFrom.currentNodeId"
                style="width: 100%"
                filterable
                clearable
                placeholder="请选择当前节点唯一ID"
                @change="(element) => handleParcelName(element, 1)"
              >
                <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item :label="`${currentTreeItem.typeName}节点字段`" label-width="100px" v-if="operaType == 2 && !isID">
              <el-cascader
                style="width: 100%"
                v-model="selectFrom.souseKey1"
                :props="attrProps"
                :options="attrList"
                @change="handleChange"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="isParentID ? 11 : 12" :offset="isParentID ? 1 : 0">
            <el-form-item label="shp字段" prop="currentParentId" v-if="currentTreeItem.levelNum != 1">
              <el-select
                v-model="selectFrom.currentParentId"
                style="width: 100%"
                filterable
                clearable
                placeholder="请选择上级节点唯一ID"
                @change="(element) => handleParcelName(element, 2)"
              >
                <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item :label="`${parentYS.typeName}节点字段`" v-if="currentTreeItem.levelNum != 1 && !isParentID">
              <el-cascader v-model="selectFrom.souseKey2" :props="attrProps" :options="parentAttr" @change="handleChange"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="属性组字段映射" style="width: 100%">
            <el-col :span="24">
              <el-select
                v-model="selectFrom.currentGroup"
                style="width: 100%"
                filterable
                clearable
                placeholder="属性组字段映射"
                value-key="id"
                @change="handleFieldCurrentGroup"
              >
                <el-option v-for="item in fieldGroupModelList" :disabled="item.disable" :key="item.id" :label="getGroupLabel(item)" :value="item">
                </el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <el-table :height="tableHeight" :data="localfields" style="width: 100%; overflow: auto" :row-style="{ height: '49px' }" height="300" border>
        <el-table-column label="本系统属性组字段别名">
          <template #default="scope">
            <span v-if="scope.row.attribution.expression">【表达式字段】</span>
            <span :style="getLableColor(scope.row)">{{ scope.row.fieldCn }}</span>
            <span v-if="scope.row.fieldType == 'String'">(文本)</span>
            <span v-if="scope.row.fieldType == 'Long'">(整数)</span>
            <span v-if="scope.row.fieldType == 'Date'">(日期)</span>
            <span v-if="scope.row.fieldType == 'Double'">(小数)</span>
          </template>
        </el-table-column>
        <el-table-column label="本系统属性组字段名称">
          <template #default="scope">
            <span :style="getLableColor(scope.row)">{{ scope.row.fieldName }}</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template v-slot:header>
            <span>SHP字段</span>
            <el-link type="primary" @click="changeYS" style="margin-left: 10px">清除所有映射</el-link>
          </template>
          <template v-slot="scope">
            <el-select
              :disabled="scope.row.attribution.expression"
              v-model="scope.row.yz"
              placeholder="请选择"
              clearable
              filterable
              @change="changeShpField(scope.row)"
            >
              <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogVisible">取 消</el-button>
          <el-button type="primary" @click="submitFields()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 处理坐标系转换 -->
    <el-dialog
      title="坐标系转换进度"
      v-model="wkidTransitionDilaog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="transitionProgress"></el-progress>
        <div style="margin-top: 10px">{{ transitionMsg }}</div>
      </div>
    </el-dialog>
    <!-- 数据上传进度 -->

    <!-- 坐标系选择 -->
    <el-dialog
      title="提示"
      v-model="chooseWkidDialog"
      width="30%"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="handleCloseChooseWkid"
    >
      <div style="margin-bottom: 10px; color: #ff4343">您上传的shp坐标系暂未兼容，请您主动选择一个正确的坐标系，否则会导致导入之后位置不对！！！</div>
      <el-select v-model="shpWkid" placeholder="请选择对应的wkid" filterable style="width: 100%">
        <el-option v-for="item in wkidMap" :key="item.wkid" :label="`${item.srsCode}(${item.wkid})`" :value="item.wkid"> </el-option>
      </el-select>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseChooseWkid">取 消</el-button>
          <el-button type="primary" @click="sumitChooseWkid">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 验证编码格式弹窗 -->
    <el-dialog title="校验编码格式" v-model="verifyDialog" :modal-append-to-body="false" :append-to-body="true" width="30%">
      <div style="color: #ff4343; margin-bottom: 10px; font-weight: bold">注意：字段内容如出现乱码，请返回重新选择编码方式！！！</div>
      <div class="verify-content" v-if="shpFields.length != 0">
        <div class="flex-row" v-for="(item, index) in shpFields" :key="index">
          {{ item }}：{{ shpList.length == 0 ? '' : shpList[0].properties[item] }}
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="verifyDialog = false">取 消</el-button>
          <el-button type="primary" @click="verifyDialog = false">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 各种进度提示弹窗 -->
    <el-dialog
      title="数据整理进度"
      v-model="publicPlanDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      width="300px"
      align-center
      @closed="handlePublickClose"
    >
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center">
        <el-progress type="circle" :stroke-width="16" :percentage="publicPrecentage" :key="publicPrecentage"> </el-progress>
        <div style="margin-top: 10px">
          {{ publicMsg }}
        </div>
      </div>
    </el-dialog>
    <!-- 最终结果公示dialog -->
    <el-dialog
      title="更新结果"
      :append-to-body="true"
      :modal-append-to-body="false"
      v-model="endMsgDialog"
      :close-on-click-modal="false"
      @closed="endClose"
      width="650px"
    >
      <div class="error-content">
        <div class="flex-row">
          成功 <span style="color: #1dc807">{{ uploadSuccessCount }}</span> 条数据
        </div>
        <div class="flex-row">
          失败 <span style="color: #ff4343">{{ uploadShpError.length }}</span> 条数据
        </div>
        <div class="error-div" v-show="uploadShpError.length != 0">
          <div class="item" v-for="(item, index) in uploadShpError" :key="index">
            {{ item }}
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="endClose">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 上传前验证弹窗 -->
    <el-dialog
      title="上传前校验"
      :append-to-body="true"
      :modal-append-to-body="false"
      v-model="verificationDialog"
      :close-on-click-modal="false"
      @closed="closeVerification"
      width="650px"
    >
      <div class="dialog-row">校验进度：</div>
      <el-progress :text-inside="true" :stroke-width="26" :percentage="verificationPlan"></el-progress>
      <!--  -->
      <template v-if="verificationPlan >= 100">
        <div class="dialog-row" style="margin-top: 10px">总数据量：{{ shpList.length }} 条</div>
        <div class="dialog-row">校验成功：{{ verSuccNum }} 条</div>
        <div class="dialog-row">校验失败：{{ verErrorNum }} 条</div>
        <div class="dialog-row" v-show="uploadShpError.length != 0"><el-link type="primary" @click="downLoadErrorLog">下载错误日志</el-link></div>
        <div class="error-div" v-show="uploadShpError.length != 0">
          <div class="item" v-for="(item, index) in uploadShpError" :key="index">
            {{ item }}
          </div>
        </div>
      </template>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="closeVerification">取 消</el-button>
          <el-button type="primary" @click="nextSubmit" v-show="verificationPlan >= 100">继续上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { loadModules } from 'esri-loader';
import { getWkidForSrsCode } from '@/utils/validate';
import { operaParcelFromShp, operaParcelFromShpCheck } from '@/api/project';
import { selectRules, formulaOrder as formulaOrderApi, updateParcel } from '@/api/modal';
import { read as shapeRead } from 'shapefile';
import proj4 from 'proj4';
import { geojsonToWKT } from '@terraformer/wkt';
import { parseExpression } from '@/utils/expressionParser';
import wkidMapData from '@/data/wkidMap.json';
import { useProjectStore } from '@/store/modules/project';

const projectStore = useProjectStore();

const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

// ---------变量部分
const shpList = ref([]);
const fullscreenLoading = ref(false);
const dialogVisible = ref(false);
const checkedRow = ref([]);
const localfields = ref([]);
const shpFields = ref([]);
const bmType = ref('utf-8');
const treeOptions = ref([]);
const updateType = ref([]);
// 选择字段类型的节点
const cascaderProps: any = reactive({
  value: 'id',
  label: 'typeName',
  expandTrigger: 'click',
  children: 'list',
  checkStrictly: true
});
const updateRuleId = ref(0); // 当前要更新的节点id
const fieldGroupModelList = ref([]); // 属性组列表
const fieldInstanceModels = ref([]); // 属性组映射字段
const currentTreeItem: any = ref({}); // 当前选中的树节点中的某一项
// 表单字段
const selectFrom: any = reactive({
  currentGroup: undefined, // 当前选中的属性组
  currentParcelName: undefined, // 当前选择的映射宗地名称
  currentParentId: undefined, // 映射父级id
  currentNodeId: undefined, // 映射当前节点id
  souseKey1: [],
  souseKey2: []
});
// 表单校验
const selectFromRules = reactive({
  currentNodeId: [{ required: true, message: '请选择节点唯一ID', trigger: 'change' }], // 映射当前节点id
  currentGroup: [{ required: true, message: '请选择属性组字段映射', trigger: 'change' }],
  // 当前选择的映射宗地名称
  currentParcelName: [
    // {  required: true, message: '请选择当前节点', trigger: 'change' }
  ],
  // 映射父级id
  currentParentId: [{ required: true, message: '请选择上级节点唯一ID', trigger: 'change' }]
});

const submitList = ref([]); // 最终提交的数据内容
const attrList = ref([]); // 选中节点的属性组
const attrProps = reactive({
  value: 'value',
  label: 'label',
  children: 'fieldModelList'
});
const operaType = ref<number | null>(null); // 1是新增;2是修改;主要针对shp操作的
const parentAttr = ref([]); // 父节点的属性组
let parentYS: any = reactive({}); // 父要素
const shpWkid = ref(); // 上传的shp的wkid
const wkidTransitionDilaog = ref(false); // 坐标系转换弹窗
const transitionProgress = ref(0); // 转换进度
const transitionStatus = ref(''); // 转换状态
const transitionMsg = ref(''); // 转换条数内容
const transitionNum = ref(0); // 需要转换的总条数
const isID = ref(false); // 是否是默认id映射
const isParentID = ref(false); // 是否是默认映射parentID
const uploadShpError = ref([]); // 上传失败的数据
const chooseWkidDialog = ref(false); // 选择坐标系弹窗
const wkidMap = ref(wkidMapData); // 坐标系映射
const shpSource = ref(null); // shp里面的数据源
const verifyDialog = ref(false); // 验证编码方式结果弹窗
const publicPlanDialog = ref(false); // 公共进度弹窗
const publicMsg = ref(''); // 公共进度弹框的描述
const publicPrecentage = ref(0); // 公共弹框的进度值
const endSubmitList = ref([]); // 最终提交
const fId = ref(1); // 手动给的排序
const uploadSuccessCount = ref(0); // 成功导入条数
const endMsgDialog = ref(false); // 最终更新结果公示弹窗
const verificationDialog = ref(false); // 上传前验证数据弹窗
const nextEndList = ref([]); // 最终提交的数据列表
const verificationPlan = ref(0); // 校验进度
const verSuccNum = ref(0); // 验证成功条数
const verErrorNum = ref(0); // 验证失败条数
const isRead = ref(false); // 是否点击了读取
const sourceWkid = ref(0); // 原始wkid
const wakeLock = ref(null); // 休眠对象
const successUploadList = ref([]); // 上传成功的数据id 根节点
// 刷新表达式要用到的内容
const expressList = ref([]); // 表达式列表
const treeExpressList = ref([]); // 树的表达式列表
const expressPlanDialog = ref(false); // 刷新表达式进度弹窗
const expressMsg = ref(''); // 公共进度弹框的描述
const expressPrecentage = ref(0); // 公共弹框的进度值
const publicExpressList = ref([]); // 通过后台得到的最终表达式顺序 所有数据都需要安装该顺序进行排序
const expressSuccessCount = ref(0);
const ruleTree = ref([]); // 规则树
const moduleId = ref(); // 模块id
//---------------- ref 引用
const ruleForm = ref(null);
const file = ref(null);
const tableHeight = ref(window.innerHeight - 300); // 表格的高度

// --------------- emit 部分
const emit = defineEmits<{
  (e: 'closeDialog'): void;
}>();

//---------------父组件传入的参数
interface Props {
  uploadProjectDialog: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  uploadProjectDialog: false
});

const uploadProjectDialogCopy = computed(() => props.uploadProjectDialog);

// ------------方法部分
/**
 * 获取树形图
 */
const getData = () => {
  selectRules({ moduleId: moduleId.value }).then((res) => {
    if (res.code == 200) {
      ruleTree.value = res.data;
      treeOptions.value = checkList(res.data);
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 检查列表
 * @param arr 列表
 * @returns
 */
const checkList = (arr) => {
  return arr.map((item) => {
    if (item.list && item.list.length > 0) {
      checkList(item.list);
      return item;
    } else {
      delete item.list;
      return item;
    }
  });
};

/**
 * 根据当前选择的属性组自己拿出来其中的字段
 * @param val 当前的属性组id
 */
const handleFieldCurrentGroup = (val) => {
  // 设置当前页面的值完成，在页面做一个标识
  val.finished = true;
  const list = [];
  val.fieldModelList.forEach((item) => {
    if (item.valueMethod == 'idCardScan') {
      // 用于在页面上不展示
      item.tempStatus = true;
      // 身份证识别
      const SFZSBOptions = [
        { label: 0, text: '姓名', enName: `${item.fieldName}_0`, fieldType: 'String' },
        { label: 1, text: '性别', enName: `${item.fieldName}_1`, fieldType: 'String' },
        { label: 2, text: '民族', enName: `${item.fieldName}_2`, fieldType: 'String' },
        { label: 3, text: '出生日期', enName: `${item.fieldName}_3`, fieldType: 'Date' },
        { label: 4, text: '住址', enName: `${item.fieldName}_4`, fieldType: 'String' },
        { label: 5, text: '身份证号', enName: `${item.fieldName}_5`, fieldType: 'String' },
        { label: 6, text: '签发机关', enName: `${item.fieldName}_6`, fieldType: 'String' },
        { label: 7, text: '有效期限', enName: `${item.fieldName}_7`, fieldType: 'String' },
        { label: 8, text: '身份证正面', enName: `${item.fieldName}_8`, fieldType: 'Pic' },
        { label: 9, text: '身份证反面', enName: `${item.fieldName}_9`, fieldType: 'Pic' }
      ];
      if (item.attribution && item.attribution.list && item.attribution.list.length > 0) {
        SFZSBOptions.forEach((sfz) => {
          if (item.attribution.list.includes(sfz.label)) {
            const obj = {
              fieldName: sfz.enName,
              fieldCn: sfz.text,
              fieldType: sfz.fieldType,
              valueMethod: 'idCardScan'
            };
            list.push(obj);
          }
        });
      }
    } else if (item.valueMethod == 'xttable') {
      // 用于在页面上不展示
      item.tempStatus = true;
      if (item.attribution && item.attribution.children && item.attribution.children.length > 0) {
        item.attribution.children.forEach((child) => {
          const obj = {
            fieldName: child.fieldName,
            fieldCn: child.fieldCn,
            fieldType: child.fieldType,
            valueMethod: 'xttable'
          };
          list.push(obj);
        });
      }
    }
  });

  if (list.length > 0) {
    list.forEach((v) => {
      val.fieldModelList.push(v);
    });
  }
  localfields.value = val.fieldModelList.filter((item) => !item.tempStatus);
  getLocalFild();
};
/**
 * 树形图选择
 * @param value 选择值
 */
const handleTreeChange = (value) => {
  updateRuleId.value = value[value.length - 1];
  handleFindTreeItem(treeOptions.value, updateRuleId.value);
  // 如果不是选择的第一级，需要找到父级的属性组  key2的时候要用
  if (value.length != 1) {
    getParentAttr(treeOptions.value, value[value.length - 2]);
  }
};

/**
 * 通过子id获取父属性组
 * @param list 列表
 * @param val 值
 */
const getParentAttr = (list, val) => {
  list.forEach((item) => {
    if (item.id == val) {
      parentYS = JSON.parse(JSON.stringify(item));
      parentAttr.value = getAttrList(JSON.parse(JSON.stringify(item.fieldGroupModelList)));
    } else if (item.list) {
      getParentAttr(item.list, val);
    }
  });
};

/**
 * 循环遍历查找当前的某一项
 * @param list 列表
 * @param id 值
 */
const handleFindTreeItem = (list, id) => {
  list.forEach((item) => {
    if (item.id == id) {
      currentTreeItem.value = JSON.parse(JSON.stringify(item));
      // 用于让用户指定属性组的字段  key1 key2
      attrList.value = getAttrList(currentTreeItem.value.fieldGroupModelList);
      initDataTree([currentTreeItem.value]);
    } else if (item.list) {
      handleFindTreeItem(item.list, id);
    }
  });
};

/**
 * 组装属性组和字段
 * @param list 列表
 * @returns
 */
const getAttrList = (list) => {
  list.forEach((v) => {
    v.label = v.typeName;
    v.value = v.linkId;
    v.fieldModelList.forEach((k) => {
      k.label = `${k.fieldCn}(${k.fieldName})`;
      k.value = k.fieldName;
    });
  });
  return list;
};

/**
 * 整理规则树结构为数据树结构
 * @param list 列表
 */
const initDataTree = (list) => {
  list.forEach((v) => {
    v.ruleId = v.id;
    v.ruleName = v.parcelName;
    v.dataState = 0;
    v.appType = 2;
    v.parcelName = v.typeName;
    delete v.id;
    // 根据app的情况，现在只需要上传有效数据 如果新增宗地就不需要同时上传空的房产等数据 2025.7.16 11：18
    v.list = [];
    // if (v.graphicalMaxNum == v.graphicalMinNum) {
    //   for (let index = 0; index < v.graphicalMinNum - 1; index++) {
    //     const item = JSON.parse(JSON.stringify(v));
    //     list.push(item);
    //   }
    // }
    // if (v.list && v.list.length != 0) {
    //   initDataTree(v.list);
    // }
  });
};

/**
 * 处理宗地名称
 * @param val 值
 * @param type 类型
 */
const handleParcelName = (val, type) => {
  if (type == 1) {
    //key1
    if (val == 'ID') {
      //代表选择的id直接映射
      isID.value = true;
    } else {
      isID.value = false;
    }
  } else if (type == 2) {
    //key2
    if (val == 'PARENTID') {
      isParentID.value = true;
    } else {
      isParentID.value = false;
    }
  }
};

/**
 * 获取组名
 * @param item 项
 * @returns
 */
const getGroupLabel = (item) => {
  if (item.ruleAttribution && (item.ruleAttribution.type == 'graphicalPoint' || item.ruleAttribution.type == 'commonPoint')) {
    if (item.finished) {
      return `${item.typeName}  (点)(已映射)`;
    } else {
      return `${item.typeName}  (点)`;
    }
  } else if (item.ruleAttribution && (item.ruleAttribution.type == 'graphicalLine' || item.ruleAttribution.type == 'commonLine')) {
    if (item.finished) {
      return `${item.typeName}  (线)(已映射)`;
    } else {
      return `${item.typeName}  (线)`;
    }
  } else {
    if (item.finished) {
      return `${item.typeName} (已映射)`;
    } else {
      return `${item.typeName}  `;
    }
  }
};

/**
 * 关闭映射字段弹框
 */
const handleDialogVisible = () => {
  selectFrom.currentGroup = undefined;
  selectFrom.currentParcelName = undefined;
  selectFrom.currentNodeId = undefined;
  selectFrom.currentParentId = undefined;
  selectFrom.souseKey1 = '';
  selectFrom.souseKey2 = '';
  localfields.value = [];
  shpFields.value = [];
  if (ruleForm.value) {
    ruleForm.value.clearValidate();
  }
  dialogVisible.value = false;
};

/**
 * 关闭
 */
const handleClose = () => {
  handleDialogVisible();
  isRead.value = false;
  //还需要把 file清除
  shpList.value = [];
  updateType.value = [];
  // 清空选择的shp列表
  const list = document.getElementById('fileList');
  // 清空现有列表
  list.innerHTML = '';
  // 清空上传
  const fileInput = file.value;
  fileInput.value = '';
  operaType.value = null;
  clearDormancy();
  submitList.value = [];
  nextEndList.value = [];
  emit('closeDialog');
};

/**
 * 提交更新项目
 */
const submitUploadProject = () => {
  // 先清空数据
  submitList.value = [];
  nextEndList.value = [];
  shpList.value = [];
  if (!operaType.value) {
    ElMessage.error('请选择操作方式');
    return;
  }
  // 判断第二步是否选择了
  if (updateType.value.length == 0) {
    ElMessage.error('请选择更新宗地!!!');
    return;
  }
  isRead.value = true;
  let files = file.value.files;
  // files = Array.from(new Array(files.length), (i, idx) => files[idx]) // 等效下面写法
  files = Array.from(files); // FileList => Array, 方便使用 Array 方法
  // 解析 shp
  parseShapefile(files); // 解析选择的 shp 并绘制显示
};

/**
 * 解析 shp
 * @param files 文件
 */
const parseShapefile = async (files) => {
  if (files.length == 0) {
    ElMessage.error('请选择文件!!!');
    return;
  }
  if (files.length != 4) {
    ElMessage.error('请选择正确的文件!!!');
    return;
  }
  fullscreenLoading.value = true;
  const shpFile = files.find((f) => f.name.endsWith('.shp'));
  const dbfFile = files.find((f) => f.name.endsWith('.dbf'));
  const prjFile = files.find((f) => f.name.endsWith('.prj'));
  const promises = [shpFile, dbfFile].map((i) => readInputFile(i));
  promises.push(readInputFile(prjFile, 'Text'));
  let prjCrs;
  Promise.all(promises)
    .then(([shp, dbf, prj]) => {
      prjCrs = new proj4.Proj(prj);
      if (prjCrs.AUTHORITY) {
        shpWkid.value = prjCrs.AUTHORITY.EPSG;
      } else {
        shpWkid.value = getWkidForSrsCode(prjCrs.srsCode);
        if (!shpWkid.value) {
          //如果没有匹配到坐标系就提示用户，并让用户自己选择一个坐标系
          chooseWkidDialog.value = true;
        }
      }
      // return shapeOpen(shp, dbf)
      // 指定 dbf 编码 'utf-8', 解决geojson properties乱码
      return shapeRead(shp, dbf, { encoding: bmType.value });
    })
    .then(async (source) => {
      if (source.features.leng != 0) {
        //获取原始坐标系
        getSourceWkid(source.features[0].geometry.coordinates[0][0]);
      }

      // 节点为点 shp数据不是点的话报错
      if (chooseWkidDialog.value) {
        //wkid没有对应上的话，需要等用户在弹窗里面把wkid选了之后再往下执行
        shpSource.value = source;
        fullscreenLoading.value = false;
        return;
      }
      if (currentTreeItem.value.graphicalType == 1 && !source.features[0].geometry.type.includes('Point')) {
        ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
        fullscreenLoading.value = false;
        return;
      }
      // 节点为线 shp数据不是线的话报错
      if (currentTreeItem.value.graphicalType == 2 && !source.features[0].geometry.type.includes('Line')) {
        ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
        fullscreenLoading.value = false;
        return;
      }
      // 节点为面 shp数据不是面的话报错
      if (currentTreeItem.value.graphicalType == 3 && !source.features[0].geometry.type.includes('Polygon')) {
        ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
        fullscreenLoading.value = false;
        return;
      }
      if (!source.features && source.features.length == 0) {
        ElMessage.error('没有数据，请重新上传!!!');
        return;
      }
      const sourceList = []; //处理图形数据 去除多面
      const MultiPolygon = [];
      source.features.forEach((v) => {
        if (!v.geometry.type.includes('MultiPolygon')) {
          sourceList.push(v);
        } else {
          MultiPolygon.push(v);
        }
      });
      if (MultiPolygon.length > 0) {
        //下载txt文件
        const element = document.createElement('a');
        const endContent = [];
        MultiPolygon.forEach((v) => {
          const propertiesKeys = Object.keys(v.properties);
          const values = [];
          propertiesKeys.forEach((k) => {
            values.push(`${k}:${v.properties[k]}`);
          });
          const oneRow = values.join(';');
          endContent.push(oneRow + '\n');
        });
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent.join('\n')));
        element.setAttribute('download', '多面数据');
        element.style.display = 'none';
        element.click();
        // document.body.removeChild(element) //下载完成移除元素
      }
      if (sourceList.length < source.features.length) {
        //代表有多面被去除了
        ElMessageBox.alert('暂不支持MultiPolygon(多面类型的导入)，本次导入已剔除！！！', '警告', {
          confirmButtonText: '确定',
          callback: (action) => {}
        });
      }
      fullscreenLoading.value = false;
      if (shpWkid.value && shpWkid.value != '3857') {
        //需要转换坐标系
        shpList.value = [];
        // 转换坐标系

        const chunkSize = 10;
        const chunks = [];
        transitionNum.value = sourceList.length;
        sourceList.forEach((v) => {
          v.oldGeo = JSON.parse(JSON.stringify(v.geometry));
        });
        // 拆分数组
        for (let i = 0; i < sourceList.length; i += chunkSize) {
          chunks.push(sourceList.slice(i, i + chunkSize));
        }
        // 初始化转换进度弹窗
        const loading = ElLoading.service({
          lock: true,
          text: '坐标系转换中',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });
        // 依次转换子数组
        for (let index = 0; index < chunks.length; index++) {
          try {
            await initWkid(chunks[index], index + 1);
          } catch (error) {
            ElMessage.error(error);
            // 处理错误，比如跳过当前子数组或中断整个上传过程
            continue;
          }
        }
        loading.close();
      } else {
        shpList.value = sourceList;
      }
      const shpFieldsTemp = Object.keys(source.features[0].properties);
      shpFields.value = shpFieldsTemp;
      const list = JSON.parse(JSON.stringify(treeOptions.value));
      await getModelGroupListById(list);
    });
};

/**
 * 当wkid没有对应上的时候 再次调用转换坐标系以及shp类型跟节点类型匹配验证
 */
const initSouse = async () => {
  if (currentTreeItem.value.graphicalType == 1 && !shpSource.value.features[0].geometry.type.includes('Point')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  // 节点为线 shp数据不是线的话报错
  if (currentTreeItem.value.graphicalType == 2 && !shpSource.value.features[0].geometry.type.includes('Line')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  // 节点为面 shp数据不是面的话报错
  if (currentTreeItem.value.graphicalType == 3 && !shpSource.value.features[0].geometry.type.includes('Polygon')) {
    ElMessage.error('节点类型跟shp类型不匹配，请重新选择上传！！！');
    fullscreenLoading.value = false;
    return;
  }
  if (!shpSource.value.features && shpSource.value.features.length == 0) {
    ElMessage.error('没有数据，请重新上传!!!');
    return;
  }
  const sourceList = []; //处理图形数据 去除多面
  const MultiPolygon = [];
  shpSource.value.features.forEach((v) => {
    if (!v.geometry.type.includes('MultiPolygon')) {
      sourceList.push(v);
    } else {
      MultiPolygon.push(v);
    }
  });
  if (MultiPolygon.length > 0) {
  }
  if (sourceList.length < shpSource.value.features.length) {
    //代表有多面被去除了
    ElMessageBox.alert('暂不支持MultiPolygon(多面类型的导入)，本次导入已剔除！！！', '警告', {
      confirmButtonText: '确定',
      callback: (action) => {}
    });
  }
  fullscreenLoading.value = false;
  if (shpWkid.value && shpWkid.value != '3857') {
    //需要转换坐标系
    shpList.value = [];
    // 转换坐标系
    const chunkSize = 10;
    const chunks = [];
    transitionNum.value = sourceList.length;
    // 拆分数组
    for (let i = 0; i < sourceList.length; i += chunkSize) {
      chunks.push(sourceList.slice(i, i + chunkSize));
    }
    // 初始化转换进度弹窗
    const loading = ElLoading.service({
      lock: true,
      text: '坐标系转换中',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255, 255, 0.7)'
    });
    // 依次转换子数组
    for (let index = 0; index < chunks.length; index++) {
      try {
        await initWkid(chunks[index], index + 1);
      } catch (error) {
        ElMessage.error(error);
        // 处理错误，比如跳过当前子数组或中断整个上传过程
        continue;
      }
    }
    loading.close();
  } else {
    shpList.value = sourceList;
  }
  const shpFieldsTemp = Object.keys(shpSource.value.features[0].properties);
  shpFields.value = shpFieldsTemp;
  const list = JSON.parse(JSON.stringify(treeOptions.value));
  await getModelGroupListById(list);
};

/**
 * 读取文件
 * @param file 文件
 * @param type 类型
 * @returns 文件数据
 */
const readInputFile = async (file, type = 'ArrayBuffer') => {
  // 读取文件
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    switch (type) {
      case 'ArrayBuffer':
        reader.readAsArrayBuffer(file);
        break;
      case 'Text':
        reader.readAsText(file);
        break;
      case 'BinaryString':
        reader.readAsBinaryString(file);
        break;
      case 'DataURL':
        reader.readAsDataURL(file);
        break;
    }

    reader.onload = function () {
      // this.result 就是读取到的文件的数据
      resolve(this.result);
    };

    reader.onerror = function () {
      reject(this);
    };
  });
};

/**
 * initWkid
 * @param list 数据
 * @param num 数量
 */
const initWkid = async (list, num) => {
  return new Promise((resolve, reject) => {
    loadModules(
      [
        'esri/geometry/SpatialReference',
        'esri/geometry/projection',
        'esri/geometry/Polygon',
        'esri/Graphic',
        'esri/geometry/Point',
        'esri/geometry/Polyline'
      ],
      config
    ).then(([SpatialReference, projection, Polygon, Graphic, Point, Polyline]) => {
      projection.load().then(() => {
        const newList = [];
        if (list[0].geometry.type.includes('Polygon')) {
          //面
          list.forEach((v) => {
            const polygon = new Polygon({
              type: 'polygon',
              rings: v.geometry.coordinates,
              spatialReference: { wkid: shpWkid.value || 3857 }
            });
            newList.push(polygon);
          });
        } else if (list[0].geometry.type.includes('Point')) {
          //点
          list.forEach((v) => {
            const point = new Point({
              x: v.geometry.coordinates[0],
              y: v.geometry.coordinates[1],
              spatialReference: {
                wkid: shpWkid.value || 3857
              }
            });
            newList.push(point);
          });
        } else if (list[0].geometry.type.includes('LineString')) {
          //线
          list.forEach((v) => {
            const line = new Polyline({
              paths: v.geometry.coordinates,
              spatialReference: {
                wkid: shpWkid.value || 3857
              }
            });
            newList.push(line);
          });
        }
        // 设置目标坐标系
        const outSR = new SpatialReference({ wkid: 3857 }); // WKID为3857代表WGS 1984 Web Mercator
        const projectedPoints = projection.project(newList, outSR);
        if (list[0].geometry.type.includes('Polygon')) {
          //面
          list.forEach((v, idx) => {
            if (projectedPoints[idx]) {
              v.geometry.coordinates = projectedPoints[idx].rings;
            } else {
              //如果转换图形失败 需要一个一个的转换
              // reject(`${}数据异常`)
              const coordinates = [];
              v.geometry.coordinates.forEach((k) => {
                const list = [];
                k[0].forEach((o) => {
                  const itePoint = new Point({
                    x: o[0],
                    y: o[1],
                    spatialReference: {
                      wkid: shpWkid.value || 3857
                    }
                  });
                  const projectedPoints = projection.project(itePoint, outSR);
                  list.push([projectedPoints.x, projectedPoints.y]);
                });
                coordinates.push([list]);
              });
              v.geometry.coordinates = coordinates;
            }
          });
        } else if (list[0].geometry.type.includes('Point')) {
          //点
          list.forEach((v, idx) => {
            v.geometry.coordinates = [projectedPoints[idx].x, projectedPoints[idx].y];
          });
        } else if (list[0].geometry.type.includes('LineString')) {
          //线
          list.forEach((v, idx) => {
            v.geometry.coordinates = projectedPoints[idx].paths[0];
          });
        }

        shpList.value.push(...list);
        resolve('');
      });
    });
  });
};

/**
 * 获取本地字段
 */
const getLocalFild = () => {
  localfields.value.forEach((f) => {
    for (let index = 0; index < shpFields.value.length; index++) {
      if (!f.attribution.expression && f.fieldName.toUpperCase() == shpFields.value[index].toUpperCase()) {
        f.yz = shpFields.value[index];
        break;
      }
    }
  });
};

/**
 * 根据id 遍历树结构 返回当前节点相等所有属性组（返回属性组中的字段）
 */
const getModelGroupListById = (list) => {
  try {
    if (list && list.length > 0) {
      list.forEach((item) => {
        if (item.id == updateRuleId.value) {
          //  这里是数组去重，暂时先不要删除这里的代码
          // let obj = {}
          // this.fieldGroupModelList = item.fieldGroupModelList.reduce((pre,cur)=>{
          //   obj[cur.id]?'':obj[cur.id] = true && pre.push(cur)
          //   return pre
          // },[])
          fieldGroupModelList.value = item.fieldGroupModelList;
          fieldGroupModelList.value.forEach((v) => {
            if (v.ruleAttribution) {
              v.disable = true;
            }
          });
          dialogVisible.value = true;
        } else {
          // 上面处理了list  在这里要做一个判断 不是每个节点下都有一个list
          if (item.list && item.list.length > 0) {
            getModelGroupListById(item.list);
          }
        }
      });
    }
  } catch (error) {}
};

/**
 * 公共方法抽取数据 分段截取数据
 */
const handleChunkData = (list, num) => {
  const count = list.length;
  const chunkSize = num || 10;
  const chunks = [];
  // 拆分数组
  for (let i = 0; i < list.length; i += chunkSize) {
    // 这里创建二维数组内容，分 10 个 截成一个二维数组
    chunks.push(list.slice(i, i + chunkSize));
  }
  const resultItem = {
    count: count,
    chunkSize: chunkSize,
    chunks: chunks
  };
  return new Promise((resolve) => {
    resolve(resultItem);
  });
};

/**
 * 强制更新计算结果到数据上面
 */
const handleUpdateRsult = (resultData, index) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const planNum =
        Number(((((index + 1) * resultData.chunkSize) / resultData.count) * 100).toFixed(2)) > 100
          ? 100
          : Number(((((index + 1) * resultData.chunkSize) / resultData.count) * 100).toFixed(2));
      publicPrecentage.value = Number(planNum);
      publicMsg.value = `数据处理中，当前进度${planNum}%`;
      resolve(publicPrecentage.value);
    }, 500);
  });
};

/**
 * 提交字段
 */
const submitFields = async () => {
  publicPlanDialog.value = true;
  publicMsg.value = `数据处理中，当前进度0%`;
  const lineAttr = []; //线 子要素属性组 集合
  const pointAttr = []; //点 子要素属性组 集合
  currentTreeItem.value.fieldGroupModelList.forEach((v) => {
    if (v.groupScope == 2) {
      //这时候才是要自动生成的
      if (v.ruleAttribution && (v.ruleAttribution.type == 'graphicalLine' || v.ruleAttribution.type == 'commonLine')) {
        //线子要素
        lineAttr.push(v);
      } else if (v.ruleAttribution && (v.ruleAttribution.type == 'graphicalPoint' || v.ruleAttribution.type == 'commonPoint')) {
        //点子要素
        pointAttr.push(v);
      }
    }
  });
  // 这里必须优先处理数据拆分
  const resultData: any = await handleChunkData(shpList.value, 100);
  const newFieldGroup = fieldGroupModelList.value.filter((item) => {
    return item.finished;
  });
  for (const [index, item] of resultData.chunks.entries()) {
    await disposeFiled(item, newFieldGroup, lineAttr, pointAttr);
    const resultNum = await handleUpdateRsult(resultData, index);
    if (resultNum == 100) {
      // 当加载进度完成100  时  关闭弹框
      publicPlanDialog.value = false;
      dialogVisible.value = false;
    }
  }
};

/**
 * 映射完字段 之后的异步处理
 */
const disposeFiled = (list, newFieldGroup, lineAttr, pointAttr) => {
  return new Promise((resolve, reject) => {
    loadModules(
      [
        'esri/geometry/Point',
        'esri/geometry/geometryEngine',
        'esri/Graphic',
        'esri/geometry/SpatialReference',
        'esri/geometry/Polyline',
        'esri/geometry/projection'
      ],
      config
    ).then(([Point, geometryEngine, Graphic, SpatialReference, Polyline, projection]) => {
      list.forEach(async (v, index) => {
        const geomWkt = `SRID=3857;${geojsonToWKT(v.geometry)}`;
        let geometry = {};
        const oldGeo = {
          rings: v.oldGeo.coordinates
        };
        // 面 多面
        if (v.geometry.type.includes('Polygon')) {
          geometry = {
            rings: v.geometry.coordinates,
            spatialReference: {
              latestWkid: 3857,
              wkid: 102100
            }
          };
        } else if (v.geometry.type.includes('Point')) {
          //点
          geometry = {
            x: v.geometry.coordinates[0],
            y: v.geometry.coordinates[1],
            spatialReference: {
              latestWkid: 3857,
              wkid: 102100
            }
          };
        } else if (v.geometry.type.includes('Line')) {
          // 线
          geometry = {
            paths: [v.geometry.coordinates],
            spatialReference: {
              latestWkid: 3857,
              wkid: 102100
            }
          };
        }
        const fieldInstanceModels = [];
        newFieldGroup.forEach((k) => {
          const groupItem = {
            appId: 0,
            attribution: {},
            groupId: k.id,
            groupName: k.typeName,
            linkId: k.linkId,
            ruleAttribution: k.ruleAttribution
          };
          // 属性组这里需要特殊处理身份证识别
          k.fieldModelList.forEach((q) => {
            if (q.valueMethod == 'idCardScan') {
              // 处理身份证识别
              if (q.attribution && q.attribution.list && q.attribution.list.length > 0) {
                q.attribution.list.forEach((i) => {
                  groupItem.attribution[`${q.fieldName}_${i}`] = (v.properties[q.yz] + '').includes('\u0000') ? '' : v.properties[q.yz];
                });
              }
            } else if (q.valueMethod == 'xttable') {
              const childList = [];
              // 处理表格
              if (q.attribution && q.attribution.children && q.attribution.children.length > 0) {
                const obj = {};
                q.attribution.children.forEach((child) => {
                  if (v.properties[child.yz]) {
                    obj[child.fieldName] = (v.properties[child.yz] + '').includes('\u0000') ? '' : v.properties[child.yz];
                  }
                });
                if (Object.keys(obj).length != 0) {
                  childList.push(obj);
                }
              }
              if (q.tempStatus && childList.length != 0) {
                groupItem.attribution[`${q.fieldName}`] = JSON.stringify(childList);
              }
            } else if (q.valueMethod == 'select' || q.valueMethod == 'radio') {
              // 里面可能是label也可能存的value 都要兼容
              let value = v.properties[q.yz];
              if (value && !(value + '').includes('\u0000')) {
                for (let i = 0; i < q.attribution.options.length; i++) {
                  if (q.attribution.options[i].label.trim() == v.properties[q.yz].trim()) {
                    value = q.attribution.options[i].value;
                    break;
                  }
                }
              } else if (value && (value + '').includes('\u0000')) {
                value = undefined;
              }
              groupItem.attribution[q.fieldName] = value;
            } else if (q.yz && q.valueMethod != 'xttable') {
              if (q.valueMethod == 'date') {
                //时间处理
                let timeValue = undefined;
                if ((v.properties[q.yz] + '').includes('\u0000')) {
                  timeValue = '';
                } else {
                  timeValue = new Date(v.properties[q.yz]).getTime();
                  if (timeValue == -2211782743000) {
                    //shp里面日期为空，但是导入进来的时候就有默认值 去掉
                    timeValue = '';
                  }
                }
                groupItem.attribution[q.fieldName] = timeValue;
              } else {
                groupItem.attribution[q.fieldName] = (v.properties[q.yz] + '').includes('\u0000') ? '' : v.properties[q.yz];
              }
            } else {
              groupItem.attribution[q.fieldName] = (v.properties[q.yz] + '').includes('\u0000') ? '' : v.properties[q.yz];
            }
          });
          fieldInstanceModels.push(groupItem);
        });
        // 直接组装成 提交的数据
        // let item = JSON.parse(JSON.stringify(this.currentTreeItem))
        const item = { ...currentTreeItem.value };
        // 手动生成一个排序
        item.shpId = fId.value;
        fId.value++;
        item.fieldInstanceModels = fieldInstanceModels;
        item.geometryModel = {
          geomArcgis: JSON.stringify(geometry),
          geomWkb: geomWkt,
          wkId: 3857
        };
        item.parcelName = v.properties[selectFrom.currentParcelName] ? v.properties[selectFrom.currentParcelName] : currentTreeItem.value.typeName;
        if (item.parcelName && (item.parcelName + '').includes('\u0000')) {
          item.parcelName = currentTreeItem.value.typeName;
        }
        item.appType = 3;
        item.ruleId = updateRuleId.value;
        // 赋值新增或者修改
        item.operaType = operaType.value;
        if (isID.value) {
          //如果直接选择的id 直接把id放入key1
          item.id = v.properties.ID;
          item.key1 = 'id';
          item.key1LinkId = selectFrom.currentGroup.linkId;
        } else {
          if (operaType.value != 1 && selectFrom.souseKey1[1].toUpperCase() == 'ID') {
            //修改类型的时候 并且key1是ID
            item.id = v.properties[selectFrom.currentNodeId];
          }
          if (selectFrom.souseKey1[1]) {
            item.key1 = selectFrom.souseKey1[1];
            item.key1Value = v.properties[selectFrom.currentNodeId].includes('\u0000') ? '' : v.properties[selectFrom.currentNodeId];
            item.key1LinkId = selectFrom.souseKey1[0];
          }
        }
        // 判断是否选了key2
        if (selectFrom.currentParentId) {
          if (isParentID.value) {
            //代表选择的是父节点的id
            item.key2 = 'parentId';
            item.key2Value = (v.properties[selectFrom.currentParentId] + '').includes('\u0000') ? '' : v.properties[selectFrom.currentParentId];
            item.key2LinkId = selectFrom.souseKey2[0];
          } else {
            item.key2 = selectFrom.souseKey2[1];
            item.key2Value = (v.properties[selectFrom.currentParentId] + '').includes('\u0000') ? '' : v.properties[selectFrom.currentParentId];
            item.key2LinkId = selectFrom.souseKey2[0];
          }
        }
        item.dataState = 1;
        // 生成子要素 && this.operaType != 2
        if (lineAttr.length != 0 || pointAttr.length != 0) {
          //有子要素的时候才进入执行 并且不是修改的时候
          const geom = JSON.parse(item.geometryModel.geomArcgis);
          const rings: any = await initJZD(oldGeo);
          const transRings: any = await initJZD(geometry);
          lineAttr.forEach((q) => {
            //组装线的属性组
            const fieldInstance = {
              appId: 0,
              attribution: { list: [] },
              groupId: q.id,
              groupName: q.typeName,
              linkId: q.linkId,
              ruleAttribution: q.ruleAttribution
            };
            rings.forEach(async (k, kdx) => {
              //循环图形的点集合
              k.forEach(async (i, idx) => {
                const itemObj = {};
                q.fieldModelList.forEach(async (o) => {
                  //找属性组的字段
                  if (o.attribution.expression) {
                    //代表是表达式
                    const value = await parseExpression(o.attribution.expression, idx, rings, kdx, transRings, Number(shpWkid.value));
                    if (typeof value != 'string') {
                      //代表有不同属性组互相取值的情况
                      for (let i = 0; i < item.fieldInstanceModels.length; i++) {
                        if (item.fieldInstanceModels[i].groupName == value.expression[1]) {
                          //代表找到属性组
                          // 给改字段重新赋值
                          itemObj[o.fieldName] = item.fieldInstanceModels[i].attribution[value.expression[2]];
                          break;
                        }
                      }
                    } else {
                      itemObj[o.fieldName] = value;
                    }
                  } else if (o.attribution.defaultValue) {
                    //代表有默认值
                    itemObj[o.fieldName] = o.attribution.defaultValue;
                  } else {
                    //直接置空
                    itemObj[o.fieldName] = '';
                  }
                });
                fieldInstance.attribution.list.push(itemObj);
              });
            });
            item.fieldInstanceModels.push(fieldInstance);
          });
          pointAttr.forEach(async (q) => {
            //组装点的属性组
            const fieldInstance = {
              appId: 0,
              attribution: { list: [] },
              groupId: q.id,
              groupName: q.typeName,
              linkId: q.linkId,
              ruleAttribution: q.ruleAttribution
            };
            let beginNum = 1;
            rings.forEach(async (k, kdx) => {
              //循环图形的点集合
              k.forEach(async (i, idx) => {
                const itemObj = {};
                q.fieldModelList.forEach(async (o) => {
                  //找属性组的字段
                  if (o.attribution.expression) {
                    //代表是表达式
                    const value = await parseExpression(o.attribution.expression, idx, rings, kdx, transRings, Number(shpWkid.value));
                    if (typeof value != 'string') {
                      //代表有不同属性组互相取值的情况
                      for (let i = 0; i < item.fieldInstanceModels.length; i++) {
                        if (item.fieldInstanceModels[i].groupName == value.expression[1]) {
                          //代表找到属性组
                          // 给改字段重新赋值
                          itemObj[o.fieldName] = item.fieldInstanceModels[i].attribution[value.expression[2]];
                          break;
                        }
                      }
                    } else {
                      itemObj[o.fieldName] = value;
                    }
                    beginNum++;
                  } else if (o.attribution.defaultValue) {
                    //代表有默认值
                    itemObj[o.fieldName] = o.attribution.defaultValue;
                  } else {
                    //直接置空
                    itemObj[o.fieldName] = '';
                  }
                });
                fieldInstance.attribution.list.push(itemObj);
              });
            });
            item.fieldInstanceModels.push(fieldInstance);
          });
        }
        submitList.value.push(item);
      });
      resolve('');
    });
  });
};

/**
 * shp字段改变
 * @param val
 */
const changeShpField = (val) => {
  fieldGroupModelList.value.map((item) => {
    item.fieldModelList.map((field) => {
      if (field.valueMethod == 'xttable' && field.tempStatus) {
        if (field.attribution && field.attribution.children && field.attribution.children.length > 0)
          field.attribution.children.map((f) => {
            if (f.fieldName == val.fieldName) {
              f.yz = val.yz;
              return f;
            }
          });
        return field;
      }
    });
    return item;
  });
};

//最终提交更新shp请求
const submitUpdateShp = async () => {
  successUploadList.value = [];
  if (!isRead.value) {
    ElMessage.error('请先点击读取数据！！！');
    return;
  }
  if (submitList.value.length == 0) {
    ElMessage.error('请勾选需要更新的数据');
    return;
  }
  if (!operaType.value) {
    ElMessage.error('请选择操作方式');
    return;
  }
  // 如果是新增的时候 不需要去映射key1 去掉验证
  if (operaType.value == 1) {
    ruleForm.value.clearValidate('currentNodeId');
  }
  // 初始化最终上传列表
  nextEndList.value = [];
  uploadShpError.value = [];
  verErrorNum.value = 0;
  verSuccNum.value = 0;

  const chunksData: any = await handleChunkData(submitList.value, 10);
  // 如果选择的是上传根节点并且是新增，那么无需验证
  // 如果不是，那么主动让用户自己选择是否验证
  // 这里需要上传前验证 为了实现整批数据要么一起上传要么都不上传
  if (operaType.value == 1 && updateType.value.length == 1) {
    //新增 并且新增的是第一级 只需要直接新增不需要验证
    nextEndList.value = submitList.value;
    nextSubmit();
  } else {
    verificationDialog.value = true;

    verificationPlan.value = 0;
    // 依次上传子数组校验
    for (let index = 0; index < chunksData.chunks.length; index++) {
      try {
        const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
        if (resultNum == 100) {
          // 当数据完成后关闭弹框
          // this.publicPlanDialog = false
          // this.handleOpenTipResult(chunksData.chunks)
        }
      } catch (error) {
        ElMessage.error(error);
        // 处理错误，比如跳过当前子数组或中断整个上传过程
        continue;
      }
    }
  }
};

/**
 * 单次校验一批数据
 * @param list
 * @param num
 * @param count
 * @param chunkSize
 */
const verificationOnece = async (list, num, count, chunkSize) => {
  return new Promise((resolve, reject) => {
    operaParcelFromShpCheck(list).then((res) => {
      if (res.code == 200) {
        setTimeout(() => {
          if (res.data.length == 0) {
            //代表该批次数据都可以成功导入
            verSuccNum.value = verSuccNum.value + list.length;
            nextEndList.value.push(...list);
          } else {
            //代表失败一部分或者全部失败
            verErrorNum.value = verErrorNum.value + res.data.length;
            if (res.data.length < list.length) {
              //这个时候需要把失败的批次里面成功的数据提取出来
              // shpId
              const errNums = [];
              res.data.forEach((v) => {
                errNums.push(parseInt(v.substring(1, v.indexOf('行'))));
              });
              list.forEach((v) => {
                if (!errNums.some((obj) => obj == v.shpId)) {
                  nextEndList.value.push(v);
                  verSuccNum.value++;
                }
              });
            }
            uploadShpError.value.push(...res.data);
          }
          verificationPlan.value = Number(
            Number((((num * chunkSize) / count) * 100).toFixed(2)) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
          );
          resolve(verificationPlan.value);
        }, 500);
      } else {
        reject(res.msg);
      }
    });
  });
};
const nextSubmit = async () => {
  if (nextEndList.value.length == 0) {
    ElMessage.error('校验成功的数据为0，请修改后重新提交！！！');
    return;
  }
  verificationDialog.value = false;
  // 点击之后马上打开等待的弹框
  publicPrecentage.value = 0;
  publicPlanDialog.value = true;
  publicMsg.value = `已成功导入0条`;
  uploadShpError.value = [];
  const chunksData: any = await handleChunkData(nextEndList.value, 10);
  // 依次上传子数组
  for (let index = 0; index < chunksData.chunks.length; index++) {
    try {
      const resultNum = await subsectionSubmit(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
      if (resultNum == 100) {
        // 当数据完成后关闭弹框
        publicPlanDialog.value = false;
        // 清除阻止电脑休眠
        clearDormancy();
        // this.handleOpenTipResult(chunksData.chunks)
      }
    } catch (error) {
      ElMessage.error(error);
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  endMsgDialog.value = true;
};

/**
 * 最终关闭更新信息弹窗
 */
const endClose = () => {
  endMsgDialog.value = false;
  sessionStorage.setItem('qiehuan_company', 'false');
  location.reload();
};

/**
 * 验证表达式
 * @param list
 */
const formulaOrder = async (list) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在验证表达式...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  formulaOrderApi(list).then(async (res: any) => {
    loading.close();
    if (res.code == 200) {
      publicExpressList.value = [];
      let num = 0;
      res.data.forEach((v) => {
        publicExpressList.value.push({
          fieldModel: v.fieldModel,
          groupName: v.groupName,
          id: num,
          linkId: v.linkId,
          ruleId: v.ruleId,
          groupId: v.groupId
        });
        num++;
      });

      verificationDialog.value = false;
      // 点击之后马上打开等待的弹框
      expressPrecentage.value = 0;
      expressPlanDialog.value = true;
      expressMsg.value = `已成功刷新表达式0条`;
      // 得到了验证表达式，就需要真正组装数据了
      const chunksData: any = await handleChunkData(successUploadList.value, 10);
      for (let index = 0; index < chunksData.chunks.length; index++) {
        try {
          const resultNum = await expressSubmit(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
          if (resultNum == 100) {
            // 当数据完成后关闭弹框
            expressPlanDialog.value = false;
            const str = `成功刷新${uploadSuccessCount.value}条数据`;
            ElMessageBox.alert(str, `刷新成功`, {
              confirmButtonText: '确定',
              callback: (action) => {
                // 在这添加是否切换公司的标识。
                // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                sessionStorage.setItem('qiehuan_company', 'false');
                location.reload();
              }
            });
          }
        } catch (error) {
          ElMessage.error(error);
          // 处理错误，比如跳过当前子数组或中断整个上传过程
          continue;
        }
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 表达式提交
 * @param list
 * @param num
 * @param count
 * @param chunkSize
 */
const expressSubmit = async (list, num, count, chunkSize) => {
  const item_list = []; //二维数组，把选择的数据拆分为表达式列表
  list.forEach((v) => {
    const one_list = [];
    getEndOneExpressData([v], v.id, one_list);
    one_list.sort((a, b) => a.id - b.id);
    item_list.push(one_list);
  });
  const endList = [];
  item_list.forEach((v) => {
    endList.push(...v);
  });
  return new Promise((resolve, reject) => {
    updateParcel(endList).then((res) => {
      publicPrecentage.value = Number(
        Number((((num * chunkSize) / count) * 100).toFixed(2)) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
      );
      if (res.code == 200) {
        setTimeout(() => {
          uploadSuccessCount.value = uploadSuccessCount.value + list.length;
          publicMsg.value = `已成功刷新表达式${uploadSuccessCount.value}条`;
          resolve(publicPrecentage.value);
        }, 500);
      } else {
        ElMessage.error(res.msg);
        reject(res.msg);
        if (publicPrecentage.value == 100) {
          publicPlanDialog.value = false;
        }
      }
    });
  });
};

// 处理映射key 倒数第二步
const disposeKey = (list) => {
  return new Promise((resolve, reject) => {
    list.forEach((currentItem) => {
      const item = JSON.parse(JSON.stringify(currentTreeItem.value));
      item.fieldInstanceModels = currentItem.fieldInstanceModels;
      item.geometryModel = currentItem.geometryModel;
      if (currentItem.parcelName) {
        item.parcelName = currentItem.parcelName.includes('\u0000') ? '' : currentItem.parcelName;
      }
      item.appType = 3;
      item.ruleId = currentItem.ruleId;

      // 赋值新增或者修改
      item.operaType = operaType.value;
      // 如果key1 是id的时候单独处理 在最外层加一个id字段
      if (isID.value) {
        //代表是选择的id 直接映射
        item.id = currentItem.key1;
        item.key1 = 'id';
        item.key1LinkId = selectFrom.currentGroup.linkId;
      } else {
        if (operaType.value != 1 && currentItem.key1.toUpperCase() == 'ID') {
          item.id = currentItem.shpId;
        }
        if (currentItem.key1) {
          item.key1 = currentItem.key1;
          item.key1LinkId = selectFrom.souseKey1[0];
        }
      }
      if (currentItem.key2) {
        if (isParentID.value) {
          //代表选择的是父节点的id
          item.key2 = 'parentId';
          item.key2Value = currentItem.key2;
          item.key2LinkId = selectFrom.souseKey2[0];
        } else {
          item.key2 = currentItem.key2;
          item.key2Value = currentItem.key2Value;
          item.key2LinkId = selectFrom.souseKey2[0];
        }
      }
      item.dataState = 1;
      endSubmitList.value.push(item);
    });
    resolve(null);
  });
};

/**
 * 第一次处理数据内容
 */
const handleSubmitList = async () => {
  const params = [];
  // 这里拆分数据
  const resultData = await handleChunkData(submitList.value, 10);
  for (let i = 0; i < params.length; i += 100) {
    // 计算当前批次的结束索引，但不超过数组长度
    const end = Math.min(i + 100, params.length);
    // 提取当前批次的元素
    const chunk = params.slice(i, end);
    // 处理这批元素
    const list = await disposeKey(chunk);
    params.push(list);
  }
};

/**
 * 当数据处理完成之后关闭加载进度的弹框之后，打开处理成功的结果
 * @param params
 */
const handleOpenTipResult = (params) => {
  const str = `成功${operaType.value == 1 ? '新增' : '更新'}${params.length - uploadShpError.value.length}条数据`;
  operaType.value = null;
  ElMessageBox.alert(str, `${operaType.value == 1 ? '新增' : '更新'}成功`, {
    confirmButtonText: '确定',
    callback: (action) => {
      // 在这添加是否切换公司的标识。
      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
      sessionStorage.setItem('qiehuan_company', 'false');
      location.reload();
    }
  });
};

/**
 * 两个属性组之间互相取值
 * @param params
 */
const speInit = async (params) => {
  return new Promise((resolve, reject) => {
    params.forEach((v) => {
      v.fieldInstanceModels.forEach((k) => {
        if (k.ruleAttribution) {
          k.attribution.list.forEach((o) => {
            const keys = Object.keys(o);
            keys.forEach((q) => {
              if (typeof o[q] != 'string') {
                //代表要取值的
                for (let i = 0; i < v.fieldInstanceModels.length; i++) {
                  if (v.fieldInstanceModels[i].groupName == o[q].expression[1]) {
                    //代表找到属性组
                    // 给改字段重新赋值
                    o[q] = v.fieldInstanceModels[i].attribution[o[q].expression[2]];
                    break;
                  }
                }
              }
            });
          });
        }
      });
    });
    resolve(null);
  });
};

/**
 *  excel导入分段提交
 *  list,当前分段处理的数据 num,数组的index+1 count,总数据量 chunkSize：公共方法中的每10条为一组
 *  chunkSize  这里使用这个参数方便后面只需要在公共方法中 handleChunkData 中修改分割的数据，如果在下面写===10  需要修改俩个地方
 * @param list
 * @param num
 * @param count
 * @param chunkSize
 */
const subsectionSubmit = async (list, num, count, chunkSize) => {
  return new Promise((resolve, reject) => {
    operaParcelFromShp(list).then((res) => {
      if (res.code == 200) {
        setTimeout(() => {
          if (res.data.strings.length == 0) {
            //代表该批次数据都导入成功
            uploadSuccessCount.value = uploadSuccessCount.value + list.length;
            successUploadList.value.push(...res.data.mainIds);
          } else {
            //代表失败一部分或者全部失败
            uploadSuccessCount.value = uploadSuccessCount.value + (list.length - res.data.strings.length);
            uploadShpError.value.push(...res.data.strings);
          }
          publicMsg.value = `已成功导入${uploadSuccessCount.value}条`;
          publicPrecentage.value = Number(
            Number((((num * chunkSize) / count) * 100).toFixed(2)) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
          );
          resolve(publicPrecentage.value);
        }, 500);
      } else {
        const errorList = [];
        list.forEach((v) => {
          errorList.push(`第${v.shpId}条数据错误`);
        });
        uploadShpError.value.push(...errorList);
        reject(res.msg);
      }
    });
  });
};

/**
 * 处理转换wkt工具类型
 * @param type
 * @returns
 */
const getType = (type) => {
  const firstChar = type[0].toLowerCase();
  const result = firstChar + type.slice(1);
  return result;
};

const handleChange = (value) => {};

/**
 * 文件变化的时候
 */
const updateFileList = () => {
  const fileList = file.value.files; // 获取文件列表
  const list = document.getElementById('fileList');
  // 清空现有列表
  list.innerHTML = '';
  // 遍历文件列表，并添加到页面上的列表中
  for (let i = 0; i < fileList.length; i++) {
    const fileName = fileList[i].name; // 获取文件名
    const listItem = document.createElement('li'); // 创建新的列表项
    listItem.textContent = fileName; // 设置列表项的文本内容为文件名
    list.appendChild(listItem); // 将列表项添加到页面上的列表中
  }
};

/**
 * 根据坐标得到两点之间的长度
 * @param p1
 * @param p2
 * @returns
 */
const getLineLength = (p1, p2) => {
  return new Promise((resolve, reject) => {
    loadModules(
      ['esri/geometry/Point', 'esri/geometry/geometryEngine', 'esri/Graphic', 'esri/geometry/SpatialReference', 'esri/geometry/Polyline'],
      config
    ).then(([Point, geometryEngine, Graphic, SpatialReference, Polyline]) => {
      const polylinePaths = [p1, p2];
      // 创建Polyline对象
      const polyline = new Polyline({
        paths: polylinePaths,
        spatialReference: { wkid: 3857 } // 使用WGS84坐标系
      });
      const length = geometryEngine.geodesicLength(polyline, 'meters').toFixed(2);
      resolve(length);
    });
  });
};

/**
 * 初始化点位置 按照J1-JN排序
 * @param geom
 * @returns
 */
const initJZD = (geom) => {
  return new Promise((resolve, reject) => {
    loadModules(['esri/geometry/geometryEngine', 'esri/geometry/Polygon', 'esri/geometry/projection'], config).then(
      ([geometryEngine, Polygon, projection]) => {
        projection.load().then(() => {
          const endRings = []; //最终的数组
          geom.rings.forEach((v, vdx) => {
            const endList = [];
            const polygon = new Polygon({
              type: 'polygon',
              rings: v,
              spatialReference: 3857
            });
            const ymax = polygon.extent.ymax;
            const xmin = polygon.extent.xmin;
            polygon.rings[0].pop();
            const area = geometryEngine.geodesicArea(polygon, 'square-meters');
            // 如果得到面积是负数为反着画，如果是正数是正着画
            const distanceList = [];
            polygon.rings[0].forEach((v) => {
              const distance = Math.sqrt(Math.pow(Math.abs(xmin - v[0]), 2) + Math.pow(Math.abs(ymax - v[1]), 2));
              distanceList.push(distance);
            });
            const min = Math.min(...distanceList);
            const index = distanceList.indexOf(min);
            if (index == 0) {
              //即第一个点就是J1，不用分割数组
              if (area > 0) {
                //顺时针
                endList.push(...polygon.rings[0]);
              } else {
                //逆时针
                endList.push(polygon.rings[0][0]);
                const tem = JSON.parse(JSON.stringify(polygon.rings[0])).reverse();
                tem.forEach((v, idx) => {
                  if (idx != tem.length - 1) {
                    endList.push(v);
                  }
                });
              }
            } else if (index == distanceList.length - 1) {
              //最后一个点是J1，也不用分割，直接倒序
              if (area > 0) {
                //顺时针
                endList.push(polygon.rings[0][index]);
                polygon.rings[0].forEach((v, idx) => {
                  if (idx != index) {
                    endList.push(v);
                  }
                });
              } else {
                //逆时针
                endList.push(...JSON.parse(JSON.stringify(polygon.rings[0])).reverse());
              }
            } else {
              //需要分割数组
              if (area > 0) {
                //顺时针
                const left = [];
                const right = [];
                polygon.rings[0].forEach((v, idx) => {
                  if (idx < index) {
                    //左侧
                    left.push(v);
                  } else {
                    right.push(v);
                  }
                });
                // endList = right.concat(left);
                endList.push(...right.concat(left));
              } else {
                //逆时针
                let left = [];
                let right = [];
                polygon.rings[0].forEach((v, idx) => {
                  if (idx <= index) {
                    //左侧
                    left.push(v);
                  } else {
                    right.push(v);
                  }
                });
                left = left.reverse();
                right = right.reverse();
                endList.push(...left.concat(right));
              }
            }
            endRings.push(endList);
          });
          resolve(endRings);
        });
      }
    );
  });
};

/**
 * 关闭选择坐标系弹窗
 */
const handleCloseChooseWkid = () => {
  ElMessageBox.confirm('确定要关闭手动选择坐标系弹窗吗？如果不选择坐标系则不允许导入！！！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      chooseWkidDialog.value = false;
    })
    .catch(() => {});
};

/**
 * 提交选择的坐标系
 */
const sumitChooseWkid = () => {
  if (!shpWkid.value) {
    ElMessage.error('请务必选择正确的坐标系！！！');
    return;
  }
  chooseWkidDialog.value = false;
  initSouse();
};

/**
 * 弹出校验编码
 */
const verifyCoding = () => {
  verifyDialog.value = true;
};

/**
 * 关闭公共数据处理的弹框
 */
const handlePublickClose = () => {
  publicPrecentage.value = 0;
  publicMsg.value = '';
};

/**
 * 关闭校验编码的弹框
 */
const closeVerification = () => {
  verificationDialog.value = false;
};

/**
 * 下载错误日志
 */
const downLoadErrorLog = () => {
  //下载txt文件
  const element = document.createElement('a');
  const endContent = uploadShpError.value.join('\n');
  element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
  element.setAttribute('download', 'shp上传错误日志');
  element.style.display = 'none';
  element.click();
  document.body.removeChild(element); //下载完成移除元素
};

/**
 * 改变所有映射
 */
const changeYS = () => {
  ElMessageBox.confirm('确定要清除所有映射吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      localfields.value.forEach((v) => {
        v.yz = '';
      });
    })
    .catch(() => {});
};

/**
 * 获取原始坐标系
 * @param itemPoint
 */
const getSourceWkid = (itemPoint) => {
  loadModules(
    [
      'esri/geometry/Point',
      'esri/geometry/geometryEngine',
      'esri/Graphic',
      'esri/geometry/SpatialReference',
      'esri/geometry/Polyline',
      'esri/geometry/projection'
    ],
    config
  ).then(([Point, geometryEngine, Graphic, SpatialReference, Polyline, projection]) => {
    projection.load().then(() => {
      const point = new Point({
        x: itemPoint[0],
        y: itemPoint[1],
        spatialReference: {
          wkid: shpWkid.value || 3857
        }
      });
      const outSR = new SpatialReference({ wkid: 4326 }); // WKID为3857代表WGS 1984 Web Mercator
      const projectedPoints = projection.project(point, outSR);
      const degree = Math.ceil((projectedPoints.x - 1.5) / 3);
      if (degree >= 13 && degree <= 23) {
        sourceWkid.value = degree + 4478;
      }
      if (degree >= 25 && degree <= 45) {
        sourceWkid.value = degree + 4488;
      }
    });
  });
};

const clearDormancy = () => {
  if (wakeLock.value) {
    wakeLock.value.release();
  }
};

/**
 * 迭代原始树表达式
 * @param list
 * @param mainId
 */
const getTreeExpress = (list, mainId) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              ruleId: v.id,
              groupId: k.id
            };
            treeExpressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getTreeExpress(v.list, mainId);
    }
  });
};

/**
 * 迭代获取树的所有表达式
 * @param list
 * @param mainId
 */
const getAllFiledExpressFromOne = (list, mainId) => {
  list.forEach((v) => {
    v.fieldGroupModels.forEach((k) => {
      if (!k.ruleAttribution && k.groupScope == 2) {
        k.fieldModelList.forEach((q) => {
          if (q.attribution && q.attribution.expression && !q.attribution.isOnceRefeshExpression) {
            //代表是表达式的
            const obj = {
              mainId: mainId, //最顶级id
              parcelId: v.id, //节点实例id
              linkId: k.linkId, //关联id
              groupName: k.typeName,
              fieldModel: q,
              groupId: k.id
            };
            expressList.value.push(obj);
          }
        });
      }
    });
    if (v.list) {
      getAllFiledExpressFromOne(v.list, mainId);
    }
  });
};

/**
 * 最终通过数据组装表达式集合
 * @param item
 * @param mainId
 * @param list
 */
const getEndOneExpressData = (item, mainId, list) => {
  item.forEach((v) => {
    publicExpressList.value.forEach((k) => {
      if (v.geomArcgis && k.ruleId == v.ruleId) {
        //当前数据的ruleid和公共表达式列表相等 需要先判断有图形的才加入
        const obj = JSON.parse(JSON.stringify(k));
        obj.parcelId = v.id;
        obj.mainId = mainId;
        list.push(obj);
      }
    });
    if (v.list && v.list.length != 0) {
      getEndOneExpressData(v.list, mainId, list);
    }
  });
};

/**
 * 通过是否映射返回相应的颜色，如果没有映射返回红色
 * @param row 当前行的数据
 * @returns {string} 返回颜色
 */
const getLableColor = (row: any) => {
  let color = '#606266';
  if (row.yz === '' || row.yz === undefined) {
    //代表没有映射
    color = 'red';
  }
  return {
    color: color
  };
};

// 生命周期 onMounted
onMounted(() => {
  moduleId.value = projectStore.proModuleId;
});

/**
 * 处理树节点点击
 */
const handleTreeNodeClick = (data: TreeNode) => {
  // 更新选中的节点ID
  updateType.value = [data.id];
  updateRuleId.value = data.id;
  handleFindTreeItem([data], data.id);
  // 如果不是选择的第一级，需要找到父级的属性组  key2的时候要用
  if (data.levelNum !== 1) {
    // 查找父节点
    const findParentNode = (nodes: TreeNode[], targetId: number): TreeNode | null => {
      for (const node of nodes) {
        if (node.list && node.list.some((child) => child.id === targetId)) {
          return node;
        }
        if (node.list) {
          const found = findParentNode(node.list, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const parentNode = findParentNode(treeOptions.value, data.id);
    if (parentNode) {
      getParentAttr(treeOptions.value, parentNode.id);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
::v-deep .el-table tr {
  height: 30px !important;
}
::v-deep .el-table--medium .el-table__cell {
  padding: 2px;
}
.error-div {
  height: 400px;
  overflow: auto;
  .item {
    margin-bottom: 5px;
  }
}
.error-content {
  .flex-row {
    margin-bottom: 10px;
  }
  .error-div {
    height: 400px;
    overflow: auto;
    .item {
      margin-bottom: 5px;
    }
  }
}
.verify-content {
  max-height: 400px;
  overflow: auto;
  .flex-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
}
:deep(.el-form-item) {
  margin-bottom: 8px;
}
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0;
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.updateSHP-main {
  width: 100%;
  height: 100%;
}
.dialog-row {
  margin-bottom: 10px;
}
.dialog-hint {
  background-color: #fff6f6;
  border-left: 4px solid #ff4d4f;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .title {
    color: #ff4d4f;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    &:before {
      content: '!';
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: #ff4d4f;
      color: white;
      border-radius: 50%;
      margin-right: 8px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .content {
    color: #666;
    line-height: 1.8;
    font-size: 13px;
    padding-left: 28px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 9px;
      top: 8px;
      width: 4px;
      height: 4px;
      background: #ff4d4f;
      border-radius: 50%;
    }
  }
}
.el-table {
  // margin-top: 14px;
  :deep(.el-table__body-wrapper::-webkit-scrollbar) {
    width: 4px;
  }
  /*定义滚动条轨道 内阴影+圆角*/
  :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  /*定义滑块 内阴影+圆角*/
  :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}

.operation-area {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  gap: 20px;

  .top-area {
    display: flex;
    gap: 40px;

    .operation-item {
      display: flex;
      justify-content: space-between;
      gap: 10px;
      flex: 1;
    }
  }

  .content-area {
    display: flex;
    gap: 20px;

    .left-area {
      flex: 1;
      min-width: 0;
    }

    .right-area {
      flex: 1;
      border-left: 1px solid #dcdfe6;
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  .bottom-area {
    display: flex;
    gap: 40px;

    .operation-item {
      flex: 1;
    }
  }

  .data-list-container {
    height: 380px;
    overflow: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    width: 100%;
    .no-data {
      color: #909399;
      text-align: center;
      line-height: 350px;
    }

    .data-list {
      .data-item {
        padding: 8px 0;
        border-bottom: 1px dashed #ebeef5;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}

.tree-container {
  height: 380px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;

  :deep(.el-tree-node__content) {
    height: 32px;
  }

  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ecf5ff;
    color: #409eff;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
}

.file-div {
  margin: 10px 0;

  .file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;

    .file-input {
      position: absolute;
      width: 0;
      height: 0;
      opacity: 0;
    }

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 5px;
      color: #f1f1f1;
      border: 1px solid #409eff;
      border-radius: 4px;
      transition: all 0.3s;
      background: #409eff;
      &:hover {
        background-color: #71b2f3;
      }

      i {
        margin-right: 4px;
        font-size: 16px;
      }
    }

    .file-list {
      margin: 8px 0 0 0;
      padding: 0;
      list-style: none;

      li {
        margin-bottom: 4px;
        padding: 4px 8px;
        font-size: 14px;
        color: #606266;
        background: #f5f7fa;
        border-radius: 4px;
      }
    }
  }
}

.dialog-row {
  margin-bottom: 15px;
}
</style>
