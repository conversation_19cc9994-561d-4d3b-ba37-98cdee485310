import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import JSONbig from 'json-bigint'; // 解决超过 16 位数字精度丢失问题
import {
  AreaParams,
  ParcelParams,
  ParcelDetailParams,
  FieldParams,
  ExportParams,
  LinyeParams,
  XKParams,
  DeleteParcelParams,
  ModifyZDParams,
  ApkParams,
  GDParams,
  AsyncParams,
  AsyncFileParams,
  PCParams,
  ZDParams,
  LogParams,
  UpdateShpParams,
  OperaParams,
  FormulaParams,
  SimpleParams,
  CheckParams,
  TuopuParams,
  RetrievalParams,
  UpdateFieldParams
} from '@/api/project/types';

/**
 * 获取行政区划
 * @param code 区域代码
 * @param notTree 是否不需要树形结构
 * @returns {AxiosPromise}
 */
export function getAreaCode(code: string, notTree?: boolean): AxiosPromise<any> {
  let url = `/qjt/area/get/${code}`;
  if (notTree) {
    url = `/qjt/area/get/${code}?notTree=${notTree}`;
  }
  return request({
    url: url,
    method: 'get'
  });
}

/**
 * 根据行政区划查询宗地列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getParcelList(params: ParcelParams): AxiosPromise<any> {
  return request({
    url: 'qjt/parcel/search/parcel',
    method: 'get',
    params: params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 根据宗地id查询宗地信息
 * @param id 宗地ID
 * @param dataScope 数据范围
 * @returns {AxiosPromise}
 */
export function getParcelInfoById(id: string | number, dataScope: string | number): AxiosPromise<any> {
  return request({
    url: `qjt/parcel/search/detail/${id}/${dataScope}`,
    method: 'get',
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 根据数据字段查询宗地信息的title
 * @param params 字段参数
 * @returns {AxiosPromise}
 */
export function getFieldList(params: FieldParams): AxiosPromise<any> {
  return request({
    url: '/qjt/field/search',
    method: 'post',
    data: params
  });
}

/**
 * 导出文件报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function exportWord(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: '/qjt/project/export/word/djdcb',
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 查询林业调查列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getLinyeList(params: LinyeParams): AxiosPromise<any> {
  return request({
    url: '/qjt/linye/smallpot/search/smallSpotList',
    method: 'get',
    params: params
  });
}

/**
 * 查询林业详情
 * @param id 林业ID
 * @returns {AxiosPromise}
 */
export function getLinyeDetail(id: string | number): AxiosPromise<any> {
  return request({
    url: '/qjt/linye/multi/detail/' + id,
    method: 'get'
  });
}

/**
 * 查询像控列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getXKlist(params: XKParams): AxiosPromise<any> {
  return request({
    url: '/qjt/xiangkong/page',
    method: 'post',
    data: params
  });
}

/**
 * 查询像控详情
 * @param id 像控ID
 * @returns {AxiosPromise}
 */
export function getXKDetail(id: string | number): AxiosPromise<any> {
  return request({
    url: '/qjt/xiangkong/detail/' + id,
    method: 'get'
  });
}

/**
 * 导出像控报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function exportXKWord(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: '/qjt/xiangkong/export/word',
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 批量删除宗地
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delZDFun(params: DeleteParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/delete/1',
    method: 'post',
    data: params
  });
}

/**
 * 批量删除林业数据
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delLinyeFun(params: DeleteParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/linye/smallpot/delete/1',
    method: 'post',
    data: params
  });
}

/**
 * 修改宗地信息
 * @param dataScope 数据范围
 * @param params 修改参数
 * @returns {AxiosPromise}
 */
export function modifyZD(dataScope: string | number, params: ModifyZDParams): AxiosPromise<any> {
  return request({
    url: `/qjt/web/parcel/modify/${dataScope}`,
    method: 'post',
    data: params
  });
}

/**
 * apk版本记录
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function apkHistory(params?: ApkParams): AxiosPromise<any> {
  return request({
    url: `/qjt/version/apk/history`,
    method: 'get',
    params: params
  });
}

/**
 * 查询核查列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getGDList(params: GDParams): AxiosPromise<any> {
  return request({
    url: '/qjt/gengdiao/parcel/search/list',
    method: 'get',
    params: params
  });
}

/**
 * 查询核查详情
 * @param id 核查ID
 * @returns {AxiosPromise}
 */
export function getGDDetail(id: string | number): AxiosPromise<any> {
  return request({
    url: '/qjt/gengdiao/parcel/detail/' + id,
    method: 'get'
  });
}

/**
 * 导出查询进度条
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function findAsyncMsg(params: AsyncParams): AxiosPromise<any> {
  return request({
    url: `/export/async/findAsyncMsg?id=${params.id}`,
    method: 'post'
  });
}

/**
 * 导入查询进度条（excel）
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function findAsyncMsgUp(params: AsyncParams): AxiosPromise<any> {
  return request({
    url: `/qjt/async/findAsyncMsg?id=${params.id}`,
    method: 'post'
  });
}

/**
 * 导出公共接口  去浏览器进度
 * @param params 导出参数
 * @param downloadProgress 下载进度回调
 * @returns {AxiosPromise}
 */
export function findAsyncFileBrowser(params: AsyncFileParams, downloadProgress?: (progressEvent: any) => void): AxiosPromise<Blob> {
  return request({
    url: `/export/async/findAsyncFile`,
    method: 'post',
    responseType: 'blob', // 将文件流转成blob对象
    params: params,
    headers: {
      noErrorMsg: true
    },
    onDownloadProgress: function (progressEvent) {
      downloadProgress(progressEvent);
    }
  });
}

/**
 * 导出公共接口  不需要取浏览器进度
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function findAsyncFile(params: AsyncFileParams): AxiosPromise<Blob> {
  return request({
    url: `/tool/async/findAsyncFile`,
    method: 'post',
    responseType: 'blob', // 将文件流转成blob对象
    params: params,
    headers: {
      noErrorMsg: true
    }
  });
}

/**
 * 获取批次--同口径
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPC(params: PCParams): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/list',
    method: 'get',
    params: params
  });
}

/**
 * 获取批次--现状
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPCXz(params: PCParams): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/xz/list',
    method: 'get',
    params: params
  });
}

/**
 * 正式调取导出批次
 * @param batchName 批次名称
 * @returns {AxiosPromise}
 */
export function downloadPC(batchName: string): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/all?batchName=' + batchName,
    method: 'post'
  });
}

/**
 * 导出勘界报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function exportWordKJBG(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: '/qjt/kj/export/word/djdcb',
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 全打包导出勘界结果
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @returns {AxiosPromise}
 */
export function exportKJAll(batchName: string, folderName: string): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/all/tkj/20230512?batchName=' + batchName + '&folderName=' + folderName,
    method: 'post'
  });
}

/**
 * 全打包导出勘界结果(新)
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @param xmmc 项目名称
 * @param xz 现状标识
 * @returns {AxiosPromise}
 */
export function exportKJNewAll(batchName: string, folderName: string, xmmc: string, xz: string): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/all/tkj/20230612?batchName=' + batchName + '&folderName=' + folderName + '&xmmc=' + xmmc + '&xz=' + xz,
    method: 'post'
  });
}

/**
 * 全打包导出勘界现状结果
 * @param batchName 批次名称
 * @param folderName 文件夹名称
 * @returns {AxiosPromise}
 */
export function exportKJAllXz(batchName: string, folderName: string): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/export/all/xz/20230512?batchName=' + batchName + '&folderName=' + folderName,
    method: 'post'
  });
}

/**
 * 农转用勘界地块列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getKJList(params: ParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/parcel/search/parcel',
    method: 'get',
    params: params
  });
}

/**
 * 农转用勘界地块详情
 * @param id 地块ID
 * @param dataScope 数据范围
 * @returns {AxiosPromise}
 */
export function getKJParcelInfoById(id: string | number, dataScope: string | number): AxiosPromise<any> {
  return request({
    url: `qjt/kj/parcel/search/detail/${id}/${dataScope}`,
    method: 'get',
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 查询日志
 * @param id 日志ID
 * @returns {AxiosPromise}
 */
export function getLog(id: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/log/parcel/${id}`,
    method: 'get'
  });
}

/**
 * 根据地块id和linkID查询某个时间点的版本比较
 * @param params 比较的参数
 * @returns {AxiosPromise}
 */
export function selectFieldCompare(params: any): AxiosPromise<any> {
  return request({
    url: `/qjt/parcel/selectFieldCompare`,
    method: 'post',
    params: params
  });
}

/**
 * 根据行政区划查询征地列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getParcelListZD(params: ZDParams): AxiosPromise<any> {
  return request({
    url: 'qjt/land/search/parcel',
    method: 'get',
    params: params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 根据征地id查询宗地信息
 * @param id 征地ID
 * @param dataScope 数据范围
 * @returns {AxiosPromise}
 */
export function getLandInfoById(id: string | number, dataScope: string | number): AxiosPromise<any> {
  return request({
    url: `qjt/land/search/detail/${id}/${dataScope}`,
    method: 'get',
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 征地导出文件报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function exportWordZD(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: '/qjt/land/export/word/djdcb',
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 批量删除征地
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delZDFunZD(params: DeleteParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/land/delete/1',
    method: 'post',
    data: params
  });
}

/**
 * 通过shp更新数据
 * @param params 更新参数
 * @returns {AxiosPromise}
 */
export function updateFromShp(params: UpdateShpParams): AxiosPromise<any> {
  return request({
    url: '/qjt/web/parcel/update/byshp',
    method: 'post',
    data: params
  });
}

/**
 * 权籍通6.0版本更新数据
 * @param params 更新参数
 * @returns {AxiosPromise}
 */
export function operaParcelFromShp(params: OperaParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/operaParcelFromShp',
    method: 'post',
    data: params
  });
}

/**
 * 根据时间和属性组查询列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function selectParcelField(params: ParcelParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/selectParcelField',
    method: 'get',
    params: params
  });
}

/**
 * 单独新增关联关系
 * @param params 关联参数
 * @returns {AxiosPromise}
 */
export function insertInstance(params: OperaParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/insertInstance',
    method: 'post',
    data: params
  });
}

/**
 * 操作业务树数据，可以为多颗树
 * @param params 操作参数
 * @returns {AxiosPromise}
 */
export function operaParcel(params: OperaParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/operaParcel',
    method: 'post',
    data: params
  });
}
/**
 * 级联删除
 * @param id 删除的id
 */
export function deleteParcel(id: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/parcel/delete?id=${id}`,
    method: 'post'
  });
}
/**
 * 验证表达式正确性
 * @param params 表达式参数
 * @returns {AxiosPromise}
 */
export function verificationFM(params: FormulaParams): AxiosPromise<any> {
  return request({
    url: `/tool/formula/calculateParcel`,
    method: 'post',
    params: params
  });
}

/**
 * 验证表达式的正确性 文件上传
 * @param params 表达式参数
 * @returns {AxiosPromise}
 */
export function verificationFile(params: FormulaParams): AxiosPromise<any> {
  return request({
    url: `/export/formula/calculateParcelFile`,
    method: 'post',
    params: params
  });
}

/**
 * 数据简易提交 用于模块的问卷调查类型
 * @param params 提交参数
 * @returns {AxiosPromise}
 */
export function saveSimple(params: SimpleParams): AxiosPromise<any> {
  return request({
    url: `/qjt/simple/opera`,
    method: 'post',
    data: params
  });
}

/**
 * 导出模板问卷内容
 * @param moduleId 模块ID
 * @returns {AxiosPromise}
 */
export function downLoadQuestion(moduleId: string | number): AxiosPromise<Blob> {
  return request({
    url: `/qjt/questionnaire/downloadoneExcelFromModuleId/${moduleId}`,
    method: 'get',
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 导出固定模板 根据字段生成excel模板 适用于问卷模板
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downloadTemp(params: ExportParams): AxiosPromise<Blob> {
  return request({
    url: `/qjt/excel/export/template`,
    method: 'post',
    data: params,
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 导出数据
 * @param moduleId 模块ID
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downDefaultTemp(moduleId: string | number, params: ExportParams): AxiosPromise<any> {
  return request({
    url: `/qjt/excel/export/data/${moduleId}`,
    method: 'post',
    data: params
  });
}

/**
 * 移植5.0勘界列表接口
 * @returns {AxiosPromise}
 */
export function kjList(): AxiosPromise<any> {
  return request({
    url: `/qjt/export/getNewlist`,
    method: 'get'
  });
}

/**
 * 移植5.0勘界下载接口
 * @param params 下载参数
 * @param sepUrl 自己配置的导出地址
 * @returns {AxiosPromise}
 */
export function exportPcNew(params: ExportParams, sepUrl: string): AxiosPromise<any> {
  let url = `/export/export/kj`;
  if (sepUrl) {
    url = `/export/${sepUrl}/export/kj`;
  }
  return request({
    url: url,
    method: 'post',
    data: params
  });
}

/**
 * 转换成老勘界
 * @param parmas 参数
 * @returns {AxiosPromise}
 */
export function transitionOldKJ(parmas: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/export/addKj?id=${parmas}`,
    method: 'post'
  });
}

/**
 * 导出json
 * @param moduleId 模块ID
 * @returns {AxiosPromise}
 */
export function downLoadJson(moduleId: string | number): AxiosPromise<Blob> {
  return request({
    url: `/qjt/rule/copyJson?id=${moduleId}`,
    method: 'post',
    responseType: 'blob' // 将文件流转成blob对象
  });
}

/**
 * 获取拓扑检查日志
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getTuopuLog(params: TuopuParams): AxiosPromise<any> {
  return request({
    url: `/qjt/parcelCheck/select`,
    method: 'post',
    data: params
  });
}

/**
 * 通过属性字段映射关系修改属性组
 * @param params 修改参数
 * @param moreFlag 标识
 * @param type 类型
 * @param batchId 批次ID
 * @returns {AxiosPromise}
 */
export function updateInstance(params: SimpleParams, moreFlag: string | number, type: string, batchId: string): AxiosPromise<any> {
  return request({
    url: '/qjt/simple/updateInstance?moreFlag=' + moreFlag + '&type=' + type + '&batchId=' + batchId,
    method: 'post',
    data: params
  });
}

/**
 * 通过属性字段映射关系修改属性组
 * @param params 修改参数
 * @param moduleId 模块ID
 * @param type 类型
 * @returns {AxiosPromise}
 */
export function updateParcelFromGroup(params: SimpleParams, moduleId: string | number, type: string): AxiosPromise<any> {
  return request({
    url: '/qjt/simple/updateParcelFromGroup?moduleId=' + moduleId + '&type=' + type,
    method: 'post',
    data: params
  });
}

/**
 * 导入前检查
 * @param params 检查参数
 * @returns {AxiosPromise}
 */
export function operaParcelFromShpCheck(params: CheckParams): AxiosPromise<any> {
  return request({
    url: '/qjt/parcel/operaParcelFromShpCheck',
    method: 'post',
    data: params
  });
}

/**
 * 通过属性字段映射关系修改属性组
 * @param params 修改参数
 * @param moreFlag 标识
 * @param batchId 批次ID
 * @param size 当前数量
 * @returns {AxiosPromise}
 */
export function updateInstanceCheck(params: SimpleParams, moreFlag: string | number, batchId: string, size: number): AxiosPromise<any> {
  return request({
    url: '/qjt/simple/updateInstanceCheck?moreFlag=' + moreFlag + '&batchId=' + batchId + '&size=' + size,
    method: 'post',
    data: params
  });
}

/**
 * 测试导出 兰刚用
 * @param params 导出参数
 * @param downloadProgress 下载进度回调
 * @returns {AxiosPromise}
 */
export function findAsyncFileText(params: AsyncFileParams, downloadProgress: (progressEvent: any) => void): AxiosPromise<Blob> {
  return request({
    url: `/tool/async/findAsyncFileText`,
    method: 'post',
    responseType: 'blob', // 将文件流转成blob对象
    params: params,
    headers: {
      noErrorMsg: true
    },
    onDownloadProgress: function (progressEvent) {
      downloadProgress(progressEvent);
    }
  });
}

/**
 * 导出未匹配到的数据
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function findAsyncFileTemp(params: AsyncFileParams): AxiosPromise<Blob> {
  return request({
    url: `/tool/async/findAsyncFileTemp`,
    method: 'post',
    responseType: 'blob', // 将文件流转成blob对象
    params: params,
    headers: {
      noErrorMsg: true
    }
  });
}

/**
 * 数据回退接口
 * @param params 回退参数
 * @returns {AxiosPromise}
 */
export function retrieval(params: RetrievalParams): AxiosPromise<any> {
  return request({
    url: `/qjt/simple/retrieval?linkId=${params.linkId}&fieldName=${params.fieldName}`,
    method: 'post',
    data: params.ids
  });
}

/**
 * 界址线找回
 * @param params 找回参数
 * @returns {AxiosPromise}
 */
export function jzxRetrieval(params: RetrievalParams): AxiosPromise<any> {
  return request({
    url: `/qjt/simple/jzxRetrieval?linkId=${params.linkId}&fieldName=${params.fieldName}`,
    method: 'post',
    data: params.ids
  });
}

/**
 * 批量修改字段内容
 * @param params 修改参数
 * @returns {AxiosPromise}
 */
export function updateField(params: UpdateFieldParams): AxiosPromise<any> {
  return request({
    url: '/qjt/simple/updateFieldNew',
    method: 'post',
    data: params
  });
}

/**
 * 查询是否会后台刷新表达式 比如是否有表达式字段，是否表达式字段排序了
 * @param params 查看参数
 * @returns {AxiosPromise}
 */
export function selectIfOrder(params: UpdateFieldParams): AxiosPromise<any> {
  return request({
    url: '/tool/field/selectIfOrder',
    method: 'post',
    params: params
  });
}

/**
 * 异步上传数据 多用于excel更新、批量更新图片等操作
 * @param type 类型
 * @param batchId 批次ID
 * @returns {AxiosPromise}
 */
export function updateInstanceAsync(batchId: string, type: number, flush: boolean = true): AxiosPromise<any> {
  return request({
    url: '/qjt/simple/updateInstanceAsync?batchId=' + batchId + '&type=' + type + '&flush=' + flush,
    method: 'post'
  });
}
