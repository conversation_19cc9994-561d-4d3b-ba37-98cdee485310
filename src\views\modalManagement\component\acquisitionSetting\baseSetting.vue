<!-- 采集信息的基本设置 -->
<template>
  <div class="acquire-base-setting-main">
    <!-- 属性标题和图标 -->
    <div class="item-title">
      <div class="handle-title">
        <span class="text">基本设置</span>
      </div>
    </div>
    <div class="item-form">
      <!-- <el-link type="primary" v-show="baseSettingForm.tools == 'kanjie'" @click="">勘界配置</el-link> -->
      <el-form v-translateModal :model="baseSettingForm" :rules="baseSettingRules" ref="baseSettingRef" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="层级名称" prop="typeName">
              <el-input v-model="baseSettingForm.typeName" placeholder="请输入名称" maxlength="10" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名" prop="wordName">
              <el-input
                v-model="baseSettingForm.wordName"
                onkeyup="value = value.replace(/[^a-zA-Z0-9|_|-]/g, '')"
                placeholder="请输入英文名"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <!-- prop="aliasNameCn" -->
            <el-form-item label="层级别名">
              <div class="alias-name">
                <el-input placeholder="请输入内容" v-model="baseSettingForm.aliasName" clearable></el-input>
                <div class="arrow">
                  <el-dropdown @command="handleCommand">
                    <div style="width: 40px; height: 30px">
                      <el-icon><ArrowDown /></el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="quickly">快捷表达式</el-dropdown-item>
                        <el-dropdown-item command="custom">自定义表达式</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="层级图标" prop="iconUrl" class="item-content">
              <div class="choose-img-div" @click="chooseImg">
                <div class="choose-img">
                  <div
                    v-if="baseSettingForm.iconUrl && baseSettingForm.iconUrl.substring(baseSettingForm.iconUrl.lastIndexOf('_') + 1) == 'blob'"
                    style="margin-top: 4px"
                  >
                    <el-image
                      style="width: 20px; height: 20px; margin-right: 8px"
                      :src="`${baseUrl}${baseSettingForm.iconUrl}?token=${token}`"
                      :fit="'cover'"
                    />
                  </div>
                  <div v-else style="margin-top: 4px">
                    <svg-icon class-name="svg-item" :icon-class="baseSettingForm.iconUrl + ''" />
                  </div>
                </div>
                <div class="choose-btn" @click="chooseImg">
                  <el-icon><Setting /></el-icon>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12" v-show="checkedLevel > 1">
            <!-- <el-form-item label="重复采集">
              <el-radio-group v-model="repetition">
                <el-radio :value="1">允许</el-radio>
                <el-radio :value="2">不允许</el-radio>
              </el-radio-group>
            </el-form-item> -->
            <el-form-item label="最小数量">
              <el-input v-model="baseSettingForm.graphicalMinNum" min="1" type="number" placeholder="请输入最小数量"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="checkedLevel > 1">
            <el-form-item label="最大数量">
              <el-input v-model="baseSettingForm.graphicalMaxNum" min="1" type="number" placeholder="请输入最大数量"></el-input>
            </el-form-item>
            <!-- <el-form-item label="重复数量">
              <el-input v-model="baseSettingForm.graphicalNum" placeholder="请输入重复数量"></el-input>
            </el-form-item> -->
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="固定别名" prop="dispalyName" class="item-content">
              <el-input v-model="baseSettingForm.dispalyName" placeholder="选填" maxlength="20"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点重名" class="item-content">
              <el-select v-model="baseSettingForm.attribution.uniq" clearable placeholder="请选择" style="width: 100%">
                <el-option v-for="item in unqOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-if="
            baseSettingForm.graphicalMinNum == baseSettingForm.graphicalMaxNum &&
            baseSettingForm.graphicalMinNum != 1 &&
            baseSettingForm.levelNum != 1
          "
        >
          <el-col :span="12">
            <el-form-item label="固定设置">
              <div class="content-box">
                <div class="content-con">
                  <el-checkbox v-model="isFexedSetting"></el-checkbox>
                  <!-- @change="changeFixedSetting(true)" -->
                </div>
                <div class="content-setting" @click="handleFixedDialog">
                  <el-icon :style="{ color: isFexedSetting ? 'var(--current-color)' : '' }"><Setting /></el-icon>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 设置图标 -->
    <setting-icon
      :iconVisible="iconVisible"
      :defaultList="treeIconList"
      :iconSelected="baseSettingForm.iconUrl"
      :isShow="isShow"
      :iconName="iconName"
      @closeSettingIcon="handleCloseSettingIcon"
      @submitIcon="handleSubmitSettingIcon"
    ></setting-icon>
    <!-- 设置映射值 -->
    <!-- <group-and-owner-field
      :moduleId="moduleId"
      :ysTableVisible="ysTableVisible"
      :fieldInfoItem="baseSettingForm"
      @closeYs="handleCloseYs"
      @submitField="handleSubmitField"
      ></group-and-owner-field> -->
    <!-- 快捷表达式 -->

    <fastExpression
      :mapFieldDialog="ysTableVisible"
      @submitFastExp="handleSubmitField"
      @handleCloseFast="handleCloseYs"
      :moduleId="moduleId"
      :isYsName="isYsName"
    ></fastExpression>
    <!-- 设置自定义表达式 -->
    <!-- 打开公式编辑的弹框 -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="checkedTreeMsg.aliasName"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="'#field'"
      :isYSCJ="true"
    ></formula-editing-dialog>
    <!-- 固定设置弹窗 -->
    <el-dialog title="固定设置" v-model="fixedSettingDialog" width="30%" :close-on-click-modal="false" :before-close="handleCloseSettingDialog">
      <el-table :data="fixedSettingList" style="width: 100%" border>
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="名称">
          <template v-slot="scope">
            <el-input placeholder="请输入名称" v-model="scope.row.name" maxlength="30"></el-input>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="fixedSettingDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitFixedSetting">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import settingIcon from '../../SettingIcon/index.vue';
import authImg from '@/components/authImg/index.vue';
import groupAndOwnerField from '../groupAndOwnerField/index.vue';
import fastExpression from '@/components/fastExpression/index.vue';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import { selectFieldById } from '@/api/modal/index';
import treeIconList from '../../SettingIcon/treeIcon.json';
import SvgIcon from '../../svgIcon/index.vue';
import { Setting, ArrowDown } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getToken } from '@/utils/auth';

const modalStore = useModalStore();
const userStore = useUserStore();
const token = getToken();

// 定义 props
const props = defineProps<{
  checkedLevel: number;
  checkedNodeId: number;
  checkedTreeMsg: Record<string, any>;
}>();

// 定义响应式数据
const isYsName = ref(true);
const isShow = ref(true);
const treeIconListRef = ref(treeIconList);
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const isFexedSetting = ref(false); //是否是固定设置

interface Attribution {
  uniq: string | null;
  fixedSetting?: Array<{ name: string }>;
}

interface BaseSettingForm {
  attribution: Attribution;
  typeName?: string;
  wordName?: string;
  aliasName?: string;
  iconUrl?: string;
  graphicalMinNum?: number;
  graphicalMaxNum?: number;
  dispalyName?: string;
  fixedSetting?: boolean;
}

const baseSettingForm = ref<BaseSettingForm>({
  attribution: {
    uniq: null
  }
});

const baseSettingRef = ref<FormInstance>(null);

// 校验规则
const validateAliasNameCn = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请选择层级别名'));
  } else {
    callback();
  }
};

const validateTypeName = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请输入层级名称'));
  } else {
    callback();
  }
};

const validateIconUrl = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请选择层级图标'));
  } else {
    callback();
  }
};

const validateWordName = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (value === '') {
    callback(new Error('请输入英文名'));
  } else {
    const pattern = /^[a-zA-Z0-9_-]+$/;
    if (!pattern.test(value)) {
      callback(new Error('只能输入英文、数字、下划线和连字符的组合'));
    }
    callback();
  }
};

const baseSettingRules = {
  typeName: [{ required: true, validator: validateTypeName, trigger: 'blur' }],
  wordName: [{ required: true, validator: validateWordName, trigger: ['blur', 'change'] }],
  aliasNameCn: [{ required: true, validator: validateAliasNameCn, trigger: 'change' }],
  iconUrl: [{ required: true, validator: validateIconUrl, trigger: 'change' }]
};

const iconVisible = ref(false);
const repetition = ref(1);
const ysTableVisible = ref(false);
const iconName = ref('树图标自定义照片');
const fixedSettingDialog = ref(false);
const fixedSettingList = ref<Array<{ name: string }>>([]);
const formulaVisible = ref(false);

const unqOption = [
  {
    label: '整棵树不允许重名',
    value: 'Tree'
  },
  {
    label: '兄弟节点不允许重名',
    value: 'Current'
  }
];

const emit = defineEmits(['update:checkedTreeMsg']);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);

// 监听 checkedTreeMsg 变化
// watch(
//   () => props.checkedTreeMsg,
//   (val) => {
//     Object.assign(baseSettingForm, val);
//     if (val.attribution && val.attribution.fixedSetting && val.attribution.fixedSetting.length > 0) {
//       baseSettingForm.fixedSetting = true;
//       fixedSettingList.value = val.attribution.fixedSetting;
//     }
//   },
//   { immediate: true, deep: true }
// );

watch(
  () => baseSettingForm.value.iconUrl,
  (val) => {
    if (val && baseSettingRef.value) {
      (baseSettingRef.value as any).clearValidate('iconUrl');
    }
  }
);

// 方法
const handleCommand = (command: string) => {
  if (command === 'custom') {
    modalStore.setIsHasAcquition(false);
    formulaVisible.value = true;
    modalStore.setIsAllGroup(true);
  } else {
    ysTableVisible.value = true;
  }
};

const initData = (val: any) => {
  // Object.assign(baseSettingForm, val);
  baseSettingForm.value = val;
  if (
    baseSettingForm.value.attribution &&
    baseSettingForm.value.attribution.fixedSetting &&
    baseSettingForm.value.attribution.fixedSetting.length > 0
  ) {
    isFexedSetting.value = true;
  }
};

const handleCloseFormulation = () => {
  formulaVisible.value = false;
};

const handleSubmitFormulation = (expression: string) => {
  // props.checkedTreeMsg.aliasName = expression;
  baseSettingForm.value.aliasName = expression;
  formulaVisible.value = false;
};

const getFieldById = (id: number) => {
  selectFieldById({ id }).then((res) => {
    if (res.code === 200 && res.data !== null) {
      baseSettingForm.value.aliasNameCn = res.data.fieldCn;
    }
  });
};

const handleCloseYs = () => {
  ysTableVisible.value = false;
};

const handleSubmitField = (fieldItem: any) => {
  // props.checkedTreeMsg.aliasName = fieldItem;
  baseSettingForm.value.aliasName = fieldItem;
  ysTableVisible.value = false;
};

const chooseImg = () => {
  iconVisible.value = true;
};

const handleCloseSettingIcon = () => {
  iconVisible.value = false;
};

const handleSubmitSettingIcon = (url: string) => {
  props.checkedTreeMsg.iconUrl = url;
};

const handleValidateForm = async () => {
  let flag = false;
  if (baseSettingRef.value) {
    await baseSettingRef.value.validate((valid, fields) => {
      if (valid) {
        flag = valid;
      } else {
      }
    });
  }
  return flag;
};

const changeFixedSetting = () => {
  // 如果固定设置没有内容的话，需要把固定设置的内容给初始化
  if (!baseSettingForm.value.attribution.fixedSetting) {
    const fixedSetting: Array<{ name: string }> = [];
    if (baseSettingForm.value.graphicalMinNum) {
      for (let index = 0; index < baseSettingForm.value.graphicalMinNum; index++) {
        fixedSetting.push({ name: baseSettingForm.value.typeName || '' });
      }
    }
    fixedSettingList.value = fixedSetting;
    baseSettingForm.value.attribution.fixedSetting = fixedSetting;
  } else {
    fixedSettingList.value = baseSettingForm.value.attribution.fixedSetting;
  }
};

const handleFixedDialog = () => {
  if (baseSettingForm.value.attribution.fixedSetting) {
    fixedSettingDialog.value = true;
    changeFixedSetting();
  }
};

const handleCloseSettingDialog = () => {
  fixedSettingDialog.value = false;
};

const submitFixedSetting = () => {
  if (baseSettingForm.value.attribution) {
    baseSettingForm.value.attribution.fixedSetting = fixedSettingList.value;
  }
  fixedSettingDialog.value = false;
};
defineExpose({
  handleValidateForm,
  initData
});
</script>

<style lang="scss" scoped>
:deep(.el-form--label-top) .el-form-item__label {
  padding: 0px;
}
.group-item {
  margin-bottom: 28px;
  border: 1px solid #ededed;
  font-size: 14px;
  .group-title {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    background: #f8f8f8;
    justify-content: space-between;
    cursor: pointer;
    .title {
      color: rgba(0, 0, 0, 0.6);
      font-size: 14px;
      font-weight: 600;
    }
    .icon {
      i {
        font-size: 18px;
        color: #999;
      }
    }
  }
  .group-content {
    padding: 16px;
    .flex-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .flex-one {
        flex: 100%;
        margin-right: 16px;
      }
      .flex-item {
        color: #161d26;
        flex: 0 0 calc(33.33% - 16px); /* 25% width minus the gap */
        margin-right: 16px; /* Right margin for the gap */
        // margin-bottom: 16px; /* Bottom margin for the gap */
        box-sizing: border-box; /* Include padding and border in the width */
        padding: 6px;
      }
      .flex-row {
        width: calc(100% - 16px);
      }
    }
  }
}
.content-box {
  width: 100%;
  height: 36px;
  border: 1px solid #e6e9ee;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .content-con {
    margin-left: 8px;
  }
  .content-setting {
    width: 40px;
    height: 32px;
    background: #f6f7f8;
    border-radius: 0px 6px 6px 0px;
    opacity: 1;
    border: 1px solid #e6e9ee;
    text-align: center;
    color: #333;
    cursor: pointer;
  }
}
.alias-name {
  display: flex;
  width: 100%;
  // margin: 0 8px;

  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  height: 36px;
  line-height: 36px;
  // border-radius: 6px;
  // border: 1px solid #E6E9EE;
  .el-input {
    :deep(&) {
      .el-input__inner {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
      }
    }
  }
  .arrow {
    width: 40px;
    height: 32px;
    background: #f6f7f8;
    opacity: 1;
    border: 1px solid #e6e9ee;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    // text-align: center;
    cursor: pointer;
    // color: #9a9a9a;
    // pointer-events: none;
    padding-left: 8px;
    padding-top: 8px;
  }
}

.acquire-base-setting-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;
    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }
  &:hover {
    border: 1px solid var(--current-color);
    color: var(--current-color);
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .item-form {
    margin: 24px 16px 0;
    .el-form {
      .item-content {
        flex: 1;
        .choose-img-div {
          width: 100%;
          display: flex;
          flex-direction: row;
          // align-items: center;
          justify-content: space-between;
          height: 36px;
          border: 1px solid #dcdfe6;
          border-radius: 6px;
          cursor: pointer;
          .choose-img {
            width: 20px;
            height: 100%;
            margin-left: 12px;
            .svg-item {
              width: 20px;
              height: 20px;
              color: #333;
            }
          }
          .choose-btn {
            width: 40px;
            height: 100%;
            border-left: 1px solid #e6e9ee;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            background: #f6f7f8;
            border-radius: 0px 6px 6px 0px;
          }
        }
      }
    }
  }
}
:deep(.el-form-item__label) {
  color: #161d26;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-size: 14px;
}
.kj-main {
  overflow: auto;
  border: #dcdfe6 solid 1px;
  border-radius: 4px;
  padding: 16px;
  .kj-item-content {
    .kj-group {
      margin-bottom: 10px;
      border: 1px solid #ededed;
      .group-title {
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0px 16px;
        background: #f8f8f8;
        justify-content: space-between;
        cursor: pointer;
        .title {
          color: rgba(0, 0, 0, 0.6);
          font-size: 12px;
          font-weight: 600;
        }
        .icon {
          i {
            font-size: 18px;
            color: #999;
          }
        }
      }
      .group-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;
        padding: 8px;
        .item {
          display: flex;
          flex-direction: row;
          align-items: center;
          .label {
            width: 100px;
            text-align: right;
            margin-right: 5px;
          }
          .content {
            flex: 1;
          }
        }
      }
      .spe-item {
        padding: 0px 8px 8px 8px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .label {
          width: 100px;
          text-align: right;
          margin-right: 5px;
        }
        .content {
          flex: 1;
        }
      }
    }
  }
}
/*滚动条样式*/
.kj-main::-webkit-scrollbar {
  width: 4px;
}
.kj-main::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.kj-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
