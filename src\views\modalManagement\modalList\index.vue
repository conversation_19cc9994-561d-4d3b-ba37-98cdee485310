<template>
  <div class="modal-list-main">
    <div class="modal-list-title">
      <div
        @click="handleunPublist"
        :style="{
          color: isUnpublish ? 'var(--current-color)' : '#8291a9',
          'background-color': isUnpublish ? '#fff' : '#f6f7f8',
          'font-weight': isUnpublish ? '600' : '400'
        }"
        class="item"
      >
        新建模块{{ `(${unPublishList.length})` }}
      </div>
      <div
        style="margin: 8px"
        @click="handleTestPublish"
        class="item"
        :style="{
          color: isTestpublish ? 'var(--current-color)' : '#8291a9',
          'background-color': isTestpublish ? '#fff' : '#f6f7f8'
        }"
      >
        测试模块{{ `(${publishTestList.length})` }}
      </div>
      <div
        @click="handlePublist"
        :style="{
          color: isPublish ? 'var(--current-color)' : '#8291a9',
          'background-color': isPublish ? '#fff' : '#f6f7f8',
          'font-weight': isPublish ? '600' : '400'
        }"
        class="item"
      >
        已发布模块{{ `(${publishList.length})` }}
      </div>
    </div>
    <!-- 已发布组件 -->
    <div v-show="isPublish" class="modal-publish">
      <div>
        <modal-item
          v-for="item in publishList"
          :key="item.id"
          :type="1"
          :currentItem="item"
          :isTestpublish="false"
          :isUnpublish="false"
          :isPublish="isPublish"
          @updateList="getList"
        ></modal-item>
      </div>
    </div>
    <!-- 测试组件 -->
    <div v-show="isTestpublish" class="modal-publish">
      <div class="publish-scoroll">
        <modal-item
          v-for="item in publishTestList"
          :key="item.id"
          :type="2"
          :currentItem="item"
          :isUnpublish="false"
          :isPublish="false"
          :isTestpublish="isTestpublish"
          @updateList="getList"
        ></modal-item>
      </div>
    </div>
    <!--- 未发布组件--- ——————:style="{height: scrollerHeight,overflow:'auto'}" -->
    <div v-show="isUnpublish" class="modal-publish">
      <el-button style="margin: 4px" type="primary" size="small" @click="handleAddModal">新建模块</el-button>
      <div>
        <modal-item
          v-for="item in unPublishList"
          :key="item.id"
          :type="0"
          :currentItem="item"
          :isUnpublish="isUnpublish"
          :isPublish="false"
          :isTestpublish="false"
          @updateList="getList"
        ></modal-item>
      </div>
    </div>
    <!-- 新建模块前类型设置 -->
    <el-dialog title="新建模块" v-model="modelTypeDialog" :close-on-click-modal="false" width="580px" @closed="handleClosed">
      <div style="margin-bottom: 10px">请选择新建模块类型：</div>
      <div>
        <el-radio-group v-model="type" @change="handleChangeType">
          <el-radio :value="1">地理信息类调查</el-radio>
          <el-radio :value="2">问卷调查</el-radio>
        </el-radio-group>
      </div>
      <div style="margin: 24px 0 8px; font-size: 14px; font-weight: 600">模块库：</div>
      <el-row class="modal-item-contianer">
        <el-col :span="12" v-for="item in defaultList" :key="item.id">
          <div
            class="row-left"
            @click="handleDefaultModel(item)"
            :style="{
              border: item.id == itemId ? '1px solid var(--current-color)' : '1px solid #f5f6f7',
              'background-color': isPay ? '#fff9ef' : ''
            }"
          >
            <div class="left-icon">
              <div v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                <el-image style="width: 44px; height: 44px; margin-right: 8px" :src="`${baseUrl}${item.iconUrl}?att=1`" :fit="fit" />
              </div>
              <div v-else>
                <svg-icon class-name="svg-item" :icon-class="item.iconUrl" />
              </div>
            </div>
            <div class="left-module">
              <div class="module-info">
                <div class="title">
                  <div
                    class="text"
                    :title="item.moduleName"
                    :style="{
                      'text-decoration': item.status == -1 ? 'line-through' : '',
                      color: item.status == -1 ? '#8291a9' : '#161d26'
                    }"
                  >
                    {{ item.moduleName }}
                  </div>
                </div>
                <div class="remark" :title="item.remark">{{ item.remark }}</div>
              </div>
            </div>
            <div class="pay-icon" v-if="isPay">付费</div>
          </div>
        </el-col>
      </el-row>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClosed">取 消</el-button>
          <el-button type="primary" @click="submitModelType">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { ref, computed, onMounted, onActivated } from 'vue';
import { useUserStore } from '@/store/modules/user';
import modalItem from './modalItem.vue';
import { getModuleList, selectDefalutList, addDefaultModule } from '@/api/modal/index.js';
import { useRouter, useRoute } from 'vue-router';
import { useModalStore } from '@/store/modules/modal';
const router = useRouter();
const route = useRoute();
const store = useUserStore();
const modalStore = useModalStore();
const props = defineProps<{
  isExpired: number;
}>();
const fit = 'cover';

// 定义响应式数据
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/downloadone/');
const publishList = ref<any[]>([]);
const unPublishList = ref<any[]>([]);
const publishTestList = ref<any[]>([]);
const isPublish = ref(false);
const isUnpublish = ref(true);
const isTestpublish = ref(false);
const modelTypeDialog = ref(false);
const type = ref(1);
const defaultList = ref<any[]>([]);
const itemId = ref(0);
const isPay = ref(false);

// 计算属性
const user = computed(() => store['user']);
const scrollerHeight = computed(() => {
  // return (window.innerHeight - 250) + 'px';
  return undefined;
});

// 初始化数据
const init = async () => {
  await getPublishList();
  await getUnPublishList();
  await getPublishTestList();
  await getDefalutList();
};

// 新建模块
const handleAddModal = () => {
  if (props.isExpired === 2) {
    // 账户过期
    ElMessageBox.alert('<span style="color:red">您的账号已过期，请续费后操作！！！</span>', '过期提示', {
      dangerouslyUseHTMLString: true
    });
    return;
  }
  modelTypeDialog.value = true;
};

// 展示发布内容或者未发布内容
const handlePublist = async () => {
  isPublish.value = true;
  isUnpublish.value = false;
  isTestpublish.value = false;
  await getPublishList();
};

const handleunPublist = async () => {
  isPublish.value = false;
  isUnpublish.value = true;
  isTestpublish.value = false;
  await getUnPublishList();
};

// 测试发布模块
const handleTestPublish = () => {
  isTestpublish.value = true;
  isPublish.value = false;
  isUnpublish.value = false;
};

// 获取发布的模块数据
const getPublishList = async () => {
  publishList.value = [];
  // 0  未发布  1发布 -1 失效数据
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  let cId = '';
  if (companyId && companyId !== undefined && companyId !== null) {
    cId = companyId as string;
  } else {
    cId = user.value.companyId;
  }
  await getModuleList(['1', '-1'], cId).then((res) => {
    if (res.code === 200) {
      if (res.data && res.data.length > 0) {
        publishList.value = res.data
          .sort((prve: any, next: any) => {
            return next.createTime - prve.createTime;
          })
          .sort((prve: any, next: any) => {
            return next.status - prve.status;
          });
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 获取新建数据而未发布的数据
const getUnPublishList = async () => {
  unPublishList.value = [];
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  let cId = '';
  if (companyId && companyId !== undefined && companyId !== null) {
    cId = companyId as string;
  } else {
    cId = user.value.companyId;
  }
  await getModuleList([0], cId).then((res) => {
    if (res.code === 200) {
      if (res.data && res.data.length > 0) {
        unPublishList.value = res.data.sort((prve: any, next: any) => {
          return next.createTime - prve.createTime;
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 获取测试数据
const getPublishTestList = async () => {
  publishTestList.value = [];
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  let cId = '';
  if (companyId && companyId !== undefined && companyId !== null) {
    cId = companyId as string;
  } else {
    cId = user.value.companyId;
  }
  await getModuleList([8, 9], cId).then((res) => {
    if (res.code === 200) {
      if (res.data && res.data.length > 0) {
        publishTestList.value = res.data.sort((prve: any, next: any) => {
          return next.createTime - prve.createTime;
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 子组件操作数据之后，更新父组件的列表
const getList = async () => {
  await getPublishList();
  await getUnPublishList();
  await getPublishTestList();
};

// 提交模块类型
const submitModelType = () => {
  if (type.value === undefined && itemId.value === 0) {
    ElMessage.error('请选择新建模块类型或模块库！');
    return false;
  }
  modelTypeDialog.value = false;
  // 新建模块 设置规则id 设置为0
  modalStore.setRuleId(0);
  // 新建模块id  设置模块id 设置为0
  modalStore.setModuleId(0);
  // 设置组id
  modalStore.setGroupId(0);
  sessionStorage.removeItem('groupItem');
  if (type.value === 1) {
    // 地理信息类调查
    router.push({
      path: '/project/addModal',
      query: {
        companyId: route.query.companyId
      }
    });
  } else if (type.value === 2) {
    // 问卷调查
    router.push({
      path: '/project/wenjuan',
      query: {
        companyId: route.query.companyId
      }
    });
  } else {
    const params = {
      id: itemId.value
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = router.currentRoute.value.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      params.companyId = companyId;
    }
    addDefaultModule(params).then((res) => {
      if (res.code === 200) {
        ElMessage.success('添加成功');
        getUnPublishList();
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 获取默认模块列表
const getDefalutList = async () => {
  await selectDefalutList().then((res) => {
    if (res.code === 200) {
      defaultList.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 处理当前选中的模块
const handleDefaultModel = (item: any) => {
  type.value = undefined;
  if (itemId.value === item.id) {
    itemId.value = 0;
  } else {
    itemId.value = item.id;
  }
};

const handleChangeType = (val: number) => {
  itemId.value = 0;
};

// 关闭弹框
const handleClosed = () => {
  type.value = 1;
  itemId.value = 0;
  modelTypeDialog.value = false;
};

onMounted(() => {
  init();
});
onActivated(() => {
  init();
  // 需要重置选择的结构树
  modalStore.setNodeTree([]);
});

// 导出需要在模板中使用的数据和方法
defineExpose({
  handleAddModal,
  handlePublist,
  handleunPublist,
  handleTestPublish,
  getList,
  submitModelType,
  handleDefaultModel,
  handleChangeType,
  handleClosed,
  getUnPublishList,
  init,
  baseUrl,
  publishList,
  unPublishList,
  publishTestList,
  isPublish,
  isUnpublish,
  isTestpublish,
  modelTypeDialog,
  type,
  defaultList,
  itemId,
  isPay,
  user,
  scrollerHeight
});
</script>

<style lang="scss" scoped>
:deep(.publish-scoroll)::-webkit-scrollbar {
  width: 4px;
}
:deep(.publish-scoroll)::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
:deep(.publish-scoroll)::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.modal-list-main {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .modal-list-title {
    display: flex;
    align-content: center;
    align-items: center;
    justify-items: center;
    height: 40px;
    line-height: 40px;
    background: #f6f7f8;
    border-radius: 0px 8px 8px 0px;
    .item {
      width: auto;
      height: 40px;
      background: #ffffff;
      padding: 0 16px;
      font-size: 14px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 400;
      cursor: pointer;
    }
  }
  .modal-publish {
    height: calc(100vh - 250px);
    overflow: auto;
    // border-bottom:1px solid #bbbbbb;
  }
}

.modal-publish::-webkit-scrollbar {
  width: 4px;
}
.modal-publish::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.modal-publish::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.modal-item-contianer {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  .row-left {
    display: flex;
    // width: 70%;
    align-items: center;
    border-radius: 6px;
    margin-right: 8px;
    margin-bottom: 10px;
    position: relative;
    cursor: pointer;

    .left-icon {
      // width: 10%;
      min-width: 48px;
      // padding: 8px;
      text-align: left;
      margin-left: 10px;
      position: relative;
      .svg-item {
        width: 44px;
        height: 44px;
      }
      .modal-icon-svg {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 0px;
      }
      .modal-icon-pic {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 8px;
      }
    }
    .left-module {
      width: 30%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-info {
        // width: 35%;
        min-width: 156px;
        .title {
          display: flex;
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 600;
          min-width: 156px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .stop {
            width: 48px;
            height: 20px;
            background: rgba(255, 61, 87, 0.1);
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #ff3d57;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .defalut {
            width: 60px;
            height: 20px;
            background: #e6ebf5;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #8291a9;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing {
            width: 48px;
            height: 20px;
            background: #f2752157;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #e53e07af;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing-end {
            width: 60px;
            height: 20px;
            background: #adffbf;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #089145;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
        }
        .remark {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 12px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .left-span {
      width: 20%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-people {
        width: 25%;
        min-width: 156px;
        .creater {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .people {
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &:hover {
      background-color: #f6f7f8;
      border-radius: 6px;
      // margin-top: 4px;
    }
    .pay-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 25px;
      background: linear-gradient(180deg, #ffa509 0%, #f1890a 100%);
      border-top-right-radius: 50%;
      border-bottom-left-radius: 50%;
      object-fit: cover;
      color: #fff;
      font-weight: 500;
      letter-spacing: 1px;
      line-height: 25px;
    }
  }
}
</style>
