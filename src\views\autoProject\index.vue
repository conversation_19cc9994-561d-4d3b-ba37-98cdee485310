<!--  -->
<template>
  <container-card>
    <defaultIndex
      v-if="modalType === 1"
      :toolType="toolType"
      :kjExportUrl="kjExportUrl"
      ref="defaultIndexRef"
      :isIfrem="isIfrem"
      :ruleIds="ruleIds"
      :isAZF="isAZF"
    />
    <modalQuestion v-if="modalType === 2" class="question" :isIfrem="isIfrem" />
  </container-card>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
// 组件部分
import defaultIndex from './defaultIndex.vue';
import modalQuestion from './modalQuestion/index.vue';
import { selectModuleById as selectModuleByIdApi, selectRulesMore } from '@/api/modal';
import { useProjectStore } from '@/store/modules/project';

interface Props {
  defaultMoudleId?: number;
  isIfrem?: boolean;
  isAZF?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  defaultMoudleId: undefined,
  isIfrem: false,
  isAZF: false
});

const route = useRoute();
const projectStore = useProjectStore();

const moduleId = ref<number | undefined>(undefined);
const modalType = ref<number>(0);
const toolType = ref<number>(0);
const ruleIds = ref();

const defaultIndexRef = ref<InstanceType<typeof defaultIndex> | null>(null);
const hasMounted = ref(false);
const kjExportUrl = ref(''); //勘界自主配置的url

// 获取模块信息
const selectModuleById = async () => {
  const path = route.path;
  const index = path.indexOf('@');
  moduleId.value = Number(path.substring(index + 1, path.length));

  if (props.defaultMoudleId) {
    // 这个的作用是我们的平台嵌入其他平台的iframe
    moduleId.value = props.defaultMoudleId;
  }

  if (props.isAZF) {
    //这个是拆迁房选择安置房嵌入的网页
    moduleId.value = -1;
  }

  if (moduleId.value === -1) {
    //代表是特殊的 用于房源销控
    try {
      const res = await selectRulesMore({ typeName: '安置房小区名称' });
      if (res.code === 200) {
        modalType.value = 1;
        toolType.value = 0;
        ruleIds.value = res.data.map((v: any) => v.id);
        moduleId.value = res.data[0].moduleId;
        projectStore.setModuleId(moduleId.value);
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('获取规则失败:', error);
    }
  } else {
    projectStore.setModuleId(moduleId.value);
    try {
      const res = await selectModuleByIdApi({ id: moduleId.value });
      if (res.code === 200) {
        if (res?.data) {
          modalType.value = res.data.type; //1是图层 2是问卷
          toolType.value = res.data.toolType; //0常规 1勘界
          kjExportUrl.value = res.data.url || ''; //勘界自主配置的导出url
        } else {
          ElMessage.error('获取不到数据');
        }
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('获取模块详情失败:', error);
    }
  }
};

onMounted(() => {
  selectModuleById();
  hasMounted.value = true;
});

onActivated(() => {
  if (!hasMounted.value) {
    selectModuleById();
    hasMounted.value = true;
  }
});

onDeactivated(() => {
  hasMounted.value = false;
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  .card {
    width: 100%;
    height: 100%;
  }
  :deep(.el-card__body) {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  .question {
    background: #fff;
    width: 100%;
    height: 100%;
  }
}
</style>
