<!-- 流程任务数据解锁管理 -->
<template>
  <div>
    <el-dialog
      title="流程任务数据解锁管理"
      v-model="flowTaskDailogCopy"
      width="680px"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleCloseFlowTask"
    >
      <el-table :data="taskList" v-loading="loading" style="width: 100%" max-height="400" v-show="active == 1" border>
        <el-table-column label="任务名称" prop="name"></el-table-column>
        <el-table-column label="任务人员" prop="receiverNames"></el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-link type="primary" @click="chooseTask(scope.row.id)">选择</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!-- 选择任务后对应的数据 -->
      <div v-show="active == 2">
        <el-link type="info" @click="goBack" style="margin-bottom: 10px">返回</el-link>
        <el-table
          :data="taskParcelModels"
          style="width: 100%"
          max-height="400"
          ref="taskParcelRef"
          @row-click="handleRowClick"
          @expand-change="handleExpandChange"
          border
        >
          <el-table-column type="expand">
            <template #default="props">
              <el-tree :data="props.row.stepList" :props="defaultProps" default-expand-all :expand-on-click-node="false" node-key="id">
                <template #default="{ data }">
                  <span class="custom-tree-node">
                    <span>{{ data.typeName }}（{{ getStepStatusName(data.stepStatus) }}）</span>
                    <span>
                      <el-button v-show="data.stepStatus == 2" @click="() => removeLock(data, props.row)"> 解锁 </el-button>
                    </span>
                  </span>
                </template>
              </el-tree>
            </template>
          </el-table-column>
          <el-table-column label="数据名称" prop="parcelName"></el-table-column>
          <el-table-column label="创建人" prop="custName"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getSearchTask, getTaskDetail, getSelectStep, taskStepUpdate } from '@/api/task';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();

// ---Props---
interface Props {
  // 打开弹框
  flowTaskDailog: boolean;
  // 模块id
  moduleId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  flowTaskDailog: false,
  moduleId: ''
});
const flowTaskDailogCopy = computed(() => props.flowTaskDailog);

// --- watch---
watch(
  flowTaskDailogCopy,
  (newVal) => {
    if (newVal) {
      getTask();
    }
  },
  { deep: true }
);

// ---定义变量---
const taskList = ref([]); //任务列表
const loading = ref(true); // 遮罩层
const active = ref(1); // 1显示任务列表 2显示对应任务的数据
interface TaskParcelModel {
  parcelId: string | number;
  parcelName?: string;
  custName?: string;
  stepList: any[];
  [key: string]: any;
}
const taskParcelModels = ref<TaskParcelModel[]>([]); // 任务数据
const nowChooseTaskId = ref(null); // 当前选中的任务id
const defaultProps = reactive({
  children: 'list',
  label: 'typeName'
});
const nowChooseData = ref<TaskParcelModel | null>(null); // 当前选择的宗地数据
const taskParcelRef = ref<{ toggleRowExpansion: (row: any) => void } | null>(null); // 任务数据表格
// ---定义emit---
const emit = defineEmits<{
  (e: 'closeFlowTask'): void;
}>();

// ---定义方法---
const getStepStatusName = (val: number) => {
  let name = '';
  if (val == 0) {
    name = '尚未上传';
  } else if (val == 1) {
    name = '上传中';
  } else if (val == 2) {
    name = '锁定';
  } else if (val == -1) {
    name = '无权限';
  }
  return name;
};
const handleCloseFlowTask = () => {
  active.value = 1;
  emit('closeFlowTask');
};

const getTask = () => {
  loading.value = true;
  const parmas = {
    pageType: 3,
    pageSize: 1000,
    pageNum: 1,
    moduleId: props.moduleId,
    flowType: 1
  };
  getSearchTask(parmas, 1).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
      loading.value = false;
    } else {
      ElMessage.error(res.msg);
      loading.value = false;
    }
  });
};

const chooseTask = (id: any) => {
  nowChooseTaskId.value = id;
  // 根据任务id 查询任务详情得到任务数据信息
  getTaskDetail(id).then((res) => {
    if (res.code == 200) {
      active.value = 2;
      taskParcelModels.value = res.data.taskParcelModels;
      taskParcelModels.value.forEach((v) => {
        v.stepList = [];
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 返回任务列表
 */
const goBack = () => {
  active.value = 1;
};

/**
 *
 * @param row
 * @param index
 * @param e
 */
const getStepData = async (row: any, index: any, e: any) => {
  if (row.stepList.length == 0) {
    const parmas = {
      mainId: row.parcelId,
      taskId: nowChooseTaskId.value
    };
    // 先查询步骤列表
    const res = await getSelectStep(parmas);
    if (res.code == 200) {
      row.stepList = res.data;
      return true;
    } else {
      ElMessage.error(res.msg);
      return false;
    }
  }
  return true;
};

const handleExpandChange = async (row: any, expandedRows: any[]) => {
  if (expandedRows.includes(row)) {
    await getStepData(row, expandedRows, null);
  }
};

const handleRowClick = async (row: any, index: any, e: any) => {
  nowChooseData.value = row;
  const success = await getStepData(row, index, e);
  if (success && taskParcelRef.value) {
    taskParcelRef.value.toggleRowExpansion(row);
  }
};

/**
 * 返回是否是只读 负责人不是登录人就是只读的
 * @param row
 * @returns
 */
const getDisable = (row: any) => {
  let flg = true;
  const nowUserId = userStore.user['userId'];
  if (row.chargesAttribution && row.chargesAttribution.list.some((obj: any) => obj.userId == nowUserId)) {
    // 找到了就允许解除
    flg = false;
  }
  return flg;
};
/**
 * 解除锁定
 * @param row
 * @param obj
 */
const removeLock = (row: any, obj: any) => {
  ElMessageBox.confirm('确定要解锁该步骤吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const list = [
        {
          parcelId: obj.parcelId,
          status: 1,
          stepId: row.id,
          taskId: row.taskId
        }
      ];
      taskStepUpdate(list).then((res) => {
        if (res.code == 200) {
          if (nowChooseData.value) {
            const parmas = {
              mainId: nowChooseData.value.parcelId,
              taskId: nowChooseTaskId.value
            };
            // 先查询步骤列表
            getSelectStep(parmas).then((res) => {
              if (res.code == 200 && nowChooseData.value) {
                nowChooseData.value.stepList = res.data;
                ElMessage({
                  type: 'success',
                  message: '操作成功!'
                });
              } else {
                ElMessage.error(res.msg);
              }
            });
          }
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};
</script>
<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
