import { defineStore } from 'pinia';

// 类型定义
interface ProcessCondition {
  formId: number | null | undefined;
  [key: string]: any;
}

interface FormItem {
  [key: string]: any;
}

interface FlowState {
  processConditions: ProcessCondition[];
  formItemList: FormItem[];
}

// 创建 store
export const useFlowStore = defineStore('flow', {
  state: (): FlowState => ({
    processConditions: [],
    formItemList: []
  }),

  actions: {
    // 检查是否存在条件
    hasCondition(formId: number | null | undefined, needIndex = false): number | boolean {
      const index = this.processConditions.findIndex((d) => d.formId === formId);
      return needIndex ? index : index > -1;
    },

    // 初始化流程条件
    initPConditions(data: ProcessCondition[]): void {
      this.processConditions = data;
    },

    // 添加流程条件
    addPCondition(data: ProcessCondition): void {
      if (data.formId === null || data.formId === undefined) return;
      if (!this.hasCondition(data.formId)) {
        this.processConditions.unshift(data);
      }
    },

    // 删除流程条件
    delPCondition(formId: number | null | undefined): void {
      if (formId === null || formId === undefined) return;
      const index = this.hasCondition(formId, true) as number;
      if (index > -1) {
        this.processConditions.splice(index, 1);
      }
    },

    // 清除所有条件
    clearPCondition(): void {
      this.processConditions = [];
    },

    // 更新表单项目列表
    updateFormItemList(list: FormItem[]): void {
      this.formItemList = list;
    }
  }
});
