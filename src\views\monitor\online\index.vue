<template>
  <container-card>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="登录地址" prop="ipaddr">
        <el-input v-model="queryParams.ipaddr" placeholder="请输入登录地址" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" style="width: 100%">
      <el-table-column label="序号" type="index" align="center" width="50px">
        <template #default="scope">
          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会话编号" align="center" prop="tokenId" :show-overflow-tooltip="true" />
      <el-table-column label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="客户名称" align="center" prop="custName" />
      <el-table-column label="主机" align="center" prop="ipaddr" :show-overflow-tooltip="true" />
      <el-table-column label="登录源" align="center" prop="source" />
      <el-table-column label="登录时间" align="center" prop="loginTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.loginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" icon="Delete" @click="handleForceLogout(scope.row)" v-hasPermi="['monitor:online:forceLogout']">强退</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" v-model:page="pageNum" v-model:limit="pageSize" :total="total" @pagination="getList" />
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { list as fetchOnlineList, forceLogout } from '@/api/monitor/online';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';

// 日期格式化函数
const parseTime = (time: string | number | Date): string => {
  if (!time) {
    return '';
  }

  const date = new Date(time);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

interface OnlineUser {
  tokenId: string;
  userName: string;
  custName?: string;
  ipaddr: string;
  source?: string;
  loginLocation?: string;
  browser?: string;
  os?: string;
  loginTime: number | string;
  [key: string]: any;
}

interface QueryParams {
  ipaddr?: string;
  userName?: string;
  companyId: string;
  pageNum: number;
  pageSize: number;
}

const route = useRoute();
const queryFormRef = ref<FormInstance>();

// 遮罩层
const loading = ref(true);
// 总条数
const total = ref(0);
// 表格数据
const onlineList = ref<OnlineUser[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
// 查询参数
const queryParams = reactive<QueryParams>({
  ipaddr: undefined,
  userName: undefined,
  companyId: '',
  pageNum: 1,
  pageSize: 10
});

// 计算处理后的表格数据
const tableData = computed(() => {
  const startIndex = (pageNum.value - 1) * pageSize.value;
  const endIndex = pageNum.value * pageSize.value;
  return onlineList.value.slice(startIndex, endIndex);
});

/** 查询登录日志列表 */
const getList = async () => {
  loading.value = true;
  try {
    queryParams.pageNum = pageNum.value;
    queryParams.pageSize = pageSize.value;
    const response = await fetchOnlineList({
      ...queryParams,
      ipaddr: queryParams.ipaddr || '',
      userName: queryParams.userName || ''
    });
    onlineList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  pageNum.value = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 强退按钮操作
 * @param row 用户数据
 */
const handleForceLogout = (row: OnlineUser) => {
  ElMessageBox.confirm(`是否确认强退名称为"${row.userName}"的用户？`)
    .then(async () => {
      await forceLogout(row.tokenId);
      getList();
      ElMessage.success('强退成功');
    })
    .catch(() => {});
};

onMounted(() => {
  queryParams.companyId = route.query.companyId as string;
  getList();
});
</script>
