import axios from 'axios';
import { getToken } from '@/utils/auth';

const baseUrl = import.meta.env.VITE_APP_BASE_API || process.env.VUE_APP_BASE_API;
const headers = {
  Authorization: getToken(),
  'Access-Control-Allow-Origin': '*'
};

/**
 * 下载文件
 * @param ite_url 文件路径
 * @param name 文件名称，如 a.txt
 */
export function downLoadFile(ite_url: string, name: string): void {
  const url = `${baseUrl}/qjt/file/downloadone/${ite_url}`;
  const list = name.split('.');
  const type = list[1];

  axios({
    method: 'get',
    url: url,
    headers: headers,
    responseType: 'blob'
  }).then((res) => {
    // 导出正常
    let downType = '';
    switch (type) {
      case 'txt':
        downType = 'text/plain';
        break;
      case 'png':
        downType = 'image/png';
        break;
      case 'jpg':
        downType = 'image/jpeg';
        break;
      case 'jpeg':
        downType = 'image/jpeg';
        break;
      case 'doc':
        downType = 'application/msword';
        break;
      case 'docx':
        downType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
      case 'xls':
        downType = 'application/vnd.ms-excel';
        break;
      case 'xlsx':
        downType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case 'pdf':
        downType = 'application/pdf';
        break;
      default:
        break;
    }

    const blob = new Blob([res.data], { type: downType });

    // 针对ie浏览器
    if (window.navigator && 'msSaveOrOpenBlob' in window.navigator) {
      (window.navigator as any).msSaveOrOpenBlob(blob, name);
    } else {
      // 非ie浏览器
      const downloadElement = document.createElement('a');
      const href = window.URL.createObjectURL(blob); // 创建下载的链接
      downloadElement.href = href;
      downloadElement.download = name; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href); // 释放blob对象
    }
  });
}

/**
 * 通过文件的类型得到通用类型名
 * @param type 文件MIME类型
 * @returns 通用类型名
 */
export function getFileType(type: string): string {
  let pubType = '';
  switch (type) {
    case 'application/vnd.ms-excel':
      pubType = 'xls';
      break;
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      pubType = 'xlsx';
      break;
    case 'application/msword':
      pubType = 'doc';
      break;
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      pubType = 'docx';
      break;
    case 'application/pdf':
      pubType = 'pdf';
      break;
    case 'text/plain':
      pubType = 'txt';
      break;
    default:
      break;
  }
  return pubType;
}
