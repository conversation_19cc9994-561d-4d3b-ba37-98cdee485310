<template>
  <div>
    <el-form label-width="80px">
      <el-form-item label="图库选择">
        <el-upload
          class="avatar-uploader"
          :headers="headers"
          name="files"
          :action="`${fileUrl}/qjt/file/multi/upload`"
          :show-file-list="false"
          :on-success="handleSuccess"
          :before-upload="beforeUpload"
          :limit="1"
        >
          <el-image
            style="width: 168px; height: 160px"
            v-if="attributeCopy.url"
            :src="attributeCopy.url ? `${fileUrl}/qjt/file/otherDownload/${attributeCopy.url}?token=${token}` : logo"
            fit="fill"
          />
          <el-icon v-else>
            <Plus></Plus>
          </el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item label="填充方式">
        <el-select v-model="attributeCopy.fit" placeholder="请选择填充方式">
          <el-option label="fill" value="fill" />
          <el-option label="contain" value="contain" />
          <el-option label="cover" value="cover" />
          <el-option label="none" value="none" />
          <el-option label="scale-down" value="scale-down" />
        </el-select>
      </el-form-item>
      <el-form-item label="点击放大">
        <el-radio-group v-model="attributeCopy.preview">
          <el-radio :value="true">是</el-radio>
          <el-radio :value="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <gallery ref="gallery" @confirmCheck="confirmCheck" />
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import Gallery from '@/components/addDataScreen/components/gallery.vue';
const logo = import('@/assets/logo/logod.png') as any;

defineOptions({
  name: 'cpt-image-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = ref(props.attribute);

const fileUrl = import.meta.env.VITE_APP_BASE_API;
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};
const token = getToken();

const confirmCheck = (fileUrl: string) => {
  attributeCopy.value.url = fileUrl;
};

const handleSuccess = (res: any) => {
  if (res.data) {
    attributeCopy.value.url = res.data[0].path;
  } else {
    ElMessage.error(res.msg);
  }
};

/**
 * 上传前验证
 * @param file
 * @returns
 */
const beforeUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 10;
  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 10MB!');
  }
  return isJPG && isLt2M;
};
</script>

<style scoped>
.formItemUpload {
  width: 120px;
  height: 120px;
  background-color: #fff;
  border-radius: 6px;
  text-align: center;
  line-height: 120px;
  font-size: 40px;
  color: #aaa;
}
</style>
