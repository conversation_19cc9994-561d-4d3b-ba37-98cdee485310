<!--视频播放的加载组件-->
<template>
  <div v-if="isPlay">
    <video preload="auto" width="128px" height="128px" align="center" controls="true">
      <source :src="videoUrl" type="video/mp4" />
    </video>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { downloadFile } from '@/api/forum';

interface Props {
  videoPath: string;
}

const props = defineProps<Props>();
const videoUrl = ref('');
const isPlay = ref(false);
let isMounted = true;

// 组件卸载前清理资源
onBeforeUnmount(() => {
  isMounted = false;
  if (videoUrl.value) {
    try {
      URL.revokeObjectURL(videoUrl.value);
    } catch (error) {
      console.error('清理视频URL失败:', error);
    }
  }
});

watch(
  () => props.videoPath,
  (newVal) => {
    if (newVal && isMounted) {
      downLoad(newVal);
    }
  },
  { immediate: true, deep: true }
);

function downLoad(val: string) {
  // 检查是否是完整的URL
  if (val.startsWith('http')) {
    isPlay.value = true;
    videoUrl.value = val;
    return;
  }

  // 如果是相对路径，处理为完整路径
  if (!val.startsWith('/')) {
    val = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/' + val + '?att=1';
  } else {
    val = import.meta.env.VITE_APP_BASE_API + val;
  }

  const path = val;
  downloadFile(path)
    .then((res) => {
      if (!isMounted) return; // 如果组件已卸载，不继续处理

      const binaryData: BlobPart[] = [];
      binaryData.push(res.data);
      isPlay.value = true;
      const url = window.URL.createObjectURL(new Blob(binaryData, { type: 'video/mpeg' }));
      videoUrl.value = url;
    })
    .catch((error) => {
      if (isMounted) {
        console.error('视频加载失败:', error);
      }
    });
}
</script>

<style lang="scss" scoped></style>
