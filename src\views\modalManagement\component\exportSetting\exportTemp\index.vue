<!-- 导出报告 -->
<template>
  <div class="exportTemp-main">
    <div class="title">导出报告</div>
    <template v-if="exportType == 2">
      <div class="normal-title">
        导出文件结构
        <div class="right-btn" @click="settingTree"><i class="el-icon-setting" style="margin-right: 5px"></i>设置</div>
      </div>
      <div class="tree-div">
        <el-tree ref="normalTree" :data="[detail]" node-key="id" :props="defaultProps" default-expand-all :expand-on-click-node="false">
          <template #default="{ data }">
            <div class="tree-row" v-if="data.delFlag != 1">
              <div class="tip-div" v-show="showTips && data.checked.source == sourceId">{{ tempName }}</div>
              <div class="tree-left" @mouseenter="mouseEnterEvent(data)" @mouseleave="mouseLeaveEvent(data)">
                <el-input placeholder="请输入内容" v-model="data.fileName" readonly>
                  <template #prefix>
                    <img src="@/assets/images/file_ico.png" class="tree-ico" v-show="data.fileType == 0" />
                    <img src="@/assets/images/word_ico.png" class="tree-ico" v-show="data.fileType == 1" />
                    <img src="@/assets/images/excel_ico.png" class="tree-ico" v-show="data.fileType == 2" />
                    <img src="@/assets/images/shp.png" class="tree-ico" v-show="data.fileType == 3" />
                    <img src="@/assets/images/gdb.png" class="tree-ico" v-show="data.fileType == 4" />
                    <img src="@/assets/images/img_ico.png" class="tree-ico" v-show="data.fileType == 5" />
                    <img src="@/assets/images/DXF.png" class="tree-ico" v-show="data.fileType == 6" />
                    <img src="@/assets/images/fujian.png" class="tree-ico" v-show="data.fileType == 7" />
                  </template>
                </el-input>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </template>
    <!-- 设置导出结构 -->
    <el-dialog draggable title="导出文件结构设置" v-model="dialogVisible" width="611px" :close-on-click-modal="false" :before-close="handleClose">
      <el-tree ref="dialogTree" :data="[detail]" node-key="id" :props="defaultProps" default-expand-all :expand-on-click-node="false">
        <template #default="{ node, data }">
          <div class="tree-row" v-if="data.delFlag != 1" :class="{ 'tree-error': data.repetition }">
            <div class="tip-div" v-show="showTips && data.checked.source == sourceId">{{ tempName }}</div>
            <div class="tree-left" @mouseenter="mouseEnterEvent(data)" @mouseleave="mouseLeaveEvent(data)">
              <el-input placeholder="请输入内容" v-model="data.fileName" @input="handleInput(data.fileName)" @focus="nowFileName(data)">
                <!-- 第一级最外层不让写表达式 -->
                <template #append v-if="getDisplay(node)">
                  <el-dropdown trigger="click" @command="handleCommandExp">
                    <!-- @click="chooseMapField(data,1)" -->
                    <span class="el-dropdown-link">
                      <span style="cursor: pointer"> 选择映射字段&nbsp;&nbsp;<i class="el-icon-arrow-right"></i></span>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="beforeHandleCommand(node, data, '1')">快捷表达式</el-dropdown-item>
                        <el-dropdown-item :command="beforeHandleCommand(node, data, '2')">自定义表达式</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
                <template #prefix>
                  <img src="@/assets/images/file_ico.png" class="tree-ico" v-show="data.fileType == 0" />
                  <img src="@/assets/images/word_ico.png" class="tree-ico" v-show="data.fileType == 1" />
                  <img src="@/assets/images/excel_ico.png" class="tree-ico" v-show="data.fileType == 2" />
                  <img src="@/assets/images/shp.png" class="tree-ico" v-show="data.fileType == 3" />
                  <img src="@/assets/images/gdb.png" class="tree-ico" v-show="data.fileType == 4" />
                  <img src="@/assets/images/img_ico.png" class="tree-ico" v-show="data.fileType == 5" />
                  <img src="@/assets/images/DXF.png" class="tree-ico" v-show="data.fileType == 6" />
                  <img src="@/assets/images/fujian.png" class="tree-ico" v-show="data.fileType == 7" />
                </template>
              </el-input>
            </div>
            <el-dropdown @command="handleCommand" trigger="click">
              <div class="tree-right">
                <span class="el-dropdown-link" style="font-size: 12px; color: var(--current-color)">
                  设置
                  <!-- <i class="el-icon-setting"></i> -->
                </span>
              </div>
              <template #dropdown>
                <el-dropdown-menu style="height: 200px; overflow: auto">
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '1')" :disabled="data.fileType != 0">新建文件夹</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '2')" :disabled="data.fileType != 0">嵌入报告</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '3')" :disabled="data.fileType != 0">嵌入图片</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '16')" :disabled="data.fileType != 0">嵌入附件</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '6')" :disabled="data.fileType != 0">嵌入shp</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '7')" :disabled="data.fileType != 0">嵌入gdb</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '15')" :disabled="data.fileType != 0">嵌入DXF</el-dropdown-item>
                  <el-dropdown-item
                    :command="beforeHandleCommand(node, data, '11')"
                    v-if="(data.fileType != 3 || data.fileType != 4) && data.multipleType == 1"
                    >设置为单文件</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="beforeHandleCommand(node, data, '12')"
                    v-if="(data.fileType != 3 || data.fileType != 4) && data.multipleType == 0"
                    >设置为多文件</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="beforeHandleCommand(node, data, '13')"
                    v-if="data.multipleType == 1 && (data.fileType == 1 || data.fileType == 2)"
                    >字段分组</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="beforeHandleCommand(node, data, '14')"
                    v-if="data.multipleType == 1 && [1, 2, 5].includes(data.fileType)"
                    >节点分组</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="beforeHandleCommand(node, data, '8')"
                    :disabled="data.fileType != 4 && data.fileType != 3 && data.fileType != 6"
                    >编辑</el-dropdown-item
                  >
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '5')" :disabled="data.fileType != 5">映射数据源</el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '9')" :disabled="data.fileType != 1" v-if="data.fileType != 6">
                    <span :class="{ 'success-span': data.checked.type == 1 }">导出为PDF</span>
                  </el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '10')" :disabled="data.fileType != 1" v-if="data.fileType != 6">
                    <span :class="{ 'success-span': data.checked.type == 2 }">导出为txt</span>
                  </el-dropdown-item>
                  <el-dropdown-item :command="beforeHandleCommand(node, data, '4')" :disabled="node.level == 1">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-tree>
      <div class="error-span" v-show="!detailFlag">同级文件夹名或同类型文件名不允许重名</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitDownTree">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 映射字段弹窗 -->
    <mapField
      :mapFieldDialog="mapFieldDialog"
      @closeFieldDialog="closeFieldDialog"
      @submitField="submitField"
      :mapFielType="mapFielType"
      :checked="checkedNode.checked"
    ></mapField>
    <!-- 映射数据源 -->
    <souseFieldTem
      :souseFieldDialog="souseFieldDialog"
      @closeSouseFieldDialog="closeSouseFieldDialog"
      @submitSouseField="submitSouseField"
      :sourceField="sourceField"
    ></souseFieldTem>
    <!-- 选择报告 -->
    <el-dialog title="提示" v-model="temDialog" :close-on-click-modal="false" width="611px" :before-close="handleCloseTem">
      <el-table
        :data="temList"
        ref="tempTableRef"
        @selection-change="handleSelectionChange"
        border
        style="width: 100%; height: 340px; overflow: auto"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="templateName" label="报告名称"> </el-table-column>
        <el-table-column label="创建时间">
          <template v-slot="scope">
            {{ formatDateAndTimeType(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="temDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitAddTem">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 嵌入shpTODO -->
    <shpOrGdbDialog
      :editShpGdbDialog="editShpGdbDialog"
      :addTypeProp="addType"
      :newMsgProp="newMsg"
      @closeShpDialog="closeShpDialog"
      @submitShpOrGdb="submitShpOrGdb"
    ></shpOrGdbDialog>
    <!-- 嵌入gdbtodo -->
    <gdbDialog
      ref="gdbDialogRef"
      :editGdbDialogProp="editGdbDialog"
      :newGdbMsgProp="newGdbMsg"
      :ysTreeProp="ysTree"
      @addGDBContent="addGDBContent"
      @closeGdbDialog="closeGdbDialog"
      @submitGdb="submitGdb"
      @changeDisplayGraph="changeDisplayGraph"
    ></gdbDialog>

    <!-- 打开公式编辑的弹框TODO -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="expression"
      :isCopy="true"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
    ></formula-editing-dialog>

    <!-- 快捷表达式TODO -->
    <fastExpression
      :mapFieldDialog="mapFieldDialogFast"
      @submitFastExp="submitFastExp"
      @handleCloseFast="handleCloseFast"
      :moduleId="moduleId"
      :isFileName="true"
    ></fastExpression>

    <!-- 字段分组 -->
    <share-group-modal
      :groupGistDialog="groupGistDialog"
      :groupInfo="checkedNode.groupInfo"
      @closeGroup="handleCloseGroup"
      @submitGroup="handleSubmitGroup"
    ></share-group-modal>

    <!-- 节点分组 -->
    <ysGroupModal
      :ysGistDialog="ysGistDialog"
      :ruleInfo="checkedNode.ruleInfo"
      @closeGroup="handleCloseGroup"
      @submitGroup="handleSubmitGroup"
    ></ysGroupModal>

    <!-- DXF嵌入弹窗 -->
    <dXFDialog
      :editDXFDialogProp="editDXFDialog"
      @handleCloseDXFDialog="handleCloseDXFDialog"
      :newDXFMsgProp="newDXFMsg"
      :ysTreeProp="ysTree"
      @submitDXFMsg="submitDXFMsg"
    ></dXFDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue';
import mapField from '../mapField/index.vue';
import { getTempList, saveExportSetting, getExportDetail, selectRules } from '@/api/modal';
import shpOrGdbDialog from './shpOrGdbDialog/index.vue';
import gdbDialog from './shpOrGdbDialog/gdbDialog.vue';
import souseFieldTem from '../souseField/index.vue';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import fastExpression from '@/components/fastExpression/index.vue';
import { hasDuplicates } from '@/utils/validate';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter } from 'vue-router';
import shareGroupModal from './components/shareGroupModal.vue';
import ysGroupModal from './components/ysGroupModal.vue';
import dXFDialog from './shpOrGdbDialog/DXFDialog.vue';
import { formatDateYmdhm, formatDateAndTimeType } from '@/utils/filters';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ElTable } from 'element-plus';
import type { Action } from 'element-plus';

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();

const tempTableRef = ref<InstanceType<typeof ElTable>>();
const gdbDialogRef = ref(null);

// 定义 props
const props = defineProps<{
  exportType: any;
  detailInfo: any;
  getDetailToParent: () => any;
}>();

// 定义响应式数据
const appType = ref('#word');
const dialogVisible = ref(false);
const mapFieldDialog = ref(false);
const checkedFiledList = ref<any[]>([]);
const checkedNode = ref<any>({});
const temDialog = ref(false);
const temList = ref<any[]>([]);
const multipleSelectionTem = ref<any[]>([]);
const mapFielType = ref(1);
const defaultProps = ref({
  children: 'list',
  label: 'fileName'
});
const editShpGdbDialog = ref(false);
const addType = ref(1);
const newMsg = ref({
  type: 3,
  exportData: {},
  fieldGroupModelList: [],
  name: '',
  checked: {},
  driveFieldGroupModelList: [],
  repeat: true
});
const isEditShpGdb = ref(false);
const souseFieldDialog = ref(false);
const sourceField = ref<any>({});
const formulaVisible = ref(false);
const expression = ref('');
const mapFieldDialogFast = ref(false);
const nodeData = ref();
const showTips = ref(false);
const tempName = ref('');
const sourceId = ref('');
const detailFlag = ref(true);
const nowTreeList = ref<any[]>([]);
const groupGistDialog = ref(false);
const groupModel = ref<any>({});
const editGdbDialog = ref(false);
const newGdbMsg = ref<any[]>([]);
const gdbFileName = ref('');
const ysTree = ref<any[]>([]);
const ysGistDialog = ref(false);
const editDXFDialog = ref(false);
const newDXFMsg = ref<any>({});
const odeData = ref<any>('');
const nowChooseNode = ref<any>(null); // 存储当前选中的节点
const addFileType = ref(1); // 1图片 2附件

// 定义 emits
const emit = defineEmits(['editTreeDetail', 'editTreeGroupInfo']);

// 定义计算属性
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = Number(router.currentRoute.value.query.id);
  }
  return moduleId;
});

const detail = computed(() => {
  return props.detailInfo;
});

// 定义方法
const initDetail = (detail: any) => {
  // 这里 props.detail 是只读的，如果需要修改，需要通过 emit 通知父组件
  // props.detail = detail;
};

const getData = () => {
  const parmas = {
    moduleId: moduleId.value,
    name: ''
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    parmas.companyId = companyId;
  }
  const moduleIdRef = router.currentRoute.value.query.id;
  if (moduleId.value == 0 && moduleId) {
    parmas.moduleId = Number(moduleIdRef);
  }
  if (!parmas.moduleId) {
    return;
  }
  getTempList(parmas).then((res) => {
    if (res.code == 200) {
      temList.value = res.data;
    } else {
    }
  });
};

const settingTree = () => {
  checkedNode.value = detail.value;
  dialogVisible.value = true;
};

const handleClose = async () => {
  detailFlag.value = true;
  // 假设 verifyDetail 函数存在于某个模块中，这里需要引入
  // await verifyDetail(props.detail.list);
  if (detailFlag.value) {
    dialogVisible.value = false;
  }
};

const closeFieldDialog = () => {
  mapFieldDialog.value = false;
};

const submitField = (list: any[], fieldCn: string, rouleId: any) => {
  const checked = {
    checkedFiledList: [],
    checkedFiledSouseList: [],
    source: ''
  };
  if (mapFielType.value == 1) {
    checked.checkedFiledList = list;
    checkedNode.value.checked = checked;
    checkedNode.value.fileName = '';
    list.forEach((v) => {
      checkedNode.value.fileName = checkedNode.value.fileName + `${v.allWorkName}`;
    });
  } else if (mapFielType.value == 2) {
    checked.checkedFiledList = list;
    list.forEach((v) => {
      checked.source = checked.source + `${v.allWorkName}`;
    });
    checkedNode.value.checked = checked;
    checkedNode.value.fileName = fieldCn;
  }
  mapFieldDialog.value = false;
};

const chooseMapField = (data: any, type: number) => {
  checkedNode.value = data;
  mapFielType.value = type;
  if (type == 1) {
    mapFieldDialog.value = true;
  } else if (type == 2) {
    sourceField.value = data.checked.sourceField;
    souseFieldDialog.value = true;
  }
};

const clearChooseField = () => {
  checkedFiledList.value = [];
};

const append = (data: any) => {
  const newChild = { id: 0, label: 'testtest', children: [] };
  if (!data.children) {
    data.children = [];
  }
  data.children.push(newChild);
};

const remove = (node: any, data: any) => {
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d: any) => d.id === data.id);
  children.splice(index, 1);
};

const initYStree = (list: any[]) => {
  list.forEach((v) => {
    if (!v.label) {
      v.label = v.typeName;
    }
    if (v.fieldGroupModelList.length != 0 && !v.special) {
      v.fieldGroupModelList.forEach((k: any) => {
        if (k.ruleAttribution && (k.ruleAttribution.type == 'graphicalLine' || k.ruleAttribution.type == 'commonLine')) {
          const node = {
            typeName: v.typeName,
            label: `${k.typeName}(线)`,
            id: v.id,
            graphicalType: 2,
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true,
            parentId: v.id,
            isChild: true
          };
          v.list.unshift(node);
        } else if (k.ruleAttribution && (k.ruleAttribution.type == 'graphicalPoint' || k.ruleAttribution.type == 'commonPoint')) {
          const node = {
            typeName: v.typeName,
            label: `${k.typeName}(点)`,
            id: v.id,
            graphicalType: 1, //子要素点
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true,
            parentId: v.id,
            isChild: true //标识为子要素
          };
          v.list.unshift(node);
        } else if (k.ruleAttribution && k.ruleAttribution.type == 'graphicalArea') {
          //面
          const node = {
            typeName: `${v.typeName}`,
            label: `${k.typeName}(面)`,
            id: v.id,
            graphicalType: 3, //子要素点
            fieldGroupModelList: [k],
            list: [],
            iconUrl: k.iconUrl,
            special: true,
            parentId: v.id,
            isChild: true //标识为子要素
          };
          v.list.unshift(node);
        }
      });
    } else {
    }
    if (v.list.length != 0) {
      initYStree(v.list);
    }
  });
};

// 生命周期钩子
onMounted(() => {
  getData();
});

// 由于原代码部分方法未完整展示，这里简单占位
const mouseEnterEvent = (val: any) => {
  if (val.checked && val.checked.source) {
    const id = val.checked.source;
    for (let index = 0; index < temList.value.length; index++) {
      if (temList.value[index].id == id) {
        tempName.value = temList.value[index].templateName;
        sourceId.value = id;
        break;
      }
    }
    showTips.value = true;
  }
};
// 鼠标移出实践
const mouseLeaveEvent = () => {
  showTips.value = false;
  sourceId.value = '';
  tempName.value = '';
};
// 阻止特殊字符输入
const handleInput = (val: string) => {
  // 移除特殊字符，可以根据需要进行调整
  nodeData.value.fileName = val.replace(/[\\/:*?<>|]/g, '');
};
// 获取当前选中的文件名
const nowFileName = (val: any) => {
  odeData.value = val;
};
// 选中某种表达式
const handleCommandExp = (val: any) => {
  checkedNode.value = val.data;
  if (val.command == '1') {
    //快捷表达式
    mapFieldDialogFast.value = true;
  } else if (val.command == '2') {
    //自定义表达式
    // 每次打开先初始化表达式
    expression.value = '';
    // 在每次打开自定义表达式的时候，需要把字段数据清除否则会有缓存
    modalStore.setIsHasAcquition(false);
    formulaVisible.value = true;
    modalStore.setIsAllGroup(true);
  }
};
const beforeHandleCommand = (node: any, data: any, command: string) => {
  return {
    'node': node,
    'data': { ...data },
    'command': command
  };
};
const handleCommand = async (data: any) => {
  if (data.command == '1') {
    // 新建文件夹
    const obj = {
      checked: {},
      fileName: '文件夹',
      fileType: 0,
      list: [],
      ifFolder: 1,
      vector: {},
      ruleId: (Math.random() + Date.now()).toString(32).slice(0, 8),
      multipleType: 1
    };
    data.data.list.push(obj);
  } else if (data.command == '2') {
    // 嵌入报告
    await getData();
    checkedNode.value = data;
    temDialog.value = true;
  } else if (data.command == '3') {
    nowChooseNode.value = data.data;
    //嵌入图片
    addFileType.value = 1;
    souseFieldDialog.value = true;
    // 嵌入图片
    // const obj = {
    //   checked: {},
    //   fileName: '图片',
    //   fileType: 5,
    //   ifFolder: 2,
    //   list: [],
    //   vector: {},
    //   ruleId: (Math.random() + Date.now()).toString(32).slice(0, 8),
    //   multipleType: 1
    // };
    // nowTreeList.value = data.list;
    // data.data.list.push(obj);
    // checkedNode.value = obj;
    // chooseMapField(checkedNode.value, 2);
    // nowChooseNode.value = data.data;
  } else if (data.command == '4') {
    if (data.data.list.length != 0) {
      ElMessage.error('该文件夹下有子文件夹或文件，不能删除');
      return;
    }
    // 删除
    ElMessageBox.confirm('确定要删除该数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        const parent = data.node.parent;
        const children = parent.data.list;
        if (data.data.id) {
          //删除已经存在的
          data.data.delFlag = 1;
          // 直接调用保存接口，然后给树重新赋值
          const exportMsg = props.getDetailToParent();
          delNode([exportMsg.detail], data.data.id);
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = router.currentRoute.value.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            exportMsg.companyId = companyId;
          }

          saveExportSetting(exportMsg).then((res) => {
            if (res.code == 200) {
              getExportDetail(res.data.id).then((resp) => {
                if (resp.code == 200) {
                  emit('editTreeDetail', resp.data.detail);
                } else {
                  ElMessage.error(resp.msg);
                }
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else {
          //代表是没有上传过的 直接删除
          const index = children.findIndex((d: any) => d.ruleId === data.data.ruleId);
          children.splice(index, 1);
        }
      })
      .catch(() => {});
  } else if (data.command == '5') {
    chooseMapField(data.data, 2);
  } else if (data.command == '6') {
    //嵌入shp
    isEditShpGdb.value = false; //代表是新增
    checkedNode.value = data.data;
    addType.value = 1;
    newMsg.value = {
      type: 3, // 1点 2线 3面
      exportData: {}, //导出数据
      fieldGroupModelList: [], //选择的属性
      name: '', //文件名称
      checked: {}, //反显选择的映射字段json
      driveFieldGroupModelList: [], //驱动字段组 按理字段只有一个
      repeat: true
    }; //新增shp 或者gdb结构
    editShpGdbDialog.value = true;
  } else if (data.command == '7') {
    //嵌入gdb
    let moduleId_item = moduleId.value;
    if (router.currentRoute.value.query.id) {
      moduleId_item = Number(router.currentRoute.value.query.id);
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = router.currentRoute.value.query.companyId;
    // if (params && companyId !== undefined && companyId !== null) {
    //   params.companyId = companyId;
    // }
    const params = {
      moduleId: moduleId_item,
      companyId: companyId
    };
    selectRules(params).then((res) => {
      if (res.code == 200) {
        ysTree.value = res.data;
        initYStree(ysTree.value);
        isEditShpGdb.value = false; //代表是新增
        checkedNode.value = data.data;
        addType.value = 2;
        newGdbMsg.value = [
          {
            type: 3, // 1点 2线 3面
            exportData: {}, //导出数据
            fieldGroupModelList: [], //选择的属性
            name: '', //文件名称
            checked: {}, //反显选择的映射字段json
            driveFieldGroupModelList: [], //驱动字段组 按理字段只有一个
            displayGraph: true, //GDB特有属性 是否显示GDB图形 因为GDB可以只导出属性不导出图形
            repeat: true,
            allOrderNameType: 'Integer' //排序字段类型
          }
        ]; //新增shp 或者gdb结构
        gdbDialogRef.value.initName('');
        editGdbDialog.value = true;
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (data.command == '8') {
    //编辑shp或gdb
    isEditShpGdb.value = true; //代表是编辑
    const vector = data.data.vector;
    checkedNode.value = data.data;
    if (data.data.fileType == 3) {
      //shp
      newMsg.value = {
        type: vector.type, // 1点 2线 3面
        exportData: vector.exportData, //导出数据
        fieldGroupModelList: vector.fieldGroupModelList, //选择的属性
        name: data.data.fileName, //文件名称
        checked: data.data.checked, //反显选择的映射字段json
        driveFieldGroupModelList: vector.driveFieldGroupModelList, //驱动字段组 按理字段只有一个
        repeat: vector.repeat
      };
      if (!newMsg.value.exportData.label) {
        //处理老数据 现在设置导出数据 显示用的label
        newMsg.value.exportData.label = newMsg.value.exportData.typeName;
      }
      addType.value = 1;
      editShpGdbDialog.value = true;
    } else if (data.data.fileType == 4) {
      //gdb
      let moduleId_item = moduleId.value;
      if (router.currentRoute.value.query.id) {
        moduleId_item = Number(router.currentRoute.value.query.id);
      }
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = router.currentRoute.value.query.companyId;
      const params = {
        moduleId: moduleId_item,
        companyId: companyId
      };
      selectRules(params).then((res) => {
        if (res.code == 200) {
          ysTree.value = res.data;
          initYStree(ysTree.value);
          addType.value = 2;
          if (!vector.list) {
            //老款GDB
            newGdbMsg.value = [vector];
          } else {
            newGdbMsg.value = vector.list;
          }
          newGdbMsg.value[0].show = true;
          editGdbDialog.value = true;
          gdbDialogRef.value.initName(data.data.fileName);
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else if (data.data.fileType == 6) {
      //dxf
      let moduleId_itme = moduleId.value;
      if (router.currentRoute.value.query.id) {
        moduleId_itme = Number(router.currentRoute.value.query.id);
      }
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = router.currentRoute.value.query.companyId;
      const params = {
        moduleId: moduleId_itme,
        companyId: companyId
      };
      selectRules(params).then((res) => {
        if (res.code == 200) {
          ysTree.value = res.data;
          newDXFMsg.value = { ...checkedNode.value };
          newDXFMsg.value.dxfData = checkedNode.value.vector.dxfData;
          editDXFDialog.value = true;
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  } else if (data.command == '9') {
    //导为PDF
    data.data.checked.type = 1;
    editNodeToDetail([detail.value], data.data, 3);
  } else if (data.command == '10') {
    //导为txt
    data.data.checked.type = 2;
    editNodeToDetail([detail.value], data.data, 4);
  } else if (data.command == '11') {
    //设置为单数据
    data.data.multipleType = 0;
    editNodeToDetail([detail.value], data.data, 1);
  } else if (data.command == '12') {
    //设置为双数据
    data.data.multipleType = 1;
    editNodeToDetail([detail.value], data.data, 2);
  } else if (data.command == '13') {
    //设置字段分组
    checkedNode.value = data.data;
    groupGistDialog.value = true;
  } else if (data.command == '14') {
    //节点分组
    checkedNode.value = data.data;
    console.log('当前节点---', checkedNode.value);

    ysGistDialog.value = true;
  } else if (data.command == '15') {
    //嵌入DXF文件
    let moduleId_item = moduleId.value;
    if (router.currentRoute.value.query.id) {
      moduleId_item = Number(router.currentRoute.value.query.id);
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = router.currentRoute.value.query.companyId;
    const params = {
      moduleId: moduleId_item,
      companyId: companyId
    };
    selectRules(params).then((res) => {
      if (res.code == 200) {
        ysTree.value = res.data;
        newDXFMsg.value = {
          fileName: '', //文件名称
          dxfData: []
        };
        editDXFDialog.value = true;
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (data.command == '16') {
    nowChooseNode.value = data.data;
    //嵌入附件
    addFileType.value = 2;
    souseFieldDialog.value = true;
    // checkedNode.value = obj;
    // chooseMapField(checkedNode.value, 2); //1映射字段 2映射数据源
  }
};
// 关闭映射数据源弹窗
const closeSouseFieldDialog = () => {
  if (!checkedNode.value.id && nowTreeList.value) {
    for (let index = 0; index < nowTreeList.value.length; index++) {
      if (nowTreeList.value[index].ruleId == checkedNode.value.ruleId) {
        nowTreeList.value.splice(index, 1);
        break;
      }
    }
  }
  souseFieldDialog.value = false;
};
const submitSouseField = (obj: any, fieldCn: any) => {
  if (nowChooseNode.value) {
    //附件
    const obj_item = {
      checked: {
        checkedFiledList: [],
        checkedFiledSouseList: [],
        source: '',
        sourceField: obj
      }, // 属性映射的列表 包含图片的映射源
      fileName: fieldCn, // 文件名
      fileType: 7, // 0,文件夹 1,word,2,excel,3,gdb,4,shp 5,图片 7附件
      ifFolder: 2, //是否文件夹 1是 2不是
      list: [],
      vector: {}, //矢量配置 json
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8), //用于删除处理
      multipleType: 1 //单数据
    };
    if (addFileType.value == 1) {
      //图片
      obj_item.fileType = 5;
    }
    nowChooseNode.value.list.push(obj_item);
    nowTreeList.value = nowChooseNode.value.list;
    souseFieldDialog.value = false;
  } else {
    const checked = {
      checkedFiledList: [],
      checkedFiledSouseList: [],
      source: '',
      sourceField: obj
    };
    checkedNode.value.checked = checked;
    checkedNode.value.fileName = fieldCn;
    souseFieldDialog.value = false;
  }
};
const handleSelectionChange = (selection: any[]) => {
  multipleSelectionTem.value = selection;
};
const submitAddTem = () => {
  if (multipleSelectionTem.value.length == 0) {
    ElMessage.error('您未选择报告!!!');
    return;
  }
  multipleSelectionTem.value.forEach((v, vdx) => {
    let type = 1;
    const index = v.templateName.indexOf('.');
    if (v.templateName.includes('.docx') || v.templateName.includes('.doc')) {
      type = 1;
    } else if (v.templateName.includes('.xls') || v.templateName.includes('.xlsx')) {
      type = 2;
    }
    const obj = {
      checked: {
        source: v.id
      }, // 属性映射的列表  包含图片的映射源
      fileName: v.templateName.substring(0, index), // 文件名
      fileType: type, // 0,文件夹 1,word,2,excel,3,gdb,4,shp 5,图片
      ifFolder: 2, //是否文件夹 1是 2不是
      list: [],
      vector: {}, //矢量配置 json
      ruleId: `${(Math.random() + new Date().getTime()).toString(32).slice(0, 8)}${vdx}`, //用于删除处理
      multipleType: 1
      //  TODO
      // groupModel:this.groupModel
    };
    checkedNode.value.data.list.push(obj);
  });
  multipleSelectionTem.value = [];
  tempTableRef.value!.clearSelection();
  temDialog.value = false;
};
const closeShpDialog = (flg = false) => {
  editShpGdbDialog.value = flg;
};
const submitShpOrGdb = (obj: any) => {
  if (!isEditShpGdb.value) {
    //新增
    const item = {
      checked: obj.checked, // 属性映射的列表  包含图片的映射源
      fileName: obj.name, // 文件名
      fileType: 3, // 0,文件夹 1,word,2,excel,3,shp,4,gdb 5,图片
      ifFolder: 2, //是否文件夹 1是 2不是
      list: [],
      vector: {
        exportData: obj.exportData,
        fieldGroupModelList: obj.fieldGroupModelList,
        type: obj.type,
        driveFieldGroupModelList: obj.driveFieldGroupModelList,
        repeat: obj.repeat
      }, //矢量配置 json
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8) //用于删除处理
    };
    if (addType.value == 1) {
      //提交的是shp
      item.fileType = 3;
    } else if (addType.value == 2) {
      //提交的是gdb
      item.fileType = 4;
      item.vector.displayGraph = obj.displayGraph;
    }
    checkedNode.value.list.push(item);
  } else {
    //编辑
    checkedNode.value.fileName = obj.name;
    checkedNode.value.checked = obj.checked;
    checkedNode.value.vector = {
      exportData: obj.exportData,
      fieldGroupModelList: obj.fieldGroupModelList,
      type: obj.type,
      driveFieldGroupModelList: obj.driveFieldGroupModelList,
      repeat: obj.repeat
    };
    if (checkedNode.value.fileType == 4) {
      //提交的是gdb
      checkedNode.value.vector.displayGraph = obj.displayGraph;
    }
  }
  newMsg.value = {
    type: 3, // 1点 2线 3面
    exportData: {}, //导出数据
    fieldGroupModelList: [], //选择的属性
    name: '', //文件名称
    checked: {}, //反显选择的映射字段json
    driveFieldGroupModelList: [], //驱动字段组 按理字段只有一个
    repeat: true
  }; //新增shp 或者gdb结构
  //判断上一级文件夹名称是不是表达式，如果是表达式就需要把表达式清空并默认文件夹名字为导出
  const flg = checkedNode.value.fileName.includes('#{');
  if (flg) {
    ElMessageBox.alert(`文件夹名称错误，子文件存在shp或gdb的时候父文件夹不能使用动态表达式，已强制改为导出！！！`, '提示', {
      confirmButtonText: '确定',
      callback: (action: Action) => {}
    });
    checkedNode.value.fileName = '导出';
  }
  addType.value = 1;
  // 这里要迭代detail里面去找到对应的值赋值
  setCheckNode([detail.value], checkedNode.value);
  editShpGdbDialog.value = false;
};
// 迭代赋值当前选择的数据
const setCheckNode = (list: any[], obj: any) => {
  for (let i = 0; i < list.length; i++) {
    if ((list[i].id && list[i].id == obj.id) || (list[i].ruleId && list[i].ruleId == obj.ruleId)) {
      list[i] = obj;
      break;
    }
    if (list[i].list.length != 0) {
      setCheckNode(list[i].list, obj);
    }
  }
};
// 增加gdb内容
const addGDBContent = (data: any) => {
  newGdbMsg.value.push({
    type: 3, // 1点 2线 3面
    exportData: {}, //导出数据
    fieldGroupModelList: [], //选择的属性
    name: '', //文件名称
    checked: {}, //反显选择的映射字段json
    driveFieldGroupModelList: [], //驱动字段组 按理字段只有一个
    displayGraph: true, //GDB特有属性 是否显示GDB图形 因为GDB可以只导出属性不导出图形
    repeat: true,
    allOrderNameType: 'Integer' //排序字段类型
  }); //新增shp 或者gdb结构
};
// 关闭编辑gdb弹窗
const closeGdbDialog = (flg = false) => {
  editGdbDialog.value = flg;
};
// 提交gdb
const submitGdb = (list: any[], name: string) => {
  if (!isEditShpGdb.value) {
    //新增
    const item = {
      checked: {}, // 属性映射的列表  包含图片的映射源
      fileName: name, // 文件名
      fileType: 4, // 0,文件夹 1,word,2,excel,3,shp,4,gdb 5,图片
      ifFolder: 2, //是否文件夹 1是 2不是
      list: [],
      vector: {
        list
      }, //矢量配置 json
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8) //用于删除处理
    };
    checkedNode.value.list.push(item);
  } else {
    // this.checkedNode.fileName = name
    if (checkedNode.value.id) {
      //代表之前保存了的
      iterationSetFileName([detail.value], checkedNode.value.id, 2, name);
    } else {
      //代表新建的
      iterationSetFileName([detail.value], checkedNode.value.ruleId, 1, name);
    }
  }

  newGdbMsg.value = [];
  //判断上一级文件夹名称是不是表达式，如果是表达式就需要把表达式清空并默认文件夹名字为导出
  const flg = checkedNode.value.fileName.includes('#{');
  if (flg) {
    ElMessageBox.alert(`文件夹名称错误，子文件存在shp或gdb的时候父文件夹不能使用动态表达式，已强制改为导出！！！`, '提示', {
      confirmButtonText: '确定',
      callback: (action: Action) => {}
    });
    checkedNode.value.fileName = '导出';
  }
  addType.value = 1;
  editGdbDialog.value = false;
};
// 迭代赋值文件名
const iterationSetFileName = (list: any[], id: string, type: number, name: string) => {
  for (let i = 0; i < list.length; i++) {
    if (type == 1) {
      //匹配ruleId
      if (list[i].ruleId == id) {
        list[i].fileName = name;
        break;
      } else {
        iterationSetFileName(list[i].list, id, type, name);
      }
    } else {
      //匹配id
      if (list[i].id == id) {
        list[i].fileName = name;
        break;
      } else {
        iterationSetFileName(list[i].list, id, type, name);
      }
    }
  }
};
// 子组件改变是否显示图形
const changeDisplayGraph = (val: any) => {
  newMsg.value.displayGraph = val;
};
// 关闭公式弹框
const handleCloseFormulation = () => {
  formulaVisible.value = false;
  modalStore.setIsAllGroup(false);
};
const handleSubmitFormulation = (expressionCopy: any) => {
  expression.value = `#{${expressionCopy}}`;
  copyFun(expression.value);
};
// 得到表达式
const copyFun = (expression: any) => {
  checkedNode.value.fileName = expression;
  if (checkedNode.value.id) {
    setExpression([detail.value], checkedNode.value.id, markRaw(expression), 2);
  } else {
    setExpression([detail.value], checkedNode.value.ruleId, markRaw(expression), 1);
  }
};
// 迭代找树赋值表达式 //type 1新增 用ruleId找 2修改用id找
const setExpression = (list: any[], id: string, val: string, type: number) => {
  for (let i = 0; i < list.length; i++) {
    if (type == 1) {
      if (list[i].ruleId == id) {
        list[i].fileName = val;
        break;
      } else {
        setExpression(list[i].list, id, val, type);
      }
    } else {
      if (list[i].id == id) {
        list[i].fileName = val;
        break;
      } else {
        setExpression(list[i].list, id, val, type);
      }
    }
  }
};
// 关闭分组的弹框 //type 1字段 2节点 分组
const handleCloseGroup = (type: number) => {
  if (type == 1) {
    groupGistDialog.value = false;
  } else {
    ysGistDialog.value = false;
  }
};
// 提交分组依据的弹框 //type 1字段 2节点 分组
const handleSubmitGroup = async (str: any, type: number) => {
  if (type == 1) {
    // this.groupModel = item
    // 直接调用保存接口，然后给树重新赋值
    // checkedNode.value.groupInfo = str;
    let id = checkedNode.value.id;
    if (checkedNode.value.ruleId) {
      //代表是新增的
      id = checkedNode.value.ruleId;
    }
    setGroup([detail.value], id, str, type);
    // this.$emit('editTreeGroupInfo',exportMsg.groupInfo)
    groupGistDialog.value = false;
  } else {
    // this.groupModel = item
    // 直接调用保存接口，然后给树重新赋值
    checkedNode.value.ruleInfo = str;
    let id = checkedNode.value.id;
    if (checkedNode.value.ruleId) {
      //代表是新增的
      id = checkedNode.value.ruleId;
    }

    await setGroup([detail.value], id, str, type);
    // this.$emit('editTreeGroupInfo',exportMsg.groupInfo)
    ysGistDialog.value = false;
  }
};
// 迭代找树赋值
const setGroup = async (list: any[], id: string, str: string, type: number) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id || list[i].ruleId == id) {
      if (type == 1) {
        list[i].groupInfo = str;
      } else {
        list[i].ruleInfo = str;
      }
      break;
    } else {
      setGroup(list[i].list, id, str, type);
    }
  }
};
// 关闭选择报告
const handleCloseTem = () => {
  ElMessageBox.confirm('确定取消添加报告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      temDialog.value = false;
    })
    .catch(() => {});
};
const handleCloseDXFDialog = () => {
  editDXFDialog.value = false;
};
// 提交DXF数据
const submitDXFMsg = (data: any) => {
  if (!data.id) {
    //新增
    checkedNode.value.list.push({
      fileType: 6,
      fileName: data.fileName,
      ifFolder: 2, //是否文件夹 1是 2不是
      list: [],
      vector: {
        dxfData: data.dxfData
      },
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8) //用于删除处理
    });
  } else {
    //编辑
    // this.checkedNode = data
    setDXFtoDetail([detail.value], data);
  }
  editDXFDialog.value = false;
};
// 迭代改变文件树的dxf数据
const setDXFtoDetail = (list: any[], data: any) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == data.id) {
      list[i].fileName = data.fileName;
      list[i].vector.dxfData = data.dxfData;
      break;
    }
    if (list[i].list.length != 0) {
      setDXFtoDetail(list[i].list, data);
    }
  }
};
const submitDownTree = async () => {
  //先初始化detailFlag
  detailFlag.value = true;
  verifyDetail(detail.value.list);
  if (detailFlag.value) {
    //文件树合法 同一级下需要验证fileName不一样 shp、gdb、文件夹
    dialogVisible.value = false;
  }
};
// 定义数据项的类型
interface DataItem {
  fileType: number;
  fileName: string;
  repetition?: boolean;
  list?: DataItem[];
}
// 用于迭代验证文件树是否有gdb或shp名称重复
const verifyDetail = (list: DataItem[]): void => {
  list.forEach((v) => {
    v.repetition = false;
  });

  // 先把 list 用 fileType 类型分组 看分组里面的文件名是否重复
  const flatArray: { [key: number]: DataItem[] } = list.reduce((accumulator, currentValue) => {
    accumulator[currentValue.fileType] = accumulator[currentValue.fileType] || [];
    accumulator[currentValue.fileType].push(currentValue);
    return accumulator;
  }, {});

  Object.values(flatArray).forEach((v) => {
    const map = new Map<string, DataItem[]>();
    v.forEach((item) => {
      const fileName = item.fileName;
      if (map.has(fileName)) {
        map.set(fileName, map.get(fileName)!.concat(item));
      } else {
        map.set(fileName, [item]);
      }
    });

    // 得到重复的数据
    const duplicateItems = Array.from(map.values()).filter((items) => items.length > 1);
    if (duplicateItems.length !== 0) {
      duplicateItems[0].forEach((k) => {
        k.repetition = true;
        // 假设 detailFlag 是一个响应式变量，需要在组件中正确定义
        // 这里暂时无法直接访问 this.detailFlag，需要根据实际情况处理
        // this.detailFlag = false;
      });
    }
  });

  list.forEach((v) => {
    if (v.list && v.list.length !== 0) {
      verifyDetail(v.list);
    }
  });
};
const getDisplay = (node: any) => {
  const data = node.data;
  if (node.level == 1) {
    return false;
  } else {
    if (data.fileType == 0) {
      //代表是文件夹 需要判断子集是否有shp或gdb
      const list = data.list;
      let flg = false;
      for (let index = 0; index < list.length; index++) {
        if (list[index].fileType == 4 || list[index].fileType == 3 || list[index].fileType == 6) {
          flg = true;
          break;
        }
      }
      if (flg) {
        return false;
      }
    } else if (data.fileType == 4 || data.fileType == 3 || data.fileType == 6) {
      return false;
    }
    return true;
  }
};
// 迭代找树的对象修改 type 1文件夹改为单数据 2改为多数据 3导出为pdf 4、导出为txt
const editNodeToDetail = (list: any[], item: any, type: number) => {
  list.forEach((v) => {
    if (v.ruleId) {
      //新增的
      if (v.ruleId == item.ruleId) {
        if (type == 1) {
          v.multipleType = 0;
        } else if (type == 2) {
          v.multipleType = 1;
        } else if (type == 3) {
          v.checked.type = 1;
        } else if (type == 4) {
          v.checked.type = 2;
        }
      }
    } else {
      if (v.id == item.id) {
        if (type == 1) {
          v.multipleType = 0;
        } else if (type == 2) {
          v.multipleType = 1;
        } else if (type == 3) {
          v.checked.type = 1;
        } else if (type == 4) {
          v.checked.type = 2;
        }
      }
    }
    if (v.list) {
      editNodeToDetail(v.list, item, type);
    }
  });
};
// 删除某个节点 也就是找树下面某个节点然后把delflg=1
const delNode = (list: any[], id: number) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      //找到
      list[i].delFlag = 1;
      break;
    }
    if (list[i].list.length != 0) {
      delNode(list[i].list, id);
    }
  }
};
const submitFastExp = (expressionRef: string) => {
  copyFun(expressionRef);
  mapFieldDialogFast.value = false;
};
const handleCloseFast = () => {
  mapFieldDialogFast.value = false;
};
</script>
<style lang="scss" scoped>
.success-span {
  color: var(--current-color);
}
.tree-ico {
  width: 20px;
  height: 20px;
  // margin-top: 8px;
  margin-right: 5px;
}
.exportTemp-main {
  width: 100%;
  height: 100%;
  padding: 20px 16px;
  color: #161d26;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .normal-title {
    font-size: 14px;
    margin-bottom: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-btn {
      color: var(--current-color);
      cursor: pointer;
    }
  }
  .tree-div {
    width: 100%;
    height: calc(100% - 150px);
    border: 1px solid rgba(224, 228, 234, 1);
    border-radius: 8px;
    overflow: auto;
    padding: 8px;
    .tree-empty {
      display: flex;
      justify-content: center;
      margin-top: 40px;
      color: rgba(130, 145, 169, 1);
    }
  }
  .mini {
    height: 100px;
  }
  .dialog-title {
    color: #161d26;
    font-weight: bold;
    margin-bottom: 8px;
  }
}
.tree-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 38px;
  width: 100%;
  // border-bottom: 1px solid #dcdfe6;
  .tree-left {
    border-radius: 8px;
    flex: 1;
    height: 38px;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
  }
  .tip-div {
    position: absolute;
    background: rgb(255, 255, 255, 1);
    z-index: 1;
    top: 0px;
    right: 5px;
    color: #333;
    border-radius: 4px;
    padding: 0px 10px;
    border: #dcdfe6 solid 1px;
    font-size: 12px;
  }
  .tree-right {
    width: 32px;
    height: 32px;
    margin-left: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}
.tree-error {
  border: red solid 1px;
}
:deep(.el-tree-node__content) {
  // .el-tree-node__expand-icon.is-leaf{
  //   display: none;
  // }
  .el-tree-node__content {
    height: 38px;
  }
}
:deep(.el-tree-node__content) {
  height: 38px;
}
.error-span {
  color: red;
}
</style>
