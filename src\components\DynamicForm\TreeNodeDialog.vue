<template>
  <div>
    <el-dialog v-bind="$attrs" :close-on-click-modal="false" :modal-append-to-body="false" @open="onOpen" @close="onClose">
      <el-row :gutter="0">
        <el-form ref="formRef" :model="formData" :rules="rules" size="small" label-width="100px">
          <el-col :span="24">
            <el-form-item label="选项名" prop="label">
              <el-input v-model="formData.label" placeholder="请输入选项名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选项值" prop="value">
              <el-input v-model="formData.value" placeholder="请输入选项值" clearable>
                <template #append>
                  <el-select v-model="dataType" :style="{ width: '100px' }">
                    <el-option v-for="item in dataTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <template #footer>
        <div>
          <el-button type="primary" @click="handelConfirm">确定</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import type { FormInstance } from 'element-plus';
import { isNumberStr } from './utils/index';
import { getTreeNodeId, saveTreeNodeId } from './utils/db';

interface FormData {
  label?: string;
  value?: string | number;
  id?: number;
}

interface DataTypeOption {
  label: string;
  value: 'string' | 'number';
}

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'commit', data: FormData): void;
}>();

const formRef = ref<FormInstance>();
const nodeId = ref(getTreeNodeId());

const formData = reactive<FormData>({
  label: undefined,
  value: undefined
});

const rules = {
  label: [{ required: true, message: '请输入选项名', trigger: 'blur' }],
  value: [{ required: true, message: '请输入选项值', trigger: 'blur' }]
};

const dataType = ref<'string' | 'number'>('string');
const dataTypeOptions: DataTypeOption[] = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' }
];

watch(
  () => formData.value,
  (val) => {
    if (typeof val === 'string') {
      dataType.value = isNumberStr(val) ? 'number' : 'string';
    }
  }
);

watch(
  () => nodeId.value,
  (val) => {
    saveTreeNodeId(val);
  }
);

const onOpen = () => {
  formData.label = undefined;
  formData.value = undefined;
};

const onClose = () => {
  // 关闭时的处理逻辑
};

const close = () => {
  emit('update:visible', false);
};

const handelConfirm = async () => {
  if (!formRef.value) return;

  const valid = await formRef.value.validate();
  if (!valid) return;

  if (dataType.value === 'number' && typeof formData.value === 'string') {
    formData.value = parseFloat(formData.value);
  }

  formData.id = nodeId.value++;
  emit('commit', { ...formData });
  close();
};
</script>

<style lang="scss" scoped>
.el-dialog {
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}
</style>
