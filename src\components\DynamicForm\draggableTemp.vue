<!-- 字段全类型 -->
<template>
  <div>
    <!-- 日期 -->
    <template v-if="field.tag === 'el-date-picker'">
      <!-- 日期 -->
      <template v-if="field.type === 'date'">
        <el-date-picker v-model="field.defaultValue" type="date" value-format="x" :style="{ width: field.style.width }" />
      </template>
      <!-- 日期区间 -->
      <template v-else-if="field.type === 'daterange'">
        <el-date-picker
          v-model="field.defaultValue"
          type="daterange"
          :placeholder="field.placeholder"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          range-separator=" 至 "
          :style="{ width: field.style.width }"
        />
      </template>
    </template>
    <!-- 时间 -->
    <template v-else-if="field.tag === 'el-time-picker'">
      <el-time-picker value-format="HH:ss:mm" v-model="field.defaultValue" :placeholder="field.placeholder" :style="{ width: field.style.width }" />
    </template>
    <!-- 输入框 -->
    <template v-else-if="field.tag === 'el-input'">
      <template v-if="field.tagIcon === 'input'">
        <el-input v-model="field.defaultValue" :placeholder="field.placeholder" :style="{ width: field.style.width }" />
      </template>
      <template v-else-if="field.tagIcon === 'textarea'">
        <el-input v-model="field.defaultValue" type="textarea" :placeholder="field.placeholder" :style="{ width: field.style.width }" />
      </template>
      <template v-else>
        <el-input v-model="field.defaultValue" :placeholder="field.placeholder" :style="{ width: field.style.width }" />
      </template>
    </template>
    <!-- 数字输入框 -->
    <template v-else-if="field.tag === 'el-input-number'">
      <el-input-number v-model="field.defaultValue" :style="{ width: field.style.width }" />
    </template>
    <!-- 单选框 -->
    <template v-else-if="field.tag === 'el-radio-group'">
      <el-radio-group v-model="field.defaultValue">
        <el-radio :value="item.value" v-for="(item, index) in field.options" :key="index">{{ item.label }}</el-radio>
      </el-radio-group>
    </template>
    <!-- 多选框 -->
    <template v-else-if="field.tag === 'el-checkbox-group'">
      <el-checkbox-group v-model="field.defaultValue">
        <el-checkbox :label="item.label" :value="item.value" v-for="(item, index) in field.options" :key="index" />
      </el-checkbox-group>
    </template>
    <!-- 下拉框 -->
    <template v-else-if="field.tag === 'el-select'">
      <el-select v-model="field.defaultValue" :placeholder="field.placeholder" :style="{ width: field.style.width }">
        <el-option v-for="item in field.options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </template>
    <!-- 级联选择 -->
    <template v-else-if="field.tag === 'el-cascader'">
      <el-cascader
        v-model="field.defaultValue"
        :options="field.options"
        :props="field.content"
        :placeholder="field.placeholder"
        :style="{ width: field.style.width }"
      />
    </template>
    <!-- 图片上传 -->
    <template v-else-if="field.tag === 'el-upload'">
      <el-upload class="avatar-uploader" action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :show-file-list="false">
        <img v-if="imageUrl" :src="imageUrl" class="avatar" />
        <div class="upload-div" v-else>
          <!-- <el-icon class="avatar-uploader-icon" v-if="['upload', 'xtfj'].includes(field.tagIcon)"><Plus /></el-icon> -->
          <svg-icon class-name="svg-icon" :icon-class="field.icon"></svg-icon>
        </div>
      </el-upload>
    </template>
    <!-- 缴费特殊 -->
    <template v-else-if="field.tag === 'el-button'">
      <el-button type="primary" :style="{ width: field.style.width }">{{ field.label }}</el-button>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
// props 传入
const props = defineProps<{
  fieldProp: any;
}>();
// const field = ref(props.fieldProp);
const field = computed({
  get() {
    return props.fieldProp;
  },
  set(val) {}
});
const imageUrl = ref('');
</script>
<style lang="scss" scoped>
.upload-div {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe7ee;
  border-radius: 4px;
  font-size: 18px;
}
</style>
