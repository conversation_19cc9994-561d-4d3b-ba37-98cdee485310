<!-- DXF弹窗 -->
<template>
  <div class="DXFDialog-main">
    <el-dialog title="新建/编辑DXF" v-model="editDXFDialog" width="611px" :close-on-click-modal="false" @close="handleClose">
      <el-form :model="newDXFMsg" :rules="newDXFMsgRules" ref="newDXFMsgRef" class="demo-ruleForm" label-position="top">
        <el-form-item label="文件名" prop="fileName">
          <el-input v-model="newDXFMsg.fileName" placeholder="请输入文件名" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="设置导出数据" prop="dxfData">
          <div class="tree-box">
            <el-tree
              :data="ysTree"
              :props="defaultProps"
              show-checkbox
              check-strictly
              default-expand-all
              node-key="id"
              :default-checked-keys="defaultChecked"
              @check-change="handleCheckChange"
            >
            </el-tree>
          </div>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';

// 定义 props
const props = defineProps<{
  editDXFDialogProp: boolean;
  newDXFMsgProp: {
    fileName: string;
    dxfData: Array<{ id: number }>;
  };
  ysTreeProp: Array<{ id: number; typeName: string; list?: Array<any> }>;
}>();

// 定义 emits
const emit = defineEmits(['handleCloseDXFDialog', 'submitDXFMsg']);

// 定义响应式数据
const newDXFMsgRules = ref({
  fileName: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
  dxfData: [{ required: true, message: '请设置导出数据', trigger: 'change' }]
});

const defaultProps = ref({
  children: 'list',
  label: 'typeName'
});

const defaultChecked = ref<number[]>([]);

const newDXFMsgRef = ref(null);

const editDXFDialog = computed(() => props.editDXFDialogProp); // 监听editDXFDialogProp变化，更新editDXFDialog
const newDXFMsg = computed(() => props.newDXFMsgProp); // 监听newDXFMsgProp变化，更新newDXFMsg
const ysTree = computed(() => props.ysTreeProp); // 监听ysTreeProp变化，更新ysTree

// 监听 editDXFDialog 变化
watch(
  () => props.editDXFDialogProp,
  (val) => {
    if (val) {
      // 需要反显树
      if (newDXFMsg.value.dxfData.length !== 0) {
        defaultChecked.value = [];
        newDXFMsg.value.dxfData.forEach((v) => {
          defaultChecked.value.push(v.id);
        });
      }
    }
  },
  { deep: true }
);

// 定义方法
const handleClose = () => {
  emit('handleCloseDXFDialog');
};

const handleCheckChange = (data: { id: number }, checked: boolean, indeterminate: boolean) => {
  if (checked) {
    newDXFMsg.value.dxfData.push(data);
    nextTick(() => {
      if (newDXFMsgRef.value) {
        (newDXFMsgRef.value as any).clearValidate('dxfData');
      }
    });
  } else {
    for (let i = 0; i < newDXFMsg.value.dxfData.length; i++) {
      if (newDXFMsg.value.dxfData[i].id === data.id) {
        newDXFMsg.value.dxfData.splice(i, 1);
        break;
      }
    }
  }
};

const submit = () => {
  if (newDXFMsgRef.value) {
    (newDXFMsgRef.value as any).validate((valid: boolean) => {
      if (valid) {
        emit('submitDXFMsg', newDXFMsg.value);
      } else {
        return false;
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.DXFDialog-main {
}
.tree-box {
  max-height: 400px;
  overflow: auto;
  border: #c0c4cc solid 1px;
  border-radius: 8px;
  padding: 10px;
  width: 100%;
}
</style>
