<!-- 上传 -->
<template>
  <div class="main">
    <div style="position: relative">
      <div class="title">权籍通6.0apk上传</div>
      <div class="upload-btn">
        <el-button type="primary" @click="uploadDialog = true">上传apk</el-button>
      </div>
    </div>

    <div class="table">
      <div class="table-content">
        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column prop="versionCode" label="version_code" width="120"> </el-table-column>
          <el-table-column prop="versionName" label="version_name" width="120"> </el-table-column>
          <el-table-column prop="url" label="download_url">
            <template #default="scope">
              <el-link type="primary" @click="downLoad(scope.row.url)">{{ scope.row.url }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="descs" label="描述" width="240"> </el-table-column>
          <el-table-column prop="updateState" label="是否强制更新" width="140">
            <template #default="scope">
              <span v-if="scope.row.updateState">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="更新时间" width="180"> </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog title="上传apk" v-model="uploadDialog" width="50%" :close-on-click-modal="false">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="110px" class="demo-ruleForm">
        <el-form-item label="是否强制更新" prop="updates">
          <el-radio v-model="ruleForm.updates" value="0">不强制更新</el-radio>
          <el-radio v-model="ruleForm.updates" value="1">强制更新</el-radio>
        </el-form-item>
        <el-form-item label="验证" prop="pwd">
          <el-input v-model="ruleForm.pwd" placeholder="请输入验证"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="descs">
          <el-input type="textarea" :rows="4" placeholder="请输入描述" v-model="ruleForm.descs" style="width: 100%"> </el-input>
        </el-form-item>
        <el-form-item label="文件" prop="file">
          <el-upload
            ref="uploadRef"
            :action="`${baseUrl}/qjt/version/apk/upload?updates=${ruleForm.updates}&descs=${encodeURI(ruleForm.descs)}&pwd=${ruleForm.pwd}`"
            class="upload-demo"
            drag
            :before-upload="beforeAvatarUpload"
            :on-success="handleAvatarSuccess"
            :on-change="changeFile"
            :limit="1"
            :auto-upload="false"
          >
            <el-icon style="font-size: 67px"><UploadFilled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelSubmit">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { apkHistory } from '@/api/project';

// 定义变量
const fileList = ref([]);
const tableData = ref([]);
const radio = ref('0');
const descs = ref('');
const uploadDialog = ref(false);
const ruleForm = ref({
  descs: '', //备注
  file: '', //文件
  updates: '0', //是否强制更新
  pwd: ''
});
const rules = ref({
  descs: [
    { required: true, message: '请输入备注', trigger: 'blur' },
    { min: 5, max: 1000, message: '备注字数不能少于5个字不能大于700', trigger: 'blur' }
  ],
  file: [{ required: true, message: '请上传文件', trigger: 'blur' }],
  updates: [{ required: true, message: '请选择是否强制更新', trigger: 'blur' }],
  pwd: [{ required: true, message: '请输入验证', trigger: 'blur' }]
});
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadRef = ref();
const ruleFormRef = ref();

// 方法部分
const beforeAvatarUpload = () => {
  const isJPG = true;
  return isJPG;
};
const handleAvatarSuccess = (res: any) => {
  uploadRef.value.clearFiles();
  if (res.code == 200) {
    ElMessage({
      message: '恭喜你，上传成功',
      type: 'success'
    });
    ruleForm.value = {
      descs: '', //备注
      file: '', //文件
      updates: '0', //是否强制更新
      pwd: ''
    };
    // uploadDialog.value = false;
    getData();
  } else {
    ElMessage.error(res.msg);
  }
};
const getData = () => {
  apkHistory().then((res) => {
    if (res.code == 200) {
      tableData.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const changeFile = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  ruleForm.value.file = formData as any;
};
//重置上传内容
const cancalForm = () => {
  ruleForm.value = {
    descs: '', //备注
    file: '', //文件
    updates: '0', //是否强制更新
    pwd: ''
  };
  uploadRef.value.uploadFiles = [];
  ruleFormRef.value.resetFields();
};
const cancelSubmit = () => {
  uploadDialog.value = false;
  cancalForm();
};
const submit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      uploadRef.value.submit();
    } else {
      return false;
    }
  });
};
/**
 * 下载
 * @param url 下载地址
 */
const downLoad = (url: string) => {
  window.location.href = `${baseUrl}/qjt/version/apk/newest?obName=${url}`;
};

onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
:deep(.el-table .el-table__header-wrapper th),
:deep(.el-table .el-table__fixed-header-wrapper th) {
  background-color: #1b9af7 !important;
  color: #fff;
}
:deep(.el-upload-list__item .el-progress__text) {
  right: -34px !important;
}

:deep(.el-upload-dragger) {
  border: 1px solid #d9d9d9;
  padding-left: 60px;
  padding-right: 60px;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0px;
}

.main {
  width: 100%;
  height: inherit;
  overflow: auto;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 600;
  padding-top: 20px;
}

.upload-div {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.table {
  display: flex;
  justify-content: center;
}

.table-content {
  width: 95%;
  margin-top: 20px;
}

.flex-row {
  display: flex;
  margin-top: 40px;
  justify-content: center;
}

.descs {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.upload-btn {
  position: absolute;
  top: 10px;
  right: 10px;
}
</style>
