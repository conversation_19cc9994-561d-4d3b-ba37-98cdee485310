<template>
  <div class="main">
    <container-card>
      <div class="title-row">
        <div style="display: flex">
          <div class="text">分销商管理</div>
          <div style="margin-left: 8px; margin-bottom: 4px">
            ( <span>下载总量:</span><span style="color: var(--current-color)">{{ dowmloadCount }}</span>
            )
          </div>
        </div>
        <div>
          <el-link type="primary" @click="addCompany">注册公司</el-link>
        </div>
      </div>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div class="search-div">
          <div class="search-item">
            <div class="label">公司类型</div>
            <div class="search-content">
              <el-select v-model="search.companyType" size="small" placeholder="请选择" @change="getData" style="width: 150px">
                <el-option :value="1" label="公司"></el-option>
                <el-option :value="2" label="个人"></el-option>
              </el-select>
            </div>
          </div>
          <div class="search-item search-end">
            <el-button type="primary" size="small" @click="getData">搜索</el-button>
          </div>
        </div>
        <div class="search-end">
          <el-button type="primary" plain @click="handleOpenShareCode" size="small">生成分享码</el-button>
        </div>
      </div>
      <div>
        <el-table
          :data="tableData"
          :default-sort="{ prop: 'expireTime', order: 'ascending' }"
          style="width: 100%"
          class="table-content"
          border
          :height="tableHeight"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="公司名称" prop="companyName"></el-table-column>
          <el-table-column label="管理人员名称" prop="custName"></el-table-column>
          <el-table-column label="管理人员电话" prop="adminPhone"></el-table-column>
          <el-table-column label="过期时间" sortable prop="expireTime">
            <template #default="scope">
              <span v-if="scope.row.expireTime >= 32505381325000">无限制</span>
              <span v-else>{{ formatDateAndTimeType(scope.row.expireTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最大人数">
            <template #default="scope">
              <span v-if="scope.row.maxUser == *********">无限制</span>
              <span v-else>{{ scope.row.maxUser }}</span>
            </template>
          </el-table-column>
          <el-table-column label="默认模板数量" prop="defaultModule" width="100"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <!-- <el-link type="primary" @click="editCompany(scope.row)" v-if="scope.row.companyId != '1'">编辑</el-link> -->
              <el-link type="primary" @click="handleOpenModal(scope.row)" style="margin-left: 10px">添加模板</el-link>
            </template>
          </el-table-column>
        </el-table>
        <div class="footer-page">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="search.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size="search.pageSize"
            layout="total, sizes, prev, pager, next"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </container-card>

    <!-- 展示下载二维码 -->

    <the-qr-code :visibleDialog="visibleDialog" @closeCode="handelCloseCode"></the-qr-code>

    <!-- 注册公司 -->
    <el-dialog title="注册公司" v-model="addCompanyDialog" width="500px" :close-on-click-modal="false" :before-close="handleCloseAddCompany">
      <el-form :model="registCompany" :rules="registCompanyRule" ref="registCompanyRef" label-position="top" class="demo-ruleForm">
        <el-form-item label="注册类型">
          <el-radio-group v-model="registCompany.companyType" @change="changeCompanyType">
            <el-radio :value="1">公司</el-radio>
            <el-radio :value="2">个人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName" v-if="registCompany.companyType == 1">
          <el-input v-model="registCompany.companyName" placeholder="请输入公司名称" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="客户姓名" prop="custName">
          <el-input v-model="registCompany.custName" placeholder="请输入客户姓名" maxlength="20"></el-input>
        </el-form-item>
        <!-- <el-form-item label="登录密码" prop="password">
          <el-input v-model="registCompany.password" placeholder="请输入登录密码" maxlength="20" show-password></el-input>
        </el-form-item> -->
        <el-form-item label="手机号" prop="username">
          <el-input v-model.trim="registCompany.username" placeholder="请输入手机号" maxlength="11" autocomplete="off" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="公司等级">
          <el-radio-group v-model="registCompany.vipType">
            <el-radio :value="1">个人版</el-radio>
            <el-radio :value="2">专业版</el-radio>
            <el-radio :value="3">企业版</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseAddCompany">取 消</el-button>
          <el-button type="primary" @click="submitAddCompany">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择模块库 -->
    <el-dialog
      title="新建模块"
      v-model="modelTypeDialog"
      :close-on-click-modal="false"
      width="580px"
      @closed="handleModalClosed"
      @open="getDefalutList"
    >
      <div style="margin: 24px 0 8px; font-size: 14px; font-weight: 600">模块库：</div>
      <el-row class="modal-item-contianer">
        <el-col :span="12" v-for="item in defaultList" :key="item.id">
          <div
            class="row-left"
            @click="handleDefaultModel(item)"
            :style="{
              'border': itemId.includes(item.id) ? '1px solid var(--current-color)' : '1px solid #f5f6f7',
              'background-color': isPay ? '#fff9ef' : ''
            }"
          >
            <div class="left-icon">
              <div v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'44px'" :height="'44px'" style="margin-right: 8px" />
              </div>
              <div v-else>
                <svg-icon class-name="svg-item" :icon-class="item.iconUrl" />
              </div>
            </div>
            <div class="left-module">
              <div class="module-info">
                <div class="title">
                  <div
                    class="text"
                    :title="item.moduleName"
                    :style="{ 'text-decoration': item.status == -1 ? 'line-through' : '', color: item.status == -1 ? '#8291a9' : '#161d26' }"
                  >
                    {{ item.moduleName }}
                  </div>
                </div>
                <div class="remark" :title="item.remark">{{ item.remark }}</div>
              </div>
            </div>
            <div class="pay-icon" v-if="isPay">付费</div>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleModalClosed">取 消</el-button>
          <el-button type="primary" @click="submitModelType" :loading="isAddModel">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import TheQrCode from './component/TheQrCode.vue';
import { getDownloadCount } from '@/api/shareCode/index';
import { getCompanyList, editCompany, initAddModule, addModel } from '@/api/control/index';
import { register } from '@/api/login/index';
import { getModuleList, selectDefalutList, addDefaultModule } from '@/api/modal/index';
import { formatDateAndTimeType } from '@/utils/filters';
import { useUserStore } from '@/store/modules/user';

// 用户Store
const userStore = useUserStore();
// 为user添加类型定义
interface UserInfo {
  userId: string | number;
  [key: string]: any;
}
const user = computed<UserInfo>(() => userStore.user as UserInfo);

// 接口定义
interface CompanyItem {
  companyId: string;
  companyName: string;
  custName: string;
  adminPhone: string;
  expireTime: number;
  maxUser: number;
  defaultModule: number;
  adminUserId: string;
  [key: string]: any;
}

interface SearchParams {
  companyName: string;
  pageSize: number;
  pageNum: number;
  companyType: number;
}

interface RegistCompanyData {
  companyName: string;
  companyType: number;
  custName: string;
  username: string;
  // password: string;
  vipType: number;
}

interface ModuleItem {
  id: string;
  moduleName: string;
  iconUrl: string;
  remark: string;
  status?: number;
  [key: string]: any;
}
// 设置table的高度
const tableHeight = ref(window.innerHeight - 300);
// 打开分享二维码弹框
const visibleDialog = ref(false);
// 统计下载量
const dowmloadCount = ref(0);
// 表格数据
const tableData = ref<CompanyItem[]>([]);
// 总计内容
const total = ref(0);
// 搜索参数
const search = reactive<SearchParams>({
  companyName: '',
  pageSize: 10,
  pageNum: 1,
  companyType: 1
});

// 注册公司弹窗
const addCompanyDialog = ref(false);
const registCompanyRef = ref<FormInstance>();
const baseUrl = import.meta.env.VITE_APP_BASE_API || '';

// 注册公司数据
const registCompany = reactive<RegistCompanyData>({
  companyName: '',
  companyType: 1,
  custName: '',
  username: '',
  // password: '',
  vipType: 1 // 默认个人
});

// 注册表单校验规则
const registCompanyRule = reactive({
  custName: [
    { required: true, trigger: 'blur', message: '请输入客户姓名' },
    { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (/^(13[0-9]|14[0-9]|15[0-9]|16[6]|18[0-9]|19[6,9]|17[0-9])\d{8}$/i.test(value) === false) {
          callback(new Error('请输入正确的手机号'));
        } else {
          // 校验通过
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, trigger: 'blur', message: '请输入您的密码' },
    { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
  ],
  companyName: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入组织名称'
    }
  ]
});

// 当前添加模板的一项
const current = ref<CompanyItem | null>(null);
// 模块类型弹窗
const modelTypeDialog = ref(false);
// 默认模块的列表
const defaultList = ref<ModuleItem[]>([]);
// 当前选中的模块的id
const itemId = ref<string[]>([]);
// 是否正在添加模块
const isAddModel = ref(false);
// 是否是付费模块
const isPay = ref(false);

// 打开二维码签名
const handleOpenShareCode = () => {
  visibleDialog.value = true;
};

// 关闭二维码签名
const handelCloseCode = () => {
  visibleDialog.value = false;
};

// 获取下载量
const fetchDownloadCount = async (userId: string | number) => {
  try {
    //

    const res = await getDownloadCount(userId);

    if (res && res.code === 200) {
      dowmloadCount.value = res.data;
    }
  } catch (error) {}
};

// 获取分销商数据
const getData = async () => {
  try {
    // const userid = userStore.userId;
    const userId = user.value.userId;
    // const res = await getDownloadCount(userId);
    const param = {
      promoterUseId: userId,
      companyType: search.companyType,
      pageNum: search.pageNum,
      pageSize: search.pageSize
    };
    const res = await getCompanyList(param);

    if (res.code === 200) {
      tableData.value = res.data.rows;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

// 切换页面大小
const handleSizeChange = (val: number) => {
  search.pageNum = 1;
  search.pageSize = val;
  getData();
};

// 切换页码
const handleCurrentChange = (val: number) => {
  search.pageNum = val;
  getData();
};

// 注册公司
const addCompany = () => {
  addCompanyDialog.value = true;
};

// 关闭添加公司弹窗
const handleCloseAddCompany = () => {
  Object.assign(registCompany, {
    companyName: '',
    companyType: 1,
    custName: '',
    username: ''
    // password: ''
  });
  nextTick(() => {
    registCompanyRef.value?.clearValidate();
  });
  addCompanyDialog.value = false;
};

// 提交添加公司
const submitAddCompany = async () => {
  if (!registCompanyRef.value) return;

  await registCompanyRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const parmas = {
          companyName: registCompany.companyName,
          companyType: registCompany.companyType,
          custName: registCompany.custName,
          username: registCompany.username,
          // password: `qjt${registCompany.username.substring(5, 11)}`,
          ifCaptcha: false,
          promoterUseId: userStore.userId,
          vipType: registCompany.vipType
        };

        const res = await register(parmas);

        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '注册成功'
          });
          Object.assign(registCompany, {
            companyName: '',
            companyType: 1,
            custName: '',
            username: ''
            // password: ''
          });
          nextTick(() => {
            registCompanyRef.value?.clearValidate();
          });
          addCompanyDialog.value = false;
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {}
    } else {
      return false;
    }
  });
};

// 公司类型改变
const changeCompanyType = (val: number) => {
  if (val === 2) {
    nextTick(() => {
      registCompanyRef.value?.clearValidate('companyName');
    });
  }
};

// 打开添加模块弹窗
const handleOpenModal = (row: CompanyItem) => {
  current.value = row;
  modelTypeDialog.value = true;
};

// 获取默认模块列表
const getDefalutList = async () => {
  try {
    const res = await selectDefalutList();
    if (res.code === 200) {
      defaultList.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};

// 处理当前选中的模块
const handleDefaultModel = (item: ModuleItem) => {
  if (itemId.value.includes(item.id)) {
    const index = itemId.value.indexOf(item.id);
    itemId.value.splice(index, 1);
  } else {
    itemId.value.push(item.id);
  }
};

// 关闭添加模块弹窗
const handleModalClosed = () => {
  itemId.value = [];
  modelTypeDialog.value = false;
};

// 提交模块类型
const submitModelType = async () => {
  if (itemId.value.length === 0) {
    ElMessage.error('请选择模块！');
    return false;
  }

  isAddModel.value = true;
  try {
    if (!current.value) {
      ElMessage.error('当前公司数据异常');
      isAddModel.value = false;
      return;
    }

    const params = {
      companyId: current.value.companyId,
      adminUserId: current.value.adminUserId
    };

    const res = await addModel(params, itemId.value);
    isAddModel.value = false;

    if (res.code === 200) {
      ElMessage.success(res.data);
      modelTypeDialog.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    isAddModel.value = false;
  }
};
// 生命周期钩子
onMounted(() => {
  const userId = user.value.userId;
  fetchDownloadCount(userId);
  getData();
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  .title-row {
    display: flex;
    justify-content: space-between;
    // height: 70px;
    border-bottom: 1px solid #dbe7ee;
    align-items: center;
    .text {
      height: 22px;
      font-size: 16px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
      line-height: 22px;
      margin-bottom: 4px;
    }
  }
  .flex-row {
    display: flex;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    margin: 8px;
    .flex-download {
      font-size: 14px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
    }
    .flex-code {
      font-size: 14px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
      cursor: pointer;
    }
  }
  .search-div {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      .label {
        width: 80px;
        // text-align: right;
      }
      .search-content {
        margin-left: 10px;
        flex: 1;
      }
    }
    .search-end {
      // justify-content: flex-end;
      margin-left: 16px;
    }
  }
  .table-content {
    margin-top: 10px;
    height: calc(100% - 200px);
    overflow: auto;
  }
  .footer-page {
    margin-top: 10px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
  }
}
.modal-item-contianer {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  .row-left {
    display: flex;
    // width: 70%;
    align-items: center;
    border-radius: 6px;
    margin-right: 8px;
    margin-bottom: 10px;
    position: relative;
    .left-icon {
      // width: 10%;
      min-width: 48px;
      // padding: 8px;
      text-align: left;
      margin-left: 10px;
      position: relative;
      .svg-item {
        width: 44px;
        height: 44px;
      }
      .modal-icon-svg {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 0px;
      }
      .modal-icon-pic {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 8px;
      }
    }
    .left-module {
      width: 30%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-info {
        // width: 35%;
        min-width: 156px;
        .title {
          display: flex;
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 600;
          min-width: 156px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .stop {
            width: 48px;
            height: 20px;
            background: rgba(255, 61, 87, 0.1);
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #ff3d57;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .defalut {
            width: 60px;
            height: 20px;
            background: #e6ebf5;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #8291a9;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing {
            width: 48px;
            height: 20px;
            background: #f2752157;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #e53e07af;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
          .testing-end {
            width: 60px;
            height: 20px;
            background: #adffbf;
            border-radius: 4px 4px 4px 4px;
            margin-left: 12px;
            text-align: center;
            line-height: 20px;
            span {
              color: #089145;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-size: 12px;
              font-weight: 400;
              padding: 4px;
            }
          }
        }
        .remark {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 12px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .left-span {
      width: 20%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-people {
        width: 25%;
        min-width: 156px;
        .creater {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .people {
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &:hover {
      background-color: #f6f7f8;
      border-radius: 6px;
      // margin-top: 4px;
    }
    .pay-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 25px;
      background: linear-gradient(180deg, #ffa509 0%, #f1890a 100%);
      border-top-right-radius: 50%;
      border-bottom-left-radius: 50%;
      object-fit: cover;
      color: #fff;
      font-weight: 500;
      letter-spacing: 1px;
      line-height: 25px;
    }
  }
}
</style>
