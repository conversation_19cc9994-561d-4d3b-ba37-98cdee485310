<!-- 快捷表达式 -->
<template>
  <div class="fastExpression-main">
    <el-dialog title="快捷表达式" v-model="dialogVisible" width="720px" :close-on-click-modal="false" :before-close="handleClose">
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">结构树</span></div>
          <div class="content">
            <el-tree
              ref="tree"
              :data="treeList"
              :props="defaultProps"
              highlight-current
              default-expand-all
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
              class="tree-div"
            >
              <template v-slot="{ data }">
                <div class="tree-row">
                  <div class="tree-row-left">
                    <div v-if="data.iconUrl && data.iconUrl.substring(data.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                      <el-image style="width: 20px; height: 20px" :src="`${baseUrl}${data.iconUrl}?token=${token}`" :fit="`cover`" />
                    </div>
                    <div v-else>
                      <svg-icon class-name="svg-item" :icon-class="data.iconUrl" />
                    </div>
                    <span style="margin-left: 4px">{{ data.typeName }}</span>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="center">
          <div class="title-div"><span class="normal-sapn">属性组</span></div>
          <div class="content">
            <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
            <template v-else>
              <div
                class="flex-row"
                v-for="(item, index) in attrbutionGroup"
                :class="{ 'flex-active': item.checked }"
                :key="index"
                @click="changeAtt(item)"
              >
                <div class="label">
                  <span>{{ item.typeName }}</span>
                  <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalPoint'"
                    >(点)</span
                  >
                  <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalLine'"
                    >(线)</span
                  >
                  <span style="padding-left: 4px" v-if="item.groupScope == 1 && item.ruleAttribution && item.ruleAttribution.type == 'graphicalArea'"
                    >(面)</span
                  >
                </div>
                <div class="ico">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="right">
          <div class="title-div"><span class="normal-sapn">字段</span></div>
          <div class="content">
            <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
            <template v-else>
              <div v-for="(item, index) in fieldList" :key="index">
                <div class="flex-row-spe" v-if="item.valueMethod == 'idCardScan'">
                  <div class="spe-title">
                    <!-- 身份证识别大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked }"
                    v-for="(ite, idx) in idCardScanList"
                    :key="idx"
                    @click="chooseField(item, ite)"
                  >
                    <!-- 身份证识别中的某个字段 -->
                    <div>
                      <span>{{ ite.enName }}</span
                      ><span style="padding-left: 4px">({{ ite.label }})</span>
                    </div>
                    <el-checkbox v-model="ite.checked" :max="1" style="margin-right: 8px" @change="chooseField(item, ite)"></el-checkbox>
                  </div>
                </div>
                <!-- 添加银行卡识别的字段 -->
                <div class="flex-row-spe" v-else-if="item.valueMethod === 'xtBankCard'">
                  <div class="spe-title">
                    <!-- 银行卡识别大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked }"
                    v-for="(ite, idx) in yhkTypeList"
                    :key="idx"
                    @click="chooseField(item, ite)"
                  >
                    <!-- 银行卡识别中的某个字段 -->
                    <div>
                      <span>{{ ite.enName }}</span
                      ><span style="padding-left: 4px">({{ ite.label }})</span>
                    </div>
                    <el-checkbox v-model="ite.checked" :max="1" style="margin-right: 8px" @change="chooseField(item, ite)"></el-checkbox>
                  </div>
                </div>
                <!-- 系统动物识别的内容 -->
                <div class="flex-row-spe" v-else-if="item.valueMethod == 'xtdwsb'">
                  <div class="spe-title">
                    <!-- 大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked }"
                    v-for="(ite, idx) in DWSBOptions"
                    :key="idx"
                    @click="chooseField(item, ite)"
                  >
                    <!-- 身份证识别中的某个字段 -->
                    <div>
                      <span>{{ ite.enName }}</span
                      ><span style="padding-left: 4px">({{ ite.label }})</span>
                    </div>
                    <el-checkbox v-model="ite.checked" :max="1" style="margin-right: 8px" @change="chooseField(item, ite)"></el-checkbox>
                  </div>
                </div>
                <!-- 植物识别 -->
                <div class="flex-row-spe" v-else-if="item.valueMethod == 'xtzwsb'">
                  <div class="spe-title">
                    <!-- 大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked }"
                    v-for="(ite, idx) in ZWSBOptions"
                    :key="idx"
                    @click="chooseField(item, ite)"
                  >
                    <!-- 身份证识别中的某个字段 -->
                    <div>
                      <span>{{ ite.enName }}</span
                      ><span style="padding-left: 4px">({{ ite.label }})</span>
                    </div>
                    <el-checkbox v-model="ite.checked" :max="1" style="margin-right: 8px" @change="chooseField(item, ite)"></el-checkbox>
                  </div>
                </div>
                <!-- 表格字段 -->
                <div class="flex-row-spe" v-else-if="item.valueMethod == 'xttable'">
                  <div class="spe-title">
                    <!-- 大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>

                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked, 'no-span': ['xtaudio', 'xtfj', 'xtvideo', 'xtsjjt'].includes(ite.valueMethod) }"
                    v-for="(ite, idx) in item.attribution.children"
                    :key="idx"
                    @click="chooseField(item, ite)"
                  >
                    <!-- 身份证识别中的某个字段 -->
                    <div>
                      <span>{{ ite.fieldName }}</span
                      ><span style="padding-left: 4px">({{ ite.fieldCn }})</span>
                    </div>
                    <el-checkbox v-model="ite.checked" :max="1" style="margin-right: 8px" @change="chooseField(item, ite)"></el-checkbox>
                  </div>
                </div>
                <!-- 单个字段的情况 -->
                <div
                  v-else
                  class="flex-row"
                  :class="{
                    'flex-active': item.id == checkedField.id,
                    'no-span': ['xtaudio', 'xtfj', 'xtvideo', 'xtsjjt'].includes(item.valueMethod)
                  }"
                  @click="chooseField(item)"
                >
                  <div class="label-field">
                    <!-- 属性组字段 -->
                    <div>{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                    <el-checkbox v-model="item.checked" :max="1" style="margin-right: 8px" @change="chooseField(item)"></el-checkbox>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitField">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue';
import { selectRules } from '@/api/modal';
import SvgIcon from '../../components/SvgIcon/index.vue';
import { getToken } from '@/utils/auth';
import { useRouter } from 'vue-router';

const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const token = getToken();
const router = useRouter();

// 类型定义
interface TreeNode {
  id: number;
  typeName: string;
  iconUrl: string;
  list: TreeNode[];
  fieldGroupModelList: AttributeGroup[];
  // 其他属性根据实际数据补充
}

interface AttributeGroup {
  id: number;
  typeName: string;
  groupScope: number;
  ruleAttribution?: { type: string };
  fieldModelList: FieldItem[];
  checked: boolean;
  // 其他属性根据实际数据补充
}

interface FieldItem {
  id: number;
  fieldName: string;
  fieldCn: string;
  valueMethod: string;
  fieldType?: string;
  checked: boolean;
  attribution?: { children: FieldItem[] };
  // 其他属性根据实际数据补充
}

// Props 定义
const props = defineProps({
  mapFieldDialog: { type: Boolean, required: true, default: false },
  isFileName: { type: Boolean, required: true, default: false },
  isYsName: { type: Boolean, required: true, default: false },
  moduleId: { type: Number, default: 0 }
});

const dialogVisible = computed({
  get() {
    return props.mapFieldDialog;
  },
  set(value) {}
});

// Emits 定义
const emit = defineEmits<{
  (e: 'submitFastExp', expression: string): void;
  (e: 'handleCloseFast'): void;
}>();

// 响应式数据
const tree = ref<any>();
const treeList = ref<TreeNode[]>([]);
const defaultProps = ref({
  children: 'list',
  label: 'typeName'
});
const attrbutionGroup = ref<AttributeGroup[]>([]);
const fieldList = ref<FieldItem[]>([]);
const checkedYS = ref<TreeNode>({} as TreeNode);
const checkedAtt = ref<AttributeGroup>({} as AttributeGroup);
const checkedField = ref<FieldItem>({} as FieldItem);
const checkedFiledList = ref<FieldItem[]>([]);
const mapFielType = ref<number>(0);
const fieldCn = ref<string>('');
const idCardScanList = ref([
  { label: '姓名', value: 0, enName: 'xm', checked: false },
  { label: '性别', value: 1, enName: 'xb', checked: false },
  { label: '民族', value: 2, enName: 'mz', checked: false },
  { label: '出生日期', value: 3, enName: 'csrq', checked: false },
  { label: '住址', value: 4, enName: 'zz', checked: false },
  { label: '身份证号', value: 5, enName: 'sfzh', checked: false },
  { label: '签发机关', value: 6, enName: 'qfjg', checked: false },
  { label: '有效期限', value: 7, enName: 'yxqx', checked: false }
]);
const yhkTypeList = ref([
  { value: 0, label: '银行卡名称', enName: 'yhkName', checked: false },
  { value: 1, label: '银行卡卡号', enName: 'yhkNum', checked: false },
  { value: 2, label: '银行卡类型', enName: 'yhkType', checked: false },
  { value: 3, label: '银行卡照片', enName: 'yhkImg', checked: false }
]);
const DWSBOptions = ref([
  { value: 0, label: '名称', enName: 'dwmc', checked: false },
  { value: 1, label: '图片', enName: 'dwtp', checked: false },
  { value: 2, label: '类型', enName: 'dwlx', checked: false },
  { value: 3, label: '描述', enName: 'dwms', checked: false }
]);
const ZWSBOptions = ref([
  { value: 0, label: '名称', enName: 'zwmc', checked: false },
  { value: 1, label: '图片', enName: 'zwtp', checked: false },
  { value: 2, label: '类型', enName: 'zwlx', checked: false },
  { value: 3, label: '描述', enName: 'zwms', checked: false }
]);

// 监听弹框显示状态
watch(
  () => props.mapFieldDialog,
  (val) => {
    if (val) {
      init();
    }
  },
  { deep: true }
);
const init = async () => {
  const params: any = { moduleId: props.moduleId };
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId) params.companyId = companyId;

  try {
    const res = await selectRules(params);
    if (res.code === 200) {
      treeList.value = res.data;
      attrbutionGroup.value = res.data[0].fieldGroupModelList;
      checkedYS.value = res.data[0];
      fieldList.value = [];
      // 设置当前选中节点
      nextTick(() => tree.value?.setCurrentKey(res.data[0].id));
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
};

// 改变选中属性组
const changeAtt = (item: AttributeGroup) => {
  attrbutionGroup.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  checkedAtt.value = item;

  // this.fieldList = item.fieldModelList;
  // 当选中属性组的时候，给属性组中的字段添加checked 属性，方便来判断是否选中该字段
  if (item.fieldModelList && item.fieldModelList.length > 0) {
    const fieldModelList = item.fieldModelList.map((field) => {
      if (field.valueMethod == 'idCardScan') {
        idCardScanList.value.forEach((v: any) => {
          v.checked = false;
        });
      } else if (field.valueMethod === 'xtBankCard') {
        yhkTypeList.value.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xtdwsb') {
        DWSBOptions.value.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xtzwsb') {
        ZWSBOptions.value.forEach((v) => {
          v.checked = false;
        });
      } else if (field.valueMethod == 'xttable') {
        if (field.attribution && field.attribution.children.length > 0) {
          field.attribution.children.forEach((v) => {
            v.checked = false;
          });
        }
      } else {
        field.checked = false;
      }
      return field;
    });
    fieldList.value = fieldModelList;
  }
};
// 改变选中某个字段
const changeField = (item: FieldItem) => {
  if (item.checked) {
    let flag = false;
    checkedFiledList.value.forEach((v) => {
      if (v.id == item.id) {
        flag = true;
      }
    });
    // 库里面没有的时候才加进去
    if (!flag) {
      if (mapFielType.value == 1) {
        //映射字段可以多选
        checkedFiledList.value.push(item);
      } else if (mapFielType.value == 2) {
        //映射源
        if (checkedFiledList.value.length == 0) {
          checkedFiledList.value.push(item);
          fieldCn.value = item.fieldCn;
        } else {
          ElMessage.error('映射数据源的时候只能选择一个值!!!');
          item.checked = false;
          return;
        }
      }
    }
  } else {
    checkedFiledList.value.forEach((v) => {
      if (v.id == item.id) {
        checkedFiledList.value.splice(v, 1);
      }
    });
  }
};
// 提交选中的快捷表达式
const submitField = () => {
  if (checkedYS.value.id && checkedAtt.value.id && checkedField.value.id) {
    let expression = '';
    // 根据不用的数据类型 来判断使用时toDb 还是 toStr()
    // 目前只有选择的字段是数字的使用toDb()  其他都是toStr()
    let str = '.toStr()';
    // if(this.checkedField.fieldType && this.checkedField.fieldType == 'Double'){
    //   str = '.toDb()'
    // }
    if (checkedField.value.fieldType && checkedField.value.fieldType == 'Double') {
      str = '.toDb()';
    } else if (checkedField.value.fieldType && checkedField.value.fieldType == 'Pic') {
      str = '.toPicture("",2,0,130,0)';
    } else {
      str = '.toStr()';
    }
    if (checkedAtt.value.groupScope == 1) {
      // 共享组的情况
      expression = handleShareExpressionText(checkedYS.value, checkedAtt.value, checkedField.value, str);
    } else {
      if (checkedYS.value.levelNum == 1) {
        // 根节点
        expression = handleRootExpressionText(checkedYS.value, checkedAtt.value, checkedField.value, str);
      } else {
        // 非根节点  -- 房产或者四至可以赋值多个值出来
        expression = handleNomalExpressionText(checkedYS.value, checkedAtt.value, checkedField.value, str);
      }
    }
    emit('submitFastExp', expression);
    checkedYS.value = {} as TreeNode;
    checkedAtt.value = {} as AttributeGroup;
    checkedField.value = {} as FieldItem;
  } else {
    ElMessage.error('请选择对应的字段');
  }
};
// 处理共享组的表达式
const handleShareExpressionText = (tree: any, group: any, field: any, str: any) => {
  let expression = '';
  // getFactorField("宗地",0,"面","权利人",0,"qm").toStr()
  let type = '面';
  if (group.groupScope == 1 && group.ruleAttribution && group.ruleAttribution.type == 'graphicalPoint') {
    type = '点';
  } else if (group.groupScope == 1 && group.ruleAttribution && group.ruleAttribution.type == 'graphicalLine') {
    type = '线';
  } else {
    type = '面';
  }
  if (field.valueMethod == 'idCardScan' || field.valueMethod === 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else if (props.isYsName) {
      expression = `getSpeFieldColStr("${tree.typeName}",0,"${group.typeName}","${field.fieldName}_${field.checkedIndex}")`;
    } else {
      //身份证识别特殊处理
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    const text = handleTableFieldType(field, field.checkedIndex);
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${text}}`;
    } else if (props.isYsName) {
      // TODO 这里是何意
      expression = `getFactorField("${tree.typeName}",0,"${group.typeName}","${field.fieldName}").getColFieldStr(${field.checkedIndex}")`;
    } else {
      //表格特殊处理
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${text}`;
    }
  } else {
    if (props.isFileName) {
      //文件名表达式要加#{}
      expression = `#{getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${str}}`;
    } else if (props.isYsName) {
      expression = `getSpeFieldColStr("${tree.typeName}",0,"${group.typeName}","${field.fieldName}")`;
    } else {
      expression = `getFactorField("${tree.typeName}",0,"${type}","${group.typeName}",0,"${field.fieldName}")${str}`;
    }
  }
  return expression;
};
// 处理跟节点的表达式
const handleRootExpressionText = (tree: any, group: any, field: any, str: any) => {
  let expression = '';
  // getCommonField("宗地.宗地信息.zdmc").toStr()
  if (field.valueMethod == 'idCardScan' || field.valueMethod === 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else {
      //身份证识别特殊处理
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    const text = handleTableFieldType(field, field.checkedIndex);
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${text}}`;
    } else {
      //表格特殊处理
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${text}`;
    }
  } else {
    if (props.isFileName) {
      //文件名表达式要加#{}
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}}`;
    } else {
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}`;
    }
  }
  return expression;
};
// 处理非根节点
const handleNomalExpressionText = (tree: any, group: any, field: any, str: any) => {
  let expression = '';
  // getFlatField("宗地",0,"宗地信息",0,"zdmc").toStr()
  if (field.valueMethod == 'idCardScan' || field.valueMethod === 'xtBankCard' || field.valueMethod == 'xtzwsb' || field.valueMethod == 'xtdwsb') {
    if (props.isFileName) {
      //身份证识别特殊处理
      expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}}`;
    } else {
      //身份证识别特殊处理
      expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}_${field.checkedIndex}")${str}`;
    }
  } else if (field.valueMethod == 'xttable') {
    const text = handleTableFieldType(field, field.checkedIndex);
    if (props.isFileName) {
      //表格特殊处理
      expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${text}}`;
    } else {
      //表格特殊处理
      expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${text}`;
    }
  } else {
    //2025/7/8 年修改成 非共享组且不是特殊字段的时候试用 getCommonField
    if (props.isFileName) {
      //文件名表达式要加#{}
      // expression = `#{getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${str}}`;
      expression = `#{getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}}`;
    } else {
      // expression = `getFlatField("${tree.typeName}",0,"${group.typeName}",0,"${field.fieldName}")${str}`;
      expression = `getCommonField("${tree.typeName}.${group.typeName}.${field.fieldName}")${str}`;
    }
  }
  return expression;
};
// 处理表格中的字段的数据类型
const handleTableFieldType = (field: any, fieldName: any) => {
  let str = `.getTableData(0,"${field.checkedIndex}")`;
  if (field.attribution && field.attribution.children.length > 0) {
    field.attribution.children.forEach((c: any) => {
      if (c.fieldName == fieldName) {
        if (c.fieldType && c.fieldType == 'Pic') {
          str = `.getTableField(0,"${field.checkedIndex}").toPicture("",2,0,130,0)`;
        }
      }
    });
  }
  return str;
};
// 选中某个字段
const chooseField = (item: any, ite: any) => {
  item.checkedIndex = '';
  // 当一个族中同时存在身份证，动植物识别 选中第一个之后 在选第二个不会把值清空 故 重新勾选的时候全部置为false
  idCardScanList.value.forEach((v) => {
    v.checked = false;
  });
  yhkTypeList.value.forEach((v) => {
    v.checked = false;
  });
  DWSBOptions.value.forEach((v) => {
    v.checked = false;
  });
  ZWSBOptions.value.forEach((v) => {
    v.checked = false;
  });
  // 特殊情况  有表格和有数据的时候怎么办。
  if (item.valueMethod == 'xttable' && item.attribution && item.attribution.children.length > 0) {
    item.attribution.children.forEach((v: any) => {
      v.checked = false;
    });
  }

  if (item.valueMethod == 'idCardScan') {
    idCardScanList.value.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField.value = item;
  } else if (item.valueMethod == 'xtBankCard') {
    yhkTypeList.value.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField.value = item;
  } else if (item.valueMethod == 'xtdwsb') {
    DWSBOptions.value.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField.value = item;
  } else if (item.valueMethod == 'xtzwsb') {
    ZWSBOptions.value.forEach((v) => {
      v.checked = false;
    });
    ite.checked = true;
    item.checkedIndex = ite.value;
    checkedField.value = item;
  } else if (item.valueMethod == 'xttable') {
    if (item.attribution && item.attribution.children.length > 0) {
      item.attribution.children.forEach((v) => {
        v.checked = false;
      });
    }
    ite.checked = true;
    item.checkedIndex = ite.fieldName;
    checkedField.value = item;
  } else {
    // 当身份证识别中有签名和指纹的情况，选择身份证识别中的1/10的字段，签名和指纹也还可以选中
    // 身份证识别和字段只能选一个字段 所以循环置为false
    idCardScanList.value.forEach((v) => {
      v.checked = false;
    });
    yhkTypeList.value.forEach((v) => {
      v.checked = false;
    });
    // 去过是其他属性组的字段
    fieldList.value.forEach((v) => {
      v.checked = false;
    });
    item.checked = true;
    checkedField.value = item;
  }
};
const handleNodeClick = (data: any) => {
  attrbutionGroup.value = data.fieldGroupModelList;
  checkedYS.value = data;
  fieldList.value = [];
};
const handleClose = () => {
  emit('handleCloseFast');
};
</script>

<style lang="scss">
.el-color-dropdown__link-btn {
  display: none;
}
</style>
<style lang="scss" scoped>
.fastExpression-main {
  // width: 100%;
  // height: 100%;
  .dialog-box {
    height: 300px;
    border: 1px solid rgba(219, 231, 238, 1);
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    .left {
      flex: 2;
    }
    .center {
      flex: 2;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .right {
      flex: 3;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .title-div {
      width: 100%;
      height: 37px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      .normal-sapn {
        margin-left: 20px;
      }
    }
    .content {
      height: calc(100% - 37px);
      padding: 0px 8px;
      width: calc(100% - 16px);
      margin-left: 8px;
      overflow: auto;
      :deep(.el-tree-node__content) {
        height: 32px;
        font-size: 12px;
      }
      .empty-span {
        color: #909399;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      .flex-row {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .label {
          font-size: 12px;
          padding-left: 12px;
          width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .label-field {
          font-size: 12px;
          padding-left: 12px;
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        }
        .ico {
          padding-right: 8px;
        }
      }
      .flex-row:hover {
        background-color: #f5f7fa;
      }
      .flex-active {
        background: #edf4fb;
      }
      .no-span {
        color: #d3d3d3 !important;
        cursor: not-allowed;
      }
      .flex-row-spe {
        height: auto;
        .spe-title {
          color: #d3d3d3;
          padding-left: 12px;
          cursor: not-allowed;
        }
        .spe-item {
          height: 32px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          font-size: 12px;
          padding-left: 24px;
        }
        .spe-item:hover {
          background: #edf4fb;
        }
        .spe-item-active {
          background: #edf4fb;
        }
      }
      .tree-div {
        height: calc(100% - 49px);
        overflow: auto;
        width: 100%;
        .tree-row {
          height: 44px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          font-size: 12px;
          width: 100%;
          // padding: 0px 16px;
          .tree-row-left {
            display: flex;
            flex-direction: row;
            align-items: center;
            .tree-img {
              width: 16px;
              height: 16px;
              margin-right: 8px;
              background: #fff;
            }
            .svg-item {
              width: 16px;
              height: 16px;
              // margin-right: 8px;
              // color: #333;
              vertical-align: middle;
            }
          }
          .tree-row-handle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
          }
          .tree-row-handle:hover {
            background: var(--current-color);
            color: #fff;
          }
        }
        .active-tree {
          background: var(--current-color);
          color: #fff;
        }
      }
    }
    /*滚动条样式*/
    .content::-webkit-scrollbar {
      width: 4px;
    }
    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    .content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
}
</style>
