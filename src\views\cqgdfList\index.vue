<!-- 超期过渡费列表发放 -->
<template>
  <container-card>
    <div class="cqgdfList-main app-container">
      <!-- 列表 -->
      <template v-if="type === 1">
        <list ref="listRef" />
      </template>
      <!-- 发放 -->
      <template v-else-if="type === 2">
        <add ref="addRef" />
      </template>
      <!-- 详情 -->
      <template v-else>
        <detail ref="detailRef" />
      </template>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, reactive, provide } from 'vue';
import list from './components/list.vue';
import detail from './components/detail.vue';
import add from './components/add.vue';

// 类型定义
interface BtchMsg {
  [key: string]: any;
}

// 组件引用
const listRef = ref();
const detailRef = ref();
const addRef = ref();

// 数据定义
const type = ref<number>(1); // 1 列表 2发放 3详情
const btchMsg = reactive<BtchMsg>({});

// 改变类型
const changeType = (newType: number, obj?: any): void => {
  type.value = newType;
  if (newType === 3 && obj) {
    Object.assign(btchMsg, obj);
  }
};

// 获取批次信息
const getBtchMsg = (): BtchMsg => {
  return btchMsg;
};

// 提供方法给子组件
provide('changeType', changeType);
provide('getBtchMsg', getBtchMsg);

// 暴露方法给父组件
defineExpose({
  changeType,
  getBtchMsg
});
</script>

<style lang="scss" scoped></style>
