<!-- 给外部ifraem嵌入的页面 -->
<template>
  <div class="externalDataManager-main">
    <autoProject ref="autoProjectRef" v-if="show" :defaultMoudleId="moudleId" :isIfrem="true"></autoProject>
  </div>
</template>

<script lang="ts" setup>
import { loginFirst } from '@/api/login';
import autoProject from '@/views/autoProject/index.vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const route = useRoute();
// ---定义变量 ---
const moudleId = ref(''); // 模块id
const loginFrom = ref({
  username: '',
  password: '',
  from: 'web',
  rememberMe: false,
  uuid: ''
});
const show = ref(false);

// --- 初始化 ---
onMounted(() => {
  moudleId.value = route.query.moudleId as string;
  loginFrom.value.username = route.query.userName as string;
  loginFrom.value.password = route.query.password as string;
  login();
});

function login() {
  loginFirst(loginFrom.value).then((res) => {
    if (res.code == 200) {
      const parmas = {
        username: loginFrom.value.username,
        companyId: res.data.sysCompanyList[0].companyId,
        password: loginFrom.value.password,
        from: 'web'
      };
      userStore
        .login(parmas)
        .then(() => {
          show.value = true;
        })
        .catch(() => {});
    } else {
    }
  });
}
</script>
<style lang="scss" scoped>
.externalDataManager-main {
  width: 100%;
  height: 100%;
}
</style>
