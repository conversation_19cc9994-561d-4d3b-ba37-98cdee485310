<template>
  <el-row class="bar">
    <el-row class="topTab">
      <el-col :span="12" :class="currentTab === 1 ? 'no-focus-tab' : 'focus-tab'">
        <div @click="currentTab = 0">组件</div>
      </el-col>
      <el-col :span="12" :class="currentTab === 0 ? 'no-focus-tab' : 'focus-tab'">
        <div @click="currentTab = 1">图层</div>
      </el-col>
    </el-row>
    <div class="mt-45px w-full">
      <div v-show="currentTab === 0" v-for="group in options" :key="group.name">
        <div style="line-height: 45px; cursor: pointer; box-shadow: 0 1px 2px #2b3340; display: flex" @click="group.opened = !group.opened">
          <div style="display: inline-block; text-indent: 1em; width: 170px">{{ group.name }}</div>
          <div style="display: inline-block">
            <el-icon>
              <ArrowDown v-if="group.opened" />
              <ArrowRight v-else />
            </el-icon>
          </div>
        </div>
        <el-row :gutter="2" v-show="group.opened">
          <el-col :span="12" v-for="(item, index) in group.children" :key="item.name + index">
            <div
              draggable="true"
              :config="JSON.stringify(item)"
              @dragstart="dragStart"
              style="background-color: #3f4b5f; height: 70px; text-align: center; margin-top: 2px"
            >
              <div style="line-height: 40px">
                <svg-icon v-if="item.icon" :icon-class="`${item.icon}`" style="width: 20px; height: 20px" />
                <el-icon v-else><QuestionFilled /></el-icon>
              </div>
              <div style="font-size: 13px">{{ item.name }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-show="currentTab === 1" class="w-full">
        <div v-show="selectedComponents.length === 0" style="text-align: center; line-height: 50px; width: 200px; z-index: 999; margin-top: 200px">
          无图层
        </div>
        <el-row
          v-for="(item, index) in selectedComponents"
          :key="item.keyId"
          class="selectedItem"
          :style="{ background: currentCptIndex === index ? '#3F4B5F' : '#353f50' }"
        >
          <el-col :span="15" @click="showConfigBar($event, item, index)" style="padding-left: 8px">{{ item.cptTitle }}</el-col>
          <el-col :span="5" style="text-align: center">
            <el-icon @click="copyCpt(item)"><CopyDocument /></el-icon>
            <el-icon style="margin-left: 4px" @click="delCpt(item, index)"><Delete /></el-icon>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-row>
</template>

<script setup lang="ts">
import optionsData from './utils/options';
import SvgIcon from '@/components/SvgIcon/index.vue';

// --- props ---
interface Props {
  selectedComponents: Array<any>;
  currentCptIndex: number;
}

const props = withDefaults(defineProps<Props>(), {
  selectedComponents: () => [],
  currentCptIndex: 0
});

//  ---定义emit---
const emit = defineEmits<{
  (e: 'dragStart', copyDom: any): void;
  (e: 'showConfigBar', data: any, item: any, index: number): void;
  (e: 'copyCpt', item: any): void;
  (e: 'delCpt', item: any, index: number): void;
}>();

// --- 定义变量 ---

const options = ref(optionsData);
const cptGroupKeys = ref([]);
const currentTab = ref(0); //0组件，1图层

// --- 定义方法 ---

const dragStart = (e: any) => {
  const copyDom = e.currentTarget.cloneNode(true);
  emit('dragStart', copyDom);
};
const showConfigBar = (data: any, item: any, index: number) => {
  emit('showConfigBar', data, item, index);
};
const copyCpt = (item: any) => {
  emit('copyCpt', item);
};
const delCpt = (item: any, index: number) => {
  emit('delCpt', item, index);
};
</script>

<style scoped>
.bar {
  position: relative;
  width: 100%;
  height: 100%;
  background: #353f50;
  color: #fff;
  overflow-x: hidden;
  overflow-y: auto;
}
.el-collapse-item__* {
  background: #353f50;
}
.topTab {
  width: 200px;
  height: 45px;
  text-align: center;
  line-height: 45px;
  cursor: pointer;
  position: fixed;
  z-index: 7;
  background: #353f50;
  box-shadow: 0 1px 3px #2b3340;
}
.no-focus-tab {
  background: #3f4b5f;
}
.focus-tab {
  color: #409eff;
}
.selectedItem {
  line-height: 45px;
  cursor: pointer;
  box-shadow: 0 1px 3px #2b3340 inset;
}
</style>
