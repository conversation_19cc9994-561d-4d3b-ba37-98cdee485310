<template>
  <div class="select-tab-contianer">
    <div v-for="item in tabList" :key="item.value" class="select-row" >
      <div :class="{'is-checked-tab':item.isClick}" @click="handleCuurentClick(item)">
       <span>{{ item.label }}</span> 
      </div>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
    
    };
  },
  props:{
    tabList:{
      type:Array,
      default:()=>[]
    }
  },
  created() {
  },
  mounted(){
    if(this.tabList.length >0){
      this.$emit('clickValue',this.tabList[0].value)
    }
  },
  methods: {
    handleCuurentClick(item){
      this.tabList.map(e=>{
        if(e.value == item.value){
          e.isClick = true
        }else{
          e.isClick= false
        }
      })
      this.$emit('clickValue',item.value)
    }
  }
};
</script>

<style lang='scss' scoped>
.select-tab-contianer{
  display: flex;
  width: 100%;
  height: 40px;
  align-content: center;
  .select-row{
    width: 80px;
    height: 40px;
    font-size: 14px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #8291A9;
    line-height: 40px;
    position: relative;
    margin-right: 16px;
    text-align: center;
    cursor: pointer;
    overflow: hidden;
  }
  .is-checked-tab{
      margin-top: 4px;
      margin-left: 0;
      margin-bottom:0;
      width: 80px;
      height: 28px;
      background: rgba(130,145,169,0.1);
      border-radius: 16px 16px 16px 16px;
      position: absolute;
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: var(--current-color);
      line-height: 28px;
      text-align: center;
    }
}
</style>