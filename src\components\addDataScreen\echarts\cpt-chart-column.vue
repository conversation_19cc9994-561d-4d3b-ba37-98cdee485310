<template>
  <div :id="uuid" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { useRoute } from 'vue-router';
import { v1 as uuidv1 } from 'uuid';

const route = useRoute();
// --- props ---
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData: any = ref({});
// --- watch ---
watch(
  () => props.option.attribute,
  (newVal) => {
    loadChart(newVal);
  },
  { deep: true }
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});

const loadData = (taskId: string) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        cptData.value = res.data;
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

const loadChart = (attribute: any) => {
  let columnColor = attribute.barColor;
  if (attribute.gradualColor) {
    columnColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: attribute.barColor1 },
      { offset: 0.5, color: attribute.barColor2 },
      { offset: 1, color: attribute.barColor3 }
    ]);
  }
  chartOption.value = {
    color: columnColor,
    title: {
      text: attribute.chartTitle,
      textStyle: {
        color: attribute.titleTextColor
      },
      left: attribute.titleLeft,
      top: attribute.titleTop
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      }
    },
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true
    },
    xAxis: {
      show: attribute.xAxisShow,
      type: 'category',
      data: cptData.value?.xdata,
      axisLabel: {
        color: attribute.xLabelColor,
        rotate: attribute.xFontRotate //倾斜角度-180~180
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor
        }
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow
      }
    },
    yAxis: {
      show: attribute.yAxisShow,
      type: 'value',
      axisLabel: {
        color: attribute.yLabelColor
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor
        }
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow
      }
    },
    series: [
      {
        data: cptData.value?.ydata,
        type: attribute.barType, //pictorialBar || bar
        showBackground: attribute.barBgShow,
        symbol: attribute.barPath,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        },
        barWidth: attribute.barWidth,
        itemStyle: {
          borderRadius: attribute.barBorderRadius
        },
        label: {
          show: attribute.barLabelShow, //开启显示
          position: 'top', //在上方显示
          color: attribute.barLabelColor,
          fontSize: attribute.barLabelSize
        }
      }
    ]
  };
  chart?.setOption(chartOption.value);
};

onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});

defineOptions({
  name: 'cpt-chart-column'
});
</script>

<style scoped></style>
