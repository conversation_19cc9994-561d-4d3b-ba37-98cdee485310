<template>
  <div class="setting-container">
    <el-form ref="elFormRef" :model="formData" :rules="rules" label-width="100px" label-position="top">
      <el-form-item label="审批名称" prop="flowName">
        <el-input v-model="formData.flowName" show-word-limit placeholder="请输入审批名称" clearable :style="{ width: '100%' }" maxlength="20">
        </el-input>
      </el-form-item>
      <el-form-item label="选择分组" prop="flowGroup">
        <el-select v-model="formData.flowGroup" placeholder="请选择选择分组" clearable :style="{ width: '100%' }">
          <el-option
            v-for="(item, index) in flowGroupOptions"
            :key="index"
            :label="item.groupName"
            :value="item.groupId"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="表单地址" prop="flowForm" v-if="formData.formType === 1">
        <el-input v-model="formData.flowForm" placeholder="请输入表单地址" clearable :style="{ width: '100%' }"> </el-input>
      </el-form-item>
      <el-form-item label="表单大小" prop="flowForm" v-if="formData.formType === 1">
        <el-input v-model="formData.width" placeholder="宽度" clearable :style="{ width: '100%' }"> </el-input>
      </el-form-item>
      <el-form-item label="模板图标" prop="icon">
        <img :src="activeIconSrc" style="width: 28px; height: 28px; vertical-align: middle" />
        <el-button plain size="small" @click="dialogVisible = true" style="margin-left: 10px">选择图标</el-button>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialogVisible" title="选择图标" width="612px">
      <div class="icon-main">
        <div class="icon-item" v-for="(icon, index) in iconList" :key="index" :class="{ active: selectedIcon === icon.id }">
          <img style="width: 40px; height: 40px; border-radius: 8px" :src="icon.src" @click="selectedIcon = icon.id" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel" size="small">取 消</el-button>
          <el-button type="primary" @click="handleConfirm" size="small">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { getGroupList } from '@/api/process';

interface FormData {
  flowName: string;
  flowImg: string;
  flowGroup?: number;
  flowRemark?: string;
  initiator: any;
  formType: number;
  flowForm: string;
  width: string;
}

interface IconItem {
  src: string;
  id: number;
}

interface GroupItem {
  groupName: string;
  groupId: number;
  disabled?: boolean;
}

const props = defineProps({
  tabName: {
    type: String,
    default: ''
  },
  initiator: {
    type: [String, Number, Array],
    default: null
  },
  conf: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['initiatorChange']);

const elFormRef = ref<FormInstance>();
const dialogVisible = ref(false);
const activeIcon = ref(0);
const selectedIcon = ref(0);
const formData = ref<FormData>({
  flowName: '',
  flowImg: '',
  flowGroup: undefined,
  flowRemark: undefined,
  initiator: null,
  formType: 0,
  flowForm: '',
  width: '600px'
});

const rules = {
  flowName: [
    {
      required: true,
      message: '请输入审批名称',
      trigger: 'blur'
    }
  ]
};

const iconList = ref<IconItem[]>([]);
const flowGroupOptions = ref<GroupItem[]>([]);

const formTypes = [
  {
    label: '在线设计',
    value: 0
  },
  {
    label: '定制表单',
    value: 1
  }
];

const activeIconSrc = computed(() => {
  const icon = iconList.value.find((t) => t.id === activeIcon.value);
  return icon ? icon.src : '';
});
/**
 * 获取当前的分组数据
 */
const handleGetGroupList = async () => {
  try {
    const res = await getGroupList({});
    if (res.code === 200) {
      flowGroupOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取分组列表失败:', error);
  }
};
/**
 * 不确定这个方法有使用的地方没，暂时注释掉
 */
// const emitInitiator = () => {
//   emit('initiatorChange', formData.value.initiator, elFormRef.value?.selectedLabels);
// };

/**
 * 关闭选择图标的弹框
 */
const handleCancel = () => {
  dialogVisible.value = false;
  selectedIcon.value = activeIcon.value;
};
/**
 * 确认选择的炒作
 */
const handleConfirm = () => {
  dialogVisible.value = false;
  activeIcon.value = selectedIcon.value;
};
/**
 * 重置表单数据，这里是在父组件中调用这个方法
 */
const resetForm = () => {
  formData.value = {
    flowName: '',
    flowImg: '',
    flowGroup: undefined,
    flowRemark: undefined,
    initiator: null,
    formType: 0,
    flowForm: '',
    width: '600px'
  };
  activeIcon.value = 0;
  selectedIcon.value = 0;
  elFormRef.value?.resetFields();
};
/**
 * 获取当前页面中填写的数据，这里是在父组件中使用这个方法
 */
const getData = () => {
  return new Promise((resolve, reject) => {
    elFormRef.value?.validate((valid: boolean) => {
      if (!valid) {
        reject({ target: props.tabName });
        return;
      }
      formData.value.flowImg = activeIcon.value.toString();
      resolve({ formData: formData.value, target: props.tabName });
    });
  });
};
/**
 * 在当前数据中校验数据内容，这里是在父组件中使用这个方法
 */
const getDataNoValidate = () => {
  return Promise.resolve({ formData: formData.value, target: props.tabName });
};

watch(
  () => props.initiator,
  (val) => {
    formData.value.initiator = val;
  },
  { immediate: true }
);

watch(
  () => props.conf,
  (val) => {
    if (val) {
      if (typeof val === 'object' && val !== null) {
        Object.assign(formData.value, val);
      }
    } else {
      resetForm();
    }
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  const modules = import.meta.glob('@/assets/images/approverIcon/*.png', { eager: true });
  iconList.value = Object.entries(modules).map(([path, module], idx) => ({
    src: (module as any).default,
    id: idx
  }));
  if (typeof props.conf === 'object' && props.conf !== null) {
    Object.assign(formData.value, props.conf);
    activeIcon.value = Number(formData.value.flowImg);
    selectedIcon.value = Number(formData.value.flowImg);
  }
  handleGetGroupList();
});

defineExpose({
  getData,
  getDataNoValidate,
  resetForm
});
</script>

<style lang="scss" scoped>
.icon-main {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex-direction: row;

  .icon-item {
    width: 48px;
    height: 48px;
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    justify-items: center;
    flex-wrap: wrap;
    flex-direction: row;
    text-align: center;
    padding-left: 2px;

    &.active {
      width: 48px;
      height: 48px;
      text-align: center;
      line-height: 48px;
      border: 1px solid #0081ff;
      border-radius: 8px;
      padding-left: 3px;
    }

    &:hover {
      opacity: 1;
    }
  }
}

.setting-container {
  width: 600px;
  height: 100%;
  margin: auto;
  background: white;
  padding: 16px;

  :deep(.el-form--label-top .el-form-item__label) {
    padding-bottom: 0;
  }
}
</style>
