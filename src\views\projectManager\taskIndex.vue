<template>
  <container-card>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="全部任务" name="all"></el-tab-pane>
      <el-tab-pane label="创建任务" name="first"></el-tab-pane>
      <el-tab-pane label="收到任务" name="second"></el-tab-pane>
      <create-index ref="createRef"></create-index>
      <!--
      <el-tab-pane label="关闭任务" name="third">
        <close-index></close-index>
      </el-tab-pane>
      -->
    </el-tabs>
  </container-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { TabsPaneContext } from 'element-plus';
import createIndex from './component/taskManagement/index.vue';

// 定义组件实例类型，包含getSearchTaskList方法
interface TaskManagementExposed {
  getSearchTaskList: (type: number) => void;
}

const activeName = ref('all');
const createRef = ref<(InstanceType<typeof createIndex> & TaskManagementExposed) | null>(null);

// 在组件挂载后初始化数据
onMounted(() => {
  // 初始化时加载全部任务，添加延时确保组件完全加载
  setTimeout(() => {
    try {
      if (createRef.value) {
        createRef.value.getSearchTaskList(3);
      }
    } catch (error) {
      console.error('Failed to load initial task list:', error);
    }
  }, 200);
});
/**
 * 点击标签
 * @param pane 标签
 */
const handleClick = (pane: TabsPaneContext) => {
  try {
    if (!createRef.value) {
      console.error('Component reference is not available');
      return;
    }

    if (pane.props.name === 'all') {
      createRef.value.getSearchTaskList(3);
    } else if (pane.props.name === 'first') {
      createRef.value.getSearchTaskList(1);
    } else if (pane.props.name === 'second') {
      createRef.value.getSearchTaskList(2);
    }
  } catch (error) {
    console.error('Error in handleClick:', error);
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  overflow: hidden;
}
</style>
