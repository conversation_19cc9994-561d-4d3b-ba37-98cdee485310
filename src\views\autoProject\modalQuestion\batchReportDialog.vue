<!-- 批量导入模板 -->
<template>
  <div class="batchReportDialog-main">
    <el-dialog title="批量导入" v-model="localDialog" :close-on-click-modal="false" width="80%" :before-close="handleClose">
      <div class="content">
        <div class="content-box">
          <div class="down-bulk">
            <div class="normal-span">1. 下载导入模板</div>
            <div class="gary-span">根据提示信息完善表格内容</div>
            <el-button size="small" style="margin-top: 6px" @click="downBulk">
              <el-icon><Download /></el-icon> 下载模板</el-button
            >
          </div>
          <div class="important-bulk">
            <div class="normal-span">2. 上传完善后的模板</div>
            <div class="important-main">
              <img src="@/assets/images/excel1.png" class="excel-img" v-if="!isUpload" />
              <img src="@/assets/images/XLS.png" class="excel-img" v-else />

              <el-upload
                class="upload-demo"
                :action="`${base}${sortUrl}`"
                :headers="headers"
                :show-file-list="false"
                :auto-upload="false"
                :multiple="false"
                :on-success="uploadSuccess"
                accept=".xlsx,.xls"
                :file-list="fileList"
                ref="uploadTemp"
                :on-change="handleChangeFile"
              >
                <template #default>
                  <div class="flex flex-col justify-center items-center">
                    <el-button v-show="!isUpload">点击上传</el-button>
                    <div v-show="isUpload" style="margin-bottom: 12px">{{ fileMsg.name }} ({{ formatFileSize(fileMsg.size) }}kb)</div>
                    <el-button v-show="isUpload">重新选择</el-button>
                  </div>
                </template>
                <template #tip>
                  <div v-show="!isUpload" class="el-upload__tip" style="color: rgba(148, 155, 164, 1)">
                    下载模板并完善信息后，可直接进行上传,支持格式：.xls、.xlsx
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
          <!-- <div class="footer-btn">
            <el-button
              size="small"
              type="primary"
              :disabled="!isUpload"
              @click="submitImport"
              >导入</el-button
            >
          </div> -->
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitImport">导 入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, UploadFile, UploadInstance } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { getToken } from '@/utils/auth';
import { downloadTemp } from '@/api/project';
import * as xlsx from 'xlsx';
import { useProjectStore } from '@/store/modules/project';
const { proxy } = getCurrentInstance();
const projectStore = useProjectStore();

interface Props {
  batchDialog: boolean;
}

interface FileMsg {
  name?: string;
  size?: number;
  raw?: File;
  [key: string]: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['changeBatchDialog', 'submitUploadTemp']);

// 状态
const localDialog = ref(props.batchDialog);
const isUpload = ref(false);
const fileMsg = ref<FileMsg>({});
const fileList = ref<UploadFile[]>([]);
const moduleId = ref(0);
const files = ref<Record<string, File> | null>(null);
const loading = ref(false);
const uploadTemp = ref<UploadInstance>();

// 常量
const base = computed(() => import.meta.env.VITE_APP_BASE_API || '');
const sortUrl = '/project/fund/excel/upload';
const headers = {
  Authorization: 'Bearer ' + getToken()
};

// 监听props变化
watch(
  () => props.batchDialog,
  (newVal) => {
    localDialog.value = newVal;
  }
);

// 监听对话框状态变化
watch(
  () => localDialog.value,
  (newVal) => {
    emit('changeBatchDialog', newVal);
  }
);

// 格式化文件大小
const formatFileSize = (size?: number): string => {
  if (!size) return '';
  return (size / 1024).toFixed(2);
};

// 关闭对话框
const handleClose = () => {
  emit('changeBatchDialog', false);
};

// 下载模板
const downBulk = () => {
  // 从pinia获取moduleId
  moduleId.value = projectStore.proModuleId;

  const params = {
    moduleId: moduleId.value
  };
  proxy.$modal.loading();
  downloadTemp(params)
    .then((res) => {
      proxy.$modal.closeLoading();
      if (res.data.type === 'application/json') {
        // 导出异常
        const read = new FileReader();
        read.readAsText(res.data, 'utf-8');
        let bugMsg = '';

        read.onload = (data) => {
          if (data.target?.result) {
            const result = JSON.parse(data.target.result as string);
            bugMsg = result.msg;
            ElMessage.error(result.msg);
            if (!bugMsg) {
              bugMsg = '未知异常';
            }
          }
        };
      } else {
        // 导出正常
        const name = decodeURI(res.headers['content-disposition']);
        const index = name.indexOf('=');
        const endFileName = name.substring(index + 1, name.length);

        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        // 针对ie浏览器
        if (window.navigator && 'msSaveOrOpenBlob' in window.navigator) {
          (window.navigator as any).msSaveOrOpenBlob(blob, endFileName);
        } else {
          // 非ie浏览器
          const downloadElement = document.createElement('a');
          const href = window.URL.createObjectURL(blob); // 创建下载的链接
          downloadElement.href = href;
          downloadElement.download = endFileName; // 下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); // 点击下载
          document.body.removeChild(downloadElement); // 下载完成移除元素
          window.URL.revokeObjectURL(href); // 释放blob对象
        }
      }
    })
    .catch(() => {
      proxy.$modal.closeLoading();
    });
};

// 上传成功回调
const uploadSuccess = (response: any, file: UploadFile, fileList: UploadFile[]) => {
  if (response.code === 200) {
    ElMessage.success('上传成功!');
    loading.value = false;
  } else if (response.code === 500) {
    let errorStr = '';
    response.data.forEach((item: string) => {
      errorStr += `<strong>${item}</strong><br/>`;
    });

    ElMessage({
      dangerouslyUseHTMLString: true,
      message: errorStr,
      type: 'error'
    });
    loading.value = false;
  } else {
    ElMessage.error(response.msg);
    loading.value = false;
  }
};

// 文件变更回调
const handleChangeFile = (file: UploadFile, uploadFiles: UploadFile[]) => {
  fileMsg.value = file;

  if (uploadFiles.length > 1) {
    uploadFiles.splice(0, 1);
  }

  fileList.value = uploadFiles;
  isUpload.value = true;

  if (file.raw) {
    // 取到File
    files.value = { 0: file.raw };
  }
};

// 读取Excel文件
const readExcel = (files: Record<string, File>) => {
  // 表格导入
  if (!files[0]) {
    // 如果没有文件名
    return false;
  } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
    ElMessage.error('上传格式不正确，请上传xls或者xlsx格式');
    return false;
  }
  const fileReader = new FileReader();
  fileReader.onload = (event) => {
    try {
      if (!event.target?.result) return false;

      const data = event.target.result;
      const workbook = xlsx.read(data, {
        type: 'binary'
      });

      const wsname = workbook.SheetNames[0]; // 取第一张表
      // 获取第一个工作表
      const worksheet = workbook.Sheets[wsname];

      // 将工作表转换为JSON对象
      const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

      // 获取表头
      const headers = jsonData[0];
      const ws = xlsx.utils.sheet_to_json(workbook.Sheets[wsname]); // 生成json表格内容

      // 先清除上传的文件
      isUpload.value = false;
      uploadTemp.value?.clearFiles();
      fileList.value = [];

      // 提交上传的数据
      emit('submitUploadTemp', ws, headers);
    } catch (e) {
      return false;
    }
  };

  fileReader.readAsBinaryString(files[0]);
};

// 提交导入
const submitImport = () => {
  if (fileList.value.length === 0) {
    ElMessage.error('您未选择数据！！！');
    return;
  }

  loading.value = true;

  if (files.value) {
    readExcel(files.value);
  }
};
</script>

<style lang="scss" scoped>
.upload-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: calc(100% - 90px);
  margin-top: 10px;
  .content-box {
    width: 669px;
    height: 100%;
    // border: #DADFE7 solid 1px;
    // border-radius: 12px;
    overflow-y: auto;
    .big-span {
      margin-bottom: 16px;
      color: rgba(47, 64, 95, 1);
      font-size: 16px;
      font-weight: 600;
    }
    .down-bulk {
      width: 100%;
      border-radius: 4px;
      background-color: rgba(242, 245, 250, 1);
      padding: 16px;
    }
    .important-bulk {
      border-radius: 4px;
      background-color: rgba(242, 245, 250, 1);
      padding: 16px;
      margin-top: 12px;
      .important-main {
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 1);
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 16px;
        .excel-img {
          width: 48px;
          height: 48px;
          margin-top: 40px;
          margin-bottom: 8px;
        }
      }
    }
    .footer-btn {
      margin-top: 24px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
    }
  }
}
.dialog-box {
  height: 72px;
  border-radius: 8px;
  background-color: rgba(242, 245, 250, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(52, 69, 99, 1);
}
</style>
