<template>
  <container-card>
    <div class="title-row">
      <span class="text">模块管理</span>
      <div>
        <el-button style="margin-bottom: 6px" type="primary" size="small" @click="copyModal(1)">接收分享码</el-button>
        <el-button style="margin-bottom: 6px" type="primary" size="small" @click="copyModal(2)">上传设计文件</el-button>
      </div>
    </div>
    <div class="row-publish-list">
      <modal-list ref="modelListRef" :isExpired="isExpired"></modal-list>
    </div>
    <!-- 接收分享码弹窗 -->
    <el-dialog title="接收分享码" v-model="receiveShareCodeDialog" width="400px" :before-close="handleClose" :close-on-click-modal="false">
      <el-form :model="receiveShareMsg" :rules="receiveShareMsgRule" ref="receiveShareMsgRef" class="demo-ruleForm" label-position="top">
        <el-form-item label="分享码" prop="code">
          <el-input v-model="receiveShareMsg.code" placeholder="请输入分享码" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="模块名称" prop="name">
          <el-input v-model="receiveShareMsg.name" placeholder="请输入模块名称" maxlength="20"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitShare">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 上传设计文件 -->
    <el-dialog title="上传设计文件" v-model="uploadDesign" width="500px" :before-close="handleCloseDesign" :close-on-click-modal="false">
      <el-upload
        style="width: 100%"
        class="upload-demo"
        ref="desginRef"
        accept=".json"
        :action="`${baseUrl}/qjt/rule/pasteJson`"
        :headers="headers"
        name="files"
        :on-success="handleSuccessDesgin"
        :auto-upload="false"
        drag
        :data="desginParams"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDesign">取 消</el-button>
          <el-button type="primary" @click="submitDesign">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </container-card>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { ref, computed, onMounted } from 'vue';
import ModalList from './modalList/index.vue';
import { pasteModal } from '@/api/modal/index.js';
import { getToken } from '@/utils/auth';
import { useUserStore } from '@/store/modules/user';
import { useRouter, useRoute } from 'vue-router';
const store = useUserStore();
const route = useRoute();
// 定义响应式数据
const receiveShareCodeDialog = ref(false);
const receiveShareMsg = ref({
  code: '',
  name: '',
  companyId: ''
});
const desginParams = ref({});
const receiveShareMsgRule = ref({
  code: [{ required: true, message: '请输入分享码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入模块名称', trigger: 'blur' }]
});
const uploadDesign = ref(false);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const headers = computed(() => {
  return {
    'Authorization': `Bearer ${getToken()}`,
    'Access-Control-Allow-Origin': '*'
  };
});
const modelListRef = ref<InstanceType<typeof ModalList> | null>(null);
const desginRef = ref<HTMLInputElement | null>(null);
const receiveShareMsgRef = ref<HTMLFormElement | null>(null);

// 从 store 中获取 isExpired
const isExpired = computed(() => store['isExpired']);

onMounted(() => {});

// 提交接收分享码
const submitShare = () => {
  if (receiveShareMsgRef.value) {
    (receiveShareMsgRef.value as any).validate((valid: boolean) => {
      if (valid) {
        // 设置公司私有模块的数据 需要传递公司id
        const companyId = route.query.companyId;
        if (companyId && companyId !== undefined && companyId !== null) {
          this.receiveShareMsg.companyId = companyId;
        }
        pasteModal(receiveShareMsg.value).then((res: any) => {
          if (res.code === 200) {
            ElMessage({
              type: 'success',
              message: '添加成功'
            });
            receiveShareCodeDialog.value = false;
            receiveShareMsg.value = {
              code: '',
              name: ''
            };
            if (modelListRef.value) {
              modelListRef.value.getUnPublishList();
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        return false;
      }
    });
  }
};

// 关闭接收分享码弹窗
const handleClose = () => {
  receiveShareCodeDialog.value = false;
  receiveShareMsg.value = {
    code: '',
    name: ''
  };
};

// 关闭上传设计文件
const handleCloseDesign = () => {
  desginRef.value?.clearFiles();
  uploadDesign.value = false;
};

// 上传设计文件
const submitDesign = () => {
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    desginParams.value.companyId = companyId;
  }
  if (desginRef.value) {
    (desginRef.value as any).submit();
  }
};

const handleSuccessDesgin = (response: any, file: any, fileList: any) => {
  if (response.code === 200) {
    ElMessage({
      type: 'success',
      message: '操作成功'
    });
    desginRef.value?.clearFiles();
    uploadDesign.value = false;
    if (modelListRef.value) {
      modelListRef.value.init();
    }
  } else {
    ElMessage.error(response.msg);
  }
};

// 1接收分享码 2上传设计文件
const copyModal = (type: number) => {
  if (isExpired.value === 2) {
    // 过期了不让新增提示
    ElMessageBox.alert('<span style="color:red">您的账号已过期，请续费后操作！！！</span>', '过期提示', {
      dangerouslyUseHTMLString: true
    });
    return;
  }
  if (type === 1) {
    receiveShareCodeDialog.value = true;
  } else if (type === 2) {
    uploadDesign.value = true;
  }
};
</script>

<style lang="scss" scoped>
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dbe7ee;
}
.main {
  overflow: hidden;
  .title-row {
    display: flex;
    justify-content: space-between;
    // height: 70px;
    border-bottom: 1px solid #dbe7ee;
    align-items: center;
    .text {
      height: 22px;
      font-size: 16px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      font-weight: 600;
      color: #161d26;
      line-height: 22px;
      margin-bottom: 4px;
    }
  }
  .row-publish-list {
    // height: calc(100vh - 50%);
    // max-height: 350px;
    // overflow-y: auto;
    // min-height: 48px;
  }
  .row-unpublish-list {
    // height: calc(100vh - 50%);
    // max-height: 350px;
    // min-height: 48px;
    // overflow-y: hidden;
  }
}
</style>
