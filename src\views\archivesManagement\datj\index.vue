<template>
  <container-card>
    <div class="app-container main">
      <div class="card-container">
        <div class="card-item" v-for="item in cardList" :key="item.id">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-num">{{ item.num }}</div>
          <div class="item-desc">
            <div class="text">{{ item.text }}</div>
            <div class="icon">
              <el-icon style="margin-top: 2px"><Bottom /></el-icon>
            </div>
            <div class="percent">{{ item.percent }}</div>
          </div>
        </div>
      </div>
      <!-- 下方这里是一个折线图 -->
      <div class="chart-container">
        <!-- <div :class="className" :style="{ height: height, width: width }" /> -->
        <div id="chartId" :style="{ height, width }"></div>
      </div>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, shallowRef, markRaw } from 'vue';
import * as echarts from 'echarts';
import { getselectZx } from '@/api/archives';

// 定义类型
interface CardItem {
  id?: number;
  filesType: string | number;
  label: string;
  ydata: number[];
  num: number;
  text: string;
  percent: string;
}

// props定义
interface Props {
  className?: string;
  width?: string;
  height?: string;
  autoResize?: boolean;
}

// 定义ECharts系列类型
interface EchartsSeriesItem {
  name: string;
  type: string;
  smooth: boolean;
  data: number[];
  itemStyle: {
    color: string;
  };
  lineStyle: {
    color: string;
    width: number;
  };
  animationDuration: number;
  animationEasing: string;
  areaStyle?: { color: string };
}

const props = withDefaults(defineProps<Props>(), {
  className: 'chart',
  width: '100%',
  height: '100%',
  autoResize: true
});

// 响应式数据
const cardList = ref<CardItem[]>([]);
// 使用shallowRef来避免Vue深度观测ECharts实例
const myChart = shallowRef<echarts.ECharts | null>(null);
const xData = ref<string[]>([]);
const allData = ref<CardItem[]>([]);

// 初始化图表
const handleInit = (xData: string[], list: CardItem[]) => {
  // 基于准备好的dom，初始化echarts实例
  const chartDom = document.getElementById('chartId');
  if (!chartDom) return;

  // 确保先销毁之前的实例
  if (myChart.value) {
    myChart.value.dispose();
  }

  myChart.value = echarts.init(chartDom);

  const definedSeriesConfig = markRaw([
    { name: '方案档案', color: '#3888fa', easing: 'quadraticOut', hasAreaStyle: true },
    { name: '征收档案', color: '#ff4949', easing: 'cubicInOut' },
    { name: '回迁档案', color: '#f8ce5e', easing: 'cubicInOut' },
    { name: '房源档案', color: '#ff7b00', easing: 'cubicInOut' },
    { name: '财务档案', color: '#5dadf7', easing: 'cubicInOut' },
    { name: '司法档案', color: '#11b95c', easing: 'cubicInOut' },
    { name: '信访档案', color: '#e78122', easing: 'cubicInOut' },
    { name: '办公室档案', color: '#8311e0', easing: 'cubicInOut' }
  ]);

  // 构建图表系列数据，使用普通数组而非响应式数据
  const echartsSeries: EchartsSeriesItem[] = [];

  // 为每个配置创建系列
  definedSeriesConfig.forEach((config) => {
    const dataItem = list.find((item) => item.label === config.name);
    // 克隆数据以避免响应式影响
    const ydata = dataItem && dataItem.ydata ? [...dataItem.ydata] : [];

    // 将series对象声明为EchartsSeriesItem类型
    const series: EchartsSeriesItem = {
      name: config.name,
      type: 'line',
      smooth: true,
      data: ydata,
      itemStyle: {
        color: config.color
      },
      lineStyle: {
        color: config.color,
        width: 2
      },
      animationDuration: 2800,
      animationEasing: config.easing
    };

    if (config.hasAreaStyle) {
      series.areaStyle = { color: '#f3f8ff' };
    }

    echartsSeries.push(series);
  });

  // 克隆xData避免响应式影响
  const xDataClone = [...xData];

  // 指定图表的配置项和数据，使用markRaw避免响应式
  const option = markRaw({
    title: {
      text: '档案总数(份)',
      left: '5%'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10]
    },
    xAxis: {
      type: 'category',
      data: xDataClone
    },
    yAxis: {
      type: 'value'
    },
    legend: {
      data: definedSeriesConfig.map((s) => s.name),
      left: 'center',
      bottom: '5%',
      orient: 'horizontal',
      selectedMode: true,
      itemWidth: 18,
      itemHeight: 10,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      tooltip: {
        show: true
      }
    },
    series: echartsSeries
  });

  // 使用指定的配置项和数据显示图表
  myChart.value.setOption(option);
};

// 获取折线图数据
const handleChartsData = async () => {
  const now = new Date();
  const year = now.getFullYear();
  // 创建1月1日00:00:00的时间对象并获取其时间戳
  const startOfYear = new Date(year, 0, 1).getTime();
  // 创建12月31日23:59:59的时间对象并获取其时间戳
  const endOfYear = new Date(year, 11, 31, 23, 59, 59, 999).getTime();

  const params = {
    begin: startOfYear,
    end: endOfYear
  };

  try {
    const res = await getselectZx(params);
    if (res.code === 200) {
      // 清空数据
      allData.value = [];

      const keys = Object.keys(res.data);
      if (keys.length > 0) {
        keys.forEach((key) => {
          const label = handleNameByType(key);
          const yObj: CardItem = {
            filesType: key,
            label: label || '',
            ydata: res.data[key].ydata || [],
            num: 0,
            text: '',
            percent: ''
          };
          allData.value.push(yObj);
        });

        // filesType 3 是第三方档案，在页面上不展示
        const list = allData.value.filter((item) => item.label).filter((item) => item.filesType !== '3' && item.filesType !== 3);

        cardList.value = sumYDataAndAddProps(list);

        // 确保获取有效的xData
        if (res.data[keys[keys.length - 1]] && res.data[keys[keys.length - 1]].xdata) {
          xData.value = [...res.data[keys[keys.length - 1]].xdata];
        } else {
          xData.value = [];
        }

        // 使用setTimeout延迟图表初始化，避免与Vue更新冲突
        setTimeout(() => {
          handleInit(xData.value, list);
        }, 0);
      }
    } else {
      ElMessage.error(res.msg || '获取数据失败');
    }
  } catch (error) {
    console.error('获取折线图数据失败', error);
  }
};

// 根据类型判断文件名称
const handleNameByType = (num: string | number): string => {
  switch (Number(num)) {
    case 1:
      return '方案档案';
    case 2:
      return '征收档案';
    case 3:
      return '第三方档案';
    case 4:
      return '回迁档案';
    case 5:
      return '房源档案';
    case 6:
      return '财务档案';
    case 7:
      return '司法档案';
    case 8:
      return '信访档案';
    case 9:
      return '办公室档案';
    default:
      return '';
  }
};

// 数据转换 (保留原有函数，但目前代码中没有使用)
const handleTransformData = (list: any[]) => {
  // 创建一个映射以存储结果
  const resultMapping: Record<string, number[]> = {};

  // 遍历每个数据项
  list.forEach((item) => {
    if (item.xdata && Array.isArray(item.xdata)) {
      item.xdata.forEach((month: string, index: number) => {
        if (!resultMapping[month]) {
          // 如果该月份还未存在于映射中，则初始化
          resultMapping[month] = [];
        }
        // 将对应月份的ydata值添加到映射中
        resultMapping[month].push(Number(item.ydata[index]));
      });
    }
  });

  // 将映射转换为所需的数组格式
  const transformedArray = Object.keys(resultMapping).map((month) => ({
    month: month,
    value: resultMapping[month]
  }));

  return transformedArray;
};

// 对折线图中的数据进行求和
const sumYDataAndAddProps = (data: CardItem[]): CardItem[] => {
  return data.map((item) => {
    // 计算 ydata 数组中所有值的和
    const sum = item.ydata.reduce((acc, curr) => acc + Number(curr), 0);

    // 返回新的对象，包含原始数据以及新增的属性
    return {
      ...item,
      num: sum,
      text: '比较上月',
      percent: '5%'
    };
  });
};

// 生命周期钩子
onMounted(() => {
  handleChartsData();
});

// 组件卸载前清理图表实例
onBeforeUnmount(() => {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
});
</script>

<style lang="scss" scoped>
.text-over {
  height: 36px;
  line-height: 36px;
  color: #0081ff;
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main {
  background-color: #fff;

  .card-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center; /* 整体居中 */
    height: 35%;
    margin-bottom: 24px;
    .card-item {
      flex: 0 0 calc(25% - 20px); /* 每个item占据25%宽度，减去外边距 */
      margin: 10px; /* 给定item之间的外边距 */
      border: 1px solid #ededed;
      height: 130px;
      border-radius: 4px;
      background-color: #f6f7f8;
      box-shadow: 1px 2px 3px #ededed;
      padding: 16px;
      .item-label {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        width: 100%;
        max-width: calc(100% - 1px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .item-num {
        width: 100%;
        height: 50px;
        line-height: 50px;
        font-size: 24px;
        color: #111112;
        font-weight: 600;
      }
      .item-desc {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        height: 30px;
        line-height: 30px;
        margin-bottom: 8px;
        .text {
          font-size: 14px;
          color: #333;
        }
        .icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: #b0efb2;
          text-align: center;
          vertical-align: middle;
          margin: 0 4px;
          line-height: 24px;
          i {
            font-size: 18px;
            color: #13ce66;
          }
        }
        .percent {
          font-size: 20px;
          color: #13ce66;
          margin-left: 8px;
        }
      }
    }
  }
  .chart-container {
    height: 65%;
    width: 100%;
    max-height: calc(100% - 320px);
    margin-top: 60px;
  }
}
</style>
