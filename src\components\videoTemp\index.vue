<!-- 视频播放 解析token -->
<template>
  <div class="videoTemp-main">
    <el-dialog title="视频" v-model="videoDialogCopy" width="500px" :before-close="handleClose">
      <div class="video-class">
        <video width="100%" height="100%" controls id="_video" :poster="coverPoster" v-if="videoUrl">
          <source :src="videoUrl" type="video/mp4" />
          <source :src="videoUrl" type="video/ogg" />
          <source :src="videoUrl" type="video/webm" />
          <object :src="videoUrl" width="100%" height="100%">
            <embed :src="videoUrl" width="100%" height="100%" />
          </object>
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getToken } from '@/utils/auth';
import { uploadTestHuaWEei, downLaodTestHuaWEei } from '@/api/forum';
import Axios from 'axios';

// --- props ---
interface Props {
  checkedVideoUrl?: any;
  videoDialog: boolean;
  checkedVideId?: any;
}

const props = withDefaults(defineProps<Props>(), {
  videoDialog: false
});

const videoDialogCopy = computed(() => props.videoDialog);

watch(
  videoDialogCopy,
  (val) => {
    if (val) {
      videoUrl.value = '';
      loading.value = true;
      getVideoUrl();
    }
  },
  { immediate: true }
);
// ---定义变量 ---
const videoUrl: any = ref('');
const loading = ref(true);
const forumImgFileList = ref([]);
const headers = reactive({
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
});
const base = import.meta.env.VITE_APP_BASE_API;
const coverPoster = ref(''); // 视频封面

// --- 定义方法 ---
const handleSubmit = () => {
  const formData = new FormData();
  forumImgFileList.value.forEach((file) => {
    formData.append('file', file.raw);
  });
  uploadTestHuaWEei(formData).then((res) => {
    if (res.code == 200) {
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 上传图片
 * @param file
 * @param fileList
 */
const handleChangeUploadImg = (file, fileList) => {
  const whiteList = ['image/png', 'image/jpeg', 'video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb', 'qlv'];
  const isFlag = whiteList.includes(file.raw.type);
  const isSize = file.size / 1024 / 1024;
  if (isSize > 100) {
    const index = fileList.findIndex((e) => e.size / 1024 / 1024 == isSize);
    fileList.splice(index, 1);
    ElMessage.warning('上传的文件不能超过100MB');
    return false;
  }
  if (file.raw.type == 'video/mp4') {
    const video = document.createElement('video'); // 也可以自己创建video
    video.src = file.url; // url地址 url跟 视频流是一样的
    const canvas = document.createElement('canvas'); // 获取 canvas 对象
    const ctx = canvas.getContext('2d'); // 绘制2d
    video.crossOrigin = 'anonymous'; // 解决跨域问题，也就是提示污染资源无法转换视频
    video.currentTime = 1; // 第一帧
    video.oncanplay = () => {
      canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
      canvas.height = video.clientHeight ? video.clientHeight : 320; //获取视频高度
      // 利用canvas对象方法绘图
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      // 转换成base64形式
      const _videoFirstimgsrc = canvas.toDataURL('image/png'); // 截取后的视频封面
      file.url = _videoFirstimgsrc; //重置文件的url为当前截取的封面，用于 el-upload展示
      video.remove();
      canvas.remove();
    };
    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration; // 视频时长
      if (Math.floor(duration) > 6000) {
        const index = fileList.findIndex((item) => item.uid == file.uid);
        fileList.splice(index, 1);
        ElMessage.error('上传的视频不能超过600S');
        return false;
      }
    });
  }
  if (isSize < 100 && isFlag) {
    forumImgFileList.value = fileList;
  }
  return isFlag && isSize < 100;
};
/**
 * 删除图片
 * @param file
 * @param fileList
 */
const handleRemoveUploadImg = (file, fileList) => {
  const index = forumImgFileList.value.findIndex((item) => item.uid == file.uid);
  forumImgFileList.value.splice(index, 1);
};

/**
 * 获取视频url
 */
const getVideoUrl = () => {
  downLaodTestHuaWEei(props.checkedVideId).then((res) => {
    if (res.code == 200) {
      loading.value = false;
      if (!res.data.authPath) {
        ElMessage.warning('视频正在缓存中，请稍后查看！！！');
      }
      videoUrl.value = res.data.authPath;
      coverPoster.value = res.data.authCoverUrl;
    } else {
      ElMessage.warning(res.msg);
    }
  });
};
/**
 * 获取视频
 */
const getVideo = () => {
  new Promise(function (resolve, reject) {
    Axios({
      method: 'get',
      url: props.checkedVideoUrl,
      headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' }
      // responseType: 'blob'
    }).then((res) => {
      const blob = res.data;
      const reader = new FileReader();
      reader.readAsDataURL(blob); // 转换为base64
      reader.onload = function () {
        videoUrl.value = reader.result; //文件数据转为video播放地址
        loading.value = false;
      };

      resolve(videoUrl.value);
    });
  });
};

//  ---定义emit---
const emit = defineEmits<{
  (e: 'closeVideoDialog'): void;
}>();

/**
 * 关闭视频
 */
const handleClose = () => {
  emit('closeVideoDialog');
};
</script>

<style lang="scss" scoped>
.videoTemp-main {
  width: 100%;
  height: 100%;
}
.video-class {
  width: 100%;
  height: 270px;
}
</style>
