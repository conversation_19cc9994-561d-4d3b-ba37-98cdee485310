<!-- 公式编辑 -->
<!-- 重要内容，请不要删除！！！
  0：操作符，无括号
  1：数字，有括号
  2：字符串，有括号
  3：系统函数，有括号
  4：图形函数，有括号
  5：属性函数，有括号
  6：定制函数，有括号
  7：快捷函数，无括号，自动生成表达式
  8: 时间函数，有括号
  9：表格函数，有括号
  10：数据大屏函数  有括号
  11 勘界函数  有括号
  55：线要素函数，自动生成表达式无括号
  56: 属性组函数，自动生成表达式，无括号
  98：全局常量，无括号
  99：变量，无括号 -->
<template>
  <div>
    <el-dialog
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      :title="'公式编辑'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
      width="80%"
      class="formula-dialog"
      @open="handleOpenFormulaDialog"
      :destroy-on-close="true"
    >
      <el-row style="margin-top: 8px">
        <el-col :span="14">
          <div ref="monacoEditorRef" class="monaco-editor" :style="{ height: scrollerEditorHeight }" />
        </el-col>
        <el-col :span="10">
          <div class="formula-tree">
            <div class="formula-tree-header">
              <el-input v-model="filterText" placeholder="请输入关键字" size="small" clearable />
            </div>
            <div class="formula-tree-content" :style="{ height: scrollerHeight }">
              <el-tree
                ref="treeRef"
                :data="defaultFormulationList"
                :props="defaultProps"
                :default-expanded-keys="defaultExpandedArr"
                :filter-node-method="filterNode"
                node-key="id"
                @node-click="handleNodeClick"
                :highlight-current="true"
              >
                <template #default="{ node, data }">
                  <div :class="[highlightIds.includes(data.id) ? 'highlight-tree' : '', data.old ? 'disabled-node' : '']">
                    <div v-if="data.name !== '界址点1' && data.name !== '界址点2'" @dblclick="handleDoubleNodeClick(data, node)">
                      {{ data.name }}
                      <span v-if="data.label" style="margin-left: 8px"> {{ data.label }}</span>
                    </div>
                    <div v-else>
                      <div>{{ data.name }}</div>
                      <div>
                        <el-select v-model="data.groupId" placeholder="请选择属性组" size="small" @change="(val) => handleChangeFieldName(data)">
                          <el-option v-for="item in data.groupList" :key="item.id" :label="item.typeName" :value="item.id" />
                        </el-select>
                      </div>
                      <div v-if="data.groupId">
                        <el-select v-model="data.fieldName" placeholder="请选择字段" size="small" @change="(val) => handleChangeFieldName(data)">
                          <el-option v-for="item in data.fieldList" :key="item.id" :label="item.fieldCn" :value="item.fieldName" />
                        </el-select>
                      </div>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="formula-footer">
        <div class="formula-footer-left">
          <div v-if="funcObj.type !== undefined">
            <div class="formula-footer-title">{{ funcTypeName }}</div>
            <div class="formula-footer-content">
              <div v-if="funcParamsList.length > 0">
                <div class="formula-footer-params">
                  <div v-for="(item, index) in funcParamsList" :key="index" class="formula-footer-param-item">
                    <div>参数{{ index + 1 }}：{{ item.param }}</div>
                    <div>描述：{{ item.desc }}</div>
                  </div>
                </div>
              </div>
              <div v-if="funcExample">
                <div class="formula-footer-example">
                  <div>示例：</div>
                  <div>{{ funcExample }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="formula-footer-right">
          <div style="margin: 8px 0">
            <el-button type="primary" size="small" @click="handleCheckIsValid"> 校验 </el-button>
          </div>
          <div class="formula-footer-result">
            <div>结果：</div>
            <div>{{ resultStr }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmitFormulation"> 确 定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 声明全局接口
declare global {
  interface Window {
    provider?: any;
    provider1?: any;
    hoverCode?: any;
    signatureText?: any;
    editor?: any;
    monaco?: any;
  }
}

import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { ElMessage, ElDialog, ElButton, ElRow, ElCol, ElInput, ElTree } from 'element-plus';
import * as monaco from 'monaco-editor';
import 'monaco-editor/esm/vs/basic-languages/html/html.contribution';
import { beautify } from 'js-beautify';
import { useRoute } from 'vue-router';
import { getOwnerListByModuleId, getFieldListByGroupId } from '@/api/modal';
import { getForumuList,exportForumuList, getForumuListByDataScreen, saveForumulation, checkForumulation, calculateForumulation } from '@/api/forumulation';
import authImg from '@/components/authImg/index.vue';
import { selectRules } from '@/api/modal';
import { defineProps, defineEmits, defineExpose, withDefaults } from 'vue';
import { useModalStore } from '@/store/modules/modal';

// Types
interface FormulationItem {
  name: string;
  type: number;
  isClick: boolean;
  children?: FormulationItem[];
  remark?: string;
}

interface Props {
  modelValue: boolean;
  expression?: string;
  isCopy?: boolean;
  inputType?: string;
  appType?: string;
  isYSCJ?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  expression: '',
  isCopy: false,
  inputType: '',
  appType: ' #console',
  isYSCJ: false
});

// Emits
const emit = defineEmits(['update:modelValue', 'closeFormulaEdit', 'submitFormulation']);

// Store
const modalStore = useModalStore();
const route = useRoute();

// State
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/downloadone/');
const defaultProps = ref({
  children: 'children',
  label: 'name',
  // 有一些过时的表达式，需要在点击的时候禁用这个写表达式
  disabled: 'disabled'
});

const expressionStr = ref('');
const defaultFormulationList = ref<FormulationItem[]>([
  {
    name: '操作符',
    type: 0,
    isClick: false,
    children: [
      { name: '+', type: 0, remark: '俩个值相加求和', isClick: true },
      { name: '-', type: 0, remark: '俩个值相减求差', isClick: true },
      { name: '*', type: 0, remark: '俩个值相乘求积', isClick: true },
      { name: '/', type: 0, remark: '俩个值相除求商', isClick: true }
    ]
  }
]);

const filterText = ref('');
const defaultExpandedArr = ref<number[]>([]);
const allHintList = ref<any[]>([]);
const funcObj = ref<any>({});
const funcParamsList = ref<any[]>([]);
const funcExample = ref('');
const highlightIds = ref<number[]>([]);
const resultStr = ref('');
const isCheckSuccess = ref(false);
const groupHintList = ref<any[]>([]);
const fieldHintList = ref<any[]>([]);
const noSaveFieldList = ref<any[]>([]);
const currentNodeList = ref<any[]>([]);
const treeList = ref<any[]>([]);

const optionSetting = ref({
  // value: '',
  automaticLayout: true,
  overviewRulerBorder: false,
  foldingStrategy: 'indentation',
  tabSize: 0,
  autoClosingBrackets: 'always',
  autoIndent: 'None',
  comments: {
    ignoreEmptyLines: true,
    insertSpace: true
  },
  cursorBlinking: 'Solid',
  cursorSmoothCaretAnimation: true,
  cursorSurroundingLines: 0,
  cursorSurroundingLinesStyle: 'all',
  cursorWidth: 2,
  diagnostics: true,
  minimap: {
    enabled: false
  },
  wordWrap: 'on',
  folding: true,
  scrollBeyondLastLine: false,
  renderLineHighlight: 'all',
  theme: 'vs',
  formatOnPaste: true,
  renderValidationDecorations: 'on',
  hover: {
    enabled: true,
    delay: 500
  }
});

const dialogHeight = ref(`${window.innerHeight - 300}px`);

// Computed
const moduleId = computed(() => modalStore.moduleId);
const elementType = computed(() => modalStore.elementType);
const pointGroupList = computed(() => modalStore.pointGroupList || []);
const isHasAcquistion = computed(() => modalStore.isHasAcquistion);
const isAllGroup = computed(() => modalStore.isAllGroup);
const checkedNodeItem = computed(() => modalStore.checkedNodeItem);
const scrollerHeight = computed(() => window.innerHeight - 450 + 'px');
const scrollerEditorHeight = computed(() => window.innerHeight - 400 + 'px');
const currentGroupItem = computed(() => modalStore.currentGroupItem);

const funcTypeName = computed(() => {
  const obj = funcObj.value;
  if (obj.type === 1) return '数字函数';
  else if (obj.type === 2) return '字符串函数';
  else if (obj.type === 3) return '系统函数';
  else if (obj.type === 4) return '图形函数';
  else if (obj.type === 55) return '线要素函数';
  else if (obj.type === 56) return '字段';
  else if (obj.type === 6) return '定制函数';
  else if (obj.type === 7) return '快捷函数';
  else if (obj.type === 0) return '操作符';
  else if (obj.type === 5) return '属性函数';
  else if (obj.type === 8) return '时间函数';
  else if (obj.type === 9) return '表格函数';
  else if (obj.type === 10) return '数据大屏函数';
  else if (obj.type === 11) return '勘界函数';
  else if (obj.type === 98) return '常量';
  else if (obj.type === 99) return '变量';
  else return '函数';
});

const monacoEditorRef = ref();
let editor: monaco.editor.IStandaloneCodeEditor | null = null;

const initMonaco = async () => {
  if (editor) {
    editor.dispose();
  }

  if (monacoEditorRef.value) {
    editor = monaco.editor.create(monacoEditorRef.value, {
      value: expressionStr.value,
      language: 'myLanguage',
      readOnly: false, // 显式设置可编辑
      ...optionSetting.value
    });

    // 添加错误处理
    try {
      editor.onDidChangeModelContent(() => {
        expressionStr.value = editor?.getValue() || '';
        handleOnEditorChange(expressionStr.value);
      });
    } catch (error) {
      console.error('编辑器初始化失败:', error);
    }
  }
};

// Watch;
// watch(
//   () => props.modelValue,
//   (val) => {
//     // debugger;
//     if (val && props.expression && props.expression !== '') {
//       // nextTick(() => {
//       editor?.setValue('11111');
//       // });
//     }
//   }
// );

watch(
  () => filterText.value,
  (val) => {
    treeRef.value?.filter(val);
  }
);
/**
 * 打开弹框之前清除上一次的 数据
 * return viod
 */
const handleClearFormulation = async () => {
  expressionStr.value = '';
  funcObj.value = {};
  funcExample.value = '';
  defaultExpandedArr.value = [];
  funcParamsList.value = [];
  currentNodeList.value = [];
  filterText.value = '';
  defaultFormulationList.value = [
    {
      name: '操作符',
      type: 0,
      isClick: false,
      children: [
        { name: '+', type: 0, remark: '俩个值相加求和', isClick: true },
        { name: '-', type: 0, remark: '俩个值相减求差', isClick: true },
        { name: '*', type: 0, remark: '俩个值相乘求积', isClick: true },
        { name: '/', type: 0, remark: '俩个值相除求商', isClick: true }
      ]
    }
  ];
};
/**
 * 打开自定义表达式弹框
 */
const handleOpenFormulaDialog = async () => {
  await handleClearFormulation();
  expressionStr.value = props.expression;
  // 调用接口数据
  await getFormulationList();
  if (elementType.value === 'line' && isHasAcquistion.value) {
    const groupObj = {
      name: '属性组函数',
      type: 56,
      isClick: false,
      children: []
    };
    defaultFormulationList.value.unshift(groupObj);
    handleAllGroupFunction();
  } else if (isHasAcquistion.value) {
    const groupObj = {
      name: '属性组函数',
      type: 56,
      isClick: false,
      children: []
    };
    defaultFormulationList.value.unshift(groupObj);
    handleAllGroupFunction();
  } else if (isAllGroup.value) {
    const groupObj = {
      name: '属性组函数',
      type: 56,
      isClick: false,
      children: []
    };
    defaultFormulationList.value.unshift(groupObj);
    handleAllGroupFunction();
  }
  isCheckSuccess.value = false;
  resultStr.value = '';
  console.log('当前页面中数据展示------', defaultFormulationList);

  await setMonacoColor();
  await setCustomHint();
  await setInputHint();
  setHoverCodeHint();
  handleAutoBacket();
  handleSetParamHint();
  handleRegistureCommand();
  handleDeleteAll();
  await initMonaco();
  checkEditorState(); // 添加状态检查
  await nextTick();
  focusEditor();
};

const handleClose = () => {
  noSaveFieldList.value = [];
  emit('closeFormulaEdit');
  window.provider?.dispose();
  window.provider1?.dispose();
  window.hoverCode?.dispose();
  window.signatureText?.dispose();
};

const getFieldList = (val: number) => {
  const params = {
    id: val
  };
  getFieldListByGroupId(params).then((res) => {
    if (res.code == 200) {
      defaultFormulationList.value.map((line) => {
        if (line.name == '线要素函数') {
          line.children?.map((c) => {
            if (c.name == 'childPoint1' || c.name == 'childPoint2') {
              c.children?.map((item) => {
                item.fieldList = [];
                item.fieldList = JSON.parse(JSON.stringify(res.data));
              });
            }
          });
        }
      });
      defaultFormulationList.value = JSON.parse(JSON.stringify(defaultFormulationList.value));
      defaultExpandedArr.value = [55];
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleCheckIsValid = async () => {
  expressionStr.value = await checkForumalationIsValid();
  if (expressionStr.value == '') {
    ElMessage.info('请输入表达式');
    return false;
  }
  const params = {
    expression: expressionStr.value
  };
  checkForumulation(params).then((res) => {
    if (res.code == 200) {
      if (!props.isCopy) {
        isCheckSuccess.value = handleFormationToFieldType(res.data);
      } else {
        resultStr.value = `验证通过`;
        isCheckSuccess.value = true;
      }
    } else {
      ElMessage.error(res.msg);
      isCheckSuccess.value = false;
    }
  });
};

const handleCalculateForumulation = async () => {
  expressionStr.value = await checkForumalationIsValid();
  if (expressionStr.value == '') {
    ElMessage.info('请输入表达式');
    return false;
  }
  const params = {
    expression: expressionStr.value
  };
  calculateForumulation(params).then((res) => {
    if (res.code == 200) {
      resultStr.value = `${res.data}`;
      isCheckSuccess.value = true;
    } else {
      ElMessage.error(res.msg);
      isCheckSuccess.value = false;
    }
  });
};

const handleFormationToFieldType = (val: string) => {
  const resultVal = val.substring(val.lastIndexOf('.') + 1, val.length);
  let type = props.inputType;
  if (props.isYSCJ) {
    type = 'String';
  }

  if (resultVal === 'Double' || resultVal === 'Int' || resultVal === 'Integer' || resultVal === 'Long') {
    const list = ['String', 'Long', 'Double', 'Date'];
    if (list.includes(type)) {
      resultStr.value = '验证通过';
      return true;
    } else {
      resultStr.value = '表达式返回值和字段返回值不能相互转换';
      return false;
    }
  } else if (resultVal === 'String') {
    const list = ['String', 'Date', 'Double', 'Pic', 'qm'];
    if (list.includes(type)) {
      resultStr.value = '验证通过';
      return true;
    } else {
      resultStr.value = '表达式返回值和字段返回值不能相互转换';
      return false;
    }
  } else if (resultVal === 'Pic') {
    const list = ['Pic'];
    if (list.includes(type)) {
      resultStr.value = '验证通过';
      return true;
    } else {
      resultStr.value = '表达式返回值和字段返回值不能相互转换';
      return false;
    }
  } else if (resultVal === 'List') {
    resultStr.value = '表达式返回值和字段返回值不能相互转换';
    return false;
  } else {
    resultStr.value = '验证失败，请检查表达式';
    return false;
  }
};

const handleSubmitFormulation = () => {
  if (!props.isCopy) {
    if (isCheckSuccess.value && expressionStr.value != '') {
      emit('submitFormulation', expressionStr.value);
      emit('closeFormulaEdit');
    } else if (isCheckSuccess.value && expressionStr.value == '') {
      emit('submitFormulation', expressionStr.value);
      emit('closeFormulaEdit');
    } else if (!isCheckSuccess.value) {
      ElMessage.info('请先点击校验，校验表达式!');
    } else if (!isCheckSuccess.value && expressionStr.value != '') {
      ElMessage.error('表达式错误，请修改表达式！');
    } else {
      ElMessage.info(resultStr.value);
    }
  } else {
    if (isCheckSuccess.value) {
      emit('submitFormulation', expressionStr.value);
      emit('closeFormulaEdit');
    } else {
      ElMessage.info('请先点击校验，校验表达式!');
    }
  }
};

// Monaco Editor 相关方法
const setMonacoColor = async () => {
  // 设置方法的颜色,从数据库中取出方法
  const funcList = allHintList.value.filter((i) => {
    const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
    if (list.includes(i.type)) {
      i.kind = monaco.languages.CompletionItemKind.Function;
      return list.includes(i.type);
    }
  });
  let reg = '/\\b(';
  funcList.forEach((item, index) => {
    if (index == funcList.length - 1) {
      reg += `${item.name}`;
    } else {
      reg += `${item.name}|`;
    }
  });
  reg += ')\\b/i';
  const formationsfunc = eval(reg);

  let str = '/\\b(';
  const varList = allHintList.value.filter((val) => {
    const list = [98, 99];
    if (list.includes(val.type)) {
      val.kind = monaco.languages.CompletionItemKind.Variable;
      return list.includes(val.type);
    }
  });
  varList.forEach((item, index) => {
    if (index === varList.length - 1) {
      str += `${item.name.substring(1)}`;
    } else {
      str += `${item.name.substring(1)}|`;
    }
  });
  str += ')\\b/i';
  const formationsValible = eval(str);

  monaco.languages.register({ id: 'myLanguage' });
  monaco.languages.setMonarchTokensProvider('myLanguage', {
    ignoreCase: false,
    tokenizer: {
      root: [
        [/CHILDPOINT1|CHILDPOINT2/, 'custom-number'],
        [/[$]/, 'custom-number'],
        [formationsValible, { token: 'custom-number' }],
        [formationsfunc, { token: 'keyword' }],
        [/[+]|[-]|[*]|[/]|[%]|[>]|[<]|[=]|[!]|[:]|[&&][||]/, { token: 'custom-oper' }],
        [/[(]|[)]/, { token: 'custom-let' }]
      ]
    }
  });

  monaco.editor.defineTheme('myTheme', {
    base: 'vs',
    inherit: true,
    rules: [
      { token: 'custom-number', foreground: '#7944F8' },
      { token: 'custom-string', foreground: '#0081ff' },
      { token: 'custom-sys', foreground: '#13ce66' },
      { token: 'custom-let', foreground: '#8ec2f2' },
      { token: 'custom-oper', foreground: '#f95e13' }
    ],
    colors: {
      'editor.background': '#e6ebf5',
      'editorLineNumber.foreground': '#0081ff',
      'editorLineNumber.activeForeground': '#7944F8',
      'editor.lineHighlightBackground': '#e6ebff',
      'editorGutter.background': '#e6ebf5'
    }
  });
  monaco.editor.setTheme('myTheme');
};

const setCustomHint = async () => {
  const commonUse: string[] = [];
  window.provider = monaco.languages.registerCompletionItemProvider('myLanguage', {
    provideCompletionItems: function (model, position) {
      const textUnitPosition = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      });

      let match = textUnitPosition.match(/(\S+)$/);
      if (!match) return [];
      match = match[0].toUpperCase();

      const suggestions: any[] = [];
      const handleSuggestions = (arr: any[], type: string, detail: string) => {
        arr.forEach(async (item) => {
          let insertText = `${item.name}`;
          const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
          const noList = [98, 99];
          if (list.includes(item.type)) {
            insertText = `${item.name}()`;
          }
          suggestions.push({
            label: item.name,
            kind: item.kind,
            sortText: commonUse.includes(item.name) ? '0' : '1',
            detail: item.remark,
            insertText: insertText
          });
        });
      };

      handleSuggestions(allHintList.value, 'Field', '库表信息');
      handleSuggestions(groupHintList.value, 'Field', '属性组');
      handleSuggestions(fieldHintList.value, 'Field', '属性组字段');
      return {
        incomplete: true,
        suggestions: suggestions
      };
    }
  });
};

const handleAutoBacket = () => {
  const config = {
    surroundingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '<', close: '>' },
      { open: "'", close: "'" },
      { open: '"', close: '"' }
    ],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: "'", close: "'", notIn: ['string', 'comment'] },
      { open: '"', close: '"', notIn: ['string', 'comment'] }
    ]
  };
  monaco.languages.setLanguageConfiguration('myLanguage', config);
};

const changeModelChange = (e: any) => {
  const currentPos = e.changes[0].rangeOffset;
  const model = monaco.editor.getModels()[0];
  const latestContent = e.changes[e.changes.length - 1].text;
  const openIndex = latestContent.indexOf('(');
  const closeIndex = latestContent.indexOf(')');
  const pos = editor.getPosition();
  const currentLine = pos.lineNumber;
  const lineText = model.getLineContent(currentLine);

  let pattern = '/';
  let label = '';
  if (openIndex != -1 && closeIndex != -1) {
    label = latestContent.substring(0, openIndex);
    for (let i = 0; i < latestContent.length; i++) {
      if (latestContent[i] == ')' || latestContent[i] == '(') {
        pattern += `\\${latestContent[i]}`;
      } else if (i == latestContent.length - 1) {
        pattern += '/g';
      } else {
        pattern += latestContent[i];
      }
    }
  }
  pattern += '/g';
  let posArr: number[] = [];
  if (pattern != '//g') {
    posArr = handleContentFindPosition(lineText, pattern);
  }

  let tempStr = latestContent;
  if (latestContent.indexOf('$') == -1) {
    tempStr = latestContent.substring(0, latestContent.length - 2);
  }

  const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
  const findItem = allHintList.value.find((item) => item.name == tempStr && list.includes(item.type));

  const isBacket = findItem && findItem.param != null && findItem.paramDesc != null;
  if (posArr.length > 0 && isBacket) {
    const col = posArr[1];
    const row = pos.lineNumber;
    const newPosition = {
      lineNumber: row,
      column: col
    };
    nextTick(() => {
      editor.setPosition(newPosition);
      editor.focus();
      editor.trigger('keyboard', 'editor.action.triggerParameterHints');
    });
  }
};

const setEditorValue = (val: string) => {
  if (editor) {
    editor.pushUndoStop();
    const model = editor.getModel();
    if (model) {
      editor.executeEdits('', [
        {
          range: model.getFullModelRange(),
          text: val
        }
      ]);
    }
    editor.pushUndoStop();
  }
};

const setInputHint = async () => {
  window.provider1 = monaco.languages.registerCompletionItemProvider('myLanguage', {
    triggerCharacters: ['.', '$', '('],
    provideCompletionItems: (model, position) => {
      const line = position.lineNumber;
      const column = position.column;
      const content = model.getLineContent(line);
      const sys = content[column - 2];
      const codePre = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      });
      const word = model.getWordUntilPosition(position);
      const suggestions: any[] = [];

      if (sys == '$') {
        const arr = allHintList.value.filter((val) => {
          const list = [98, 99];
          if (list.includes(val.type)) {
            val.kind = monaco.languages.CompletionItemKind.Variable;
            return list.includes(val.type);
          }
        });
        arr.forEach((item) => {
          suggestions.push({
            label: item.name,
            kind: item.kind,
            insertText: item.name.substring(1),
            detial: item.remark,
            range: {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: word.startColumn,
              endColumn: word.endColumn
            }
          });
        });
      } else if (sys == '.') {
        const pointList = handleInputPointToHint();
        pointList.forEach((item) => {
          let insertText = `${item.name}`;
          const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
          if (list.includes(item.type)) {
            insertText = `${item.name}()`;
          }
          suggestions.push({
            label: item.name,
            kind: item.kind,
            insertText: insertText,
            detial: item.remark,
            range: {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: word.startColumn,
              endColumn: word.endColumn
            }
          });
        });
      } else {
        suggestions.push({
          label: word.word,
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: word.word,
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range: {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn
          }
        });
      }
      return {
        suggestions: suggestions
      };
    }
  });
};

const setHoverCodeHint = () => {
  window.hoverCode = monaco.languages.registerHoverProvider('myLanguage', {
    provideHover: function (model, position) {
      const line = position.lineNumber;
      const content = model.getLineContent(line);
      const codePre = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      });
      let contents: any[] = [];
      let obj = {};
      let valueStr = '';
      let valueDesc = '';
      let objDesc = {};

      const handleSuggestions = (arr: any[], type: string, detail: string) => {
        contents = [];
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        arr.forEach(async (item) => {
          if (list.includes(item.type) && content.substring(0, content.length - 2) == item.name) {
            if (item.param != null || item.param != undefined) {
              obj = {};
              valueStr = '';
              const pKeys = Object.keys(item.param).sort();
              pKeys.forEach((p, index) => {
                if (index == pKeys.length - 1) {
                  valueStr += `参数${index + 1}:***${item.param[p]}*** `;
                } else {
                  valueStr += `参数${index + 1}:***${item.param[p]}*** | `;
                }
                obj = { value: valueStr };
              });
              contents.push(obj);
            } else {
              obj = { value: '暂无参数' };
              contents.push(obj);
            }

            if (item.paramDesc != null || item.paramDesc != undefined) {
              valueDesc = '';
              objDesc = {};
              const dKeys = Object.keys(item.paramDesc).sort();
              dKeys.forEach((d, index) => {
                if (index == dKeys.length - 1) {
                  valueDesc += `参数${index + 1}:***${item.paramDesc[d]}*** `;
                } else {
                  valueDesc += `参数${index + 1}:***${item.paramDesc[d]}*** | `;
                }
                objDesc = { value: valueDesc };
              });
              contents.push(objDesc);
            }
          }
        });
      };

      handleSuggestions(allHintList.value, 'Field', '库表信息');
      return {
        range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
        contents: contents
      };
    }
  });
};

const handleContentFindPosition = (content: string, pattern: string) => {
  const pat = eval(pattern);
  const matches = content.match(pat);
  const arr: number[] = [];
  if (matches) {
    for (let i = 0; i < matches.length; i++) {
      const start_index = content.indexOf(matches[i]);
      const end_index = start_index + matches[i].length;
      arr.push(start_index);
      arr.push(end_index);
    }
  }
  return arr;
};

const handleInputPointToHint = () => {
  const text = editor.getValue();
  const textList = text.split('.');
  const lastContent = textList[textList.length - 2];
  const Sindex = lastContent.indexOf('(');
  const eIndex = lastContent.indexOf(')');
  let str = '';
  if (Sindex != -1 && eIndex != -1) {
    str = lastContent.substring(0, Sindex);
  } else {
    str = lastContent;
  }
  let returnVal = '';
  let pointList: any[] = [];
  allHintList.value.forEach((item) => {
    if (item.name == str) {
      returnVal = item.returnVal;
    }
  });
  if (returnVal != null || returnVal != '') {
    pointList = allHintList.value.filter((item) => item.parentVal == returnVal);
  } else {
    pointList = allHintList.value;
  }
  return pointList;
};

const handleSetParamHint = () => {
  window.signatureText = monaco.languages.registerSignatureHelpProvider('myLanguage', {
    signatureHelpTriggerCharacters: ['(', ',', '()', ')'],
    provideSignatureHelp: (model, position, token) => {
      const line = position.lineNumber;
      const content = model.getLineContent(line);
      const p = /\b(\w+)\s*\(/;
      const pointPos = content.lastIndexOf('.');
      const leftBacketPos = content.lastIndexOf('(');
      const rightBacketPos = content.lastIndexOf(')');
      let newLastContent = '';
      if (pointPos != -1 && pointPos < leftBacketPos) {
        newLastContent = content.substring(pointPos + 1, leftBacketPos);
      } else {
        newLastContent = content.substring(0, leftBacketPos);
      }
      const signatures: any[] = [];
      let parameters: any[] = [];
      const currentItem = allHintList.value.find((item) => item.name === newLastContent);
      if (currentItem && currentItem.param && currentItem.paramDesc) {
        const pkeys = Object.keys(currentItem.param).sort();
        const paramList: string[] = [];
        pkeys.map((k, index) => {
          paramList.push(`${currentItem.param[k]} $param${index + 1}`);
        });
        const paramStr = paramList.join(',');

        const dKeys = Object.keys(currentItem.paramDesc).sort();
        parameters = pkeys.map((param, index) => {
          return {
            label: `${currentItem.param[param]} $param${index + 1}`,
            documentation: currentItem.paramDesc[dKeys[index]]
          };
        });
        signatures.push({
          label: `${currentItem.name}(${paramStr})`,
          parameters: parameters
        });
      }

      let paramContent = '';
      if (leftBacketPos != -1 && rightBacketPos != -1) {
        paramContent = content.substring(leftBacketPos + 1, rightBacketPos);
      }
      let activeParameter = 0;
      const count = paramContent.split(',').length;
      if (count > 0 && count <= parameters.length) {
        activeParameter = count - 1;
      }

      return {
        dispose: () => {},
        value: {
          activeParameter: activeParameter,
          activeSignature: 0,
          signatures: signatures
        }
      };
    }
  });
};

const handleRegistureCommand = () => {
  if (editor) {
    editor.addAction({
      id: 'formateCodeForce',
      label: '强制格式化',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyMod.Alt | monaco.KeyCode.KeyF],
      contextMenuGroupId: '9_cutcopypaste',
      run: () => {
        const a = editor?.getModel()?.getValue() || '';
        const b = beautify(a);
        editor?.executeEdits('', [
          {
            range: new monaco.Range(1, 1, editor.getModel()?.getLineCount() || 1, 1),
            text: b
          }
        ]);
      }
    });

    editor.addAction({
      id: 'deleteAllText',
      label: '一键清空',
      keybindings: [monaco.KeyMod.Alt | monaco.KeyCode.Delete],
      contextMenuGroupId: '9_cutcopypaste',
      run: () => {
        editor?.executeEdits('', [
          {
            range: new monaco.Range(1, 1, editor.getModel()?.getLineCount() || 1, 1),
            text: ''
          }
        ]);
      }
    });
  }
};

const handleDeleteAll = () => {
  if (editor) {
    editor.addAction({
      id: 'deleteAllText',
      label: '一键清空',
      keybindings: [monaco.KeyMod.Alt | monaco.KeyCode.Delete],
      contextMenuGroupId: '9_cutcopypaste',
      run(ed, opt) {
        editor.executeEdits('', [
          {
            range: new monaco.Range(1, 1, editor.getModel()?.getLineCount() || 1, 1),
            text: ''
          }
        ]);
      }
    });
  }
};
const handleQuicklyFixError = () => {
  monaco.languages.registerCodeActionProvider('myLanguage', {
    provideCodeActions: function (model, token, context) {
      const fixes = [
        {
          title: '修复波浪线',
          fix: function (editor, languageService) {},
          edit: {
            range: new monaco.Range(1, 1, model.getLineCount() + 1, 1),
            text: 'max(1,2)'
          }
        }
      ];
      return {
        actions: fixes,
        dispose: () => {}
      };
    }
  });
};

// 自定义表达式添加属性组
const handleAllGroupFunction = () => {
  const params = { moduleId: moduleId.value };
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  selectRules(params).then((res) => {
    if (res.code == 200) {
      if (res.data.length != 0) {
        treeList.value = res.data;
        handleForAllGroup(res.data);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 循环处理数据
const handleForAllGroup = (list: any[]) => {
  let fieldGroupModelList: any[] = [];
  const childrenList: any[] = [];
  const fieldList: any[] = [];
  const obj = {};
  let currentNodeItem: any[] = [];
  list.forEach((nodeItem) => {
    const level = nodeItem.levelNum;
    currentNodeItem = {
      name: nodeItem.typeName,
      type: 56,
      isClick: false,
      level: level,
      id: nodeItem.id,
      children: [],
      remark: '属性组',
      kind: monaco.languages.CompletionItemKind.Property
    };
    currentNodeList.value.push(currentNodeItem);
    fieldGroupModelList = JSON.parse(JSON.stringify(nodeItem.fieldGroupModelList));
    fieldGroupModelList.forEach((item, index) => {
      if (item.groupScope == 1) {
        if (item.ruleAttribution && item.ruleAttribution.type == 'graphicalPoint') {
          currentNodeItem.children.push({
            name: item.typeName,
            isAttrType: '点',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          childrenList.push({
            name: `${item.typeName}`,
            isAttrType: '点',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          handleFiledModelGroupFunction(
            index,
            childrenList,
            fieldList,
            item.fieldModelList,
            level,
            item.groupScope,
            item.ruleAttribution.type,
            currentNodeItem
          );
        } else if (item.ruleAttribution && item.ruleAttribution.type == 'graphicalLine') {
          currentNodeItem.children.push({
            name: item.typeName,
            isAttrType: '线',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          childrenList.push({
            name: `${item.typeName}`,
            isAttrType: '线',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          handleFiledModelGroupFunction(
            index,
            childrenList,
            fieldList,
            item.fieldModelList,
            level,
            item.groupScope,
            item.ruleAttribution.type,
            currentNodeItem
          );
        } else {
          currentNodeItem.children.push({
            name: item.typeName,
            isAttrType: '面',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          childrenList.push({
            name: `${item.typeName}`,
            isAttrType: '面',
            isClick: false,
            level: level,
            label: item.fieldCn,
            type: 56,
            id: item.id,
            fieldType: item.fieldType,
            children: [],
            remark: '属性组',
            kind: monaco.languages.CompletionItemKind.Property
          });
          handleFiledModelGroupFunction(
            index,
            childrenList,
            fieldList,
            item.fieldModelList,
            level,
            item.groupScope,
            item.ruleAttribution.type,
            currentNodeItem
          );
        }
      } else {
        const type = undefined;
        currentNodeItem.children.push({
          name: item.typeName,
          isClick: false,
          level: level,
          label: item.fieldCn,
          type: 56,
          id: item.id,
          fieldType: item.fieldType,
          children: [],
          remark: '属性组',
          kind: monaco.languages.CompletionItemKind.Property
        });
        childrenList.push({
          name: `${item.typeName}`,
          isClick: false,
          level: level,
          label: item.fieldCn,
          type: 56,
          id: item.id,
          fieldType: item.fieldType,
          children: [],
          remark: '属性组',
          kind: monaco.languages.CompletionItemKind.Property
        });

        if (item.typeName == currentGroupItem.value.typeName && noSaveFieldList.value.length > 0) {
          item.fieldModelList = JSON.parse(JSON.stringify(noSaveFieldList.value));
          handleFiledModelGroupFunction(index, childrenList, fieldList, item.fieldModelList, level, item.groupScope, type, currentNodeItem);
        } else {
          handleFiledModelGroupFunction(index, childrenList, fieldList, item.fieldModelList, level, item.groupScope, type, currentNodeItem);
        }
      }
    });
    if (nodeItem.list.length > 0) {
      handleForAllGroup(nodeItem.list);
    }
    groupHintList.value = childrenList;
    fieldHintList.value = fieldList;
    const list = JSON.parse(JSON.stringify(defaultFormulationList.value));
    list.map((item) => {
      if (item.type == 56) {
        item.children = JSON.parse(JSON.stringify(currentNodeList.value));
      }
    });
    defaultFormulationList.value = JSON.parse(JSON.stringify(list));
  });
};

/**
 * 点击当前函数节点
 * @param data 当前绑定的树节点的数据
 * @param node 当前绑定的树节点
 * if (data.old) return;  有一些过期的表达式不在使用，所以再点击的时候直接禁用当前表达式
 */
const handleNodeClick = (data: any, node: any) => {
  // if (data.old) return;
  funcObj.value = {};
  funcExample.value = '';
  defaultExpandedArr.value = [];
  funcParamsList.value = [];
  if (!defaultExpandedArr.value.includes(data.type)) {
    defaultExpandedArr.value.push(data.type);
  }
  const list = [1, 2, 3, 4, 5, 6, 7, 98, 99, 8, 9, 10, 11];
  const noList = [55, 56, 0];
  if (list.includes(data.type) && data.isClick) {
    funcObj.value = data;
    funcExample.value = data.example.replace(/&gt;/g, '>');
    if (data.param && data.paramDesc) {
      const pkeys = Object.keys(data.param).sort();
      const dKeys = Object.keys(data.paramDesc).sort();
      funcParamsList.value = pkeys.map((param, index) => ({
        param: data.param[param],
        desc: data.paramDesc[dKeys[index]]
      }));
    }
  }
  if (noList.includes(data.type) && data.isClick) {
    if (data.type == 0) {
      funcObj.value = data;
    } else {
      if (data.isClick) {
        if (isAllGroup.value) {
          if (data.isChild) {
            const obj = {
              groupName: node.parent.parent.data.name,
              treeName: node.parent.parent.parent.data.name,
              fieldName: node.parent.data.name,
              fieldLabel: node.parent.data.label,
              type: 56,
              isChild: true,
              name: data.name
            };
            funcObj.value = obj;
          } else {
            const obj = {
              groupName: node.parent.data.name,
              treeName: node.parent.parent.data.name,
              type: 56,
              isChild: false,
              name: data.name
            };
            funcObj.value = obj;
          }
        } else {
          const nodeItem = JSON.parse(JSON.stringify(checkedNodeItem.value));
          if (data.isChild) {
            const obj = {
              groupName: node.parent.parent.data.name,
              treeName: node.parent.parent.parent.data.name,
              fieldName: node.parent.data.name,
              fieldLabel: node.parent.data.label,
              type: 56,
              isChild: true,
              name: data.name
            };
            funcObj.value = obj;
          } else {
            const obj = {
              groupName: node.parent.data.name,
              treeName: node.parent.parent.data.name,
              type: 56,
              isChild: false,
              name: data.name
            };
            funcObj.value = obj;
          }
        }
      }
    }
  }
};

// 过滤树节点
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

/**
 * 双击树事件，根据点击的事件来映射当前点击的表达式
 * @param data 当前绑定的树节点的数据
 * @param node 当前绑定的树节点
 * if (data.old) return;  有一些过期的表达式不在使用，所以再点击的时候直接禁用当前表达式
 */
const handleDoubleNodeClick = (data: any, node: any) => {
  if (data.old) return;
  const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
  const noList = [99, 98, 55, 0];
  const pList = [56];
  let textStr = '';
  if (list.includes(data.type)) {
    textStr = `${data.name}()`;
  }
  if (noList.includes(data.type)) {
    textStr = `${data.name}`;
  }
  if (pList.includes(data.type) && data.isClick) {
    if (data.isChild && data.type == 56) {
      textStr = handleSpeicalField(data, node);
    } else {
      if (data.level == 1 && data.groupScope == 2) {
        textStr = handleRootNodeField(data, node);
      } else {
        textStr = handleNomalField(data, node);
      }
    }
  }
  const pos = editor?.getPosition();
  if (pos) {
    editor?.executeEdits('', [
      {
        range: new monaco.Range(pos.lineNumber, pos.column, pos.lineNumber, pos.column),
        text: textStr
      }
    ]);
    editor?.setPosition({
      lineNumber: pos.lineNumber,
      column: pos.column + textStr.length
    });
    editor?.focus();
  }
};

// 除根结点其他节点深层的属性组
const handleNomalField = (data: any, node: any) => {
  let text = '';
  const name = node.parent.parent.data.name;
  let transitionText = '.toStr()';
  if (data.fieldType && data.fieldType == 'Double') {
    transitionText = '.toDb()';
  } else if (data.fieldType && data.fieldType == 'Pic') {
    transitionText = '.toPicture("",2,0,130,0)';
  } else {
    transitionText = '.toStr()';
  }
  if (data.groupScope == 1) {
    text = `getFactorField("${name}",0,"${data.marker}","${node.parent.data.name}",0,"${data.name}")${transitionText}`;
  } else {
    // 2025/7/8 年修改成 非共享组且不是特殊字段的时候试用 getCommonField
    text = `getCommonField("${name}",0,"${node.parent.data.name}",0,"${data.name}")${transitionText}`;
  }
  return text;
};

// 根节点生成的表达式
const handleRootNodeField = (data: any, node: any) => {
  let text = '';
  const name = node.parent.parent.data.name;
  let transitionText = '.toStr()';
  if (data.fieldType && data.fieldType == 'Double') {
    transitionText = '.toDb()';
  } else if (data.fieldType && data.fieldType == 'Pic') {
    transitionText = '.toPicture("",2,0,130,0)';
  } else {
    transitionText = '.toStr()';
  }
  text = `getCommonField("${name}.${node.parent.data.name}.${data.name}")${transitionText}`;
  return text;
};

// 处理特殊字段
const handleSpeicalField = (data: any, node: any) => {
  let text = '';
  const name = node.parent.parent.parent.data.name;

  if (data.special === 'sfz') {
    const SFZSBOptions = JSON.parse(JSON.stringify(data.SFZSBOptions));
    const list = SFZSBOptions.filter((ite) => ite.enName === data.name);

    if (data.groupScope == 1) {
      text = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    } else {
      text = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    }
  } else if (data.special === 'yhk') {
    const YHKOptions = JSON.parse(JSON.stringify(data.YHKOptions));
    const list = YHKOptions.filter((ite) => ite.enName === data.name);

    if (data.groupScope == 1) {
      text = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    } else {
      text = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    }
  } else if (data.special === 'dw') {
    const DZWSBOptions = [
      { label: 0, text: '名称', enName: 'dwmc' },
      { label: 1, text: '图片', enName: 'dwtp' },
      { label: 2, text: '类型', enName: 'dwlx' },
      { label: 3, text: '描述', enName: 'dwms' }
    ];
    const list = DZWSBOptions.filter((ite) => ite.enName === data.name);
    if (data.groupScope == 1) {
      text = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}.${data.name}").toStr()`;
      expressionStr.value = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    } else {
      text = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}.${data.name}").toStr()`;
      expressionStr.value = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    }
  } else if (data.special === 'zw') {
    const DZWSBOptions = [
      { label: 0, text: '名称', enName: 'zwmc' },
      { label: 1, text: '图片', enName: 'zwtp' },
      { label: 2, text: '类型', enName: 'zwlx' },
      { label: 3, text: '描述', enName: 'zwms' }
    ];
    const list = DZWSBOptions.filter((ite) => ite.enName === data.name);
    if (data.groupScope == 1) {
      text = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}.${data.name}").toStr()`;
      expressionStr.value = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    } else {
      text = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}.${data.name}").toStr()`;
      expressionStr.value = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}_${list[0].label}").toStr()`;
    }
  } else if (data.special === 'table') {
    let transitionText = `.getTableData(0,"${data.name}")`;
    if (data.fieldType && data.fieldType == 'Pic') {
      transitionText = `.getTableField(0,"${data.name}").toPicture("",2,0,130,0)`;
    }

    if (data.groupScope == 1) {
      text = `getFactorField("${name}",0,"${data.marker}","${node.parent.parent.data.name}",0,"${node.parent.data.name}")${transitionText}`;
    } else {
      text = `getFlatField("${name}",0,"${node.parent.parent.data.name}",0,"${node.parent.data.name}")${transitionText}`;
    }
  }
  return text;
};

// 编辑器内容变化处理
const handleOnEditorChange = (value: string) => {
  if (!value || value == '') {
    expressionStr.value = '';
    resultStr.value = '';
  } else {
    isCheckSuccess.value = false;
    resultStr.value = '';
  }
};

// 编辑器挂载处理
const handleOnEditorMounted = (editorInstance: monaco.editor.IStandaloneCodeEditor) => {
  window.editor = editorInstance;
  window.monaco = monaco;
  editorInstance.onDidChangeModelContent(changeModelChange);
};

// Lifecycle
onMounted(async () => {
  await nextTick(); // 等待 DOM 更新
  initMonaco();
  window.addEventListener('setItem', () => {
    const list = JSON.parse(sessionStorage.getItem('noSaveField') || '[]');
    noSaveFieldList.value = list;
  });
});

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose();
  }
  window.provider?.dispose();
  window.provider1?.dispose();
  window.hoverCode?.dispose();
  window.signatureText?.dispose();
});

// Template refs
const treeRef = ref();

// Expose necessary methods/properties
defineExpose({
  handleOpenFormulaDialog,
  handleClose,
  handleCheckIsValid,
  handleSubmitFormulation
});

/**
 * 根据类型查询公式列表
 * 因为数据大屏和字段设置、报告设置调用的接口不一致，所以这里需要写2次
 * 数据大屏 ：#console 报告设置：#word 字段设置：#field
 */
const getFormulationList = async () => {
  const apiCall =
    props.appType === '#console'
      ? getForumuListByDataScreen({ type: 0, appType: props.appType })
      : exportForumuList({ type: 0, appType: props.appType });

  await apiCall.then((res) => {
    if (res.code == 200) {
      if (res.data && res.data.length != 0) {
        const list = res.data.map((i) => {
          const funcTypes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
          if (funcTypes.includes(i.type)) {
            i.kind = monaco.languages.CompletionItemKind.Function;
          } else {
            i.kind = monaco.languages.CompletionItemKind.Variable;
          }
          return i;
        });
        // 提示的方法
        allHintList.value = JSON.parse(JSON.stringify(list));
        // 默认的所有公式
        const children1: any[] = [];
        const children2: any[] = [];
        const children3: any[] = [];
        const children4: any[] = [];
        const children5: any[] = [];
        const children6: any[] = [];
        const children7: any[] = [];
        const children8: any[] = [];
        const children9: any[] = [];
        const children10: any[] = [];
        const children11: any[] = [];
        const children99: any[] = [];
        const children98: any[] = [];

        list.forEach((ite) => {
          ite.isClick = true;
          ite.disabled = ite.old ? true : false;
          switch (ite.type) {
            case 1:
              children1.push(ite);
              break;
            case 2:
              children2.push(ite);
              break;
            case 3:
              children3.push(ite);
              break;
            case 4:
              children4.push(ite);
              break;
            case 5:
              children5.push(ite);
              break;
            case 6:
              children6.push(ite);
              break;
            case 7:
              children7.push(ite);
              break;
            case 8:
              children8.push(ite);
              break;
            case 9:
              children9.push(ite);
              break;
            case 10:
              children10.push(ite);
              break;
            case 11:
              children11.push(ite);
              break;
            case 98:
              children98.push(ite);
              break;
            case 99:
              children99.push(ite);
              break;
          }
        });
        const addFormulationItem = (name: string, type: number, children: any[]) => {
          if (children.length > 0) {
            defaultFormulationList.value.push({
              name,
              type,
              isClick: false,
              children
            });
          }
        };

        addFormulationItem('数字函数', 1, children1);
        addFormulationItem('字符串函数', 2, children2);
        addFormulationItem('系统函数', 3, children3);
        addFormulationItem('图形函数', 4, children4);
        addFormulationItem('属性函数', 5, children5);
        addFormulationItem('定制函数', 6, children6);
        addFormulationItem('快捷函数', 7, children7);
        addFormulationItem('时间函数', 8, children8);
        addFormulationItem('表格函数', 9, children9);
        addFormulationItem('数据大屏函数', 10, children10);
        addFormulationItem('勘界专属函数', 11, children11);
        addFormulationItem('常量', 98, children98);
        addFormulationItem('变量', 99, children99);
      }
    } else {
      ElMessage.error(res.msg);
      return;
    }
  });
};
const checkForumalationIsValid = async () => {
  const str = editor?.getModel()?.getValue() || '';
  const py = /["][^"]*["]/g;
  const strList = str.match(py);
  const temp = str.replace(/["][^"]*["]/g, '#temp#');
  const tempStr = temp.replace(/\s+/g, '');
  let index = 0;
  const endStr = tempStr.replace(/#temp#/g, () => {
    const replacement = strList?.[index] || '';
    index++;
    return replacement;
  });
  expressionStr.value = endStr;
  return expressionStr.value;
};

const handleChangeFieldName = (val: any) => {
  let groupName = '';
  val.groupList.forEach((group: any) => {
    if (val.groupId == group.id) {
      groupName = group.typeName;
    }
  });
  let point1 = '';
  let point2 = '';
  if (val.name == '界址点1') {
    point1 = `$CHILDPOINT1.'${groupName}'.'${val.fieldName}'`;
  } else if (val.name == '界址点2') {
    point2 = `$CHILDPOINT2.'${groupName}'.'${val.fieldName}'`;
  }
  if (point1 !== '') {
    expressionStr.value += point1;
  }
  if (point2 != '') {
    expressionStr.value += point2;
  }
};

interface FieldModel {
  fieldName: string;
  fieldCn: string;
  fieldType: string;
  valueMethod?: string;
  attribution?: {
    list?: number[];
    children?: any[];
    expendList?: any[];
  };
  id?: number;
}

interface FieldItem {
  name: string;
  type: number;
  label?: string;
  isClick?: boolean;
  isChild?: boolean;
  remark?: string;
  marker?: string;
  groupScope?: number;
  level?: number;
  fieldType?: string;
  kind?: any;
  children?: FieldItem[];
  special?: string;
  isPic?: boolean;
  id?: number;
  SFZSBOptions?: any[];
}

const handleFiledModelGroupFunction = (
  index: number,
  childrenList: FieldItem[],
  fieldList: FieldItem[],
  list: FieldModel[],
  level: number,
  groupScope: number,
  type: string | undefined,
  currentNodeItem: any
) => {
  let marker = '面';
  if (groupScope == 1 && type == 'graphicalPoint') {
    marker = '点';
  } else if (groupScope == 1 && type == 'graphicalLine') {
    marker = '线';
  }

  if (list.length != 0) {
    list.forEach((e) => {
      const isPic = e.fieldType == 'Pic';

      // 身份证识别
      if (e.valueMethod == 'idCardScan') {
        const sfzItem: FieldItem = {
          name: e.fieldName,
          type: 56,
          label: e.fieldCn,
          isClick: false,
          isChild: true,
          remark: '属性组字段',
          marker,
          groupScope,
          level,
          fieldType: e.fieldType,
          kind: monaco.languages.CompletionItemKind.Value,
          children: []
        };

        let SFZSBOptions = [
          { label: 0, text: '姓名', enName: 'xm', cnName: '姓名', strLength: '', inputHint: '请输入', valueMethod: 'input' },
          {
            label: 1,
            text: '性别',
            enName: 'xb',
            cnName: '性别',
            strLength: '',
            inputHint: '请选择',
            valueMethod: 'radio',
            isJson: true,
            options: [
              { id: new Date().getTime(), label: '男', value: 1 },
              { id: new Date().getTime() + 1, label: '女', value: 2 },
              { id: new Date().getTime() + 2, label: '不详', value: 3 },
              { id: new Date().getTime() + 3, label: '其他', value: 99 }
            ]
          },
          { label: 3, text: '出生日期', enName: 'csrq', cnName: '出生日期', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'date' },
          { label: 4, text: '住址', enName: 'zz', cnName: '住址', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
          { label: 5, text: '身份证号', enName: 'sfzh', cnName: '身份证号', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
          { label: 6, text: '签发机关', enName: 'qfjg', cnName: '签发机关', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
          {
            label: 7,
            text: '有效期限',
            enName: 'yxqx',
            cnName: '有效期限',
            strLength: '',
            maxlength: '',
            inputHint: '请输入',
            valueMethod: 'date-range'
          },
          {
            label: 8,
            text: '身份证正面',
            enName: 'sfzzm',
            cnName: '身份证正面',
            strLength: undefined,
            inputHint: '请输入',
            valueMethod: 'idCardBitmap'
          },
          {
            label: 9,
            text: '身份证反面',
            enName: 'sfzfm',
            cnName: '身份证反面',
            strLength: undefined,
            inputHint: '请输入',
            valueMethod: 'idCardBitmap'
          }
        ];

        if (e.attribution?.expendList?.length) {
          SFZSBOptions = JSON.parse(JSON.stringify(e.attribution.expendList));
        }

        SFZSBOptions.forEach((sfz) => {
          if (e.attribution?.list?.includes(sfz.label)) {
            const obj: FieldItem = {
              name: sfz.enName,
              type: 56,
              label: sfz.cnName,
              isChild: true,
              isClick: true,
              special: 'sfz',
              remark: '属性组字段',
              fieldType: 'String',
              marker,
              groupScope,
              level,
              kind: monaco.languages.CompletionItemKind.Value,
              SFZSBOptions: JSON.parse(JSON.stringify(SFZSBOptions))
            };
            sfzItem.children?.push(obj);
          }
        });

        if (currentNodeItem) {
          currentNodeItem.children[index].children.push(sfzItem);
        }
        childrenList[index].children?.push(sfzItem);
        fieldList.push(sfzItem);
      } else if (e.valueMethod === 'xtBankCard') {
        const yhkItem: FieldItem = {
          name: e.fieldName,
          type: 56,
          label: e.fieldCn,
          isClick: false,
          isChild: true,
          remark: '属性组字段',
          marker,
          groupScope,
          level,
          fieldType: e.fieldType,
          kind: monaco.languages.CompletionItemKind.Value,
          children: []
        };

        let YHKOptions = [
          {
            label: 0,
            text: '银行卡名称',
            enName: 'yhkName',
            cnName: '银行卡名称',
            strLength: '',
            maxlength: '',
            inputHint: '请输入',
            valueMethod: 'input'
          },
          {
            label: 1,
            text: '银行卡卡号',
            enName: 'yhkNum',
            cnName: '银行卡卡号',
            strLength: '',
            maxlength: '',
            inputHint: '请输入',
            valueMethod: 'input'
          },
          {
            label: 2,
            text: '银行卡类型',
            enName: 'yhkType',
            cnName: '银行卡类型',
            strLength: '',
            maxlength: '',
            inputHint: '请输入',
            valueMethod: 'input'
          },
          {
            label: 3,
            text: '银行卡照片',
            enName: 'yhkImg',
            cnName: '银行卡照片',
            strLength: undefined,
            maxlength: undefined,
            inputHint: '请输入',
            valueMethod: 'BankCardBitmap'
          }
        ];

        if (e.attribution?.expendList?.length) {
          YHKOptions = JSON.parse(JSON.stringify(e.attribution.expendList));
        }

        YHKOptions.forEach((sfz) => {
          if (e.attribution?.list?.includes(sfz.label)) {
            const obj: FieldItem = {
              name: sfz.enName,
              type: 56,
              label: sfz.cnName,
              isChild: true,
              isClick: true,
              special: 'yhk',
              remark: '属性组字段',
              fieldType: 'String',
              marker,
              groupScope,
              level,
              kind: monaco.languages.CompletionItemKind.Value,
              YHKOptions: JSON.parse(JSON.stringify(YHKOptions))
            };
            yhkItem.children?.push(obj);
          }
        });
        if (currentNodeItem) {
          currentNodeItem.children[index].children.push(yhkItem);
        }
        childrenList[index].children?.push(yhkItem);
        fieldList.push(yhkItem);
      }
      // 动植物识别
      else if (e.valueMethod == 'xtdwsb') {
        const dwItem: FieldItem = {
          name: e.fieldName,
          type: 56,
          label: e.fieldCn,
          isClick: false,
          isChild: true,
          remark: '属性组字段',
          fieldType: e.fieldType,
          kind: monaco.languages.CompletionItemKind.Value,
          marker,
          groupScope,
          level,
          children: []
        };

        const DZWSBOptions = [
          { label: 0, text: '名称', enName: 'dwmc' },
          { label: 1, text: '图片', enName: 'dwtp' },
          { label: 2, text: '类型', enName: 'dwlx' },
          { label: 3, text: '描述', enName: 'dwms' }
        ];

        DZWSBOptions.forEach((dzw) => {
          if (e.attribution?.list?.includes(dzw.label)) {
            const obj: FieldItem = {
              name: dzw.enName,
              type: 56,
              label: dzw.text,
              isChild: true,
              isClick: true,
              special: 'dw',
              remark: '属性组字段',
              marker,
              groupScope,
              level,
              kind: monaco.languages.CompletionItemKind.Value
            };
            dwItem.children?.push(obj);
          }
        });

        if (currentNodeItem) {
          currentNodeItem.children[index].children.push(dwItem);
        }
        childrenList[index].children?.push(dwItem);
        fieldList.push(dwItem);
      }
      // 表格处理
      else if (e.valueMethod == 'xttable') {
        const tableItem: FieldItem = {
          name: e.fieldName,
          type: 56,
          label: e.fieldCn,
          isPic,
          isChild: true,
          isClick: false,
          marker,
          groupScope,
          level,
          remark: '属性组字段',
          fieldType: e.fieldType,
          kind: monaco.languages.CompletionItemKind.Value,
          children: []
        };

        if (e.attribution?.children?.length) {
          e.attribution.children.forEach((child) => {
            const isChildPic = child.fieldType == 'Pic';
            const obj: FieldItem = {
              name: child.fieldName,
              type: 56,
              isPic: isChildPic,
              label: child.fieldCn,
              isChild: true,
              isClick: true,
              marker,
              groupScope,
              level,
              special: 'table',
              remark: '属性组字段',
              fieldType: child.fieldType,
              kind: monaco.languages.CompletionItemKind.Value
            };
            tableItem.children?.push(obj);
          });

          if (currentNodeItem) {
            currentNodeItem.children[index].children.push(tableItem);
          }
          childrenList[index].children?.push(tableItem);
          fieldList.push(tableItem);
        }
      }
      // 普通字段
      else {
        const fieldItem: FieldItem = {
          name: e.fieldName,
          label: e.fieldCn,
          isPic,
          type: 56,
          marker,
          groupScope,
          level,
          isClick: true,
          id: e.id,
          children: [],
          fieldType: e.fieldType,
          kind: monaco.languages.CompletionItemKind.Property
        };

        if (currentNodeItem) {
          currentNodeItem.children[index].children.push(fieldItem);
        }
        childrenList[index].children?.push(fieldItem);
        fieldList.push({
          ...fieldItem,
          remark: '属性组字段',
          kind: monaco.languages.CompletionItemKind.Value
        });
      }
    });
  }
  return childrenList;
};

const focusEditor = () => {
  if (editor) {
    editor.focus();
  }
};

const checkEditorState = () => {
  if (editor) {
    const isReadOnly = editor.getOption(monaco.editor.EditorOption.readOnly);
    if (isReadOnly) {
      editor.updateOptions({ readOnly: false });
    }
  }
};
</script>

<style lang="scss" scoped>
.formula-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }

  .monaco-editor {
    height: v-bind(scrollerEditorHeight);
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .formula-tree {
    margin-left: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    &-header {
      padding: 10px;
      border-bottom: 1px solid #dcdfe6;
    }

    &-content {
      padding: 10px;
      overflow-y: auto;
    }
  }

  .formula-footer {
    display: flex;
    margin-top: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    &-left {
      flex: 1;
      padding: 10px;
      border-right: 1px solid #dcdfe6;
    }

    &-right {
      width: 300px;
      padding: 10px;
    }

    &-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    &-content {
      font-size: 14px;
    }

    &-params {
      margin-bottom: 10px;
    }

    &-param-item {
      margin-bottom: 5px;
    }

    &-example {
      color: #666;
    }

    &-result {
      margin-top: 10px;
      color: #666;
    }
  }
}

.highlight-tree {
  color: #409eff;
  height: 32px;
  line-height: 32px;
}
/* 禁用节点样式 */
.disabled-node {
  color: #c0c4cc !important; /* 与 el-button 禁用状态相同的文字颜色 */
  cursor: not-allowed !important; /* 禁用鼠标指针 */
  width: 100%;
  height: 32px;
  line-height: 32px;
  text-decoration: line-through;
}

/* 禁用节点不可点击 */
.disabled-node .el-tree-node__content {
  pointer-events: none !important;
}

/* hover 时显示禁用标识 */
.disabled-node:hover::after {
  // content: '⛔';
  content: '';
  display: inline-block;
  margin-left: 8px;
  color: #f56c6c; /* 与 el-button 禁用状态相同的红色 */
  // font-size: 14px;
}
:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
  padding: 4px 0;
}
</style>
