<!-- 我的组织 -->
<template>
  <div>
    <select-tab :tab-list="tabList" @clickValue="handleShowTab"></select-tab>
    <div v-if="isShowOrg" style="margin-top: 20px">
      <organization-list></organization-list>
    </div>
    <div v-if="!isShowOrg" style="margin-top: 20px">
      <organization-invited></organization-invited>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import selectTab from '@/components/selectTab/selectTab.vue';
import organizationList from './organizationList.vue';
import organizationInvited from './organizationInvited.vue';

// 定义标签列表接口
interface TabItem {
  label: string;
  value: number;
  isClick: boolean;
}

// 标签列表数据
const tabList = ref<TabItem[]>([
  { label: '组织列表', value: 1, isClick: true },
  { label: '受邀列表', value: 2, isClick: false }
]);

// 是否显示组织列表组件
const isShowOrg = ref<boolean>(true);

/**
 * 处理标签点击事件
 * @param value 标签值 1:显示组织列表 2:显示受邀列表
 */
const handleShowTab = (value: number): void => {
  // value  1  显示组织列表  2  显示受邀列表
  if (value === 1) {
    isShowOrg.value = true;
  }
  if (value === 2) {
    isShowOrg.value = false;
  }
};
</script>

<style lang="scss" scoped></style>
