const dataText = JSON.stringify([
  { 'name': '毕节市', 'value': 105 },
  { 'name': '遵义市', 'value': 41 },
  { 'name': '铜仁市', 'value': 30 },
  { 'name': '贵阳市', 'value': 52 },
  { 'name': '黔东南苗族侗族自治州', 'value': 66 },
  { 'name': '黔南布依族苗族自治州', 'value': 12 },
  { 'name': '安顺市', 'value': 99 },
  { 'name': '六盘水市', 'value': 72 },
  { 'name': '黔西南布依族苗族自治州', 'value': 82 }
]);
export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: dataText
  },
  attribute: {
    map: 'areaGuizhou',
    titleText: '地图', //标题
    mapHeight: 4, //地图高度
    distance: 10, //地图缩放 缩放大小，数值越大，地图越小
    alpha: 50, //上下倾斜角度
    beta: -30, //左右倾斜度
    isShowLabel: true, //是否显示名字
    labelColor: '#fff', //字体颜色
    fontSize: 14, //字体大小
    mapBackgroundColor: '#4389ED', //地图背景颜色
    borderWidth: 2, //分界线宽度
    borderColor: '#61CFF8', //分界线颜色
    isShowLed: true, //是否高亮
    ledColor: '#fff', //高亮文字颜色
    mapLedColor: '#007EE8', //地图高亮颜色
    mapLedBorderWidth: 50, //地图高亮分界线宽度
    mapLedBorderColor: '#6BECF5' //地图高亮分界线颜色
  }
};
