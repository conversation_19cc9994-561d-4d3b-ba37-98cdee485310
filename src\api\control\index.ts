// 控制台
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CompanyData, CompanyQuery, ModelData } from '@/api/control/types';

/**
 * 获取公司列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getCompanyList(params: CompanyQuery): AxiosPromise<any> {
  return request({
    url: '/system/company/list',
    method: 'get',
    params: params
  });
}

/**
 * 修改公司信息
 * @param params 公司数据
 * @returns {AxiosPromise}
 */
export function editCompany(params: CompanyData): AxiosPromise<any> {
  return request({
    url: '/system/company/updateMyCompany',
    method: 'post',
    data: params
  });
}

/**
 * 修改我的公司信息
 * @param params 公司数据
 * @returns {AxiosPromise}
 */
export function editMyCompany(params: CompanyData): AxiosPromise<any> {
  return request({
    url: '/system/company/updateMyCompany',
    method: 'post',
    data: params
  });
}

/**
 * 主动生成默认模板
 * @param params 公司ID
 * @returns {AxiosPromise}
 */
export function initAddModule(params: string | number): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/controlAddModule?CompanyId=' + params,
    method: 'post'
  });
}

/**
 * 获取公司信息
 * @returns {AxiosPromise}
 */
export function getCompany(): AxiosPromise<any> {
  return request({
    url: '/system/company/selectMyCompany',
    method: 'post'
  });
}

/**
 * 添加模型
 * @param params 查询参数
 * @param data 模型数据
 * @returns {AxiosPromise}
 */
export function addModel(params: CompanyQuery, data: ModelData): AxiosPromise<any> {
  return request({
    url: 'qjt/rule/addOtherDefaultModule',
    method: 'post',
    params: params,
    data: data
  });
}

/**
 * 从控制台更新公司信息
 * @param params 公司数据
 * @returns {AxiosPromise}
 */
export function updateFromControl(params: CompanyData): AxiosPromise<any> {
  return request({
    url: '/system/company/updateFromControl',
    method: 'post',
    data: params
  });
}
