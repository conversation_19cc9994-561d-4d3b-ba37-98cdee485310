<!-- 列表 -->
<template>
  <div class="list-main">
    <div class="handle-title">费用发放</div>
    <el-button type="primary" style="margin: 10px 0px" @click="addRecord"
      ><el-icon><Plus /></el-icon>添加发放记录</el-button
    >
    <el-table :data="tableData" :height="tableHeight" style="width: 100%" v-loading="loading" border>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="操作人员" prop="czr"></el-table-column>
      <el-table-column label="批次号" prop="batch"></el-table-column>
      <el-table-column label="提交日期" prop="createDate">
        <template #default="scope">{{ formatDate(scope.row.createDate) }}</template>
      </el-table-column>
      <el-table-column label="备注" prop="remark"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-link type="primary" @click="detail(scope.row)">查看详情</el-link>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, inject } from 'vue';
import { getModuleList, getPlaceList } from '@/api/modal';
import { Plus } from '@element-plus/icons-vue';

interface TableItem {
  handleUser: string;
  createDate: string | number;
  batch: string;
  rq: string | number;
  xmmc: string;
  xqmc: string;
  FJ: any[];
  czr: string;
  remark?: string;
}

interface ApiResponse {
  code: number;
  data: {
    list: Array<{
      id?: number;
      parcelName: string;
      fieldInstanceModels: Array<{
        groupName: string;
        createTime: string | number;
        attribution: {
          batch: string;
          rq: string | number;
          xmmc: string;
          xqmc: string;
          FJ: any[];
          czr: string;
        };
      }>;
    }>;
  };
  msg?: string;
}

const tableHeight = computed(() => window.innerHeight - 300);
const tableData = ref<TableItem[]>([]);
const loading = ref(false);

// 注入父组件提供的方法
const changeType = inject<(type: number, obj?: any) => void>('changeType');

const getData = () => {
  getModuleList([1] as unknown as string[])
    .then(async (res: ApiResponse) => {
      if (res.code === 200 && res.data.list && res.data.list.length > 0) {
        if (res.data.list[0].id !== undefined) {
          getProjectNode(res.data.list[0].id);
        }
      }
    })
    .catch((error) => {
      console.error('获取模块列表失败:', error);
    });
};

// 获取项目资料信息
const getProjectNode = (id: number) => {
  const params = {
    groupNames: ['批次信息'],
    areaCode: '',
    ifCheck: false,
    pageNum: 1,
    pageSize: 100000,
    ruleName: '项目文件'
  };

  loading.value = true;
  getPlaceList(params)
    .then((res: ApiResponse) => {
      loading.value = false;
      if (res.code === 200 && res.data.list) {
        for (let i = 0; i < res.data.list.length; i++) {
          if (res.data.list[i].parcelName === '项目资料') {
            tableData.value = [];
            if (res.data.list[i].fieldInstanceModels) {
              res.data.list[i].fieldInstanceModels.forEach((v) => {
                if (v.groupName === '批次信息' && v.attribution) {
                  const ite: TableItem = {
                    handleUser: '',
                    createDate: v.createTime || '',
                    batch: v.attribution.batch || '',
                    rq: v.attribution.rq || '',
                    xmmc: v.attribution.xmmc || '',
                    xqmc: v.attribution.xqmc || '',
                    FJ: v.attribution.FJ || [],
                    czr: v.attribution.czr || ''
                  };
                  tableData.value.push(ite);
                }
              });
            }
            break;
          }
        }
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('获取项目资料信息失败:', error);
    });
};

const detail = (item: TableItem) => {
  if (changeType) {
    changeType(3, item);
  } else {
    console.error('无法访问父组件方法 changeType');
  }
};

// 新增发放记录
const addRecord = () => {
  if (changeType) {
    changeType(2);
  } else {
    console.error('无法访问父组件方法 changeType');
  }
};

const formatDate = (date: string | number) => {
  if (!date) return '';
  // 简单的日期格式化函数实现
  const dateObj = new Date(date);
  return dateObj.toLocaleString();
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.list-main {
  width: 100%;
  height: 100%;
  .handle-title {
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: rgba(0, 0, 0, 0.1) solid 1px;
  }
}
</style>
