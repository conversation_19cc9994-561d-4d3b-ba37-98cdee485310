<template>
  <div>
    <!--    组件内部没有设置deep监听props，数据变更时，请生成新的props，不然组件将无法刷新状态-->
    <dv-scroll-board :key="refreshFlagKey" :config="config" :style="{ width: width + 'px', height: height + 'px' }" />
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import Axios from 'axios';
import { getToken } from '@/utils/auth';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-dataV-scrollTable'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const config = ref({});
const uuid = ref(null);
const refreshFlagKey = ref(null);
const base = import.meta.env.VITE_APP_BASE_API;
const taskId = ref('');

// --- watch ---
watch(
  () => props.option.attribute,
  () => {
    loadData();
  },
  { deep: true } //深度监听
);

// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = () => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };

    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        const temp = JSON.parse(JSON.stringify(props.option.attribute));
        const columns = JSON.parse(temp.columns);
        temp.header = [];
        temp.columnWidth = [80]; //列宽
        temp.data = [];
        temp.align = ['center']; //对齐方式
        columns.forEach((item) => {
          temp.header.push(item.title);
          if (item.width) {
            temp.columnWidth.push(item.width);
          }
          temp.align.push('center');
        });
        if (res.data && Array.isArray(res.data.listList)) {
          res.data.listList.forEach((item) => {
            const row = [];
            columns.forEach((column, cdx) => {
              row.push(item[cdx]);
            });
            temp.data.push(row);
          });
        }
        config.value = temp;
        refreshFlagKey.value = uuidv1();
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    const temp = JSON.parse(JSON.stringify(props.option.attribute));
    const columns = JSON.parse(temp.columns);
    temp.header = [];
    temp.columnWidth = [80]; //列宽
    temp.data = [];
    temp.align = ['center']; //对齐方式
    columns.forEach((item) => {
      temp.header.push(item.title);
      if (item.width) {
        temp.columnWidth.push(item.width);
      }
      temp.align.push('center');
    });
    const list = JSON.parse(props.option.cptDataForm.dataText);
    list.forEach((item) => {
      const row = [];
      columns.forEach((column, cdx) => {
        row.push(item[cdx]);
      });
      temp.data.push(row);
    });
    config.value = temp;
  } else if (props.option.cptDataForm.dataSource == 3) {
    //接口类型
    Axios({
      method: 'post',
      url: `${base}/${props.option.cptDataForm.apiUrl}`,
      headers: { 'Authorization': 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' }
    }).then((res) => {
      const temp = JSON.parse(JSON.stringify(props.option.attribute));
      const columns = JSON.parse(temp.columns);
      temp.header = [];
      temp.columnWidth = [80]; //列宽
      temp.data = [];
      temp.align = ['center']; //对齐方式
      columns.forEach((item) => {
        temp.header.push(item.title);
        if (item.width) {
          temp.columnWidth.push(item.width);
        }
        temp.align.push('center');
      });
      res.data.data.listList.forEach((item) => {
        const row = [];
        columns.forEach((column, cdx) => {
          row.push(item[cdx]);
        });
        temp.data.push(row);
      });
      config.value = temp;
      refreshFlagKey.value = uuidv1();
    });
  }
};
// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshFlagKey.value = uuidv1();
  refreshCptData();
});
</script>

<style scoped></style>
