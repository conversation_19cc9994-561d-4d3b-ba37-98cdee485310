<!-- 评论内容 -->
<template>
  <!-- @click="handleDetial" -->
  <div class="forum-content-contianer" @click="$router.push('/forum/list')">
    <!-- 发表人信息 -->
    <el-row>
      <el-col :span="24" :xs="24">
        <div class="people-info">
          <auth-img
            class="img"
            :authSrc="`${baseUrl.value}${props.forumItem.avatar}?att=1`"
            :width="'32px'"
            :height="'32px'"
            :radios="'50%'"
          ></auth-img>
          <div class="title-info">
            <div class="name">{{ props.forumItem.createBy }}</div>
            <div class="name">
              <span>{{ formatDateYmdhm(props.forumItem.createTime) }}</span>
              <span class="sys-type" v-if="props.isShowTitleType">
                <span v-if="props.forumItem.titleType == 1" style="color: var(--current-color)">系统建议</span>
                <span v-if="props.forumItem.titleType == 2" style="color: red">BUG反馈</span>
                <span v-if="props.forumItem.titleType == 3" style="color: #09b66d">交流互动</span>
              </span>
            </div>
          </div>
          <div class="more-info">
            <el-dropdown size="mini" trigger="click">
              <i class="el-icon-more" style="transform: rotate(90deg)"></i>
              <el-dropdown-menu>
                <el-dropdown-item style="width: 100px">投诉</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 发表内容 -->
    <el-row>
      <el-col :span="24" :xs="24">
        <div class="forum-main">
          <div class="forum-title">
            {{ props.forumItem.titleInfo }}
          </div>
          <div class="forum-content">
            {{ props.forumItem.contents }}
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 发表图片 -->
    <el-row>
      <el-col :span="24" :xs="24">
        <div class="content-img">
          <div v-for="(item, index) in props.forumItem.picUrls" :key="index">
            <auth-img :authSrc="`${baseUrl.value}${item}?att=1`" :width="'87px'" :height="'87px'" :radios="'6px'"></auth-img>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 最后一行 -->
    <el-row>
      <el-col :span="24" :xs="24">
        <div class="all-main">
          <div><i class="el-icon-view"></i>145</div>
          <div @click="handleDetial"><i class="el-icon-chat-dot-square"></i>60</div>
          <div class="dz-main">
            <el-image :src="dzIcon"></el-image>
            <div>500</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';
import authImg from '../authImg/index.vue';
import dzIcon from '../../assets/images/good.png';

const props = defineProps({
  forumItem: {
    type: Object as () => {
      avatar: string;
      createBy: string;
      createTime: string;
      titleType: number;
      titleInfo: string;
      contents: string;
      picUrls: string[];
    },
    default: () => ({})
  },
  isShowTitleType: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['detialInfo']);

const baseUrl = ref(`${process.env.VUE_APP_BASE_API}/system/user/profile/downloadone/`);

const handleDetial = () => {
  emit('detialInfo', props.forumItem);
};

const formatDateYmdhm = (dateString: string) => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};
</script>

<style lang="scss" scoped>
.forum-content-contianer {
  display: flex;
  justify-content: flex-start;
  align-content: center;
  flex-direction: column;
  margin-top: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #dbe7ee;
  .people-info {
    display: inline-flex;
    flex: 1;
    align-content: center;
    height: 40px;
    width: 100%;
    .title-info {
      height: 40px;
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      .name {
        width: 100%;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #161d26;
        line-height: 20px;
        .sys-type {
          width: 60px;
          height: 17px;
          font-size: 12px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          line-height: 17px;
          padding-left: 12px;
        }
      }
    }
    .more-info {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
  }
  .forum-main {
    flex: 1;
    margin: 8px;
    .forum-title {
      width: 100%;
      height: 20px;
      font-size: 16px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #161d26;
      line-height: 20px;
    }
    .forum-content {
      width: 100%;
      height: auto;
      font-size: 14px;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #161d26;
      line-height: 20px;
    }
  }
  .content-img {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    flex-wrap: wrap;
  }
  .all-main {
    display: flex;
    justify-content: space-around;
    flex-wrap: nowrap;
    height: 40px;
    .dz-main {
      display: flex;
      height: 17px;
      justify-content: center;
      align-content: center;
      .el-image {
        overflow: visible;
        :deep(.el-image__inner) {
          vertical-align: baseline;
        }
      }
    }
  }
}
</style>
