<template>
  <div class="main" :style="{ 'min-width': width + 'px', height: height + 'px' }">
    <div class="box">
      <canvas
        ref="signCanvas"
        :width="width"
        :height="height"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      ></canvas>
    </div>
    <!-- <div class="footer-btn">
      <el-button type="danger" size="small" @click="handleReset">重写</el-button>
      <el-button type="primary" size="small" @click="handleGenerate">生成签名</el-button>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { uploadImg } from '@/api/esign';

// 定义props接口
interface Props {
  width?: number;
  height?: number;
  lineWidth?: number;
  lineColor?: string;
  bgColor?: string;
}

// 定义props默认值
const props = withDefaults(defineProps<Props>(), {
  width: 300,
  height: 150,
  lineWidth: 6,
  lineColor: '#000000',
  bgColor: '#ffffff'
});

// Canvas和绘图上下文
const signCanvas = ref<HTMLCanvasElement | null>(null);
let ctx: CanvasRenderingContext2D | null = null;

// 绘图状态
const isDrawing = ref(false);
const lastX = ref(0);
const lastY = ref(0);
const blobData = ref<Blob | null>(null);
const isEmpty = ref(true);

// 初始化画布
onMounted(() => {
  if (signCanvas.value) {
    ctx = signCanvas.value.getContext('2d');
    if (ctx) {
      ctx.lineWidth = props.lineWidth;
      ctx.strokeStyle = props.lineColor;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      // 初始化画布背景
      clearCanvas();
    }
  }
});

// 鼠标事件处理
const handleMouseDown = (e: MouseEvent) => {
  if (!ctx || !signCanvas.value) return;
  isDrawing.value = true;
  isEmpty.value = false;

  const rect = signCanvas.value.getBoundingClientRect();
  // 计算缩放比例
  const scaleX = signCanvas.value.width / rect.width;
  const scaleY = signCanvas.value.height / rect.height;
  lastX.value = (e.clientX - rect.left) * scaleX;
  lastY.value = (e.clientY - rect.top) * scaleY;
};

const handleMouseMove = (e: MouseEvent) => {
  if (!isDrawing.value || !ctx || !signCanvas.value) return;
  const rect = signCanvas.value.getBoundingClientRect();
  // 计算缩放比例
  const scaleX = signCanvas.value.width / rect.width;
  const scaleY = signCanvas.value.height / rect.height;
  const x = (e.clientX - rect.left) * scaleX;
  const y = (e.clientY - rect.top) * scaleY;

  draw(x, y);
};

const handleMouseUp = () => {
  isDrawing.value = false;
};

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  if (!ctx || !signCanvas.value) return;
  e.preventDefault();

  if (e.touches.length === 1) {
    isDrawing.value = true;
    isEmpty.value = false;

    const rect = signCanvas.value.getBoundingClientRect();
    // 计算缩放比例
    const scaleX = signCanvas.value.width / rect.width;
    const scaleY = signCanvas.value.height / rect.height;
    lastX.value = (e.touches[0].clientX - rect.left) * scaleX;
    lastY.value = (e.touches[0].clientY - rect.top) * scaleY;
  }
};

const handleTouchMove = (e: TouchEvent) => {
  if (!isDrawing.value || !ctx || !signCanvas.value) return;
  e.preventDefault();

  if (e.touches.length === 1) {
    const rect = signCanvas.value.getBoundingClientRect();
    // 计算缩放比例
    const scaleX = signCanvas.value.width / rect.width;
    const scaleY = signCanvas.value.height / rect.height;
    const x = (e.touches[0].clientX - rect.left) * scaleX;
    const y = (e.touches[0].clientY - rect.top) * scaleY;

    draw(x, y);
  }
};

const handleTouchEnd = (e: TouchEvent) => {
  e.preventDefault();
  isDrawing.value = false;
};

// 绘图函数
const draw = (x: number, y: number) => {
  if (!ctx) return;

  ctx.beginPath();
  ctx.moveTo(lastX.value, lastY.value);
  ctx.lineTo(x, y);
  ctx.stroke();

  lastX.value = x;
  lastY.value = y;
};

// 清除画布
const clearCanvas = () => {
  if (!ctx || !signCanvas.value) return;

  ctx.fillStyle = props.bgColor;
  ctx.fillRect(0, 0, signCanvas.value.width, signCanvas.value.height);
  isEmpty.value = true;
};

// 重置签名
const handleReset = () => {
  clearCanvas();
};

// 生成签名图片
const handleGenerate = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!signCanvas.value) {
      ElMessage.error('签名组件未正确加载');
      reject('签名组件未正确加载');
      return;
    }

    if (isEmpty.value) {
      ElMessage.error('请先完成签名');
      reject('请先完成签名');
      return;
    }

    try {
      // 生成Base64图片数据
      const dataURL = signCanvas.value.toDataURL('image/jpeg', 0.8);

      // 转换为Blob
      base64ToBlob(dataURL)
        .then((blob) => {
          blobData.value = blob;
          // 上传图片
          return uploadImage();
        })
        .then((path) => {
          resolve(path);
        })
        .catch((err) => {
          reject(err);
        });
    } catch (error) {
      console.error('生成签名失败:', error);
      reject('生成签名失败');
    }
  });
};

// Base64转Blob
const base64ToBlob = async (base64Data: string): Promise<Blob> => {
  const arr = base64Data.split(',');
  const fileType = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let l = bstr.length;
  const u8Arr = new Uint8Array(l);

  while (l--) {
    u8Arr[l] = bstr.charCodeAt(l);
  }

  return new Blob([u8Arr], {
    type: fileType
  });
};

// 上传签名图片
const uploadImage = async (): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!blobData.value) {
      reject('没有可上传的图片数据');
      return;
    }

    const formData = new FormData();
    const fileOfBlob = new File([blobData.value], 'signature.jpeg');
    formData.append('files', fileOfBlob);

    uploadImg(formData)
      .then((response) => {
        if (response.code === 200) {
          resolve(response.data[0].path);
        } else {
          ElMessage.error(response.msg);
          reject(response.msg);
        }
      })
      .catch((error) => {
        ElMessage.error('上传失败');
        reject(error);
      });
  });
};

// 清理事件
onUnmounted(() => {
  // 如有需要的清理代码
});

// 暴露方法给父组件
defineExpose({
  handleReset,
  handleGenerate
});
</script>

<style scoped>
.main {
  display: flex;
  flex-direction: column;
}

.box {
  border: 1px solid rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
}

canvas {
  width: 100%;
  height: 100%;
  touch-action: none;
}

.footer-btn {
  margin: 5px 0px 10px 0px;
}
</style>
