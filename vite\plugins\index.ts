import vue from '@vitejs/plugin-vue';
// import vueDevTools from 'vite-plugin-vue-devtools';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';

import createUnoCss from './unocss';
import createAutoImport from './auto-import';
import createComponents from './components';
import createIcons from './icons';
import createSvgIconsPlugin from './svg-icon';
import createCompression from './compression';
import createSetupExtend from './setup-extend';
import createVisualizer from './visualizer';

export default (viteEnv: any, isBuild = false): [] => {
  const vitePlugins: any = [];
  vitePlugins.push(vue());
  // vitePlugins.push(vueDevTools());
  vitePlugins.push(createUnoCss());
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createComponents());
  vitePlugins.push(createCompression(viteEnv));
  vitePlugins.push(createIcons());
  vitePlugins.push(createSvgIconsPlugin());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(viteCommonjs());
  isBuild && vitePlugins.push(createVisualizer());
  return vitePlugins;
};
