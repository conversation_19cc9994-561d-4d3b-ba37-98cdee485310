<template>
  <el-image
    :class="{ 'active-img': active }"
    ref="img"
    fit="scale-down"
    :src="img"
    :style="{ width: width, 'max-width': widthMax, height: height, borderRadius: radios }"
    :preview-src-list="bigImg"
  >
    <template #error>
      <div class="image-slot">
        <img src="@/assets/images/img-error.png" alt="" :style="{ width: width, height: height }" />
      </div>
    </template>
  </el-image>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { getToken } from '@/utils/auth';
import { isExternal } from '@/utils/validate';
import axios from 'axios';

// 定义props
interface Props {
  authSrc?: string;
  width?: string;
  widthMax?: string;
  height?: string;
  radios?: string;
}

const props = withDefaults(defineProps<Props>(), {
  authSrc: '',
  width: '300px',
  widthMax: '500px',
  height: '200px',
  radios: '0'
});

// 响应式状态
const img = ref('');
const active = ref(false);
const bigImg = ref<string[]>([]);

// 计算属性
const realSrcList = computed(() => {
  if (!props.authSrc) {
    return [];
  }

  const real_src_list = props.authSrc.split(',');
  const srcList: string[] = [];

  real_src_list.forEach((item) => {
    if (isExternal(item)) {
      srcList.push(item);
    } else {
      srcList.push(import.meta.env.VITE_APP_BASE_API + item);
    }
  });

  return srcList;
});

// 获取图片方法
const getImg = async () => {
  if (!props.authSrc) return;

  try {
    const res = await axios({
      method: 'get',
      url: props.authSrc,
      headers: {
        'Authorization': getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    });

    const blob = res.data;
    const reader = new FileReader();
    reader.readAsDataURL(blob); // 转换为base64

    reader.onload = () => {
      if (reader.result) {
        img.value = reader.result as string;
        bigImg.value = [reader.result as string];
      }
    };
  } catch (error) {
    console.error('获取图片失败:', error);
  }
};

// 监听authSrc变化
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  },
  { immediate: true }
);

// 组件挂载后获取图片
onMounted(() => {
  getImg();
});
</script>

<style scoped>
.img-item {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 300px;
  height: 200px;
}
.samll-img {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 50px;
  height: 40px;
}
.el-image {
  margin: 0px;
}
:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd dashed 1px;
  background: rgba(0, 0, 0, 0.1);
}
.active-img {
  border: var(--current-color) solid 1px;
}
</style>
