/** *Copyright: Copyright (c) 2020 *Author:<PERSON><PERSON><PERSON><PERSON> *Version 1.0 *Title: form-generator/Element UI表单设计及代码生成器 *GitHub:
https://github.com/JakHuang/form-generator */
<template>
  <div class="right-board">
    <div class="field-box">
      <!-- v-if="activeData.formId == item.formId" :ref="`activeDataRef${item.formId}`" -->
      <!-- 组件属性 -->
      <el-form v-show="showField" :model="activeData" ref="activeDataRef" label-position="top">
        <div class="components-title">
          <div class="title-color"></div>
          字段属性
        </div>
        <el-form-item
          v-if="activeData.vModel !== undefined"
          label="字段名称"
          prop="vModel"
          :rules="[
            {
              required: true,
              message: '请输入字段名称',
              trigger: ['blur', 'change']
            },
            {
              min: 1,
              max: 10,
              message: '长度在 1 到 10 个字符',
              trigger: ['blur', 'change']
            },
            {
              message: '请输入2-10位数字或大小写字母组成的字段名称',
              pattern: /^[a-zA-Z0-9_]{1,10}$/,
              trigger: ['blur', 'change']
            }
          ]"
        >
          <el-input
            v-model="activeData.vModel"
            oninput="value = value.replace(/(?:^|\W)(date)(?=\W|$)|[_]|\p{Script=Han}/igu, '')"
            placeholder="请输入字段名称"
            maxlength="10"
            @blur="handleBlurUpdateField"
            @input="handelUpdatevModel"
          />
        </el-form-item>
        <el-form-item
          v-if="activeData.label !== undefined"
          label="字段别名"
          prop="label"
          :rules="[
            { required: true, message: '请输入字段别名', trigger: 'blur' },
            {
              min: 1,
              max: 200,
              message: '长度在 1 到 200 个字符',
              trigger: 'blur'
            }
          ]"
        >
          <!-- @input="handleInputField"   -->
          <el-input
            v-model="activeData.label"
            placeholder="请输入字段字段别名"
            @blur="handleBlurUpdateField"
            @input="handleLabelInput(activeData)"
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="字段状态" prop="status">
          <el-switch :model-value="activeData.status" @update:model-value="handleFieldStatus" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="日期类型" v-if="['el-date-picker'].indexOf(activeData.tag) > -1 && activeData.tagIcon == 'date'">
          <el-select v-model="activeData.type" placeholder="请选择日期类型" style="width: 100%">
            <el-option label="年" value="year"></el-option>
            <el-option label="年-月" value="month"></el-option>
            <el-option label="年-月-日" value="date"></el-option>
            <!-- <el-option label="年-月-日 时:分:秒" value="datetime"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="日期类型" v-if="activeData.tagIcon == 'date-range'">
          <el-select v-model="activeData.type" placeholder="请选择日期类型" style="width: 100%">
            <el-option label="年-月-日" value="daterange"></el-option>
            <!-- <el-option label="年-月-日 时:分:秒" value="datetimerange"></el-option> -->
          </el-select>
        </el-form-item>
        <!-- 行政区域的自定义格式 -->
        <!-- <el-form-item label="格式"  v-if="activeData.tagIcon == 'area' && activeData.rowType == 'XZQYCom'">
          <el-select v-model="activeData.xzqyLevel" placeholder="请选择行政区域的格式内容" @change="handleXZQYLevel" style="width: 100%;">
            <el-option label="省市" :value="2"></el-option>
            <el-option label="省市区" :value="3"></el-option>
            <el-option label="省市区街道" :value="4"></el-option>
            <el-option label="省市区街道社区" :value="5"></el-option>
            <el-option label="省市区街道社区-详细信息" :value="6"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="提示语"
          prop="placeholder"
          v-if="
            activeData.label !== undefined &&
            activeData.placeholder != undefined &&
            !isSFZSBComType &&
            !isUploadComType &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isTableComType &&
            !isRowComType &&
            !isXTFJComType &&
            !isAudioComType &&
            !isVideoComType &&
            !isLxrComType &&
            !isSjctComType
          "
          :rules="[
            {
              required: false,
              message: '请输入提示语',
              trigger: ['blur', 'change']
            },
            {
              min: 2,
              max: 203,
              message: '长度在 2 到 200 个字符',
              trigger: ['blur', 'change']
            }
          ]"
        >
          <!-- @blur="handleBlurPlaceholder" -->
          <el-input v-model="activeData.placeholder" placeholder="请输入提示语" @input="handleInputPlaceholder" maxlength="203" />
        </el-form-item>
        <el-form-item v-if="activeData.tagIcon == 'idCardScan'">
          <div class="default-text">识别信息</div>
          <el-checkbox-group v-model="activeData.sfzsbList" style="width: 100%" v-if="activeData.expendList && activeData.expendList.length > 0">
            <div style="display: flex; flex-direction: column">
              <div class="sfzsb-check-main" v-for="item in activeData.expendList" :key="item.label">
                <el-checkbox :value="item.label" @change="handleBlurUpdateField">{{ item.text }} </el-checkbox>
                <div class="sfzsb-row">
                  <div class="text-label">字段名称</div>
                  <el-input placeholder="请输入字段名称" v-model="item.enName" clearable> </el-input>
                </div>
                <div class="sfzsb-row">
                  <div class="text-label">字段别名</div>
                  <el-input placeholder="请输入字段别名" v-model="item.cnName" clearable> </el-input>
                </div>
                <div class="sfzsb-row" v-if="item.strLength != undefined">
                  <div class="text-label">字段长度</div>
                  <el-input placeholder="请输入字段别名" v-model="item.strLength" clearable> </el-input>
                </div>
                <div class="sfzsb-row-option" v-if="item.options && item.options.length > 0">
                  <div class="text-label">选项值</div>
                  <div class="option-main">
                    <div class="option-item" v-for="(ite, index) in item.options" :key="ite.id">
                      <el-input
                        v-if="item.isJson"
                        placeholder="选项名"
                        v-model="ite.label"
                        @input="setJsonOptionValue(ite, $event, 'label')"
                        maxlength="64"
                        style="width: 50%; margin-right: 4px"
                      />
                      <el-input
                        placeholder="选项值"
                        v-model="ite.value"
                        :value="ite.value"
                        @input="setJsonOptionValue(ite, $event, 'value')"
                        maxlength="64"
                        style="width: 50%"
                        v-if="item.isJson"
                      />
                      <el-input
                        v-else
                        placeholder="选项值"
                        :value="ite.value"
                        @input="setOptionValue(item, $event)"
                        maxlength="64"
                        style="width: 100%"
                      />
                      <div style="color: red; margin-left: 5px" @click="item.options.splice(index, 1)">
                        <el-icon><Delete /></el-icon>
                      </div>
                    </div>
                    <div class="option-btn">
                      <el-button style="padding-bottom: 0" :icon="CirclePlus" type="text" @click="handleAddSfzItem(item)">添加选项</el-button>
                      <div style="font-size: 16px; margin-left: 10px; height: 22px; margin-top: 15px">
                        <el-dropdown @command="handleSFZSelectCommand" style="font-size: 12px">
                          <span class="el-dropdown-link" style="color: #1890ff">
                            <el-icon><Setting /></el-icon><span style="padding-left: 4px">{{ item.isJson ? '数组对象' : '字符串' }}</span
                            ><el-icon><ArrowDown /></el-icon>
                          </span>
                          <template v-slot:dropdown>
                            <el-dropdown-menu style="font-size: 12px">
                              <el-dropdown-item :command="beforeCommand(item, 'string')">字符串</el-dropdown-item>
                              <el-dropdown-item :command="beforeCommand(item, 'json')">数组对象</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <el-button style="padding-bottom: 0; margin-left: 10px" :icon="CirclePlus" type="text" @click="jumpLogic">跳题逻辑</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-checkbox-group>
          <el-checkbox-group v-model="activeData.sfzsbList" style="width: 100%" v-else>
            <el-checkbox v-for="item in SFZSBOptions" :key="item.label" :value="item.label" @change="handleBlurUpdateField"
              >{{ item.text }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- 身份证识别设置最多输入能写几个字符  --- 等到 布局完成之后的写 -->
        <!-- 布局内容---栅格布局 --app  说都可以 -->
        <!-- <div v-if="activeData.tagIcon == 'idCardScan' && isSFZSBComType">
          <el-form-item v-for="item in SFZSBOptions" :key="item.label" :label="`${item.text}最多输入`">
            <el-input v-model="activeData.maxlength" placeholder="请输入字符长度">
              <template slot="append">个字符</template>
            </el-input>
          </el-form-item>
        </div> -->
        <!-- 缴费项目 -->
        <el-form-item v-if="activeData.tagIcon == 'xtpay'">
          <div class="default-text">识别信息</div>
          <el-checkbox-group v-model="activeData.paySelectList" style="width: 100%">
            <div style="display: flex; flex-direction: column">
              <div class="sfzsb-check-main" v-for="item in activeData.payList" :key="item.label">
                <el-checkbox :value="item.label" @change="handleBlurUpdateField">{{ item.text }}</el-checkbox>
                <div class="sfzsb-row">
                  <div class="text-label">字段名称</div>
                  <el-input
                    v-model="item.enName"
                    clearable
                    oninput="value = value.replace(/(?:^|\W)(date)(?=\W|$)|[_]|\p{Script=Han}/igu, ''); if(value.length > 10) value = value.slice(0, 10);"
                    onblur="if(value.length < 1) value = '';"
                    placeholder="请输入字段名称(1-10位)"
                  >
                  </el-input>
                </div>
                <div class="sfzsb-row">
                  <div class="text-label">字段别名</div>
                  <el-input placeholder="请输入字段别名" v-model="item.cnName" clearable> </el-input>
                </div>
                <div class="sfzsb-row" v-if="item.strLength != undefined">
                  <div class="text-label">字段长度</div>
                  <el-input placeholder="请输入字段别名" v-model="item.strLength" clearable> </el-input>
                </div>
                <div class="sfzsb-row-option" v-if="item.options && item.options.length > 0">
                  <div class="text-label">选项值</div>
                  <div class="option-main">
                    <div class="option-item" v-for="(ite, index) in item.options" :key="ite.id">
                      <el-input
                        v-if="item.isJson"
                        placeholder="选项名"
                        v-model="ite.label"
                        @input="setJsonOptionValue(ite, $event, 'label')"
                        maxlength="64"
                        style="width: 50%; margin-right: 4px"
                      />
                      <el-input
                        placeholder="选项值"
                        v-model="ite.value"
                        :value="ite.value"
                        @input="setJsonOptionValue(ite, $event, 'value')"
                        maxlength="64"
                        style="width: 50%"
                        v-if="item.isJson"
                      />
                      <el-input
                        v-else
                        placeholder="选项值"
                        :value="ite.value"
                        @input="setOptionValue(item, $event, 'xtpay')"
                        maxlength="64"
                        style="width: 100%"
                      />
                      <div style="color: red; margin-left: 5px" @click="item.options.splice(index, 1)">
                        <el-icon><Delete /></el-icon>
                      </div>
                    </div>
                    <div class="option-btn">
                      <el-button style="padding-bottom: 0" :icon="CirclePlus" type="text" @click="handleAddSfzItem(item)">添加选项</el-button>
                      <div style="font-size: 16px; margin-left: 10px; height: 22px; margin-top: 15px">
                        <el-dropdown @command="handlePaySelectCommand" style="font-size: 12px">
                          <span class="el-dropdown-link" style="color: #1890ff">
                            <el-icon><Setting /></el-icon><span style="padding-left: 4px">{{ item.isJson ? '数组对象' : '字符串' }}</span
                            ><el-icon><ArrowDown /></el-icon>
                          </span>
                          <template v-slot:dropdown>
                            <el-dropdown-menu style="font-size: 12px">
                              <el-dropdown-item :command="beforeCommand(item, 'string')">字符串</el-dropdown-item>
                              <el-dropdown-item :command="beforeCommand(item, 'json')">数组对象</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <el-button style="padding-bottom: 0; margin-left: 10px" :icon="CirclePlus" type="text" @click="jumpLogic">跳题逻辑</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <!-- 银行卡识别 -->
        <el-form-item v-if="activeData.tagIcon == 'xtBankCard'">
          <div class="default-text">识别信息</div>
          <el-checkbox-group v-model="activeData.bankCardSelectList" style="width: 100%">
            <div style="display: flex; flex-direction: column">
              <div class="sfzsb-check-main" v-for="item in activeData.expendList" :key="item.label">
                <el-checkbox :value="item.label" @change="handleBlurUpdateField">{{ item.text }}</el-checkbox>
                <div class="sfzsb-row">
                  <div class="text-label">字段名称</div>
                  <el-input
                    v-model="item.enName"
                    clearable
                    oninput="value = value.replace(/(?:^|\W)(date)(?=\W|$)|[_]|\p{Script=Han}/igu, ''); if(value.length > 10) value = value.slice(0, 10);"
                    onblur="if(value.length < 1) value = '';"
                    placeholder="请输入字段名称(1-10位)"
                  >
                  </el-input>
                </div>
                <div class="sfzsb-row">
                  <div class="text-label">字段别名</div>
                  <el-input placeholder="请输入字段别名" v-model="item.cnName" clearable> </el-input>
                </div>
                <div class="sfzsb-row" v-if="item.strLength != undefined">
                  <div class="text-label">字段长度</div>
                  <el-input placeholder="请输入字段别名" v-model="item.strLength" clearable> </el-input>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <!-- 银行卡识别结束 -->
        <el-form-item v-if="activeData.tagIcon == 'xtdwsb'">
          <div class="default-text">识别信息</div>
          <el-checkbox-group v-model="activeData.sbList" style="width: 100%">
            <el-checkbox v-for="item in DWSBOptions" :key="item.label" :value="item.label" disabled>{{ item.text }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="activeData.tagIcon == 'xtzwsb'">
          <div class="default-text">识别信息</div>
          <el-checkbox-group v-model="activeData.sbList" style="width: 100%">
            <el-checkbox v-for="item in ZWSBOptions" :key="item.label" :value="item.label" disabled>{{ item.text }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- 开始提示和结束提示、分割符的设置和App端沟通 app端不设置这几个值 -->
        <!-- <el-form-item v-if="activeData['start-placeholder']!==undefined" label="开始提示">
          <el-input v-model="activeData['start-placeholder']" placeholder="请输入开始提示" />
        </el-form-item>
        <el-form-item v-if="activeData['end-placeholder']!==undefined" label="结束提示">
          <el-input v-model="activeData['end-placeholder']" placeholder="请输入结束提示" />
        </el-form-item> -->
        <el-row>
          <el-col :span="11" :offset="1">
            <el-form-item v-if="activeData.autosize !== undefined" label="最小行数">
              <el-input-number v-model="activeData.autosize.minRows" :min="1" placeholder="最小行数" />
            </el-form-item>
          </el-col>
          <el-col :span="11" :offset="1">
            <el-form-item v-if="activeData.autosize !== undefined" label="最大行数">
              <el-input-number v-model="activeData.autosize.maxRows" :min="1" placeholder="最大行数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item v-if="['input', 'textarea', 'area', 'checkbox', 'radio', 'select'].includes(activeData.tagIcon)" label="最多输入">
          <el-input
            v-model="activeData.maxlength"
            placeholder="请输入字符长度"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            max="3000"
            @input="handleMaxLengthInput"
          >
            <template v-slot:append>个字符</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="activeData['show-summary'] !== undefined" label="显示合计">
          <el-switch v-model="activeData['show-summary']" />
        </el-form-item>
        <el-form-item v-if="activeData.justify !== undefined && activeData.type === 'flex'" label="水平排列">
          <el-select v-model="activeData.justify" placeholder="请选择水平排列" :style="{ width: '100%' }">
            <el-option v-for="(item, index) in justifyOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="activeData.align !== undefined && activeData.type === 'flex'" label="垂直排列">
          <el-radio-group v-model="activeData.align">
            <el-radio-button label="top" />
            <el-radio-button label="middle" />
            <el-radio-button label="bottom" />
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            activeData.vModel !== undefined &&
            !isSFZSBComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isXZQYComType &&
            !isTableComType &&
            !isRowComType &&
            !isXtsjyComType &&
            !isXtsjdlComType &&
            !isUploadComType &&
            !isAudioComType &&
            !isVideoComType &&
            !isLxrComType &&
            !isYhkComType &&
            notObject(activeData.defaultValue)
          "
        >
          <div class="default-text">
            {{ ['el-checkbox-group', 'el-radio-group', 'el-select', 'el-cascader'].indexOf(activeData.tag) > -1 ? '选项' : '默认值' }}
          </div>
          <el-radio-group v-model="activeData.defalutSelect" style="display: flex; margin-left: 10px">
            <div v-if="!isXZQYComType" style="margin-right: 10px" @click="handleRadioValue(1)">
              <el-radio :label="1">自定义</el-radio>
            </div>
            <!-- <div style="margin-right: 10px;" @click="handleRadioValue(2)"><el-radio :label="2">数据联动</el-radio></div>  -->
            <!-- 企业账号才有公式编辑功能 -->
            <div v-if="!isXZQYComType && vipType == 3" @click="handleRadioValue(3)">
              <el-radio :label="3">公式编辑</el-radio>
            </div>
          </el-radio-group>
          <el-input v-if="activeData.expression != undefined" v-model="activeData.expression" placeholder="计算公式" :disabled="true" />
          <!-- 下拉，单选，多选 ，选择的位置 -->
          <template v-else-if="['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1 && activeData.defalutSelect == 1">
            <draggable :list="activeData.options" :animation="340" group="selectItem" handle=".option-drag">
              <template #item="{ element, index }">
                <div class="select-item">
                  <div class="select-line-icon option-drag">
                    <el-icon><Operation /></el-icon>
                  </div>
                  <!-- 这里设置el-select 下拉框的数据内容  数组对象 -->
                  <div v-if="activeData.tag == 'el-select' && activeData.optionIsJson" style="display: flex">
                    <el-input
                      placeholder="选项名"
                      v-model="element.label"
                      @input="setJsonOptionValue(element, $event, 'label')"
                      maxlength="64"
                      style="width: 50%"
                    />
                    <el-input
                      placeholder="选项值"
                      v-model="element.value"
                      :value="element.value"
                      @input="setJsonOptionValue(element, $event, 'value')"
                      maxlength="64"
                      style="width: 50%"
                    />
                  </div>
                  <el-input v-else placeholder="选项值" v-model="element.value" @input="setOptionValue(element, $event)" maxlength="64" />
                  <div
                    class="select-value"
                    v-if="['el-select', 'el-radio-group'].indexOf(activeData.tag) > -1"
                    :class="{
                      'selec-value-hover': activeData.defaultValue == element.value
                    }"
                    style="bottom: 10px"
                  >
                    <!-- :style="{display:activeData.defaultValue == item.value?'flex':'none'}" 
                  @click.native.prevent="handleSelectDefaultValue(item.value)" -->
                    <el-radio-group v-model="activeData.defaultValue" @click.prevent="handleSelectDefaultValue(element.value)">
                      <el-radio :label="element.value"></el-radio>
                    </el-radio-group>
                  </div>
                  <div
                    class="select-value"
                    v-else-if="['el-checkbox-group'].indexOf(activeData.tag) > -1"
                    :class="{
                      'selec-value-hover': activeData.defaultValue.includes(element.value)
                    }"
                  >
                    <!-- @click.native.prevent="handleCheckDefaultValue(item.value)" -->
                    <el-checkbox-group v-model="activeData.defaultValue">
                      <el-checkbox :value="element.value"></el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="close-btn select-line-icon" @click="activeData.options.splice(index, 1)">
                    <el-icon><Delete /></el-icon>
                  </div>
                </div>
              </template>
            </draggable>
            <div style="margin-left: 20px; display: flex">
              <el-button style="padding-bottom: 0" :icon="CirclePlus" type="text" @click="addSelectItem">添加选项</el-button>
              <div style="font-size: 16px; margin-left: 10px; height: 22px; margin-top: 12px">
                <el-dropdown v-if="activeData.tag == 'el-select'" @command="handleSelectCommand" style="font-size: 12px">
                  <span class="el-dropdown-link" style="color: #1890ff">
                    <el-icon><Setting /></el-icon><span style="padding-left: 4px">{{ activeData.optionIsJson ? '数组对象' : '字符串' }}</span
                    ><el-icon><ArrowDown /></el-icon>
                  </span>
                  <template v-slot:dropdown>
                    <el-dropdown-menu style="font-size: 12px">
                      <el-dropdown-item command="string">字符串</el-dropdown-item>
                      <el-dropdown-item command="json">数组对象</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <el-button style="padding-bottom: 0; margin-left: 10px" :icon="CirclePlus" type="text" @click="jumpLogic">跳题逻辑</el-button>
            </div>
          </template>
          <template v-else-if="activeData.tag == 'el-cascader'">
            <el-button style="padding-bottom: 0; margin-left: 10px; margin-top: -12px" :icon="CirclePlus" type="text" @click="addCascaderItem"
              >添加选项</el-button
            >
          </template>
          <!-- 日期 ，日期范围-->
          <div v-else-if="activeData.tagIcon == 'date' && activeData.defalutSelect == 1">
            <el-date-picker v-model="activeData.defaultValue" :type="activeData.type" style="width: 100%" value-format="timestamp"> </el-date-picker>
          </div>
          <div v-else-if="activeData.tagIcon == 'date-range' && activeData.defalutSelect == 1">
            <el-date-picker
              v-model="activeData.defaultValue"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator=" 至 "
              type="daterange"
              style="width: 100%"
            >
            </el-date-picker>
          </div>
          <!-- 行政区域的下拉菜单 -->
          <!-- <div v-else-if="['el-cascader'].indexOf(activeData.tag) > -1 && activeData.defalutSelect == 1 "> -->
          <!-- <el-cascader
              v-model="activeData.defaultValue"
              :options="activeData.areaOptions"
              style="width: 100%;"
              placeholder="请选择行政区域"
              :props="{ checkStrictly: true,value:'value',label:'label',children:'children'}"
              clearable
              filterable></el-cascader> -->
          <!-- <area-code
                @changeCityCode="handelChangeCityCode"
                :level="activeData.xzqyLevel"></area-code> -->
          <!-- </div> -->
          <!-- 时间选择的默认值 time -->
          <div v-else-if="['el-time-picker'].indexOf(activeData.tag) > -1 && activeData.defalutSelect == 1">
            <el-time-picker
              v-model="activeData.defaultValue"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              placeholder="请选择默认时间"
              style="width: 100%"
            >
            </el-time-picker>
          </div>
          <!-- <el-input :value="setDefaultValue(activeData.defaultValue)" placeholder="请输入默认值" @input="onDefaultValueInput" v-else /> -->
          <el-input v-model="activeData.defaultValue" placeholder="请输入默认值" @input="onDefaultValueInput" v-else />
        </el-form-item>
        <!-- 表达式单次刷新 -->
        <el-form-item
          v-if="activeData.expression && activeData.expression !== undefined && activeData.expression !== '' && activeData.defalutSelect == 3"
          label="表达式单次刷新"
        >
          <el-switch v-model="activeData.isOnceRefeshExpression"></el-switch>
        </el-form-item>
        <el-form-item v-if="activeData.min !== undefined" label="最小值">
          <el-input-number v-model="activeData.min" placeholder="最小值" />
        </el-form-item>
        <el-form-item v-if="activeData.max !== undefined" label="最大值">
          <el-input-number v-model="activeData.max" placeholder="最大值" />
        </el-form-item>
        <el-form-item v-if="activeData.step !== undefined" label="步长">
          <el-input-number v-model="activeData.step" placeholder="步数" />
        </el-form-item>
        <el-form-item v-if="['el-input-number', 'fc-amount'].includes(activeData.tag)" label="单位">
          <el-input v-model="activeData.unit" placeholder="单位" />
        </el-form-item>
        <el-form-item v-if="['el-input-number', 'fc-amount'].includes(activeData.tag) && !activeData.isStep" label="数字类型">
          <el-select v-model.trim="activeData.numberType" placeholder="请选择数字类型" style="width: 100%" clearable @change="handleNumberType">
            <el-option v-for="item in numberTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="['el-input-number', 'fc-amount'].includes(activeData.tag) && !activeData.isStep" label="精度">
          <el-input-number
            type="number"
            v-model="activeData.accuracy"
            :min="minAccuracy"
            :max="maxAccuracy"
            placeholder="请输入精度"
            @change="handleFoucusAccuracyNumber"
          />
        </el-form-item>
        <el-form-item
          v-if="['el-input-number', 'fc-amount'].includes(activeData.tag) && (activeData.numberType == 3 || activeData.numberType == 4)"
          label="小数位数"
        >
          <el-input-number
            type="number"
            v-model="activeData.precision"
            :min="minPrecision"
            :max="maxPrecision"
            placeholder="不限制"
            @change="handleFoucusNumber"
          />
        </el-form-item>
        <el-form-item v-if="activeData.actionText !== undefined" label="动作文字">
          <el-input v-model="activeData.actionText" placeholder="请输入动作文字" />
        </el-form-item>
        <el-form-item
          v-if="
            activeData.accept !== undefined &&
            activeData.tagIcon !== 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
          label="文件类型"
        >
          <el-select v-model="activeData.accept" placeholder="请选择文件类型" style="width: 100%" clearable>
            <el-option label="图片" value="image" />
            <!-- <el-option label="视频" value="video" />
            <el-option label="音频" value="audio" /> -->
            <!--  <el-option label="excel" value="xls" />
            <el-option label="word" value="doc" />
            <el-option label="pdf" value="pdf" />
            <el-option label="txt" value="txt" /> -->
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            activeData.tagIcon == 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
          label="文件类型"
        >
          <el-select v-model="activeData.acceptType" placeholder="请选择文件类型" :style="{ width: '100%' }" clearable multiple>
            <el-option label="xls" value="xls" />
            <el-option label="xlsx" value="xlsx" />
            <el-option label="docx" value="docx" />
            <el-option label="doc" value="doc" />
            <el-option label="pdf" value="pdf" />
            <el-option label="txt" value="txt" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            activeData.picNum !== undefined &&
            activeData.tagIcon !== 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
          label="限制张数"
        >
          <el-input v-model="activeData.picNum" placeholder="请输入最大限制张数" oninput="value=value.replace(/[^\d]/g,'')" @input="handlePicNum" />
        </el-form-item>
        <!-- 设置附件的内容 -->
        <el-form-item
          v-if="
            activeData.picNum !== undefined &&
            activeData.tagIcon == 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
          label="限制个数"
        >
          <el-input v-model="activeData.picNum" placeholder="请输入最大限制个数" oninput="value=value.replace(/[^\d]/g,'')" @input="handlePicNum" />
        </el-form-item>
        <el-form-item
          v-if="
            activeData.fileSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
          label="单张图片大小"
        >
          <el-input v-model.number="activeData.fileSize" placeholder="请输入单张图片大小">
            <template v-slot:append>
              <el-select v-model="activeData.sizeUnit" :style="{ width: '66px' }">
                <!-- <el-option label="KB" value="KB" /> -->
                <el-option label="MB" value="MB" />
                <!-- <el-option label="GB" value="GB" /> -->
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="
            activeData.fileSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
        >
          <div class="default-text">图片尺寸</div>
          <div style="display: flex; justify-content: space-between" v-if="activeData.sizeList">
            <span
              ><el-input
                placeholder="长"
                v-model="activeData.sizeList[0]"
                clearable
                style="width: 99%"
                oninput="value=value.replace(/[^\d]/g,'')"
              ></el-input
            ></span>
            <span style="padding: 0 8px; color: #8291a9">x</span>
            <span
              ><el-input
                placeholder="宽"
                v-model="activeData.sizeList[1]"
                clearable
                style="width: 99%"
                oninput="value=value.replace(/[^\d]/g,'')"
              ></el-input
            ></span>
            <span style="color: red; font-weight: bold; padding-left: 8px" @click="handelclearSizeList"
              ><el-icon><CircleClose /></el-icon
            ></span>
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            activeData.fileSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isVideoComType &&
            !isAudioComType &&
            !isSjctComType
          "
        >
          <div class="default-main">
            <div class="default-text">
              <span>图片信息</span>
              <el-tooltip class="item" effect="dark" placement="top">
                <el-icon><QuestionFilled style="color: #8291a9; padding-left: 8px" /></el-icon>
              </el-tooltip>
            </div>
            <div class="defalut-right">
              <div class="text">水印</div>
              <div class="text">写入属性</div>
            </div>
          </div>

          <div class="attr-main">
            <!-- <div class="attr-title" @click="handleIsPicFold">
              <span v-if="isPicFold" style="color:#8291a9;padding-left:8px;"><i class="el-icon-caret-bottom"></i></span>
              <span v-if="!isPicFold" style="color:#8291a9;padding-left:8px;"><i class="el-icon-caret-top"></i></span>
              <span>文件属性</span>
            </div>  -->
            <div class="attr-select">
              <div class="attr-item" v-for="item in activeData.attrList" :key="item.id">
                <div>{{ item.text }}</div>
                <div class="attr-checked">
                  <el-checkbox v-model="item.isSy" @change="choosePictureAttr(item)"></el-checkbox>
                  <el-checkbox v-model="item.isXr" @change="choosePictureAttr(item)"></el-checkbox>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="attr-main" style="margin-top:8px">
             <div class="attr-title" @click="handleIsAttachFold">
              <span v-if="isAttachFold" style="color:#8291a9;padding-left:8px;"><i class="el-icon-caret-bottom"></i></span>
              <span v-if="!isAttachFold" style="color:#8291a9;padding-left:8px;"><i class="el-icon-caret-top"></i></span>
              <span>附加属性</span>
            </div> 
            <div class="attr-select" v-if="isAttachFold">
              <div class="attr-item" v-for="item in activeData.attrList" :key="item.id" v-show="item.label == 'dlzh'">
                <div >{{ item.text }}</div>
                <el-checkbox  class="attr-checked" v-model="item.ischecked" @change="choosePictureAttr(item)"></el-checkbox>
              </div>
            </div>
          </div> -->
        </el-form-item>
        <!-- 视频内容 -->
        <el-form-item
          label="限制个数"
          v-if="
            activeData.picNum !== undefined &&
            activeData.tagIcon !== 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isAudioComType &&
            !isSjctComType
          "
        >
          <el-input
            v-model="activeData.picNum"
            placeholder="请输入最大限制个数"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @input="handleAudioPicNum"
          />
        </el-form-item>
        <!--oninput="value=value.replace(/[^\d]/g,'')"  -->
        <el-form-item
          label="单个视频时长"
          v-if="
            activeData.timeSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isAudioComType &&
            !isSjctComType
          "
        >
          <el-input
            v-model="activeData.timeSize"
            :min="0"
            :max="30"
            placeholder="请输入单个视频时长(30s)"
            oninput="if(value){value=value.replace(/[^\d]/g,'')}  if(value>30){value=30}"
          >
            <template v-slot:append>
              <el-select v-model="activeData.sizeUnit" :style="{ width: '66px' }">
                <!-- <el-option label="KB" value="KB" /> -->
                <el-option label="S" value="S" />
                <!-- <el-option label="GB" value="GB" /> -->
              </el-select>
            </template>
          </el-input>
        </el-form-item>

        <!-- 音频内容 -->
        <el-form-item
          label="限制个数"
          v-if="
            activeData.picNum !== undefined &&
            activeData.tagIcon !== 'xtfj' &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isVideoComType &&
            !isSjctComType
          "
        >
          <el-input
            v-model="activeData.picNum"
            placeholder="请输入最大限制个数"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @input="handleAudioPicNum"
          />
        </el-form-item>
        <el-form-item
          label="单个音频大小"
          v-if="
            activeData.fileSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isVideoComType &&
            !isSjctComType
          "
        >
          <el-input v-model="activeData.fileSize" placeholder="请输入单个音频大小" oninput="value=value.replace(/[^\d]/g,'')">
            <template v-slot:append>
              <el-select v-model="activeData.sizeUnit" :style="{ width: '66px' }">
                <!-- <el-option label="KB" value="KB" /> -->
                <el-option label="MB" value="MB" />
                <!-- <el-option label="GB" value="GB" /> -->
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <!--  单个文件大小 -->
        <el-form-item
          label="限制个数"
          v-if="
            activeData.fileNum !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isVideoComType &&
            !isSjctComType
          "
        >
          <el-input v-model="activeData.fileNum" placeholder="请输入限制个数" onkeyup="value=value.replace(/[^\d]/g,'')" />
        </el-form-item>
        <el-form-item
          label="单个文件大小"
          v-if="
            activeData.oneFileSize !== undefined &&
            !isXTQMComType &&
            !isXTZWComType &&
            !isXTDWSBComType &&
            !isXTZWSBComType &&
            !isUploadComType &&
            !isVideoComType &&
            !isSjctComType
          "
        >
          <el-input v-model="activeData.oneFileSize" placeholder="请输入单个文件大小" oninput="value=value.replace(/[^\d]/g,'')">
            <template v-slot:append>
              <el-select v-model="activeData.sizeUnit" :style="{ width: '66px' }">
                <!-- <el-option label="KB" value="KB" /> -->
                <el-option label="MB" value="MB" />
                <!-- <el-option label="GB" value="GB" /> -->
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="activeData.buttonText !== undefined" v-show="'picture-card' !== activeData['list-type']" label="按钮文字">
          <el-input v-model="activeData.buttonText" placeholder="请输入按钮文字" />
        </el-form-item>
        <!-- <el-form-item v-if="activeData['range-separator'] !== undefined" label="分隔符">
          <el-input v-model="activeData['range-separator']" placeholder="请输入分隔符" />
        </el-form-item> -->
        <el-form-item v-if="activeData['active-color'] !== undefined" label="开启颜色">
          <el-color-picker v-model="activeData['active-color']" />
        </el-form-item>
        <el-form-item v-if="activeData['inactive-color'] !== undefined" label="关闭颜色">
          <el-color-picker v-model="activeData['inactive-color']" />
        </el-form-item>
        <el-form-item v-if="activeData['allow-half'] !== undefined" label="允许半选">
          <el-switch v-model="activeData['allow-half']" />
        </el-form-item>
        <el-form-item v-if="activeData['show-text'] !== undefined" label="辅助文字">
          <el-switch v-model="activeData['show-text']" @change="rateTextChange" />
        </el-form-item>
        <el-form-item v-if="activeData['show-score'] !== undefined" label="显示分数">
          <el-switch v-model="activeData['show-score']" @change="rateScoreChange" />
        </el-form-item>
        <el-form-item v-if="activeData['show-stops'] !== undefined" label="显示间断点">
          <el-switch v-model="activeData['show-stops']" />
        </el-form-item>
        <el-form-item v-if="activeData.range !== undefined" label="范围选择">
          <el-switch v-model="activeData.range" @change="rangeChange" />
        </el-form-item>
        <el-form-item v-if="activeData.tag === 'el-color-picker'" label="颜色格式">
          <el-select v-model="activeData['color-format']" placeholder="请选择颜色格式" :style="{ width: '100%' }" @change="colorFormatChange">
            <el-option v-for="(item, index) in colorFormatOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <template v-if="activeData.tag === 'fc-org-select'">
          <el-form-item label="弹框名称" v-if="activeData.title !== undefined">
            <el-input v-model="activeData.title" placeholder="请输入弹框名称" />
          </el-form-item>
          <el-form-item label="可选数量" v-if="activeData.maxNum !== undefined">
            <el-input-number v-model="activeData.maxNum" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="按钮类型">
            <el-select v-model="activeData.buttonType">
              <el-option label="Button" value="button" />
              <el-option label="Input" value="input" />
            </el-select>
          </el-form-item>
          <template v-if="activeData.buttonType === 'button' && activeData.tagConfig">
            <el-divider content-position="left">标签展示</el-divider>
            <el-form-item label="大小" v-if="activeData.tagConfig.size !== undefined">
              <el-radio-group v-model="activeData.tagConfig.size">
                <el-radio-button label="medium">中等</el-radio-button>
                <el-radio-button label="small">较小</el-radio-button>
                <el-radio-button label="mini">迷你</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="主题" v-if="activeData.tagConfig.effect !== undefined">
              <el-select v-model="activeData.tagConfig.effect" placeholder="请选择">
                <el-option v-for="item in themeOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签样式" v-if="activeData.tagConfig.type !== undefined">
              <el-select v-model="activeData.tagConfig.type" placeholder="请选择">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="渐变动画" v-if="activeData.tagConfig['disable-transitions'] !== undefined">
              <el-switch v-model="activeData.tagConfig['disable-transitions']" :inactive-value="true" :active-value="false" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="边框描边" v-if="activeData.tagConfig.hit !== undefined">
              <el-switch v-model="activeData.tagConfig.hit" placeholder="请输入" />
            </el-form-item>
          </template>
        </template>
        <el-form-item
          v-if="activeData.size !== undefined && (activeData.optionType === 'button' || activeData.border || activeData.tag === 'el-color-picker')"
          label="选项尺寸"
        >
          <el-radio-group v-model="activeData.size">
            <el-radio-button label="medium">中等</el-radio-button>
            <el-radio-button label="small">较小</el-radio-button>
            <el-radio-button label="mini">迷你</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item v-if="activeData.tag === 'el-cascader'  && !isXZQYComType" label="是否多选">
          <el-switch v-model="activeData.props.props.multiple" />
        </el-form-item>
        <el-form-item v-if="activeData.tag === 'el-cascader'  && !isXZQYComType" label="可否筛选">
          <el-switch v-model="activeData.filterable" />
        </el-form-item> -->
        <el-form-item v-if="activeData.tagIcon == 'xtqm'" label="是否允许电子签名">
          <el-switch v-model="activeData.esign" />
        </el-form-item>
        <!-- <el-form-item v-if="activeData.readonly !== undefined  && !isSFZSBComType" label="是否只读">
          <el-switch v-model="activeData.readonly" />
        </el-form-item>
         -->
        <!-- && !isSFZSBComType && !isXZQYComType  && !isXTQMComType && !isXTZWComType&& !isXTDWSBComType &&!isXTZWSBComType&& !isVideoComType && !isAudioComType -->
        <el-form-item v-if="activeData.required !== undefined && !isXtsjyComType && !isXtsjdlComType" label="是否必填">
          <el-switch v-model="activeData.required" @change="requireChange" />
        </el-form-item>
        <el-form-item v-if="activeData.tagIcon == 'idCardScan'" label="是否共享">
          <el-switch v-model="activeData.isShare" />
        </el-form-item>
        <el-form-item label="是否合计" v-if="activeData.isCount != undefined">
          <el-radio-group v-model="activeData.isCount">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否加入总计" v-if="activeData.isTotal != undefined">
          <el-radio-group v-model="activeData.isTotal">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否需要总计" v-if="activeData.isTotalCount != undefined && activeData.tagIcon == 'xttable'">
          <el-radio-group v-model="activeData.isTotalCount">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 表格内容 -->
        <el-form-item v-if="activeData.tableType !== undefined" label="填写方式">
          <el-radio-group v-model="activeData.tableType">
            <el-radio label="table">表格</el-radio>
            <el-radio label="list">列表</el-radio>
          </el-radio-group>
          <div class="block" v-if="activeData.tableType == 'table'">
            <el-image style="width: 100%; height: 150px" :src="tablePic" fit="cover"></el-image>
          </div>
          <div class="block" v-if="activeData.tableType == 'list'">
            <el-image style="width: 100%; height: 150px" :src="listPic" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item v-if="activeData.tableType !== undefined" label="默认行">
          <el-input-number v-model="activeData.defaultRow" label="请输入默认行"></el-input-number>
        </el-form-item>
        <!-- 月份区间 主要给计算超期过渡费的 -->
        <el-form-item v-if="activeData.tableType !== undefined" label="是否开启月份区间">
          <el-switch v-model="activeData.openMonthArea" />
        </el-form-item>
        <!-- 表格的扩展数据 -->
        <el-form-item v-if="activeData.attrExpend !== undefined" label="扩展配置">
          <!-- 这里如果时有值保存到attribution  中  需要在 index 页面中的保存方法中设置 handleTableLayout   在这个方法中处理-+ -->
          <el-button type="primary" size="mini" @click="addAttrExpend">扩展配置</el-button>
        </el-form-item>
        <el-form-item v-if="activeData.showChinese !== undefined" label="显示大写">
          <el-switch v-model="activeData.showChinese" />
        </el-form-item>

        <!-- 日期选择控件的预警功能 -->
        <!-- <el-form-item v-if="activeData.tagIcon == 'date' && activeData.icon == 'dateIcon'" label="时间预警">
          <el-switch v-model="activeData.timeWarning" />
        </el-form-item>
        <div v-if="activeData.timeWarning">
          <el-form-item  label="预警日期">
            <el-date-picker
              v-model="activeData.timeDate"
              type="date"
              style="width: 100%;"
              value-format="timestamp"
              placeholder="请选择预警选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item  label="预警频率">
            <el-select v-model="activeData.frequency" placeholder="请选择频率" style="width: 100%;">
              <el-option
                v-for="item in timeWarningOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item  label="预警提示语">
            <el-input
              placeholder="请输入提示语"
              v-model="activeData.message"
              clearable>
            </el-input>
          </el-form-item>
        </div> -->
        <!--数据 草图绘制的功能 -->
        <el-form-item v-if="activeData.tagIcon == 'xtsjjt'" label="选择树节点">
          <el-tree
            v-if="activeData.tagIcon == 'xtsjjt'"
            :data="treeList"
            node-key="id"
            ref="tree"
            default-expand-all
            :props="defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            show-checkbox
            @check-change="handleCheckChange"
            :default-checked-keys="defaultCheckedKeys"
            :check-strictly="true"
            class="tree-div"
          >
            <template v-slot="{ node, data }">
              <div class="tree-row">
                <div class="tree-row-left">
                  <span>{{ data.typeName }}</span>
                </div>
                <div class="tree-row-right">
                  <div class="tree-row-handle">
                    <!-- :disabled="data.id == checkedNodeItem.id" -->
                    <el-checkbox label="图形" v-model="data.graph" @change="handelChangeTx(data, 'graph', data.graph)"></el-checkbox>
                    <el-checkbox label="点号" v-model="data.dot" @change="handelChangeTx(data, 'dot', data.dot)"></el-checkbox>
                    <el-checkbox label="文字" v-model="data.word" @change="handelChangeTx(data, 'word', data.word)"></el-checkbox>
                  </div>
                </div>
              </div>
            </template>
          </el-tree>
        </el-form-item>
        <!-- 数据源 -->
        <el-form-item v-if="isXtsjyComType">
          <div class="default-text">数据源类型</div>
          <el-checkbox-group v-model="activeData.sjyList" @change="handleChangeSJY">
            <el-checkbox label="graph">图形</el-checkbox>
            <el-checkbox label="shpData">SHP数据</el-checkbox>
            <el-checkbox label="perv">前面步骤结果</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- 数据地类信息 -->
        <el-form-item v-if="isXtsjdlComType">
          <div class="default-text">地类</div>
          <el-select v-model="activeData.dileiTypelist" multiple collapse-tags filterable style="width: 100%" placeholder="请选择地类">
            <el-option v-for="item in activeData.dileiList" :key="item.id" :label="item.label" :value="item.key"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <treeNode-dialog v-model:visible="dialogVisible" title="添加选项" @commit="addNode" />
    <!-- TODO -->
    <!-- <icons-dialog v-model:visible="iconsVisible" :current="activeData[currentIconModel]" @select="setIcon"></icons-dialog> -->

    <!-- 打开公式编辑的弹框TODO -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="activeData.expression"
      :inputType="activeData.inputType"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
    ></formula-editing-dialog>

    <!-- 级联选择添加数据弹窗 -->
    <el-dialog title="级联数据" v-model="cascaderDialog" width="80%" :close-on-click-modal="false" :before-close="handleCloseCascader">
      <el-tree
        :data="activeData.options"
        node-key="id"
        default-expand-all
        class="tree-content"
        :style="{ height: cascaderHeight }"
        :expand-on-click-node="false"
      >
        <template v-slot="{ node, data }">
          <div class="tree-row-cas">
            <div class="left">
              <el-input
                v-model="data.value"
                maxlength="100"
                @change="(element) => changeCasInput(element, data)"
                :readonly="node.level == 1"
              ></el-input>
            </div>
            <div class="end">
              <el-link type="primary" @click="addLowLev(node)">新增下级</el-link>
              <el-link type="danger" style="margin-left: 10px" @click="delLev(node, data)" v-show="node.level != 1">删除</el-link>
            </div>
          </div>
        </template>
      </el-tree>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseCascader">取 消</el-button>
          <el-button type="primary" @click="submitCascader">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 扩展配置弹窗 -->
    <skuMange
      :expendDialogProp="expendDialog"
      @closeExpend="closeExpend"
      :activeData="activeData"
      @submitExpend="submitExpend"
      ref="skuMangeRef"
    ></skuMange>

    <!-- 跳题逻辑弹窗 -->
    <el-dialog title="跳题逻辑" v-model="conditionSettingDialog" width="30%" :close-on-click-modal="false" :before-close="handleCloseCondition">
      <el-table :data="activeData.options" border style="width: 100%">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="选项" prop="label"></el-table-column>
        <el-table-column label="跳转到">
          <template v-slot="scope">
            <el-select v-model="scope.row.condition" placeholder="请选择" clearable @change="changeContion($event, scope.row)">
              <el-option v-for="item in conditionList" :key="item.vModel" :label="`${item.label}(${item.vModel})`" :value="item.vModel"> </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseCondition">取 消</el-button>
          <el-button type="primary" @click="submitCondition">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
// import { isArray, log } from 'util';
import { isArray } from '../../utils/validate';
// import TreeNodeDialog from './TreeNodeDialog/index.vue';
import { isNumberStr } from './utils/index';
import IconsDialog from './IconsDialog';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import { inputComponents, selectComponents, layoutComponents } from './components/generator/config';
import { selectRules } from '@/api/modal';
import { saveFormConf } from './utils/db';
import draggable from 'vuedraggable';
// import { mergeNumberOfExps, validExp, toRPN, calcRPN } from "@/utils/index.js";
import AreaCode from './areaCodeTemp/index.vue';
// import tablePic from './styles/table.png';
// import listPic from './styles/list.png';
// import { mapGetters } from 'vuex';
import skuMange from '../../components/skuMange/index.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter } from 'vue-router';
import type { FormInstance, FormRules } from 'element-plus';
import { Delete, ArrowDown, Setting, CirclePlus } from '@element-plus/icons-vue';

const router = useRouter();
const modalStore = useModalStore();
const userStore = useUserStore();
const dateTimeFormat = {
  date: 'yyyy-MM-dd',
  week: 'yyyy 第 WW 周',
  month: 'yyyy-MM',
  year: 'yyyy',
  datetime: 'yyyy-MM-dd HH:mm:ss',
  daterange: 'yyyy-MM-dd',
  monthrange: 'yyyy-MM',
  datetimerange: 'yyyy-MM-dd HH:mm:ss'
};

const props = defineProps<{
  showFieldProp: boolean;
  activeDataProp: any;
  formConfProp: any;
  drawingListProp: any[];
}>();

const emit = defineEmits(['tagChange', 'updateField', 'updateLabel', 'update:activeData']);
const showField = ref(false);
const activeData = computed(() => props.activeDataProp);
const formConf = ref(props.formConfProp);
const drawingList = ref(props.drawingListProp);

const input = ref<string>('');
// 使用 ref 定义响应式数据
const input1 = ref('');
const cascaderHeight = ref('');
const treeList = ref([]);
const defaultCheckedKeys = ref([]);
const appType = ref('#field');
const dateFormat = ref('yyyy-MM-dd');
const formulaVisible = ref(false);
const expressionTemp = ref([]);
const currentNode = ref(null);
const dialogVisible = ref(false);
const iconsVisible = ref(false);
const currentIconModel = ref(null);
const expDialogVisible = ref(false);
const expValid = ref(true);
const isPicFold = ref(true);
const isAttachFold = ref(true);
const cascaderDialog = ref(false);
const expendDialog = ref(false);
const conditionSettingDialog = ref(false);
const conditionList = ref([]);
const activeDataRef = ref<FormInstance>();
const skuMangeRef = ref(null);
// 身份证识别的 options
const SFZSBOptions = ref([
  {
    label: 0,
    text: '姓名',
    enName: 'xm',
    cnName: '姓名',
    strLength: 3000,
    inputHint: '请输入',
    valueMethod: 'input'
  },
  {
    label: 1,
    text: '性别',
    enName: 'xb',
    cnName: '性别',
    strLength: 3000,
    inputHint: '请选择',
    valueMethod: 'radio',
    options: [
      {
        id: new Date().getTime(),
        label: '男',
        value: 1
      },
      {
        id: new Date().getTime() + 1,
        label: '女',
        value: 2
      },
      {
        id: new Date().getTime() + 2,
        label: '不详',
        value: 3
      },
      {
        id: new Date().getTime() + 3,
        label: '其他',
        value: 99
      }
    ]
  }
  // ... 其他选项
]);
// 判断是不是首次加载
const isFristTrigger = ref(true);
// 动植物识别的 Options
const DWSBOptions = ref([
  { label: 0, text: '名称', enName: 'dwmc' },
  { label: 1, text: '图片', enName: 'dwtp' },
  { label: 2, text: '类型', enName: 'dwlx' },
  { label: 3, text: '描述', enName: 'dwms' }
]);

const ZWSBOptions = ref([
  { label: 0, text: '名称', enName: 'zwmc' },
  { label: 1, text: '图片', enName: 'zwms' },
  { label: 2, text: '类型', enName: 'zwlx' },
  { label: 3, text: '描述', enName: 'zwms' }
]);

// 各种选项列表
const themeOptions = ref([
  { value: 'dark', label: '深色' },
  { value: 'light', label: '亮色' },
  { value: 'plain', label: '扁平' }
]);

const typeOptions = ref([
  {
    value: 'success',
    label: 'success'
  },
  {
    value: 'info',
    label: 'info'
  },
  {
    value: 'warning',
    label: 'warning'
  },
  {
    value: 'danger',
    label: 'danger'
  }
]);
const dateTypeOptions = ref([
  {
    label: '日(date)',
    value: 'date'
  },
  {
    label: '周(week)',
    value: 'week'
  },
  {
    label: '月(month)',
    value: 'month'
  },
  {
    label: '年(year)',
    value: 'year'
  },
  {
    label: '日期时间(datetime)',
    value: 'datetime'
  }
]);
const dateRangeTypeOptions = ref([
  {
    label: '日期范围(daterange)',
    value: 'daterange'
  },
  {
    label: '月范围(monthrange)',
    value: 'monthrange'
  },
  {
    label: '日期时间范围(datetimerange)',
    value: 'datetimerange'
  }
]);
const colorFormatOptions = ref([
  {
    label: 'hex',
    value: 'hex'
  },
  {
    label: 'rgb',
    value: 'rgb'
  },
  {
    label: 'rgba',
    value: 'rgba'
  },
  {
    label: 'hsv',
    value: 'hsv'
  },
  {
    label: 'hsl',
    value: 'hsl'
  }
]);
const justifyOptions = ref([
  {
    label: 'start',
    value: 'start'
  },
  {
    label: 'end',
    value: 'end'
  },
  {
    label: 'center',
    value: 'center'
  },
  {
    label: 'space-around',
    value: 'space-around'
  },
  {
    label: 'space-between',
    value: 'space-between'
  }
]);

// 布局树属性
const layoutTreeProps = ref({
  label(data, node) {
    return data.componentName || `${data.label}: ${data.vModel}`;
  }
});

// 验证规则
const activeDataRules = ref({
  vModel: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    {
      min: 2,
      max: 10,
      message: '长度在 2 到 10 个字符',
      trigger: 'blur'
    },
    {
      message: '请输入2-10位数字或大小写字母组成的字段名称',
      pattern: /^[a-zA-Z0-9_]{2,10}$/,
      trigger: ['blur', 'change']
    }
  ],
  label: [
    { required: true, message: '请输入字段别名', trigger: 'blur' },
    {
      min: 2,
      max: 10,
      message: '长度在 2 到 10 个字符',
      trigger: 'blur'
    }
  ]
});

// 数字类型列表
const numberTypeList = ref([
  { label: '短整型', value: 1 },
  { label: '长整型', value: 2 },
  { label: '浮点型', value: 3 },
  { label: '双精度', value: 4 }
]);

// 时间警告选项
const timeWarningOptions = ref([
  { id: 1, value: 'hour', label: '一小时' },
  { id: 2, value: 'day', label: '一天' },
  { id: 3, value: 'week', label: '一周' },
  { id: 4, value: 'month', label: '一个月' }
]);

// 草图树的相关设置
const defaultProps = ref({
  children: 'list',
  label: 'typeName'
});

const tablePic = import('./styles/table.png');
const listPic = import('./styles/list.png');
// 计算属性
const vipType = userStore.vipType;
const dateOptions = computed(() => {
  if (activeData.value.type !== undefined && ['el-date-picker', 'fc-date-duration'].includes(activeData.value.tag)) {
    if (activeData.value['start-placeholder'] === undefined) {
      return dateTypeOptions.value;
    }
    return dateRangeTypeOptions.value;
  }
  return [];
});
const tagList = computed(() => {
  return [
    {
      label: '输入型组件',
      options: inputComponents
    },
    {
      label: '选择型组件',
      options: selectComponents
    }
  ];
});
const minPrecision = computed(() => {
  // numberType 1 短整型 2 长整型 3 浮点型  4  双精度
  if (activeData.value.numberType == 1 || activeData.value.numberType == 2) {
    return 0;
  } else if (activeData.value.numberType == 3 || activeData.value.numberType == 4) {
    return 1;
  } else {
    //  默认情况下是0
    return 0;
  }
});
// 请暂时不要删除
// 备注数据内容设置值，主要是为了设置导出GDB
// 短整型，精度位数在1-4 无小数位数
// 长整型，精度位数大于4，无小数位数
// 浮点型，精度位数在2-7 小数位数设置的值为精度值-1
// 双精度，精度位数大于8，小数位数设置的值为精度值-1
// 设置的值内容 默认值最大值为20，以上选择那种数字都不能超过20
// 小数位数最大值
const maxPrecision = computed(() => {
  // numberType 1 短整型 2 长整型 3 浮点型  4  双精度
  if (activeData.value.numberType == 1 || activeData.value.numberType == 2) {
    return 0;
  } else if (activeData.value.numberType == 3 || activeData.value.numberType == 4) {
    return activeData.value.accuracy - 1;
  } else {
    return 10;
  }
});
// 精度最小值
const minAccuracy = computed(() => {
  // numberType 1 短整型 2 长整型 3 浮点型  4  双精度
  if (activeData.value.numberType == 1) {
    // activeData.value.accuracy = 1
    return 1;
  } else if (activeData.value.numberType == 2) {
    // activeData.value.accuracy = 4
    return 4;
  } else if (activeData.value.numberType == 3) {
    // activeData.value.accuracy = 1
    return 2;
  } else if (activeData.value.numberType == 4) {
    // activeData.value.accuracy = 1
    return 8;
  } else {
    return 1;
  }
});
const maxAccuracy = computed(() => {
  // numberType 1 短整型 2 长整型 3 浮点型  4  双精度
  if (activeData.value.numberType == 1) {
    return 4;
  } else if (activeData.value.numberType == 2) {
    return 20;
  } else if (activeData.value.numberType == 3) {
    return 7;
  } else if (activeData.value.numberType == 4) {
    return 20;
  } else {
    return 20;
  }
});
const activeDataExpression = computed(() => {
  return activeData.value?.expression;
});
// 判断是不是增强控件
// 身份证识别
const isSFZSBComType = computed(() => {
  const isSFZSB = activeData.value?.tagIcon == 'idCardScan';
  return isSFZSB;
});
// 行政区域
const isXZQYComType = computed(() => {
  const isXZQY = activeData.value?.tagIcon == 'area';
  return isXZQY;
});
// 系统签名
const isXTQMComType = computed(() => {
  const isXTQM = activeData.value?.tagIcon == 'xtqm';
  return isXTQM;
});
// 系统指纹
const isXTZWComType = computed(() => {
  const isXTZW = activeData.value?.tagIcon == 'xtzw';
  return isXTZW;
});
// 系统动物识别
const isXTDWSBComType = computed(() => {
  const isXTDWSB = activeData.value?.tagIcon == 'xtdwsb';
  return isXTDWSB;
});
// 系统植物识别
const isXTZWSBComType = computed(() => {
  const isXTZWSB = activeData.value?.tagIcon == 'xtzwsb';
  return isXTZWSB;
});
// 判断是不是视频
const isVideoComType = computed(() => {
  const isVideo = activeData.value?.tagIcon == 'xtvideo';
  return isVideo;
});
// 判断是不是音频
const isAudioComType = computed(() => {
  const isAudio = activeData.value?.tagIcon == 'xtaudio';
  return isAudio;
});
// 判断是不是图片
const isUploadComType = computed(() => {
  const isUpload = activeData.value?.tagIcon == 'upload';
  return isUpload;
});
// 判断是不是表格
const isTableComType = computed(() => {
  const isTable = activeData.value?.tagIcon == 'xttable';
  return isTable;
});
// 判断是不是容器 行布局
const isRowComType = computed(() => {
  const isRow = activeData.value?.rowType == 'layout' && activeData.value.tagIcon == 'row';
  return isRow;
});
// 判断是不是联系人控件
const isLxrComType = computed(() => {
  const isLxr = activeData.value?.tag == 'el-xtlxr' && activeData.value.tagIcon == 'xtlxr';
  return isLxr;
});
// 判断是不是银行卡识别控件
const isYhkComType = computed(() => {
  const isYhk = activeData.value?.tagIcon == 'xtBankCard';
  return isYhk;
});
const isXTFJComType = computed(() => {
  const isXTFJ = activeData.value?.tagIcon == 'xtfj';
  return isXTFJ;
});
// 数据草图
const isSjctComType = computed(() => {
  const isCt = activeData.value?.tagIcon == 'xtsjjt';
  return isCt;
});
// 数据源
const isXtsjyComType = computed(() => {
  const isSjy = activeData.value?.tagIcon == 'xtsjy';
  return isSjy;
});
// 追溯地类
const isXtsjdlComType = computed(() => {
  const isXtsjdl = activeData.value?.tagIcon == 'xtzsdl';
  return isXtsjdl;
});
// 存取当前节点下的某一个节点内容
const checkedNodeItem = computed(() => {
  return modalStore.checkedNodeItem;
});
const moduleId = computed(() => {
  return modalStore.moduleId;
});
const modal_state = computed(() => {
  return parseInt(router.currentRoute.value.query.status);
});

//watch
watch(
  activeDataExpression,
  (val) => {
    if (val && (val != '' || val != null)) {
      activeData.value.defalutSelect = 3;
    } else {
      activeData.value.defalutSelect = 1;
    }
  },
  { deep: true }
);
// 监听本地数据变化
watch(
  activeData,
  (newVal) => {
    emit('update:activeData', { ...newVal });
  },
  { deep: true }
);

const handleSelectCommand = (command: string) => {
  if (command == 'string') {
    activeData.value.optionIsJson = false;
    activeData.value.options = [
      {
        id: new Date().getTime(),
        label: '选项1',
        value: '选项1'
      },
      {
        id: new Date().getTime() + 100,
        label: '选项2',
        value: '选项2'
      }
    ];
  } else {
    activeData.value.optionIsJson = true;
    activeData.value.options = [
      {
        id: new Date().getTime(),
        label: '选项1',
        value: '1'
      },
      {
        id: new Date().getTime() + 100,
        label: '选项2',
        value: '2'
      }
    ];
  }
};
const beforeCommand = (item, command) => {
  return {
    item: item,
    command: command
  };
};
// 当切换身份证识别控件的时候设置的性别的值的时候
const handleSFZSelectCommand = (command: any) => {
  if (command.command == 'string') {
    command.item.isJson = false;
    command.item.options = [
      {
        id: new Date().getTime(),
        label: '男',
        value: '男'
      },
      {
        id: new Date().getTime() + 1,
        label: '女',
        value: '女'
      },
      {
        id: new Date().getTime() + 2,
        label: '不详',
        value: '不详'
      },
      {
        id: new Date().getTime() + 3,
        label: '其他',
        value: '其他'
      }
    ];
  } else {
    command.item.isJson = true;
    command.item.options = [
      {
        id: new Date().getTime(),
        label: '男',
        value: 1
      },
      {
        id: new Date().getTime() + 1,
        label: '女',
        value: 2
      },
      {
        id: new Date().getTime() + 2,
        label: '不详',
        value: 3
      },
      {
        id: new Date().getTime() + 3,
        label: '其他',
        value: 99
      }
    ];
  }
};
/**
 * 当缴费控件切换数据对象和字符串对象的时候重置当前值
 * @param command 但前需要设置的值
 */
const handlePaySelectCommand = (command: any) => {
  if (command.command == 'string') {
    command.item.isJson = false;
    command.item.options = [
      {
        id: new Date().getTime(),
        label: '未缴费',
        value: '未缴费'
      },
      {
        id: new Date().getTime() + 1,
        label: '缴费中',
        value: '缴费中'
      },
      {
        id: new Date().getTime() + 2,
        label: '已缴费',
        value: '已缴费'
      },
      {
        id: new Date().getTime() + 3,
        label: '退费中',
        value: '退费中'
      },
      {
        id: new Date().getTime() + 4,
        label: '已退费',
        value: '已退费'
      },
      {
        id: new Date().getTime() + 5,
        label: '退费失败',
        value: '退费失败'
      },
      {
        id: new Date().getTime() + 5,
        label: '缴费失败',
        value: '缴费失败'
      }
    ];
  } else {
    command.item.isJson = true;
    command.item.options = [
      {
        id: new Date().getTime(),
        label: '未缴费',
        value: 1
      },
      {
        id: new Date().getTime() + 1,
        label: '缴费中',
        value: 2
      },
      {
        id: new Date().getTime() + 2,
        label: '已缴费',
        value: 3
      },
      {
        id: new Date().getTime() + 3,
        label: '退费中',
        value: 4
      },
      {
        id: new Date().getTime() + 4,
        label: '已退费',
        value: 5
      },
      {
        id: new Date().getTime() + 5,
        label: '退费失败',
        value: 6
      },
      {
        id: new Date().getTime() + 5,
        label: '缴费失败',
        value: 7
      }
    ];
  }
};
// 将日期类型的控件转换成日期类型
const handleDateTransLate = (type, value) => {
  if (!value) return '';
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  if (type == 'year') {
    return year;
  } else if (type == 'month') {
    return year + '-' + month;
  } else {
    return year + '-' + month + '-' + day;
  }
};
const handleChangeSJY = (val) => {
  activeData.value.sjyCheckList = [];
  if (val.includes('graph')) {
    activeData.value.sjyCheckList.push({ value: 'graph', label: '图形' });
  }
  if (val.includes('shpData')) {
    activeData.value.sjyCheckList.push({
      value: 'shpData',
      label: 'SHP数据'
    });
  }
  if (val.includes('perv')) {
    activeData.value.sjyCheckList.push({
      value: 'perv',
      label: '前面步骤结果'
    });
  }
};
// 设置提示语是否变化的标识
const handleInputPlaceholder = () => {
  activeData.value.isPlaceholder = true;
};
// 下拉和单选框的选择默认值
const handleSelectDefaultValue = (val) => {
  if (val === '') {
    activeData.value.defaultValue = undefined;
    return;
  }
  if (val === activeData.value.defaultValue) {
    activeData.value.defaultValue = undefined;
  } else {
    activeData.value.defaultValue = val;
  }
};
// 多选框设置默认事件
const handleCheckDefaultValue = (val) => {
  activeData.value.defaultValue.push(val);
  activeData.value.defaultValue.forEach((item) => {
    if (item == val) {
      const index = activeData.value.defaultValue.indexOf(val);
      activeData.value.defaultValue.splice(index, 1);
    }
    // else{
    //   activeData.value.defaultValue.push(val)
    // }
    return item;
  });
};
// 选择公式编辑时  打开弹框
const handleRadioValue = (val: any) => {
  activeData.value.defalutSelect = val;
  if (val == 3) {
    formulaVisible.value = true;
  } else {
    activeData.value.expression = undefined;
  }
};
// 情况图片的长宽
const handelclearSizeList = () => {
  activeData.value.sizeList = [];
};
const choosePictureAttr = (item) => {};
// 图片展开收起
const handleIsPicFold = (val) => {
  isPicFold.value = !isPicFold.value;
};
// 附加展开收起
const handleIsAttachFold = (val) => {
  isAttachFold.value = !isAttachFold.value;
};
// 提交公式
const handleSubmitFormulation = (expression) => {
  activeData.value.expression = expression;
};
const handleBlurUpdateField = () => {
  emit('updateField');
};
const handleInputField = (val) => {
  if (val.length > 200) {
    activeDataRef.value.validateField('label');
  }
};
/**
 * 设置字段生效或者失效的提示
 * @param val
 */
const handleFieldStatus = (val) => {
  if (isFristTrigger.value) {
    isFristTrigger.value = false;
    return;
  }
  const text =
    val === 1 ? '生效字段之后该字段在任务中可以对该字段设置，确认生效这个字段吗？' : '失效字段之后该字段在任务中不可见，确认失效这个字段吗？';
  setTimeout(() => {
    ElMessageBox.confirm(text, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        activeData.value.status = val;
      })
      .catch(() => {
        // 还原 switch 状态
        activeData.value.status = val === 1 ? 0 : 1;
      });
  }, 500);
};
//  及时更新输入框中的值,有时候输入字段别名 不能够及时更新 在这里解决这个问题。
const handelUpdatevModel = (val) => {
  if (val.toUpperCase() == 'ID' || val.toUpperCase() == 'PARENTID') {
    val = '';
  }
  activeData.value.vModel = val;
  // this.$forceUpdate(); TODO
};
//根据输入的字段别名来处理提示语的内容
const handleLabelInput = (item) => {
  emit('updateLabel', item.label);
};
// 关闭公式弹框
const handleCloseFormulation = () => {
  formulaVisible.value = false;
};
const notObject = (val) => {
  return val !== null || val !== undefined || Object(val) === val;
};
const requireChange = (required) => {
  // 下拉 单选 计数 日期区间 时间区间 需要写进流程条件中
  if (!activeData.value.proCondition) return;
  if (required && !activeData.value.multiple) {
    // 没有设置时长的时间范围组件不能作为流程条件
    const isRangeCmp = ['fc-date-duration', 'fc-time-duration'].includes(activeData.value.tag);
    const showDuration = activeData.value.showDuration;
    if (isRangeCmp && !showDuration) {
      // this.$store.commit("delPCondition", activeData.value.formId);
      return;
    }
    modalStore.addPCondition(activeData.value);
  } else {
    modalStore.delPCondition(activeData.value.formId);
  }
};
const addReg = () => {
  activeData.value.regList.push({
    pattern: '',
    message: ''
  });
};
const addSelectItem = () => {
  const id = new Date().getTime() + Math.floor(Math.random() * 100) + 1;
  const leng = activeData.value.options.length + 1;
  if (activeData.value.tag == 'el-select' && activeData.value.optionIsJson) {
    activeData.value.options.push({
      id: id,
      label: `选项${leng}`,
      value: `${leng}`
    });
  } else {
    activeData.value.options.push({
      id: id,
      label: `选项${leng}`,
      value: `选项${leng}`
    });
  }
};
// 添加身份证性别时候的选项
const handleAddSfzItem = (item, ite) => {
  const id = new Date().getTime() + Math.floor(Math.random() * 100) + 1;
  const leng = item.options.length + 1;
  if (item.isJson) {
    item.options.push({
      id: id,
      label: `选项${leng}`,
      value: `${leng}`
    });
  } else {
    item.options.push({
      id: id,
      label: `选项${leng}`,
      value: `选项${leng}`
    });
  }
};
const addCascaderItem = () => {
  // 主动添加一个虚拟的全部分类
  const options = [
    {
      id: 1,
      value: '全部分类',
      label: '全部分类',
      children: [...activeData.value.options]
    }
  ];
  activeData.value.options = options;
  cascaderHeight.value = window.innerHeight - 300 + 'px';
  cascaderDialog.value = true;
};
const addTreeItem = () => {
  // ++idGlobal.value; TODO
  dialogVisible.value = true;
  currentNode.value = activeData.value.options;
};
const renderContent = (h: any, { node, data, store }: any) => {
  return h('div', { class: 'custom-tree-node' }, [
    h('span', node.label),
    h('span', { class: 'node-operation' }, [
      h('i', {
        class: 'el-icon-plus',
        title: '添加',
        onClick: () => append(data)
      }),
      h('i', {
        class: 'el-icon-delete',
        title: '删除',
        onClick: () => remove(node, data)
      })
    ])
  ]);
};
const append = (data: any) => {
  if (!data.children) {
    data.children = [];
  }
  dialogVisible.value = true;
  currentNode.value = data.children;
};
const remove = (node: any, data: any) => {
  const { parent } = node;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
};
const addNode = (data: any) => {
  currentNode.value.push(data);
};
const setOptionValue = (item, val, params) => {
  const res = isNumberStr(val) ? +val : val;

  if (params) {
    nextTick(() => {
      item.label = res;
      item.value = res;
      // this.$forceUpdate()
    });
  } else {
    item.label = res;
    item.value = res;
  }
};
const setJsonOptionValue = (item, val, type) => {
  const res = isNumberStr(val) ? +val : val;
  item[type] = res;
  // item.type = res TODO
};
const setDefaultValue = (val) => {
  if (Array.isArray(val)) {
    return val.join(',');
  }
  if (['string', 'number'].indexOf(val) > -1) {
    return val;
  }
  if (typeof val === 'boolean') {
    return `${val}`;
  }
  return val;
};
const onDefaultValueInput = (str) => {
  if (activeData.value.tagIcon == 'number') {
    //数字输入需要判断精度 如精度2 那么该数据不能超过99
    // 需要通过精度 判断默认值不能大于精度位数
    const accuracy = activeData.value.accuracy;
    const maxValue = Math.pow(10, accuracy) - 1;
    const reg = new RegExp(`^(0|[1-9]\\d{0,${accuracy - 1}})$`);

    if (!reg.test(str) || parseInt(str) > maxValue) {
      ElMessage.error(`请输入1-${maxValue}范围内的数字`);
      activeData.value.defaultValue = maxValue;
      return;
    }
  }
  if (isArray(activeData.value.defaultValue)) {
    // 数组
    activeData.value.defaultValue = str.split(',').map((val) => (isNumberStr(val) ? +val : val));
  } else if (['true', 'false'].indexOf(str) > -1) {
    // 布尔
    activeData.value.defaultValue = JSON.parse(str);
  } else {
    // 字符串和数字
    activeData.value.defaultValue = isNumberStr(str) ? +str : str;
  }
};
const onSwitchValueInput = (val, name) => {
  if (['true', 'false'].indexOf(val) > -1) {
    activeData.value[name] = JSON.parse(val);
  } else {
    activeData.value[name] = isNumberStr(val) ? +val : val;
  }
};
const setTimeValue = (val, type) => {
  const valueFormat = type === 'week' ? dateTimeFormat.date : val;
  activeData.value.defaultValue = null;
  activeData.value['value-format'] = valueFormat;
  activeData.value.format = val;
};
const spanChange = (val) => {};
const multipleChange = (val) => {
  activeData.value.defaultValue = val ? [] : '';
};
const dateTypeChange = (val) => {
  setTimeValue(dateTimeFormat[val], val);
};
const rangeChange = (val) => {
  activeData.value.defaultValue = val ? [activeData.value.min, activeData.value.max] : activeData.value.min;
};
const rateTextChange = (val) => {
  if (val) activeData.value['show-score'] = false;
};
const rateScoreChange = (val) => {
  if (val) activeData.value['show-text'] = false;
};
const colorFormatChange = (val) => {
  activeData.value.defaultValue = null;
  activeData.value['show-alpha'] = val.indexOf('a') > -1;
  activeData.value.renderKey = activeData.value.formId + new Date().getTime(); // 更新renderKey,重新渲染该组件
};
const openIconsDialog = (model) => {
  iconsVisible.value = true;
  currentIconModel.value = model;
};
const setIcon = (val) => {
  activeData.value[this.currentIconModel] = val;
};
const tagChange = (val) => {
  let target = inputComponents.find((item) => item.tagIcon === tagIcon);
  if (!target) target = selectComponents.find((item) => item.tagIcon === tagIcon);
  emit('tagChange', target);
};
const handleXZQYLevel = (val) => {
  const list = [
    { value: 2, text: '(省市)' },
    { value: 3, text: '(省市区)' },
    { value: 4, text: '(省市区街道)' },
    { value: 4, text: '(省市区街道社区)' },
    { value: 5, text: '(省市区街道社区-详细信息)' }
  ];
  const arr = list.filter((item) => item.value == val);
  if (arr.length > 0) {
    activeData.value.placeholder = `请选择${activeData.value.label}${arr[0].text}`;
  }
  activeData.value.xzqyLevel = val;
};
const handleOptions = (list, level) => {
  const filteredArray = [];
  for (let i = 0; i < list.length; i++) {
    if (list[i].level === level) {
      filteredArray.push({
        level: list[i].level,
        label: list[i].label,
        value: list[i].value
      });
    }
    if (list[i].children && list[i].level < level) {
      const children = handleOptions(list[i].children, level);
      if (children.length > 0) {
        filteredArray.push({
          level: list[i].level,
          label: list[i].label,
          value: list[i].value,
          children: children
        });
      }
    }
  }
  return filteredArray;
};
const handelChangeCityCode = (val) => {
  activeData.value.defaultValue = val;
};
// 处理限制小数位数如果输入了小数点的内容，则只匹配整数部分
const handleFoucusNumber = (currentValue, oldValue) => {
  const regex = /(\d+)\.?.*$/;
  const match = regex.exec(currentValue);
  const integerPart = Number(match[1]);
  activeData.value.precision = integerPart;
};
const handleFoucusAccuracyNumber = (currentValue, oldValue) => {
  const regex = /(\d+)\.?.*$/;
  const match = regex.exec(currentValue);
  const integerPart = Number(match[1]);
  activeData.value.accuracy = integerPart;
};
const handleNumberType = (val) => {
  if (val == 1) {
    // activeData.value.accuracy = 1;
    // activeData.value.precision = undefined;
    activeData.value.precision = undefined;
    activeData.value.accuracy = 1;
  } else if (val == 2) {
    // activeData.value.accuracy = 4;
    // activeData.value.precision = undefined;
    activeData.value.precision = undefined;
    activeData.value.accuracy = 4;
  } else if (val == 3) {
    // activeData.value.accuracy = 7;
    // activeData.value.precision = 1;
    activeData.value.precision = 1;
    activeData.value.accuracy = 7;
  } else if (val == 4) {
    // activeData.value.accuracy = 8;
    // activeData.value.precision = 1;
    activeData.value.precision = 1;
    activeData.value.accuracy = 8;
  }
};
// 单行和多行的最多输入 只能输入
// 2024/12/12 因为工作需求调整这里用户输入的字段3000  实际公司的需求是2000
const handleMaxLengthInput = (val) => {
  if (parseInt(val) > 3000) {
    activeData.value.maxlength = 3000;
  }
};
// 图片和附件的上传数量只能限制100
const handlePicNum = (val) => {
  if (parseInt(val) > 1000) {
    activeData.value.picNum = 100;
  }
};
// 视频和音频的显示数量10
const handleAudioPicNum = (val) => {
  const value = val.replace(/[^\d]/g, '');
  if (parseInt(value) > 10) {
    activeData.value.picNum = 10;
  }
};

const getModuleDetial = (moduleName: string) => {
  const params = {
    moduleId: moduleId.value
  };
  selectRules(params).then((res) => {
    if (res.code == 200) {
      defaultCheckedKeys.value = [];
      if (res.data.length != 0) {
        treeList.value = res.data;
        handleForData(res.data);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};
// 循环遍历处理树结构  给树结构添加一个图形和文字的标识
const handleForData = (list: any) => {
  if (activeData.value.vlaue.attrIdList && activeData.value.attrIdList.length > 0) {
    // 有值代表时当前数据返乡
    activeData.value.attrIdList.forEach((item) => {
      handlerTree(item, list);
    });
    // 这里return 之后就不在往下走了，回显数据内容
    // 下面时新建的内容
    return;
  }
  // else{
  //   // 无值的情况下 来回切换值的时候
  //   activeData.value.attrIdList = []
  // }
  // 无值代表要新增
  list.forEach((item) => {
    if (item.id == checkedNodeItem.value?.id) {
      defaultCheckedKeys.value.push(item.id);
      const obj = {
        id: checkedNodeItem.value?.id,
        word: false,
        graph: true,
        dot: false
      };

      item.word = false;
      item.graph = true;
      item.dot = false;
      activeData.value.attrIdList.push(obj);
    } else {
      item.word = false;
      item.graph = false;
      item.dot = false;
    }
    if (item.list.length > 0) {
      handleForData(item.list);
    }
  });
};
const handlerTree = (item: any, list: any) => {
  list.map((it) => {
    if (item.id == it.id) {
      it.word = item.word;
      it.graph = item.graph;
      it.dot = item.dot;
      defaultCheckedKeys.value.push(it.id);
    }
    if (it.list.length > 0) {
      handlerTree(item, it.list);
    }
    return it;
  });
};
const handleCheckChange = (data, checked) => {
  let flg = false;
  for (let index = 0; index < activeData.value.attrIdList.length; index++) {
    if (activeData.value.attrIdList[index].id == data.id) {
      flg = true;
      break;
    }
  }
  if (checked && !flg) {
    activeData.value.attrIdList.forEach((ite) => {
      if (ite.id !== data.id) {
        const obj = {
          id: data.id,
          word: data.word,
          graph: data.graph,
          dot: data.dot
        };
        activeData.value.attrIdList.push(obj);
      }
    });
  } else if (!checked && flg) {
    const obj = activeData.value.attrIdList.find((i) => {
      return i.id == data.id;
    });
    const index = activeData.value.attrIdList.indexOf(obj);
    // 删除该数据的时候 要把图形和文字选项都清空掉
    data.word = false;
    data.graph = false;
    data.dot = false;
    activeData.value.attrIdList.splice(index, 1);
  }
  // 如果当前id 没有 要把树节点置空 要把图形和文字选项都清空掉
  if (!flg) {
    data.word = false;
    data.graph = false;
    data.dot = false;
  }
};
const handelChangeTx = (data, str, val) => {
  activeData.value.attrIdList.map((i) => {
    if (i.id == data.id) {
      i.str = val;
    }
    return i;
  });
};
const handleCloseCascader = () => {
  activeData.value.options = activeData.value.options[0].children;
  cascaderDialog.value = false;
};
// 提交 编辑的级联数据
const submitCascader = () => {
  if (activeData.value.options[0].children.length == 0) {
    ElMessage.error('请添加级联选择内容！！！');
    return;
  }
  activeData.value.options = activeData.value.options[0].children;
  cascaderDialog.value = false;
};
// 新增下级
const addLowLev = (node) => {
  if (node.level == 5) {
    ElMessage.error('最多支持4级！！！');
    return;
  }
  const id = new Date().getTime() + Math.floor(Math.random() * 100) + 1;
  const newChild = { id: id, value: `选项`, children: [] };
  if (!node.data.children) {
    node.data.children = [];
  }
  node.data.children.push(newChild);
};
const delLev = (node, data) => {
  ElMessageBox.confirm('是否要删除该节点？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex((d) => d.id === data.id);
      children.splice(index, 1);
    })
    .catch(() => {});
};
// 改变值
const changeCasInput = (element, data) => {
  data.label = element;
};
const addAttrExpend = (data) => {
  // 判断表格控件中是否有其他控件，有控件的情况下才可以打开
  const flag = activeData.value.children.length == 0;
  if (flag) {
    ElMessage.error('请先拖动控件到表格控件中，再设置表格配置!');
    return;
  } else {
    expendDialog.value = true;
    skuMangeRef.value.init(activeData.value);
  }
};
const closeExpend = () => {
  expendDialog.value = false;
};
const submitExpend = (tableList, tableData, rows, attrs) => {
  activeData.value.attrExpend.tableList = tableList;
  activeData.value.attrExpend.tableData = tableData;
  activeData.value.attrExpend.rows = rows;
  activeData.value.attrExpend.attrs = attrs;
  expendDialog.value = false;
};
const jumpLogic = () => {
  // 需要先得到当前显示组件的位置
  let num = 0;
  for (let i = 0; i < drawingList.value.length; i++) {
    if (drawingList.value[i].vModel == activeData.value.vModel) {
      num = i;
      break;
    }
  }
  const list = [];
  drawingList.value.forEach((v, vdx) => {
    if (v.vModel != activeData.value.vModel && vdx > num) {
      //剔除自己 并只能跳转当前题下面的内容
      list.push({
        label: v.label,
        vModel: v.vModel
      });
    }
  });
  conditionList.value = list;
  conditionSettingDialog.value = true;
};
const handleCloseCondition = () => {
  conditionSettingDialog.value = false;
};
const submitCondition = () => {
  conditionSettingDialog.value = false;
};
const changeContion = (item, row) => {
  if (item) {
    const delList = [];
    for (let i = 0; i < conditionList.value.length; i++) {
      if (conditionList.value[i].vModel == item) {
        // 只取选择跳转的字段和第一个字段之间的值
        break;
      }
      delList.push(conditionList.value[i].vModel);
    }
    row.breaks = delList.join(',');
  } else {
    // 需要跳转的设置为空
    row.breaks = '';
  }
};
const handleInputNumber = () => {};
const initShow = (val: any) => {
  if (val.length > 0) {
    showField.value = true;
  } else {
    showField.value = false;
  }
};

defineExpose({
  getModuleDetial,
  initShow,
  defaultCheckedKeys
});
</script>

<style lang="scss" scoped>
.flex-row {
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .label {
    width: 100px;
    text-align: right;
    margin-right: 10px;
  }
  .end {
    flex: 1;
  }
}
.sfzsb-check-main {
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  .sfzsb-row {
    display: flex;
    align-content: center;
    align-items: center;
    .text-label {
      vertical-align: middle;
      font-size: 14px;
      color: #606266;
      line-height: 40px;
      font-weight: 600;
      margin-left: 10px;
      // margin-right: 10px;
      width: 100px;
    }
  }
  .sfzsb-row-option {
    display: flex;
    align-content: center;
    align-items: flex-start;
    width: 100%;
    .text-label {
      font-size: 14px;
      color: #606266;
      line-height: 40px;
      font-weight: 600;
      margin-left: 10px;
      margin-right: 10px;
      width: 85px;
    }
    .option-main {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-content: center;
      .option-item {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-content: center;
        margin: 4px 0;
        align-items: center;
      }
      .option-btn {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}

:deep(.el-tree-node__content) {
  height: auto;
}
:deep(.el-tree) {
  overflow: auto;
}
.tree-content {
  // height: 400px;
  overflow: auto;
}
.tree-row-cas {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  width: 100%;
  .left {
    flex: 1;
    margin-right: 20px;
  }
  .end {
    width: 120px;
  }
}
.right-board {
  min-width: 360px;
  width: 20%;
  // position: absolute;
  // right: 16px;
  // top: 8px;
  flex: 1;
  padding-top: 3px;
  overflow: hidden;
  margin-right: 16px;
  .field-box {
    position: relative;
    height: calc(100vh - 200px);
    box-sizing: border-box;
    overflow-y: auto;
    margin: 0 0 0 16px;
  }
}
/*滚动条样式*/
.field-box::-webkit-scrollbar {
  width: 4px;
}
.field-box::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.field-box::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.select-item {
  display: flex;
  border: 1px dashed #fff;
  box-sizing: border-box;
  & .close-btn {
    cursor: pointer;
    color: #f56c6c;
  }
  & .el-input + .el-input {
    margin-left: 4px;
  }
}
.select-item + .select-item {
  margin-top: 4px;
}

.select-line-icon {
  line-height: 32px;
  font-size: 16px;
  padding: 0 4px;
  color: #777;
}
.option-drag {
  cursor: move;
}
.default-text {
  vertical-align: middle;
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  font-weight: 600;
}
// .el-checkbox{
//   width:35%;
// }
.select-item {
  &:hover {
    .select-value {
      // position: ;
      display: flex;
    }
  }
}
.select-value {
  position: relative;
  display: none;
  & {
    .el-radio {
      position: absolute;
      right: 10px;
      top: 10px;
      :deep(.el-radio__label) {
        display: none !important;
      }
    }
    .el-checkbox-group {
      position: absolute;
      right: 10px;
      top: 1px;
      .el-checkbox {
        :deep(.el-checkbox__label) {
          display: none;
        }
      }
    }
  }
}
.selec-value-hover {
  display: flex;
  &:hover {
    display: flex;
    .el-checkbox-group {
      position: absolute;
      right: 10px;
      top: 1px;
      .el-checkbox {
        :deep(.el-checkbox__label) {
          display: none;
        }
      }
    }
  }
}
.default-main {
  display: flex;
  justify-content: space-between;
  .default-text {
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    font-weight: 600;
  }
  .defalut-right {
    display: flex;
    justify-content: space-between;
    .text {
      vertical-align: middle;
      font-size: 14px;
      color: #606266;
      line-height: 40px;
      font-weight: 600;
      width: 60px;
      text-align: left;
    }
  }
}
.attr-main {
  border-radius: 8px;
  border: 1px #e6ebf5 solid;
  .attr-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px #e6ebf5 solid;
  }
  .attr-select {
    margin: 0 8px;
    .attr-item {
      height: 24px;
      line-height: 24px;
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
      margin: 4px 0;
      .attr-checked {
        width: 100px;
        display: flex;
        justify-content: space-between;
        text-align: center;
      }
    }
  }
}
.tree-div {
  height: calc(100% - 49px);
  overflow: auto;
  width: 100%;
  color: #161d26;
  margin-left: -5px;
  .tree-row {
    height: 44px;
    display: flex;
    flex-direction: row;
    align-items: center;
    // justify-content: space-between;
    font-size: 12px;
    width: 100%;
    // margin-left: -20px;
    .tree-row-left {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .tree-row-right {
      width: auto;
      height: 40px;
      position: absolute;
      right: 20px;
      // left: 200px;
      display: flex;
      align-items: center;
      .el-checkbox {
        width: 25px;
        :deep(.el-checkbox__label) {
          padding-left: 4px;
          font-size: 12px;
        }
      }
    }
    /*滚动条样式*/
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
}
</style>
