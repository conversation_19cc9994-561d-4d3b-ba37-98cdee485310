$editorTabsborderColor: #121315;
body, html{
  margin: 0;
  padding: 0;
  background: #fff;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "PingFang SC";
}

input, textarea{
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
}

.editor-tabs{
  background: $editorTabsborderColor;
  .el-tabs__header{
    margin: 0;
    border-bottom-color: $editorTabsborderColor;
    .el-tabs__nav{
      border-color: $editorTabsborderColor;
    }
  }
  .el-tabs__item{
    height: 32px;
    line-height: 32px;
    color: #888a8e;
    border-left: 1px solid $editorTabsborderColor!important;
    background: #363636;
    margin-right: 5px;
    user-select: none;
    &.is-active{
      background: #1e1e1e;
      border-bottom-color: #1e1e1e!important;
      color: #fff;
    }
  }
  .el-icon-edit{
    color: #f1fa8c;
  }
  .el-icon-document{
    color: #a95812;
  }
}

// home
.right-scrollbar {
  .el-scrollbar__view {
    padding: 12px 18px 15px 15px;
  }
}
.el-scrollbar__wrap {
  box-sizing: border-box;
  overflow-x: hidden !important;
  margin-bottom: 0 !important;
}
.center-tabs{
  .el-tabs__header{
    margin-bottom: 0!important;
  }
  .el-tabs__item{
    width: 50%;
    text-align: center;
  }
  .el-tabs__nav{
    width: 100%;
  }
}
.reg-item{
  padding: 12px 6px;
  background: #f8f8f8;
  position: relative;
  border-radius: 4px;
  .close-btn{
    position: absolute;
    right: -6px;
    top: -6px;
    display: block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    color: #fff;
    text-align: center;
    z-index: 1;
    cursor: pointer;
    font-size: 12px;
    &:hover{
      background: rgba(210, 23, 23, 0.5)
    }
  }
  & + .reg-item{
    margin-top: 18px;
  }
}
.action-bar{
  & .el-button+.el-button {
    margin-left: 15px;
  }
  & i {
    font-size: 20px;
    vertical-align: middle;
    position: relative;
    top: -1px;
  }
}

.custom-tree-node{
  width: 100%;
  font-size: 14px;
  .node-operation{
    float: right;
  }
  i[class*="el-icon"] + i[class*="el-icon"]{
    margin-left: 6px;
  }
  .el-icon-plus{
    color: #409EFF;
  }
  .el-icon-delete{
    color: #157a0c;
  }
}

.el-scrollbar__view{
  overflow-x: hidden;
}

.el-rate{
  display: inline-block;
  vertical-align: text-top;
}
.el-upload__tip{
  line-height: 1.2;
}

$selectedColor: #f6f7ff;
$lighterBlue: #409EFF;

.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
}

.components-list {
  box-sizing: border-box;
  height: 100%;
  padding-left: 8px;
  padding-right: 8px;
  
  .components-item {
    display: inline-block;
    width: 48%;
    margin: 1%;
    transition: transform 0ms !important;
  }
}

.components-draggable {
  padding-bottom: 10px;
}

.components-title {
  font-size: 14px;
  color: #222;
  margin: 6px 2px;
  
  .svg-icon {
    color: #666;
    font-size: 18px;
  }
}

.components-body {
  padding: 8px 18px;
  background: #FFF;
  font-size: 12px;
  line-height: 18px;
  cursor: move;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  
  .svg-icon {
    color: #777;
    font-size: 15px;
  }
  
  &:hover {
    border: 1px dashed #787be8;
    color: #787be8;
    
    .svg-icon {
      color: #787be8;
    }
  }
}

.left-board {
  width: 300px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

.left-scrollbar {
  height: 100%;
  padding: 0 0 8px 0;
  overflow: hidden;
  box-sizing: border-box;
  border-right: 1px solid #f1e8e8;
}

.center-scrollbar {
  text-align: left;
  height: 100%;
  overflow: hidden;
  border-left: 1px solid #f1e8e8;
  border-right: 1px solid #f1e8e8;
  // box-sizing: border-box;
  margin-top: 16px;
}

.center-board {
  height: 100%;
  width: auto;
  margin: 0 350px 0 300px;
  // box-sizing: border-box;
  position: relative;
}

.empty-info {
  position: absolute;
  top: 33%;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 18px;
  color: #ccb1ea;
  letter-spacing: 4px;
}

.action-bar {
  position: absolute;
  right: 1rem;
  display: flex;
  flex-direction: column;
  z-index: 99;

  .delete-btn {
    color: #F56C6C;
    margin-left: 0 !important;
    margin-bottom: 16px !important;
  }
}

.logo-wrapper {
  position: relative;
  height: 42px;
  background: #fff;
  border-bottom: 1px solid #f1e8e8;
  // box-sizing: border-box;
}

.logo {
  position: absolute;
  left: 12px;
  top: 6px;
  line-height: 30px;
  color: #00afff;
  font-weight: 600;
  font-size: 17px;
  white-space: nowrap;
  
  > img {
    width: 30px;
    height: 30px;
    vertical-align: top;
  }
  
}

.center-board-row {
  padding: 12px 12px 15px 1px;
  // box-sizing: border-box;
  
  & > .el-form {
    height: calc(100vh - 69px);
  }
}

.drawing-board {
  height: 100%;
  position: relative;
  
  .components-body {
    padding: 0;
    margin: 0;
    font-size: 0;
    min-height: 60px;
  }
  
  .sortable-ghost {
    position: relative;
    display: block;
    height: 60px;
    overflow: hidden;
    background: white;
    
    &::before {
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      height: 3px;
      background: rgb(89, 89, 223);
      z-index: 2;
    }
  }
  
  .components-item.sortable-ghost {
    width: 100%;
    height: 60px;
    overflow: hidden;
    background-color: $selectedColor;
  }
  
  .active-from-item {
    & > .el-form-item {
      background: $selectedColor;
      border-radius: 6px;
      outline: 1px solid #38adff;
    }
    
    & > .drawing-item-copy,
    & > .drawing-item-delete {
      display: none;
    }
    
    & > .component-name {
      color: $lighterBlue;
    }
  }
  
  .el-form-item {
    margin-bottom: 12px !important;
    overflow: hidden;
  }
}

.drawing-item {
  position: relative;
  cursor: move;
  
  &.unfocus-bordered:not(.active-from-item) > div:first-child {
    border: 1px dashed #ccc;
  }
  
  .el-form-item {
    padding: 8px 10px;
  }
}

.drawing-row-item {
  position: relative;
  cursor: move;
  box-sizing: border-box;
  outline: 1px dashed #ccc;
  border-radius: 3px;
  margin-bottom: 15px;
  
  .drawing-row-item {
    margin-bottom: 2px;
  }
  
  .el-form-item {
    margin-bottom: 0;
  }
  
  .drag-wrapper {
    min-height: 80px;
    padding: 20px 0;
    overflow: hidden;
  }
  
  &.active-from-item {
    border: 1px dashed $lighterBlue;
  }
  
  .component-name {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    color: #bbb;
    display: inline-block;
    padding: 0 6px;
  }
}

.drawing-item,
.drawing-row-item {
  &:hover {
    & > .el-form-item {
      background: $selectedColor;
      border-radius: 6px;
      outline: 1px dashed #38adff;
    }
    
    & > .drawing-item-copy,
    & > .drawing-item-delete {
      display: initial;
      background: #38adff;
    }
  }
  
  & > .drawing-item-copy,
  & > .drawing-item-delete {
    display: none;
    position: absolute;
    top: 0px;
    width: 22px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    font-size: 18px;
    cursor: pointer;
    z-index: 1;
  }
  
  & > .drawing-item-copy {
    right: 56px;
    border-color: $lighterBlue;
    color: $lighterBlue;
    
    &:hover {
      background: $lighterBlue;
      color: #fff;
    }
  }
  
  & > .drawing-item-delete {
    right: 6px;
    border-color: #F56C6C;
    color: #fff;
  }
}