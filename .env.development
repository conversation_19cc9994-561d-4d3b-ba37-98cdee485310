# 页面标题
VITE_APP_TITLE = 神马调查管理系统plus

# 开发环境配置
VITE_APP_ENV = 'development'

# 接口域名url（包括服务）
 VITE_APP_BASE_API = 'http://************:8180'

# 接口域名url（不包括服务）
VITE_APP_URL_BASE = http://************:60

#  H5 url
VITE_H5_BASE_URL = 'http://***********:9000/#/pages'

# 地图服务地址
VITE_APP_LAYER_BASE = 'http://************:53333/geoserver/'

# 地图服务地址js
VITE_APP_ARCGIS_CONFIGJS = 'https://qjt.smgis.com/three/arcgis_js_v430_api/arcgis_js_api/javascript/4.30/init.js'

# 地图服务地址css
VITE_APP_ARCGIS_CONFIGCSS = 'https://qjt.smgis.com/three/arcgis_js_v430_api/arcgis_js_api/javascript/4.30/esri/themes/light/main.css'

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'

# 端口
VITE_APP_PORT = 80

# 接口加密功能开关(如需关闭 后端也必须对应关闭)
VITE_APP_ENCRYPT = false

# 接口加密传输 RSA 公钥与后端解密私钥对应 如更换需前后端一同更换
VITE_APP_RSA_PUBLIC_KEY = ' -----BEGIN PUBLIC KEY-----
			MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCb4IsPHLwpcixzljr9GilIZcPHuCTV/Fl1NdJ0wdJkQbs88yOxhFo5Xi5rhrfVNWUam3c6enCWpGfuG0CbQlrf7OZDRRhwzcoEi/Hd1DXjVnWui1pr1fqraDlU6gtNxVf7QeYVFGfP7yg9Q/fZ1obBsq6nZIb7LlJ3KVBDhyAQkwIDAQAB
		-----END PUBLIC KEY-----'

# 接口响应解密 RSA 私钥与后端加密公钥对应 如更换需前后端一同更换
VITE_APP_RSA_PRIVATE_KEY = ''

# websocket 开关 默认使用sse推送
VITE_APP_WEBSOCKET = false

# 公司类型 1：神马调查 2：智图资产管理系统
VITE_APP_COMPANY_TYPE = '1'

# 是否打包成离线包  用在离线地图那里
VITE_BUILD_PACKAGE_OFFLINE = false
