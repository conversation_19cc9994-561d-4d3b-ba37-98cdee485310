// import httpUtil from '@/utils/HttpUtil';
import { ElMessage } from 'element-plus';
// import { executeSelectApi } from '@/api/sqlExecuteApi';

interface CptDataForm {
  dataText: string;
  dataSource: number;
  apiUrl?: string;
  sql?: string;
  pollTime?: number;
}

export async function getDataJson(cptDataForm: CptDataForm): Promise<any> {
  let resStr: any = '{}'; // 防止JSON解析报错
  let iptStr: string = cptDataForm.dataText;

  if (cptDataForm.dataSource === 1) {
    resStr = JSON.parse(iptStr);
  } else if (cptDataForm.dataSource === 2) {
    // 调接口
    iptStr = cptDataForm.apiUrl || '';
    if (iptStr) {
      // await httpUtil.doRequest(iptStr, 'get').then(res => {
      //   resStr = res.data ? res.data : res;
      // });
    } else {
      // ElMessage.error('接口地址不能为空');
    }
  } else if (cptDataForm.dataSource === 3) {
    iptStr = cptDataForm.sql || '';
    if (iptStr) {
      const staticData = JSON.parse(cptDataForm.dataText);
      const isArray = Array.isArray(staticData);
      // await executeSelectApi({ sql: iptStr, isArray }).then(res => {
      //   resStr = res.data;
      // });
    } else {
      ElMessage.error('SQL不能为空');
    }
  }

  return resStr;
}

interface TimerMap {
  [key: string]: number;
}

const cptTimer: TimerMap = {};

export function pollingRefresh(uuid: string, cptDataForm: CptDataForm, loadData: () => void): void {
  if (uuid) {
    clearInterval(cptTimer[uuid]); // 清除旧的定时器
  }

  if (!cptDataForm) {
    ElMessage.warning('cptDataForm==>null');
    return;
  }

  if (!loadData) {
    ElMessage.warning('子组件未实现数据解析方法');
    return;
  }

  loadData();

  if (cptDataForm.pollTime && cptDataForm.pollTime !== 0) {
    // 轮询
    cptTimer[uuid] = window.setInterval(function () {
      loadData();
    }, cptDataForm.pollTime * 1000);
  }
}

export function clearCptInterval(uuid?: string, clearAll?: boolean): void {
  if (uuid) {
    clearInterval(cptTimer[uuid]); // 清除旧的定时器
  }

  if (clearAll) {
    for (const key in cptTimer) {
      clearInterval(cptTimer[key]);
    }
  }
}
