<template>
  <div style="position: relative">
    <el-form label-width="100px">
      <el-form-item label="表头背景色">
        <el-color-picker v-model="attributeCopy.headerBGC" show-alpha />
      </el-form-item>
      <el-form-item label="奇数行背景色">
        <el-color-picker v-model="attributeCopy.oddRowBGC" show-alpha />
      </el-form-item>
      <el-form-item label="偶数行背景色">
        <el-color-picker v-model="attributeCopy.evenRowBGC" show-alpha />
      </el-form-item>
      <el-form-item label="显示行数">
        <el-input-number v-model="attributeCopy.rowNum" :min="1" :max="60" />
      </el-form-item>
      <el-form-item label="轮播时间间隔ms">
        <el-input-number v-model="attributeCopy.waitTime" :min="200" :max="20000" :step="500" />
      </el-form-item>
      <el-form-item label="表头高度">
        <el-input-number v-model="attributeCopy.headerHeight" :min="5" :max="1000" />
      </el-form-item>
      <el-form-item label="行号表头">
        <el-input v-model="attributeCopy.indexHeader" />
      </el-form-item>
      <el-form-item label="滚动方式">
        <el-select v-model="attributeCopy.carousel" placeholder="请选择">
          <el-option label="单列滚动" value="single" />
          <el-option label="全表滚动" value="page" />
        </el-select>
      </el-form-item>
      <el-form-item label="表头">
        <el-input type="textarea" v-model="attributeCopy.columns" :rows="10" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'cpt-dataV-scrollTable-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);

// --- 定义变量 ---
const columns = ref([]);
onMounted(() => {
  if (props.attribute.columns) {
    columns.value = JSON.parse(props.attribute.columns);
  }
});
</script>

<style lang="scss" scoped>
.th-box {
  margin-top: -10px;
  margin-left: 5px;
  .coumns-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    .item {
      flex: 1;
    }
    .end {
      width: 50px;
    }
    .end-ico {
      width: 32px;
    }
  }
}
</style>
