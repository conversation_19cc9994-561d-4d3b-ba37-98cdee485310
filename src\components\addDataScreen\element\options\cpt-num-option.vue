<template>
  <el-form labelWidth="90px">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.title" />
    </el-form-item>
    <el-form-item label="数值大小">
      <el-input-number :min="13" :max="200" v-model="attributeCopy.numSize" />
    </el-form-item>
    <el-form-item label="数值颜色">
      <el-color-picker v-model="attributeCopy.numColor" />
    </el-form-item>
    <el-form-item label="数值行高">
      <el-input-number :min="10" :max="200" v-model="attributeCopy.numHeight" />
    </el-form-item>
    <el-form-item label="单位">
      <el-input v-model="attributeCopy.unit" placeholder="请输入单位" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-num-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>

<style scoped></style>
