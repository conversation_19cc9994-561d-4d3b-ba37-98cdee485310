<!--  -->
<template>
  <div class="chooseField-main">
    <el-cascader v-model="selectedValue" :options="treeList" @change="changeField" style="width: 100%"></el-cascader>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, ref } from 'vue';
import type { CascaderValue } from 'element-plus';

// 定义props
const props = defineProps<{
  defaultField?: any;
  treeList: any;
  num: number;
  isQuestion?: boolean;
}>();

// 存储选中的值
const selectedValue = ref<CascaderValue>([]);

// 定义emit
const emit = defineEmits<{
  (e: 'editField', field: any, num: number, specialField: any | null, parentField: any): void;
}>();

const changeField = (val: CascaderValue) => {
  // 确保val是数组
  const valArray = Array.isArray(val) ? val : [val];

  if (valArray.length == 3) {
    //代表是正常的字段
    emit('editField', valArray[2], props.num, null, valArray[1]);
  } else if (valArray.length == 4) {
    //代表是特殊的
    const valueMethod = JSON.parse(String(valArray[2])).valueMethod;
    if (valueMethod == 'idCardScan' || valueMethod == 'xtdwsb' || valueMethod == 'xtzwsb') {
      emit('editField', valArray[2], props.num, valArray[3], valArray[1]);
    } else if (valueMethod == 'xttable') {
      emit('editField', valArray[3], props.num, null, valArray[1]);
    }
  }
};
</script>

<style lang="scss" scoped>
.chooseField-main {
  width: 100%;
  height: 100%;
}
</style>
