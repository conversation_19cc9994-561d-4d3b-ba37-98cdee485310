import { to } from 'await-to-js';
import { getToken, removeToken, setToken } from '@/utils/auth';
import { login as loginApi, logout as logoutApi, getInfo as getUserInfo, loginMessage as loginMessageApi } from '@/api/login/index';
import { LoginData } from '@/api/types';

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken());
  const name = ref('');
  const nickname = ref('');
  const avatar = ref('');
  const roles = ref<Array<string>>([]); // 用户角色编码集合 → 判断路由权限
  const permissions = ref<Array<string>>([]); // 用户权限编码集合 → 判断按钮权限
  const vipType = ref(1); // 公司类型默认1 个人
  const isExpired = ref(1); // 是否过期 1没过期 2过期
  const user = ref();

  // 判断是否为超级管理员
  const isAdmin = computed(() => {
    return roles.value.includes('admin');
  });

  /**
   * 登录
   * @param userInfo
   * @returns
   */
  const login = async (userInfo: LoginData): Promise<void> => {
    const [err, res] = await to(loginApi(userInfo.username, userInfo.password, userInfo.captcha, userInfo.uuid, userInfo.companyId));
    if (res) {
      const data = res.data;
      setToken(data.access_token);
      token.value = data.access_token;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 获取用户信息
  const getInfo = async (): Promise<any> => {
    const [err, res] = await to(getUserInfo());
    if (res) {
      const data = res.data;
      const userData = data.user;
      user.value = userData;

      if (data.roles && data.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        roles.value = data.roles;
        permissions.value = data.permissions;
      } else {
        roles.value = ['ROLE_DEFAULT'];
      }
      name.value = userData.userName;
      nickname.value = userData.nickName;
      avatar.value = userData.avatar;
      vipType.value = res['vipType'];
      const timestamp = new Date().getTime();
      res.data.user['expireTime'] > timestamp ? (isExpired.value = 1) : (isExpired.value = 2);
      return Promise.resolve(res);
    }
    return Promise.reject(err);
  };

  const loginMessage = async (userInfo: any) => {
    const username = userInfo.username;
    const captcha = userInfo.captcha;
    const uuid = userInfo.uuid;
    const companyId = userInfo.companyId;

    const [err, res] = await to(loginMessageApi(captcha, uuid, companyId, username));

    if (res) {
      setToken(res.data['access_token']);
      token.value = res.data['access_token'];
      token.value = res['token'];
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 注销
  const logout = async (): Promise<void> => {
    await logoutApi();
    token.value = '';
    roles.value = [];
    permissions.value = [];
    removeToken();
  };

  const setAvatar = (value: string) => {
    avatar.value = value;
  };

  return {
    token,
    nickname,
    avatar,
    roles,
    permissions,
    user,
    vipType,
    isExpired,
    isAdmin,
    login,
    getInfo,
    loginMessage,
    logout,
    setAvatar
  };
});
