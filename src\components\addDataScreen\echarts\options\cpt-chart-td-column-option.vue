<template>
  <div>
    <el-form label-width="100px">
      <el-form-item label="标题">
        <el-input v-model="attributeCopy.chartTitle" />
      </el-form-item>
      <el-form-item label="标题位置(左)">
        <el-input v-model="attributeCopy.titleLeft" />
      </el-form-item>
      <el-form-item label="标题位置(上)">
        <el-input v-model="attributeCopy.titleTop" />
      </el-form-item>
      <el-form-item label="标题颜色">
        <el-color-picker v-model="attributeCopy.titleTextColor" show-alpha />
      </el-form-item>
      <el-form-item label="x轴">
        <el-switch v-model="attributeCopy.xAxisShow" active-text="开" inactive-text="关" />
      </el-form-item>
      <div v-show="attributeCopy.xAxisShow">
        <el-form-item label="x轴线显示">
          <el-switch v-model="attributeCopy.xLineShow" active-text="开" inactive-text="关" />
        </el-form-item>
        <el-form-item label="x轴线颜色">
          <el-color-picker v-model="attributeCopy.xLineColor" show-alpha />
        </el-form-item>
        <el-form-item label="x轴刻度线">
          <el-switch v-model="attributeCopy.xTickShow" active-text="开" inactive-text="关" />
        </el-form-item>
        <el-form-item label="x轴字体颜色">
          <el-color-picker v-model="attributeCopy.xLabelColor" show-alpha />
        </el-form-item>
        <el-form-item label="x轴字体倾斜">
          <el-slider v-model="attributeCopy.xFontRotate" :min="-180" :max="180" />
        </el-form-item>
      </div>
      <el-form-item label="y轴">
        <el-switch v-model="attributeCopy.yAxisShow" active-text="开" inactive-text="关" />
      </el-form-item>
      <div v-show="attributeCopy.yAxisShow">
        <el-form-item label="y轴线显示">
          <el-switch v-model="attributeCopy.yLineShow" active-text="开" inactive-text="关" />
        </el-form-item>
        <el-form-item label="y轴颜色">
          <el-color-picker v-model="attributeCopy.yLineColor" show-alpha />
        </el-form-item>
        <el-form-item label="y轴网格线">
          <el-switch v-model="attributeCopy.yGridLineShow" active-text="开" inactive-text="关" />
        </el-form-item>
        <el-form-item label="y轴刻度线">
          <el-switch v-model="attributeCopy.yTickShow" active-text="开" inactive-text="关" />
        </el-form-item>
        <el-form-item label="y轴字体颜色">
          <el-color-picker v-model="attributeCopy.yLabelColor" show-alpha />
        </el-form-item>
      </div>
      <el-form-item label="颜色渐变">
        <el-switch v-model="attributeCopy.gradualColor" active-text="开" inactive-text="关" />
      </el-form-item>
      <div v-if="attributeCopy.gradualColor">
        <el-form-item label="柱顶颜色">
          <el-color-picker v-model="attributeCopy.barColor1" show-alpha />
        </el-form-item>
        <el-form-item label="柱中颜色">
          <el-color-picker v-model="attributeCopy.barColor2" show-alpha />
        </el-form-item>
        <el-form-item label="柱底颜色">
          <el-color-picker v-model="attributeCopy.barColor3" show-alpha />
        </el-form-item>
      </div>
      <div v-else>
        <el-form-item label="柱体颜色">
          <el-color-picker v-model="attributeCopy.barColor" show-alpha />
        </el-form-item>
      </div>
      <el-form-item label="柱体背景" v-show="attribute.barType === 'bar'">
        <el-switch v-model="attributeCopy.barBgShow" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="柱体宽度">
        <el-input-number v-model="attributeCopy.barWidth" :min="1" :max="100" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-td-column-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>

<style scoped></style>
