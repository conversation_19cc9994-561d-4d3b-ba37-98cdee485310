import copyText from './common/copyText';
import { hasPermi, hasRoles } from './permission';
import vTranslate from './translate/index';
import vTranslateModal from './common/tanslateModal';
import dialogDrag from './common/dialogDrag';
import { App } from 'vue';

export default (app: App) => {
  app.directive('copyText', copyText);
  app.directive('hasPermi', hasPermi);
  app.directive('hasRoles', hasRoles);
  app.directive('translate', vTranslate);
  app.directive('translateModal', vTranslateModal);
  app.directive('dialogDrag', dialogDrag);
};
