import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ApplyData, ApplyQuery } from '@/api/apply/types';

/**
 * 获取邀请客户的列表（添加申请）
 * @param data 申请数据
 * @returns {AxiosPromise}
 */
export function addApply(data: ApplyData): AxiosPromise<any> {
  return request({
    url: '/system/company/addApply',
    method: 'post',
    data: data
  });
}

/**
 * 查询申请公司的列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getApplyList(params?: ApplyQuery): AxiosPromise<any> {
  return request({
    url: '/system/company/applyList',
    method: 'get',
    params: params
  });
}

/**
 * 处理申请公司 同意/拒绝
 * @param data 处理数据
 * @returns {AxiosPromise}
 */
export function updateApply(data: ApplyData): AxiosPromise<any> {
  return request({
    url: '/system/company/updateApply',
    method: 'post',
    data: data
  });
}
