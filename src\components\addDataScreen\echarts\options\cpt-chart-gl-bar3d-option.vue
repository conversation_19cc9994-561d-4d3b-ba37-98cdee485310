<template>
  <el-form label-width="100px" size="mini">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-gl-bar3d-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
</script>

<style scoped></style>
