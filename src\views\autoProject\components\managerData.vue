<!-- 管理宗地 -->
<template>
  <div class="managerData-main">
    <el-dialog
        title="数据管理"
        :visible.sync="managerDialog"
        width="800px"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        :append-to-body="true"
        :before-close="handleClose">
        <span>这是一段信息</span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="managerDialog = false">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
    };
  },
  props:['managerDialog'],

  components: {},

  computed: {},

  mounted() {},

  methods: {
    handleClose(){
        this.$emit('changeMange',false)
    }
  }
}

</script>
<style lang='scss' scoped>
.managerData-main{

}
</style>