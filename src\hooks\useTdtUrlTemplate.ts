import { computed } from 'vue';

/**
 * 天地图urlTemplate hook
 * @param type 'img' | 'vec' | 'cia' | 'cva'
 * @returns { urlTemplate, isOffline }
 */
export function useTdtUrlTemplate(type: 'img' | 'vec' | 'cia' | 'cva') {
  // 读取环境变量，vite下环境变量均为字符串
  const isOffline = import.meta.env.VITE_BUILD_PACKAGE_OFFLINE === 'true';

  // 离线和在线的url模板
  const urlTemplate = computed(() => {
    if (isOffline) {
      // 离线包路径（根据实际目录结构调整）
      switch (type) {
        case 'img':
          return '/tiles/ying_xiang/{z}/{x}/{y}.png';
        case 'vec':
          return '/tiles/shi_liang/{z}/{x}/{y}.png';
        case 'cia':
          return '/tiles/ying_xiang_anno/{z}/{x}/{y}.png';
        case 'cva':
          return '/tiles/shi_liang_anno/{z}/{x}/{y}.png';
        default:
          return '';
      }
    } else {
      // 在线天地图服务（token请替换为你的变量）
      const token = import.meta.env.VITE_APP_TDT_TOKEN || 'your_token';
      switch (type) {
        case 'img':
          return `https://t{0-7}.tianditu.gov.cn/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=${token}`;
        case 'vec':
          return `https://t{0-7}.tianditu.gov.cn/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=${token}`;
        case 'cia':
          return `https://t{0-7}.tianditu.gov.cn/DataServer?T=cia_w/wmts&x={col}&y={row}&l={level}&tk=${token}`;
        case 'cva':
          return `https://t{0-7}.tianditu.gov.cn/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=${token}`;
        default:
          return '';
      }
    }
  });

  return {
    urlTemplate,
    isOffline
  };
}
