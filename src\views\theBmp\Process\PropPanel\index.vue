<template>
  <el-drawer v-model="visible" size="550px" class="drawer" :show-close="false" style="text-align: left" @close="cancel" v-if="properties">
    <!-- 标题 -->
    <template #header>
      <header class="header" v-if="value?.type === 'start'">
        {{ value?.type === 'start' ? '发起节点' : properties?.title }}
      </header>
      <header class="header" v-else>
        <span>{{ properties?.title }}</span>
      </header>
    </template>

    <!-- 条件分支的选择情况 -->
    <section class="condition-pane" v-if="value && isConditionNode()">
      <div class="condition-pane-group" v-for="(ite, outIndex) in nodeConditions" :key="outIndex">
        <div @click="handleDeleteConditionGroup(ite, outIndex)" title="删除条件组" class="group-delete">
          <el-icon><Delete /></el-icon>
        </div>
        <div v-for="(item, index) in ite" :key="index">
          <!-- 发起人 -->
          <row-wrapper title="发起人" v-if="item.formId === -1">
            <fc-org-select ref="condition-org" :type="'user'" v-model="item.initiator" />
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper>
          <!-- 计数 -->
          <row-wrapper :key="index" :title="item.label" v-if="couldShowIt(item, 'el-input-number')">
            <num-input
              :key="index"
              :title="timeTangeLabel(item)"
              :value="item.conditionValue"
              @update="(val) => (item.conditionValue = val)"
              style="padding-right: 6px"
            />
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper>
          <!-- 单选组 -->
          <row-wrapper :key="index" :title="item.label" v-if="couldShowIt(item, 'el-radio-group')">
            <el-checkbox-group v-model="item.conditionValue">
              <el-checkbox v-for="ite in item.options" :value="ite.value" :key="ite.value">{{ ite.label }}</el-checkbox>
            </el-checkbox-group>
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper>
          <!-- 多选框 -->
          <row-wrapper :key="index" :title="item.label" v-if="couldShowIt(item, 'el-checkbox-group')">
            <div style="display: flex">
              <el-select v-model="item.matchType" placeholder="请选择" size="small">
                <el-option v-for="item in checkConditionList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select v-model="item.conditionValue" placeholder="请选择" size="small" multiple>
                <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value" />
              </el-select>
            </div>
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper>
          <!-- 下拉 -->
          <row-wrapper :key="index" :title="item.label" v-if="couldShowIt(item, 'el-select')">
            <el-select v-model="item.conditionValue" placeholder="请选择" size="small">
              <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper>
          <!-- 组织机构 -->
          <!-- <row-wrapper :key="index" :title="item.label" v-if="couldShowIt(item, 'fc-org-select')">
            <fc-org-select v-model="item.conditionValue" :ref="'org' + index" :type="'dep'" />
            <template #action>
              <el-icon @click="handleDeleteCondition(item, outIndex, index)" title="删除条件">
                <Delete />
              </el-icon>
            </template>
          </row-wrapper> -->
          <div v-if="index !== ite.length - 1" class="group-and">且</div>
        </div>
        <div style="padding-left: 10px; margin-top: 2em">
          <el-button type="primary" size="small" :icon="Plus" @click="handleAddCondition(ite, outIndex)" :disabled="notUseConNum(ite) === 0">
            添加条件
          </el-button>
          <span style="color: #aaa; margin-left: 16px">还有{{ notUseConNum(ite) }}个可用条件</span>
        </div>
        <div v-if="outIndex !== nodeConditions.length - 1" class="group-or">或</div>
      </div>
      <div style="padding-left: 10px; margin-top: 2em">
        <el-button type="primary" size="small" :icon="Plus" @click="handleAddConditionGroup">添加条件组</el-button>
      </div>
    </section>

    <!-- 审批人 -->
    <section class="approver-pane" style="height: 100%" v-if="value && (isApproverNode() || isStartNode())">
      <el-tabs v-model="activeName" class="pane-tab">
        <el-tab-pane :label="'设置' + (value.type === 'approver' ? '审批人' : '发起人')" name="config">
          <!-- 开始节点 -->
          <el-row style="padding: 10px" v-if="value.type === 'start'">
            <el-col :span="4" style="font-size: 12px">发起人</el-col>
            <el-col :span="18" style="padding-left: 12px">
              <fc-org-select ref="start-org" :type="'user'" v-model="initiator" />
            </el-col>
          </el-row>

          <div v-else-if="value.type === 'approver'">
            <div style="padding: 12px">
              <el-radio-group v-model="approverForm.actionRuleType" style="line-height: 32px" @change="resetOrgColl">
                <el-radio v-for="item in assigneeTypeOptions" :value="item.value" :key="item.value" class="radio-item">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </div>
            <div style="border-bottom: 1px solid #e5e5e5; padding-bottom: 1rem">
              <div v-if="approverForm.actionRuleType === 'target_originator'" class="option-box" style="color: #a5a5a5">
                发起人自己将作为审批人处理审批单
              </div>
              <!-- 这里是发起人自选的内容 -->
              <div v-else-if="approverForm.actionRuleType === 'target_select'" class="option-box">
                <p>可选多人</p>
                <el-switch v-model="approverForm.optionalMultiUser" active-color="#13ce66" />
                <p>选择范围</p>
                <el-select v-model="approverForm.select" size="small" @change="optionalRangeChange" style="width: 100%">
                  <el-option v-for="(item, index) in rangeOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled" />
                </el-select>
                <div style="margin-top: 10px" v-if="approverForm.select != 'allStaff'">
                  <!-- 这里是非全公司的额外选择人，但是目前项目中没有做到这一步 -->
                  <fc-org-select
                    ref="approver-org"
                    buttonType="button"
                    v-model="orgCollection"
                    :title="getOptionalAssignTypeLabel()"
                    :type="'user'"
                    @change="onOrgChange"
                    :maxNum="approverForm.optionalMultiUser ? 99 : 1"
                  />
                </div>
              </div>
              <!-- 这里时主管 -->
              <!-- <div v-else-if="approverForm.actionRuleType === 'director'">
                <div style="font-size: 14px; padding-left: 1rem">
                  发起人的
                  <el-select v-model="directorLevel" size="small">
                    <el-option v-for="item in 5" :key="item" :label="item === 1 ? '直接主管' : `第${item}级主管`" :value="item" />
                  </el-select>
                  <br />
                  <el-checkbox v-model="useDirectorProxy" style="margin-top: 1rem">找不到主管时，由上级主管代审批</el-checkbox>
                </div>
              </div> -->
              <div v-else class="option-box">
                <fc-org-select
                  ref="orgApprovelRef"
                  buttonType="button"
                  v-model="orgCollection"
                  :title="getAssignTypeLabel()"
                  :type="'user'"
                  @change="onOrgChange"
                />
              </div>
            </div>
            <div class="option-box" style="border-bottom: 1px solid #e5e5e5" v-if="isShowCounterSignSelect">
              <p>多人审批时采用的审批方式</p>
              <el-radio v-model="approverForm.actType" :value="'or'" class="radio-item">或签（一名审批人同意或拒绝即可）</el-radio>
              <br />
              <el-radio v-model="approverForm.actType" :value="'and'" class="radio-item">会签（须所有审批人同意）</el-radio>
            </div>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane label="表单权限" name="formAuth" v-if="isOnlineForm">
          <div class="form-auth-table">
            <header class="auth-table-header">
              <div class="row">
                <div class="label">表单字段</div>
                <el-radio-group v-model="globalFormOperate" class="radio-group" @change="changeAllFormOperate">
                  <el-radio :label="2" style="margin-left: 1rem">可编辑</el-radio>
                  <el-radio :label="1">只读</el-radio>
                  <el-radio :label="0">隐藏</el-radio>
                </el-radio-group>
              </div>
            </header>
            <div class="auth-table-body">
              <div v-for="item in formOperates" :key="item.formId" class="row">
                <div class="label">
                  <span class="required" v-show="item.required">*</span>
                  {{ item.label }}
                </div>
                <el-radio-group v-model="item.formOperate" class="radio-group">
                  <el-radio :label="2" style="margin-left: 1rem"><span style="opacity: 0">可编辑</span></el-radio>
                  <el-radio :label="1"><span style="opacity: 0">只读</span></el-radio>
                  <el-radio :label="0"><span style="opacity: 0">隐藏</span></el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </el-tab-pane> -->
      </el-tabs>
    </section>

    <!-- 抄送人 -->
    <section v-if="value && isCopyNode()" style="padding-left: 1rem">
      <p>抄送人</p>
      <fc-org-select ref="orgApprovelRef" v-model="properties.approvals" :type="'user'" buttonType="button" title="抄送人" />
      <br />
      <el-checkbox v-model="properties.userOptional">允许发起人自选抄送人</el-checkbox>
    </section>

    <!-- 办理人 -->
    <section v-if="value && isAuditNode()" style="padding-left: 1rem">
      <p>办理人</p>
      <fc-org-select ref="orgApprovelRef" v-model="properties.approvals" :type="'user'" buttonType="button" title="办理人" />
      <div class="option-box" style="border-bottom: 1px solid #e5e5e5" v-if="isShowAuditSignSelect">
        <p>多人审批时采用的审批方式</p>
        <el-radio v-model="properties.actType" :value="'or'" class="radio-item">或签（一名审批人同意或拒绝即可）</el-radio>
        <br />
        <el-radio v-model="properties.actType" :value="'and'" class="radio-item">会签（须所有审批人同意）</el-radio>
      </div>
      <br />
    </section>

    <!-- 添加分支的时候 添加到流程节点中的数据 -->
    <el-dialog v-model="dialogVisible" title="选择条件" width="500px" top="30vh" :append-to-body="true" custom-class="condition-dialog">
      <el-checkbox-group v-model="showingPCons">
        <el-checkbox :value="-1">发起人</el-checkbox>
        <el-checkbox v-for="(item, index) in pconditions" :key="index" :value="item.formId" @change="handleConditionChecked($event, item)">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" size="small" @click="handleConfirmCondition">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="actions" style="text-align: center; border-top: 1px solid #eee">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Delete, Plus } from '@element-plus/icons-vue';
import { useFlowStore } from '@/store/modules/flow';
import RowWrapper from './RowWrapper.vue';
import NumInput from './NumInput.vue';
import FcOrgSelect from '../../FormControls/OrgSelect/index.vue';
import { NodeUtils } from '../FlowCard/util';

// 类型定义
interface RangeOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface ApproverForm {
  approvals: any[];
  actionRuleType: string;
  formOperates: any[];
  actType: string;
  optionalMultiUser: boolean;
  select: string;
}

interface Properties {
  approvals: any[];
  actType: string;
  conditions: any[];
  initiator?: any[];
  formOperates?: any[];
  userOptional?: boolean;
}

interface ConditionItem {
  formId: number;
  label: string;
  tag: string;
  conditionValue: any;
  paramKey?: string;
  initiator?: any[];
  options?: any[];
  matchType?: string;
  conditionNumValue?: any;
  vModel?: string;
}

interface NodeCondition {
  paramKey: string;
  formId: number;
  initiator: any[];
  label: string;
  conditionValue: any;
  matchType?: string;
  conditionNumValue?: any;
  paramValues?: any[];
}

// Props定义
const props = defineProps<{
  value: any;
  processData: any;
  isOnlineForm: boolean;
}>();

// Emits定义
const emit = defineEmits<{
  (e: 'cancel'): void;
  (e: 'confirm', properties: Properties, content: string): void;
}>();

// Store
const flowStore = useFlowStore();

// 常量定义
const rangeType = {
  lt: '<',
  lte: '≤',
  gt: '>',
  gte: '≥',
  eq: '='
};

const defaultApproverForm: ApproverForm = {
  approvals: [],
  actionRuleType: 'target_select',
  formOperates: [],
  actType: 'normal',
  optionalMultiUser: false,
  select: 'allStaff'
};

// Refs
const visible = ref(false);
const globalFormOperate = ref<number>(2);
const titleInputVisible = ref(false);
const activeName = ref('config');
const showingPCons = ref<number[]>([]);
const pconditions = ref<any[]>([]);
const dialogVisible = ref(false);
const properties = ref<Properties>({
  approvals: [],
  actType: 'normal',
  conditions: []
});
const initiator = ref<any[]>([]);
const priorityLength = ref(0);
const orgCollection = ref<any[]>([]);
const useDirectorProxy = ref(true);
const directorLevel = ref(1);
const startForm = ref({ formOperates: [] });
const approverForm = ref<ApproverForm>(JSON.parse(JSON.stringify(defaultApproverForm)));
const nodeConditions = ref<any[][]>([[]]);
const outConditionIndex = ref(0);
const formOperates = ref<any[]>([]);
const orgApprovelRef = ref();

// 选项数据
const optionalOptions = [
  { label: '自选一个人', value: false },
  { label: '自选多个人', value: true }
];

const rangeOptions: RangeOption[] = [{ label: '全公司', value: 'allStaff' }];

const assigneeTypeOptions = [
  { label: '发起人自选', value: 'target_select' },
  { label: '指定成员', value: 'target_approval' },
  { label: '发起人自己', value: 'target_originator' }
];

const checkConditionList = [
  { label: '任意', value: 'any' },
  { label: '完全等于', value: 'all' }
];

// Computed
const notUseConNum = computed(() => {
  return (ite: any[]) => {
    const resultNumber = pconditions.value.length - ite.length + 1;
    return resultNumber <= 0 ? 0 : resultNumber;
  };
});

const isShowCounterSignSelect = computed(() => {
  if (approverForm.value.actionRuleType === 'target_originator') {
    return orgCollection.value?.length > 1;
  } else if (approverForm.value.actionRuleType === 'target_approval') {
    if (orgCollection.value?.length > 1) {
      if (approverForm.value.actType === 'normal') {
        approverForm.value.actType = 'or';
      }
      return true;
    }
  }
  return false;
});

const isShowAuditSignSelect = computed(() => {
  if (properties.value.approvals.length > 1) {
    if (properties.value.actType === 'normal') {
      properties.value.actType = 'or';
    }
  }
  return properties.value.approvals.length > 1;
});

// Methods
const isConditionNode = () => {
  return props.value ? NodeUtils.isConditionNode(props.value) : false;
};

const isApproverNode = () => {
  return props.value ? NodeUtils.isApproverNode(props.value) : false;
};

const isStartNode = () => {
  return props.value ? NodeUtils.isStartNode(props.value) : false;
};

const isCopyNode = () => {
  return props.value ? NodeUtils.isCopyNode(props.value) : false;
};

const isAuditNode = () => {
  return props.value ? NodeUtils.isAuditNode(props.value) : false;
};

const resetOrgColl = (val: string) => {
  orgCollection.value = [];
};

const onOrgChange = (data: any) => {
  // 实现组织变更逻辑
};

const timeTangeLabel = (item: any) => {
  const index = ['fc-time-duration', 'fc-date-duration'].findIndex((t) => t === item.tag);
  if (index > -1) {
    return '时长' + ['(小时)', '(天)'][index];
  }
  return item.label;
};

const getAssignTypeLabel = () => {
  const res = assigneeTypeOptions.find((t) => t.value === approverForm.value.actionRuleType);
  return res ? res.label : '';
};

const getOptionalAssignTypeLabel = () => {
  const res = rangeOptions.find((t) => t.value === approverForm.value.select);
  return res ? res.label : '';
};

const changeAllFormOperate = (val: number) => {
  const target = isStartNode() ? startForm.value : approverForm.value;
  target.formOperates.forEach((t) => (t.formOperate = val));
};

const couldShowIt = (item: any, ...tag: string[]) => {
  return tag.includes(item.tag);
};

// 初始化方法
const initFormOperates = (target: any) => {
  const formOperates = (target.properties && target.properties.formOperates) || [];
  const res: any[] = [];
  const defaultType = isApproverNode() ? 1 : 2;

  const getPermissionById = (id: number) => {
    const permission = formOperates.find((t) => t.formId === id);
    return permission !== undefined ? permission.formOperate : defaultType;
  };

  const format = (list: any[], parentName = '') => {
    list.forEach((t) => {
      const data = {
        formId: t.formId,
        required: t.required,
        label: parentName ? [parentName, t.label].join('.') : t.label,
        formOperate: getPermissionById(t.formId)
      };
      res.push(data);
      Array.isArray(t.children) && format(t.children, t.label);
    });
  };

  const formItems = flowStore.formItemList.filter((t) => t.cmpType !== 'custom');
  format(formItems);
  return res;
};

// 确认方法
const confirm = () => {
  if (isCopyNode()) {
    copyNodeConfirm();
  } else if (isStartNode()) {
    startNodeComfirm();
  } else if (isApproverNode()) {
    approverNodeComfirm();
  } else if (isConditionNode()) {
    conditionNodeComfirm();
  } else if (isAuditNode()) {
    auditNodeConfim();
  }
};

const cancel = () => {
  setTimeout(async () => {
    emit('cancel');
    visible.value = false;
  }, 0);
};

// Watch
watch(
  () => visible.value,
  (val) => {
    if (!val) {
      approverForm.value = JSON.parse(JSON.stringify(defaultApproverForm));
      return;
    }
    if (props.processData.properties) {
      props.processData.properties.formOperates = initFormOperates(props.processData).map((t) => ({ formId: t.formId, formOperate: t.formOperate }));
    }
    isStartNode() && initStartNodeData();
    isApproverNode() && initApproverNodeData();
    isConditionNode() && initConditionNodeData();
  }
);

watch(
  () => props.value,
  (newVal) => {
    if (newVal && newVal?.properties) {
      visible.value = true;
      properties.value = JSON.parse(JSON.stringify(newVal.properties));
      if (properties.value) {
        NodeUtils.isConditionNode(newVal) && getPriorityLength();
      }
    }
  }
);

// 方法实现
const handleDeleteCondition = (condition: ConditionItem, outIndex: number, innerIndex: number) => {
  nodeConditions.value[outIndex].splice(innerIndex, 1);
};

const handleDeleteConditionGroup = (item: any[], outIndex: number) => {
  nodeConditions.value.splice(outIndex, 1);
};

const getPrevData = () => {
  return NodeUtils.getPreviousNode(props.value.prevId, props.processData);
};

const getPriorityLength = () => {
  const prevData = getPrevData();
  if (prevData?.conditionNodes) {
    priorityLength.value = prevData.conditionNodes.length;
  }
};

const initInitiator = () => {
  debugger;
  const initiator = props.value.properties?.initiator;
  initiator.value = Array.isArray(initiator) ? initiator : [];
};

// 初始化开始节点数据
const initStartNodeData = (): void => {
  // initInitiator();
  startForm.value.formOperates = initFormOperates(props.value);
};

const initApproverNodeData = () => {
  for (const key in approverForm.value) {
    if (props.value.properties.hasOwnProperty(key)) {
      approverForm.value[key as keyof ApproverForm] = props.value.properties[key];
    }
  }
  const approvals = approverForm.value.approvals;
  resetOrgColl('');
  if (Array.isArray(approverForm.value.approvals)) {
    if (approverForm.value.actionRuleType === 'target_select') {
      orgCollection.value = approvals;
    } else {
      orgCollection.value = approvals;
    }
  }
  approverForm.value.formOperates = initFormOperates(props.value);
};

const initConditionNodeData = () => {
  const nodeConditions = props.value.properties?.conditions;
  nodeConditions.value = props.value.properties?.conditions;
  pconditions.value = JSON.parse(JSON.stringify(flowStore.processConditions));
  initiator.value = props.value.properties.initiator;

  if (Array.isArray(pconditions.value) && pconditions.value.length > 0) {
    let temp = {};
    showingPCons.value = []; // 默认显示发起人
    pconditions.value.forEach((t) => {
      if (Array.isArray(nodeConditions)) {
        nodeConditions.forEach((nodes) => {
          nodes.forEach((item: NodeCondition) => {
            if (item.paramKey === 'starterInfo') {
              item.formId = -1;
              item.initiator = item.initiator;
            } else {
              if (item.formId === t.formId) {
                if (t.tag === 'el-radio-group') {
                  item.label = t.label;
                  item.conditionValue = item.paramValues;
                } else if (t.tag === 'el-checkbox-group') {
                  item.label = t.label;
                  item.conditionNumValue = item.conditionNumValue;
                  item.conditionValue = item.paramValues;
                } else if (t.tag === 'el-input-number') {
                  let conditionType = item.matchType;
                  if (conditionType?.indexOf('bet') > 0) {
                    conditionType = 'bet';
                  }
                  item.label = t.label;
                  temp = {
                    conditionNumValue: item.conditionNumValue,
                    matchType: conditionType
                  };
                  item.conditionValue = temp;
                } else {
                  item.conditionValue = initiator.value;
                }
              }
            }
          });
        });
      }
    });
  } else {
    const hasStarterInfo = nodeConditions.some((nodes) => nodes.some((item) => item.paramKey === 'starterInfo'));
    if (hasStarterInfo) {
      nodeConditions.value.forEach((nodes) => {
        nodes.forEach((item) => {
          item.formId = -1;
          item.initiator = item.initiator;
        });
      });
    }
  }
};

const optionalRangeChange = () => {
  orgCollection.value = [];
};

const handleConditionChecked = (checked: boolean, item: ConditionItem) => {
  if (checked) {
    item.conditionValue = [];
  }
};

const handleAddConditionGroup = () => {
  nodeConditions.value.push([]);
};

const handleAddCondition = (ite: any[], outIndex: number) => {
  showingPCons.value = [];
  const innerConditionList = nodeConditions.value[outIndex] || [];
  if (innerConditionList.length > 0) {
    showingPCons.value = innerConditionList.map((item: any) => item.formId);
  }
  dialogVisible.value = true;
  outConditionIndex.value = outIndex;
};

const handleConfirmCondition = () => {
  const list = JSON.parse(JSON.stringify(nodeConditions.value[outConditionIndex.value]));
  const hasAlreadyList = list.map((item: any) => item.formId);
  const diff1 = hasAlreadyList.filter((it: number) => !showingPCons.value.includes(it));
  const diff2 = showingPCons.value.filter((it: number) => !hasAlreadyList.includes(it));
  const differentList = [...diff1, ...diff2];

  pconditions.value
    .filter((p: any) => differentList.includes(p.formId))
    .forEach((selectItem: any) => {
      nodeConditions.value[outConditionIndex.value].push(JSON.parse(JSON.stringify(selectItem)));
    });

  if (differentList.includes(-1)) {
    nodeConditions.value[outConditionIndex.value].unshift({
      formId: -1,
      initiator: []
    });
  }
  dialogVisible.value = false;
};

// 确认方法实现
const copyNodeConfirm = () => {
  emit('confirm', properties.value, getOrgSelectLabel('notifier') || '发起人自选');
  visible.value = false;
};

const startNodeComfirm = () => {
  properties.value.initiator = initiator.value;
  const formOperates = startForm.value.formOperates.map((t) => ({
    formId: t.formId,
    formOperate: t.formOperate
  }));
  Object.assign(properties.value, { formOperates });
  emit('confirm', properties.value, getOrgSelectLabel('start') || '所有人');
  visible.value = false;
};

const approverNodeComfirm = () => {
  const assigneeType = approverForm.value.actionRuleType;
  let content = '';
  if (['target_originator'].includes(assigneeType)) {
    content = assigneeTypeOptions.find((t) => t.value === assigneeType)?.label || '';
  } else if (assigneeType === 'target_approval') {
    content = getOrgSelectLabel('approver');
  } else {
    content = '发起人自选';
  }

  const formOperates = approverForm.value.formOperates.map((t) => ({
    formId: t.formId,
    formOperate: t.formOperate
  }));

  if (assigneeType === 'target_select') {
    approverForm.value.approvals = orgCollection.value;
  } else {
    approverForm.value.approvals = orgCollection.value;
  }

  Object.assign(properties.value, approverForm.value, { formOperates });
  emit('confirm', properties.value, content || '请设置审批人');
  visible.value = false;
};

const auditNodeConfim = () => {
  emit('confirm', properties.value, getOrgSelectLabel('audit') || '发起人自选');
  visible.value = false;
};

const getOrgSelectLabel = (type: string) => {
  return orgApprovelRef.value?.selectedLabels;
};

// 条件节点确认保存的回调
const conditionNodeComfirm = () => {
  let nodeContent = '';
  let name = '';
  const outerConditions: any[] = [];

  // 在这里只处理后续添加如今的数据内容
  nodeConditions.value.forEach((nodes, nodeIndex) => {
    const innerConditions: any[] = [];
    let res: any = {};

    nodes.forEach((t: any) => {
      if (typeof t.formId === 'number' && t.formId === -1) {
        // 这里是发起人 发起人是基于 initiator 数组来处理
        if (t.initiator && t.initiator.length > 0) {
          // 没有选择发起人的时候直接推出 取消计算
          const selectedList = JSON.parse(JSON.stringify(t.initiator));
          const selectIds = selectedList.map((item: any) => item.userId);

          res = {
            type: 'condition_starter',
            // 这里暂时先写死，如果是角色 值为 role 部门值为 dept
            matchType: 'user',
            paramValues: selectIds,
            // 这里固定针对发起人的情况 固定写死 paramKey 和 paramLabel
            paramKey: 'starterInfo',
            paramLabel: '发起者',
            initiator: t.initiator
          };

          innerConditions.push(res);
          // 发起人的提示语
          name += selectedList.map((item: any) => item.userName).join(',');
          if (name !== '') {
            name = name + ',';
          }
          nodeContent = `[发起人: ${name}]\n`;
        }
      } else {
        const cValue = t.conditionValue;
        // TODO 这里的cValue 需要排查原因有时候会是undefined
        if (!cValue) return;

        // 这里设置只有单选、多选和数字输入框显示值
        const numberTypeCmp = ['el-input-number'];
        let matchType = cValue.matchType;

        if (numberTypeCmp.includes(t.tag)) {
          if (matchType && matchType.indexOf('bet') > -1) {
            const cType = JSON.parse(JSON.stringify(cValue.conditionNumValue));
            // 介于的情况处理
            const type1 = cType[1];
            const type2 = cType[2];

            if (type1 === 'lt' && type2 === 'lt') {
              matchType = 'bet';
            } else if (type1 === 'lte' && type2 === 'lte') {
              matchType = 'ebete';
            } else if (type1 === 'lt' && type2 === 'lte') {
              matchType = 'bete';
            } else if (type1 === 'lte' && type2 === 'lt') {
              matchType = 'ebet';
            }

            nodeContent += `[${t.label}值范围为:${cValue.lowerBound}-${cValue.upperBound}]\n`;
          } else {
            const list = [
              { type: 'lt', key: 'upperBound' },
              { type: 'lte', key: 'upperBound' },
              { type: 'gt', key: 'lowerBound' },
              { type: 'gte', key: 'lowerBound' },
              { type: 'eq', key: 'boundEqual' }
            ];
            const itemVal = list.find((it) => it.type === cValue.matchType);
            const matchType = cValue.matchType as keyof typeof rangeType;
            nodeContent += `[${t.label} ${rangeType[matchType]} ${cValue[itemVal?.key]}]\n`;
          }

          res = {
            type: 'condition_range',
            paramKey: t.vModel,
            paramLabel: t.label,
            lowerBound: cValue.lowerBound,
            upperBound: cValue.upperBound,
            lowerBoundEqual: cValue.lowerBoundEqual,
            upperBoundEqual: cValue.upperBoundEqual,
            valid: cValue.valid,
            matchType: matchType,
            conditionNumValue: cValue.conditionNumValue,
            formId: t.formId,
            tag: t.tag
          };
        } else if (t.tag === 'el-radio-group') {
          res = {
            type: 'condition_value',
            paramValues: cValue,
            paramKey: t.vModel,
            paramLabel: t.label,
            formId: t.formId,
            tag: t.tag,
            options: t.options
          };

          nodeContent += `${t.label}属于: `;
          const options = JSON.parse(JSON.stringify(t.options));
          const labelList = options.filter((op: any) => cValue.includes(op.value)).map((item: any) => item.label);

          labelList.forEach((item: string, index: number) => {
            if (index !== labelList.length - 1) {
              nodeContent += `${item}或`;
            } else {
              nodeContent += `${item}`;
            }
          });
        } else if (t.tag === 'el-checkbox-group') {
          // 多选这里是 any(任意等于) 和 all (完全等于)
          res = {
            type: 'condition_multi_range',
            matchType: t.matchType,
            paramValues: cValue,
            paramKey: t.vModel,
            paramLabel: t.label,
            formId: t.formId,
            tag: t.tag,
            options: t.options
          };

          let typeLabel = '任意';
          if (t.matchType === 'all') {
            typeLabel = '完全等于';
          }

          nodeContent += `${t.label}(${typeLabel}): `;
          const options = JSON.parse(JSON.stringify(t.options));
          const labelList = options.filter((op: any) => cValue.includes(op.value)).map((item: any) => item.label);

          labelList.forEach((item: string, index: number) => {
            if (index !== labelList.length - 1) {
              nodeContent += `${item}或`;
            } else {
              nodeContent += `${item}`;
            }
          });
        } else {
          nodeContent += `[${t.label} = ${cValue}]\n`;
        }

        if (matchType === 'bet') {
          // 介于的情况处理
          const type1 = cValue.conditionNumValue[1];
          const type2 = cValue.conditionNumValue[2];

          if (type1 === 'lt' && type2 === 'lt') {
            matchType = 'bet';
          } else if (type1 === 'lte' && type2 === 'lte') {
            matchType = 'ebete';
          } else if (type1 === 'lt' && type2 === 'lte') {
            matchType = 'bete';
          } else if (type1 === 'lte' && type2 === 'lt') {
            matchType = 'ebet';
          }
        }

        innerConditions.push(res);
      }
    });

    // 当前不是最后一个就拼接一个或者
    if (nodeIndex !== nodeConditions.value.length - 1) {
      nodeContent += '或';
    }

    // 外层也需要推送
    outerConditions.push(innerConditions);
  });

  properties.value.conditions = outerConditions;
  // 发起人虽然是条件 但是这里把发起人放到外部单独判断
  emit('confirm', properties.value, nodeContent || '请设置条件');
  setTimeout(() => {
    visible.value = false;
  }, 500);
};

// 其他方法实现...
</script>

<style lang="scss" scoped>
.drawer {
  text-align: left;
}

.header {
  font-size: 16px;
  font-weight: bold;
}

.condition-pane {
  padding: 1rem;
}

.condition-pane-group {
  position: relative;
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.group-delete {
  position: absolute;
  right: 0rem;
  top: 0rem;
  cursor: pointer;
}

.group-and {
  text-align: center;
  color: #666;
  margin: 0.5rem 0;
}

.group-or {
  text-align: center;
  color: #666;
  margin: 1rem 0;
  font-weight: bold;
}

.radio-item {
  margin-right: 1rem;
}

.form-auth-table {
  padding: 1rem;
}

.auth-table-header {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 1rem;
}

.auth-table-body {
  .row {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
  }
}

.label {
  width: 200px;
  padding-right: 1rem;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.radio-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.option-box {
  padding: 1rem;
  color: #666;
}

.actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: #fff;
  z-index: 1;
}

.condition-dialog {
  :deep(.el-dialog__body) {
    padding: 1rem;
  }
}
</style>
