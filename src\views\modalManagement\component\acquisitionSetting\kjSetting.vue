<!-- 勘界设置 -->
<template>
  <div class="kjSetting-main">
    <div class="item-title">
      <div class="handle-title">
        <span class="text">勘界配置</span>
      </div>
    </div>
    <el-row style="margin: 24px 16px 4px">
      <el-col>
        <el-button type="primary" plain :icon="Plus" size="small" @click="addKJSetting">新增勘界配置</el-button>
      </el-col>
    </el-row>
    <div class="attribute-main" v-if="baseSettingForm && baseSettingForm.attribution.kjSetting">
      <div class="attribite-list">
        <el-row class="attribite-item" v-for="(item, index) in baseSettingForm.attribution.kjSetting" :key="index">
          <el-col :span="15" style="display: flex; align-items: center; margin-top: -10px">
            {{ item.name }}
          </el-col>
          <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end; margin-top: -10px">
            <el-tooltip class="item" effect="dark" content="修改" placement="top">
              <div class="text-btn" @click="editKJ(item)">
                <svg-icon class-name="svg-item" icon-class="more_edit" />
              </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="删除" placement="top">
              <div class="text-btn" @click="delKJ(index)">
                <svg-icon class-name="svg-item" icon-class="more_delete" />
              </div>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 勘测配置弹窗 -->
    <el-dialog
      title="勘界配置"
      v-model="KJSettingDialig"
      width="972px"
      height="900px"
      :close-on-click-modal="false"
      :before-close="handleCloseKJDialog"
    >
      <el-form :model="nowAnalyse" :rules="nowAnalyseRules" ref="nowAnalyseRef" label-width="100px" class="demo-ruleForm" label-position="top">
        <div class="dialog-row" style="margin-bottom: 10px">
          <div class="item">
            <el-form-item label="配置名称" prop="name">
              <el-input v-model="nowAnalyse.name" placeholder="请输入配置名称"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="导出类型" prop="exportType">
              <el-select v-model="nowAnalyse.exportType" placeholder="请选择数据归属" style="width: 100%">
                <el-option label="默认" :value="0"></el-option>
                <el-option label="金沙2023" :value="1"></el-option>
                <el-option label="六盘水2023" :value="2"></el-option>
                <!-- 增加此类型是为了表一中把其他草地合并到草地 2025.5.23 11点34分 陈忠兰提出 -->
                <el-option label="工程报件(合并：其他草地、草地)" :value="4"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="面积计算方式" prop="areaType">
              <el-select v-model="nowAnalyse.areaType" placeholder="请选择面积计算方式" style="width: 100%">
                <el-option label="投影坐标系" :value="1"></el-option>
                <el-option label="大地坐标系" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="自定义字段" prop="customFieldGroupString">
              <el-select v-model="nowAnalyse.customFieldGroupString" placeholder="请选择字段所在的属性组" style="width: 100%">
                <el-option :label="item.typeName" :value="item.typeName" v-for="(item, index) in groupList" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-form-item label="截图设置">
          <el-checkbox-group v-model="nowAnalyse.jtSetting">
            <el-checkbox :label="item.value" v-for="(item, index) in checkBoxOption" :key="index">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <div class="switch-mian">
          <!-- <el-form-item label="是否追溯河流水面">
						<el-radio-group v-model="nowAnalyse.isAnalyzeHlsm">
							<el-radio :label="true">是</el-radio>
							<el-radio :label="false">否</el-radio>
						</el-radio-group>
					</el-form-item> -->
          <div class="switch-item">
            <span class="text">是否追溯河流水面</span>
            <span><el-switch v-model="nowAnalyse.isAnalyzeHlsm"> </el-switch></span>
          </div>
          <div class="switch-item">
            <span class="text">是否追溯水库水面</span>
            <span><el-switch v-model="nowAnalyse.isAnalyzeSksm"> </el-switch></span>
          </div>
          <!-- <el-form-item label="是否追溯水库水面">
						<el-radio-group v-model="nowAnalyse.isAnalyzeSksm">
							<el-radio :label="true">是</el-radio>
							<el-radio :label="false">否</el-radio>
						</el-radio-group>
					</el-form-item> -->
        </div>

        <el-form-item label="配置步骤" prop="step">
          <div style="display: flex; flex-direction: column; width: 100%">
            <div style="display: flex; flex-direction: row; align-items: center">
              <el-dropdown style="cursor: pointer; margin-bottom: 4px" @command="handleCommandKJ">
                <span class="el-dropdown-link"> 增加分析步骤<i class="el-icon-arrow-down el-icon--right"></i> </span>
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="(item, index) in analyseStep" :key="index" :disabled="item.disable" :command="item.value">{{
                      item.title
                    }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-link type="primary" style="margin-left: 10px" @click="changeNode">
                <span v-show="allOpen">全部收起</span>
                <span v-show="!allOpen">全部展开</span>
              </el-link>
            </div>
            <div class="kj-main" :style="{ height: KJHeight }">
              <div class="kj-item-content" v-for="(item, index) in nowAnalyse.step" :key="index">
                <div class="kj-group">
                  <div class="group-title" @click="item.isShow = !item.isShow">
                    <div class="title">
                      步骤{{ index + 1 }}:
                      <span v-show="item.type == 1">相交分析</span>
                      <span v-show="item.type == 2">追溯分析</span>
                      <span v-show="item.type == 3">多面积分析</span>
                      <span v-show="item.type == 4">现状分析</span>
                      <span v-show="item.type == 5">现状追溯分析</span>
                      <span v-show="item.type == 6">农转非分析</span>
                      <span v-show="item.type == 7">面积分析</span>
                      <span v-show="item.type == 8">违法勘界</span>
                      <el-link type="danger" @click.stop="delItem(index)" style="margin-left: 10px">删除</el-link>
                    </div>
                    <div class="icon">
                      <el-link type="primary" @click.stop="moveUp(index)" style="margin-right: 10px" v-show="index != 0">上移</el-link>
                      <el-link type="primary" @click.stop="moveDown(index)" style="margin-right: 10px" v-show="index != nowAnalyse.step.length - 1"
                        >下移</el-link
                      >
                      <i class="el-icon-arrow-up" v-show="item.isShow"></i>
                      <i class="el-icon-arrow-down" v-show="!item.isShow"></i>
                    </div>
                  </div>
                  <div class="group-content" v-show="item.isShow">
                    <div class="flex-row">
                      <div class="label">数据源：</div>
                      <div class="content">
                        <el-input
                          v-model="item.dataSource"
                          placeholder="请选择数据源"
                          v-if="index == 0"
                          :style="{
                            width: index == 0 ? `calc(100% - 0px)` : '100%'
                          }"
                        ></el-input>
                        <el-select
                          v-model="item.dataSource"
                          placeholder="请选择数据源"
                          v-else
                          :style="{
                            width: index == 0 ? `calc(100% - 0px)` : '100%'
                          }"
                        >
                          <el-option v-for="ite in getSourceList(index)" :key="ite.value" :label="ite.label" :value="ite.value"> </el-option>
                        </el-select>
                      </div>
                    </div>
                    <template v-if="index == 0 && item.type != 8">
                      <div class="flex-item">
                        <div class="label">标题Key：</div>
                        <div class="content">
                          <el-input v-model="item.titleKey" placeholder="请选择标题Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">姓名Key：</div>
                        <div class="content">
                          <el-input v-model="item.nameKey" placeholder="请选择姓名Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">地址Key：</div>
                        <div class="content">
                          <el-input v-model="item.addressKey" placeholder="请选择地址Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">批次Key：</div>
                        <div class="content">
                          <el-input v-model="item.batchKey" placeholder="请选择批次Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">身份证号Key：</div>
                        <div class="content">
                          <el-input v-model="item.idCardKey" placeholder="请选择身份证号Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">四至-东Key：</div>
                        <div class="content">
                          <el-input v-model="item.szEastKey" placeholder="请选择四至-东Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">四至-南Key：</div>
                        <div class="content">
                          <el-input v-model="item.szSouthKey" placeholder="请选择四至-南Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">四至-西Key：</div>
                        <div class="content">
                          <el-input v-model="item.szWestKey" placeholder="请选择四至-西Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">四至-北Key：</div>
                        <div class="content">
                          <el-input v-model="item.szNorthKey" placeholder="请选择四至-北Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">联系电话Key：</div>
                        <div class="content">
                          <el-input v-model="item.phoneKey" placeholder="请选择联系电话Key"></el-input>
                        </div>
                      </div>
                    </template>
                    <template v-if="item.type == 8">
                      <div class="flex-item">
                        <div class="label">标题Key：</div>
                        <div class="content">
                          <el-input v-model="item.titleKey" placeholder="请选择标题Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">姓名Key：</div>
                        <div class="content">
                          <el-input v-model="item.nameKey" placeholder="请选择姓名Key"></el-input>
                        </div>
                      </div>
                    </template>
                    <div class="flex-row">
                      <div class="label">分析shp：</div>
                      <div class="content">
                        <el-input v-model="item.analyseShp" placeholder="请选择分析SHP" style="width: calc(100% - 0px)"></el-input>
                      </div>
                    </div>
                    <template v-if="item.type == 8">
                      <div class="flex-item">
                        <div class="label">地类编码Key：</div>
                        <div class="content">
                          <el-input v-model="item.DLCodeKey" placeholder="请选择地类编码key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">权属性质Key：</div>
                        <div class="content">
                          <el-input v-model="item.qsxzKey" placeholder="请选择权属性质Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">田坎系数Key：</div>
                        <div class="content">
                          <el-input v-model="item.tkxsKey" placeholder="请选择田坎系数Key"></el-input>
                        </div>
                      </div>
                    </template>
                    <!-- 追溯分析 -->
                    <template v-if="item.type == 2">
                      <div class="flex-row">
                        <div class="label">追溯地类：</div>
                        <div class="content">
                          <el-select
                            v-model="item.ascendDL"
                            multiple
                            placeholder="请选择地类"
                            style="width: 100%"
                            @change="changeDLType($event, item)"
                          >
                            <el-option v-for="(ite, idx) in ascendDLOption" :key="idx" :label="ite.label" :value="ite.value"> </el-option>
                          </el-select>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">地类编码key：</div>
                        <div class="content">
                          <el-input v-model="item.DLCodeKey" placeholder="请选择地类编码Key"></el-input>
                        </div>
                      </div>
                    </template>
                    <!-- 多面积分析 -->

                    <div class="flex-item" v-if="item.type != 2 && item.type != 8">
                      <div class="label">地类编码Key：</div>
                      <div class="content">
                        <el-input v-model="item.DLCodeKey" placeholder="请选择地类编码key"></el-input>
                      </div>
                    </div>
                    <div class="flex-item" v-if="item.type != 2 && item.type != 8">
                      <div class="label">田坎系数Key：</div>
                      <div class="content">
                        <el-input v-model="item.tkxsKey" placeholder="请选择田坎系数key"></el-input>
                      </div>
                    </div>
                    <div class="flex-item" v-if="item.type != 2 && item.type != 8">
                      <div class="label">田坎比例：</div>
                      <div class="content">
                        <el-input v-model="item.tkbl" placeholder="请输入田坎比例"></el-input>
                      </div>
                    </div>
                    <div class="flex-item" v-if="item.type == 3">
                      <div class="label">年份key：</div>
                      <div class="content">
                        <el-input v-model="item.nfKey" placeholder="请输入年份key"></el-input>
                      </div>
                    </div>
                    <template v-if="item.type == 3">
                      <div class="flex-item">
                        <div class="label">开始年份：</div>
                        <div class="content">
                          <el-input v-model="item.startYear" placeholder="请输入开始年份"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">结束年份：</div>
                        <div class="content">
                          <el-input v-model="item.endYear" placeholder="请输入结束年份"></el-input>
                        </div>
                      </div>
                      <div class="flex-row">
                        <div class="label">现状地物SHP：</div>
                        <div class="content">
                          <el-input v-model="item.xzdwShp" placeholder="请选择现状地物SHP（没有可不选）" style="width: calc(100% - 0px)"></el-input>
                        </div>
                      </div>
                      <div class="flex-row">
                        <div class="label">零星地物SHP：</div>
                        <div class="content">
                          <el-input v-model="item.lxdwShp" placeholder="请选择零星地物SHP（没有可不选）" style="width: calc(100% - 0px)"></el-input>
                        </div>
                      </div>
                    </template>
                    <!-- 违法勘界 -->
                    <template v-if="item.type == 8">
                      <div class="flex-row">
                        <div class="label">乡镇shp：</div>
                        <div class="content">
                          <el-input v-model="item.analyseShp" placeholder="请选择乡镇SHP" style="width: calc(100% - 0px)"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">乡镇名称Key：</div>
                        <div class="content">
                          <el-input v-model="item.xzmcKey" placeholder="请选择乡镇名称Key"></el-input>
                        </div>
                      </div>
                      <div class="flex-row">
                        <div class="label">村shp：</div>
                        <div class="content">
                          <el-input v-model="item.analyseShp" placeholder="请选择村SHP" style="width: calc(100% - 0px)"></el-input>
                        </div>
                      </div>
                      <div class="flex-item">
                        <div class="label">村名称Key：</div>
                        <div class="content">
                          <el-input v-model="item.cunKey" placeholder="请选择村名称Key"></el-input>
                        </div>
                      </div>
                    </template>
                    <!-- 年份year 字段 -->
                    <div class="flex-item" v-if="![1, 2, 3, 8].includes(item.type)">
                      <div class="label">年份：</div>
                      <div class="content">
                        <el-input v-model="item.year" placeholder="请输入年份"></el-input>
                      </div>
                    </div>
                    <!-- 结果 -->
                    <div class="flex-row">
                      <div class="label">结果类型：</div>
                      <div class="content">
                        <el-select v-model="item.resultType" placeholder="请选择结果类型" style="width: 100%">
                          <el-option label="相交部分" :value="1"></el-option>
                          <el-option label="不相交部分" :value="2"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <!-- 绑定表 只有表1 表2 表3 表4 相交分析、追溯分析没有该内容 -->
                    <div class="flex-row" v-show="item.type != 1 && item.type != 2">
                      <div class="label">数据归属：</div>
                      <div class="content">
                        <el-select v-model="item.dataOwner" multiple placeholder="请选择数据归属" style="width: 100%">
                          <el-option label="表1" :value="1"></el-option>
                          <el-option label="表2" :value="2"></el-option>
                          <el-option label="表3" :value="3"></el-option>
                          <el-option label="表4" :value="4"></el-option>
                          <el-option label="表5" :value="5"></el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseKJDialog">取 消</el-button>
          <el-button type="primary" @click="submitStep">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

//Props定义
const props = defineProps({
  checkedTreeMsg: {
    type: Array,
    default: () => []
  }
});

// 定义ref
const nowAnalyseRef = ref<FormInstance>();
const baseSettingForm = ref<any>({
  attribution: {
    kjSetting: []
  }
});
const showKJSeting = ref(false);
const KJSettingDialig = ref(false);
const KJHeight = window.innerHeight - 600 + 'px';
const analyseStep = [
  { title: '相交分析', value: 1, disable: false },
  { title: '追溯分析', value: 2, disable: false },
  { title: '多面积分析', value: 3, disable: false },
  { title: '现状分析', value: 4, disable: false },
  { title: '现状追溯分析', value: 5, disable: false },
  { title: '农转非分析', value: 6, disable: false },
  { title: '面积分析', value: 7, disable: false },
  { title: '违法勘界', value: 8, disable: false }
];
const nowAnalyse = ref<any>({
  name: '',
  step: [], //  在每一个对应的数据中添加一个dataSourceList 和分析shp
  exportType: 0, //导出类型 0:默认 1：金沙2023 2：六盘水2023
  jtSetting: [], //截图设置
  areaType: 1,
  // 自己配置选择固定的属性组的中名字
  customFieldGroupString: '',
  //  是否追溯河流水面
  isAnalyzeHlsm: false,
  // 是否追溯水库水面
  isAnalyzeSksm: false
});
const nowAnalyseRules = ref<FormRules>({
  name: [{ required: true, message: '请输入勘界分析名字', trigger: 'blur' }],
  step: [
    {
      type: 'array',
      required: true,
      message: '请选择分析步骤',
      trigger: 'change'
    }
  ],
  exportType: [{ required: true, message: '请选择导出类型', trigger: 'change' }],
  areaType: [{ required: true, message: '请选择面积类型', trigger: 'change' }]
});
const ascendDLOption = [
  { label: '果园', value: '021;0201' },
  { label: '可调整果园', value: '0201K' },
  { label: '茶园', value: '022;0202' },
  { label: '可调整茶园', value: '0202K' },
  { label: '其他园地', value: '023;0204' },
  { label: '可调整其他园地', value: '0204K' },
  { label: '橡胶园', value: '0203' },
  { label: '可调整橡胶园', value: '0203K' },

  { label: '有林地', value: '031' },
  { label: '灌木林地', value: '032;0305' },
  { label: '其他林地', value: '033;0307' },
  { label: '可调整其他林地', value: '0307K' },
  { label: '乔木林地', value: '0301' },
  { label: '可调整乔木林地', value: '0301K' },
  { label: '竹林地', value: '0302' },
  { label: '可调整竹林地', value: '0302K' },
  { label: '红树林地', value: '0303' },
  { label: '森林沼泽', value: '0304' },
  { label: '灌丛沼泽', value: '0306' },

  { label: '天然牧草地', value: '041;0401' },
  { label: '人工牧草地', value: '042;0403' },
  { label: '可调整人工牧草地', value: '0403K' },
  { label: '其他草地', value: '043;0404' },
  { label: '沼泽草地', value: '0402' },

  { label: '田坎', value: '123;1203' },
  { label: '农村道路', value: '104;1006' },
  { label: '沟渠', value: '117;1107;1107A' },
  { label: '水库水面', value: '113;1103' },
  { label: '坑塘水面', value: '114;1104;1104A' },
  { label: '设施农业建设用地', value: '122;1202' }
];
const isEdit = ref(false); // 是否编辑
const checkBoxOption = [
  { value: 1, label: '现状图' },
  { value: 2, label: '2009年现状图' },
  { value: 3, label: '勘测定界图' },
  { value: 4, label: '套合图' },
  { value: 5, label: '三区三县图' }
];
const allOpen = ref(true); // 全部展开
const customFieldVisible = ref(false); // 设置自定义内容相关
const customForm = ref<any>({
  nameKey: '',
  titleKey: ''
});
const customFormRules = ref<FormRules>({
  nameKey: [{ required: true, message: '请输入字段标题', trigger: 'blur' }],
  titleKey: [
    { required: true, message: '请输入字段值', trigger: 'blur' },
    {
      pattern: /^[A-Za-z0-9]+$/g, //正则校验不用字符串
      message: '只能输入数字和英文',
      trigger: 'blur'
    }
  ]
});

const groupList = computed({
  get() {
    return props.checkedTreeMsg?.fieldGroupModelList?.filter((item) => item.typeName != '基础信息' && item.typeName != '临时变量');
  },
  set(val) {}
});

//方法
/**
 * 初始化
 * @param item 当前选中的节点
 */
const initData = (item: any) => {
  baseSettingForm.value = item;
};

/**
 * 新增勘界配置
 */
const addKJSetting = () => {
  if (baseSettingForm.value.attribution && baseSettingForm.value.attribution.kjSetting.length > 0) {
    ElMessage.error('勘界只允许配置一个！！！');
    return;
  }
  nowAnalyse.value = {
    name: '',
    step: [],
    jtSetting: [],
    areaType: 1,
    // 自己配置选择固定的属性组的中名字
    customFieldGroupString: ''
    // id:(Math.floor(100000 * Math.random()) + new Date().getTime()).toString(32).slice(0,8)
  };
  isEdit.value = false;
  KJSettingDialig.value = true;
};
/**
 * 关闭设置弹窗
 */
const handleCloseKJDialog = () => {
  KJSettingDialig.value = false;
};
const handleCommandKJ = (command: any) => {
  // 如果当前是第一步骤的时候增加 dataSourceList 且里面增加固定字段
  const obj = {
    dataSource: '', //数据源
    analyseShp: '', //分析shp
    resultType: 1, //结果类型 1相交部分 2不相交部分
    isShow: true,
    shpList: [],
    type: 1 //类型 1相交分析 2追溯分析 3多面积分析 4现状分析 5现状追溯分析 6农转非分析 7面积分析 8违法勘界
  };
  if (command == 1) {
    //相交分析
    obj.type = 1;
    if (nowAnalyse.value.step.length == 0) {
      //第一步的时候 需要增加一些字段
      obj.titleKey = 'xmmc';
      obj.nameKey = 'qlr';
      obj.addressKey = 'qsdw';
      obj.batchKey = 'xmmc';
      obj.idCardKey = 'sfzh';
      obj.szEastKey = 'zdszd';
      obj.szSouthKey = 'zdszn';
      obj.szWestKey = 'zdszx';
      obj.szNorthKey = 'zdszb';
      obj.phoneKey = 'phone';
    }
  } else if (command == 2) {
    //追溯分析
    obj.type = 2;
    obj.ascendDL = []; //追溯地类
    obj.ascendDLNames = []; //追溯地类名字
    ascendDLOption.forEach((v) => {
      obj.ascendDL.push(v.value);
      obj.ascendDLNames.push(v.label);
    });
    obj.DLCodeKey = 'dlbm'; //地类编码key
  } else if (command == 3) {
    //多面积分析
    obj.type = 3;
    obj.xzdwShp = ''; //现状地物shp
    obj.lxdwShp = ''; //零星地物shp
    obj.DLCodeKey = 'dlbm'; //地类编码key
    obj.tkxsKey = 'tkxs'; //田坎系数
    obj.tkbl = 100; //田坎比例
    obj.nfKey = 'bgnd'; //年份key
    obj.startYear = '2009'; //开始年份
    obj.endYear = '2018'; //结束年份
    obj.dataOwner = [];
  } else if (command == 8) {
    //违法勘界
    obj.type = 8;
    obj.DLCodeKey = 'dlbm'; //地类编码
    obj.qsxzKey = 'qsxz'; //权属性质
    obj.tkxsKey = 'tkxs'; //田坎系数
    obj.titleKey = 'xmmc';
    obj.nameKey = 'qlr';
    obj.xzmcKey = 'xzqmc'; //乡镇名称key
    obj.cunKey = 'zldwmc'; //村名称key
    obj.xzShp = ''; //乡镇shp
    obj.cunShp = ''; //村shp
    obj.dataOwner = [];
  } else {
    // 现状分析 现状追溯分析 农转非分析 面积分析 只是类型不一样 字段都一样
    obj.type = command;
    obj.DLCodeKey = 'dlbm';
    obj.tkxsKey = 'kcxs';
    obj.year = ''; //年份
    obj.dataOwner = [];
  }

  nowAnalyse.value.step.push(obj);
};
/**
 * 根据下标返回对应的结果内容
 * @param index 下标
 * @returns 结果内容
 */
const getSourceList = (index: number) => {
  const options = [];
  for (let i = 0; i < index; i++) {
    const obj = { label: `步骤${i + 1}结果`, value: i + 1 };
    options.push(obj);
  }
  return options;
};

/**
 * 删除某个步骤
 * @param index 下标
 */
const delItem = (index: number) => {
  ElMessageBox.confirm('确定要删除该步骤吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      nowAnalyse.value.step.splice(index, 1);
      if (index == 0) {
        //如果把第一个删除，那么就需要把下一个赋值初始内容
        nowAnalyse.value.step[0].titleKey = 'xmmc';
        nowAnalyse.value.step[0].nameKey = 'qlr';
        nowAnalyse.value.step[0].addressKey = 'qsdw';
        nowAnalyse.value.step[0].batchKey = 'xmmc';
        (nowAnalyse.value.step[0].idCardKey = 'sfzh'),
          (nowAnalyse.value.step[0].szEastKey = 'zdszd'),
          (nowAnalyse.value.step[0].szSouthKey = 'zdszn'),
          (nowAnalyse.value.step[0].szWestKey = 'zdszx'),
          (nowAnalyse.value.step[0].szNorthKey = 'zdszb');
      }
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {});
};

const handleFieldCloseField = () => {
  // this.$refs['customRef'].resetFields();
  customFieldVisible.value = false;
};

/**
 * 提交步骤
 */
const submitStep = () => {
  nowAnalyseRef.value?.validate(async (valid: boolean) => {
    if (!valid) {
      return;
    }
    const flg = validateStep();
    if (!flg) {
      ElMessage.error('您有内容未填写完整！！！');
      return;
    }
    if (!isEdit.value) {
      if (baseSettingForm.value.attribution && 'kjSetting' in baseSettingForm.value.attribution) {
        baseSettingForm.value.attribution.kjSetting.push(nowAnalyse.value);
      } else {
        //没有的时候
        baseSettingForm.value.attribution = {
          kjSetting: [nowAnalyse.value]
        };
      }
    }
    KJSettingDialig.value = false;
  });
};

/**
 * 校验数据不是第一步起，的其他几步的数据是否选择了数据源的追溯地类。
 * @returns boolean
 */
const validateStep = () => {
  let flg = true;
  for (let i = 0; i < nowAnalyse.value.step.length; i++) {
    if (i != 0 && !nowAnalyse.value.step[i].dataSource) {
      flg = false;
      break;
    }
    if (nowAnalyse.value.step[i].type == 2) {
      //追溯分析验证
      if (nowAnalyse.value.step[i].ascendDL.length == 0) {
        flg = false;
        break;
      }
    }
  }
  return flg;
};

/**
 * 修改勘界配置
 * @param item 修改的内容
 */
const editKJ = (item: any) => {
  nowAnalyse.value = item;
  if (!nowAnalyse.value.jtSetting) {
    nowAnalyse.value.jtSetting = [];
  }
  isEdit.value = true;
  KJSettingDialig.value = true;
};

/**
 * 删除勘界
 * @param index 下标
 */
const delKJ = (index: number) => {
  ElMessageBox.confirm('确定要删除该配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      baseSettingForm.value.attribution.kjSetting.splice(index, 1);
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {});
};

/**
 * 追溯地类中文名赋值
 * @param val 改变的内容
 * @param ele 改变的选项
 */
const changeDLType = (val: any, ele: any) => {
  const ascendDLNames: any[] = [];
  ascendDLOption.forEach((v) => {
    if (val.includes(v.value)) {
      ascendDLNames.push(v.label);
    }
  });
  ele.ascendDLNames = ascendDLNames;
};

/**
 * 上移
 * @param index 当前下标
 */
const moveUp = (index: number) => {
  if (index > 0 && index < nowAnalyse.value.step.length) {
    [nowAnalyse.value.step[index], nowAnalyse.value.step[index - 1]] = [nowAnalyse.value.step[index - 1], nowAnalyse.value.step[index]];
  }
};

/**
 * 下移
 * @param index 当前下标
 */
const moveDown = (index: number) => {
  if (index >= 0 && index < nowAnalyse.value.step.length - 1) {
    [nowAnalyse.value.step[index], nowAnalyse.value.step[index + 1]] = [nowAnalyse.value.step[index + 1], nowAnalyse.value.step[index]];
  }
};

/**
 * // 改变展开收起 全部
 * @param node 节点
 */
const changeNode = () => {
  allOpen.value = !allOpen.value;
  if (allOpen.value) {
    nowAnalyse.value.step.forEach((v: any) => {
      v.isShow = true;
    });
  } else {
    nowAnalyse.value.step.forEach((v: any) => {
      v.isShow = false;
    });
  }
};

defineExpose({
  initData
});
</script>
<style lang="scss" scoped>
.switch-mian {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  .switch-item {
    margin-right: 16px;
    .text {
      font-size: 14px;
      font-weight: 600;
      padding-right: 16px;
    }
  }
}
.kjSetting-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  min-height: 80px;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;
    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }
  &:hover {
    border: 1px solid var(--current-color);
    // color: #0081ff;
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .attribute-main {
    // margin:24px 0 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    .attribite-list {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      // align-content: center;
      // align-items: center;
      flex: 1;
      margin: 0px 16px 12px;
      .attribite-item {
        margin: 12px 0;
        padding: 8px 12px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        background-color: rgba(246, 247, 248, 1);
        // text-align: center;
        display: flex;
        align-items: center;
        .svg-item {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          color: #333;
        }
        .item-type-name {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
        }
        .item-remark {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .text-btn {
          margin: 0 8px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .svg-item {
            width: 16px;
            height: 16px;
            margin: auto;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
          &:hover .svg-item {
            width: 16px;
            height: 16px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
        }
      }
    }
  }
}

.el-dialog {
  .item-content {
    flex: 1;
    .choose-img-div {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      cursor: pointer;
      .choose-img {
        width: 20px;
        height: 20px;
        margin-left: 16px;
      }
      .choose-btn {
        width: 52px;
        height: 100%;
        border-left: 1px solid #dcdfe6;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 20px;
      }
    }
  }
}
.dynamic-form-class {
  width: 100%;
}
.kj-main {
  overflow: auto;
  border: #dcdfe6 solid 1px;
  border-radius: 4px;
  padding: 16px;
  .kj-item-content {
    .kj-group {
      margin-bottom: 10px;
      border: 1px solid #ededed;
      .group-title {
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0px 16px;
        background: #f8f8f8;
        justify-content: space-between;
        cursor: pointer;
        .title {
          color: rgba(0, 0, 0, 0.6);
          font-size: 12px;
          font-weight: 600;
        }
        .icon {
          i {
            font-size: 18px;
            color: #999;
          }
        }
      }
      .group-content {
        padding: 8px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .flex-one {
          flex: 100%;
          margin-right: 16px;
        }
        .flex-item {
          flex: 0 0 calc(33.33% - 16px); /* 25% width minus the gap */
          margin-right: 16px; /* Right margin for the gap */
          // margin-bottom: 16px; /* Bottom margin for the gap */
          box-sizing: border-box; /* Include padding and border in the width */
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          position: relative;
          .label {
            width: 100px;
            text-align: right;
            margin-right: 5px;
          }
          .content {
            flex: 1;
          }
        }
        .flex-row {
          width: calc(100% - 16px);
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .label {
            width: 100px;
            text-align: right;
            margin-right: 5px;
          }
          .content {
            flex: 1;
          }
        }
      }
      //   .group-content {
      //     display: grid;
      //     grid-template-columns: repeat(3, 1fr);
      //     grid-column-gap: 10px;
      //     grid-row-gap: 10px;
      //     padding: 8px;
      //     .item{
      //       display: flex;
      //       flex-direction: row;
      //       align-items: center;
      //       .label{
      //         width: 100px;
      //         text-align: right;
      //         margin-right: 5px;
      //       }
      //       .content{
      //         flex:1;
      //       }
      //     }
      //   }
      .spe-item {
        padding: 0px 8px 8px 8px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .label {
          width: 100px;
          text-align: right;
          margin-right: 5px;
        }
        .content {
          flex: 1;
        }
      }
    }
  }
}
/*滚动条样式*/
.kj-main::-webkit-scrollbar {
  width: 4px;
}
.kj-main::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.kj-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  .item {
    width: calc(100% / 2 - 20px);
    margin: 0 2px;
    &:last-child,
    :first-child {
      margin: 0;
    }
  }
}
</style>
