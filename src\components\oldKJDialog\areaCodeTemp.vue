<!-- 反显省市区并可选择省市区 用于宗地管理 -->
<template>
  <div class="areaCodeTemp-main">
    <el-cascader
      :props="cascaderProps as any"
      :size="size"
      v-model="selectCityCode"
      clearable
      @change="handleChangeCity"
      ref="cascaderHandleRef"
      style="width: 100%"
      filterable
      placeholder="请选择行政区划"
    ></el-cascader>
  </div>
</template>

<script setup lang="ts">
import { getAreaCode } from '@/api/project';

// ---Props---
interface Props {
  selectAreaCode: string;
  size?: 'default' | 'small' | 'large';
}

const props = withDefaults(defineProps<Props>(), {
  selectAreaCode: '',
  size: 'default'
});

// --- watch ---
watch(
  () => props.selectAreaCode,
  (val) => {
    selectCityCode.value = [];
  }
);

const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    getAreaCode(String(520000000000)).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (node.level < 4) {
    //  只展示到乡镇的数据
    const areaCode = node.data.areaCode;
    getAreaCode(areaCode).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    resolve([]);
  }
};
// --- 定义变量 ---
const cascaderProps = reactive({
  lazy: true,
  lazyLoad: loadNode,
  value: 'areaCode',
  label: 'areaName',
  expandTrigger: 'click',
  children: 'children',
  checkStrictly: true
});
const selectCityCode: any = ref([]);
const selAreaCode = ref('');
const cascaderHandleRef = ref();

// ---定义emit---
const emit = defineEmits<{
  (e: 'changeCityCode', areaCode: string, areaName: any, address: any): void;
}>();

// --- 定义方法 ---

/**
 * 选择城市的改变
 * @param val 选择的值
 */
const handleChangeCity = async (val: any) => {
  // 把搜索置空
  selAreaCode.value = '';
  cascaderHandleRef.value.dropDownVisible = false; //监听值发生变化就关闭它
  let itemCode = 0;
  let address = '';
  if (val.length > 1) {
    itemCode = val[val.length - 2];
  } else {
    itemCode = 520000000000;
  }
  if (itemCode != 0) {
    await getAreaCode(itemCode.toString()).then((res) => {
      if (res.code == 200) {
        for (let index = 0; index < res.data.length; index++) {
          if (res.data[index].areaCode == val[val.length - 1]) {
            address = res.data[index].areaName;
            break;
          }
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
  if (val[val.length - 1]) {
    emit('changeCityCode', val[val.length - 1], val, address);
  }
};
</script>
