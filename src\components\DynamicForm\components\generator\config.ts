const baseUrl = import.meta.env.VITE_APP_BASE_API;

interface FormConfig {
  formRef: string;
  size: string;
  labelPosition: string;
  labelWidth: number;
  gutter: number;
  disabled: boolean;
  span: number;
  formBtns: boolean;
}

interface FormComponent {
  label: string;
  tag: string;
  tagIcon: string;
  icon: string;
  placeholder?: string;
  isPlaceholder?: boolean;
  defaultValue?: any;
  delFlag?: number;
  span?: number;
  labelWidth?: number | null;
  style?: Record<string, string>;
  defalutSelect?: number;
  expression?: string;
  maxlength?: string;
  'show-word-limit'?: boolean;
  readonly?: boolean;
  disabled?: boolean;
  required?: boolean;
  regList?: any[];
  changeTag?: boolean;
  proCondition?: boolean;
  asSummary?: boolean;
  content?: string;
  inputType?: string;
  // 是否单次刷新表达式
  isOnceRefeshExpression?: boolean;
  clearable?: boolean;
  prepend?: string;
  append?: string;
  type?: string;
  // ...其他属性根据实际情况添加
}
export const formConf: FormConfig = {
  formRef: 'elForm',
  size: 'small',
  labelPosition: 'right',
  labelWidth: 100,
  gutter: 15,
  disabled: false,
  span: 24,
  formBtns: true
};

export const inputComponents: FormComponent[] = [
  {
    label: '单行输入框',
    tag: 'el-input',
    tagIcon: 'input',
    icon: 'inputIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: '',
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '多行输入框',
    tag: 'el-input',
    tagIcon: 'textarea',
    icon: 'textareaIcon',
    type: 'textarea',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defaultValue: undefined,
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    span: 24,
    labelWidth: null,
    autosize: {
      minRows: 1,
      maxRows: 4
    },
    style: { width: '100%' },
    maxlength: '',
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '数字输入框',
    tag: 'el-input-number',
    tagIcon: 'number',
    icon: 'numberIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defaultValue: undefined,
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    style: { width: '100%' },
    span: 24,
    labelWidth: null,
    min: undefined,
    max: undefined,
    step: undefined,
    'step-strictly': false,
    numberType: 4,
    precision: 15,
    accuracy: 32, //精度
    'controls-position': 'right',
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    inputType: 'Double',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '计数器',
    tag: 'el-input-number',
    tagIcon: 'number',
    icon: 'numberIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defaultValue: '',
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    style: { width: '100%' },
    span: 24,
    labelWidth: null,
    min: 0,
    max: undefined,
    isStep: true,
    step: 1,
    'step-strictly': false,
    accuracy: 0, //精度
    'controls-position': '',
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    inputType: 'Double',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  }
];

export const selectComponents: FormComponent[] = [
  {
    label: '下拉选择',
    tag: 'el-select',
    tagIcon: 'select',
    icon: 'selectIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    style: { width: '100%' },
    defaultValue: undefined,
    maxlength: '',
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    span: 24,
    labelWidth: null,
    clearable: true,
    disabled: false,
    required: false,
    filterable: false,
    multiple: false,
    options: [
      {
        id: new Date().getTime(),
        label: '选项1',
        value: '选项1'
      },
      {
        id: new Date().getTime() + 1,
        label: '选项2',
        value: '选项2'
      }
    ],
    // 选项值类型 false 是默认的字符串类型 true json 数组类型
    optionIsJson: false,
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false,
    conditionSetting: [] //隐藏题目条件判断
  },
  {
    label: '级联选择',
    tag: 'el-cascader',
    tagIcon: 'cascader',
    icon: 'cascaderIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '请选择',
    defaultValue: '',
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    props: {
      props: {
        multiple: false
      }
    },
    'show-all-levels': true,
    disabled: false,
    clearable: true,
    filterable: false,
    required: false,
    options: [],
    dataType: 'static',
    labelKey: 'label',
    valueKey: 'value',
    childrenKey: 'children',
    separator: '/',
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false,
    conditionSetting: [] //隐藏题目条件判断
  },
  {
    label: '单选框组',
    tag: 'el-radio-group',
    tagIcon: 'radio',
    icon: 'radioIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defaultValue: undefined,
    delFlag: 0,
    defalutSelect: 1,
    maxlength: '',
    expression: undefined,
    span: 24,
    labelWidth: null,
    style: {},
    optionType: 'default',
    border: false,
    size: 'medium',
    disabled: false,
    required: false,
    // 设置默认值绑定的数据
    // defaultItem:{},
    // defaultId:undefined,
    options: [
      {
        id: new Date().getTime(),
        label: '选项1',
        value: '选项1'
      },
      {
        id: new Date().getTime() + 1,
        label: '选项2',
        value: '选项2'
      }
    ],
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false,
    conditionSetting: [] //隐藏题目条件判断
  },
  {
    label: '多选框组',
    tag: 'el-checkbox-group',
    tagIcon: 'checkbox',
    icon: 'checkboxIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    defaultValue: [],
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    span: 24,
    labelWidth: null,
    maxlength: '',
    style: {},
    optionType: 'default',
    border: false,
    size: 'medium',
    disabled: false,
    required: false,
    options: [
      {
        id: new Date().getTime(),
        label: '选项1',
        value: '选项1'
      },
      {
        id: new Date().getTime() + 1,
        label: '选项2',
        value: '选项2'
      }
    ],
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false,
    conditionSetting: [] //隐藏题目条件判断
  },
  {
    // el-time-picker
    label: '时间选择',
    tag: 'el-time-picker',
    tagIcon: 'time',
    icon: 'timeIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    defaultValue: null,
    delFlag: 0,
    defalutSelect: 1,
    // maxlength:'',
    expression: undefined,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    'picker-options': {
      selectableRange: '00:00:00-23:59:59'
    },
    format: 'HH:mm:ss',
    'value-format': 'HH:mm:ss',
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '日期选择',
    tag: 'el-date-picker',
    tagIcon: 'date',
    icon: 'dateIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    defaultValue: null,
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    dateType: 'date',
    type: 'date',
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    // format: 'yyyy-MM-dd',
    // 'value-format': 'yyyy-MM-dd',
    'value-format': 'timestamp',
    readonly: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Date',
    // 时间预警的功能
    timeWarning: false,
    // 预警时间
    timeDate: '',
    // 预警频率
    frequency: '',
    // 预警提示语
    message: '',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '日期区间',
    tag: 'el-date-picker',
    tagIcon: 'date-range',
    icon: 'date-rangeIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    defaultValue: null,
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    dateType: 'daterange',
    type: 'daterange',
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    format: 'yyyy-MM-dd',
    'value-format': 'yyyy-MM-dd',
    'range-separator': '至',
    'start-placeholder': '开始日期',
    'end-placeholder': '结束日期',
    readonly: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    // maxlength:'',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '图片',
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    tag: 'el-upload',
    tagIcon: 'upload',
    icon: 'uploadIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: '',
    'auto-upload': true,
    showTip: false,
    // buttonText: '点击上传附件',
    fileSize: 1,
    // 图片的长宽  角标为0 是长  1 是宽
    sizeList: [],
    // 是否显示属性
    attrList: [
      { id: 1, label: 'pssb', text: '拍摄设备', isSy: false, isXr: false },
      { id: 2, label: 'pssj', text: '拍摄时间', isSy: false, isXr: false },
      { id: 3, label: 'fwj', text: '方位角', isSy: false, isXr: false },
      // {id:4,label:'jwd',text:'经纬度',isSy:false,isXr:false},
      { id: 5, label: 'wzxx', text: '位置信息', isSy: false, isXr: false },
      { id: 6, label: 'psdz', text: '拍摄地址', isSy: false, isXr: false },
      { id: 7, label: 'psry', text: '拍摄人员', isSy: false, isXr: false }
    ],
    // attrList:[],
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Pic',
    class: 'xt-uplaod-css',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    layout: 'colFormItem',
    tagIcon: 'input',
    icon: 'phoneIcon',
    label: '手机号',
    vModel: 'mobile',
    tag: 'el-input',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    defaultValue: '',
    defalutSelect: 1,
    delFlag: 0,
    expression: undefined,
    span: 24,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': 'el-icon-mobile',
    'suffix-icon': '',
    maxlength: 11,
    'show-word-limit': true,
    readonly: false,
    disabled: false,
    required: false,
    changeTag: true,
    regItem: {
      pattern: '/1[3-9]\\d{9}/',
      message: '手机号格式错误'
    },
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    layout: 'colFormItem',
    tagIcon: 'input',
    label: '身份证号码',
    vModel: 'idCrad',
    icon: 'idCardIcon',
    tag: 'el-input',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    // 判断输入提示语是否自动生成
    isPlaceholder: false,
    defaultValue: '',
    defalutSelect: 1,
    delFlag: 0,
    expression: undefined,
    span: 24,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': 'el-icon-user-solid',
    'suffix-icon': '',
    maxlength: 18,
    'show-word-limit': true,
    readonly: false,
    disabled: false,
    required: false,
    changeTag: true,
    regItem: {
      // pattern: '/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/',
      pattern: '/\\d{17}[0-9Xx]|\\d{15}/',
      message: '身份证输入错误'
    },
    inputType: 'String',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '附件',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtfj',
    icon: 'xtfjIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    defalutSelect: 1,
    expression: undefined,
    labelWidth: null,
    disabled: false,
    required: false,
    acceptType: ['xls', 'xlsx', 'doc', 'docx', 'pdf', 'txt'],
    // accept: 'image',
    name: 'file',
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传附件',
    oneFileSize: 1,
    picNum: '',
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'file',
    class: 'xt-uplaod-css',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  }
];
// 容器组件
export const layoutComponents = [
  {
    layout: 'rowFormItem',
    rowType: 'layout',
    tagIcon: 'row',
    icon: 'layoutIcon',
    type: 'default',
    justify: 'start',
    align: 'top',
    label: '布局容器',
    layoutTree: true,
    children: [],
    showDivider: true,
    content: '',
    inputType: 'List'
  }
];
export const commonComponents = [...inputComponents, ...selectComponents].map((t) => Object.assign({ cmpType: 'common' }, t));
const getConfigByTag = (targetList, tag) => targetList.find((t) => t.tag === tag);
const copyConfigAsCustom = (rowConf, childrenConf) => {
  const clone = (target, conf = {}) => {
    const template = JSON.parse(JSON.stringify(target));
    return Object.assign({}, template, { cmpType: 'custom' }, conf);
  };
  const defaultRow = clone(layoutComponents[0], rowConf);
  defaultRow.children = childrenConf.map((t) => clone(t.target, t.config));
  return defaultRow;
};

// 增强控件 身份证识别 cmpType = custom

export const customComponents = [
  {
    label: '身份证识别',
    tag: 'el-input',
    tagIcon: 'idCardScan',
    icon: 'idCardScanIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '识别的信息会自动填充到表单',
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    // maxlength: '',
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    sfzsbList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    expendList: [
      { label: 0, text: '姓名', enName: 'xm', cnName: '姓名', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      {
        label: 1,
        text: '性别',
        enName: 'xb',
        cnName: '性别',
        strLength: '',
        maxlength: '',
        inputHint: '请选择',
        valueMethod: 'radio',
        isJson: true, // 如果是数组json key value true,字符串的时候是false
        options: [
          {
            id: new Date().getTime(),
            label: '男',
            value: 1
          },
          {
            id: new Date().getTime() + 1,
            label: '女',
            value: 2
          },
          {
            id: new Date().getTime() + 2,
            label: '不详',
            value: 3
          },
          {
            id: new Date().getTime() + 3,
            label: '其他',
            value: 99
          }
        ]
      },
      { label: 2, text: '民族', enName: 'mz', cnName: '民族', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 3, text: '出生日期', enName: 'csrq', cnName: '出生日期', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'date' },
      { label: 4, text: '住址', enName: 'zz', cnName: '住址', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 5, text: '身份证号', enName: 'sfzh', cnName: '身份证号', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 6, text: '签发机关', enName: 'qfjg', cnName: '签发机关', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      {
        label: 7,
        text: '有效期限',
        enName: 'yxqx',
        cnName: '有效期限',
        strLength: '',
        maxlength: '',
        inputHint: '请输入',
        valueMethod: 'date-range'
      },
      {
        label: 8,
        text: '身份证正面',
        enName: 'sfzzm',
        cnName: '身份证正面',
        strLength: undefined,
        maxlength: undefined,
        inputHint: '请输入',
        valueMethod: 'idCardBitmap'
      },
      {
        label: 9,
        text: '身份证反面',
        enName: 'sfzfm',
        cnName: '身份证反面',
        strLength: undefined,
        maxlength: undefined,
        inputHint: '请输入',
        valueMethod: 'idCardBitmap'
      }
    ],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Integer[]',
    rowType: 'SFZSB',
    vModel: 'SFZSB'
  },
  {
    label: '行政区域',
    tag: 'el-cascader',
    tagIcon: 'area',
    icon: 'areaIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    defaultValue: [],
    delFlag: 0,
    span: 24,
    labelWidth: null,
    maxlength: '',
    style: { width: '100%' },
    props: {
      props: {
        checkStrictly: true,
        value: 'value',
        label: 'label',
        children: 'children'
      }
    },
    'show-all-levels': true,
    disabled: false,
    clearable: true,
    filterable: false,
    required: false,
    // options: PROVINCE,
    // areaOptions:PROVINCE,
    options: [],
    areaOptions: [],
    dataType: 'static',
    labelKey: 'label',
    valueKey: 'value',
    childrenKey: 'children',
    separator: '/',
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String',
    isHide: false,
    rowType: 'XZQYCom',
    xzqyLevel: 4
  },
  {
    label: '签名',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtqm',
    icon: 'xtqmIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: 0,
    esign: true,
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传签名',
    fileSize: 1,
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Pic',
    class: 'xt-uplaod-css',
    rowType: 'SIGNATURE',
    cmpType: 'custom',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '指纹',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtzw',
    icon: 'xtzwIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: 0,
    'auto-upload': true,
    showTip: false,
    fileSize: 1,
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Pic',
    class: 'xt-uplaod-css',
    rowType: 'FINGERPRINT',
    cmpType: 'custom'
  },
  {
    label: '植物识别',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtzwsb',
    icon: 'xtzwsbIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: 0,
    'auto-upload': true,
    showTip: false,
    fileSize: 1,
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    sbList: [0, 1, 2, 3],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Integer[]',
    class: 'xt-uplaod-css',
    rowType: 'PLANT',
    cmpType: 'custom'
  },
  {
    label: '动物识别',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtdwsb',
    icon: 'xtdwsbIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: [],
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: 0,
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传附件',
    fileSize: 1,
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    sbList: [0, 1, 2, 3],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Integer[]',
    class: 'xt-uplaod-css',
    rowType: 'ANIMAL',
    cmpType: 'custom'
  },
  {
    label: '视频',
    placeholder: '请上传视频',
    tag: 'el-upload',
    tagIcon: 'xtvideo',
    icon: 'xtvideoIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: null,
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: '',
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传视频',
    fileSize: 30,
    timeSize: 30,
    sizeUnit: 'S',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'video',
    class: 'xt-uplaod-css',
    rowType: 'XTVIDEO',
    cmpType: 'custom'
  },
  {
    label: '音频',
    placeholder: '请上传音频',
    tag: 'el-upload',
    tagIcon: 'xtaudio',
    icon: 'xtaudioIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: null,
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: '',
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传视频',
    fileSize: '',
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'audio',
    class: 'xt-uplaod-css',
    rowType: 'XTAUDIO',
    cmpType: 'custom'
  },
  // {
  //   layout: 'colFormItem',
  //   tagIcon: 'xtlxr',
  //   icon: 'xtlxrIcon',
  //   // 字段的生效和失效  0  生效  1 失效
  //   status: 1,
  //   label: '联系人',
  //   vModel: 'mobile',
  //   tag: 'el-xtlxr',
  //   placeholder: '',
  //   defaultValue: '',
  //   defalutSelect: 1,
  //   delFlag: 0,
  //   expression: undefined,
  //   span: 24,
  //   style: { width: '100%' },
  //   clearable: true,
  //   prepend: '',
  //   append: '',
  //   'prefix-icon': 'el-icon-mobile',
  //   'suffix-icon': '',
  //   maxlength: 11,
  //   'show-word-limit': true,
  //   readonly: false,
  //   disabled: false,
  //   required: false,
  //   changeTag: true,
  //   regItem: {
  //     pattern: '/1[3-9]\\d{9}/',
  //     message: '手机号格式错误'
  //   },
  //   inputType: 'String',
  //   // child  选中这个组件的时候会出现2个组件
  //   childrenList: [
  //     {
  //       label: '姓名',
  //       tag: 'el-input',
  //       tagIcon: 'input',
  //       icon: 'inputIcon',
  //       placeholder: '请输入姓名',
  //       defalutSelect: 1,
  //       expression: undefined,
  //       defaultValue: undefined,
  //       delFlag: 0,
  //       span: 24,
  //       labelWidth: null,
  //       style: { width: '100%' },
  //       clearable: true,
  //       prepend: '',
  //       append: '',
  //       'prefix-icon': '',
  //       'suffix-icon': '',
  //       maxlength: 100,
  //       'show-word-limit': false,
  //       readonly: false,
  //       disabled: false,
  //       required: false,
  //       regList: [],
  //       changeTag: true,
  //       proCondition: false,
  //       asSummary: false,
  //       content: '',
  //       inputType: 'String',
  //       vModel: 'xtUserName'
  //     },
  //     {
  //       layout: 'colFormItem',
  //       tagIcon: 'input',
  //       icon: 'phoneIcon',
  //       label: '手机号',
  //       vModel: 'xtMobile',
  //       tag: 'el-input',
  //       placeholder: '请输入手机号',
  //       defaultValue: '',
  //       defalutSelect: 1,
  //       delFlag: 0,
  //       expression: undefined,
  //       span: 24,
  //       style: { width: '100%' },
  //       clearable: true,
  //       prepend: '',
  //       append: '',
  //       'prefix-icon': 'el-icon-mobile',
  //       'suffix-icon': '',
  //       maxlength: 11,
  //       'show-word-limit': true,
  //       readonly: false,
  //       disabled: false,
  //       required: false,
  //       changeTag: true,
  //       regItem: {
  //         pattern: '/1[3-9]\\d{9}/',
  //         message: '手机号格式错误'
  //       },
  //       inputType: 'String'
  //     }
  //   ]
  // },
  {
    layout: 'rowFormItem',
    icon: 'xttableIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    rowType: 'table',
    tagIcon: 'xttable',
    tag: 'el-table',
    type: 'default',
    justify: 'start',
    align: 'top',
    label: '表格',
    layoutTree: true,
    children: [],
    showDivider: true,
    content: '',
    inputType: 'List',
    // 填写方式table 表格 list 列表
    tableType: 'table',
    attrExpend: {
      attrs: [],
      tableList: []
    },
    required: false,
    defaultRow: 0, //默认行
    openMonthArea: false, //是否开启月份期间 用于计算超期过渡费
    placeholder: '请上传表格'
  },
  {
    label: '数据截图',
    placeholder: '',
    tag: 'el-upload',
    tagIcon: 'xtsjjt',
    icon: 'xtsjjtIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    action: `${baseUrl}/qjt/file/multi/upload`,
    defaultValue: null,
    delFlag: 0,
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    picNum: '',
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传',
    fileSize: '',
    sizeUnit: 'MB',
    'list-type': 'picture-card',
    multiple: false,
    regList: [],
    // 图片的长宽  角标为0 是长  1 是宽
    sizeList: [],
    // 同级当前草图的节点id
    attrIdList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'audio',
    class: 'xt-uplaod-css',
    rowType: 'XTAUDIO',
    cmpType: 'custom',
    // 是否单次刷新表达式
    isOnceRefeshExpression: false
  },
  {
    label: '数据源',
    tag: 'el-input',
    tagIcon: 'xtsjy',
    icon: 'inputIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: '',
    // 数据源的数据
    sjyList: ['graph', 'shpData', 'perv'],
    sjyCheckList: [
      { value: 'graph', label: '图形' },
      { value: 'shpData', label: 'SHP数据' },
      { value: 'perv', label: '前面步骤结果' }
    ],
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String'
  },
  {
    label: '追溯地类',
    tag: 'el-input',
    tagIcon: 'xtzsdl',
    icon: 'inputIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: '',
    // 地类的数据
    dileiList: [
      { label: '旱地', key: ['0103', '013'], id: 1 },
      { label: '水浇地', key: ['0102', '012'], id: 2 },
      { label: '水田', key: ['0101', '011'], id: 3 },
      { label: '果园', key: ['0201', '021'], id: 4 },
      { label: '茶园', key: ['0202', '022'], id: 5 },
      { label: '其他园地', key: ['0204', '023'], id: 6 },
      { label: '乔木林地', key: ['0301', '031'], id: 7 },
      { label: '灌木林地', key: ['0305', '032'], id: 8 },
      { label: '竹林地', key: ['0302'], id: 9 },
      { label: '其他林地', key: ['0307', '033'], id: 10 },
      { label: '天然牧草地(不包含其他草地)', key: ['0401', '041'], id: 11 },
      { label: '设施农用地', key: ['1202', '122'], id: 12 },
      { label: '农村道路', key: ['1006', '104'], id: 13 },
      { label: '水域及水利设施用地', key: ['1103', '1104', '1107'], id: 14 },
      {
        label: '建设用地',
        key: [
          '102',
          '202',
          '203',
          '204',
          '05H1',
          '0508',
          '0601',
          '0602',
          '0603',
          '0701',
          '0702',
          '08H1',
          '08H2',
          '0809',
          '0810',
          '09',
          '1001',
          '1002',
          '1003',
          '1004',
          '1005',
          '1007',
          '1008',
          '1009',
          '1109',
          '1201'
        ],
        id: 15
      }
      // {label:'田坎',key:["000"],id:16},
      // {label:'未利用地',key:[""],id:17},
    ],
    dileiTypelist: [],
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String'
  },
  {
    label: '成员选择',
    tag: 'el-input',
    tagIcon: 'xtcy',
    icon: 'inputIcon',
    // 字段的生效和失效  0  生效  1 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: '',
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'String'
  },
  {
    label: '缴费',
    tag: 'el-button',
    tagIcon: 'xtpay',
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { border: 'none', padding: '3px 7px' },
    clearable: true,
    prepend: '',
    type: 'primary',
    icon: 'button',
    round: false,
    size: 'mini',
    plain: false,
    circle: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Pay',
    paySelectList: [0, 1, 2, 3, 4, 5, 6, 7, 8],
    payList: [
      { label: 0, text: '唯一标识', enName: 'uniqueId', cnName: '唯一标识', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 1, text: '订单号', enName: 'orderNum', cnName: '订单号', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 2, text: '金额', enName: 'price', cnName: '金额', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'number' },
      {
        label: 3,
        text: '缴费状态',
        enName: 'status',
        cnName: '缴费状态',
        strLength: '',
        maxlength: '',
        inputHint: '请选择',
        valueMethod: 'radio',
        options: [
          {
            id: new Date().getTime(),
            label: '未缴费',
            value: '未缴费'
          },
          {
            id: new Date().getTime() + 1,
            label: '缴费中',
            value: '缴费中'
          },
          {
            id: new Date().getTime() + 2,
            label: '已缴费',
            value: '已缴费'
          },
          {
            id: new Date().getTime() + 3,
            label: '退费中',
            value: '退费中'
          },
          {
            id: new Date().getTime() + 4,
            label: '已退费',
            value: '已退费'
          },
          {
            id: new Date().getTime() + 5,
            label: '退费失败',
            value: '退费失败'
          },
          {
            id: new Date().getTime() + 5,
            label: '缴费失败',
            value: '缴费失败'
          }
        ]
      },
      { label: 4, text: '支付时间', enName: 'pTime', cnName: '支付时间', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 5, text: '退费时间', enName: 'rTime', cnName: '退费时间', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      { label: 6, text: '退费操作员', enName: 'op', cnName: '退费操作员', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' },
      {
        label: 7,
        text: '缴费手机号',
        enName: 'phone',
        cnName: '缴费手机号',
        strLength: '11',
        maxlength: '11',
        inputHint: '请输入',
        valueMethod: 'input'
      },
      { label: 8, text: '数字', enName: 'num', cnName: '数字', strLength: '', maxlength: '', inputHint: '请输入', valueMethod: 'input' }
    ]
  },
  {
    label: '银行卡识别',
    tag: 'el-input',
    tagIcon: 'xtBankCard',
    icon: 'inputIcon',
    // 字段的生效和失效  1 生效  0 失效
    status: 1,
    placeholder: '',
    isPlaceholder: false,
    defalutSelect: 1,
    expression: undefined,
    defaultValue: undefined,
    delFlag: 0,
    span: 24,
    labelWidth: null,
    style: { border: 'none', padding: '3px 7px' },
    clearable: true,
    prepend: '',
    type: 'primary',
    round: false,
    size: 'mini',
    plain: false,
    circle: false,
    disabled: true,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    inputType: 'Integer[]',
    bankCardSelectList: [0, 1, 2, 3],
    expendList: [
      {
        label: 0,
        text: '银行卡名称',
        enName: 'yhkName',
        cnName: '银行卡名称',
        strLength: '',
        maxlength: '',
        inputHint: '请输入',
        valueMethod: 'input'
      },
      {
        label: 1,
        text: '银行卡卡号',
        enName: 'yhkNum',
        cnName: '银行卡卡号',
        strLength: '',
        maxlength: '',
        inputHint: '请输入',
        valueMethod: 'input'
      },
      {
        label: 2,
        text: '银行卡类型',
        enName: 'yhkType',
        cnName: '银行卡类型',
        strLength: '',
        maxlength: '',
        inputHint: '请输入',
        valueMethod: 'input'
      },
      {
        label: 3,
        text: '银行卡照片',
        enName: 'yhkImg',
        cnName: '银行卡照片',
        strLength: undefined,
        maxlength: undefined,
        inputHint: '请输入',
        valueMethod: 'BankCardBitmap'
      }
    ]
  }
];
// .map( t => Object.assign( { cmpType: 'custom', t } ) )
// 组件rule的触发方式，无触发方式的组件不生成rule
// inputTable组件也有一份此常量的copy 如有改动 最好同步
export const trigger = {
  'el-input': 'blur',
  'el-input-number': 'blur',
  'el-select': 'change',
  'el-radio-group': 'change',
  'el-checkbox-group': 'change',
  'el-cascader': 'change',
  'el-time-picker': 'change',
  'el-date-picker': 'change',
  'el-rate': 'change',
  'el-upload': 'change',
  'xt-xtlxr': 'change'
};
