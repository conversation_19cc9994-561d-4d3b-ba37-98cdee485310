<!-- 任务数据选择 -->
<template>
  <div>
    <el-dialog
      :title="isManager ? '数据管理' : '数据选择'"
      v-model="dialogVisible"
      width="90%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="handleClose"
      border
    >
      <div class="task-main" :style="{ height: bodyHeight }">
        <div class="left">
          <div class="dialog-search">
            <el-button type="primary" size="default" @click="searchBtn">筛选数据</el-button>
            <div style="margin-left: 10px" v-show="isShowSearch">
              已筛选<span type="primary" @click="searchBtn" style="margin-left: 5px; color: #1890ff; cursor: pointer"
                >展开<i class="el-icon-arrow-down"></i
              ></span>
            </div>
          </div>
          <div style="color: #ff3434; margin: 10px 0px; font-size: 12px">注：灰色数据表示已在其他任务执行！括号里即为任务名称。</div>
          <el-checkbox v-model="isCheckedAll" @change="changeCheckAll">全选(当前列表)</el-checkbox>
          <div class="user-box box-left">
            <div class="user-item" v-for="(item, index) in parcelList" :key="index" @click="showOne(item, true)" style="padding: 0px">
              <div class="gary-span" v-show="item.taskId">
                <el-checkbox style="margin-right: 5px" v-model="item.checked" @change="() => leftChooseOne(item)" disabled></el-checkbox>
                {{ item.parcelName }}({{ item.taskName }})
              </div>
              <div class="gree-span" v-show="!item.taskId">
                <el-checkbox style="margin-right: 5px" v-model="item.checked" @change="() => leftChooseOne(item)"></el-checkbox>
                {{ item.parcelName }}
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div id="viewDiv" ref="gisMap" class="map"></div>
          <div class="tuli-box">
            <div class="tuli-row tuli-title">图例：</div>
            <div class="tuli-row">
              <div class="tuli-right-gree"></div>
              <div class="label">未分配数据</div>
            </div>
            <div class="tuli-row">
              <div class="tuli-right-red"></div>
              <div class="label">其他任务数据</div>
            </div>
            <div class="tuli-row">
              <div class="tuli-spe">
                <div class="tuli-right-hight"></div>
              </div>
              <div class="label">当前选择数据</div>
            </div>
          </div>
          <div class="min-handle-label" @click="changeLabel">
            <span v-show="!showLable">标注</span>
            <span v-show="showLable">取消标注</span>
          </div>
          <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
            <svg-icon icon-class="map" />
          </div>
          <div class="map-change" v-show="showChangeMap">
            <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
              <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">天地图地图矢量</div>
            </div>
            <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
              <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">天地图影像</div>
            </div>
          </div>
        </div>
        <div class="left end" v-show="multipleSelection.length != 0">
          <div class="handle-row">
            <div>已选数据({{ multipleSelection.length }}条)</div>
            <el-link type="primary" @click="removeAllLayer">一键移除</el-link>
          </div>
          <div class="user-box user-end">
            <div class="user-item" v-for="(item, index) in multipleSelection" :key="index" @click="showOne(item, false)">
              {{ item.parcelName }}
              <div @click.prevent="removeOne(item, index)">
                <el-link type="primary">移除</el-link>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-page">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="dialogSearch.pageNum"
            :page-sizes="[10, 20, 50, 100, 200, 500, 1000, 2000]"
            :page-size="dialogSearch.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit" v-if="!isManager">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选弹窗 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      :moduleId="moduleIdPop"
      @editCondition="editCondition"
    ></dataSearch>
    <!-- 标注弹窗 -->
    <LabelDialog :labelDialog="labelDialog" :moduleId="moduleIdPop" @handleCloseLabel="handleCloseLabel" @submitField="submitField"></LabelDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { getPlaceList, selectFields } from '@/api/modal';
import dataSearch from '@/components/dataSearch/index.vue';
import { getSearchTask } from '@/api/task';
import { operaParcel } from '@/api/project';
import { loadModules } from 'esri-loader';
import LabelDialog from '../../views/autoProject/components/labelDialog.vue';
import { ElMessageBox, ElMessage } from 'element-plus';

interface Props {
  searchDialog: boolean;
  zdList: any[];
  isManager: boolean;
  moduleIdPop: string;
  taskId: string;
  taskName: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['closeSearchDialog', 'getChooseData']);

const dialogVisible = ref(props.searchDialog);

watch(
  () => props.searchDialog,
  (newVal) => {
    dialogVisible.value = newVal;
  }
);

const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

const tiandituBaseUrl = 'http://{subDomain}.tianditu.gov.cn';
const token = 'f250cc0a2b1fe177a3a5b8ce821a6c8d';

let mapInstance: any = null;
let viewInstance: any = null;
let graphicsLayerInstance: any = null;
let sketchLayerInstance: any = null;
let tiledLayerInstance: any = null;
let tiledLayerAnnoInstance: any = null;
let normalLayerInstance: any = null;
let normalAnnoInstance: any = null;
let labelLayerInstance: any = null;

const fullscreenLoading = ref(false);
const parcelList = ref<any[]>([]);
const total = ref(0);
const multipleSelection = ref<any[]>([]);
const dialogSearch = ref({
  areaCode: '',
  createDate: '',
  optUserId: '',
  createUserId: '',
  updateUserId: '',
  taskId: '',
  allocation: '',
  pageNum: 1,
  pageSize: 100,
  parcelName: '',
  moduleId: '',
  conditionFields: [] as Array<{
    name: string;
    operator: string;
    value: string;
    index: string;
    relation: string;
    type: number;
    linkId: undefined | string | number;
  }>,
  ifTp: false,
  ruleIds: [],
  parcelCode: null as string | string[] | null,
  createTimeStart: '',
  createTimeEnd: '',
  updateTimeStart: '',
  updateTimeEnd: '',
  createUserName: '',
  optUserName: '',
  express: false //是否查询表达式异常的数据
});
const shaixuanDialog = ref(false);
const taskList = ref<any[]>([]);
const isShowSearch = ref(false);
const isInit = ref(true);
const bodyHeight = ref(window.innerHeight - 260 + 'px');
const showChangeMap = ref(false);
const checkedMap = ref('image');
const HighlightList = ref<any[]>([]);
const rellayRemoves = ref<any[]>([]);
const isCenter = ref(false);
const showLable = ref(false);
const labelDialog = ref(false);
const isCheckedAll = ref(false);

const map = ref(null);
const view = ref(null);
const gisMap = ref<HTMLElement | null>(null);

const init = (flg: boolean, isLook: boolean) => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine',
      'esri/widgets/Sketch',
      'esri/layers/FeatureLayer'
    ],
    config
  ).then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine, Sketch]) => {
    tiledLayerInstance = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    tiledLayerAnnoInstance = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cia_w?T=vec_c/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    normalLayerInstance = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    normalAnnoInstance = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    mapInstance = new Map({
      basemap: {
        baseLayers: [tiledLayerInstance, tiledLayerAnnoInstance, normalLayerInstance, normalAnnoInstance]
      },
      logo: false,
      spatialReference: {
        wkid: 102100
      }
    });

    viewInstance = new MapView({
      container: gisMap.value,
      map: mapInstance,
      center: [116.39126, 39.90763],
      zoom: 6,
      ui: {
        components: []
      },
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    viewInstance.constraints = {
      minZoom: 2,
      maxZoom: 24
    };

    const compassWidget = new Compass({ view: viewInstance });
    viewInstance.ui.add(compassWidget, 'bottom-right');
    const zoom = new Zoom({
      view: viewInstance
    });
    viewInstance.ui.add(zoom, 'bottom-right');
    viewInstance.ui.remove('attribution');
    const scaleBar = new ScaleBar({
      view: viewInstance,
      unit: 'metric',
      style: 'line'
    });
    viewInstance.ui.add(scaleBar, {
      position: 'bottom-right'
    });

    graphicsLayerInstance = new GraphicsLayer({
      id: '123'
    });
    mapInstance.add(graphicsLayerInstance);

    sketchLayerInstance = new GraphicsLayer({
      id: 'sketch-1'
    });
    mapInstance.add(sketchLayerInstance);

    labelLayerInstance = new GraphicsLayer({
      id: 'label-1',
      minScale: 5000
    });
    mapInstance.add(labelLayerInstance);

    viewInstance.when(() => {
      const sketch = new Sketch({
        layer: sketchLayerInstance,
        view: viewInstance,
        creationMode: 'update',
        availableCreateTools: ['polygon']
      });
      sketch.visibleElements = {
        selectionTools: {
          'lasso-selection': false,
          'rectangle-selection': false
        },
        settingsMenu: false
      };
      viewInstance.ui.add(sketch, 'top-left');
      getData(dialogSearch.value);
      sketch.on('update', function (event: any) {
        sketch.visible = true;
        const rectangle = event.graphics[0];
        if (event.state == 'start') {
          comparisonGeometry(rectangle, true);
        }
      });
    });
  });
};

const comparisonGeometry = (rectangle: any, flg: boolean) => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine',
      'esri/widgets/Sketch',
      'esri/symbols/TextSymbol'
    ],
    config
  ).then(
    ([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine, Sketch, TextSymbol]) => {
      graphicsLayerInstance.graphics.items.forEach((graphic: any) => {
        if (geometryEngine.contains(rectangle.geometry, graphic.geometry)) {
          if (!graphic.attributes.taskId) {
            if (!multipleSelection.value.some((item) => item.id === graphic.attributes.id)) {
              multipleSelection.value.push({
                geomArcgis: graphic.attributes.geomArcgis,
                id: graphic.attributes.id,
                parcelName: graphic.attributes.parcelName,
                relationId: graphic.attributes.relationId || null,
                taskId: graphic.attributes.taskId || null,
                userId: graphic.attributes.userId || null,
                custName: graphic.attributes.custName || null
              });
              for (let i = 0; i < parcelList.value.length; i++) {
                if (parcelList.value[i].id == graphic.attributes.id) {
                  parcelList.value[i].taskId = props.taskId;
                  parcelList.value[i].taskName = props.taskName;
                  break;
                }
              }
            }
          }
        }
      });
      HighlightList.value.forEach((v) => {
        v.remove();
      });
      highlightZD();
      sketchLayerInstance.removeAll();
    }
  );
};

const drawAll = (flg: boolean, isLook: boolean) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    graphicsLayerInstance.removeAll();
    parcelList.value.forEach((v, idx) => {
      let outLineColor = [255, 0, 0];
      if (!v.taskId) {
        outLineColor = [84, 255, 159];
      }
      if (v.geomArcgis) {
        try {
          const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
          const attributes = {
            id: v.id,
            parcelName: v.parcelName,
            index: idx,
            geomArcgis: v.geomArcgis,
            relationId: v.relationId,
            taskId: v.taskId,
            userId: v.userId,
            custName: v.custName,
            type: polygon.type
          };

          let graphic;
          if (polygon.type === 'point') {
            const textSymbol = {
              type: 'simple-marker',
              color: outLineColor,
              width: 2,
              size: 8
            };
            graphic = new Graphic({
              geometry: polygon,
              symbol: textSymbol,
              attributes: attributes
            });
          } else if (polygon.type === 'line' || polygon.type === 'polyline') {
            const lineSymbol = {
              type: 'simple-line',
              color: outLineColor,
              width: 2,
              style: 'solid'
            };
            graphic = new Graphic({
              geometry: polygon,
              symbol: lineSymbol,
              attributes: attributes
            });
          } else if (polygon.type === 'polygon') {
            const simpleFillSymbol = {
              type: 'simple-fill',
              color: [240, 230, 140, 0.2],
              outline: {
                color: outLineColor,
                width: 2,
                style: 'solid'
              }
            };
            graphic = new Graphic({
              geometry: polygon,
              symbol: simpleFillSymbol,
              attributes: attributes
            });
          }

          if (graphic) {
            graphicsLayerInstance.add(graphic);

            // 如果是选中状态，立即添加高亮
            if (multipleSelection.value.some((item) => item.id === v.id)) {
              viewInstance.whenLayerView(graphic.layer).then(function (layerView: any) {
                const highlight = layerView.highlight(graphic);
                if (highlight) {
                  HighlightList.value.push({
                    id: v.id,
                    remove: highlight.remove.bind(highlight)
                  });
                }
              });
            }

            // 设置中心点
            if (!isCenter.value) {
              if (polygon.type === 'point') {
                viewInstance.center = [polygon.longitude, polygon.latitude];
              } else if ((polygon.type === 'line' || polygon.type === 'polyline') && polygon.extent) {
                const centerX = (polygon.extent.xmin + polygon.extent.xmax) / 2;
                const centerY = (polygon.extent.ymin + polygon.extent.ymax) / 2;
                viewInstance.center = [centerX, centerY];
              } else if (polygon.type === 'polygon' && polygon.centroid) {
                viewInstance.center = [polygon.centroid.longitude, polygon.centroid.latitude];
              }
              viewInstance.zoom = 18;
              isCenter.value = true;
            }
          }
        } catch (error) {
          console.error('处理图形时出错:', error, v);
        }
      }
    });
  });
};

const highlightZD = () => {
  // 先清除所有现有的高亮
  HighlightList.value.forEach((highlight) => {
    if (highlight && highlight.remove) {
      highlight.remove();
    }
  });
  HighlightList.value = [];

  // 重新添加高亮
  graphicsLayerInstance.graphics.items.forEach((v: any) => {
    if (multipleSelection.value.some((obj) => obj.id === v.attributes.id)) {
      viewInstance.whenLayerView(v.layer).then(function (layerView: any) {
        const highlight = layerView.highlight(v);
        if (highlight) {
          HighlightList.value.push({
            id: v.attributes.id,
            remove: highlight.remove.bind(highlight)
          });
        }
      });
    }
  });
};

const changeMap = (type: number) => {
  if (type == 1) {
    checkedMap.value = 'normal';
    tiledLayerInstance.visible = false;
    tiledLayerAnnoInstance.visible = false;
    normalLayerInstance.visible = true;
    normalAnnoInstance.visible = true;
  } else if (type == 2) {
    checkedMap.value = 'image';
    tiledLayerInstance.visible = true;
    tiledLayerAnnoInstance.visible = true;
    normalLayerInstance.visible = false;
    normalAnnoInstance.visible = false;
  }
};

const getData = (parmas: any) => {
  fullscreenLoading.value = true;
  if (parmas.ruleIds && parmas.ruleIds.length == 0) {
    parmas.levelNum = 1;
  }
  getPlaceList(parmas, { moudleId: dialogSearch.value.moduleId }).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      parcelList.value = [];
      res.data.list.forEach((v: any) => {
        const foundItem = multipleSelection.value.find((item) => item.id === v.id);
        if (foundItem) {
          v.custName = foundItem.custName;
          v.taskId = foundItem.taskId;
          v.userId = foundItem.userId;
          v.relationId = foundItem.relationId;
          v.checked = true;
          if (!foundItem.relationId) {
            v.taskId = props.taskId;
            v.taskName = props.taskName;
          }
        } else {
          v.checked = false; // 确保未选中的数据checkbox状态为false
        }
        if (rellayRemoves.value.some((obj) => obj == v.id)) {
          v.taskId = null;
          v.taskName = null;
          v.checked = false; // 确保被移除的数据checkbox状态为false
        }
        parcelList.value.push(v);
      });

      total.value = res.data.total;
      drawAll(false, false);

      // 更新全选状态
      updateCheckAllState();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 添加更新全选状态的方法
const updateCheckAllState = () => {
  const availableItems = parcelList.value.filter((item) => !item.taskId); // 只考虑未分配任务的数据
  if (availableItems.length === 0) {
    isCheckedAll.value = false;
    return;
  }
  isCheckedAll.value = availableItems.every((item) => item.checked);
};

const getModuleId = () => {
  if (props.moduleIdPop) {
    return props.moduleIdPop;
  } else {
    // Assuming there's a store with project.proModuleId
    // This might need to be adjusted based on your actual store implementation
    return '';
  }
};

const handleClose = () => {
  // 先重置数据
  dialogSearch.value = {
    areaCode: '',
    createDate: '',
    optUserId: '',
    createUserId: '',
    updateUserId: '',
    taskId: '',
    allocation: '',
    pageNum: 1,
    pageSize: 100,
    parcelName: '',
    moduleId: getModuleId(),
    conditionFields: [],
    ifTp: false,
    ruleIds: [],
    parcelCode: null,
    createTimeStart: '',
    createTimeEnd: '',
    updateTimeStart: '',
    updateTimeEnd: '',
    createUserName: '',
    optUserName: ''
  };
  multipleSelection.value = [];
  isInit.value = true;

  // 设置dialogVisible为false，但不触发事件
  dialogVisible.value = false;

  // 使用setTimeout确保关闭事件只触发一次
  setTimeout(() => {
    emit('closeSearchDialog');
  }, 0);
};

const submit = () => {
  if (multipleSelection.value.length > 3000) {
    ElMessageBox.alert('您选择的数据大于3000条，为了有更好的体验，建议每次任务绑定数据不要超过3000条！！！', '提示', {
      confirmButtonText: '确定',
      callback: () => {}
    });
  }

  emit('getChooseData', multipleSelection.value);
  multipleSelection.value = [];
  isInit.value = true;
};

const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

const submitSearch = () => {
  if (dialogSearch.value.parcelCode) {
    if (typeof dialogSearch.value.parcelCode === 'string') {
      dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
    }
  }
  if (dialogSearch.value.taskId) {
    dialogSearch.value.allocation = 'true';
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.value.allocation = '';
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  // Handle updateTimeStart and updateTimeEnd directly
  // No need to reference updateDate which doesn't exist in the dialogSearch object

  const conditionFields: any[] = [];
  const itemParmas = JSON.parse(JSON.stringify(dialogSearch.value));
  if (itemParmas.conditionFields && itemParmas.conditionFields.length != 0) {
    itemParmas.conditionFields.forEach((v: any, idx: any) => {
      conditionFields.push(v);
      if (idx != itemParmas.conditionFields.length - 1) {
        const obj = {
          type: 2,
          value: itemParmas.conditionFields[idx + 1].relation
        };
        conditionFields.push(obj);
      }
    });
    if (conditionFields.length != 0 && conditionFields[conditionFields.length - 1].type == 2) {
      conditionFields.pop();
    }
    itemParmas.conditionFields = conditionFields;
  }
  getData(itemParmas);
  const val = dialogSearch.value;
  if (val.areaCode || val.createDate || val.optUserId || val.createUserId || val.updateUserId || val.taskId || val.parcelName) {
    isShowSearch.value = true;
  } else {
    isShowSearch.value = false;
  }
  shaixuanDialog.value = false;
};

const clearUser = (type: number) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '',
    createDate: '',
    optUserId: '',
    createUserId: '',
    updateUserId: '',
    taskId: '',
    allocation: '',
    pageNum: 1,
    pageSize: 100,
    parcelName: '',
    moduleId: getModuleId(),
    conditionFields: [],
    ifTp: false,
    ruleIds: [],
    parcelCode: null,
    createTimeStart: '',
    createTimeEnd: '',
    updateTimeStart: '',
    updateTimeEnd: '',
    createUserName: '',
    optUserName: '',
    express: false //是否查询表达式异常的数据
  };
  isShowSearch.value = false;
  getData(dialogSearch.value);
  shaixuanDialog.value = false;
};

const editCondition = (type: number, idx: number) => {
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '',
      operator: '=',
      value: '',
      index: '',
      relation: 'and',
      type: 1,
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: dialogSearch.value.moduleId
  };
  if (props.moduleIdPop) {
    params.moduleId = props.moduleIdPop;
  }
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
      fullscreenLoading.value = false;
    }
  });
};

const searchBtn = async () => {
  shaixuanDialog.value = true;
  await getSearchTaskList();
};

const itemRemoveOne = (item: any, index: number) => {
  multipleSelection.value.splice(index, 1);
  for (let i = 0; i < HighlightList.value.length; i++) {
    if (HighlightList.value[i].id == item.id) {
      HighlightList.value[i].remove();
      if (!rellayRemoves.value.some((obj) => obj == item.id)) {
        rellayRemoves.value.push(item.id);
      }
      break;
    }
  }
  for (let i = 0; i < parcelList.value.length; i++) {
    if (parcelList.value[i].id == item.id) {
      parcelList.value[i].taskId = null;
      parcelList.value[i].taskName = null;
      parcelList.value[i].checked = false;
      break;
    }
  }
};

const removeOne = (item: any, index: number) => {
  ElMessageBox.confirm('移除数据后该数据绑定的任务人员关系会被清空,确定还要移除该条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      itemRemoveOne(item, index);
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
      drawAll(false, false);
    })
    .catch(() => {});
};

const handleSizeChange = (val: number) => {
  dialogSearch.value.pageSize = val;
  dialogSearch.value.pageNum = 1;
  submitSearch();
};

const handleCurrentChange = (val: number) => {
  dialogSearch.value.pageNum = val;
  submitSearch();
};

const removeAllLayer = () => {
  ElMessageBox.confirm('移除数据后该数据绑定的任务人员关系会被清空,确定要移除所有数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 清除高亮显示
      HighlightList.value.forEach((v) => {
        v.remove();
      });
      HighlightList.value = [];

      // 记录被移除的数据ID
      rellayRemoves.value = [];
      multipleSelection.value.forEach((v) => {
        rellayRemoves.value.push(v.id);
      });

      // 清空选中数据
      multipleSelection.value = [];

      // 更新左侧列表数据状态
      parcelList.value.forEach((v) => {
        if (rellayRemoves.value.some((obj) => obj == v.id)) {
          v.taskId = null;
          v.taskName = null;
          v.userId = null;
          v.custName = null;
          v.checked = false; // 重置选中状态
        }
      });

      // 重置全选状态
      isCheckedAll.value = false;

      // 重绘地图
      drawAll(false, false);

      ElMessage({
        type: 'success',
        message: '操作成功'
      });
    })
    .catch(() => {});
};

const showOne = (item: any, flg: boolean) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const polygon = jsonUtils.fromJSON(JSON.parse(item.geomArcgis));
    if (polygon.type == 'point') {
      viewInstance.center = [polygon.longitude, polygon.latitude];
      viewInstance.zoom = 18;
    } else if (polygon.type == 'polyline') {
      viewInstance.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
    } else if (polygon.type == 'polygon') {
      viewInstance.center = [polygon.centroid.longitude, polygon.centroid.latitude];
    }
  });
};

const drawLabel = (list: any[]) => {
  if (!labelLayerInstance) {
    console.warn('标注图层未初始化');
    return;
  }

  // 清空现有标注
  labelLayerInstance.removeAll();

  // 如果没有数据，直接返回
  if (!list || list.length === 0) {
    return;
  }

  loadModules(['esri/Graphic', 'esri/geometry/Point', 'esri/geometry/support/jsonUtils', 'esri/geometry/SpatialReference'], config)
    .then(([Graphic, Point, jsonUtils, SpatialReference]) => {
      list.forEach((item) => {
        try {
          if (!item.geomArcgis || !item.linkIdValue) {
            return;
          }

          // 解析几何数据
          const geometry = jsonUtils.fromJSON(JSON.parse(item.geomArcgis));

          // 创建标注符号
          const textSymbol = {
            type: 'text',
            text: item.linkIdValue,
            color: [255, 0, 0],
            haloSize: '1px',
            xoffset: 0,
            yoffset: 0,
            font: {
              family: 'Arial',
              size: 12,
              style: 'italic',
              weight: 'bold'
            }
          };

          // 根据几何类型获取中心点
          let centerPoint;
          if (geometry.type === 'point') {
            centerPoint = new Point({
              x: geometry.x,
              y: geometry.y,
              spatialReference: { wkid: 102100 }
            });
          } else if (geometry.type === 'polygon' && geometry.centroid) {
            centerPoint = new Point({
              x: geometry.centroid.x,
              y: geometry.centroid.y,
              spatialReference: { wkid: 102100 }
            });
          } else if (geometry.extent && geometry.extent.center) {
            centerPoint = new Point({
              x: geometry.extent.center.x,
              y: geometry.extent.center.y,
              spatialReference: { wkid: 102100 }
            });
          }

          if (centerPoint) {
            const labelGraphic = new Graphic({
              geometry: centerPoint,
              symbol: textSymbol
            });
            labelLayerInstance.add(labelGraphic);
          }
        } catch (error) {
          console.error('创建标注时出错:', error);
        }
      });
    })
    .catch((error) => {
      console.error('加载模块失败:', error);
    });
};

const handleCloseLabel = () => {
  labelDialog.value = false;
};

const submitField = (fieldName: string, linkId: number) => {
  labelDialog.value = false;
  const parcels: (string | number)[] = [];
  parcelList.value.forEach((v) => {
    parcels.push(v.id);
  });
  if (parcels.length != 0) {
    selectFields(fieldName, linkId, parcels, { moudleId: dialogSearch.value.moduleId }).then((res) => {
      if (res.code == 200) {
        const labelList: { id: string | number; geomArcgis: string; linkIdValue: string }[] = [];
        res.data.forEach((v: any) => {
          labelList.push({
            id: v.id,
            geomArcgis: v.geomArcgis,
            linkIdValue: v.linkIdValue
          });
        });
        showLable.value = true;
        drawLabel(labelList);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

const changeLabel = () => {
  if (!showLable.value) {
    labelDialog.value = true;
  } else {
    if (labelLayerInstance) {
      labelLayerInstance.removeAll();
    }
    showLable.value = false;
  }
};

const leftChooseOne = (item: any) => {
  // 查找数据在multipleSelection中的索引
  const existingIndex = multipleSelection.value.findIndex((obj) => obj.id === item.id);

  // 如果数据已存在，移除它
  if (existingIndex !== -1) {
    multipleSelection.value.splice(existingIndex, 1);
    item.checked = false;
    item.taskId = null;
    item.taskName = null;

    // 移除高亮
    const highlightIndex = HighlightList.value.findIndex((h) => h.id === item.id);
    if (highlightIndex !== -1) {
      if (HighlightList.value[highlightIndex].remove) {
        HighlightList.value[highlightIndex].remove();
      }
      HighlightList.value.splice(highlightIndex, 1);
    }

    // 添加到已移除列表
    if (!rellayRemoves.value.some((obj) => obj == item.id)) {
      rellayRemoves.value.push(item.id);
    }

    // 重绘图形
    drawAll(false, false);

    // 更新全选状态
    updateCheckAllState();
    return;
  }

  // 如果数据不存在，添加它
  item.checked = true;
  item.taskId = props.taskId;
  item.taskName = props.taskName;

  // 检查是否已经存在于 multipleSelection 中
  const exists = multipleSelection.value.some((obj) => obj.id === item.id);
  if (!exists) {
    // 创建新的选中项
    const newSelection = {
      geomArcgis: item.geomArcgis,
      id: item.id,
      parcelName: item.parcelName,
      relationId: null,
      taskId: props.taskId,
      userId: null,
      custName: null,
      isNew: true
    };

    multipleSelection.value.push(newSelection);

    // 更新图形属性并添加高亮
    if (graphicsLayerInstance) {
      const graphic = graphicsLayerInstance.graphics.items.find((g: any) => g.attributes && g.attributes.id === item.id);
      if (graphic) {
        // 更新图形属性
        graphic.attributes = {
          ...graphic.attributes,
          taskId: props.taskId,
          taskName: props.taskName
        };

        // 添加高亮
        viewInstance.whenLayerView(graphic.layer).then(function (layerView: any) {
          const highlight = layerView.highlight(graphic);
          if (highlight) {
            HighlightList.value.push({
              id: item.id,
              remove: highlight.remove.bind(highlight)
            });
          }
        });
      }
    }

    // 重绘图形
    drawAll(false, false);

    // 更新全选状态
    updateCheckAllState();
  }
};

const changeCheckAll = (val: string | number | boolean) => {
  if (val) {
    // 批量收集需要添加的数据
    const newSelections: any[] = [];
    const graphicsToHighlight: any[] = [];

    parcelList.value.forEach((v) => {
      if (!multipleSelection.value.some((obj) => obj.id == v.id) && !v.taskId) {
        // 创建新的选中项
        const newSelection = {
          geomArcgis: v.geomArcgis,
          id: v.id,
          parcelName: v.parcelName,
          relationId: null,
          taskId: props.taskId,
          userId: null,
          custName: null,
          isNew: true
        };
        newSelections.push(newSelection);

        // 更新列表项状态
        v.checked = true;
        v.taskId = props.taskId;
        v.taskName = props.taskName;

        // 收集需要高亮的图形
        if (graphicsLayerInstance) {
          const graphic = graphicsLayerInstance.graphics.items.find((g: any) => g.attributes && g.attributes.id === v.id);
          if (graphic) {
            graphic.attributes = {
              ...graphic.attributes,
              taskId: props.taskId,
              taskName: props.taskName
            };
            graphicsToHighlight.push(graphic);
          }
        }
      }
    });

    // 批量添加选中数据
    if (newSelections.length > 0) {
      multipleSelection.value = [...multipleSelection.value, ...newSelections];
    }

    // 批量添加高亮效果
    if (graphicsToHighlight.length > 0) {
      viewInstance.whenLayerView(graphicsLayerInstance).then(function (layerView: any) {
        graphicsToHighlight.forEach((graphic) => {
          const highlight = layerView.highlight(graphic);
          if (highlight) {
            HighlightList.value.push({
              id: graphic.attributes.id,
              remove: highlight.remove.bind(highlight)
            });
          }
        });
      });
    }
  } else {
    // 取消全选时的批量处理
    const itemsToRemove = multipleSelection.value.filter((item) => parcelList.value.some((obj) => obj.id == item.id));

    // 批量移除高亮
    itemsToRemove.forEach((item) => {
      const highlightIndex = HighlightList.value.findIndex((h) => h.id === item.id);
      if (highlightIndex !== -1) {
        if (HighlightList.value[highlightIndex].remove) {
          HighlightList.value[highlightIndex].remove();
        }
        HighlightList.value.splice(highlightIndex, 1);
      }
    });

    // 更新列表项状态
    parcelList.value.forEach((v) => {
      if (itemsToRemove.some((item) => item.id === v.id)) {
        v.checked = false;
        v.taskId = null;
        v.taskName = null;
      }
    });

    // 批量移除选中数据
    multipleSelection.value = multipleSelection.value.filter((item) => !itemsToRemove.some((removeItem) => removeItem.id === item.id));

    // 添加到已移除列表
    itemsToRemove.forEach((item) => {
      if (!rellayRemoves.value.some((obj) => obj == item.id)) {
        rellayRemoves.value.push(item.id);
      }
    });
  }

  // 只在所有操作完成后重绘一次
  drawAll(false, false);
};

// 在setup函数中添加
watch(
  () => props.searchDialog,
  (val) => {
    if (val) {
      multipleSelection.value = [];
      props.zdList.forEach((v) => {
        const obj = {
          custName: v.custName,
          geomArcgis: v.geomArcgis,
          id: JSON.parse(JSON.stringify(v.parcelId)),
          relationId: JSON.parse(JSON.stringify(v.id)) || null,
          parcelName: v.parcelName,
          taskId: v.taskId,
          userId: v.userId
        };
        multipleSelection.value.push(obj);
      });

      if (props.moduleIdPop) {
        dialogSearch.value.moduleId = props.moduleIdPop;
      } else {
        // 如果需要使用store，需要先引入
        // import { useStore } from 'vuex';
        // const store = useStore();
        // dialogSearch.value.moduleId = store.state.project.proModuleId;
        dialogSearch.value.moduleId = '';
      }

      isCenter.value = false;
      init(true, true);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
:deep(.esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
.gree-span {
  color: #161d26;
}
.gary-span {
  color: rgba(0, 0, 0, 0.4);
  cursor: not-allowed;
}
:deep(.esri-sketch__panel) {
  background: #fff;
}
:deep(.esri-view .esri-view-surface:focus::after) {
  outline: none !important;
}
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom .esri-widget--button) {
  background-color: transparent;
  color: #fff;
}
:deep(.esri-zoom .esri-widget--button:hover) {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 10px;
}
.handle-div {
  margin-bottom: 10px;
  padding: 0px 16px;
}
.footer-page {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: absolute;
  bottom: 50px;
  left: 30px;
}
.task-main {
  display: flex;
  flex-direction: row;
  .left {
    width: 200px;
    border-right: #d3d3d3 solid 1px;
    overflow: auto;
    .gary-div {
      color: rgba(0, 0, 0, 0.5);
      cursor: no-drop !important;
    }
    .title-div {
      height: 70px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0px 16px;
      .big-span {
        font-size: 16px;
        font-weight: 600;
      }
      .success-span {
        color: #67c23a;
      }
      .warry-span {
        color: #f56c6c;
      }
    }
    .end-title {
      display: flex;
      padding: 0px 16px;
      color: #f56c6c;
    }
    .end-box {
      height: calc(100% - 50px) !important;
    }
    .handle-row {
      padding: 0px 16px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: #161d26;
      font-size: 14px;
    }
    .user-end {
      height: calc(100% - 18px) !important;
    }
    .user-left {
      height: calc(100% - 40px) !important;
    }
    .box-left {
      height: calc(100% - 100px) !important;
    }
    .user-box {
      height: calc(100% - 0px);
      overflow: auto;
      .dialog-search {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .user-item {
        height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
        padding: 0px 16px;
        justify-content: space-between;
        flex-wrap: wrap;
        font-size: 12px;
      }
      .user-item:hover {
        background: #d3d3d3;
      }
      .active-user {
        background: #edf6ff;
        color: #409eff;
      }
    }
    .user-box::-webkit-scrollbar {
      width: 4px;
    }
    .user-box::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    .user-box::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
  .right {
    flex: 1;
    position: relative;
    .map {
      height: 100%;
    }
    .map-change {
      position: absolute;
      bottom: 180px;
      right: 60px;
      display: flex;
      flex-direction: row;
      .map-item {
        width: 96px;
        height: 72px;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        align-items: flex-end;
        .map-footer {
          width: 100%;
          height: 22px;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
          border-radius: 0px 0px 8px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #fff;
        }
        .map-footer-active {
          color: #1b9af7;
        }
      }
      .map-left {
        background-image: url('../../assets/images/normal-map.png');
        background-size: cover;
      }
      .map-right {
        background-image: url('../../assets/images/image-map.png');
        background-size: cover;
        margin-left: 8px;
      }
      .map-active {
        border: #1b9af7 solid 1px;
      }
    }
    .tuli-box {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.4);
      padding: 16px;
      border-radius: 8px;
      color: #fff;
      .tuli-title {
        font-size: 14px !important;
        font-weight: 600;
      }
      .tuli-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 12px;
        .label {
          text-align: left;
          margin-left: 10px;
        }
        .tuli-right-gree {
          height: 20px;
          width: 50px;
          border: rgba(84, 255, 159) solid 1px;
          background: rgba(240, 230, 140, 0.2);
        }
        .tuli-right-red {
          height: 20px;
          width: 50px;
          border: rgba(255, 0, 0) solid 1px;
          background: rgba(240, 230, 140, 0.2);
        }
        .tuli-spe {
          height: 20px;
          width: 50px;
          border: rgb(5, 250, 250) solid 3px;
          background: rgb(81, 153, 135);
          display: flex;
          align-items: center;
          justify-content: center;
          .tuli-right-hight {
            height: 14px;
            width: 44px;
            border: rgba(255, 0, 0) solid 1px;
            background: rgba(240, 230, 140, 0.2);
          }
        }
      }
    }
    .min-handle-label {
      padding: 0px 16px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #fff;
      position: absolute;
      top: 16px;
      right: 16px;
    }
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #6a6a6a;
      position: absolute;
      bottom: 165px;
      right: 16px;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .end {
    width: 260px !important;
  }
}
</style>
