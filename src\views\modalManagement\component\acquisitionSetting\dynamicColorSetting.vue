<!-- 动态色值配置 -->
<template>
  <el-dialog draggable v-model="dialogVisible" title="动态色值配置" width="875" :before-close="handleClose" @opened="handleOpen">
    <div class="dialog-content">
      <div class="tips-content">
        <div class="tips-title">释义及注意事项</div>
        <p>该功能主要适用于用户需要根据该节点下的某个属性组下的某个字段的值来决定该图形的颜色！！！</p>
        <p>需要用户先依次选择该节点下的某个属性组然后再选择相应的字段，再添加对应值结果条件。</p>
        <p>单选、下拉选择的字段不允许删除、新增规则，输入框、数字字段用户可自由定制内容。</p>
      </div>
      <div class="dialog-row" style="margin-top: 20px">
        <div class="dialog-item">
          <div style="margin-bottom: 10px">1、选择属性组</div>
          <el-select v-model="nowDynamicColor.linkId" placeholder="请选择属性组" style="width: 240px" clearable filterable @change="changeGroup">
            <el-option v-for="item in groups" :key="item.linkId" :label="item.typeName" :value="item.linkId" />
          </el-select>
        </div>
        <div class="dialog-item">
          <div style="margin-bottom: 10px">2、选择字段</div>
          <el-select v-model="nowDynamicColor.fieldName" placeholder="请选择字段" style="width: 240px" clearable filterable @change="changeField">
            <el-option v-for="item in fields" :key="item.fieldName" :disabled="item.disabled" :label="item.fieldCn" :value="item.fieldName" />
          </el-select>
        </div>
      </div>
      <div class="dialog-row" style="margin-top: 20px">
        <div class="dialog-item">
          <div>
            3、配置规则（<em style="color: #f56c6c">选择了属性组和字段后才可以配置规则</em>）<el-link
              type="primary"
              v-show="nowDynamicColor.fieldName && !isDisableHandle"
              @click="handleAddRules"
              >新增规则</el-link
            >
          </div>
          <el-table :data="nowDynamicColor.rules" max-height="200" style="width: 100%; margin-top: 10px" v-show="nowDynamicColor.fieldName">
            <el-table-column label="序号" width="80" type="index"></el-table-column>
            <el-table-column label="值结果">
              <template #default="scope">
                <el-input v-model="scope.row.result" :readonly="isDisableHandle" placeholder="请输入值结果" style="width: 100%" />
              </template>
            </el-table-column>
            <el-table-column label="颜色">
              <template #default="scope">
                <el-color-picker v-model="scope.row.color" show-alpha />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-link type="danger" @click="handleDel(scope.$index)" :disabled="isDisableHandle">删除</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { rgbaToHex } from '@/utils/validate';
// 定义 props
const props = defineProps<{
  dynamicColorDialog: boolean; // 动态色值配置
  checkedTreeMsg: any; // 选中的树节点信息
  dynamicType: string; //当前配置的动态色值类型 point 点 line 线 polygon 面
}>();

// 定义 emits
const emit = defineEmits<{
  (e: 'handleSubmitDynamicColor', value: any): void;
  (e: 'handleCloseDynamicColor'): void;
}>();

const dialogVisible = computed({
  get: () => {
    return props.dynamicColorDialog;
  },
  set: (value) => {
    // emit('handleCloseDynamicColor'); // 触发更新事件
  }
});

//定义绑定属性
const nowDynamicColor = ref({
  linkId: '',
  fieldName: '',
  rules: []
}); // 当前选中的动态色值配置信息
const groups = ref([]); // 所有的属性组信息
const fields = ref([]); // 当前选中的属性组下的所有字段信息
const isDisableHandle = ref(false); // 是否允许操作删除 选择的字段是radio/select 时不允许删除

/**
 * 关闭对话框结束后触发的事件
 */
const handleClose = () => {
  //初始化
  nowDynamicColor.value = {
    linkId: '',
    fieldName: '',
    rules: []
  }; // 当前选中的动态色值配置信息
  emit('handleCloseDynamicColor'); // 触发更新事件
};

/**
 * 打开对话框结束后触发的事件
 */
const handleOpen = () => {
  groups.value = props.checkedTreeMsg.fieldGroupModelList; // 赋值属性组信息
  //初始化
  nowDynamicColor.value = {
    linkId: '',
    fieldName: '',
    rules: []
  }; // 当前选中的动态色值配置信息
  if (props.dynamicType === 'point') {
    if (props.checkedTreeMsg.styleAttribution.pointColorDynamic) {
      //存在该值则赋值给nowDynamicColor
      nowDynamicColor.value = JSON.parse(JSON.stringify(props.checkedTreeMsg.styleAttribution.pointColorDynamic));
      //赋值的时候还需要判断下选择的字段时什么类型的
      initField();
    }
  } else if (props.dynamicType === 'line') {
    if (props.checkedTreeMsg.styleAttribution.polylineColorDynamic) {
      //存在该值则赋值给nowDynamicColor
      nowDynamicColor.value = JSON.parse(JSON.stringify(props.checkedTreeMsg.styleAttribution.polylineColorDynamic));
      //赋值的时候还需要判断下选择的字段时什么类型的
      initField();
    }
  } else if (props.dynamicType === 'polygon') {
    if (props.checkedTreeMsg.styleAttribution.polygonFillColorDynamic) {
      //存在该值则赋值给nowDynamicColor
      nowDynamicColor.value = JSON.parse(JSON.stringify(props.checkedTreeMsg.styleAttribution.polygonFillColorDynamic));
      initField();
    }
  }
};

/**
 * 初始化选择的字段内容 如果字段是radio/select 则不允许删除
 */
const initField = () => {
  // 赋值完了之后需要判断字段类型，只有单选、下拉选择、输入框、数字框才允许选择字段
  changeGroup(nowDynamicColor.value.linkId); // 赋值字段信息
  isDisableHandle.value = false;
  let nowField = null;
  for (let i = 0; i < fields.value.length; i++) {
    if (fields.value[i].fieldName === nowDynamicColor.value.fieldName) {
      nowField = fields.value[i];
      break;
    }
  }
  if (['radio', 'select'].includes(nowField.valueMethod)) {
    isDisableHandle.value = true; // 单选多选不允许删除
  }
};

/**
 * 选择属性组时触发的事件
 * @param value 选择的属性组linkId
 */
const changeGroup = (value: any) => {
  fields.value = []; // 清空字段信息
  for (let i = 0; i < props.checkedTreeMsg.fieldGroupModelList.length; i++) {
    if (props.checkedTreeMsg.fieldGroupModelList[i].linkId === value) {
      fields.value = props.checkedTreeMsg.fieldGroupModelList[i].fieldModelList; // 赋值字段信息
      break;
    }
  }
  // 赋值完了之后需要判断字段类型，只有单选、下拉选择、输入框、数字框才允许选择字段
  fields.value.forEach((item: any) => {
    if (!['radio', 'select', 'input', 'number'].includes(item.valueMethod)) {
      item.disabled = true;
    }
  });
};

/**
 * 选择字段时触发的事件
 * @param value 选择的字段fieldName
 */
const changeField = (value: any) => {
  isDisableHandle.value = false;
  let nowField = null;
  for (let i = 0; i < fields.value.length; i++) {
    if (fields.value[i].fieldName === value) {
      nowField = fields.value[i];
      break;
    }
  }
  nowDynamicColor.value.rules = []; // 清空规则信息
  if (['radio', 'select'].includes(nowField.valueMethod)) {
    // 单选多选直接把选项列出来，然后让用户自己配置每个选项的颜色
    nowField.attribution.options.forEach((item: any) => {
      nowDynamicColor.value.rules.push({
        label: item.label, // 选项值
        color: '', // 选项颜色
        result: item.value
      });
    });
    isDisableHandle.value = true; // 单选多选不允许删除
  }
  // 输入框和数字框需要用户输入条件，然后根据条件来配置颜色
};

/**
 * 删除规则时触发的事件
 * @param index 当前行下标
 */
const handleDel = (index: number) => {
  ElMessageBox.confirm('确定要删除该规则吗？', '删除提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      nowDynamicColor.value.rules.splice(index, 1); // 删除规则信息
      ElMessage({
        type: 'success',
        message: '删除成功'
      });
    })
    .catch(() => {});
};

/**
 * 新增规则时触发的事件
 */
const handleAddRules = () => {
  nowDynamicColor.value.rules.push({
    label: '', // 选项值
    color: '', // 选项颜色
    result: '' // 条件
  });
};

/**
 * 提交时触发的事件
 */
const submit = () => {
  if (nowDynamicColor.value.rules.length === 0) {
    ElMessage({
      type: 'error',
      message: '请先添加规则'
    });
    return;
  }
  //需要先判断是不是有不合法的内容
  let isError = false;
  for (let i = 0; i < nowDynamicColor.value.rules.length; i++) {
    if (nowDynamicColor.value.rules[i].result === '') {
      isError = true;
      break;
    }
  }
  if (isError) {
    ElMessage({
      type: 'error',
      message: '请填写完整的规则信息'
    });
    return;
  }
  // 把提交的色值改为 16进制
  nowDynamicColor.value.rules.forEach((item: any) => {
    item.color = rgbaToHex(item.color);
  });
  emit('handleSubmitDynamicColor', nowDynamicColor.value); // 触发更新事件
};
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  .tips-content {
    width: 100%;
    background: rgba(245, 108, 108, 0.1);
    border-left: 5px solid #f56c6c;
    padding: 10px 10px 5px 10px;
    .tips-title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 10px;
    }
  }
  .dialog-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .dialog-item {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
