<template>
  <select :style="selectStyle" v-model="taskId" @change="changeTask">
    <option :value="item.value" v-for="item in cptData.option" :key="item.value">{{ item.label }}</option>
  </select>
</template>

<script lang="ts" setup>
import { pollingRefresh } from '@/utils/refreshCptData';
import { getSearchTask } from '@/api/task';
import { v1 as uuidv1 } from 'uuid';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();

// ---定义变量
const cptData = ref({
  option: []
});
const uuid = ref('');
const taskId = ref('');
const selectStyle = ref();

//  ---定义emit---
const emit = defineEmits<{
  (e: 'reload'): void;
}>();

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData);
};
defineExpose({
  refreshCptData
});

const loadData = () => {
  if (props.option.cptDataForm.dataSource == 2 && cptData.value.option.length == 0) {
    // 获取任务 根据模块id
    const params = {
      moduleId: props.option.cptDataForm.moduleId,
      pageNum: 1,
      pageSize: 1000,
      pageType: 3
    };
    getSearchTask(params).then((res) => {
      if (res.code == 200) {
        cptData.value.option = [];
        res.data.list.forEach((v) => {
          cptData.value.option.push({
            value: v.id,
            label: v.name
          });
        });

        // 默认选中第一个
        if (Array.isArray(cptData.value.option) && cptData.value.option.length != 0) {
          taskId.value = cptData.value.option[0].value;
          document.title = cptData.value.option[0].label + '任务数据大屏';
          emit('changeTaskId', cptData.value.option[0].value);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    cptData.value.option = JSON.parse(props.option.cptDataForm.dataText);
  }
};
const redirect = () => {
  if (props.option.attribute.url) {
    if (props.option.attribute.url.startsWith('view')) {
      router.push(props.option.attribute.url);
      emit('reload');
    } else {
      window.open(props.option.attribute.url);
    }
  }
};

// 切换任务
const changeTask = () => {
  for (let i = 0; i < cptData.value.option.length; i++) {
    if (cptData.value.option[i].value == taskId.value) {
      document.title = cptData.value.option[i].label + '任务数据大屏';
      break;
    }
  }
  emit('changeTaskId', taskId.value);
};

watch(
  () => props.option.attribute,
  (newAttribute) => {
    selectStyle.value = {
      backgroundColor: newAttribute.bgColor,
      color: newAttribute.textColor,
      height: newAttribute.textLineHeight + 'px',
      border: 'transparent',
      fontSize: newAttribute.textSize + 'px',
      fontFamily: newAttribute.textFamily,
      textAlign: newAttribute.textAlign
    };
  },
  { deep: true } //深度监听
);

// --- onMounted ---
onMounted(() => {
  selectStyle.value = {
    backgroundColor: props.option.attribute.bgColor,
    color: props.option.attribute.textColor,
    height: props.option.attribute.textLineHeight + 'px',
    border: 'transparent',
    fontSize: props.option.attribute.textSize + 'px',
    fontFamily: props.option.attribute.textFamily,
    textAlign: props.option.attribute.textAlign
  };
  uuid.value = uuidv1();
  refreshCptData();
});

defineOptions({
  name: 'cpt-select'
});
</script>

<style scoped>
select:focus-visible {
  outline: none;
}
option {
  font-size: 14px;
  color: #333;
  text-align: left;
}
</style>
