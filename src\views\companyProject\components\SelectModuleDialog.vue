<template>
  <el-dialog title="新建模块" v-model="dialogVisible" :close-on-click-modal="false" width="580px" @closed="handleModalClosed" @open="getDefalutList">
    <div style="margin: 24px 0 8px; font-size: 14px; font-weight: 600">模块库：</div>
    <el-row class="modal-item-contianer">
      <el-col :span="12" v-for="item in defaultList" :key="item.id">
        <div
          class="row-left"
          @click="handleDefaultModel(item)"
          :style="{
            border: itemId.includes(item.id) ? '1px solid var(--current-color)' : '1px solid #f5f6f7',
            'background-color': isPay ? '#fff9ef' : ''
          }"
        >
          <div class="left-icon">
            <div v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) === 'blob'">
              <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'44px'" :height="'44px'" style="margin-right: 8px" />
            </div>
            <div v-else>
              <svg-icon class-name="svg-item" :icon-class="item.iconUrl" />
            </div>
          </div>
          <div class="left-module">
            <div class="module-info">
              <div class="title">
                <div
                  class="text"
                  :title="item.moduleName"
                  :style="{
                    'text-decoration': item.status == -1 ? 'line-through' : '',
                    color: item.status == -1 ? '#8291a9' : '#161d26'
                  }"
                >
                  {{ item.moduleName }}
                </div>
              </div>
              <div class="remark" :title="item.remark">{{ item.remark }}</div>
            </div>
          </div>
          <div class="pay-icon" v-if="isPay">付费</div>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleModalClosed">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isAddModel">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { selectDefalutList } from '@/api/modal';
import { addModel } from '@/api/control';
interface ModuleData {
  id: string;
  moduleName: string;
  remark: string;
  status: number;
  iconUrl: string;
  [key: string]: any;
}

export default defineComponent({
  name: 'SelectModuleDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    current: {
      type: Object,
      required: true
    },
    baseUrl: {
      type: String,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const dialogVisible = computed({
      get: () => props.visible,
      set: (val) => emit('update:visible', val)
    });

    const defaultList = ref<ModuleData[]>([]);
    const itemId = ref<string[]>([]);
    const isPay = ref(false);
    const isAddModel = ref(false);

    const getDefalutList = async () => {
      try {
        const res = await selectDefalutList();
        if (res.code === 200) {
          defaultList.value = res.data;
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('Failed to fetch default list:', error);
        ElMessage.error('获取默认列表失败');
      }
    };

    const handleDefaultModel = (item: ModuleData) => {
      const index = itemId.value.indexOf(item.id);
      if (index !== -1) {
        itemId.value.splice(index, 1);
      } else {
        itemId.value.push(item.id);
      }
    };

    const handleModalClosed = () => {
      itemId.value = [];
      dialogVisible.value = false;
    };

    const handleSubmit = async () => {
      if (itemId.value.length === 0) {
        ElMessage.error('请选择模块！');
        return;
      }

      isAddModel.value = true;
      const params = {
        companyId: props.current.companyId,
        adminUserId: props.current.adminUserId
      };

      try {
        const res = await addModel(params, itemId.value);
        isAddModel.value = false;
        if (res.code === 200) {
          ElMessage.success(res.data);
          handleModalClosed();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('Submit model type failed:', error);
        ElMessage.error('提交模块类型失败');
      }
    };

    return {
      dialogVisible,
      defaultList,
      itemId,
      isPay,
      isAddModel,
      getDefalutList,
      handleDefaultModel,
      handleModalClosed,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
.modal-item-contianer {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: #ffffff;
  color: #101010;
  font-size: 14px;
  text-align: center;
  font-family: Roboto;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  .row-left {
    display: flex;
    align-items: center;
    border-radius: 6px;
    margin-right: 8px;
    margin-bottom: 10px;
    position: relative;
    .left-icon {
      min-width: 48px;
      text-align: left;
      margin-left: 10px;
      position: relative;
      .svg-item {
        width: 44px;
        height: 44px;
      }
      .modal-icon-svg {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 0px;
      }
      .modal-icon-pic {
        background-color: #fff;
        opacity: 0.5;
        position: absolute;
        width: 44px;
        height: 44px;
        bottom: 0;
        left: 8px;
        right: 8px;
        top: 8px;
      }
    }
    .left-module {
      width: 30%;
      min-width: 156px;
      padding: 8px;
      text-align: left;
      .module-info {
        min-width: 156px;
        .title {
          display: flex;
          color: #161d26;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 14px;
          font-weight: 600;
          min-width: 156px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .remark {
          color: #8291a9;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-size: 12px;
          font-weight: 400;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &:hover {
      background-color: #f6f7f8;
      border-radius: 6px;
    }
    .pay-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 25px;
      background: linear-gradient(180deg, #ffa509 0%, #f1890a 100%);
      border-top-right-radius: 50%;
      border-bottom-left-radius: 50%;
      object-fit: cover;
      color: #fff;
      font-weight: 500;
      letter-spacing: 1px;
      line-height: 25px;
    }
  }
}
</style>
