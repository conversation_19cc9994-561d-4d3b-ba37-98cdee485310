<!-- 驱动字段选择 -->
<template>
  <div class="driveCommpont-main">
    <el-dialog
      title="选择驱动字段"
      :model-value="showDriveDialog"
      width="875px"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @update:model-value="handleClose"
    >
      <div class="chooseAttr-main">
        <div class="empty" v-if="list.length === 0">暂无数据</div>
        <div class="content" v-else>
          <div class="tree-box">
            <div class="title">结构树</div>
            <div class="attr-content">
              <el-tree
                :data="ruleTree"
                :props="defaultProps"
                default-expand-all
                node-key="id"
                ref="tree"
                highlight-current
                :current-node-key="defaultActiveNodeId"
                @node-click="handleClickNode"
              />
            </div>
          </div>
          <div class="attr-box">
            <div class="title">属性组</div>
            <div class="attr-content">
              <div class="flex-row" v-for="(item, index) in groupList" :key="index" @click="chooseAttr(item)" :class="{ 'active': item.checked }">
                <div>{{ item.typeName }}</div>
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          <div class="fied-box">
            <div class="title">字段</div>
            <div class="attr-content">
              <div
                class="flex-row"
                v-for="(item, index) in fieldModelList"
                :key="index"
                :class="{
                  'active': item.checked,
                  'no-power': item.fieldName.toUpperCase() === 'ID' || item.fieldName.toUpperCase() === 'PARENTID'
                }"
                @click="chooseField(item)"
              >
                <div>{{ item.fieldCn }} ({{ item.fieldName }})</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Ref } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';

// 类型定义
interface FieldModel {
  id: number;
  fieldName: string;
  fieldCn: string;
  groupId: number;
  checked: boolean;
}

interface FieldGroupModel {
  id: number;
  typeName: string;
  fieldModelList: FieldModel[];
  checked: boolean;
}

const defaultProps = {
  children: 'list',
  label: 'typeName'
};

const ruleTree = ref(null);
const defaultActiveNodeId = ref(0);
const groupList = ref<FieldGroupModel[]>([]);

// Props 定义
const props = defineProps<{
  showDriveDialog: boolean;
  list: FieldGroupModel[];
  driveFieldGroupModelList: FieldGroupModel[];
  fieldGroupModelList: FieldGroupModel[];
}>();

// Emits 定义
const emit = defineEmits<{
  (e: 'closeDriveDialog'): void;
  (e: 'submitDriveField', fieldGroup: FieldGroupModel): void;
}>();

// 响应式数据
const fieldModelList: Ref<FieldModel[]> = ref([]);
const checkedFieldGroupModelList: Ref<FieldGroupModel> = ref({} as FieldGroupModel);

// 监听对话框显示状态
watch(
  () => props.showDriveDialog,
  (val) => {
    if (val) {
      if (localStorage.getItem('ruleTree')) {
        ruleTree.value = JSON.parse(localStorage.getItem('ruleTree') || []);
        defaultActiveNodeId.value = ruleTree.value[0].id;
        groupList.value = [];
        ruleTree.value[0].fieldGroupModelList.forEach((item: any) => {
          if (item.linkType == 1) {
            groupList.value.push(item);
          }
        });
        fieldModelList.value = groupList.value[0].fieldModelList;
      }
      console.log('驱动字段需要反显的内容---', props.driveFieldGroupModelList);

      // 反显逻辑
      if (props.driveFieldGroupModelList.length > 0) {
        // 初始化属性组选中状态
        props.list.forEach((v) => (v.checked = false));
        const targetGroup = props.list.find((v) => v.id === props.driveFieldGroupModelList[0].id);
        if (targetGroup) {
          targetGroup.checked = true;
          fieldModelList.value = targetGroup.fieldModelList;
        }

        // 初始化字段选中状态
        fieldModelList.value.forEach((v) => (v.checked = false));
        const targetField = fieldModelList.value.find((v) => v.id === props.driveFieldGroupModelList[0].fieldModelList[0].id);
        if (targetField) {
          targetField.checked = true;
          checkedFieldGroupModelList.value = {
            ...props.driveFieldGroupModelList[0],
            fieldModelList: [targetField]
          };
        }
      }
    }
  },
  { deep: true }
);

// 关闭对话框
const handleClose = () => {
  emit('closeDriveDialog');
};

// 提交选择
const submit = () => {
  emit('submitDriveField', checkedFieldGroupModelList.value);
};

// 选择属性组
const chooseAttr = (item: FieldGroupModel) => {
  // 重置所有属性组选中状态
  groupList.value.forEach((v) => (v.checked = false));
  item.checked = true;
  fieldModelList.value = item.fieldModelList;
};

// 选择字段
const chooseField = (item: FieldModel) => {
  // 过滤ID/PARENTID字段
  if (item.fieldName.toUpperCase() === 'ID' || item.fieldName.toUpperCase() === 'PARENTID') return;

  // 校验字段名重复
  const verifyField = props.fieldGroupModelList.flatMap((v) => v.fieldModelList.map((k) => k.fieldName));
  if (verifyField.includes(item.fieldName)) {
    ElMessage.error('选择的驱动字段名字跟已选择的字段重复！');
    // 重置状态
    groupList.value.forEach((v) => (v.checked = false));
    fieldModelList.value.forEach((v) => (v.checked = false));
    checkedFieldGroupModelList.value = {} as FieldGroupModel;
    return;
  }

  // 找到对应属性组并选中
  const targetGroup = groupList.value.find((v) => v.id === item.groupId);
  if (targetGroup) {
    // 重置属性组和字段选中状态
    groupList.value.forEach((v) => (v.checked = false));
    fieldModelList.value.forEach((v) => (v.checked = false));

    // 更新选中状态
    targetGroup.checked = true;
    item.checked = true;
    checkedFieldGroupModelList.value = {
      ...targetGroup,
      fieldModelList: [item]
    };
  }
};

const handleClickNode = (data: any) => {
  defaultActiveNodeId.value = data.id;
  groupList.value = [];
  data.fieldGroupModelList.forEach((item: any) => {
    if (item.linkType == 1) {
      groupList.value.push(item);
    }
  });
};
</script>

<style lang="scss" scoped>
// ... 原有样式保持不变 ...
.driveCommpont-main {
}
.title {
  height: 36px;
  padding-left: 12px;
  font-size: 12px;
  font-weight: bold;
  line-height: 36px;
}
.attr-content {
  height: calc(100% - 36px);
  overflow: auto;
  .flex-row {
    // height: 32px;
    display: flex;
    padding: 5px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    .flex-ico {
      width: 14px;
      height: 14px;
    }
  }
  .active {
    background: #edf4fb;
    color: var(--current-color);
  }
  .no-power {
    cursor: no-drop;
  }
  .flex-row:hover {
    background-color: #f5f7fa;
  }
}
.attr-content ::-webkit-scrollbar {
  width: 1px;
}
/*滚动条样式*/
.attr-content::-webkit-scrollbar {
  width: 4px;
}
.attr-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgb(255, 255, 255, 0.5);
}
.attr-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.chooseAttr-main {
  .empty {
    color: #8291a9;
  }
  .content {
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    width: 100%;
    height: 280px;
    display: flex;
    .tree-box {
      flex: 1;
      height: 100%;
      border-right: #dcdfe6 solid 1px;
    }
    .attr-box {
      flex: 1;
      height: 100%;
      border-right: #dcdfe6 solid 1px;
    }
    .fied-box {
      width: 388px;
      height: 100%;
    }
  }
}
</style>
