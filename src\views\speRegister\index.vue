<template>
  <div class="login-main">
    <div class="login-bgcontent">
      <div class="left-content">
        <img class="logo-img" src="../../assets/images/logo.png" alt="" />
        <div class="left-big-span">
          <span>Welcome to</span>
          <span>Shenma Investigation!</span>
        </div>
        <div class="left-small-span">
          <span>Professional geographic information</span>
          <span>survey software.</span>
        </div>
      </div>
      <div class="right-content">
        <!-- 登录 -->
        <div v-show="showType == 1">
          <div class="big-title">欢迎使用「神马调查」</div>
          <div class="small-title">神马调查管理端</div>
          <div class="log-tab-div">
            <div class="tab-item" v-for="item in tabList" :key="item.title" :class="{ 'active-tab': item.checked }" @click="changeTab(item)">
              {{ item.title }}
              <div class="hr" v-if="item.checked"></div>
            </div>
          </div>
          <!-- 账号密码登录 -->
          <el-form :model="loginForPwd" :rules="loginForPwdRule" ref="loginForPwd" label-width="0px" class="demo-ruleForm" v-if="tabList[0].checked">
            <el-form-item prop="username">
              <el-input v-model="loginForPwd.username" placeholder="请输入账号"></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input placeholder="请输入密码" v-model="loginForPwd.password" show-password> </el-input>
            </el-form-item>
          </el-form>
          <!-- 短信验证登录 -->
          <el-form :model="lgoinForSms" :rules="lgoinForSmsRule" ref="lgoinForSmsRef" label-width="0px" class="demo-ruleForm" v-else>
            <el-form-item prop="username">
              <el-input v-model="lgoinForSms.username" placeholder="请输入手机号"></el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <el-input v-model="lgoinForSms.captcha" maxlength="6" placeholder="请输入验证码">
                <template #suffix>
                  <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode(1)" style="line-height: 44px">{{
                    sendCodeText
                  }}</el-link>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <div class="login-handle">
            <el-checkbox v-model="loginForPwd.rememberMe">记住密码</el-checkbox>
            <el-link type="primary" @click="showType = 4">忘记密码?</el-link>
          </div>
          <el-button type="primary" class="login-btn" @click="firstLogin">登录</el-button>
          <div class="footer-msg">还没有账号？<el-link type="primary" @click="changeType(5)">立即注册</el-link></div>
        </div>
        <!-- 选择组织 -->
        <div v-show="showType == 3">
          <div class="big-title">选择登录组织</div>
          <div class="small-title">你存在以下组织</div>
          <div class="org-div">
            <div class="org-item" v-for="item in orgList" :key="item.companyId" @click="chooseOrg(item)">
              <img src="../../assets/images/org-ico.png" alt="" class="org-ico" />
              <span v-if="item.companyType == 1">{{ item.companyName }}</span>
              <span v-else>我的组织</span>
            </div>
          </div>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"><i class="el-icon-arrow-left"></i> 返回登录页</el-link>
          </div>
        </div>
        <!-- 忘记密码 -->
        <div v-show="showType == 4">
          <div class="big-title">忘记密码</div>
          <div class="big-hr"></div>
          <el-form :model="forgetPwdMsg" :rules="forgetPwdMsgRule" ref="forgetPwdMsgRef" label-width="0px" class="demo-ruleForm">
            <el-form-item prop="username">
              <el-input v-model="forgetPwdMsg.username" placeholder="请输入手机号"></el-input>
            </el-form-item>
            <el-form-item prop="smscode">
              <el-input v-model="forgetPwdMsg.smscode" maxlength="6" placeholder="请输入验证码">
                <template #suffix>
                  <el-link :type="showTypeTitle" :disabled="showDisabled" :underline="false" @click="getCode(2)" style="line-height: 44px">{{
                    sendCodeText
                  }}</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="forgetPwdMsg.password" placeholder="请输入新密码"></el-input>
            </el-form-item>
            <el-form-item prop="alginPassword">
              <el-input v-model="forgetPwdMsg.alginPassword" placeholder="再次输入新密码"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" class="login-btn" @click="forgetPwd">确定</el-button>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"><i class="el-icon-arrow-left"></i> 返回登录页</el-link>
          </div>
        </div>
        <!-- 特殊注册给指定公司 -->
        <div v-show="showType == 5">
          <div class="big-title">【{{ decodeURIComponent(companyName) }}】用户注册</div>
          <div class="big-hr"></div>
          <el-form :model="registerMsg" :rules="registerMsgRule" ref="registerMsgRef" label-width="0px" class="demo-ruleForm">
            <el-form-item prop="custName">
              <el-input v-model="registerMsg.custName" placeholder="请输入姓名"></el-input>
            </el-form-item>
            <el-form-item prop="username">
              <el-input v-model="registerMsg.username" placeholder="请输入手机号" maxlength="11"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" class="login-btn" @click="register">注册</el-button>
          <div class="go-login" @click="goLogin">
            <el-link type="primary"><i class="el-icon-arrow-left"></i> 返回登录页</el-link>
          </div>
        </div>
        <div class="footer" @mouseover="isShowErweima = true" @mouseleave="isShowErweima = false">
          <img src="../../assets/images/erweima.png" alt="" class="erweima" />神马调查APP
        </div>
        <div v-show="isShowForget" @click="isShowForget = false">
          <i class="el-icon-arrow-left" style="font-size: 18px; margin-right: 5px"></i> 返回登录页
        </div>
        <div class="erweima-box" v-show="isShowErweima">
          <img src="../../assets/images/big-erweima5.0.png" alt="" class="erweima" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getMessageCode, loginFirst, forgetPassword } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import { isPhone } from '@/utils/validate';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const props = defineProps<{
  companyName: string;
  moduleId: string | number;
}>();

const validatePhone = (rule: any, value: any, callback: any) => {
  if (!isPhone(value)) {
    callback(new Error('请输入正确的手机号'));
  } else {
    callback();
  }
};
const validatePassRegister = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value != forgetPwdMsg.value.password) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
};

// ---定义变量 ---
const redirect = ref<any>(undefined);
const showType = ref<number>(1); // 1登录 2注册 3选择组织 4忘记密码 5个人注册
const tabList = ref<any[]>([
  { title: '账号登录', checked: true },
  { title: '验证码登录', checked: false }
]);
const codeUrl = ref<string>('');
const loginForPwd = ref<any>({
  uuid: '',
  password: '',
  username: '',
  rememberMe: false,
  from: 'web'
});
const loginForPwdRule = ref<any>({
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
});
const lgoinForSms = ref<any>({
  username: '',
  captcha: '',
  uuid: '',
  from: 'web'
});
const lgoinForSmsRule = ref<any>({
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
});
const orgList = ref<any[]>([]); //组织列表
const oldChooseCompanyId = ref<any>(null); //上一次选择的组织
const time = ref<number>(60); //倒计时
const sendCodeText = ref<string>('获取验证码'); //按钮文本
const showTypeTitle = ref<string>('primary'); //按钮类型
const showDisabled = ref<boolean>(false); //按钮是否禁用
const forgetPwdMsg = ref<any>({
  username: '',
  password: '',
  smscode: '',
  alginPassword: ''
});
const forgetPwdMsgRule = ref<any>({
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  smscode: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
  alginPassword: [{ validator: validatePassRegister, trigger: 'blur' }]
});
const registerMsg = ref<any>({
  username: '',
  mobile: '',
  password: '',
  companyType: 2, //1组织 2个人
  custName: '' //用户名
});
const registerMsgRule = ref<any>({
  custName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ]
});

const isShowErweima = ref<boolean>(false); //  是否显示app二维码
const isShowForget = ref<boolean>(false); //  是否显示忘记密码
const lgoinForSmsRef = ref(null);
const forgetPwdMsgRef = ref(null);
const registerMsgRef = ref(null);

// --- watch --
watch(
  () => route.query,
  (newQuery) => {
    redirect.value = newQuery && newQuery.redirect;
  },
  { immediate: true }
);

// --- 方法 --
const changeTab = (item: any) => {
  tabList.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  loginForPwd.value.password = '';
  lgoinForSms.value.captcha = '';
};
const firstLogin = () => {
  if (tabList.value[0].checked) {
    //账号密码登录
    loginForPwd.value.validate((valid) => {
      if (valid) {
        loginFirst(loginForPwd.value).then((res) => {
          if (res.code == 200) {
            if (res.data.sysCompanyList.length == 0) {
              ElMessage.error('您没有登录权限,请联系管理员！！！');
              return;
            }
            if (res.data.sysCompanyList.length < 2) {
              //代表只有一个组织或者个人用户 直接默认选中第一个组织
              const companyId = res.data.sysCompanyList[0].companyId;
              const parmas = {
                username: loginForPwd.value.username,
                companyId: companyId,
                password: loginForPwd.value.password,
                from: 'web'
              };

              endLogin(parmas);
            } else {
              //代表多个组织 需要先选组织
              oldChooseCompanyId.value = res.data.companyId;
              showType.value = 3;
              orgList.value = res.data.sysCompanyList;
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        return false;
      }
    });
  } else {
    //短信登录
    lgoinForSmsRef.value.validate((valid) => {
      if (valid) {
        loginFirst(lgoinForSms.value).then((res) => {
          if (res.code == 200) {
            if (res.data.sysCompanyList.length == 0) {
              ElMessage.error('您没有登录权限,请联系管理员！！！');
              return;
            }
            if (res.data.sysCompanyList.length < 2) {
              //代表只有一个组织或者个人用户 直接默认选中第一个组织
              const companyId = res.data.sysCompanyList[0].companyId;
              const parmas = {
                username: lgoinForSms.value.username,
                companyId: companyId,
                captcha: lgoinForSms.value.captcha,
                from: 'web'
              };
              endLoginMessage(parmas);
            } else {
              //代表多个组织 需要先选组织
              oldChooseCompanyId.value = res.data.companyId;
              showType.value = 3;
              orgList.value = res.data.sysCompanyList;
            }
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        return false;
      }
    });
  }
};

/**
 * 最终登录
 * @param parmas
 */
const endLogin = (parmas: any) => {
  userStore
    .login(parmas)
    .then(() => {
      router.push({ path: `/map/autoProject@${props.moduleId}` });
    })
    .catch(() => {});
};

/**
 * 选中某个组织
 * @param item
 */
const chooseOrg = (item: any) => {
  if (tabList.value[0].checked) {
    const parmas = {
      username: loginForPwd.value.username,
      companyId: item.companyId,
      password: loginForPwd.value.password,
      from: 'web'
    };
    endLogin(parmas);
  } else {
    //  短信登录
    const parmas = {
      username: lgoinForSms.value.username,
      companyId: item.companyId,
      captcha: lgoinForSms.value.captcha,
      from: 'web'
    };
    endLoginMessage(parmas);
  }
};

/**
 * 返回登录页
 */
const goLogin = () => {
  showType.value = 1;
};
/**
 * 获取验证码
 * @param type
 */
const getCode = (type: number) => {
  if (type == 1) {
    if (!lgoinForSms.value.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  } else if (type == 2) {
    if (!forgetPwdMsg.value.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  } else if (type == 3) {
    if (!registerMsg.value.username) {
      ElMessage.error('请先输入手机号');
      return;
    }
  }

  const param = {
    username: lgoinForSms.value.username
  };
  if (type == 2) {
    param.username = forgetPwdMsg.value.username;
  }
  if (type == 3) {
    param.username = registerMsg.value.username;
  }
  getMessageCode(param).then((res) => {
    if (res.code === 200) {
      setTime(time.value);
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const setTime = (val: number) => {
  if (val == 0) {
    sendCodeText.value = '获取验证码';
    time.value = 60;
    showTypeTitle.value = 'primary';
    showDisabled.value = false;
  } else {
    showTypeTitle.value = 'info';
    showDisabled.value = true;
    time.value--;
    sendCodeText.value = `重新发送${time.value}`;
    setTimeout(() => {
      setTime(time.value);
    }, 1000);
  }
};

/**
 * 忘记密码
 */
const forgetPwd = () => {
  forgetPwdMsgRef.value.validate((valid) => {
    if (valid) {
      forgetPassword(forgetPwdMsg.value).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '密码重置成功'
          });
          showType.value = 1;
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      return false;
    }
  });
};
/**
 * 注册
 */
const register = () => {
  registerMsgRef.value.validate((valid) => {
    if (valid) {
      registerMsg.value.mobile = registerMsg.value.username;
      //组装一个专门给指定公司指定角色注册
      const parms = {
        custName: registerMsg.value.custName,
        nickName: registerMsg.value.custName,
        userName: registerMsg.value.username,
        sex: '0'
      };
      directRegister(parms).then((res) => {
        if (res.code == 200) {
          const username = registerMsg.value.username;
          ElMessageBox.alert(`<font color='red'>${res.data}</font>`, '系统提示', {
            dangerouslyUseHTMLString: true,
            type: 'success'
          })
            .then(() => {
              showType.value = 1;
            })
            .catch(() => {});
        } else {
          const errorText = res.msg;
          ElMessageBox.alert(`<font color='red'>${errorText} </font>`, '系统提示', {
            dangerouslyUseHTMLString: true,
            type: 'error'
          })
            .then(() => {
              showType.value = 5;
            })
            .catch(() => {});
        }
        registerMsg.value = {
          username: '',
          mobile: ''
        };
      });
    } else {
      return false;
    }
  });
};
/**
 * 切换类型
 * @param num
 */
const changeType = (num: number) => {
  showType.value = num;
  if (num == 5) {
    registerMsgRef.value.clearValidate();
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-input--medium .el-input__inner) {
  height: 44px;
  background: #edf4fb;
}
.login-btn {
  width: 100%;
  height: 44px;
}
.login-main {
  width: 100%;
  height: 100%;
  background: #dbe7ee;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .login-bgcontent {
    background-image: url('../../assets/images/login-bg.png');
    background-size: cover;
    width: 1322px;
    height: 900px;
    display: flex;
    align-items: center;
    justify-content: center;
    .left-content {
      width: 432px;
      height: 700px;
      background: linear-gradient(135deg, #0081ff 0%, #22cce2 100%);
      opacity: 1;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      .logo-img {
        width: 198px;
        height: 44px;
        position: absolute;
        top: 24px;
        left: 32px;
      }
      .left-big-span {
        width: 394px;
        height: 84px;
        font-size: 30px;
        font-family: Poppins-Bold, Poppins;
        font-weight: 700;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 120px;
      }
      .left-small-span {
        width: 294px;
        height: 48px;
        font-size: 14px;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 22px;
      }
    }
    .right-content {
      width: 696px;
      height: 700px;
      opacity: 1;
      background: #fff;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      padding: 0px 84px;
      position: relative;
      .big-title {
        font-size: 32px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: 600;
        color: #161d26;
        margin-top: 72px;
      }
      .big-hr {
        width: 48px;
        height: 4px;
        background: var(--current-color);
        border-radius: 4px 4px 4px 4px;
        margin-top: 2px;
        margin-bottom: 40px;
      }
      .small-title {
        font-size: 14px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: normal;
        color: #8291a9;
        margin-top: 4px;
      }
      .log-tab-div {
        margin-top: 43px;
        margin-bottom: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .tab-item {
          height: 28px;
          font-size: 20px;
          color: #8291a9;
          margin-right: 32px;
          cursor: pointer;
          font-family:
            PingFangSC-328080,
            PingFang SC;
          .hr {
            width: 48px;
            height: 4px;
            background: var(--current-color);
            border-radius: 4px 4px 4px 4px;
            margin-top: 2px;
          }
        }
        .active-tab {
          font-size: 24px;
          color: #161d26;
          font-weight: 600;
        }
      }
      .login-handle {
        margin: 20px 0px 32px 0px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .org-div {
        margin-top: 40px;
        height: 425px;
        overflow: auto;
        /*滚动条样式*/
        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: rgba(176, 175, 175, 0.5);
        }
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          border-radius: 0;
          background: rgba(248, 248, 248, 0.1);
        }
        .org-item {
          width: 100%;
          height: 72px;
          background: #edf4fb;
          border-radius: 8px 8px 8px 8px;
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 18px;
          font-family:
            PingFang SC-Medium,
            PingFang SC;
          font-weight: 600;
          color: #161d26;
          margin-bottom: 20px;
          cursor: pointer;
          position: relative;
          .org-ico {
            width: 32px;
            height: 32px;
            margin-left: 16px;
            margin-right: 20px;
          }
        }
        .org-item:hover {
          box-shadow: 0px 4px 12px 0px rgba(0, 54, 106, 0.2);
          color: var(--current-color);
        }
      }
      .footer-msg {
        margin-top: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #8291a9;
      }
      .go-login {
        position: absolute;
        bottom: 60px;
      }
      .footer {
        position: absolute;
        bottom: 48px;
        right: 95px;
        color: #1b9af7;
        display: flex;
        align-items: center;
        cursor: pointer;
        .erweima {
          width: 16px;
          height: 16px;
          margin-right: 5px;
        }
      }
      .erweima-box {
        width: 200px;
        height: 200px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 4px 10px 0px rgba(27, 154, 247, 0.16);
        position: absolute;
        bottom: 84px;
        right: 39px;
        display: flex;
        align-items: center;
        justify-content: center;
        .erweima {
          width: 168px;
          height: 168px;
        }
      }
    }
  }
}
</style>
