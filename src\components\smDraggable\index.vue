<!-- 自己封装的拖拽组件 -->
<template>
  <div class="main">
    <div class="title-row">
      <span class="text" @click="back"
        ><el-icon><ArrowLeftBold /></el-icon>返回</span
      >
      <div class="empty" v-show="isEmpty">请拖拽或点击左侧控件</div>
      <div>
        <el-button style="margin-bottom: 6px" type="primary" @click="submit">发布</el-button>
      </div>
    </div>
    <div class="content">
      <div class="menu">
        <div class="menu-title">
          <div class="hr"></div>
          基础控件
        </div>
        <draggable
          v-model="menuList"
          :group="groupA"
          animation="300"
          :sort="false"
          dragClass="dragClass"
          ghostClass="ghostClass"
          chosenClass="chosenClass"
          class="menu-end"
        >
          <template #item="{ element, index }">
            <div class="item" :key="index" @click="addItem(element)">
              {{ element.name }}
            </div>
          </template>
        </draggable>
      </div>
      <draggable
        v-model="fieldList"
        :group="groupB"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        class="menu-content"
        @add="handleAdd"
        item-key="uid"
      >
        <template #item="{ element, index }">
          <div class="item" :class="{ 'active': element.checked }" @click="chooseItem(element)" v-show="element.delFlag != 1">
            <div class="item-label">
              <div>{{ element.problemTitle }}</div>
              <div class="delete-ico" @click.stop="delItem(index)">
                <el-icon><Close /></el-icon>
              </div>
            </div>
            <!-- 单行输入框 -->
            <template v-if="element.problemType == 'input'">
              <el-input :placeholder="`请输入${element.problemTitle}`"></el-input>
            </template>
            <!-- 多行输入框 -->
            <template v-else-if="element.problemType == 'textarea'">
              <el-input type="textarea" :rows="2" :placeholder="`请输入${element.problemTitle}`"></el-input>
            </template>
            <!-- 单选框 -->
            <template v-else-if="element.problemType == 'radio'">
              <el-radio v-model="element.defaultValue" :label="ite.value" v-for="(ite, idx) in element.problemSelect" :key="idx"></el-radio>
            </template>
            <!-- 复选框 -->
            <template v-else-if="element.problemType == 'checkbox'">
              <el-checkbox
                v-model="ite.checked"
                :value="ite.value"
                v-for="(ite, idx) in element.problemSelect"
                :key="idx"
                @change="changeCheckbox(ite, element)"
              ></el-checkbox>
            </template>
            <!-- 下拉框 -->
            <template v-else-if="element.problemType == 'select'">
              <el-select v-model="element.defaultValue" :placeholder="`请选择${element.problemTitle}`">
                <el-option v-for="(ite, idx) in element.problemSelect" :key="idx" :label="ite.value" :value="ite.value"> </el-option>
              </el-select>
            </template>
            <!-- 评分 -->
            <template v-else-if="element.problemType == 'rate'">
              <el-rate v-model="element.defaultValue"></el-rate>
            </template>
            <!-- 评分集 -->
            <template v-else-if="element.problemType == 'rateList'">
              <div v-for="(ite, idx) in element.problemSelect" :key="idx" style="margin-bottom: 10px">
                <div style="margin-bottom: 5px">{{ ite.label }}</div>
                <div>
                  <el-rate v-model="ite.value"></el-rate>
                </div>
              </div>
            </template>
            <!-- 图片上传 -->
            <template v-else-if="element.problemType == 'upload'">
              <div class="upload-div">
                <el-icon><Plus /></el-icon>
              </div>
            </template>
          </div>
        </template>
      </draggable>
      <!-- 当前选中组件的属性 -->
      <div class="option">
        <div class="menu-title">
          <div class="hr"></div>
          组件属性
        </div>
        <div class="menu-end" v-if="checkedNode">
          <el-form :model="checkedNode" :rules="checkedNodeRule" label-position="top" ref="ruleForm" class="demo-ruleForm">
            <el-form-item label="题目名称" prop="problemTitle">
              <el-input v-model="checkedNode.problemTitle" placeholder="请输入题目名称"></el-input>
            </el-form-item>
            <el-form-item
              label="选项"
              v-show="checkedNode.problemType == 'radio' || checkedNode.problemType == 'checkbox' || checkedNode.problemType == 'select'"
            >
              <div v-for="(ite, idx) in checkedNode.problemSelect" :key="idx" class="option-row">
                <el-input v-model="ite.value" placeholder="请输入选项" class="left"></el-input>
                <div class="right" @click="delOption(idx)">
                  <i class="el-icon-delete ico"></i>
                </div>
              </div>
              <el-link type="primary" @click="addOption"><i class="el-icon-plus"></i>添加</el-link>
            </el-form-item>
            <el-form-item label="默认值" v-show="checkedNode.problemType != 'upload'">
              <el-link type="primary" class="init-a" @click="initDefault(checkedNode)">初始化默认值</el-link>
              <template v-if="checkedNode.problemType == 'input' || checkedNode.problemType == 'textarea'">
                <el-input placeholder="请输入默认值" v-model="checkedNode.defaultValue"></el-input>
              </template>
              <template v-else-if="checkedNode.problemType == 'rate'">
                <el-input v-model="checkedNode.defaultValue" type="number"></el-input>
              </template>
              <template v-else-if="checkedNode.problemType == 'radio'">
                <el-radio v-model="checkedNode.defaultValue" :label="ite.value" v-for="(ite, idx) in checkedNode.problemSelect" :key="idx"></el-radio>
              </template>
              <template v-else-if="checkedNode.problemType == 'checkbox'">
                <el-checkbox
                  v-model="ite.checked"
                  :value="ite.value"
                  v-for="(ite, idx) in checkedNode.problemSelect"
                  :key="idx"
                  @change="changeCheckbox(ite, checkedNode)"
                ></el-checkbox>
              </template>
              <template v-else-if="checkedNode.problemType == 'select'">
                <el-select v-model="checkedNode.defaultValue" :placeholder="`请选择${checkedNode.problemTitle}`">
                  <el-option v-for="(ite, idx) in checkedNode.problemSelect" :key="idx" :label="ite.value" :value="ite.value"> </el-option>
                </el-select>
              </template>
              <template v-else-if="checkedNode.problemType == 'rateList'">
                <div v-for="(ite, idx) in checkedNode.problemSelect" :key="idx" class="default-row">
                  <el-input v-model="ite.label" class="left"></el-input>
                  <el-input v-model="ite.value" type="number" class="center"></el-input>
                  <div class="end" @click="delOption(idx)">
                    <i class="el-icon-delete ico"></i>
                  </div>
                </div>
                <el-link type="primary" @click="addOption"><i class="el-icon-plus"></i>添加</el-link>
              </template>
            </el-form-item>
            <el-form-item label="是否必填">
              <el-radio v-model="checkedNode.required" :label="1">是</el-radio>
              <el-radio v-model="checkedNode.required" :label="0">否</el-radio>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, defineExpose } from 'vue';
import draggable from 'vuedraggable';
import type { PropType } from 'vue';

// Types
interface MenuItem {
  name: string;
  type: string;
}

interface ProblemSelect {
  value: string | number;
  label?: string;
  checked?: boolean;
}

interface FieldItem {
  problemTitle: string;
  problemType: string;
  uid: string;
  checked: boolean;
  required: number;
  delFlag?: number;
  problemSelect?: ProblemSelect[];
  defaultValue: string | number;
  id?: string;
}

// Props & Emits
const emit = defineEmits(['back', 'submitQuestion']);

// State
const menuList = ref<MenuItem[]>([
  { name: '单行输入框', type: 'input' },
  { name: '多单行输入框', type: 'textarea' },
  { name: '单选框', type: 'radio' },
  { name: '复选框', type: 'checkbox' },
  { name: '下拉框', type: 'select' },
  { name: '评分', type: 'rate' },
  { name: '评分集', type: 'rateList' },
  { name: '图片上传', type: 'upload' }
]);

const fieldList = ref<FieldItem[]>([]);
const checkedNode = ref<FieldItem | null>(null);
const isEmpty = ref(true);

const groupA = reactive({
  name: 'itxst',
  pull: 'clone',
  put: false
});

const groupB = reactive({
  name: 'itxst',
  pull: (e: any) => {},
  put: true
});

const checkedNodeRule = reactive({
  problemTitle: [{ required: true, message: '请输入题目名称', trigger: 'blur' }]
});

// Methods
const back = () => {
  emit('back');
};

const handleAdd = (e: any) => {
  const type = e.item._underlying_vm_.type;
  const name = e.item._underlying_vm_.name;
  const item: FieldItem = {
    problemTitle: name,
    problemType: type,
    uid: (Math.random() + new Date().getTime()).toString(32).slice(0, 8),
    checked: true,
    required: 0,
    defaultValue: ''
  };

  if (type === 'radio') {
    item.problemSelect = [{ value: '单选框1' }, { value: '单选框2' }];
  } else if (type === 'checkbox') {
    item.problemSelect = [{ value: '复选框1' }, { value: '复选框2' }];
  } else if (type === 'select') {
    item.problemSelect = [{ value: '下拉选项1' }, { value: '下拉选项2' }];
  } else if (type === 'rate') {
    item.defaultValue = 0;
  } else if (type === 'rateList') {
    item.problemSelect = [
      { value: 0, label: '评分1' },
      { value: 0, label: '评分2' }
    ];
  }

  const index = e.newIndex;
  fieldList.value.forEach((v) => {
    v.checked = false;
  });
  fieldList.value[index] = item;
  checkedNode.value = fieldList.value[index];
  isEmpty.value = false;
};

const addItem = (obj: MenuItem) => {
  const item: FieldItem = {
    problemTitle: obj.name,
    problemType: obj.type,
    uid: (Math.random() + new Date().getTime()).toString(32).slice(0, 8),
    checked: true,
    required: 0,
    defaultValue: ''
  };

  if (obj.type === 'radio') {
    item.problemSelect = [{ value: '单选框1' }, { value: '单选框2' }];
  } else if (obj.type === 'checkbox') {
    item.problemSelect = [{ value: '复选框1' }, { value: '复选框2' }];
  } else if (obj.type === 'select') {
    item.problemSelect = [{ value: '下拉选项1' }, { value: '下拉选项2' }];
  } else if (obj.type === 'rate') {
    item.defaultValue = 0;
  } else if (obj.type === 'rateList') {
    item.problemSelect = [
      { value: 0, label: '评分1' },
      { value: 0, label: '评分2' }
    ];
  }

  fieldList.value.forEach((v) => {
    v.checked = false;
  });
  fieldList.value.push(item);
  checkedNode.value = item;
  isEmpty.value = false;
};

const chooseItem = (item: FieldItem) => {
  fieldList.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  checkedNode.value = item;
};

const addOption = () => {
  if (!checkedNode.value) return;
  const type = checkedNode.value.problemType;
  const index = checkedNode.value.problemSelect?.length || 0;

  if (type === 'radio') {
    checkedNode.value.problemSelect?.push({ value: `单选框${index + 1}` });
  } else if (type === 'checkbox') {
    checkedNode.value.problemSelect?.push({ value: `复选框${index + 1}` });
  } else if (type === 'select') {
    checkedNode.value.problemSelect?.push({ value: `下拉选项${index + 1}` });
  } else if (type === 'rateList') {
    checkedNode.value.problemSelect?.push({ label: `评分${index + 1}`, value: 0 });
  }
};

const delOption = (idx: number) => {
  if (!checkedNode.value?.problemSelect) return;
  checkedNode.value.problemSelect.splice(idx, 1);
};

const delItem = (index: number) => {
  if (fieldList.value[index].id) {
    fieldList.value[index].delFlag = 1;
  } else {
    fieldList.value.splice(index, 1);
  }
  checkedNode.value = null;
};

const submit = () => {
  emit('submitQuestion', fieldList.value);
};

const changeCheckbox = (ite: ProblemSelect, item: FieldItem) => {
  const values: string[] = [];
  const list = item.problemSelect || [];
  list.forEach((v) => {
    if (v.checked) {
      values.push(String(v.value));
    }
  });
  item.defaultValue = values.join(',');
};

const initDefault = (item: FieldItem) => {
  if (item.problemType === 'checkbox') {
    item.problemSelect?.forEach((v) => {
      v.checked = false;
    });
  } else {
    item.defaultValue = '';
  }
};

const initFileLise = (list: FieldItem[]) => {
  if (list.length === 0) {
    fieldList.value = [];
  } else {
    const newFieldList = list.map((v) => ({
      ...v,
      problemSelect: Object.values(v.problemSelect || {})
    }));
    fieldList.value = newFieldList;
    checkedNode.value = fieldList.value[0];
    isEmpty.value = false;
  }
};

// Expose the method for the parent component
defineExpose({
  initFileLise
});
</script>

<style lang="scss" scoped>
.default-row {
  display: flex;
  margin-bottom: 20px;
  .left {
    flex: 1;
  }
  .center {
    margin-left: 5px;
    width: 70px;
  }
  .end {
    margin-left: 5px;
    color: red;
    .ico {
      cursor: pointer;
      padding: 5px;
    }
    .ico:hover {
      background: #d3d3d3;
    }
  }
}
.rate-div {
  display: flex;
  flex-direction: column;
  .rate-row {
    margin-bottom: 10px;
  }
}
.init-a {
  position: absolute;
  top: -46px;
  left: 60px;
}
.upload-div {
  width: 100px;
  height: 100px;
  border: dashed 1px #d3d3d3;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
/*定义要拖拽元素的样式*/
.ghostClass {
  background-color: blue !important;
}

.chosenClass {
  background-color: #d3d3d3 !important;
  opacity: 1 !important;
}

.dragClass {
  background-color: #d3d3d3 !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
  background-image: none !important;
}
.itxst {
  margin: 10px;
  min-height: 200px;
}

.title {
  padding: 6px 12px;
}

.col {
  width: 40%;
  flex: 1;
  padding: 10px;
  border: solid 1px #eee;
  border-radius: 5px;
  float: left;
}

.col + .col {
  margin-left: 10px;
}

.item {
  padding: 6px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #eee;
  background-color: #f1f1f1;
}

.item:hover {
  background-color: #fdfdfd;
  cursor: move;
  .delete-ico {
    background: #2672ff;
  }
}

.item + .item {
  border-top: none;
  margin-top: 6px;
}
.main {
  width: 100%;
  height: 100%;
  font-size: 14px;
  .title-row {
    display: flex;
    justify-content: space-between;
    // height: 70px;
    border-bottom: 1px solid #dbe7ee;
    align-items: center;
    .empty {
      font-size: 18px;
      color: #f56c6c;
    }
    .text {
      height: 22px;
      font-size: 16px;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      color: #161d26;
      line-height: 22px;
      margin-bottom: 4px;
      cursor: pointer;
    }
  }
  .content {
    height: calc(100% - 35px);
    display: flex;
    flex-direction: row;
    .menu {
      width: 256px;
      height: 100%;
    }
    .menu-content {
      flex: 1;
      height: 100%;
      min-height: 100%;
      overflow: auto;
      padding: 10px 0px;
      border-left: #d3d3d3 solid 1px;
      border-right: #d3d3d3 solid 1px;
      .active {
        border: 1px solid #2672ff;
      }
      .item-label {
        margin-bottom: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .delete-ico {
          font-size: 18px;
          width: 18px;
          height: 18px;
          color: #fff;
          cursor: pointer;
        }
      }
    }
    /*滚动条样式*/
    .menu-content::-webkit-scrollbar {
      width: 4px;
    }
    .menu-content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    .menu-content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
    .option {
      width: 259px;
      height: 100%;
      margin-left: 10px;
    }
  }
}
.menu-title {
  margin-top: 10px;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .hr {
    height: 100%;
    width: 4px;
    background: #2672ff;
    margin-right: 8px;
  }
}
.menu-end {
  height: calc(100% - 42px);
  overflow: auto;
  padding: 10px 0px;
}
.option-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  .left {
    flex: 1;
  }
  .right {
    margin-left: 10px;
    width: 40px;
    color: red;
    .ico {
      cursor: pointer;
      padding: 5px;
    }
    .ico:hover {
      background: #d3d3d3;
    }
  }
}
</style>
