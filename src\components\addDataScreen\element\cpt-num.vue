<template>
  <div style="text-align: center">
    <div
      style="font-family: '840CAI978', serif"
      :style="{ color: option.attribute.numColor, fontSize: option.attribute.numSize + 'px', lineHeight: option.attribute.numHeight + 'px' }"
    >
      {{ cptData?.value }}<span style="font-size: 14px; color: #ccc">{{ props?.option?.attribute?.unit }}</span>
    </div>
    <div style="color: #ccc">{{ option.attribute.title }}</div>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';

const route = useRoute();

// --- 定义props ---
const props = defineProps<{
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const cptData: any = ref({});
const uuid = ref(null);

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (id) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (id != '') {
      parmas.taskId = id;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        cptData.value = {
          value: res.data
        };
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
    });
  }
};

// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});

defineOptions({
  name: 'cpt-num'
});
</script>

<style scoped></style>
