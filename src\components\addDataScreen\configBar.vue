<template>
  <div style="width: 100%; height: 100%; background: #e3e5ec; overflow: hidden">
    <el-row class="cptTitle">
      <el-col :span="24"
        ><div>{{ configBarShow ? '组件配置' : '大屏配置' }}</div></el-col
      >
    </el-row>
    <el-tabs v-show="configBarShow" v-model="configTab" :stretch="true">
      <el-tab-pane label="坐标" name="basic">
        <div style="width: 200px; margin: 0 auto">
          <div class="flex items-center">
            <span>宽度：</span>
            <el-input-number :min="20" :max="2000" v-model="currentCptCopy.cptWidth" value-on-clear="min" />
          </div>
          <div class="flex items-center mt-12px">
            <span>高度：</span>
            <el-input-number :min="20" :max="1500" v-model="currentCptCopy.cptHeight" value-on-clear="min" />
          </div>
          <div class="flex items-center mt-12px">
            <span>X 轴：</span>
            <el-input-number :min="-500" :max="2500" v-model="currentCptCopy.cptX" :value-on-clear="0" />
          </div>
          <div class="flex items-center mt-12px">
            <span>Y 轴：</span>
            <el-input-number :min="-500" v-model="currentCptCopy.cptY" :value-on-clear="0" />
          </div>
          <div class="flex items-center mt-12px">
            <span>Z 轴：</span>
            <el-input-number :min="1" :max="1800" v-model="currentCptCopy.cptZ" :value-on-clear="0" />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="属性" name="custom">
        <div class="customForm" :style="{ height: height - 140 + 'px' }" v-if="currentCpt && currentCpt.cptOption">
          <component
            :is="currentCptCopy.cptOptionKey ? currentCptCopy.cptOptionKey : currentCptCopy.cptKey + '-option'"
            :attribute="currentCptCopy.cptOption.attribute"
            :key="currentCptCopy.id || currentCptCopy.cptKey"
          />
        </div>
      </el-tab-pane>
      <!-- 展示数据表单需在option.js初始化cptDataForm-->
      <el-tab-pane label="数据" name="data" v-if="cptDataFormShow">
        <div class="customForm" :style="{ height: height - 140 + 'px' }">
          <el-form label-position="top">
            <el-form-item label="数据类型">
              <el-radio-group v-model="currentCptCopy.cptOption.cptDataForm.dataSource" @change="changeDataSource">
                <el-radio :value="1">静态数据</el-radio>
                <el-radio :value="2">表达式</el-radio>
                <!-- 管理后台1公司才有 -->
                <el-radio :value="3" v-if="userStore.user['companyId'] == '1'">接口</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="轮询" v-show="currentCptCopy.cptOption.cptDataForm.dataSource !== 1">
              <el-switch v-model="dataPollEnable" active-text="开启" inactive-text="关闭" />
            </el-form-item>
            <el-form-item label="轮询时间(s)" v-show="dataPollEnable">
              <el-input-number v-model="currentCptCopy.cptOption.cptDataForm.pollTime" :min="0" :max="100" label="描述文字" />
            </el-form-item>
            <el-form-item
              label="绑定模块"
              v-show="currentCptCopy.cptOption.cptDataForm.dataSource === 2 && !currentCptCopy.cptOption.cptDataForm.displayModule"
            >
              <el-select v-model="currentCptCopy.cptOption.cptDataForm.code" placeholder="请选择业务模块" clearable class="form-select">
                <el-option v-for="(item, index) in appTypeOptions" :key="index" :label="getAddTaskLable(item)" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="dataLabels[currentCptCopy.cptOption.cptDataForm.dataSource - 1]">
              <el-button
                type="primary"
                size="small"
                v-show="currentCptCopy.cptOption.cptDataForm.dataSource === 2 && !currentCptCopy.cptOption.cptDataForm.displayExpress"
                style="margin-bottom: 10px"
                @click="handleCopy"
                >自定义表达式</el-button
              >

              <json-editor-vue
                v-show="currentCptCopy.cptOption.cptDataForm.dataSource === 1 && dataJson"
                class="editor"
                v-model="dataJson"
                language="zh-CN"
                :options="{
                  mainMenuBar: true,
                  enableTransform: false,
                  enableSort: false,
                  history: false
                }"
              />
              <el-input
                v-show="currentCptCopy.cptOption.cptDataForm.dataSource === 2 && !currentCptCopy.cptOption.cptDataForm.displayExpress"
                type="textarea"
                :rows="5"
                v-model="currentCptCopy.cptOption.cptDataForm.apiUrl"
              />
              <el-input
                v-show="currentCptCopy.cptOption.cptDataForm.dataSource === 3"
                type="textarea"
                :rows="5"
                v-model="currentCptCopy.cptOption.cptDataForm.apiUrl"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="width: 100%" @click="refreshCptData">刷新数据</el-button>
            </el-form-item>
            <!-- 交互配置 暂时隐藏  以后可能会用 -->
            <!-- <el-form-item label="交互配置">
              <el-button type="primary" size="small" @click="interactionSetting(currentCptCopy)">配置</el-button>
            </el-form-item> -->
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div v-show="!configBarShow" style="margin-top: 10px; padding-right: 10px">
      <el-form :model="designDataCopy" label-width="90px">
        <el-form-item label="网站标题">
          <el-input v-model="designDataCopy.title" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="网站描述">
          <el-input type="textarea" v-model="designDataCopy.simpleDesc"></el-input>
        </el-form-item>
        <el-form-item>
          <template #label>
            <div class="flex items-center">
              <span class="mr-2px">分辨率X</span>
              <el-tooltip content="最小640，最大10240" placement="top">
                <el-icon class="cursor-pointer"><InfoFilled /></el-icon
              ></el-tooltip>
            </div>
          </template>
          <el-input-number v-model="designDataCopy.scaleX" :min="640" :max="10240" style="width: 100%" />
        </el-form-item>
        <el-form-item>
          <template #label>
            <div class="flex items-center">
              <span class="mr-2px">分辨率Y</span>
              <el-tooltip content="最小320，最大10240" placement="top">
                <el-icon class="cursor-pointer"><InfoFilled /></el-icon
              ></el-tooltip>
            </div>
          </template>
          <el-input-number v-model="designDataCopy.scaleY" :min="320" :max="10240" style="width: 100%" />
        </el-form-item>
        <el-form-item label="背景颜色">
          <el-color-picker v-model="designDataCopy.bgColor" show-alpha />
        </el-form-item>
        <el-form-item label="背景图片">
          <el-upload
            class="avatar-uploader"
            :headers="headers"
            name="files"
            :action="`${fileUrl}/qjt/file/multi/upload`"
            :show-file-list="false"
            :on-success="handleSuccess"
            :before-upload="beforeUpload"
          >
            <el-image
              style="width: 168px; height: 160px"
              v-if="designDataCopy.bgImg"
              :src="designDataCopy.bgImg ? `${fileUrl}/qjt/file/otherDownload/${designDataCopy.bgImg}?token=${token}` : logoImg"
              fit="fill"
            />

            <el-icon v-else><Plus /></el-icon>
          </el-upload>
          <el-link type="danger" @click="clearBG" v-show="designDataCopy.bgImg">清除背景图片</el-link>
        </el-form-item>
      </el-form>
    </div>
    <gallery ref="galleryRef" @confirmCheck="confirmCheck" />
    <!-- 打开公式编辑的弹框 todo -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="expression"
      :isCopy="true"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
    ></formula-editing-dialog>
    <!-- 配置交互弹窗 暂时隐藏  以后可能会用 -->
    <!-- <el-dialog title="交互配置" v-model="interactionSettingDialog" width="950px" :close-on-click-modal="false" :before-close="handleCloseInteraction">
      <div class="inter-box">
        <div class="flex-row">
          <div class="label">标题</div>
          <div class="content">
            <el-input v-model="currentCptCopy.cptOption.interaction.title" placeholder="清输入标题"></el-input>
          </div>
        </div>
        <div class="flex-row">
          <div class="label">数据类型</div>
          <div class="content">
            <el-radio-group v-model="currentCptCopy.cptOption.interaction.dataSource">
              <el-radio :value="1">静态数据</el-radio>
              <el-radio :value="2">表达式</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="flex-row">
          <div class="label">
            <el-link type="primary" @click="addContent">添加内容</el-link>
          </div>
        </div>
        <div class="flex-row" v-for="(item, index) in currentCptCopy.cptOption.interaction.contentList" :key="index">
          <div class="flex-item">
            <div class="flex-label">标题</div>
            <div class="flex-content">
              <el-input v-model="item.label" placeholder="请输入标题"></el-input>
            </div>
          </div>
          <div class="flex-item">
            <div class="flex-label">内容</div>
            <div class="flex-content">
              <template v-if="currentCptCopy.cptOption.interaction.dataSource == 1">
                <el-input v-model="item.value" placeholder="请输入内容"></el-input>
              </template>
              <template v-else>
                <el-select v-model="item.code" placeholder="请选择业务模块" clearable class="form-select" style="margin-right: 5px">
                  <el-option v-for="(ite, idx) in appTypeOptions" :key="idx" :label="getAddTaskLable(ite)" :value="ite.code"></el-option>
                </el-select>
                <el-input v-model="item.expression" placeholder="请输入内容" readonly @click="showExpression(item, index)"></el-input>
              </template>
            </div>
          </div>
          <div class="flex-handle">
            <el-link type="danger" @click="delContent(index)">删除</el-link>
          </div>
        </div>
      </div>
    </el-dialog> -->
  </div>
</template>

<script lang="ts" setup>
import { isJSON } from '@/utils/index';
import JsonEditorVue from 'json-editor-vue3';
import Gallery from './components/gallery.vue'; //光标行背景高亮，配置里面也需要styleActiveLine设置为true
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import { getToken } from '@/utils/auth';
import { cloneDeep } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
const modalStore = useModalStore();
const userStore = useUserStore();

import logoImg from '@/assets/images/logo.png';
// --- props ---
interface Interaction {
  title: string;
  dataSource: number;
  dataText: string;
  expression: string;
  contentList: any[];
}

interface CptOption {
  interaction: Interaction;
  cptDataForm?: any;
}

interface CurrentCpt {
  cptOption: CptOption;
}

// 默认值使用 withDefaults 包裹
const props = withDefaults(
  defineProps<{
    currentCpt: CurrentCpt;
    designData: Record<string, any>;
    height: number;
    appTypeOptions: any[];
  }>(),
  {
    currentCpt: () => ({
      cptOption: {
        interaction: {
          title: '',
          dataSource: 1,
          dataText: '',
          expression: '',
          contentList: []
        }
      }
    })
  }
);

const designDataCopy = computed(() => props.designData);

const currentCptCopy: any = computed(() => props.currentCpt);

//  ---定义emit---
const emit = defineEmits<{
  (e: 'initCptConfig'): void;
  (e: 'refreshCptData'): void;
}>();

// --- computed ---
// 轮询开关
const dataPollEnable = computed({
  get: () => {
    const form = props.currentCpt.cptOption.cptDataForm;
    return !!(form && form.pollTime && form.pollTime !== 0);
  },
  set: (newValue: boolean) => {
    const form = props.currentCpt.cptOption.cptDataForm;
    if (!form) return;
    if (newValue) {
      form.pollTime = 8;
    } else {
      form.pollTime = 0;
    }
  }
});

// JSON 数据
const dataJson = computed({
  get: () => {
    const form = props.currentCpt.cptOption.cptDataForm;
    if (form && isJSON(form.dataText)) {
      return JSON.parse(form.dataText);
    }
    return {};
  },
  set: (newValue: Record<string, any>) => {
    const form = props.currentCpt.cptOption.cptDataForm;
    if (form) {
      form.dataText = JSON.stringify(newValue);
    }
  }
});

// --- 定义变量 ---
const cptDataFormShow = ref(false);
const configTab = ref('custom');
const dataLabels = ref(['数据', '表达式', '接口']);
const configBarShow = ref(false);
const hasJsonFlag = ref(true);
const cmOptions = ref({
  tabSize: 4, // tab的空格个数
  theme: 'dracula', //主题样式
  lineNumbers: true, //是否显示行数
  lineWrapping: true, //是否自动换行
  styleActiveLine: true, //line选择是是否加亮
  matchBrackets: true, //括号匹配
  mode: 'text/x-sparksql', //实现javascript代码高亮
  readOnly: false, //只读

  keyMap: 'default',
  extraKeys: { tab: 'autocomplete' },
  foldGutter: true,
  gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
  hintOptions: {
    completeSingle: false,
    tables: {}
  }
});
const formulaVisible = ref(false);
const expression = ref(''); // 表达式
const appType = ref('#console');
const moduleId = ref(0);
const fileUrl = import.meta.env.VITE_APP_BASE_API;
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};
const token = getToken();
const interactionSettingDialog = ref(false); //配置交互弹窗
const nowEditRow = ref(null); //当前编辑的行
const galleryRef = ref<any>(null);

// --- watch ---
watch(currentCptCopy, (newVal) => {
  if (!newVal.cptOption) {
    //处理默认触发内容 新增的
    emit('initCptConfig');
  }
  cptDataFormShow.value = false;
  if (!newVal || !newVal.cptOption) {
    configBarShow.value = false; //清空时
  } else {
    if (currentCptCopy.value.cptOption.cptDataForm) {
      cptDataFormShow.value = true;
    } else {
      configTab.value = 'basic'; //解決上一組件沒有数据表单导致tab栏未选中bug
    }
  }
});

// --- 定义方法 ---
const confirmCheck = (fileUrl: string) => {
  designDataCopy.value.bgImg = fileUrl;
};

const changeDataSource = (val: number): void => {
  //静态数据不显示轮询按钮
  if (val === 1) {
    currentCptCopy.value.cptOption.cptDataForm.pollTime = 0;
  }
};

/**
 * 刷新数据，调用父组件(index)中refreshCptData方法
 * 在父组件中再调用选中图层中的refreshCptData方法
 * 图层中的refreshCptData方法再自行调后端接口渲染数据，文本框的内容和数据类型组装在option.cptDataForm中
 */
const refreshCptData = () => {
  emit('refreshCptData');
};
const showCptConfig = () => {
  configBarShow.value = true;
};
/**
 * 关闭公式弹框
 */
const handleCloseFormulation = () => {
  formulaVisible.value = false;
  modalStore.setIsAllGroup(false);
};
/**
 * 提交公式
 */
const handleSubmitFormulation = (expression: string) => {
  if (interactionSettingDialog.value) {
    //代表是交互设置的表达式
    currentCptCopy.value.cptOption.interaction.expression = expression;
  } else {
    currentCptCopy.value.cptOption.cptDataForm.apiUrl = expression;
  }
};
/**
 * 复制表达式
 */
const handleCopy = () => {
  // 在每次打开自定义表达式的时候，需要把字段数据清除否则会有缓存
  modalStore.setIsHasAcquition(false);
  modalStore.setIsAllGroup(true);
  expression.value = '';
  formulaVisible.value = true;
};
/**
 * 判断任务名称  当是测试模块的时候，添加（测试） 做标识
 * @param item
 */
const getAddTaskLable = (item: any) => {
  return item.status == 8 ? `${item.moduleName}(测试)` : item.moduleName;
};
/**
 *
 * @param res
 */
const handleSuccess = (res: any) => {
  if (res.data) {
    designDataCopy.value.bgImg = res.data[0].path;
  } else {
    ElMessage.error(res.msg);
  }
};
/**
 * 上传前验证
 * @param file
 */
const beforeUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 10;
  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 10MB!');
  }
  return isJPG && isLt2M;
};
/**
 * 清除背景图片
 */
const clearBG = () => {
  ElMessageBox.confirm('确定要清除背景图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      designDataCopy.value.bgImg = '';
      ElMessage({
        type: 'success',
        message: '清除成功!'
      });
    })
    .catch(() => {});
};
/**
 * 配置交互
 */
const interactionSetting = (obj: any) => {
  if (!obj.cptOption.interaction) {
    const setting = {
      title: '', //交互标题
      dataSource: 1, //1 静态数据 2表达式
      dataText: '',
      expression: '', //表达式
      contentList: [] //显示内容
    };
    obj.cptOption.interaction = setting;
  }
  interactionSettingDialog.value = true;
};
/**
 * 关闭交互配置
 */
const handleCloseInteraction = () => {
  interactionSettingDialog.value = false;
};

/**
 * 添加内容
 */
const addContent = () => {
  const obj = {
    label: '',
    value: '',
    expression: '',
    code: ''
  };
  currentCptCopy.value.cptOption.interaction.contentList.push(obj);
};
/**
 * 删除某个内容
 */
const delContent = (index: number) => {
  ElMessageBox.confirm('确定要删除该条内容吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      currentCptCopy.value.cptOption.interaction.contentList.splice(index, 1);
    })
    .catch(() => {});
};
/**
 * 某条数据的表达式
 */
const showExpression = (item: any, index: number) => {
  nowEditRow.value = item;
  handleCopy();
};

// 对外暴露方法
defineExpose({
  showCptConfig
});
</script>

<style lang="scss" scoped>
.cptTitle {
  line-height: 35px;
  text-align: center;
  background: #3f4b5f;
  color: #fff;
}
.closeItem:hover {
  cursor: pointer;
  background: #2b3340;
}
.customForm {
  padding: 0 6px 0 4px;
  overflow-y: scroll;
}
.uploadItem {
  width: 120px;
  height: 120px;
  text-align: center;
  line-height: 120px;
  border: 1px solid #ddd;
  cursor: pointer;
}
.inter-box {
  width: 100%;
  max-height: 500px;
  overflow: auto;
  .flex-row {
    display: flex;
    flex-direction: row;
    // align-items: center;
    margin-bottom: 10px;
    .label {
      width: 100px;
      text-align: right;
      margin-right: 10px;
    }
    .content {
      flex: 1;
    }
    .flex-column {
      display: flex;
      flex-direction: column;
    }
    .flex-item {
      flex: 1;
      display: flex;
      align-items: center;
      .flex-label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }
      .flex-content {
        flex: 1;
        display: flex;
      }
    }
    .flex-handle {
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
:deep(.json-editor-vue) {
  .jsoneditor-repair,
  .jsoneditor-search,
  .jsoneditor-undo,
  .jsoneditor-redo,
  .jsoneditor-poweredBy {
    display: none;
  }
}
</style>
