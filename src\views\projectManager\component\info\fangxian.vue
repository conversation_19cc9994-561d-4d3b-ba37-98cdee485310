<!-- 界址点描述 -->
<template>
  <div class="boundaryInfo-main">
    <el-table :data="geomAttribution" class="table-class" border :highlight-current-row="false" ref="multipleTable">
      <el-table-column label="界址点" prop="jzd" />
      <el-table-column label="目标点">
        <el-table-column label="X" prop="longitude" />
        <el-table-column label="Y" prop="latitude" />
      </el-table-column>
      <el-table-column label="放线点">
        <el-table-column label="X" prop="longitudeSettingOut" />
        <el-table-column label="Y" prop="latitudeSettingOut" />
      </el-table-column>
    </el-table>
    <el-dialog
      :title="chooseTile"
      v-model="dialogVisible"
      width="30%"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <template v-if="chooseType === 1">
        <div class="dialog-row" v-for="item in jzdlx" :key="item.value" @click="submitOneChange(item.label)">{{ item.label }}</div>
      </template>
      <template v-if="chooseType === 2">
        <div class="dialog-row" v-for="item in jzxlx" :key="item.value" @click="submitOneChange(item.label)">{{ item.label }}</div>
      </template>
      <template v-if="chooseType === 3">
        <div class="dialog-row" v-for="item in jzxwz" :key="item.value" @click="submitOneChange(item.label)">{{ item.label }}</div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useProjectStore } from '@/store/modules/project';

interface Option {
  label: string;
  value: number;
}

interface GeomAttributionItem {
  id: string | number;
  jzd: string;
  jzdType: string;
  jzx: string;
  jzxType: string;
  jzxLocation: string;
  longitude: number;
  latitude: number;
  longitudeSettingOut: number;
  latitudeSettingOut: number;
}

const store = useProjectStore();

const jzdlx = ref<Option[]>([
  { label: '钢钉', value: 1 },
  { label: '水泥桩', value: 2 },
  { label: '石灰柱', value: 3 },
  { label: '喷涂', value: 4 },
  { label: '瓷标志', value: 5 },
  { label: '无标准', value: 6 },
  { label: '其他', value: 7 }
]);

const jzxlx = ref<Option[]>([
  { label: '围墙', value: 7 },
  { label: '墙壁', value: 8 },
  { label: '栅栏', value: 9 },
  { label: '铁丝网', value: 10 },
  { label: '滴水线', value: 11 },
  { label: '路涯线', value: 12 },
  { label: '两点连线', value: 13 },
  { label: '其他', value: 14 }
]);

const jzxwz = ref<Option[]>([
  { label: '内', value: 15 },
  { label: '中', value: 16 },
  { label: '外', value: 17 }
]);

const chooseTile = ref<string>('界址点类型');
const dialogVisible = ref<boolean>(false);
const chooseType = ref<number>(1);
const chooseId = ref<string | number>('');
const multipleSelection = ref<GeomAttributionItem[]>([]);

const geomAttribution = computed(() => {
  const val = store.parcelInfo;
  const data = [...val.geomAttribution].sort((a, b) => {
    const aIndex = parseInt(a.jzd.substring(1));
    const bIndex = parseInt(b.jzd.substring(1));
    return aIndex - bIndex;
  });
  return data;
});
/**
 * 提交单个修改
 * @param item 类型
 */
const submitOneChange = (item: string) => {
  if (chooseId.value) {
    //单个改
    if (chooseType.value === 1) {
      geomAttribution.value.forEach((v) => {
        if (v.id === chooseId.value) {
          v.jzdType = item;
        }
      });
    } else if (chooseType.value === 2) {
      geomAttribution.value.forEach((v) => {
        if (v.id === chooseId.value) {
          v.jzxType = item;
        }
      });
    } else if (chooseType.value === 3) {
      geomAttribution.value.forEach((v) => {
        if (v.id === chooseId.value) {
          v.jzxLocation = item;
        }
      });
    }
  } else {
    //批量改
    if (chooseType.value === 1) {
      geomAttribution.value.forEach((v) => {
        if (multipleSelection.value.includes(v)) {
          v.jzdType = item;
        }
      });
    } else if (chooseType.value === 2) {
      geomAttribution.value.forEach((v) => {
        if (multipleSelection.value.includes(v)) {
          v.jzxType = item;
        }
      });
    } else if (chooseType.value === 3) {
      geomAttribution.value.forEach((v) => {
        if (multipleSelection.value.includes(v)) {
          v.jzxLocation = item;
        }
      });
    }
  }
  dialogVisible.value = false;
};

// 暴露方法给父组件
defineExpose({
  getGeomAttribution: () => geomAttribution.value
});
</script>

<style lang="scss" scoped>
:deep(.el-table--border th.el-table__cell) {
  border-bottom: 1px solid rgba(194, 194, 194, 0.2);
}
:deep(.el-table thead.is-group th.el-table__cell) {
  background: transparent;
}
:deep(.el-table__body tr:hover > td) {
  background-color: transparent !important;
}
:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid rgba(194, 194, 194, 0.2);
}
:deep(.el-table th.el-table__cell.is-leaf) {
  border-bottom: 1px solid rgba(194, 194, 194, 0.2);
}
:deep(.el-table tr) {
  background-color: transparent;
}
:deep(.el-table) {
  background-color: transparent;
  color: #fff;
}
:deep(.el-table .el-table__header-wrapper th) {
  color: #fff;
}
:deep(.el-table .el-table__header-wrapper th),
.el-table .el-table__fixed-header-wrapper th {
  background-color: rgb(0, 0, 0);
}
:deep(.el-table--group),
.el-table--border {
  border: 1px solid rgba(194, 194, 194, 0.2);
}
:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid rgba(194, 194, 194, 0.2);
}
:deep(.el-table::before),
.el-table--group::after,
.el-table--border::after {
  background-color: transparent;
}
.boundaryInfo-main {
  padding: 12px;
  height: 600px;
  overflow: auto;
}
.dialog-row {
  height: 40px;
  display: flex;
  align-items: center;
  border-bottom: #d3d3d3 solid 1px;
  cursor: pointer;
}
.dialog-row:hover {
  background: #d3d3d3;
  color: var(--current-color);
}
.handle {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
}
.normal-btn {
  color: #fff;
  background: transparent;
  border: 0.5px solid rgba(153, 153, 153, 0.5);
  font-size: 12px;
  padding: 7px 15px;
  margin-right: 10px;
  cursor: pointer;
  border-radius: 4px;
}
.dise-normal-btn {
  color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
}
.table-class {
  width: 100%;
  height: 500px;
  overflow: auto;
}
.table-class::-webkit-scrollbar {
  width: 4px;
}
.table-class::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  opacity: 0.2;
  background: rgb(255, 255, 255, 0.5);
}
.table-class::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgb(0, 0, 0, 0.5);
}
</style>
