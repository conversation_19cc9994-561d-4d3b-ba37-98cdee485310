import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  FormParams,
  ProcessDefinitionParams,
  ProcessInstanceParams,
  DeptQuery,
  UserQuery,
  TaskQuery,
  ApproveParams,
  ProcessGroupParams
} from '@/api/process/types';

/**
 * 流程审批中的第二步，新增表单
 * @param params 表单参数
 * @returns {AxiosPromise}
 */
export function addForm(params: FormParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/form/add',
    method: 'post',
    data: params
  });
}

/**
 * 获取审批的列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getFormList(params: FormParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/form/get',
    method: 'get',
    params: params
  });
}

/**
 * 获取当前的表单详情
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getFormDetial(params: FormParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/form/detail',
    method: 'get',
    params: params
  });
}

/**
 * 添加流程审批的第三步
 * @param params 流程定义参数
 * @returns {AxiosPromise}
 */
export function addProcess(params: ProcessDefinitionParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pdefinition/add',
    method: 'post',
    data: params
  });
}

/**
 * 编辑流程定义
 * @param params 流程定义参数
 * @returns {AxiosPromise}
 */
export function modifyProcess(params: ProcessDefinitionParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pdefinition/modify',
    method: 'post',
    data: params
  });
}

/**
 * 获取流程列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPdefinitionList(params: ProcessDefinitionParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pdefinition/get',
    method: 'get',
    params: params
  });
}

/**
 * 获取流程列表的第三步内容
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getPdefinitionDetial(params: ProcessDefinitionParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pdefinition/detail',
    method: 'get',
    params: params
  });
}

/**
 * 新增流程实例，这是在列表中填写的发起一个流程内容
 * @param params 流程实例参数
 * @returns {AxiosPromise}
 */
export function addInstance(params: ProcessInstanceParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/instance/add',
    method: 'post',
    data: params
  });
}

/**
 * 获取流程实例的列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getInstanceList(params: ProcessInstanceParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/instance/get',
    method: 'get',
    params: params
  });
}

/**
 * 获取审批流程定义的实例
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getInstanceDetial(params: ProcessInstanceParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/instance/detail',
    method: 'get',
    params: params
  });
}

/**
 * 用户撤销自己发起的流程（审批中的才可以撤销）
 * @param processInstanceId 流程实例ID
 * @returns {AxiosPromise}
 */
export function withdrawProcess(processInstanceId: string): AxiosPromise<any> {
  return request({
    url: `/qjt/process/relation/instance/withdraw/${processInstanceId}`,
    method: 'post'
  });
}

/**
 * 获取当前部门和部门下面的子级
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getDpts(params: DeptQuery): AxiosPromise<any> {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: params
  });
}

/**
 * 查询当前部门下的人
 * @param data 查询参数
 * @returns {AxiosPromise}
 */
export function getUsers(data: UserQuery): AxiosPromise<any> {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: data
  });
}

/**
 * 获取待办列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getUndoList(params: TaskQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/process/task/my/undo',
    method: 'get',
    params: params
  });
}

/**
 * 获取已办理的审批列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getDoneList(params: TaskQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/process/task/my/done',
    method: 'get',
    params: params
  });
}

/**
 * 提交审批的流程
 * @param params 审批参数
 * @returns {AxiosPromise}
 */
export function submitApprove(params: ApproveParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/task/approve',
    method: 'post',
    data: params
  });
}

/**
 * 获取我发起的流程
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getLaunchList(params: TaskQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/process/task/my/launch',
    method: 'get',
    params: params
  });
}

/**
 * 我收到的流程（主要来自于抄送）
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getNotifyList(params: TaskQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/process/task/notify/user',
    method: 'get',
    params: params
  });
}

/**
 * 获取流程分组
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getGroupList(params: ProcessGroupParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pgroup/list',
    method: 'post',
    data: {}
  });
}

/**
 * 添加流程分组
 * @param data 分组参数
 * @returns {AxiosPromise}
 */
export function addGroup(data: ProcessGroupParams): AxiosPromise<any> {
  return request({
    url: '/qjt/process/relation/pgroup/add',
    method: 'post',
    data: data
  });
}
