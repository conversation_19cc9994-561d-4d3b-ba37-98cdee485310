<template>
  <div class="zong-di-list-continer" v-loading.fullscreen.lock="fullscreenLoading" data-type="list" data-handle="list-title">
    <div class="continer-content">
      <!-- 横向变幻大小 -->
      <div class="list-title">
        <el-link type="primary" class="shaixuan" :underline="false" @click="shaixuan">筛选</el-link>
        <span class="title">
          <span v-show="!iscq">{{ isAZF ? '安置房源' : '数据列表' }}</span>
          <span v-show="iscq">项目资料</span>
        </span>
        <span @click="settingTree">
          <el-icon class="icon"><Setting /></el-icon>
        </span>
      </div>
      <div class="list-search">
        <el-input v-model="dialogSearch.parcelName" :placeholder="`请输入${title}名称/ID`" clearable size="small">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" size="small" class="search-button" @click="searchZD">搜索</el-button>
      </div>
      <div class="list-content" v-loading="listLoading">
        <div v-for="item in parcelListCopy" :key="item.id">
          <div
            v-if="item.container == 0"
            class="content-item"
            @click="handleParceItem(item)"
            :class="{ 'list-content-active': nowCheckedZD == item.id }"
          >
            <div class="item-name" :class="{ 'active-span': nowCheckedZD == item.id }" :title="item.parcelName">
              <div>
                {{ item.parcelName ? item.parcelName : item.typeName }}
              </div>
            </div>
            <svg-icon
              icon-class="tuopuCheck"
              class="icon-tuopuCheck"
              v-show="item.noCheckInt && item.noCheckInt != 0 && queryParams.pageSize <= 50"
            />
            <el-dropdown trigger="hover" :class="{ 'icon-success': !item.completeFlag, 'icon-error': item.completeFlag }">
              <el-icon class="icon-right">
                <MoreFilled />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-show="queryParams.pageSize <= 50" @click.stop="checkComplete(item.id)">
                    <el-icon :class="{ 'icon-success': !item.completeFlag, 'icon-error': item.completeFlag }">
                      <Tickets />
                    </el-icon>
                    <span :class="{ 'icon-success': !item.completeFlag, 'icon-error': item.completeFlag }">完整性检查</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.stop="handleDelete(item)">
                    <el-icon class="icon-delete">
                      <Delete />
                    </el-icon>
                    <span>删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-else class="content-item" @click="handleParceItem(item)" :class="{ 'list-content-active': nowCheckedZD == item.id }">
            <div class="item-name" :class="{ 'active-span': nowCheckedZD == item.id }" :title="item.parcelName">
              {{ item.typeName }}({{ item.children.length }})
            </div>
          </div>
          <!-- 图层要素展示子内容 -->
          <div v-if="nowChooseNodeType === 1 && nowChooseTcId == item.id" class="child-node">
            <div
              class="child-item"
              v-for="(ite, idx) in item.children"
              :key="idx"
              @click="handleParceItem(ite)"
              :class="{ 'list-content-active': nowCheckedZD == ite.id }"
            >
              {{ ite.parcelName }}
              <div v-if="nowCheckedZD == ite.id && ite.checked" class="child-tree">
                <el-tree
                  ref="graphicalTree"
                  :data="graphicalList"
                  node-key="id"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  default-expand-all
                  highlight-current
                  :current-node-key="defaultExpand"
                  @node-click="checkedNode"
                  :render-content="renderContent"
                >
                </el-tree>
              </div>
            </div>
          </div>
          <div v-if="nowCheckedZD == item.id && item.checked && nowChooseNodeType !== 1" class="child-tree">
            <el-tree
              ref="graphicalTree"
              :data="graphicalList"
              node-key="id"
              :props="defaultProps"
              :expand-on-click-node="false"
              default-expand-all
              highlight-current
              :current-node-key="defaultExpand"
              @node-click="checkedNode"
              :render-content="renderContent"
            >
            </el-tree>
          </div>
        </div>
        <div class="list-empty" v-if="parcelListCopy.length == 0">
          <el-empty :image="emptyImg"></el-empty>
        </div>
      </div>
      <div class="list-pagination">
        <div class="pagination-container">
          <pagination
            size="small"
            :total="totalCount || props.total"
            v-model:page="queryParamsCopy.pageNum"
            v-model:limit="queryParamsCopy.pageSize"
            :pager-count="pagerCount"
            :background="false"
            layout="total, sizes, pager"
            :page-sizes="[20, 50, 100, 1000, 2000]"
            @pagination="getParceListByPage"
          />
        </div>
      </div>
    </div>
    <!-- 筛选详情展示 -->
    <div class="search-div" :class="{ 'min-search': !isUnfold, 'min-width': !showDetail }" v-show="showSearchMsg">
      <div class="search-title" @click="showDetail = !showDetail">
        <div>筛选条件:</div>
        <div>
          <el-icon v-if="showDetail"><ArrowUp /></el-icon>
          <el-icon v-else><ArrowDown /></el-icon>
        </div>
      </div>
      <div v-if="showDetail" style="margin-top: 10px">
        <div class="search-item" v-if="dialogSearch.parcelName">数据名称：{{ dialogSearch.parcelName }}</div>
        <div class="search-item" v-if="dialogSearch.areaCode">行政区划：{{ dialogSearch.areaCodeName }}</div>
        <div class="search-item" v-if="dialogSearch.createDate">
          创建时间：{{ formatDateType(dialogSearch.createDate[0]) }} -
          {{ formatDateType(dialogSearch.createDate[1]) }}
        </div>
        <div class="search-item" v-if="dialogSearch.updateDate">
          最后修改时间：{{ formatDateType(dialogSearch.updateDate[0]) }} -
          {{ formatDateType(dialogSearch.updateDate[1]) }}
        </div>
        <div class="search-item" v-if="dialogSearch.createUserName">采集人：{{ dialogSearch.createUserName }}</div>
        <div class="search-item" v-if="dialogSearch.optUserName">最后修改人：{{ dialogSearch.optUserName }}</div>
        <div class="search-item" v-if="dialogSearch.taskId">任务：{{ getTaskName(dialogSearch.taskId) }}</div>
      </div>
    </div>
    <!-- 公共导出 -->
    <el-dialog
      :title="nowDownMsg.exportName"
      v-model="publicDownDialoig"
      width="874px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <el-form :model="downLoadMsg" :rules="downLoadMsgRule" label-position="top" ref="downLoadRef" class="demo-ruleForm">
        <el-form-item label="选择坐标系统" :prop="nowDownMsg.coordinate.must == 1 ? 'onWkid' : ''">
          <el-cascader
            v-model="downLoadMsg.onWkid"
            :options="wkidList"
            :show-all-levels="false"
            style="width: 100%"
            :placeholder="nowDownMsg.coordinate.placeholder"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="数据列表" :prop="nowDownMsg.dataList.must == 1 ? 'zdList' : ''">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            :placeholder="nowDownMsg.dataList.placeholder"
            v-model="downLoadMsg.zdListNames"
            @click="chooseData"
            readonly
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <template #label>
            <span>是否分批导出</span>
            <span class="tip-class">
              <el-tooltip
                class="item"
                effect="dark"
                content="分批导出默认是10条一批次，如果没有超过10条不会分批。（分批导出会生成批次号，方便排查问题）"
                placement="top-start"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <div class="flex items-center">
            <div>
              <el-radio-group v-model="isBatch">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
              </el-radio-group>
            </div>
            <div style="width: 200px">
              <el-select v-show="isBatch == 1" v-model="batchNum" placeholder="请选择每个批次数量" style="margin-left: 20px">
                <el-option v-for="item in 10" :key="item" :label="`${item}条`" :value="item"> </el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="面积计算方式">
          <el-radio-group v-model="downLoadMsg.areaType">
            <el-radio :value="1">投影坐标系</el-radio>
            <el-radio :value="2">大地坐标系</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导出shp编码">
          <template #label>
            <span>导出shp编码</span>
            <span class="tip-class">
              <el-tooltip
                class="item"
                effect="dark"
                content="一般情况下：UTF-8适配于arcmap 10.7版本,GBK适配于arcmap 10.2及以下版本"
                placement="top-start"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-radio-group v-model="downLoadMsg.code">
            <el-radio value="UTF-8">UTF-8</el-radio>
            <el-radio value="GBK">GBK</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="包围盒类型" v-if="showBoxType">
          <div class="tip-class">
            <el-tooltip
              class="item"
              effect="dark"
              content="包围盒默认是按照矩形，为了适配以前老数据（已入库）用户可选择按正方形计算包围盒，会影响J1位置判断"
              placement="top-start"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <el-radio-group v-model="downLoadMsg.boxType">
            <el-radio :label="1">矩形</el-radio>
            <el-radio :label="2">正方形</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="公司信息">
          <el-select v-model="downLoadMsg.companyName" placeholder="请选择" style="width: 100%" @change="changeCompany">
            <el-option v-for="item in companyList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-checkbox v-model="isNoDel" v-if="userStore.user['userName'] === '18285070490'">不删除服务器数据</el-checkbox>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitDown">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="downLoadMsg.zdList"
      :isManager="managerDialog"
      :ifTree="ifTree"
      :isKJ="isKJ"
      :ruleTree="ruleTree"
      :ruleIds="ruleIds"
    ></searchData>
    <!-- 进度条弹窗 -->
    <el-dialog
      title="数据下载中"
      v-model="progressDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleCloseProgress"
      :width="showBatch ? '500px' : '300px'"
    >
      <!-- 不是批量导出的时候 -->
      <div class="down-dialog" v-if="!showBatch">
        <el-progress :stroke-width="16" type="circle" :percentage="progress" :status="downStatus as any"></el-progress>
        <div style="margin-top: 10px">{{ downMsg }}</div>
      </div>
      <!-- 批量导出的时候 -->
      <div class="down-content" v-else>
        <div class="flex-row" style="font-weight: bold">总进度：</div>
        <div class="flex-row">
          <el-progress :text-inside="true" :stroke-width="26" :percentage="batchProgress"></el-progress>
          <div style="margin-top: 10px">{{ batchMsg }}</div>
        </div>
        <div class="flex-row" style="font-weight: bold">当前导出进度：</div>
        <div class="flex-row">
          <el-progress :text-inside="true" :stroke-width="26" :percentage="oneceProgress"></el-progress>
          <div style="margin-top: 10px">{{ downMsg }}</div>
        </div>
      </div>
    </el-dialog>
    <!-- 筛选弹窗 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      @editCondition="editCondition"
      :moduleIdPop="moduleId"
      :isShowSpe="isShowSpe"
    ></dataSearch>

    <!-- 导入/更新SHP -->
    <update-shp :uploadProjectDialog="shpUploadDialog" :moduleId="moduleId" @closeDialog="handleCloseShpDialog"></update-shp>
    <!-- 完整性检查弹窗 -->
    <completeness
      :zdid="checkZdId"
      :completenessDialog="completenessDialog"
      @closeCompleteDialog="closeCompleteDialog"
      :ruleTree="ruleTree"
    ></completeness>

    <!-- 选择任务弹窗 -->
    <el-dialog title="请选择任务" v-model="taskDialog" :close-on-click-modal="false" :modal-append-to-body="false" :append-to-body="true" width="30%">
      <el-select v-model="chooseTaskId" placeholder="请选择任务" style="width: 100%">
        <el-option v-for="item in ourTaskList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitTask">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 移植5.0的勘界 -->
    <oldKJDialog
      v-show="kjDialog"
      :companyData="companyData"
      :kjDialog="kjDialog"
      @closeKJDialog="closeKJDialog"
      @sumbitDownloadKJ="sumbitDownloadKJ"
    ></oldKJDialog>
    <!-- 标注弹窗 -->
    <labelDialogComponent
      :labelDialog="labelDialog"
      :moduleId="moduleId"
      @handleCloseLabel="handleCloseLabel"
      @submitField="submitField"
    ></labelDialogComponent>
    <!-- 用excel导入更新 -->
    <updateExcel :uploadExcelDialog="uploadExcelDialog" @closeExcelUpload="closeExcelUpload" :moduleId="moduleId"></updateExcel>
    <!-- 特殊用excel导入更新 适用杭州的那张村提供的彩色复杂表格 -->
    <updateSpe :updateSpeDialog="updateSpeDialog" :moduleId="moduleId" @closeSpeUpload="closeSpeUpload"></updateSpe>
    <!-- shp更新界址点、界址线弹窗 -->
    <updateShpChild :updateShpChildDialog="updateShpChildDialog" :moduleId="moduleId" @closeShpChild="closeShpChild"></updateShpChild>
    <!-- 批量更新图片 -->
    <template v-if="updateTPFileDialog">
      <updateTPFile :updateTPFileDialog="updateTPFileDialog" :moduleId="moduleId" @closeUpdateTP="closeUpdateTP"></updateTPFile>
    </template>
    <!-- 批量更新宗地和房产图 -->
    <updateTPZDFC :updateZDFCDialog="updateZDFCDialog" :moduleId="moduleId" @closeUpdateZDFC="closeUpdateZDFC"></updateTPZDFC>
    <!-- 刷新表达式 -->
    <refreshExpress
      :refreshExpressDialog="refreshExpressDialog"
      @closeRefreshExpress="closeRefreshExpress"
      :moduleIdPop="moduleId"
      :ruleTree="ruleTree"
    ></refreshExpress>
    <!-- 切换弹窗 -->
    <el-dialog
      title="节点树切换"
      v-model="catNodeDialog"
      width="680px"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="handleCloseCatNode"
    >
      <el-form :model="catMsg" :rules="catMsgRules" ref="catMsgRef" label-width="120px" class="demo-ruleForm">
        <el-form-item label="节点类型" prop="nodeType">
          <el-radio-group v-model="catMsg.nodeType" @input="changeType">
            <el-radio :value="1">要素节点</el-radio>
            <el-radio :value="2">图层节点</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="显示的节点树" prop="ruleIds">
          <el-checkbox-group v-model="catMsg.ruleIds">
            <el-checkbox :value="item.ruleId" v-for="(item, index) in firstNodes" :key="index" :disabled="item.disable">{{
              item.typeName
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseCatNode">取 消</el-button>
          <el-button type="primary" @click="submitCat">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 流程任务数据解锁管理 -->
    <flowTaskManager :flowTaskDailog="flowTaskDailog" @closeFlowTask="closeFlowTask" :moduleId="moduleId"></flowTaskManager>
    <el-dialog
      title="请选择下载方式"
      v-model="downloadTypeDialog"
      width="800px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleCloseDownType"
    >
      <div class="dialog-box">
        <div class="dialog-item" @click="changeDownloadType(1)" :class="{ 'dialog-item-active': downloadType == 1 }">
          <img src="../../../assets/images/xunlei.png" alt="" class="left" />
          <div class="right">
            <span class="top">迅雷下载</span>
            <span class="bottom">高速稳定下载</span>
          </div>
          <div class="end">
            <el-checkbox v-model="xlDown" class="custom-size"></el-checkbox>
          </div>
        </div>
        <div class="dialog-item" @click="changeDownloadType(2)" :class="{ 'dialog-item-active': downloadType == 2 }">
          <img src="../../../assets/images/normal.png" alt="" class="left" />
          <div class="right">
            <span class="top">普通下载</span>
            <span class="bottom">浏览器下载</span>
          </div>
          <div class="end">
            <el-checkbox v-model="ptDown" class="custom-size"></el-checkbox>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDownType">取 消</el-button>
          <el-button type="primary" @click="submitDaownType">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 设置显示图形内容 根据选择的树得到 -->
    <showTreeSetting
      :treeSettingDialog="treeSettingDialog"
      @handleCloseTreeSetting="handleCloseTreeSetting"
      @handleSubmitTreeSetting="handleSubmitTreeSetting"
      :ruleTree="ruleTree"
      :queryParams="queryParams"
      :showGraphs="showGraphs"
    ></showTreeSetting>
    <!-- 批量更新字段 -->
    <bitchEditField
      :bitchUpdateFieldDialog="bitchUpdateFieldDialog"
      :ruleTree="ruleTree"
      :moduleId="queryParams.moduleId"
      @handleCloseBitchUpdateFieldDialog="handleCloseBitchUpdateFieldDialog"
    ></bitchEditField>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { formatDateType } from '@/utils/filters';
// 图片部分
import shouqiIcon from '@/assets/images/shouqi.png';
import zhankaizongdi from '@/assets/images/zhankaizongdi.png';
import emptyImg from '@/assets/images/empty.png';
// Element Plus 图标
import {
  Setting,
  Search,
  ArrowDown,
  ArrowUp,
  ArrowRight,
  QuestionFilled,
  Tickets,
  Loading,
  Delete,
  MoreFilled,
  Open,
  TurnOff
} from '@element-plus/icons-vue';
// api部分
import { findAsyncMsg as findAsyncMsgApi, findAsyncFileBrowser, exportPcNew, selectIfOrder } from '@/api/project';
import {
  exportSettingList,
  getExportDetail,
  downLoadPublic,
  downLoadPublicCheck,
  selectParcelOne,
  getPlaceList,
  selectParcelOneList
} from '@/api/modal';
import { listUser } from '@/api/system/user';
import { getSearchTask } from '@/api/task';
import { getCompany as getCompanyApi } from '@/api/control';
import { operaParcel, deleteParcel } from '@/api/project';
// 组件部分
import dataSearch from '@/components/dataSearch/index.vue';
import updateShp from '@/components/updateSHP/index.vue';
import completeness from '@/components/completeness/index.vue';
import searchData from './searchData/index.vue';
import oldKJDialog from '@/components/oldKJDialog/index.vue';
import labelDialogComponent from './labelDialog.vue';
import updateExcel from '@/components/updateExcel/index.vue';
import updateSpe from '@/components/updateSpe/index.vue';
import updateShpChild from '@/components/updateShpChild/index.vue';
import updateTPFile from '@/components/updateTPFile/index.vue';
import updateTPZDFC from '@/components/updateTPZDFC/index.vue';
import refreshExpress from '@/components/refreshExpress/index.vue';
import flowTaskManager from '@/components/flowTaskManager/index.vue';
import showTreeSetting from '@/components/showTreeSetting/index.vue';
import bitchEditField from './bitchEditField.vue';
import SvgIcon from '@/components/SvgIcon/index.vue';
import AuthImg from '@/components/authImg/index.vue';
import { ElIcon } from 'element-plus';
// 工具函数
import { checkPermi } from '@/utils/permission';
import { isArray } from '@/utils/validate';
// pinia
import { useAppStore, useProjectStore, useUserStore } from '@/store';
import { log } from 'mathjs';
import { list } from '@/api/monitor/loginInfo';
const appStore = useAppStore();
const userStore = useUserStore();
const route = useRoute();
const projectStore = useProjectStore();
const totalCount = ref();
// 定义emit
const emit = defineEmits<{
  (e: 'getParceItem', id: number): void;
  (e: 'noChangeList'): void;
  (e: 'getLinyeData', flag?: boolean): void;
  (e: 'labelCQMsg', cqMsg: any, id: number, name: string, isShow: boolean): void;
  (e: 'initChangeCenterToAZF', list: any[]): void;
  (e: 'initAzfCenter'): void;
  (e: 'changeGraph', node: any): void;
  (e: 'clearCQLabel'): void;
  (e: 'getParmas', parmas: any): void;
  (e: 'removerLabelLayer'): void;
  (e: 'changeIsShowJZD'): void;
  (e: 'drawLabel', fieldName: string, linkId: number): void;
  (e: 'initShowGraphs', list: any[], ruleIds: any[]): void;
  (e: 'dwarChildNodeToSub', list: any[]): void;
  (e: 'sendRuleId', ruleId: number): void;
}>();

// 接收的参数部分
interface QueryParams {
  pageNum: number;
  pageSize: number;
  parcelName: string;
  moduleId: number | string;
  areaCode: string;
}

interface Props {
  parcelList: any[];
  total: number;
  queryParams: QueryParams;
  nowCheckedZD: number;
  pageCount: number;
  title: string;
  ruleTree: any[];
  mainHeight: number;
  isShowJZD: boolean;
  firstNodes: any[];
  isKJ: boolean;
  toolType: number;
  isAZFI: boolean;
  ruleIds: any[];
}
interface ModuleInfo {
  moudleId: string;
}
const props = withDefaults(defineProps<Props>(), {
  parcelList: () => [],
  total: 0,
  queryParams: () => ({
    pageNum: 1,
    pageSize: 50,
    parcelName: '',
    moduleId: undefined,
    areaCode: ''
  }),
  nowCheckedZD: 0,
  pageCount: 0,
  title: '',
  ruleTree: () => [],
  mainHeight: 0,
  isShowJZD: true,
  firstNodes: () => [],
  isKJ: false,
  toolType: 0,
  isAZFI: false,
  ruleIds: () => []
});

// 定义变量部分

const sqIcon = ref(shouqiIcon); // 展开收起图标
const zdIcon = ref(zhankaizongdi); // 收起的宗地Icon
const paraceName = ref(''); // 搜索的宗地名
const isUnfold = ref(true); // 是否展开
const fullscreenLoading = ref(false);
const shpDialog = ref(false); // 导出shp弹窗
const wkidList = reactive([
  {
    value: 'CGCS_2000',
    label: 'CGCS_2000',
    children: [
      {
        value: 'cgcs2000_3',
        label: '三度带',
        children: []
      },
      {
        value: 'cgcs2000_6',
        label: '六度带',
        children: []
      }
    ]
  }
]);
const pagerCount = ref(5);
const exportBtn = ref([]); // 导出按钮
const publicDownDialoig = ref(false); // 公共导出弹窗
let nowDownMsg: any = reactive({
  //选中的导出设置内容
  coordinate: {
    placeholder: '',
    must: 1,
    defaultValue: undefined
  },
  dataList: {
    placeholder: ''
  },
  exportName: '',
  detail: {
    fileName: ''
  },
  areaType: undefined
});
const downLoadMsg: any = ref({
  //导出设置
  zdList: [],
  companyName: ''
});
const downLoadMsgRule = ref({}); //导出设置校验
const searchDialog = ref(false); // 数据筛选弹窗
let shpGdbTree = reactive({
  //导出的树结构迭代 整理出来shp和gdb列表
  shp: [],
  gdb: [],
  pic: [],
  word: [],
  excel: [],
  fileMap: []
});
const progress = ref(0); // 进度
const progressDialog = ref(false); // 进度条弹窗
const downLoadFileName = ref(''); // 导出的文件名
const graphicalList = ref([]); // 图形信息 树
const defaultProps = reactive({
  children: 'list',
  label: 'parcelName'
});
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';
const itemGraphicalList = ref([]); // 把得到详情的树整理成列表
const defaultExpand = ref(0);
const shaixuanDialog = ref(false); // 筛选弹窗
const queryParamsCopy = computed(() => props.queryParams);
const dialogSearch: any = ref({
  //弹窗筛选
  areaCode: queryParamsCopy.value.areaCode, // 行政区划
  createDate: '', //创建时间
  updateDate: '', //最后修改时间
  createUserId: '', //采集人
  updateUserId: '', //最后修改人
  taskId: '', //任务id
  allocation: '', //查询是否分配任务 如果选择了任务设置为true
  pageNum: queryParamsCopy.value.pageNum,
  pageSize: queryParamsCopy.value.pageSize,
  parcelName: queryParamsCopy.value.parcelName,
  moduleId: queryParamsCopy.value.moduleId,
  conditionFields: [], //字段查询
  ruleIds: [],
  parcelCode: null,
  ifCheck: false,
  downLoadId: undefined,
  createTimeStart: '',
  createTimeEnd: '',
  updateTimeStart: '',
  updateTimeEnd: '',
  createUserName: '',
  optUserName: '',
  optUserId: '',
  express: false //是否查询表达式异常的数据
});
const userPages = ref(0);
const searchUser = reactive({
  pageNum: 1,
  pageSize: 20
});
const userList = ref([]); // 用户列表
const taskList = ref([]); // 任务列表
const chooseUserType = ref(1); // 选择人员的类型 1采集人 2最后修改人
const chooseUserDialog = ref(false); // 选择人员弹窗
const chooseUserId = ref(0); // 当前需要反显的人员id
const chooseUserTitle = ref(''); // 选择人员的title
const showSearchMsg = ref(false); // 是否展示筛选结构内容
const showDetail = ref(true); // 展示详细筛选条件
const shpUploadDialog = ref(false); // 导入或者更新shp
const currentPercent = ref(0); // 当前下载进度
let loadingProgessInstance: any = null;
const managerDialog = ref(false); // 数据管理
const checkZdId = ref(undefined); // 当前需要检查完整性的宗地id
const completenessDialog = ref(false); // 数据完整性弹窗
// const addParcelDialog = ref(false); // 打开新增实例数据弹窗
const ourTaskList = ref([]); // 我能看到的任务
const taskDialog = ref(false); // 选择任务弹窗
const chooseTaskId = ref(''); // 选择的任务id 用于新增实例数据
const downMsg = ref(''); // 导出进度信息
const downStatus = ref('');
const lastBytesReceived = ref(0);
const lastBytesSent = ref(0);
const lastTimeStamp = ref(0);
const networkSpeed = ref(0);
const isContinue = ref(false); // 是否继续下载 用于关闭了进度条就不继续下载
const isChecked = ref(false);
const kjDialog = ref(false); // 勘界弹窗
const isDefault = ref(false); // 是否是默认导出
const labelDialog = ref(false); // 标注弹窗
const moduleId = ref(0); // 模块id
const companyList = ref<Array<{ label: string; value: string }>>([]); // 公司列表
const uploadExcelDialog = ref(false); // excel导入更新
const updateSpeDialog = ref(false); // 特殊 excel固定导入更新
const updateShpChildDialog = ref(false); // shp更新界址点界址线子要素弹窗
const updateTPFileDialog = ref(false); // 批量更新图片
const updateZDFCDialog = ref(false); // 批量更新房产或者图片
const refreshExpressDialog = ref(false); // 筛选表达式弹窗
const isShowSpe = ref(false); // 切换要素节点和图层节点 当有图层节点的时候才切换
const catNodeDialog = ref(false); // 切换类型弹窗
const catMsg = reactive({
  nodeType: 1, // 1要素节点 2图层要素节点
  ruleIds: [] // 选择的树列表
});
const catMsgRules = reactive({
  ruleIds: [{ required: true, message: '请至少选择一颗树', trigger: 'change' }]
});
const flowTaskDailog = ref(false); // 流程任务数据解锁管理
const downloadTypeDialog = ref(false); // 选择下载方式弹窗
const downLoadId = ref(null); // 下载的id
const ifTree = ref(true); // 是否需要查询树
const downloadType = ref(null); // 下载方式 1迅雷下载 2普通下载
const fileSize = ref(null); // 文件大小
const progressEnd = ref(null); // 进度情况
const isNoDel = ref(false); // 是否删除服务器zip包 默认都是删除的
const isAZF = ref(false); // 是否是安置房
const iscq = ref(false); // 是否是拆迁
const treeSettingDialog = ref(false);
const showGraphs = ref([]); // 需要显示图形的节点id(对于数据来说是ruleId)
const isBatch = ref(0); // 是否批量
const showBatch = ref(false); // 是否显示批量
const batchProgress = ref(0); // 批量导出进度
const batchMsg = ref(''); // 批量导出信息
const oneceProgress = ref(0); // 单次导出进度
const nowBatch = ref(0); // 批次当前量
const batchNum = ref(10); // 每个批次数量
const bitchUpdateFieldDialog = ref(false); // 批量更新字段
const downLoadRef = ref(null); // 导出设置弹窗
const catMsgRef = ref(null); // 切换类型弹窗
const parcelListCopy = computed(() => props.parcelList);
const companyData = ref(); // 公司数据，请求接口返回的
const showBoxType = ref(false); // 是否显示包围盒类型 导出 只有本地和神马勘测公司才显示
const listLoading = ref(false); // 列表加载中
const graphicalTree = ref(null); // 图形树
const nowChooseNodeType = ref(0); // 0要素节点 1图层要素节点
const nowChooseTcId = ref(0); //当前选择图层要素的图层id

// computed部分
const ptDown = computed(() => {
  let flg = false;
  if (downloadType.value == 2) {
    flg = true;
  }
  return flg;
});

const xlDown = computed(() => {
  let flg = false;
  if (downloadType.value == 1) {
    flg = true;
  }
  return flg;
});

// watch部分
watch(
  () => appStore.needRefresh,
  (val) => {
    if (val) {
      searchZD(); // 刷新宗地列表
      appStore.setRefresh(false); // 重置标记
    }
  }
);

// 监听 props.nowCheckedZD
watch(
  () => props.nowCheckedZD,
  (val) => {
    if (val) {
      parcelListCopy.value.forEach((v) => {
        v.checked = false;
        if (v.id == val) {
          v.checked = true;
        }
      });
    }
  },
  { deep: true }
);
// 监听 dialogSearch
watch(
  dialogSearch.value,
  (val: any) => {
    if (val.areaCode || val.createDate || val.optUserId || val.createUserId || val.updateUserId || val.taskId || val.parcelName) {
      showSearchMsg.value = true;
    } else {
      showSearchMsg.value = false;
    }
  },
  { deep: true }
);

// 监听 currentPercent
watch(
  currentPercent,
  (val: any) => {
    const percent = val.toFixed(2);
    let planNum = 0;
    const size = (fileSize.value / 1024 / 1024).toFixed(2);
    if (progressEnd.value && progressEnd.value.loaded) {
      planNum = Number(((progressEnd.value.loaded / fileSize.value) * 100).toFixed(2));
    }
  },
  { deep: true }
);

// --- 定义方法---
const setIsShowSpe = () => {
  isShowSpe.value = true;
};

/**
 * 获取导出设置
 */
const getExportSetting = () => {
  //判断公司id是神马勘测或者是神马测试环境 允许配置包围盒类型
  if (userStore.user.companyId == '9bd5578788ae4f8f9577b56ab8baf088' || userStore.user.companyId == '970e14222c2340dba1877be4ed81df51') {
    showBoxType.value = true;
  }
  const moduleIdTemp = Number(projectStore.proModuleId);
  moduleId.value = moduleIdTemp;
  const params = {
    moduleId: moduleIdTemp,
    companyId: undefined
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  exportSettingList(params).then((res) => {
    if (res.code == 200) {
      if (res.data.length != 0) {
        exportBtn.value = res.data;
        exportBtn.value.unshift({ exportName: '系统默认导出', id: -101 });
      } else {
        exportBtn.value.push({ exportName: '系统默认导出', id: -101 });
      }
      if (props.toolType == 1) {
        //特殊判断勘界类型
        if (res.data.length == 0) {
          exportBtn.value = [];
          exportBtn.value.unshift({ exportName: '系统默认导出', id: -101 });
        }
        exportBtn.value.push({ exportName: '勘界导出', id: -1 });
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 搜索宗地
 */
const searchZD = () => {
  queryParamsCopy.value.pageSize = queryParamsCopy.value.pageSize;
  queryParamsCopy.value.pageNum = 1;
  queryParamsCopy.value.parcelName = dialogSearch.value.parcelName;
  submitSearch();
};

const getParceListByPage = () => {
  queryParamsCopy.value.pageSize = queryParamsCopy.value.pageSize;
  queryParamsCopy.value.pageNum = queryParamsCopy.value.pageNum;
  dialogSearch.value.pageSize = queryParamsCopy.value.pageSize;
  dialogSearch.value.pageNum = queryParamsCopy.value.pageNum;
  dialogSearch.value.ifPage = true;
  submitSearch();
};

/**
 * 点击某一项宗地
 */
const handleParceItem = (item) => {
  if (props.nowCheckedZD != item.id) {
    item.checked = true;
    if (item.container !== 0 && item.isMain) {
      nowChooseNodeType.value = 1;
      nowChooseTcId.value = item.id;
    } else if (item.container !== 0 && !item.isMain) {
      // nowChooseNodeType.value = 0;
      emit('getParceItem', item.id);
    } else {
      nowChooseNodeType.value = 0;
      emit('getParceItem', item.id);
    }
  } else {
    // 宗地列表变化但是不需要重新绘制地图
    emit('noChangeList');
    item.checked = !item.checked;
  }
};

/**
 * 返回新的command对象
 */
const beforeHandleCommand = (item) => {
  //index我这里是遍历的角标，即你需要传递的额外参数
  return item;
};

/**
 * 获取公司信息
 */
const getCompany = () => {
  getCompanyApi().then((res) => {
    if (res.code == 200) {
      companyData.value = res.data;
      companyList.value = [];
      companyList.value.push({
        label: `${res.data.companyName}(主公司)`,
        value: res.data.companyName
      });
      res.data.sysCompanyChildList.forEach((v) => {
        companyList.value.push({
          label: `${v.companyName}(关联公司)`,
          value: v.companyName
        });
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 选中某个导出按钮
 */
const handleCommand = (data) => {
  if (data.id == -1) {
    //勘界专用 5.0移植过来的
    const flg = checkPermi(['qjt:kanjie:export']);
    if (!flg) {
      ElMessageBox.alert('您没有权限导出勘界，请联系管理员！！！', '提示', {
        confirmButtonText: '确定',
        callback: (action) => {}
      });
      return;
    }
    kjDialog.value = true;
  } else if (data.id == -101) {
    //默认导出
    initDownMsg();
    nowDownMsg.exportName = '默认导出';
    downLoadMsg.value.onWkid = ['CGCS_2000', 'cgcs2000_3', 35];
    downLoadMsg.value.areaType = 1;
    isDefault.value = true;
  } else {
    isDefault.value = false;
    getExportDetail(data.id).then((res) => {
      if (res.code == 200) {
        nowDownMsg = { ...res.data };
        // 先初始化树
        shpGdbTree = {
          shp: [],
          gdb: [],
          pic: [],
          word: [],
          excel: [],
          fileMap: []
        }; //导出的树结构迭代 整理出来shp和gdb列表
        downLoadFileName.value = nowDownMsg.detail.fileName;
        getShpGdb([nowDownMsg.detail]);
        initDownMsg();
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 初始化导出设置内容
const initDownMsg = () => {
  const downLoadMsgTemp = {
    zdList: [], //选中的宗地
    zdListNames: '', //选择的宗地中文名
    companyName: companyList.value[0].value,
    areaType: nowDownMsg.areaType,
    code: 'UTF-8',
    onWkid: undefined
  }; //导出设置
  if (showBoxType.value) {
    downLoadMsgTemp.boxType = 1;
  }
  const downLoadMsgRuleTemp: any = {
    zdList: [{ required: true, message: '请选择数据', trigger: 'change' }]
  }; //导出设置校验
  if (nowDownMsg.coordinate.disable == 0) {
    //坐标系
    if (nowDownMsg.coordinate.defaultValue) {
      downLoadMsgTemp.onWkid = nowDownMsg.coordinate.defaultValue;
    } else {
      downLoadMsgTemp.onWkid = '';
    }
    if (nowDownMsg.coordinate.must == 1) {
      //必填
      const rules = [{ required: true, message: '请选择坐标系', trigger: 'change' }];
      downLoadMsgRuleTemp.onWkid = rules;
    }
  }

  downLoadMsg.value = downLoadMsgTemp;
  downLoadMsgRule.value = downLoadMsgRuleTemp;
  isChecked.value = false;
  downloadType.value = null;
  isNoDel.value = false;
  publicDownDialoig.value = true;
};

/**
 * 关闭公共导出弹窗
 */
const handleClose = () => {
  downLoadMsg.value = {
    zdList: []
  };
  publicDownDialoig.value = false;
};

/**
 * 关闭筛选数据弹窗
 */
const closeSearchDialog = () => {
  // 需要重新请求下列表 因为可能管理删除了某些数据
  if (managerDialog.value) {
    // 管理才进行重新查询
    emit('getLinyeData', true);
  }
  downLoadMsg.value.zdList = [];
  downLoadMsg.value.zdListNames = '';
  searchDialog.value = false;
};

/**
 * 选择数据
 */
const chooseData = () => {
  managerDialog.value = false;
  ifTree.value = false;
  searchDialog.value = true;
};

/**
 * 得到筛选要素数据
 */
const getChooseData = (list) => {
  downLoadMsg.value.zdList = list;
  const names = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  downLoadMsg.value.zdListNames = names.join(',');
  searchDialog.value = false;
};

/**
 * 真正导出
 */
const submitDown = () => {
  downLoadRef.value.validate((valid) => {
    if (valid) {
      const wkId = 3857;
      const parmas: any = {
        exportId: nowDownMsg.id,
        moduleId: nowDownMsg.moduleId,
        wkId: wkId,
        areaType: downLoadMsg.value.areaType,
        code: downLoadMsg.value.code,
        boxType: downLoadMsg.value.boxType
      };
      if (nowDownMsg.coordinate.disable == 1) {
        //坐标系统不显示的时候需要主动去取默认值
        if (nowDownMsg.coordinate.defaultValue[2] < 25) {
          //六度带
          parmas.wkId = 4478 + nowDownMsg.coordinate.defaultValue[2];
        } else if (nowDownMsg.coordinate.defaultValue[2] >= 25) {
          //三度带
          parmas.wkId = 4513 + nowDownMsg.coordinate.defaultValue[2] - 25;
        }
      }
      if (downLoadMsg.value.onWkid) {
        if (downLoadMsg.value.onWkid[2] < 25) {
          //六度带
          parmas.wkId = 4478 + downLoadMsg.value.onWkid[2];
        } else if (downLoadMsg.value.onWkid[2] >= 25) {
          //三度带
          parmas.wkId = 4513 + downLoadMsg.value.onWkid[2] - 25;
        }
      }
      const zdId = [];
      downLoadMsg.value.zdList.forEach((v) => {
        zdId.push(v.id);
      });
      if (!isDefault.value) {
        //默认导出不需要进入
        const gdbMap = [];
        const shpMap = [];
        const picMap = [];
        const wordMap = [];
        const excelMap = [];
        const fileMap = [];
        shpGdbTree.shp.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          shpMap.push(obj);
        });
        shpGdbTree.gdb.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          gdbMap.push(obj);
        });
        shpGdbTree.pic.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          picMap.push(obj);
        });
        shpGdbTree.word.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          wordMap.push(obj);
        });
        shpGdbTree.excel.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          excelMap.push(obj);
        });
        shpGdbTree.fileMap.forEach((v) => {
          const obj = {
            exportDetailId: v,
            zdId: zdId
          };
          fileMap.push(obj);
        });
        parmas.gdbMap = gdbMap;
        parmas.shpMap = shpMap;
        parmas.picMap = picMap;
        parmas.wordMap = wordMap;
        parmas.excelMap = excelMap;
        parmas.fileMap = fileMap;
        parmas.companyName = downLoadMsg.value.companyName;
      }
      parmas.zdId = zdId;
      downLoadMsg.value = {
        zdList: [], //选中的宗地
        zdListNames: '', //选择的宗地中文名
        onWkid: ''
      };
      if (isChecked.value) {
        if (isDefault.value) {
          //默认导出
          const moduleId = projectStore.proModuleId;
          const newParmas = {
            zdId: zdId,
            defaultExport: true,
            moduleId: moduleId,
            wkId: parmas.wkId
          };
          const loading = ElLoading.service({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          downLoadPublicCheck(newParmas).then((res) => {
            loading.close();
            if (res.code == 200) {
              ElNotification({
                title: '错误提示',
                message: res.data,
                duration: 0
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        } else {
          const loading = ElLoading.service({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          downLoadPublicCheck(parmas).then((res) => {
            loading.close();
            if (res.code == 200) {
              ElNotification({
                title: '错误提示',
                message: res.data,
                duration: 0
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
      } else {
        if (isDefault.value) {
          //默认导出
          const moduleId = projectStore.proModuleId;
          const newParmas: any = {
            zdId: zdId,
            defaultExport: true,
            moduleId: moduleId,
            wkId: parmas.wkId,
            code: parmas.code
          };
          if (isNoDel.value) {
            newParmas.del = 0;
          }
          if (isBatch.value && newParmas.zdId.length > batchNum.value) {
            //是需要分批导出 并且数量大于分批量
            // 先把导出的弹窗关了
            // 然后计算出来一共多少批次 然后开始每一批次进度
            publicDownDialoig.value = false;
            showBatch.value = true;
            batchProgress.value = 0; //批量导出进度
            batchMsg.value = ''; //批量导出信息
            oneceProgress.value = 0; //单次导出进度
            progressDialog.value = true;
            batchDown(newParmas);
          } else {
            const loading = ElLoading.service({
              lock: true,
              text: '加载中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            downLoadPublic(newParmas).then((res) => {
              loading.close();
              if (res.code == 200) {
                progress.value = res.data.progress;
                showBatch.value = false; //是否显示批量
                progressDialog.value = true;
                downStatus.value = '';
                publicDownDialoig.value = false;
                isContinue.value = true;
                findAsyncMsg(res.data.id);
              } else {
                ElMessage.error(res.msg);
              }
            });
          }
        } else {
          if (isNoDel.value) {
            parmas.del = 0;
          }
          if (isBatch.value && parmas.zdId.length > batchNum.value) {
            //是需要分批导出 并且数量大于分批量
            // 先把导出的弹窗关了
            // 然后计算出来一共多少批次 然后开始每一批次进度
            publicDownDialoig.value = false;
            showBatch.value = true;
            batchProgress.value = 0; //批量导出进度
            batchMsg.value = ''; //批量导出信息
            oneceProgress.value = 0; //单次导出进度
            progressDialog.value = true;
            batchDown(parmas);
          } else {
            const loading = ElLoading.service({
              lock: true,
              text: '加载中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            downLoadPublic(parmas).then((res) => {
              loading.close();
              if (res.code == 200) {
                progress.value = res.data.progress;
                showBatch.value = false; //是否显示批量
                progressDialog.value = true;
                downStatus.value = '';
                publicDownDialoig.value = false;
                isContinue.value = true;
                findAsyncMsg(res.data.id);
              } else {
                ElMessage.error(res.msg);
              }
            });
          }
        }
      }
    } else {
      return false;
    }
  });
};

// 批量导出
const batchDown = (ite_params) => {
  const zdId = ite_params.zdId;
  const batchSize = batchNum.value;
  let currentIndex = 0;
  const processBatch = async () => {
    const currentBatch = zdId.slice(currentIndex, currentIndex + batchSize);
    if (currentBatch.length === 0) {
      // 所有批次都处理完毕
      showBatch.value = false;
      progressDialog.value = false;
      return;
    }

    // 更新当前批次的参数
    const batchParams = { ...ite_params, zdId: currentBatch };

    // 发送导出请求
    try {
      const res = await downLoadPublic(batchParams);
      if (res.code == 200) {
        oneceProgress.value = res.data.progress;
        downStatus.value = '';
        batchMsg.value = `正在导出第 ${Math.floor(currentIndex / batchSize) + 1} 批次（当前批次数量${currentBatch.length}）`;
        nowBatch.value = Math.floor(currentIndex / batchSize) + 1;
        isContinue.value = true;
        await waitForBatchCompletion(res.data.id, `_第${nowBatch.value}批次`);
        // 当前批次完成，更新批量导出进度
        batchProgress.value = Number((((currentIndex + batchSize) / zdId.length) * 100).toFixed(2));
        if (batchProgress.value > 100) {
          batchProgress.value = 100;
        }
        // 处理下一批次
        currentIndex += batchSize;
        await processBatch();
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      ElMessage.error('导出过程中出现错误');
      isContinue.value = false;
      progressDialog.value = false;
    }
  };

  // 开始处理第一批
  processBatch();
};

const waitForBatchCompletion = (id, fileName) => {
  return new Promise((resolve) => {
    const checkProgress = async () => {
      if (isContinue.value) {
        const parmas = { id };
        const res = await findAsyncMsgApi(parmas);
        if (res.code == 200) {
          if (!res.data) {
            ElMessageBox.alert('数据异常，请重试！！！', {
              confirmButtonText: '确定',
              callback: (action) => {
                progressDialog.value = false;
              }
            });
            return;
          }
          downMsg.value = res.data.remark;
          if (parseFloat(res.data.progress) != 1) {
            oneceProgress.value = Number((res.data.progress * 100).toFixed(2));
            if (res.data.status == -1) {
              ElMessage.error(res.data.result);
              downStatus.value = 'exception';
              progressDialog.value = false;
            } else {
              setTimeout(checkProgress, 1000);
            }
          } else if (parseFloat(res.data.progress) == 1) {
            downStatus.value = 'success';
            if (res.data.path) {
              downLoadFileName.value = res.data.path;
            }
            oneceProgress.value = Number((res.data.progress * 100).toFixed(2));
            downLoadId.value = id;
            fileSize.value = res.data.size;
            generalDown(`${downLoadFileName.value}${fileName}`);
            setTimeout(() => {
              resolve(res);
            }, 1000);
          }
        } else {
          ElMessage.error(res.msg);
        }
      }
    };
    checkProgress();
  });
};

/**
 * 把导出里面的文件结构迭代 整理出来代表gdb和shp的树id
 * @param list
 */
const getShpGdb = (list) => {
  list.forEach((v) => {
    if (v.fileType == 3) {
      // shp
      shpGdbTree.shp.push(v.id);
    } else if (v.fileType == 4) {
      // gdb
      shpGdbTree.gdb.push(v.id);
    } else if (v.fileType == 5) {
      // 图片
      shpGdbTree.pic.push(v.id);
    } else if (v.fileType == 1) {
      // word
      shpGdbTree.word.push(v.id);
    } else if (v.fileType == 2) {
      //excel
      shpGdbTree.excel.push(v.id);
    } else if (v.fileType == 7) {
      // 附件
      shpGdbTree.fileMap.push(v.id);
    }
    if (v.list.length != 0) {
      getShpGdb(v.list);
    }
  });
};

/**
 * 循环获取进度条，当进度条==1的时候停止调用
 * @param id
 * @param fileName
 */
const findAsyncMsg = (id, fileName?: string) => {
  if (isContinue.value) {
    const parmas = {
      id: id
    };
    findAsyncMsgApi(parmas).then((res) => {
      if (res.code == 200) {
        if (!res.data) {
          ElMessageBox.alert('数据异常，请重试！！！', {
            confirmButtonText: '确定',
            callback: (action) => {
              progressDialog.value = false;
            }
          });
          return;
        }
        downMsg.value = res.data.remark;
        if (parseFloat(res.data.progress) != 1) {
          progress.value = Number((res.data.progress * 100).toFixed(2));
          if (res.data.status == -1) {
            ElMessage.error(res.data.result);
            downStatus.value = 'exception';
            progressDialog.value = false;
          } else {
            setTimeout(() => {
              findAsyncMsg(id, fileName);
            }, 1000);
          }
        } else if (parseFloat(res.data.progress) == 1) {
          downStatus.value = 'success';
          if (res.data.path) {
            downLoadFileName.value = res.data.path;
          }
          progress.value = Number((res.data.progress * 100).toFixed(2));
          downLoadId.value = id;
          fileSize.value = res.data.size;
          // this.downloadTypeDialog = true
          generalDown(fileName);
          setTimeout(() => {
            progressDialog.value = false;
          }, 1000);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 调用迅雷下载  暂时不用，以后可能用
const xunleiDownLoad = () => {
  // 创建单个任务
  const url = `${import.meta.env.VITE_APP_BASE_API}/tool/async/findAsyncFileXL?id=${downLoadId.value}&fileName=${downLoadFileName.value}.zip`;
  // thunderLink.newTask({
  //   downloadDir: '', // 指定当前任务的下载目录名称，迅雷会在用户剩余空间最大的磁盘根目录中创建这个目录。【若不填此项，会下载到用户默认下载目录】
  //   tasks: [
  //     {
  //       name: '', // 指定下载文件名（含扩展名）。【若不填此项，将根据下载 URL 自动获取文件名】
  //       url: url // 指定下载地址【必填项】
  //     }
  //   ]
  // });
  downloadTypeDialog.value = false;
};

//通用下载
const generalDown = (spfileName?: string) => {
  const parmas = {
    id: downLoadId.value,
    fileName: downLoadFileName.value
  };
  findAsyncFileBrowser(parmas, showProgess).then((res) => {
    downloadTypeDialog.value = false;
    if (res.data.type == 'application/json') {
      loadingProgessInstance.close();
      //导出异常
      const read = new FileReader();
      read.readAsText(res.data, 'utf-8');
      let bugMsg = '';
      read.onload = (data) => {
        bugMsg = JSON.parse(data.currentTarget['result']).msg;
        ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
        if (!bugMsg) {
          bugMsg = '未知异常';
        }
      };
    } else {
      //导出正常
      const name = decodeURI(res.headers['content-disposition']);
      const index = name.indexOf('=');
      let fileName = downLoadFileName.value;
      if (spfileName) {
        //如果是批量导出
        fileName = spfileName;
      }
      const blob = new Blob([res.data], { type: 'application/zip' });
      // 针对ie浏览器
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        //非ie浏览器
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob); //常见下载的链接
        downloadElement.href = href;
        downloadElement.download = fileName; //下载后文件名
        document.body.appendChild(downloadElement);
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放blob对象
      }
      setTimeout(() => {
        loadingProgessInstance.close();
      }, 3000);
    }
  });
};

/**
 * 获取当前下载进度
 * @param progress
 */
const showProgess = (progress) => {
  if (!showBatch.value) {
    //如果不是批量导出才需要下载进度
    progressEnd.value = progress;
    currentPercent.value = progress.loaded / 1024 / 1024;
    const percent = currentPercent.value.toFixed(2);
    const planNum = ((progress.loaded / fileSize.value) * 100).toFixed(2);
    loadingProgessInstance = ElLoading.service({
      lock: true,
      text: `数据下载中${percent}MB，进度${planNum}%`,
      spinner: 'Loading',
      background: 'rgba(255, 255, 255, 0.9)'
    });
  }
};

/**
 * 父组件传过来选中的宗地详情
 * @param obj
 * @param type
 */
const getDetail = (obj, type) => {
  //type 1 表示需要找到obj.id 并选中该宗地
  graphicalList.value = [];
  graphicalList.value.push(obj);
  // 点击列表 请求到该条数据的树的时候，第一次是需要同时加载根树第一层子集 TODO
  defaultExpand.value = obj.id;
  if (iscq.value) {
    // 拆迁进度信息
    const cqMsg = {
      cqOkNum: 0, //已拆迁 统计为拆迁户的拆迁进度为已完成拆除
      cqNoNum: 0 //未拆迁 统计为拆迁户的拆迁进度不为已完成拆除
    };
    // 得到拆迁进度信息
    getCQLableMain(obj, cqMsg);
    emit('labelCQMsg', cqMsg, obj.id, obj.parcelName, true);
  }
  if (type == 1) {
    for (let index = 0; index < parcelListCopy.value.length; index++) {
      if (parcelListCopy.value[index].id == obj.id) {
        parcelListCopy.value[index].checked = true;
        break;
      }
    }
  }
};

/**
 * 选中节点
 * @param node
 * @param data
 * @param data1
 */
const checkedNode = (node: any, data: any, data1: any) => {
  if (node.isLoadMore) {
    //代表是加载更多操作
    loadChildren(node, data);
  } else {
    //像datamap.vue传递每个子要素的ruleId
    emit('sendRuleId', node.ruleId);
    if (isAZF.value && node.levelNum == 3) {
      //安置房并且等级是3 代表是单元 这时候中间地图要换成安置房展示方式
      emit('initChangeCenterToAZF', node.list);
    } else {
      emit('initAzfCenter');
    }
    defaultExpand.value = node.id;
    listLoading.value = true;
    selectParcelOne(node.id).then((res) => {
      listLoading.value = false;
      if (res.code == 200) {
        node.fieldInstanceModels = res.data.fieldInstanceModels;
        node = { ...node, ...res.data };
        emit('changeGraph', node);
      } else {
        ElMessage.error(res.msg);
      }
    });
    if (node.list.length == 0) {
      loadChildren(node, data);
    }
    // 拆迁户特殊判断点击幢的时候要统计已拆迁数据 标注
    if (iscq.value && (node.levelNum == 2 || node.levelNum == 1)) {
      //拆迁幢 需要得到已拆迁数据并标注
      // 拆迁进度信息
      const cqMsg = {
        cqOkNum: 0, //已拆迁 统计为拆迁户的拆迁进度为已完成拆除
        cqNoNum: 0 //未拆迁 统计为拆迁户的拆迁进度不为已完成拆除
      };
      let isMainNode = false; //是否是根节点
      if (node.levelNum == 2) {
        // 得到拆迁进度信息
        getCQLable(node, cqMsg);
        isMainNode = false;
      } else if (node.levelNum == 1) {
        // 得到拆迁进度信息
        getCQLableMain(node, cqMsg);
        isMainNode = true;
      }
      emit('labelCQMsg', cqMsg, node.id, node.parcelName, isMainNode);
    } else {
      // 这里需要触发地图取消拆迁进度信息显示
      emit('clearCQLabel');
    }
  }
};

/**
 * 分页获取节点的平级数据
 * @param node 当前节点的数据信息
 * @param data 当前节点
 */
const loadChildren = async (node: any, data: any) => {
  let pageNum = 0;
  if (node.pageNum) {
    pageNum = node.pageNum;
  }
  // const params = {
  //   id: node.id,
  //   pageNum: pageNum + 1,
  //   pageSize: 20
  // };
  const params = { ...props.queryParams };
  // const params = {};
  params.parentId = node.id;
  params.pageNum = pageNum + 1;
  params.pageSize = 20;
  // 因为是查询子节点 所以参数需要特殊处理一下
  if (params.conditionFields && params.conditionFields.length != 0) {
    params.conditionFields.forEach((v) => {
      v.top = false;
    });
  }
  // 加强处理
  params.ifCheck = false;
  selectParcelOneList(params).then((resp) => {
    if (resp.code === 200) {
      if (node.isLoadMore) {
        resp.data.list.forEach((v) => {
          const insertIndex = data.parent.data.list.length > 0 ? data.parent.data.list.length - 1 : 0;
          data.parent.data.list.splice(insertIndex, 0, v);
        });
        node.pageNum = pageNum + 1;
        if (node.pageNum === resp.data.pages) {
          //代表是最后一页
          data.parent.data.list.pop(); //删除加载更多
        }
      } else {
        //代表是加载更多
        resp.data.list.forEach((v) => {
          node.list.push(v);
        });
        node.total = resp.data.total;
        node.pageNum = pageNum + 1;
        node.pageSize = 20;
        //判断是不是需要增加加载更多、分页内容 当node的
        if (node.pageNum == 1 && node.total > 20) {
          node.list.push({
            parcelName: '加载更多',
            id: node.id,
            isLoadMore: true, //标识是加载更多
            pageNum: pageNum + 1,
            pageSize: 20
          });
        }
      }
    } else {
      ElMessage.error(resp.msg);
    }
  });
};

/**
 * 获取小区下面拆迁进度
 * @param data
 * @param cqMsg
 */
const getCQLableMain = (data, cqMsg) => {
  data.list.forEach((w) => {
    w.list.forEach((v) => {
      //循环栋下面的单元
      v.list.forEach((k) => {
        //循环单元下的层
        k.list.forEach((q) => {
          //循环层下面的户
          for (let i = 0; i < q.fieldInstanceModels.length; i++) {
            if (q.fieldInstanceModels[i].groupName == '征迁进度') {
              if (q.fieldInstanceModels[i].attribution.ZQJD) {
                if (q.fieldInstanceModels[i].attribution.ZQJD == '已完成拆除') {
                  cqMsg.cqOkNum++;
                } else {
                  cqMsg.cqNoNum++;
                }
              }
              break;
            }
          }
        });
      });
    });
  });
};

/**
 * 获取幢下面拆迁进度
 * @param data
 * @param cqMsg
 */
const getCQLable = (data, cqMsg) => {
  data.list.forEach((v) => {
    //循环栋下面的单元
    v.list.forEach((k) => {
      //循环单元下的层
      k.list.forEach((q) => {
        //循环层下面的户
        for (let i = 0; i < q.fieldInstanceModels.length; i++) {
          if (q.fieldInstanceModels[i].groupName == '征迁进度') {
            if (q.fieldInstanceModels[i].attribution.ZQJD) {
              if (q.fieldInstanceModels[i].attribution.ZQJD == '已完成拆除') {
                cqMsg.cqOkNum++;
              } else {
                cqMsg.cqNoNum++;
              }
            }
            break;
          }
        }
      });
    });
  });
};

/**
 * 打开筛选弹窗
 */
const shaixuan = () => {
  shaixuanDialog.value = true;
  getSearchTaskList();
  searchUser.pageNum = 1;
  listUser(searchUser).then((response) => {
    if (response.code) {
      userList.value = response.rows;
      let userPages = Number(response.total / 10);
      if (response.total % 10 > 0) {
        userPages = userPages + 1;
      }
      userPages = userPages;
    } else {
      ElMessage.error(response.msg);
    }
  });
};

/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 重置筛选条件
 */
const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 20,
    parcelName: '',
    moduleId: queryParamsCopy.value.moduleId,
    conditionFields: [],
    ruleIds: [],
    parcelCode: null,
    express: false //是否查询表达式异常的数据
  }; //弹窗筛选
  emit('getParmas', dialogSearch.value);
  shaixuanDialog.value = false;
};

/**
 * 提交筛选条件
 * @param isMapping
 */
const submitSearch = (isMapping?: number) => {
  //isMapping 1为导出未匹配到的数据
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  if (dialogSearch.value.parcelCode === '') {
    dialogSearch.value.parcelCode = null;
  }
  if (dialogSearch.value.parcelCode && !isArray(dialogSearch.value.parcelCode)) {
    dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
  }
  if (dialogSearch.value.taskId) {
    //选择了任务id 就要改变这个值
    dialogSearch.value.allocation = true;
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.allocation = '';
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  if (dialogSearch.value.updateDate && dialogSearch.value.updateDate.length != 0) {
    dialogSearch.value.updateTimeStart = dialogSearch.value.updateDate[0];
    dialogSearch.value.updateTimeEnd = dialogSearch.value.updateDate[1];
  } else if (!dialogSearch.value.updateDate) {
    dialogSearch.value.updateTimeStart = '';
    dialogSearch.value.updateTimeEnd = '';
  }
  // this.dialogSearch.parcelName = this.paraceName
  dialogSearch.value.moduleId = queryParamsCopy.value.moduleId;
  dialogSearch.value.pageNum = queryParamsCopy.value.pageNum;
  dialogSearch.value.pageSize = queryParamsCopy.value.pageSize;
  emit('getParmas', dialogSearch.value);
  shaixuanDialog.value = false;
};

/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: queryParamsCopy.value.moduleId
  };
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 清除选中的用户 1采集人 2最后更新人
const clearUser = (type) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

/**
 * 通过任务id得到任务名称
 * @param id
 * @returns
 */
const getTaskName = (id) => {
  let taskName = '';
  for (let index = 0; index < taskList.value.length; index++) {
    if (taskList.value[index].id == id) {
      taskName = taskList.value[index].name;
      break;
    }
  }
  return taskName;
};

/**
 * 导入或者更新shp 打开弹窗
 */
const handleUpdateShp = async () => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  shpUploadDialog.value = true;
};

/**
 * excle导入更新
 */
const handleUpdateExcel = async () => {
  const code = await getAllowUpdate();
  if (code < 0) {
    ElMessageBox.alert('请对表达式进行排序后再修改数据！！！', '提示', {
      confirmButtonText: '确定',
      callback: (action: any) => {}
    });
    return;
  }
  uploadExcelDialog.value = true;
};

/**
 * 导入或者更新shp  关闭弹框
 */
const handleCloseShpDialog = () => {
  shpUploadDialog.value = false;
};
/**
 * 修改筛选条件
 * @param type
 * @param idx
 */
const editCondition = (type, idx) => {
  //type 1新增 2删除 idx 删除的下标
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

/**
 * 检查树完整性
 * @param id
 */
const checkComplete = (id) => {
  checkZdId.value = id;
  completenessDialog.value = true;
};

/**
 * 关闭树完整性弹窗
 */
const closeCompleteDialog = () => {
  completenessDialog.value = false;
};
/**
 * 提交选择的任务
 */
const submitTask = () => {
  if (!chooseTaskId.value) {
    ElMessage.error('您未选择任务！！！');
    return;
  }
  taskDialog.value = false;
  // addParcelDialog.value = true;
};

/**
 * 关闭进度条
 */
const handleCloseProgress = () => {
  progressDialog.value = false;
  isContinue.value = false;
};

/**
 * 关闭勘界弹窗
 */
const closeKJDialog = () => {
  kjDialog.value = false;
};

/**
 * 下载勘界数据
 * @param parmas
 * @param flg
 */
const sumbitDownloadKJ = (parmas, flg) => {
  const moduleId = projectStore.proModuleId;
  parmas.moduleId = moduleId;
  if (flg) {
    parmas.del = 0;
  }
  exportPcNew(parmas).then((res) => {
    if (res.code == 200) {
      progress.value = res.data.progress;
      progressDialog.value = true;
      downStatus.value = '';
      publicDownDialoig.value = false;
      isContinue.value = true;
      downLoadFileName.value = `${parmas.xmmc}共${parmas.num}户`;
      findAsyncMsg(res.data.id);
      kjDialog.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 关闭标注弹窗
 */
const handleCloseLabel = () => {
  labelDialog.value = false;
};

/**
 * 获取
 * @param fieldName
 * @param linkId
 */
const submitField = (fieldName, linkId) => {
  labelDialog.value = false;
  emit('drawLabel', fieldName, linkId);
};

/**
 * 修改公司名称
 * @param val
 */
const changeCompany = (val) => {
  downLoadMsg.value.companyName = val;
};

/**
 * 关闭excel更新
 */
const closeExcelUpload = () => {
  uploadExcelDialog.value = false;
};

/**
 * 关闭固定excel 更新弹窗
 */
const closeSpeUpload = () => {
  updateSpeDialog.value = false;
};

/**
 * 关闭shp更新子要素弹窗
 */
const closeShpChild = () => {
  updateShpChildDialog.value = false;
};

/**
 * 关闭批量更新图片
 */
const closeUpdateTP = () => {
  updateTPFileDialog.value = false;
};
/** 关闭批量更新宗地和房产图 */
const closeUpdateZDFC = () => {
  updateZDFCDialog.value = false;
};
/**
 * 关闭刷新表达式
 */
const closeRefreshExpress = () => {
  refreshExpressDialog.value = false;
};

/**
 * 关闭切换类型
 */
const handleCloseCatNode = () => {
  catNodeDialog.value = false;
};

/**
 * 切换类型
 */
const changeType = () => {
  const isEmpty = catMsg.ruleIds.length == 0 ? true : false;
  if (catMsg.nodeType == 1) {
    //节点要素 需要禁用图层节点
    props.firstNodes.forEach((v) => {
      if (v.container !== 0) {
        v.disable = true;
      } else if (v.container == 0 && isEmpty) {
        //选择的ruleId如果是空的需要把所有要素节点勾选上
        catMsg.ruleIds.push(v.ruleId);
      }
    });
    // 还需要去掉已经选择的图层节点数据
    const delList = [];
    catMsg.ruleIds.forEach((v, vdx) => {
      for (let i = 0; i < props.firstNodes.length; i++) {
        if (v == props.firstNodes[i].ruleId && props.firstNodes[i].container !== 0) {
          delList.push(v);
          break;
        }
      }
    });
    catMsg.ruleIds = catMsg.ruleIds.filter((item) => !delList.includes(item));
  } else {
    //图层节点要素
    props.firstNodes.forEach((v) => {
      v.disable = false;
    });
  }
  catNodeDialog.value = true;
};

// 提交切换
const submitCat = () => {
  catMsgRef.value.validate((valid) => {
    if (valid) {
      dialogSearch.value.ruleIds = catMsg.ruleIds;
      submitSearch();
      catNodeDialog.value = false;
    } else {
      return false;
    }
  });
};

/**
 * 关闭流程任务数据解锁
 */
const closeFlowTask = () => {
  flowTaskDailog.value = false;
};

/**
 * 关闭下载方式
 */
const handleCloseDownType = () => {
  downloadTypeDialog.value = false;
};

/**
 * 提交下载方式
 */
const submitDaownType = () => {
  if (!downloadType.value) {
    ElMessage.error('请选择下载方式！！！');
    return;
  }
  if (downloadType.value == 1) {
    //迅雷下载
    xunleiDownLoad();
  } else if (downloadType.value == 2) {
    //普通下载
    generalDown();
  }
};

/**
 * 切换下载方式
 */
const changeDownloadType = (val) => {
  downloadType.value = val;
};

/**
 * 树结构重写
 * @param h
 * @param {Object} node
 * @param {Object} data
 * @param {Object} store
 * @returns
 */
const renderContent = (h: any, { node, data, store }: { node: any; data: TreeNode; store: any }) => {
  // 自定义的图片
  let authImg = null;
  let svg = null;
  let color = '';
  if (!data.geometryId && !data.isLoadMore) {
    color = '#ff3333 !important';
  }
  if (data.id == defaultExpand.value) {
    color = 'var(--current-color) !important';
  }
  if (data.iconUrl && data.iconUrl.includes('_blob')) {
    authImg = h(AuthImg, {
      authSrc: `${baseUrl}${data.iconUrl}?att=1`,
      width: '20px',
      height: '20px'
    });
  } else {
    const classes = ['svg-ico'];
    if (!data.geomArcgis) {
      classes.push('no-span');
    }
    if (data.id == defaultExpand.value) {
      classes.push('checked-span');
    }
    svg = h(SvgIcon, {
      iconClass: data.iconUrl,
      style: { color: color }
    });
  }
  if (isAZF.value) {
    //安置房特殊处理
    // 根据层级决定是否渲染节点内容
    if (data.levelNum >= 4) {
      node.visible = false;
      // 对于四级及以下的节点，返回一个空元素（或者您可以返回一个隐藏的占位符）
      return h('span', { style: { display: 'none' } });
    } else {
      // 对于四级以上的节点，正常渲染节点内容
      return h('div', { class: 'custom-tree-node' }, [
        authImg,
        svg,
        h('span', { style: { fontSize: '12px', marginLeft: '10px', color: color } }, data.parcelName),
        h(
          ElIcon,
          {
            class: 'icon-delete',
            onClick: (e: Event) => {
              e.stopPropagation();
              handleTreeNodeDelete(data);
            }
          },
          () => h(Delete)
        )
      ]);
    }
  } else {
    return h('div', { class: 'custom-tree-node' }, [
      authImg,
      svg,
      h('span', { style: { fontSize: '12px', marginLeft: '0px', color: color } }, data.parcelName),
      !data?.isLoadMore &&
        h(
          ElDropdown,
          {
            trigger: 'hover',
            style: {
              position: 'absolute',
              right: '0px',
              fontSize: '12px'
            }
          },
          {
            default: () =>
              h(
                ElIcon,
                {
                  class: 'icon-right',
                  onClick: (e: Event) => {
                    e.stopPropagation();
                  }
                },
                () => h(MoreFilled)
              ),
            dropdown: () =>
              h(
                ElDropdownMenu,
                {},
                {
                  default: () => [
                    h(
                      ElDropdownItem,
                      {
                        onClick: (e: Event) => {
                          e.stopPropagation();
                          handleTreeNodeDelete(data);
                        }
                      },
                      {
                        default: () => [h(ElIcon, { class: 'icon-delete' }, () => h(Delete)), h('span', { style: { marginLeft: '5px' } }, '删除')]
                      }
                    )
                  ]
                }
              )
          }
        )
    ]);
  }
};

/**
 * 打开设置显示具体树的层级
 */
const settingTree = () => {
  treeSettingDialog.value = true;
};

/**
 * 关闭树的层级显示
 */
const handleCloseTreeSetting = () => {
  treeSettingDialog.value = false;
};

/**
 * 提交显示具体数的设置
 */
const handleSubmitTreeSetting = (list, ruleIds) => {
  showGraphs.value = list;
  emit('initShowGraphs', list, ruleIds);
  treeSettingDialog.value = false;
};

/**
 * 关闭批量更新字段
 */
const handleCloseBitchUpdateFieldDialog = () => {
  bitchUpdateFieldDialog.value = false;
};

/**
 * 得到是否允许更新 通过获取是否有表达式，如果有表达式，但是没有排序 不允许更新数据
 * @returns code -1 表达式未排序 0 没有表达式 1 有表达式且已排序
 */
const getAllowUpdate = async () => {
  return new Promise((resolve, reject) => {
    const params = {
      moduleId: queryParamsCopy.value.moduleId
    };
    selectIfOrder(params).then((res) => {
      if (res.code == 200) {
        resolve(res.data);
      } else {
        ElMessage.error(res.msg);
        resolve(-1);
      }
    });
  });
};
const getTotalData = async () => {
  const params = {
    areaCode: '',
    ifCheck: false,
    ifPage: true,
    levelNum: 1,
    moduleId: queryParamsCopy.value.moduleId,
    pageNum: queryParamsCopy.value.pageNum - 1,
    pageSize: 20,
    parcelName: '',
    ruleIds: []
  };
  const moduleInfo: ModuleInfo = {
    moudleId: String(params.moduleId)
  };
  const res = await getPlaceList(params, moduleInfo);
  if (res.code == 200) {
    totalCount.value = res.data.total;
  }
};

const heightNodeForId = (id: number) => {
  defaultExpand.value = id;
};

// 对外暴露方法
defineExpose({
  getDetail,
  setIsShowSpe,
  heightNodeForId
});
/**
 * 生命周期 挂载完成
 */
onMounted(() => {
  const list_3 = [];
  for (let i = 25; i < 46; i++) {
    list_3.push({ value: i, label: `${i}` });
  }
  const list_6 = [];
  for (let i = 13; i < 24; i++) {
    list_6.push({ value: i, label: `${i}` });
  }
  wkidList[0].children[0].children = list_3;
  wkidList[0].children[1].children = list_6;
  getExportSetting();
  getCompany();

  isAZF.value = route.query.isAZF === 'true';
  iscq.value = route.query.iscq === 'true';

  // 自动获取总数据
  getTotalData();
});
// 添加删除处理函数
const handleDelete = (item: TreeNode) => {
  ElMessageBox.confirm('确定要删除该数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(() => {
      ElMessageBox.confirm('该操作会彻底删除数据，数据将无法找回，请谨慎操作！！！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const list: TreeNode[] = [];
        // 如果是父节点,需要递归收集所有子节点
        const collectNodes = (node: TreeNode) => {
          list.push({
            id: node.id,
            delFlag: 1,
            taskId: 0,
            ruleId: node.ruleId
          } as TreeNode);
          if (node.list && node.list.length > 0) {
            node.list.forEach((child) => {
              collectNodes(child);
            });
          }
        };
        collectNodes(item);

        operaParcel(list).then((res) => {
          if (res.code == 200) {
            ElMessage({
              type: 'success',
              message: '删除成功!'
            });
            emit('getLinyeData', true);
            getTotalData(); // 删除成功后更新总数
          } else {
            ElMessage.error(res.msg);
          }
        });
      });
    })
    .catch(() => {});
};

const handleTreeNodeDelete = (node: TreeNode) => {
  ElMessageBox.confirm('确定要删除该数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(() => {
      ElMessageBox.confirm('该操作会彻底删除数据，数据将无法找回，请谨慎操作！！！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const id = JSON.stringify(node.id);
        deleteParcel(id).then((res) => {
          if (res.code == 200) {
            ElMessage({
              type: 'success',
              message: '删除成功!'
            });
            emit('getLinyeData', true);
            getTotalData(); // 删除成功后更新总数
          } else {
            ElMessage.error(res.msg);
          }
        });
      });
    })
    .catch(() => {});
};
</script>
<style lang="scss" scoped>
.tip-class {
  margin-left: 10px;
  position: relative;
  top: 2px;
}
.no-span {
  color: #ff3333 !important;
}
.checked-span {
  color: var(--current-color) !important;
}
:deep(.el-pager) {
  padding: 5px;
}
:deep(.custom-size .el-checkbox__input .el-checkbox__inner) {
  zoom: 150%;
}
.dialog-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  padding: 20px 0px 0px 0px;
  .dialog-item {
    padding: 12px 16px;
    border: #dbe7ee solid 1px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: center;
    .left {
      width: 72px;
      height: 72px;
    }
    .right {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .top {
        font-size: 14px;
        font-weight: bold;
      }
      .bottom {
        font-size: 12px;
        color: rgb(0, 0, 0, 0.6);
        margin-top: 6px;
      }
    }
    .end {
      margin-left: 10px;
      .box {
        width: 20px;
        height: 20px;
        border: #d3d3d3 solid 1px;
        border-radius: 2px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .box:hover {
        border: #1890ff solid 1px;
      }
      .box-active {
        border: #1890ff solid 1px;
      }
    }
  }
  .dialog-item:hover {
    border: #1890ff solid 1px;
    color: #1890ff !important;
  }
  .dialog-item-active {
    border: #1890ff solid 1px;
    color: #1890ff !important;
  }
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.down-content {
  display: flex;
  flex-direction: column;
  .flex-row {
    margin-bottom: 10px;
  }
}
:deep(.el-empty__description) {
  margin-top: -20px;
}

.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .dialog-label {
    color: #161d26;
    margin-right: 10px;
  }
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
:deep(.el-form--label-top .el-form-item__label) {
  padding: 0px;
}
.dialog-row-allChecked {
  position: absolute;
  right: 0px;
  top: -40px;
}
.zong-di-list-continer {
  border: #ededed solid 1px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  .continer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 1;
    backdrop-filter: blur(5px);
    &:after {
      content: '';
      height: auto;
      position: relative;
      left: 0;
      top: 0;
      background: inherit;
      z-index: 2;
      backdrop-filter: blur(10px);
    }
    .resize-x {
      height: 100%;
      cursor: w-resize;
      width: 10px;
      position: absolute;
      right: 0px;
      z-index: 99;
    }
    .list-title {
      height: 40px;
      line-height: 40px;
      background: #f7f8f6;
      border-radius: 8px 8px 0px 0px;
      display: flex;
      justify-content: center;
      align-content: center;
      position: relative;
      border-bottom: 1px solid #ededed;
      z-index: 3;
      .shaixuan {
        position: absolute;
        left: 12px;
      }
      .title {
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        line-height: 20px;
        padding-top: 10px;
        padding-bottom: 10px;
      }
      .icon {
        width: 14px;
        height: 14px;
        position: absolute;
        right: 12px;
        top: 12px;
        cursor: pointer;
      }
      .add-link {
        position: absolute;
        right: 5px;
      }
    }
    .list-search {
      border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
      padding: 12px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-content: center;
      position: relative;
      z-index: 3;
      .el-input {
        :deep(.el-input__inner) {
          color: #6e6e6e;
          width: 100%;
          height: 32px;
          border-radius: 4px;
          border-color: #e6ebf5;
          z-index: 8;
          &::placeholder {
            color: #6e6e6e;
          }
          /* 谷歌 */
          &::-webkit-input-placeholder {
            color: #6e6e6e;
          }
          /* 火狐 */
          &::-moz-placeholder {
            color: #6e6e6e;
          }
          /*ie*/
          &::-ms-input-placeholder {
            color: #6e6e6e;
          }
        }
        :deep(.el-input__prefix) {
          height: 32px;
          line-height: 32px;
          left: 12px;
          color: #333;
        }
      }
      .search-button {
        width: 40px;
        height: 24px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        margin-left: 8px;
        padding-left: 8px;
        margin-top: 4px;
      }
    }
    /*滚动条样式*/
    .list-content::-webkit-scrollbar {
      width: 4px;
    }
    .list-content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    .list-content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
    .list-content {
      width: 100%;
      //  顶部 40 搜索 57 分页 40 上一个下有一个40 管理和导出 (40 4*40 +60 =220)
      height: 700px;
      overflow-y: auto;
      overflow-x: hidden;
      z-index: 3;
      padding: 0 4px;
      .list-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .content-item {
        width: 100%;
        height: 42px;
        border-bottom: 0.5px solid #ededed;
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        color: #333;
        padding: 16px;
        position: relative;
        z-index: 4;
        cursor: pointer;
        display: flex;
        .item-name {
          width: calc(100% - 50px);
          height: 30px;
          font-size: 14px;
          font-family:
            PingFang SC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #000;
          overflow: hidden;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          white-space: nowrap;
          display: block;
        }
        .icon-right {
          position: absolute;
          right: -56px;
          font-size: 12px;
        }
        .icon-right-margin {
          position: absolute;
          right: 36px;
          top: 15px;
          font-size: 14px;
        }

        .icon-tuopuCheck {
          position: absolute;
          right: 56px;
          top: 15px;
          font-size: 14px;
          color: #ff3333;
        }
        .icon-delete {
          position: absolute;
          right: 76px;
          top: 15px;
          font-size: 14px;
          color: #f56c6c;
          cursor: pointer;
          &:hover {
            color: #ff4d4f;
          }
        }
      }
      .child-tree {
        .el-tree {
          background: transparent;
          padding: 0px 10px 10px 10px;
          width: 100%;
          .el-icon svg {
            //原有的箭头 去掉
            display: none !important;
            height: 0;
            width: 0;
          }
          :deep(.el-tree-node__content) {
            height: 44px;
            background: rgba(0, 0, 0, 0);
            border-radius: 0px 0px 0px 0px;
            position: relative;
            .custom-tree-node {
              margin-left: 12px;
              display: flex;
              align-items: center;
              width: 100%;
              height: 44px;
              border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
            }
            .icon-delete {
              position: absolute;
              right: 76px;
              top: 15px;
              font-size: 14px;
              color: #f56c6c;
              cursor: pointer;
              z-index: 10;
              &:hover {
                color: #ff4d4f;
              }
            }
          }
        }
      }
      .child-node {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        .child-item {
          padding: 10px 20px 10px 20px;
          border-bottom: 0.5px solid #ededed;
          cursor: pointer;
        }
      }
    }
    .list-content-active {
      color: var(--current-color) !important;
    }
    .active-span {
      color: var(--current-color) !important;
    }
    .list-pagination {
      width: 100%;
      border-top: 1px solid var(--el-border-color-lighter);
      .pagination-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;
        margin-top: 0 !important;
        padding: 0 !important;
        .pagination-left {
          display: flex;
          align-items: center;
          width: 100%;
          .pagination-left-item {
            display: flex;
            align-items: center;
            margin-top: 10px;
          }
        }

        .pagination-right {
          display: flex;
          :deep(.custom-pagination) {
            .el-pagination {
              display: flex;
              // 创建包装容器实现上下布局
              .pagination-wrapper {
                display: flex;
              }
            }
          }
        }
      }
    }
    .flex-row {
      display: flex;
    }
    .page-total {
      color: #dbe7ee;
      margin-left: 17px;
      font-size: 12px;
    }
    .page-div {
      display: flex;
      flex-direction: row;
      align-items: center;
      .page-total {
        color: #dbe7ee;
        margin-left: 17px;
        font-size: 12px;
      }
      .page-right {
        flex: 1;
        display: flex;
        justify-content: flex-start;
      }
    }
    .list-btn {
      z-index: 3;
      display: flex;
      justify-content: center;
      margin-top: 10px;
      margin-bottom: 24px;
      .el-button {
        z-index: 4;
      }
    }
    .zongdi-icon {
      display: flex;
      justify-content: center;
      align-self: center;
      width: 40px;
      height: 40px;
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      cursor: pointer;
      .icon {
        width: 20px;
        height: 20px;
        margin: auto;
      }
    }
  }
  .search-div {
    position: absolute;
    top: 0px;
    left: 340px;
    border-radius: 8px 8px 8px 8px;
    color: #fff;
    font-size: 12px;
    padding: 10px;
    cursor: pointer;
    margin-left: 10px;
    width: 240px;
    .search-title {
      font-size: 14px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
    .search-item {
      font-size: 12px;
      margin-bottom: 5px;
      display: flex;
      flex-wrap: wrap;
    }
  }
  .min-search {
    left: 40px;
  }
  .min-width {
    width: 100px;
  }
}

.close-ico {
  position: absolute;
  right: 10px;
  top: 0px;
  z-index: 1;
  cursor: pointer;
  font-size: 16px;
}
.icon-success {
  color: #00ff22 !important;
}
.icon-error {
  color: #f56c6c !important;
}
</style>
