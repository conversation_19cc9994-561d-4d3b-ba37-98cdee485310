<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="任务状态">
              <el-col :span="6">
                <el-select v-model="queryParams.taskStatus" clearable placeholder="请输入任务状态" class="form-select">
                  <el-option
                    v-for="item in taskStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @keyup="handleQuery"
                  ></el-option>
                </el-select>
              </el-col>
            </el-form-item>
            <el-form-item label="任务类型">
              <el-col :span="6">
                <el-select v-model="queryParams.taskOptType" clearable placeholder="请选择任务类型" class="form-select">
                  <el-option
                    v-for="item in taskOptTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @keyup="handleQuery"
                  ></el-option>
                </el-select>
              </el-col>
            </el-form-item>
            <el-form-item label="任务名称">
              <el-col :span="6">
                <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable style="width: 240px" @keyup="handleQuery" />
              </el-col>
            </el-form-item>
            <el-form-item label="任务位置">
              <el-col :span="6">
                <el-input v-model="queryParams.taskSite" placeholder="请输入任务位置" clearable style="width: 240px" @keyup="handleQuery" />
              </el-col>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="任务模块">
              <el-col :span="6">
                <el-select v-model="queryParams.taskModule" clearable placeholder="请选择任务模块" class="form-select">
                  <el-option
                    v-for="item in taskModuleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @keyup="handleQuery"
                  ></el-option>
                </el-select>
              </el-col>
            </el-form-item>
            <el-form-item label="任务人员">
              <el-col :span="6">
                <el-select v-model="queryParams.taskReceivers" clearable placeholder="请选择任务人员" class="form-select">
                  <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.nickName" :value="item.userId"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-col :span="6">
                <el-date-picker
                  v-model="queryParams.dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-col>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              
              @click="handleAddTask"
              v-hasPermi="['system:user:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
          <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              
            >删除</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getSearchTaskList" :columns="columns"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="taskList">
          <el-table-column type="index" width="50" align="center" label="序号" />
          <el-table-column label="任务类型" align="center" key="taskOptType" prop="taskOptType" v-if="columns[0].visible">
            <template #default="scope">
              <span v-if="scope.row.taskOptType == 1">新增</span>
              <span v-else-if="scope.row.taskOptType == 2">修改</span>
              <span v-else>补充</span>
            </template>
          </el-table-column>

          <el-table-column label="任务名称" align="center" key="taskName" prop="taskName" v-if="columns[1].visible" />
          <el-table-column label="任务位置" align="center" v-if="columns[2].visible"> </el-table-column>
          <el-table-column label="任务描述" align="center" key="taskDescs" prop="taskDescs" v-if="columns[3].visible">
            <template #default="scope">
              <span :title="scope.row.taskDescs">{{ formatSpanRule(scope.row.taskDescs, 8) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="任务模块" align="center" key="taskModule" prop="taskModule" v-if="columns[4].visible">
            <template #default="scope">
              <span :title="scope.row.taskModule">{{ getTaskModuleName(scope.row.taskModule) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="任务人员" align="center" key="taskReceivers" prop="taskReceivers" v-if="columns[5].visible" />
          <el-table-column label="任务备注" align="center" key="taskRemark" prop="taskRemark" v-if="columns[6].visible">
            <template #default="scope">
              <span :title="scope.row.taskRemark">{{ formatSpanRule(scope.row.taskRemark, 8) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="taskCreateTime" width="160" v-if="columns[7].visible"> </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template #default="{ row }">
              <el-dropdown
                v-if="row.userId !== 1"
                @command="(command) => handleCommand(command, row)"
                v-hasPermi="['system:user:resetPwd', 'system:user:edit']"
              >
                <i class="el-icon-more" style="transform: rotate(90deg)"></i>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="handleUpdate" icon="el-icon-edit" v-hasPermi="['system:user:edit']">修改</el-dropdown-item>
                    <el-dropdown-item command="handleDelete" icon="el-icon-delete" v-hasPermi="['system:user:remove']">删除</el-dropdown-item>
                    <el-dropdown-item command="handleClose" icon="el-icon-key">关闭</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getSearchTaskList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" v-model:visible="addTaskDialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="taskForm" :model="taskForm" :rules="taskFormRules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务模块" prop="appType">
              <el-select v-model="taskForm.appType" placeholder="请选择业务模块" style="width: 100%">
                <el-option v-for="item in appTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="11" :offset="1">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务模块" prop="taskModule">
              <el-select v-model="taskForm.taskModule" placeholder="请选择任务模块" style="width: 100%">
                <el-option v-for="item in taskModuleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="11" :offset="1">
            <el-form-item label="任务类型" prop="taskOptType">
              <el-select v-model="taskForm.taskOptType" placeholder="请选择任务类型" style="width: 100%">
                <el-option v-for="item in taskOptTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务人员" prop="taskReceivers">
              <el-select v-model="taskForm.taskReceivers" multiple placeholder="请选择任务人员" style="width: 100%">
                <el-option v-for="item in taskReceiversOptions" :key="item.userId" :label="item.nickName" :value="item.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务描述" prop="taskDescs">
              <el-input v-model="taskForm.taskDescs" type="textarea" placeholder="请输入任务描述" maxlength="500"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务备注">
              <el-input v-model="taskForm.taskRemark" type="textarea" placeholder="请输入任务备注" maxlength="500"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitAddTask">确 定</el-button>
          <el-button @click="handleCancleAddTask">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { listUser } from '@/api/system/user';
import { getSearchTask, addTask, updateTask, deleteTask } from '@/api/task';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { TableColumnCtx } from 'element-plus';

interface TaskForm {
  appType?: string | number;
  taskName?: string;
  taskModule?: string | number;
  taskOptType?: string | number;
  taskReceivers?: string[];
  taskDescs?: string;
  taskRemark?: string;
  taskId?: number;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  taskName?: string;
  taskOptType?: string | number;
  taskModule?: string | number;
  taskStatus?: string | number;
  taskSite?: string;
  dateRange?: string[];
  taskReceivers?: string | number;
}

interface Column {
  key: string;
  label: string;
  visible: boolean;
}

interface TaskRow {
  userId: number;
  taskId: number;
  taskName: string;
  taskReceivers: string;
  [key: string]: any;
}

interface TableScope {
  row: TaskRow;
  column: TableColumnCtx<TaskRow>;
  $index: number;
}

// 状态定义
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const taskList = ref<any[]>([]);
const total = ref(0);
const addTaskDialogVisible = ref(false);
const taskReceiversOptions = ref<any[]>([]);
const taskForm = ref<TaskForm>({});
const queryForm = ref(null);

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  taskName: '',
  taskOptType: '',
  taskModule: '',
  taskStatus: '',
  taskSite: '',
  dateRange: [],
  taskReceivers: ''
});

// 列信息
const columns = ref<Column[]>([
  { key: '0', label: '任务类型', visible: true },
  { key: '1', label: '任务名称', visible: true },
  { key: '2', label: '任务位置', visible: true },
  { key: '3', label: '任务描述', visible: true },
  { key: '4', label: '任务模块', visible: true },
  { key: '5', label: '任务人员', visible: true },
  { key: '6', label: '任务备注', visible: true },
  { key: '7', label: '创建时间', visible: true }
]);

// 选项数据
const taskStatusOptions = [
  { label: '正常', value: 9 },
  { label: '关闭', value: 4 }
];

const taskModuleOptions = [
  { label: '宗地', value: 1 },
  { label: '房产', value: 2 },
  { label: '楼层', value: 3 },
  { label: '四至', value: 4 },
  { label: '附属设施', value: 5 },
  { label: '指界人', value: 6 },
  { label: '权利人', value: 7 },
  { label: '照片', value: 8 },
  { label: '红线', value: 9 }
];

const appTypeOptions = [
  { label: '房地一体', value: 1 },
  { label: '林业调查', value: 2 }
];

const taskOptTypeOptions = [
  { label: '新增', value: 1 },
  { label: '修改', value: 2 },
  { label: '补充', value: 3 }
];

// 表单校验规则
const taskFormRules = {
  appType: [{ required: true, message: '请选择业务模块', trigger: 'change' }],
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  taskModule: [{ required: true, message: '请选择任务模块', trigger: 'change' }],
  taskOptType: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  taskReceivers: [{ required: true, message: '请选择任务人员', trigger: 'change' }],
  taskDescs: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
};

/**
 * 格式化文本
 * @param text 文本
 * @param length 长度
 * @returns 格式化后的文本
 */
const formatSpanRule = (text: string, length: number): string => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

/**
 * 获取任务模块名称
 * @param val 值
 * @returns 任务模块名称
 */
const getTaskModuleName = (val: number): string => {
  const module = taskModuleOptions.find((item) => item.value === val);
  return module ? module.label : '宗地';
};

/**
 * 获取用户列表
 * @param queryParams 查询参数
 */
const getUserList = async () => {
  try {
    const response = await listUser(queryParams);
    if (response.code === 200) {
      taskReceiversOptions.value = response.rows;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

/**
 * 获取任务列表
 * @param queryParams 查询参数
 */
const getSearchTaskList = async () => {
  const params = {
    type: 1,
    pageSize: queryParams.pageSize,
    pageNum: queryParams.pageNum,
    taskOptType: queryParams.taskOptType,
    taskName: queryParams.taskName,
    taskModule: queryParams.taskModule,
    taskStatus: queryParams.taskStatus,
    taskCreateTimeStart: queryParams.dateRange?.[1],
    taskCreateTimeEnd: queryParams.dateRange?.[0],
    taskSite: queryParams.taskSite,
    taskReceivers: queryParams.taskReceivers
  };

  loading.value = true;
  try {
    const res = await getSearchTask(params);
    if (res.code === 200) {
      taskList.value = res.data.list;
      total.value = res.data.total;
      queryParams.pageSize = res.data.pageSize;
      queryParams.pageNum = res.data.pageNum;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};
/**
 * 处理查询
 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getSearchTaskList();
};
/**
 * 重置查询
 * @param queryParams 查询参数
 */
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    taskName: '',
    taskOptType: '',
    taskModule: '',
    taskStatus: '',
    taskSite: '',
    dateRange: [],
    taskReceivers: ''
  });
  handleQuery();
};

const handleAddTask = async () => {
  await getUserList();
  addTaskDialogVisible.value = true;
};
/**
 * 提交添加任务
 */
const handleSubmitAddTask = async () => {
  const params: {
    appType: string | number;
    taskName: string;
    taskModule: string | number;
    taskOptType: string | number;
    taskRemark: string;
    taskReceivers: string;
    taskDescs: string;
    taskId?: number;
  } = {
    appType: taskForm.value.appType,
    taskName: taskForm.value.taskName,
    taskModule: taskForm.value.taskModule,
    taskOptType: taskForm.value.taskOptType,
    taskRemark: taskForm.value.taskRemark,
    taskReceivers: taskForm.value.taskReceivers?.join(','),
    taskDescs: taskForm.value.taskDescs
  };

  if (taskForm.value.taskId) {
    params.taskId = taskForm.value.taskId;
    try {
      const res = await updateTask(params);
      if (res.code === 200) {
        ElMessage.success(res.data);
        getSearchTaskList();
        addTaskDialogVisible.value = false;
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('更新任务失败:', error);
    }
  } else {
    try {
      const res = await addTask(params);
      if (res.code === 200) {
        ElMessage.success(res.data);
        getSearchTaskList();
        addTaskDialogVisible.value = false;
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('添加任务失败:', error);
    }
  }
};
/**
 * 取消添加任务
 */
const handleCancleAddTask = () => {
  addTaskDialogVisible.value = false;
};
/**
 * 处理更新
 * @param row 行
 */
const handleUpdate = async (row: any) => {
  await getUserList();
  taskForm.value = { ...row };
  taskForm.value.taskReceivers = row.taskReceivers.split(',').map(Number);
  addTaskDialogVisible.value = true;
};
/**
 * 处理删除
 * @param type 类型
 * @param row 行
 */
const handleDelete = async (type: number, row: any) => {
  const str = type === 4 ? `是否确认删除【${row.taskName}】?` : `是否确认关闭【${row.taskName}】?`;

  try {
    await ElMessageBox.confirm(str, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const params = [row.taskId];
    const res = await deleteTask(type, params);
    if (res.code === 200) {
      ElMessage.success(res.data);
      getSearchTaskList();
      addTaskDialogVisible.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    ElMessage.info('已取消操作');
  }
};
/**
 * 处理命令
 * @param command 命令
 * @param row 行
 */
const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'handleClose':
      handleDelete(2, row);
      break;
    case 'handleDelete':
      handleDelete(4, row);
      break;
    case 'handleUpdate':
      handleUpdate(row);
      break;
  }
};

// 生命周期钩子
onMounted(() => {
  getSearchTaskList();
});
</script>

<style lang="scss" scoped>
.form-select {
  width: 240px;
  :deep(.el-input) {
    .el-input__inner {
      width: 100%;
    }
  }
}
</style>
