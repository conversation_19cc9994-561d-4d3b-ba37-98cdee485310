


import { TDT_BASE_URL, TDT_TOKEN } from '@/constants';

// 地图服务相关
const tiandituBaseUrl = TDT_BASE_URL; // 天地图服务地址
const token = TDT_TOKEN; // 天地图管网申请token
/**
 * 天地图urlTemplate hook
 */
export function useTdtInstanceComponent(WebTileLayer) {
  // 读取环境变量，vite下环境变量均为字符串
  const isOffline = import.meta.env.VITE_BUILD_PACKAGE_OFFLINE === 'true';
  let tiledLayerIns = null;
  let tiledLayerAnnoIns = null;
  let normalLayerIns = null;
  let normalAnnoIns = null;



  if (isOffline) {
     // 服务器url
  const serverUrl = import.meta.env.VITE_OFFLINE_SERVER_URL + '/resource';
    // 球面墨卡托投影矢量底图
    tiledLayerIns = new WebTileLayer({
      urlTemplate: `${serverUrl}/tiles/ying_xiang/img_w_{row}_{col}_{level}.png`,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return `${serverUrl}/tiles/ying_xiang/img_w_${row}_${col}_${level}.png`;
        } else {
          return '';
        }
      }
    });

    // 矢量标注(球面墨卡托投影)
    tiledLayerAnnoIns = new WebTileLayer({
      urlTemplate: `${serverUrl}/tiles/ying_xiang/cia_w_{row}_{col}_{level}.png`,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return `${serverUrl}/tiles/ying_xiang/cia_w_${row}_${col}_${level}.png`;
        } else {
          return '';
        }
      }
    });

    // 经纬度投影 矢量底图
    normalLayerIns = new WebTileLayer({
      urlTemplate: `${serverUrl}/tiles/shi_liang/vec_w_{row}_{col}_{level}.png`,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      getTileUrl: function (level, row, col) {
        if (level <= 18) {
          return `${serverUrl}/tiles/shi_liang/vec_w_${row}_${col}_${level}.png`;
        } else {
          return '';
        }
      }
    });

    // 矢量注记
    normalAnnoIns = new WebTileLayer({
      urlTemplate: `${serverUrl}/tiles/shi_liang/cva_w_{row}_{col}_{level}.png`,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      getTileUrl: function (level, row, col) {
        if (level <= 18) {
          return `${serverUrl}/tiles/shi_liang/cva_w_${row}_${col}_${level}.png`;
        } else {
          return '';
        }
      }
    });


  } else {
    // 球面墨卡托投影矢量底图
    tiledLayerIns = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=img_w/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
        } else {
          return '';
        }
      }
    });

    // 矢量标注(球面墨卡托投影)
    tiledLayerAnnoIns = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cia_w?T=vec_c/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=cia_w?T=vec_c/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
        } else {
          return '';
        }
      }
    });

    // 经纬度投影 矢量底图
    normalLayerIns = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false
    });

    // 矢量注记
    normalAnnoIns = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false
    });
  }

  return {
    tiledLayerIns,
    tiledLayerAnnoIns,
    normalLayerIns,
    normalAnnoIns
  };
}
