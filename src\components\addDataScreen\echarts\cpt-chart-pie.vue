<template>
  <div :id="uuid" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-pie'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData = ref([]);

// --- watch---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});

const loadData = (taskId: string) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.map) {
          const names = Object.keys(res.data.map);
          const values = Object.values(res.data.map);
          cptData.value = [];
          names.forEach((v, idx) => {
            cptData.value.push({ name: v, value: values[idx] });
          });
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

const loadChart = (attribute: any) => {
  chartOption.value = {
    color: attribute.pieColor,
    title: {
      text: attribute.chartTitle,
      subtext: attribute.subtext,
      left: attribute.titleX,
      top: attribute.titleY,
      textStyle: { fontSize: attribute.titleFontSize, color: attribute.titleTextColor },
      // 副标题文本样式设置
      subtextStyle: { fontSize: 12, color: attribute.subtextColor }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      show: attribute.legendShow,
      orient: attribute.orient,
      x: attribute.legendX,
      y: attribute.legendY,
      textStyle: {
        color: attribute.legendTextColor,
        fontSize: attribute.legendFontSize
      }
    },
    series: [
      {
        name: attribute.chartTitle,
        type: 'pie',
        roseType: attribute.roseType === 'false' ? false : attribute.roseType,
        radius: [attribute.radiusInside + '%', attribute.radiusOutside + '%'],
        label: {
          position: attribute.labelPosition,
          fontSize: attribute.labelFontSize,
          color: attribute.labelColor
        },
        itemStyle: {
          borderRadius: attribute.borderRadius
        },
        data: cptData.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(255, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  chart?.setOption(chartOption.value);
};

// --- onMounted ---
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
