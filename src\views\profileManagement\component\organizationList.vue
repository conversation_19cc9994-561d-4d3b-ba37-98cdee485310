<!-- 组织列表 -->
<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <!-- <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="small"
            >新建组织</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <!-- 加入组织这里到了申请列表的数据 -->
            <!-- <el-button
              type="warning"
              plain
              icon="el-icon-user-solid"
              size="small"
              @click="handleAddOrg"
            >加入组织</el-button> -->
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="organizationList">
          <el-table-column label="组织名称" align="center" prop="companyName" />
          <el-table-column label="角色" align="center">
            <template #default>
              <span>--</span>
            </template>
          </el-table-column>
          <el-table-column label="授权" align="center" prop="deptNames">
            <template #default>
              <el-image :src="vipIcon"></el-image>
            </template>
          </el-table-column>
          <el-table-column label="到期时间" align="center">
            <template #default="scope">
              <span v-if="scope.row.expireTime != null && scope.row.expireTime != ''">{{ formatDate(scope.row.expireTime) }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button v-show="scope.row.companyId == user.companyId && ifEditCompany" link @click="editCompany(scope.row)">编辑</el-button>
              <el-button link @click="handleXufei">立即续费</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <!-- 加入组织对话框的内容 -->
    <el-dialog v-model="addOriginzationVisible" title="加入组织" width="400px" :close-on-click-modal="false">
      <div style="display: flex; margin: 8px">
        <el-input placeholder="请搜索公司名称" prefix-icon="el-icon-search" v-model="searchCompanyName"></el-input>
        <el-button type="primary" size="small" icon="el-icon-search" @click="getAllCompanyList" style="margin-left: 6px">搜索</el-button>
      </div>
      <el-table :data="addOrgList">
        <el-table-column label="组织名称" align="center" prop="companyName" />
        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link @click="handleSelectOneAddOrg(scope.row)">加入</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="addTotal > 0" :total="addTotal" v-model:page="pageNum" v-model:limit="pageSize" @pagination="getAllCompanyList" />
    </el-dialog>
    <!-- 修改公司信息 -->
    <el-dialog v-model="editCompanyDialog" title="修改公司信息" width="30%" :close-on-click-modal="false" :before-close="handleClose">
      <el-form :model="nowCompany" :rules="nowCompanyRules" ref="nowCompanyRef" label-position="top" label-width="100px" class="demo-ruleForm">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="nowCompany.companyName" placeholder="请输入公司名称" maxlength="15"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitCompany">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCompanyList } from '@/api/login/index';
import { addClient } from '@/api/client';
import { getOrgizationList } from '@/api/login/index';
import vipIcon from '@/assets/images/org-vip.png';
import { getToken } from '@/utils/auth';
import { editMyCompany } from '@/api/control';
import { useUserStore } from '@/store/modules/user';

interface CompanyInfo {
  companyId: string;
  companyName: string;
  expireTime?: string;
  [key: string]: any;
}

// 定义用户类型接口
interface UserInfo {
  userName: string;
  nickName: string;
  phonenumber: string;
  companyId: string;
  [key: string]: any;
}

// store
const userStore = useUserStore();
const router = useRouter();

// 遮罩层
const loading = ref(true);
// 总条数
const total = ref(0);
// 用户表格数据
const organizationList = ref<CompanyInfo[]>([]);
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});

// 当前选中的部门
const selectDeptName = ref('');
// 加入组织的弹框
const addOriginzationVisible = ref(false);
// 加入的table数组
const addOrgList = ref<CompanyInfo[]>([]);
const addTotal = ref(0);
const pageSize = ref(10);
const pageNum = ref(1);
// 搜索公司出现列表
const searchCompanyName = ref('');
// 是否允许编辑公司
const ifEditCompany = ref(false);
const editCompanyDialog = ref(false);
const nowCompany = reactive<CompanyInfo>({
  companyId: '',
  companyName: ''
});
const nowCompanyRules = reactive<FormRules>({
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 3, max: 15, message: '长度在 3 到 15 个字符', trigger: 'blur' }
  ]
});

// 表单引用
const nowCompanyRef = ref<FormInstance>();

// 计算属性 - 获取用户信息
const user = computed(() => userStore.user as UserInfo);

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '--';
  return date; // 这里可以添加自定义的日期格式化逻辑，取代原来的过滤器
};

// 查询个人所在的公司和角色
const getList = () => {
  loading.value = true;
  getOrgizationList(user.value.userName).then((res) => {
    if (res.code === 200) {
      organizationList.value = res.data;
      total.value = res.data.length;
      loading.value = false;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 获取公司列表
const getAllCompanyList = () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    companyName: searchCompanyName.value
  };
  getCompanyList(params).then((res) => {
    if (res.code == 200) {
      addOrgList.value = res.data.rows;
      addTotal.value = res.data.total;
    }
  });
};

// 加入组织 打开弹框
const handleAddOrg = () => {
  addOriginzationVisible.value = true;
};

// 选择加入组织
const handleSelectOneAddOrg = (row: CompanyInfo) => {
  ElMessageBox.confirm('是否确定加入【' + row.companyName + '】公司？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = {
        custName: user.value.nickName,
        custPhone: user.value.phonenumber,
        direction: 2,
        status: 0,
        type: 2,
        companyName: row.companyName,
        companyId: row.companyId,
        remark: '客户申请'
      };
      // 新建-邀请客户
      addClient(params).then((res) => {
        if (res.code == 200) {
          addOriginzationVisible.value = false;
          ElMessage.success('申请加入成功');
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消加入'
      });
    });
};

// 续费
const handleXufei = () => {
  const routeInfo = {
    path: '/pay',
    query: {
      token: getToken()
    }
  };
  router.push(routeInfo);
};

// 编辑公司
const editCompany = (obj: CompanyInfo) => {
  nowCompany.companyId = obj.companyId;
  nowCompany.companyName = obj.companyName;
  editCompanyDialog.value = true;
};

// 关闭弹窗
const handleClose = () => {
  editCompanyDialog.value = false;
};

// 提交公司信息
const submitCompany = () => {
  nowCompanyRef.value?.validate((valid: boolean) => {
    if (valid) {
      // 修改公司信息
      editMyCompany(nowCompany).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          editCompanyDialog.value = false;
          userStore.logout().then(() => {
            location.href = '/index';
          });
          // 当退出登录的时候，删除该账号存储在sessionstorage中的数据
          sessionStorage.removeItem('filterMsg');
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  });
};

// 生命周期钩子
onMounted(() => {
  getList();
  if (userStore.roles.includes('admin')) {
    ifEditCompany.value = true;
  }
});
</script>

<style lang="scss" scoped>
.dept-header {
  .dept-name {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    letter-spacing: 0em;
    color: #161d26;
  }
  .dept-text {
    padding-left: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    letter-spacing: 0em;
    color: #8291a9;
  }
  .dept-number {
    padding-left: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    letter-spacing: 0em;
    color: var(--current-color);
  }
}
</style>
