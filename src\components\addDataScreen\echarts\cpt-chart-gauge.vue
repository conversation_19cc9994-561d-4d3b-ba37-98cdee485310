<template>
  <div :id="uuid" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-gauge'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption: any = ref({});
let chart: any = null;
const cptData = ref();
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = () => {
  getDataJson(props.option.cptDataForm).then((res) => {
    cptData.value = res;
    loadChart(props.option.attribute);
  });
};
const loadChart = (attribute) => {
  chartOption.value = {
    backgroundColor: '',
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: 'Pressure',
        type: 'gauge',
        startAngle: attribute.startAngle,
        endAngle: attribute.endAngle,
        radius: '100%',
        min: attribute.min,
        max: attribute.max,
        axisLabel: {
          distance: attribute.labelDistance,
          color: '#999',
          fontSize: attribute.labelSize
        },
        axisTick: {
          show: true,
          length: attribute.tickLength,
          distance: attribute.lineDistance
        },
        splitLine: {
          distance: attribute.lineDistance,
          length: attribute.tickLength * 1.5,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        axisLine: {
          lineStyle: {
            width: attribute.lineWidth,
            color: [
              [0.3, attribute.color1],
              [0.7, attribute.color2],
              [1, attribute.color3]
            ]
          }
        },
        progress: {
          show: true,
          width: attribute.lineWidth
        },
        pointer: {
          length: attribute.pointerLength + '%',
          width: attribute.pointerWidth
        },
        itemStyle: {
          color: attribute.itemColor
        },
        title: {
          show: true,
          fontSize: attribute.titleSize,
          color: attribute.titleColor
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: attribute.detailColor,
          fontSize: attribute.detailSize
        },
        data: [
          {
            value: cptData.value,
            name: attribute.title
          }
        ]
      }
    ]
  };
  chart?.setOption(chartOption.value);
};

// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
