<!-- 数据大屏 -->
<template>
  <div class="dataScreen-main" v-loading.fullscreen.lock="fullscreenLoading">
    <div class="hanle-div">
      <el-button type="primary" size="small" @click="addBigScreen">新建大屏</el-button>
    </div>
    <el-table :data="tableData" style="width: 100%; margin-top: 20px" border>
      <el-table-column label="序号" type="index" width="80"></el-table-column>
      <el-table-column label="大屏名称" prop="title"></el-table-column>
      <el-table-column label="创建时间" prop="createTime"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-link type="primary" @click="checkScreen(scope.row)">查看</el-link>
          <el-link type="primary" @click="deitScreen(scope.row)" style="margin-left: 10px">编辑</el-link>
          <!-- <el-link type="success" @click="editStatus(scope.row,1)" style="margin-left:10px;" v-show="scope.row.state == 0">启用</el-link>
          <el-link type="warning" @click="editStatus(scope.row,0)" style="margin-left:10px;" v-show="scope.row.state == 1">停用</el-link> -->
          <el-link type="danger" @click="delScreenHandler(scope.row)" style="margin-left: 10px" v-show="scope.row.isDefault != 1">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="btn-next-step">
      <el-button type="primary" @click="handleNext" size="small">
        下一步<el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue';
import { getScreenList, saveScreen, delScreen } from '@/api/dataScreen';
import taskDefaultBigData from '@/data/taskDefaultBigData.json';
import { selectModuleById } from '@/api/modal/index';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter, useRoute } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const tableData = ref<any[]>([]);
const fullscreenLoading = ref(false);
const searchMsg = reactive({
  pageNo: 1,
  pageSize: 10,
  groupName: '',
  moduleId: 0,
  companyId: undefined
});
const total = ref(0);

const moduleId = computed(() => {
  let id = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    id = Number(router.currentRoute.value.query.id);
  }
  return id;
});

// 创建 BroadcastChannel 实例
const channel = new BroadcastChannel('dataScreenChannel');

const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();

const addBigScreen = () => {
  window.open(`/addDataScreen/${moduleId.value}/0`);
};

const handleVisiable = () => {
  getData();
};

const getData = async () => {
  searchMsg.moduleId = moduleId.value;
  if (moduleId.value) {
    try {
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        searchMsg.companyId = companyId;
      }
      const res = await getScreenList(searchMsg);
      if (res.code === 200) {
        tableData.value = res.data.records;
        total.value = res.data.total;
        // 如果没有数据的话，就添加一个默认任务数据大屏
        if (res.data.records.length === 0) {
          const bigDataJson = { ...taskDefaultBigData };
          bigDataJson.moduleId = moduleId.value;

          const data = { id: moduleId.value };
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            data.companyId = companyId;
          }
          const code = await selectModuleByIdFun();
          bigDataJson.components.forEach((v: any) => {
            if (v.cptOption.cptDataForm && v.cptOption.cptDataForm.dataSource === 2) {
              // 是表达式的时候需要修改绑定的code
              v.cptOption.cptDataForm.code = code;
            }
            if (v.cptKey === 'cpt-select') {
              // 任务组件单独处理
              v.cptOption.cptDataForm.moduleId = moduleId.value;
            }
          });
          bigDataJson.components = JSON.stringify(bigDataJson.components);
          bigDataJson.id = ''; // id置空
          bigDataJson.isDefault = 1; // 默认模板
          // 设置公司私有模块的数据 需要传递公司id
          if (companyId && companyId !== undefined && companyId !== null) {
            bigDataJson.companyId = companyId;
          }
          const saveRes = await saveScreen(bigDataJson);
          if (saveRes.code !== 200) {
            ElMessage.error(saveRes.msg);
          }
        }
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      ElMessage.error('获取数据出错，请稍后重试');
    }
  }
};

/**
 * @description: 获取模块信息
 */
const selectModuleByIdFun = async () => {
  return new Promise((resolve, reject) => {
    selectModuleById({ id: moduleId.value }).then((res) => {
      if (res.code == 200) {
        resolve(res.data.code);
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

// 编辑大屏
const deitScreen = (obj: any) => {
  window.open(`/addDataScreen/${moduleId.value}/${obj.id}`);
};

// 删除大屏
const delScreenHandler = (obj: any) => {
  ElMessageBox.confirm('确定要删除该数据大屏吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const res = await delScreen({ id: obj.id });
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        ElMessage.error('删除数据出错，请稍后重试');
      }
    })
    .catch(() => {});
};

// 查看大屏
const checkScreen = (obj: any) => {
  window.open(`/preview/${moduleId.value}/${obj.id}`);
};

// 修改大屏状态
const editStatus = (obj: any, num: number) => {
  const item = JSON.parse(JSON.stringify(obj));
  item.state = num;
  let message = '';
  if (num === 0) {
    // 启用
    message = `确定要启用【${item.title}】吗`;
  } else {
    // 停用
    message = `确定要停用【${item.title}】吗`;
  }
  ElMessageBox.confirm(message, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      fullscreenLoading.value = true;
      try {
        // 设置公司私有模块的数据 需要传递公司id
        const companyId = route.query.companyId;
        if (companyId && companyId !== undefined && companyId !== null) {
          item.companyId = companyId;
        }
        const res = await saveScreen(item);
        fullscreenLoading.value = false;
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        fullscreenLoading.value = false;
        ElMessage.error('修改状态出错，请稍后重试');
      }
    })
    .catch(() => {});
};

const handleNext = () => {
  // 这里假设 $emit 是通过自定义事件实现的，在 setup 中可以使用 emits
  // 由于不清楚具体实现，暂时保留注释
  emit('nextStep', 5);
};

onMounted(() => {
  // window.addEventListener('focus', handleVisiable);
  // getData();
  // 监听消息事件
  channel.onmessage = (event) => {
    if (event.data.type === 'DATA_SAVED') {
      getData();
    }
  };
});

onBeforeUnmount(() => {
  // window.removeEventListener('focus', handleVisiable);
  channel.close();
});

defineExpose({
  getData
});
</script>

<style lang="scss" scoped>
.dataScreen-main {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  .btn-next-step {
    position: absolute;
    bottom: 120px;
    right: 42px;
  }
  .hanle-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
