import compression from 'vite-plugin-compression';

export default (env: any) => {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin: any[] = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(',');
    if (compressList.includes('gzip')) {
      // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
      plugin.push(
        compression({
          verbose: true, // 是否在控制台输出压缩结果
          disable: false, // 是否禁用
          threshold: 10240, // 体积大于 threshold 才会被压缩，单位 b，10kb
          algorithm: 'gzip', // 压缩算法, 可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
          ext: '.gz', // 生成的压缩包后缀
          deleteOriginFile: false // 是否删除源文件，设置为 true 后仅保留压缩包
        })
      );
    }
    if (compressList.includes('brotli')) {
      plugin.push(
        compression({
          verbose: true,
          disable: false,
          threshold: 10240,
          algorithm: 'brotliCompress',
          ext: '.br',
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
};
