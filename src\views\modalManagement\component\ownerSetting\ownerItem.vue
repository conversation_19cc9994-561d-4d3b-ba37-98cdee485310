<template>
  <!-- 权属人设置好的每一项 -->
  <div class="owner-item-main">
    <el-row class="attribite-item">
      <el-col :span="7" style="display: flex; align-items: center">
        <div v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) === 'blob'" style="display: flex; align-items: center">
          <!-- <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" /> -->
          <el-image style="width: 20px; height: 20px; margin-right: 10px" :src="`${baseUrl}${item.iconUrl}?token=${token}`" :fit="fit" />
        </div>
        <div v-else style="margin-top: 5px">
          <svg-icon class-name="svg-item" :icon-class="item.iconUrl" />
        </div>
        <div class="item-type-name">{{ item.typeName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="item-remark">{{ item.remark }}</div>
      </el-col>
      <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end">
        <el-tooltip class="item" effect="dark" content="字段" placement="top">
          <div class="text-btn" @click="handleOpenInfoField(item)">
            <svg-icon class-name="svg-item" icon-class="more_field" />
          </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="修改" placement="top">
          <div class="text-btn" @click="handleOwnerSetting('修改属性', item)">
            <svg-icon class-name="svg-item" icon-class="more_edit" />
          </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="删除" placement="top">
          <div class="text-btn" @click="handleOwnerDelete(item)">
            <svg-icon class-name="svg-item" icon-class="more_delete" />
          </div>
        </el-tooltip>
      </el-col>
    </el-row>
    <!-- 修改权属人信息 -->
    <add-owner-type-dialog
      :title="ownerTitle"
      :ownerVisible="ownerVisible"
      :currentItem="currentItem"
      @closeOwner="handleCloseOwner"
      @updateOwner="$emit('updateOwner')"
    ></add-owner-type-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue';
import authImg from '@/components/authImg/index.vue';
import addOwnerTypeDialog from './addOwnerTypeDialog.vue';
import { saveFieldInOwner, saveFieldGroup } from '@/api/modal/index';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import SvgIcon from '../../svgIcon/index.vue';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const userStore = useUserStore();
const modalStore = useModalStore();

// 定义 props
const props = defineProps<{
  item: {
    id?: number;
    iconUrl?: string;
    typeName?: string;
    remark?: string;
    attribution?: {
      formData: any;
    };
    fieldModelList?: Array<{
      fieldName: string;
      id: number;
    }>;
    moduleId?: number;
    attribution?: any;
  };
  ownerTypeList: Array<any>;
}>();

// 定义 emits
const emit = defineEmits(['reflectField', 'updateOwner']);

const token = useUserStore().token;

// 定义响应式数据
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const ownerVisible = ref(false);
const ownerTitle = ref('新增类型');
const currentItem = ref({});

// 打开字段的页面
const handleOpenInfoField = (item: typeof props.item) => {
  modalStore.setGroupId(item.id);
  // 判断是权属人设置要点及字段 还是采集要素设置字段
  modalStore.setIsHasAcquition(false);
  // 设置当前树节点下的某一个节点的内容
  modalStore.setCheckedNodeItem({});
  // 将数据存到session中
  // sessionStorage.setItem('groupItem',JSON.stringify(item) )
  modalStore.setCurrentGroupItem(item);
  if (item.attribution) {
    const formData = item.attribution.formData;
    item.fieldModelList?.forEach((i) => {
      formData.fields?.forEach((e: any) => {
        // 回显字段时  对联系人进行特殊处理
        if (e.tagIcon === 'xtlxr' && e.tag === 'el-xtlxr') {
          e.childrenList?.forEach((child: any) => {
            if (i.fieldName === child.vModel) {
              child.id = i.id;
            }
          });
        } else {
          if (i.fieldName === e.vModel) {
            e.id = i.id;
          }
        }
      });
    });
    emit('reflectField', formData);
  } else {
    emit('reflectField', {});
  }
};

// 行布局 属性的反显
const handleRowShowField = (e: any, item: typeof props.item) => {
  e.children?.forEach((it: any) => {
    if (it.layout === 'rowFormItem') {
      handleRowShowField(it, item);
    } else {
      item.fieldModelList?.find((i) => {
        if (i.fieldName === it.vModel) {
          it.id = i.id;
        }
      });
    }
  });
  return e;
};

// 设置  修改权属人信息
const handleOwnerSetting = (str: string, item: typeof props.item) => {
  currentItem.value = item;
  ownerTitle.value = str;
  ownerVisible.value = true;
};

// 关闭新增权属人弹框
const handleCloseOwner = () => {
  ownerVisible.value = false;
};

// 删除权属人类型
const handleOwnerDelete = (item: typeof props.item) => {
  const params = {
    delFlag: 1, // 1删除 0不删除  默认0
    id: item.id,
    moduleId: item.moduleId
  };
  ElMessageBox.confirm(`你确定删除【${item.typeName}】组吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        params.companyId = companyId;
      }
      saveFieldGroup(params).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '删除成功'
          });
          emit('updateOwner');
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      });
    })
    .catch(() => {});
};

// 禁用和启用按钮  修改 字段的display 的值 0 不显示 1 显示
const handleFieldSetting = (row: any, num: number) => {
  const params = {
    fieldCn: row.fieldCn,
    fieldName: row.fieldName,
    fieldType: row.fieldType,
    inputHint: row.inputHint,
    valueMethod: row.valueMethod,
    quantify: row.quantify,
    required: row.required,
    options: row.options,
    defaults: '否',
    display: num, //  0  禁用  1  启用
    groupId: row.groupId, //  当前添加的字段属于权属组的id
    moduleId: row.moduleId,
    id: row.id
  };
  let str = '启用';
  if (num === 0) {
    str = '禁用';
  } else if (num === 3) {
    str = '删除';
    params.delFlag = 1; //1 已删除 0 未删除  默认0
  } else {
    str = '启用';
  }
  const { proxy } = getCurrentInstance()!;
  proxy
    .$confirm(`确认${str}该字段吗?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        params.companyId = companyId;
      }
      saveFieldInOwner([params]).then((res) => {
        if (res.code === 200) {
          proxy.$message.success(`${str}成功`);
          emit('updateOwner');
        } else {
          proxy.$message.error(res.msg);
        }
      });
    })
    .catch(() => {
      proxy.$message({
        type: 'info',
        message: '已取消'
      });
    });
};
</script>

<style lang="scss" scoped>
.owner-item-main {
  background-color: #f6f7f8;
  border-radius: 4px;
  margin: 8px 16px;
  position: relative;
  width: 100%;
  .attribite-item {
    // padding: 8px 16px;
    padding: 0px 16px;
    height: 40px;
    line-height: 38px;
    border-radius: 4px;
    background-color: rgba(246, 247, 248, 1);
    // text-align: center;
    display: flex;
    align-items: center;
    .svg-item {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      color: #333;
    }
    .item-type-name {
      height: 40px;
      font-size: 14px;
      text-align: left;
      color: #161d26;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-weight: 600;
    }
    .item-remark {
      height: 40px;
      color: #161d26;
      font-size: 14px;
      text-align: left;
      font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        Arial,
        sans-serif;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .text-btn {
      margin: 0 8px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      .svg-item {
        width: 16px;
        height: 16px;
        margin: auto;
        color: #8291a9;
        &:hover {
          color: var(--current-color);
        }
      }
      &:hover {
        width: 24px;
        height: 24px;
        background-color: rgba(0, 129, 255, 0.1);
        color: var(--current-color);
      }
      &:hover .svg-item {
        width: 16px;
        height: 16px;
        background-color: rgba(0, 129, 255, 0.1);
        color: var(--current-color);
      }
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--current-color);
  font-size: 16px;
}
.el-icon-arrow-down {
  font-size: 16px;
}
</style>
