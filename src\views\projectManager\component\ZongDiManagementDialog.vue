<!-- 管理宗地的弹框 -->
<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      class="container-box"
      :title="zdTitle"
      :model-value="zdMangerDialog"
      @update:model-value="(val: any) => emit('update:zdMangerDialog', val)"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="handleClose"
      @open="handleOpenZDManagement"
      width="874px"
    >
      <div class="dialog-row">
        <!-- <div class="dialog-label" v-if="zdTitle == '管理宗地'">宗地位置</div>
        <areaCodeTemp :size="`default`" style="margin-right:16px" v-if="zdMangerDialog && zdTitle == '管理宗地'" @changeCityCode="changeCityCode" :selectAreaCode="queryParams.areaCode"></areaCodeTemp> -->
        <div class="dialog-label">数据名称</div>
        <el-input
          placeholder="请输入数据名称"
          v-model="queryParams.parcelName"
          style="width: 216px; margin-right: 24px"
          @clear="getParcelList"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="getParcelList">查询</el-button>
        <el-button icon="el-icon-refresh-right" @click="resetParcelList">重置</el-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="managerZDData"
        tooltip-effect="dark"
        style="width: 100%; height: 400px; overflow: auto"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClickName"
        row-key="id"
      >
        <el-table-column type="selection" reserve-selection width="55"> </el-table-column>
        <el-table-column :label="tableTitle">
          <template #default="{ row }">
            {{ row.parcelName }}
          </template>
        </el-table-column>
        <el-table-column :label="tablePosition">
          <template #default="{ row }">
            {{ row.fullAddress ? row.fullAddress : '- -' }}
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: space-between">
        <div style="padding: 32px 16px">
          <span>已选择</span><span style="color: var(--current-color); padding-left: 4px">{{ multipleSelection.length }}</span>
        </div>
        <div>
          <el-pagination
            v-show="total > 0"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNum"
            :page-sizes="pageSizes"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitSelectedZongDi">确 认</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { selectRules, getPlaceList } from '@/api/modal';

interface QueryParams {
  areaCode: string;
  pageNum: number;
  pageSize: number;
  parcelName: string;
  moduleId: number;
  levelNum?: number;
}

interface ParcelItem {
  id: number | string;
  parcelName: string;
  fullAddress?: string;
  flag?: boolean;
}

const props = defineProps<{
  zdMangerDialog: boolean;
  zdTitle: string;
  moduleId: string;
  selectedList: ParcelItem[];
}>();

const emit = defineEmits<{
  (e: 'selectedZongDi', selection: ParcelItem[]): void;
  (e: 'closeManagement'): void;
  (e: 'update:zdMangerDialog', value: boolean): void;
}>();

// 查询列表参数
const queryParams = reactive<QueryParams>({
  areaCode: '',
  pageNum: 1,
  pageSize: 10,
  parcelName: '',
  moduleId: 0
});

const total = ref(0);
const managerZDData = ref<ParcelItem[]>([]);
const multipleSelection = ref<ParcelItem[]>([]);
const tableTitle = ref('数据名称');
const tablePosition = ref('数据位置');
const pageSizes = [10, 50, 100, 200, 1000, 2000];
const fullscreenLoading = ref(false);
const multipleTable = ref();

// 监听selectedList变化
watch(
  () => props.selectedList,
  (val) => {
    nextTick(() => {
      multipleSelection.value = val;
    });
  },
  { deep: true }
);

/**
 * 打开弹框时判断请求列表
 */
const handleOpenZDManagement = async () => {
  try {
    const res = await selectRules({ moduleId: props.moduleId });
    if (res.code === 200) {
      tableTitle.value = res.data[0].typeName + '名称';
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
  }
  getParcelList();
};

/**
 * 数据的回显
 */
const toggleSelectionItem = () => {
  const ids = props.selectedList;
  const list = managerZDData.value || [];
  const rowItem = list.filter((item) => ids.some((id) => id.id === item.id));

  nextTick(() => {
    multipleTable.value?.clearSelection();
    rowItem.forEach((row) => {
      multipleTable.value?.toggleRowSelection(row, true);
    });
  });
};

// 获取宗地列表
const getParcelList = async () => {
  fullscreenLoading.value = true;
  queryParams.moduleId = Number(props.moduleId);
  queryParams.levelNum = 1;

  try {
    const res = await getPlaceList(queryParams);
    if (res.code === 200) {
      managerZDData.value = res.data.list;
      managerZDData.value.forEach((v) => {
        multipleSelection.value.forEach((k) => {
          if (k.id === v.id) {
            nextTick(() => {
              multipleTable.value?.toggleRowSelection(v, true);
            });
          }
        });
      });
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
  } finally {
    fullscreenLoading.value = false;
  }
};

// 批量选择
const handleSelectionChange = (val: ParcelItem[]) => {
  multipleSelection.value = val;
};

const handleRowClickName = (row: ParcelItem) => {
  row.flag = !row.flag;
  multipleTable.value?.toggleRowSelection(row, row.flag);
};

// 提交选中的宗地
const handleSubmitSelectedZongDi = () => {
  emit('selectedZongDi', multipleSelection.value);
};

// 关闭时清除数据
const handleClose = () => {
  total.value = 0;
  queryParams.pageSize = 10;
  queryParams.pageNum = 1;
  queryParams.areaCode = '';
  queryParams.parcelName = '';
  managerZDData.value = [];
  multipleSelection.value = [];
  multipleTable.value?.clearSelection();
  emit('closeManagement');
};

// 重置清除数据
const resetParcelList = () => {
  queryParams.pageSize = 10;
  queryParams.pageNum = 1;
  queryParams.areaCode = '';
  queryParams.parcelName = '';
  handleOpenZDManagement();
};

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  queryParams.pageNum = 1;
  getParcelList();
};

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val;
  getParcelList();
};
</script>

<style lang="scss" scoped>
.container-box {
  //弹出层里内容的高度
  :deep(.el-dialog__body) {
    padding: 10px 20px 0px 20px !important;
  }
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .dialog-label {
    color: #161d26;
    margin-right: 10px;
  }
}
</style>
