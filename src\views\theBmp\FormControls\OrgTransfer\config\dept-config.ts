import { getDpts, getUsers } from '@/api/process';
import { ElMessage } from 'element-plus';

interface TreeNode {
  level: number;
  data: {
    deptId?: string | number;
    userId?: string | number;
    [key: string]: any;
  };
}

interface DeptNode {
  deptId: string | number;
  deptName: string;
  label: string;
  nodeId: string | number;
}

interface UserNode {
  userId: string | number;
  userName: string;
  label: string;
  nodeId: string | number;
}

type NodeData = DeptNode | UserNode;

const toHump = (name: string): string => name.replace(/\_(\w)/g, (_, letter) => letter.toUpperCase());

async function getDepChildNode(orgId: string | number): Promise<NodeData[]> {
  try {
    const res = await getDpts({ parentId: orgId });
    if (res.code === 200) {
      return res.data.map((v: any) => ({
        deptId: v.deptId,
        deptName: v.deptName,
        label: v.deptName,
        nodeId: v.deptId
      }));
    }
  } catch (error) {
    console.error('获取子节点数据出错:', error);
  }
  return [];
}

async function loadDepOrUser(node: TreeNode | null, loadDep = true): Promise<NodeData[]> {
  let nodeData: NodeData[] = [];

  if (!node || node.level === 0) {
    try {
      const res = await getDpts({});
      if (res.code === 200) {
        return res.data
          .filter((item: any) => item.parentId === 0)
          .map((v: any) => ({
            deptId: v.deptId,
            deptName: v.deptName,
            label: v.deptName,
            nodeId: v.deptId
          }));
      } else {
        ElMessage.error(res.msg);
        return [];
      }
    } catch (error) {
      console.error('获取根部门失败:', error);
      return [];
    }
  }

  if (node.data.deptId) {
    try {
      // 获取下级部门
      const deptRes = await getDpts({ parentId: node.data.deptId });
      if (deptRes.code === 200) {
        nodeData = deptRes.data.map((v: any) => ({
          deptId: v.deptId,
          deptName: v.deptName,
          label: v.deptName,
          nodeId: v.deptId
        }));
      }

      // 获取部门下的成员
      const userRes = await getUsers({
        deptId: node.data.deptId,
        pageNum: 1,
        pageSize: 1000
      });

      if (userRes.code === 200) {
        const userData = userRes.rows.map((v: any) => ({
          userId: v.userId,
          userName: v.nickName,
          label: v.nickName,
          nodeId: v.userId
        }));

        nodeData = nodeData.length > 0 ? [...nodeData, ...userData] : userData;
      }
    } catch (error) {
      console.error('获取部门或用户数据失败:', error);
    }
  }

  return nodeData;
}

async function getRootDept(): Promise<DeptNode | null> {
  let rootDepts: DeptNode[] = [];
  try {
    const res = await getDpts({});
    if (res.code === 200) {
      rootDepts = res.data
        .filter((item: any) => item.parentId === 0)
        .map((v: any) => ({
          deptId: v.deptId,
          deptName: v.deptName,
          label: v.deptName,
          nodeId: v.deptId
        }));
    }
  } catch (err) {
    console.error('获取根部门失败:', err);
  }
  return rootDepts[0] || null;
}

export const DEP_CONFIG = {
  tabName: '部门',
  type: 'dep',
  children: 'children',

  nodeId: (data: NodeData): string | number => {
    return 'userId' in data ? data.userId : data.deptId;
  },

  label: (data: NodeData): string => {
    return data.label;
  },

  isLeaf: (): boolean => {
    return true;
  },

  searchResTip: (): string => {
    return '';
  },

  disabled: (): boolean => {
    return false;
  },

  onload: (node: TreeNode) => {
    return loadDepOrUser(node);
  },

  onsearch: async (searchString: string, resolve: (data: NodeData[]) => void): Promise<void> => {
    try {
      const res = await getDpts({ deptName: searchString });
      if (res.code === 200) {
        const list = res.data.map((v: any) => ({
          deptId: v.deptId,
          deptName: v.deptName,
          label: v.deptName,
          nodeId: v.deptId
        }));
        resolve(list);
      } else {
        ElMessage.error(res.msg);
        resolve([]);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      resolve([]);
    }
  }
};
