<!-- 属性 -->
<template>
  <div class="msgInfo-main" v-loading="loading">
    <div class="title">
      {{ checkedLinye.parcelName }}
      <div class="title-right" @click="closeInfo">
        <el-icon><CircleClose /></el-icon>
      </div>
    </div>
    <div class="info-select-tab">
      <div class="info-left">
        <div class="menu-box">
          <div v-for="(item, index) in selectTitleList" :key="index">
            <div class="menu-item" :class="{ 'menu-active': item.checked }" @click="changeTab(item)">
              {{ item.typeName }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 基础属性组 -->
    <template v-if="nowEditAttr && !nowEditAttr.ruleAttribution">
      <div class="content">
        <div class="content-normal">
          <div class="grid-row" v-for="(zditem, zindex) in nowEditAttr.fieldModelList" :key="zindex">
            <div class="label" :title="zditem.fieldCn" @contextmenu.prevent="copyText(zditem)">
              <span v-show="zditem && zditem.attribution.expression" :title="zditem && zditem.attribution.expression" style="color: red"
                ><el-icon><InfoFilled /></el-icon
              ></span>
              {{ zditem.fieldCn }}
            </div>
            <div class="hr"></div>
            <div class="right-content">
              <!-- 文本输入框 -->
              <template v-if="zditem.valueMethod == 'input'">
                <el-input v-model="zditem.content" :placeholder="zditem.inputHint"></el-input>
              </template>
              <!-- 多行输入框 -->
              <template v-if="zditem.valueMethod == 'textarea'">
                <el-input type="textarea" :rows="2" :placeholder="zditem.inputHint" v-model="zditem.content"> </el-input>
              </template>
              <!-- 数字输入框 -->
              <template v-if="zditem.valueMethod == 'number'">
                <el-input
                  type="number"
                  v-model="zditem.content"
                  :placeholder="zditem.inputHint"
                  :maxlength="getNumberMaxLength(zditem)"
                  style="width: 100%"
                  @input="handleNumberInput($event, zditem)"
                ></el-input>
              </template>
              <!-- 下拉选择 -->
              <template v-if="zditem.valueMethod == 'select'">
                <el-select v-model="zditem.content" :placeholder="zditem.inputHint" clearable filterable style="width: 100%">
                  <el-option v-for="item in zditem.attribution.options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </template>

              <!-- 级联选择 -->
              <template v-if="zditem.valueMethod == 'cascader'">
                <el-cascader
                  v-model="zditem.content"
                  :options="zditem.attribution.options"
                  :props="{ checkStrictly: true }"
                  style="width: 100%"
                ></el-cascader>
              </template>

              <!-- 单选radio -->
              <template v-if="zditem.valueMethod == 'radio'">
                <el-radio-group v-model="zditem.content">
                  <el-radio :value="item.value" v-for="(item, index) in zditem.attribution.options" :key="index">{{ item.label }}</el-radio>
                </el-radio-group>
              </template>
              <!-- 多选框 -->
              <template v-if="zditem.valueMethod == 'checkbox'">
                <el-checkbox-group v-model="zditem.content">
                  <el-checkbox :value="item.value" v-for="(item, index) in zditem.attribution.options" :key="index">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
              </template>
              <!-- 日期类型 -->
              <template v-if="zditem.valueMethod == 'date'">
                <el-date-picker
                  v-model="zditem.content"
                  :type="zditem.attribution.type"
                  value-format="x"
                  :placeholder="zditem.inputHint"
                  style="width: 100%"
                >
                </el-date-picker>
              </template>
              <!-- 日期区间 -->
              <template v-if="zditem.valueMethod == 'date-range'">
                <el-date-picker
                  style="width: 100%"
                  :modelValue="formatDateRangeValue(zditem.content)"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @update:modelValue="handleDateRangeUpdate($event, zditem)"
                >
                </el-date-picker>
              </template>
              <!-- time类型 -->
              <template v-if="zditem.valueMethod == 'time'">
                <el-time-picker
                  v-model="zditem.content"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                  value-format="HH:mm:ss"
                  :placeholder="zditem.inputHint"
                  style="width: 100%"
                >
                </el-time-picker>
              </template>
              <!-- 图片类型 -->
              <template v-if="zditem.valueMethod == 'upload'">
                <el-upload
                  :action="`${base}/qjt/file/multi/upload`"
                  :headers="headers"
                  name="files"
                  accept=".jpg,.png"
                  list-type="picture-card"
                  :file-list="zditem.fieldList"
                  :limit="zditem.attribution.picNum || 1"
                  :on-success="(response) => handleSuccessTP(response, zditem)"
                  :on-remove="(file) => handleRemoveTP(file, zditem)"
                  :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem)"
                >
                  <el-icon>
                    <Plus></Plus>
                  </el-icon>
                </el-upload>
              </template>
              <!-- 附件 -->
              <template v-if="zditem.valueMethod == 'xtfj'">
                <el-upload
                  class="upload-demo"
                  :headers="headers"
                  :action="`${base}/qjt/file/multi/upload`"
                  :on-success="(response) => handleSuccessFJ(response, zditem)"
                  :on-remove="(file) => handleRemoveFJ(file, zditem)"
                  multiple
                  name="files"
                  :before-upload="(file) => beforeAvatarUpload(file, zditem)"
                  :limit="zditem.attribution.picNum"
                  :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem)"
                  :file-list="zditem.content"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <template #tip>
                    <div class="el-upload__tip" style="color: red">只能上传{{ zditem.attribution.acceptType.join(',') }}文件</div>
                  </template>
                  <!-- <template #file="{ file }">
                    <div class="fj-row">
                      {{ file.name }}
                      <div style="cursor: pointer; color: #409eff" @click="handleRemove(file, zditem)">×</div>
                    </div>
                  </template> -->
                </el-upload>
              </template>
              <!-- 表格 -->
              <template v-if="zditem.valueMethod == 'xttable'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持表格</span>
              </template>
              <!-- 身份证识别 -->
              <template v-if="zditem.valueMethod == 'idCardScan'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持身份证识别</span>
              </template>
              <!-- 银行卡识别 -->
              <template v-if="zditem.valueMethod == 'xtBankCard'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持银行卡识别</span>
              </template>
              <!-- 视频 -->
              <template v-if="zditem.valueMethod == 'xtvideo'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持视频</span>
              </template>
              <!-- 行政区域 -->
              <template v-if="zditem.valueMethod == 'area'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持行政区域</span>
              </template>
              <!-- 签名 -->
              <template v-if="zditem.valueMethod == 'xtqm'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持签名</span>
              </template>
              <!-- 指纹 -->
              <template v-if="zditem.valueMethod == 'xtzw'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持指纹</span>
              </template>
              <!-- 植物识别 -->
              <template v-if="zditem.valueMethod == 'xtzwsb'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持植物识别</span>
              </template>
              <!-- 动物识别 -->
              <template v-if="zditem.valueMethod == 'xtdwsb'">
                <span style="color: rgb(0, 0, 0, 0.4)">暂不支持动物识别</span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 多采属性组 -->
    <template v-else>
      <div class="content">
        <el-button type="text" class="add-more" size="mini" @click="addMoreGroup">新增</el-button>
        <el-card class="box-card" v-for="(item, index) in nowEditAttr.list" :key="index" style="margin: 8px">
          <template #header>
            <div class="clearfix">
              <span>{{ item.label }}</span>
              <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
              <div style="float: right; display: flex">
                <template v-if="item.fieldModelList.length > 4">
                  <el-link type="danger" style="margin-right: 10px" @click="delChild(nowEditAttr, index)">删除</el-link>
                  <span style="cursor: pointer" v-show="!item.isShow" @click="changeShow(item, 1)"
                    >展开 <el-icon><ArrowDown /></el-icon>
                  </span>
                  <span style="cursor: pointer" v-show="item.isShow" @click="changeShow(item, 2)"
                    >收起 <el-icon><ArrowUp /></el-icon
                  ></span>
                </template>
                <template v-else>
                  <el-link type="danger" style="margin-right: 10px" @click="delChild(nowEditAttr, index)">删除</el-link>
                </template>
              </div>
            </div>
          </template>

          <div class="conten-box">
            <div class="child-row" v-for="(zditem, zindex) in item.fieldModelList" :key="zindex" v-show="item.isShow || (!item.isShow && zindex < 4)">
              <div class="child-item child-left" :title="zditem.fieldCn" @contextmenu.prevent="copyText(zditem)">
                <span v-show="zditem && zditem.attribution.expression" :title="zditem && zditem.attribution.expression" style="color: red"
                  ><el-icon><InfoFilled /></el-icon
                ></span>
                {{ zditem.fieldCn }}
              </div>
              <div class="hr"></div>
              <div class="child-item child-right">
                <!-- 文本输入框 -->
                <template v-if="zditem.valueMethod == 'input'">
                  <el-input v-model="zditem.content" :placeholder="zditem.inputHint"></el-input>
                </template>
                <!-- 多行输入框 -->
                <template v-if="zditem.valueMethod == 'textarea'">
                  <el-input type="textarea" :rows="2" :placeholder="zditem.inputHint" v-model="zditem.content"> </el-input>
                </template>
                <!-- 数字输入框 -->
                <template v-if="zditem.valueMethod == 'number'">
                  <el-input
                    type="number"
                    v-model="zditem.content"
                    :placeholder="zditem.inputHint"
                    :maxlength="getNumberMaxLength(zditem)"
                    style="width: 100%"
                    @input="handleNumberInput($event, zditem)"
                  ></el-input>
                </template>
                <!-- 下拉选择 -->
                <template v-if="zditem.valueMethod == 'select'">
                  <el-select v-model="zditem.content" :placeholder="zditem.inputHint" clearable filterable style="width: 100%">
                    <el-option v-for="item in zditem.attribution.options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                </template>
                <!-- 单选radio -->
                <template v-if="zditem.valueMethod == 'radio'">
                  <el-radio-group v-model="zditem.content">
                    <el-radio :label="item.value" v-for="(item, index) in zditem.attribution.options" :key="index">{{ item.label }}</el-radio>
                  </el-radio-group>
                </template>
                <!-- 多选框 -->
                <template v-if="zditem.valueMethod == 'checkbox'">
                  <el-checkbox-group v-model="zditem.content">
                    <el-checkbox :value="item.value" v-for="(item, index) in zditem.attribution.options" :key="index">{{ item.label }}</el-checkbox>
                  </el-checkbox-group>
                </template>
                <!-- 日期类型 -->
                <template v-if="zditem.valueMethod == 'date'">
                  <el-date-picker
                    v-model="zditem.content"
                    :type="zditem.attribution.type"
                    value-format="x"
                    :placeholder="zditem.inputHint"
                    style="width: 100%"
                  >
                  </el-date-picker>
                </template>
                <!-- time类型 -->
                <template v-if="zditem.valueMethod == 'time'">
                  <el-time-picker
                    v-model="zditem.content"
                    :picker-options="{
                      selectableRange: '00:00:00 - 23:59:59'
                    }"
                    value-format="HH:mm:ss"
                    :placeholder="zditem.inputHint"
                    style="width: 100%"
                  >
                  </el-time-picker>
                </template>
                <!-- 图片类型 -->
                <template v-if="zditem.valueMethod == 'upload'">
                  <el-upload
                    :action="`${base}/qjt/file/multi/upload`"
                    :headers="headers"
                    name="files"
                    accept=".jpg,.png"
                    list-type="picture-card"
                    :file-list="zditem.fieldList"
                    :limit="zditem.attribution.picNum || 1"
                    :on-success="(response) => handleSuccessTP(response, zditem)"
                    :on-remove="(file) => handleRemoveTP(file, zditem)"
                    :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem)"
                  >
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </template>
                <!-- 附件 -->
                <template v-if="zditem.valueMethod == 'xtfj'">
                  <el-upload
                    class="upload-demo"
                    :headers="headers"
                    :action="`${base}/qjt/file/multi/upload`"
                    :on-success="(response) => handleSuccessFJ(response, zditem)"
                    :on-remove="(file) => handleRemoveFJ(file, zditem)"
                    multiple
                    name="files"
                    :before-upload="(file) => beforeAvatarUpload(file, zditem)"
                    :limit="zditem.attribution.picNum"
                    :on-exceed="(files, fileList) => handleExceed(files, fileList, zditem)"
                    :file-list="zditem.content"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>

                    <template #tip>
                      <div class="el-upload__tip" style="color: red">只能上传{{ zditem.attribution.acceptType.join(',') }}文件</div>
                    </template>
                    <!-- <template #file="{ file }">
                      <div>
                        <div class="fj-row">
                          {{ file.title }}
                          <div style="cursor: pointer" @click="handleRemove(file, zditem)">×</div>
                        </div>
                      </div>
                    </template> -->
                  </el-upload>
                </template>
                <!-- 表格 -->
                <template v-if="zditem.valueMethod == 'xttable'">
                  <span style="color: rgb(0, 0, 0, 0.4)">暂不支持表格</span>
                </template>
                <!-- 身份证识别 -->
                <template v-if="zditem.valueMethod == 'idCardScan'">
                  <span style="color: rgb(0, 0, 0, 0.4)">暂不支持身份证识别</span>
                </template>
                <!-- 银行卡识别 -->
                <template v-if="zditem.valueMethod == 'xtBankCard'">
                  <span style="color: rgb(0, 0, 0, 0.4)">暂不支持银行卡识别</span>
                </template>
                <!-- 视频 -->
                <template v-if="zditem.valueMethod == 'xtvideo'">
                  <span style="color: rgb(0, 0, 0, 0.4)">暂不支持视频</span>
                </template>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import { isArray } from '@/utils/validate';
import { getFileType } from '@/utils/publicFun';

// --- 定义变量 ---
const loading = ref(false);
const selectTitleList = ref([]);
const nowEditAttr = ref({});
const headers = ref({
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
});
const token = getToken();
const base = import.meta.env.VITE_APP_BASE_API;

const props = defineProps<{
  checkedLinye: any;
}>();

// --- 定义方法 ---
/**
 * 初始格式化时间选择
 * @param {string | null} value  值
 */
const formatDateRangeValue = (value: string | null) => {
  if (!value) return null;
  const dates = value.split('至');
  return dates.length === 2 ? dates : null;
};

/**
 * 格式时间
 * @param {string | null} value  值
 * @param {any} item
 */
const handleDateRangeUpdate = (value: string[], item: any) => {
  if (!value) {
    item.content = '';
    return;
  }
  item.content = value.join('至');
};

/**
 * 处理数据
 */
const initData = (val: any) => {
  selectTitleList.value = [];
  val.fieldGroupModelList.forEach((v: any) => {
    v.fieldModelList.forEach((e) => {
      e.content = e.valueMethod === 'checkbox' ? [] : '';
    });

    const obj = {
      typeName: v.typeName,
      fieldModelList: v.fieldModelList,
      checked: false,
      ruleAttribution: v.ruleAttribution,
      linkId: v.linkId,
      linkType: v.linkType,
      groupId: v.id
    };
    if (v.ruleAttribution && !v.list) {
      //多采
      v.list = [];
      obj.list = v.list;
    }
    selectTitleList.value.push(obj);
  });
  selectTitleList.value[0].checked = true;
  nowEditAttr.value = selectTitleList.value[0];
  if (nowEditAttr.value.ruleAttribution && !nowEditAttr.value.list) {
    //多采
    nowEditAttr.value.list = [];
  }
  disposeInitAttr();
};
/**
 * 关闭图形信息
 */
const closeInfo = () => {
  emit('closeLinyeInfo');
};
/**
 * 切换属性组
 */
const changeTab = (item) => {
  selectTitleList.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
  nowEditAttr.value = item;
  if (item.ruleAttribution && !item.list) {
    //多采
    item.list = [];
  }
  disposeInitAttr();
};
/**
 * 初始化处理选择的属性组字段内容
 */
const disposeInitAttr = () => {
  // 需要把所有字段赋值 content
  nowEditAttr.value.fieldModelList.forEach((v) => {
    // 没有初始化的才进入初始化
    if (!v.content) {
      // 图片特殊处理
      if (v.valueMethod == 'upload' || v.valueMethod == 'xtqm' || v.valueMethod == 'xtzw') {
        v.content = [];
        v.fieldList = [];
      } else if (v.valueMethod == 'xtfj') {
        //附件
        v.content = [];
      } else {
        v.content = '';
      }
    }
  });
};
/**
 * 右键复制
 */
const copyText = (item) => {
  let text = '';
  if (item.attribution.expression) {
    text = item.attribution.expression;
  } else {
    text = item.label;
  }
  try {
    const tag = document.createElement('textarea'); // create textarea标签，注意：创建input标签则不会换行
    document.body.appendChild(tag); // 添加到body中
    tag.value = text; // 给textarea设置value属性为需要copy的内容
    tag.select(); // 选中
    document.execCommand('copy', false); // copy已经选中的内容
    ElMessage({
      message: '复制成功',
      type: 'success'
    });
    tag.remove();
  } catch (err) {
    console.error('复制到剪贴板失败:', err);
  }
};
/**
 * 自动计算当前数字输入宽的可输入的长度
 */
const getNumberMaxLength = (zdItem) => {
  let intMax = 32;
  if (zdItem.attribution.numberType && (zdItem.attribution.numberType === 1 || zdItem.attribution.numberType === 2)) {
    intMax = zdItem.attribution.accuracy ? zdItem.attribution.accuracy : 32;
  } else if (zdItem.attribution.numberType && (zdItem.attribution.numberType === 3 || zdItem.attribution.numberType === 4)) {
    // 浮点数和双精度 的精度位数不包括小数点，我这里需要去精度位数+1
    intMax = zdItem.attribution.accuracy ? zdItem.attribution.accuracy + 1 : 32;
  }
  return intMax;
};
/**
 * 处理数字输入框中的内容--使用正则来匹配内容
 * @param {string} val 输入的值
 * @param {object} field 字段对象
 */
const handleNumberInput = (val, field) => {
  // 正则表达式，匹配最多5位整数和2位小数
  const intMax = field.attribution.accuracy ? field.attribution.accuracy : 32;
  const decMax = field.attribution.precision ? field.attribution.precision : 15;
  let regex = new RegExp(`^(-?\\d{0,${intMax}})(\\.\\d{0,${decMax}})?$`);
  if (field.attribution.numberType && (field.attribution.numberType === 1 || field.attribution.numberType === 2)) {
    regex = new RegExp(`^\d{1,${intMax}}$|^$`);
  } else {
    regex = new RegExp(`^(-?\\d{0,${intMax}})(\\.\\d{0,${decMax}})?$`);
  }
  // 检查输入是否符合正则表达式
  if (!regex.test(val)) {
    // 不符合规则，修正输入值
    let correctedValue = val.replace(/[^0-9.-]/g, ''); // 移除非数字和非小数点字符
    correctedValue = correctedValue.replace(/(\..*)\./g, '$1'); // 移除多余的点
    correctedValue = correctedValue.replace(/^-/, ''); // 移除多余的负号
    // 修正整数部分
    const integerPart = correctedValue.split('.')[0];
    correctedValue = `${integerPart.slice(0, integerPart.length)}.${correctedValue.includes('.') ? correctedValue.split('.')[1] : ''}`;

    //修正小数部分
    const decimalPart = correctedValue.split('.')[1] || '';
    correctedValue = `${correctedValue.split('.')[0]}${decimalPart ? '.' + decimalPart.slice(0, decMax) : ''}`;

    // 最终修正值
    field.value = correctedValue;
  }
};

/**
 * 图片上传成功事件
 */
const handleSuccessTP = (response, item) => {
  if (!item.content || !isArray(item.content)) {
    item.content = [];
  }
  item.content.push({
    url: response.data[0].path,
    name: response.data[0].name
  });
  item.fieldList.push({
    oldUrl: response.data[0].path,
    url: `${base}/qjt/file/otherDownload/${response.data[0].path}?token=${token}`
  });
};
/**
 * 图片上传移除事件
 */
const handleRemoveTP = (file, item) => {
  let num = 0;
  for (let index = 0; index < item.content.length; index++) {
    if (file.response) {
      //代表是新上传的图片
      if (file.response.data[0].path == item.content[index].url) {
        num = index;
        break;
      }
    } else {
      //代表是老图片
      if (file.oldUrl == item.content[index].url) {
        num = index;
        break;
      }
    }
  }
  item.content.splice(num, 1);
  item.fieldList.splice(num, 1);
};
/**
 * 图片上传超过最大限制提示
 */
const handleExceed = (files, fileList, item) => {
  if (item.valueMethod == 'idCardScan') {
    //身份证识别的图片单独处理
    ElMessage.error('身份证识别正反面只允许上传一张！！！');
  } else {
    if (fileList.length >= item.attribution.picNum) {
      ElMessage.error(`${item.fieldCn}最多允许上传${item.attribution.picNum}`);
    }
  }
};
/**
 * 附件上传成功
 */
const handleSuccessFJ = (response, item) => {
  if (response.data && isArray(response.data)) {
    const obj = {};
    obj[`${item.fieldName}_0`] = response.data[0].path;
    obj[`${item.fieldName}_1`] = response.data[0].name;
    obj.url = response.data[0].path;
    obj.title = response.data[0].name;
    item.content.push(obj);
  }
};
/**
 * 附件移除
 */
const handleRemoveFJ = (file, zditem) => {
  let num = 0;
  if (zditem.content && isArray(zditem.content)) {
    for (let index = 0; index < zditem.content.length; index++) {
      if (file.response.data[0].path == zditem.content[index].url) {
        num = index;
        break;
      }
    }
    zditem.content.splice(num, 1);
  }
};
/**
 * 移除照片
 */
const handleRemove = (file, item) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      for (let i = 0; i < item.content.length; i++) {
        if (item.content[i].title === file.name) {
          item.content.splice(i, 1);
          break;
        }
      }
    })
    .catch(() => {});
};
/**
 * 上传前验证
 */
const beforeAvatarUpload = (file, item) => {
  const type = getFileType(file.type);
  let flg = false;
  for (let index = 0; index < item.attribution.acceptType.length; index++) {
    if (type.includes(item.attribution.acceptType[index])) {
      flg = true;
      break;
    }
  }
  if (!flg) {
    ElMessage.error(`不支持上传${file.type}格式！！！`);
  }
  return flg;
};
/**
 * 多采新增属性组
 */
const addMoreGroup = () => {
  nowEditAttr.value.list.push({
    typeName: nowEditAttr.value.typeName,
    groupId: nowEditAttr.value.groupId,
    linkId: nowEditAttr.value.linkId,
    linkType: nowEditAttr.value.linkType,
    ruleAttribution: nowEditAttr.value.ruleAttribution,
    fieldModelList: JSON.parse(JSON.stringify(nowEditAttr.value.fieldModelList)),
    label: `${nowEditAttr.value.typeName}${nowEditAttr.value.list.length + 1}`
  });
};
/**
 * 删除多采属性组某条数据
 */
const delChild = (obj, index) => {
  ElMessageBox.confirm('确定要删除该条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      obj.list.splice(index, 1);
    })
    .catch(() => {});
};
/**
 * 展开收起
 */
const changeShow = (item, type) => {
  if (type == 1) {
    //展开
    item.isShow = true;
    item.isEdit = false;
  } else {
    //收起
    item.isShow = false;
    item.isEdit = false;
  }
};

defineExpose({
  initData
});
</script>
<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px 10px 16px 10px;
}
.conten-box {
  width: calc(100% - 20px);
  margin: 10px 10px 0px 10px;
  border-top: 1px solid #46a6ffb0;
  border-left: 1px solid #46a6ffb0;
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  .child-title {
    font-size: 12px;
    padding: 10px;
  }
  .child-row {
    flex: 1 1 50%; /* 每行两条，减去左右padding */
    padding-bottom: 10px;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #46a6ffb0;
    border-right: 1px solid #46a6ffb0;
    padding: 8px 0px;
    align-items: center;
    .child-item {
      display: flex;
      padding-left: 10px;
    }
    .child-left {
      width: 140px;
      font-weight: bold;
      color: #111112 !important;
      font-size: 14px !important;
    }
    .hr {
      height: calc(100% + 16px);
      border-right: 1px solid #46a6ffb0;
    }
    .child-right {
      flex: 1;
      color: rgb(0, 0, 0, 0.7);
    }
    .el-input {
      & :deep(.el-input__inner) {
        border: var(--current-color) solid 1px;
        font-size: 14px;
      }

      & :deep(.el-select) {
        border: var(--current-color) solid 1px;
        font-size: 14px;
      }
    }
  }
  .child-footer {
    color: var(--current-color);
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    padding-bottom: 10px;
    cursor: pointer;
  }
}
.add-more {
  width: 45px;
}
.fj-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  justify-content: space-between;
  cursor: pointer;
}
.fj-row :hover {
  color: #409eff;
}
.azf-group {
  margin: 10px;
}
.flex-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 10px;
}
.add-flex-row {
  display: flex;
  align-items: center;
  overflow-x: auto;
  padding: 0px 10px;
  margin: 10px 0px;
}

.active-img {
  border: var(--current-color) solid 1px;
}
:deep(.el-upload--picture-card) {
  width: 59px;
  height: 59px;
  line-height: 59px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 59px;
  height: 59px;
  line-height: 59px;
}

.edit-div {
  height: 40px;
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0px;
  z-index: 1;
  // background: rgb(0, 0, 0);
  width: 100%;
  border-radius: 0px 0px 8px 8px;
  justify-content: flex-end;
  padding-right: 12px;
  .edit-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
  }
  .edit-btn-active {
    background: var(--current-color);
  }
  .edit-btn:hover {
    background: var(--current-color);
  }
  .sumbit-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background: var(--current-color);
    margin-left: 12px;
  }
  .reset-btn {
    color: #fff;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background: #909399;
    margin-left: 12px;
  }
}
.edit-handle {
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
}
:deep(.el-checkbox__label) {
  font-size: 12px;
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
}
.label {
  font-size: 13px !important;
  font-weight: bold !important;
  color: #ffffffad !important;
}
.dialog-table {
  width: 100%;
  max-height: 500px;
  overflow: auto;
}
.table-class {
  max-height: 500px;
  width: 100%;
  border-spacing: 2px;
  border: none;
  border-left: solid 1px #e6e7eb;
  border-top: solid 1px #e6e7eb;
}
.table-class td {
  border-left: none;
  border-top: none;
  padding: 10px;
  word-break: keep-all;
}
.table-class tr {
  border-top: none;
}
.th-class {
  padding: 10px;
  word-break: keep-all;
  font-weight: bold;
}
:deep(.el-tree-node__expand-icon.is-leaf) {
  display: none;
}
:deep(.el-dropdown) {
  position: relative;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 10px;
  background-color: var(--current-color);
  border-radius: 4px;
  height: 28px;
  padding: 0px 10px;
  cursor: pointer;
  white-space: nowrap;
}
.mini-main {
  height: 0px;
}
.msgInfo-main {
  width: 100%;
  height: 100%;
  font-size: 14px;
  border: #ededed solid 1px;
  display: flex;
  flex-direction: column;
  position: relative;
  .title {
    background: #f7f8f6;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    height: 40px;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    position: relative;
    .title-num {
      position: absolute;
      left: 10px;
      display: flex;
      align-items: center;
    }
  }

  .title-right {
    position: absolute;
    top: 0;
    right: 16px;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .deg-box {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
    position: relative;
    .handle-div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 56px;
      padding: 0px 5px;
    }
    .deg-close {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgb(0, 0, 0, 0.4);
      color: #fff;
      position: absolute;
      right: 25px;
      top: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 1;
      font-size: 16px;
    }
    .deg-img {
      width: calc(100%);
      height: 100%;
    }
    .sy-div {
      position: absolute;
      bottom: 0px;
      right: 0px;
      background: rgb(0, 0, 0, 0.4);
      font-size: 12px;
      color: #fff;
      .sy-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding: 0px 5px;
      }
    }
    .setting {
      position: absolute;
      top: 56px;
      left: 0px;
      background: rgb(0, 0, 0, 0.4);
      color: #fff;
      padding: 2px;
      cursor: pointer;
      z-index: 999;
      font-size: 12px;
      .checked-row {
        display: flex;
      }
    }
  }
  .info-select-tab {
    display: flex;
    flex-direction: row;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    .info-left {
      flex: 1;
      display: flex;
      flex-direction: row;
      height: auto;
      .menu-box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding: 6px;
        .menu-item {
          min-width: 106px;
          padding: 5px 10px;
          cursor: pointer;
          margin-right: 5px;
          margin-bottom: 5px;
          border: #e0e0e0 solid 1px;
          text-align: center;
        }
        .menu-item:hover {
          background-color: #e0e0e0;
        }
        .menu-active {
          background: #1890ff;
          color: #fff;
        }
      }
      .right-handle {
        padding: 5px 10px;
      }
    }
    .info-right {
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
    .info-end-handle {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
    .log-btn {
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 0px 10px;
      border-radius: 4px;
      margin-left: 10px;
    }
    .log-btn:hover {
      background: var(--current-color);
    }
    .active-log {
      background: var(--current-color);
    }
  }
  .content {
    flex: 1;
    width: 100%;
    overflow: auto;
    padding-bottom: 45px;
    .content-normal {
      border-top: 1px solid #46a6ffb0;
      border-left: 1px solid #46a6ffb0;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      .grid-row {
        flex: 1 1 50%; /* 每行两条，减去左右padding */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 8px 0px;
        border-bottom: 1px solid #46a6ffb0;
        border-right: 1px solid #46a6ffb0;
        .label {
          width: 130px;
          flex-wrap: wrap;
          font-weight: bold;
          color: #111112 !important;
          font-size: 14px !important;
          padding-left: 5px;
        }
        .hr {
          height: calc(100% + 16px);
          border-right: 1px solid #46a6ffb0;
        }
        .right-content {
          padding-left: 5px;
          padding-right: 5px;
          flex: 2;
          color: rgb(0, 0, 0, 0.7);
          display: flex;
          flex-wrap: wrap;
        }
      }
      // 这里展示表格的内容数据
      .grid-table-main {
        margin-top: 10px;
        display: flex;
        width: 100%;
        flex-direction: column;
        .table-list {
          width: 100%;
          .table-title {
            width: 100%;
            flex-wrap: wrap;
            font-weight: 600;
            color: #111112 !important;
            font-size: 14px !important;
            padding-left: 5px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-right: 8px;
          }
          .table-main {
            width: 100%;
            overflow: auto;
            .table-class {
              width: 100%;
              border-spacing: 2px;
              border: none;
              border-left: solid 1px #46a6ffb0;
              border-top: solid 1px #46a6ffb0;
              td {
                border-left: solid 1px #46a6ffb0;
                border-top: solid 1px #46a6ffb0;
                padding: 10px;
                word-break: keep-all;
              }
              tr {
                border-top: none;
              }
              .table-th-class {
                border-left: 1px solid #46a6ffb0;
                border-top: 1px solid #46a6ffb0;
                padding: 10px;
                word-break: keep-all;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    .info-row {
      display: flex;
      width: 100%;
      flex-direction: row;
      padding: 10px 16px;
      &:nth-child(odd) {
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
      }
      .label {
        width: 130px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        margin-right: 10px;
      }
      .content {
        flex: 1;
        max-width: 350px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        display: flex;
        flex-wrap: wrap;
      }
      .el-input {
        & :deep(.el-input__inner) {
          border: var(--current-color) solid 1px;
          font-size: 14px;
        }

        & :deep(.el-select) {
          border: var(--current-color) solid 1px;
          font-size: 14px;
        }
      }
    }
  }
  .content ::-webkit-scrollbar {
    width: 1px;
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
  .info-bottom {
    position: absolute;
    bottom: 0;
    background: #f7f8f6;
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 10px;
  }
}
.no-img {
  width: 59px;
  height: 59px;
  border-radius: 6px;
  background: rgba(248, 248, 248, 0.1);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  // margin-top: 5px;
  border: dashed 1px rgba(255, 255, 255, 0.5);
  font-size: 12px;
}
.img-box {
  width: 140px;
  height: 110px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
}
.video-box {
  width: 100%;
  height: 100px;
}
.discern-box {
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  .footer-div {
    position: absolute;
    bottom: 0px;
    height: 30px;
    background: rgb(0, 0, 0, 0.7);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--current-color);
    font-size: 12px;
    cursor: pointer;
    z-index: 1;
  }
}
.content-spe {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.spe-content {
  display: flex;
  max-height: 500px;
  overflow: auto;
  flex-direction: row;
  .left-img {
    flex: 1;
  }
  .right-content {
    margin-left: 10px;
    width: 100px;
    font-size: 12px;
    color: #666;
    .spe-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
.content-img {
  flex: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.content-img-table {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.content-video {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
}
.fj-content {
  display: flex;
  flex-direction: column;
  // color: #fff;
  .fj-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 32px;
    cursor: pointer;
  }
  .fj-item:hover {
    color: var(--current-color);
  }
}
.jc-div {
  position: absolute;
  left: 10px;
}
.jc-div1 {
  position: absolute;
}
.dialog-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  .dialog-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .label {
      width: 120px;
      text-align: right;
      margin-right: 10px;
    }
    .flex-content {
      flex: 1;
    }
  }
}
</style>
