<html xmlns="http://www.w3.org/1999/xhtml">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><link rel="icon" href="/favicon.ico" type="image/x-icon" /><title>
	Spire.Office
</title>

    <style>
        html {
            height: 100%;
            width: 100%;
        }

        body {
            background: #fff;
            color: #333;
            font-family: Arial, Tahoma,sans-serif;
            font-size: 12px;
            font-weight: normal;
            height: 100%;
            margin: 0;
            overflow-y: hidden;
            padding: 0;
            text-decoration: none;
        }

        form {
            height: 99%;
        }

        div {
            margin: 0;
            padding: 0;
        }
    </style>

    <script language="javascript" type="text/javascript" src="https://api.e-iceblue.cn/web/editors/spireapi/SpireCloudEditor.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <script type="text/javascript" language="javascript">
        let downloadUrl = getQueryString(window.location.search, "downloadUrl")
        let templateName = getQueryString(window.location.search, "templateName")
        let tempId = getQueryString(window.location.search, "tempId")
        let baseUrl = getQueryString(window.location.search, "baseUrl")
        let docEditor;
        let fileName = "";
        let lang = "zh";
        let fileType = "";
        let token = getQueryString(window.location.search, "token");
        let appid = "e9654077bf7e0312ba3e1c13bc1df1b4";
        let appkey = "69dLHOMVSYq0FrFd6WWJk7bFzsIonsaN";

        let innerAlert = function (message) {
            if (console && console.log)
                console.log(message);
        };

        let onReady = function () {
            innerAlert("Document editor ready");

        };

        let onDocumentStateChange = function (event) {
            let title = document.title.replace(/\*$/g, "");
            document.title = title + (event.data ? "*" : "");
        };

        let onRequestEditRights = function () {
            location.href = location.href.replace(RegExp("action=view\&?", "i"), "");
        };

        let onError = function (event) {
            if (event)
                innerAlert(event.data);
        };

        let onOutdatedVersion = function (event) {
            location.reload(true);
        };
        // 获取参数
        function getQueryString (queryString, name) {
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = queryString.substr(1).match(reg);
            if (r != null) {
                return decodeURI(r[2]);
            }
            return null;
        }
        function ajax(options) {
            options = options || {};
            let method = (options.type || "GET").toUpperCase(),
                url = options.url,
                queryString = null;
            if (!url)
                return;
            if (options.data) {
                queryString = [];
                for (let attr in options.data) {
                    queryString.push(attr + "=" + options.data[attr]);
                }
                queryString = queryString.join("&");
            }
            if (method === "GET" && queryString) {
                url += "?" + queryString;
                queryString = "";
            }
            let xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            if (method === "POST")
                xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.send(queryString);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        let data = xhr.responseText;
                        if (options.dataType === "json")
                            data = JSON.parse(data);
                        options.success && options.success(data);
                    } else {
                        options.error && options.error(xhr.status);
                    }
                }
            }
        }
        let callbackfn = async function (result) {
            if (result && result.data) {
                let data = result.data,
                    fileName = data[0],
                    url = data[1];
                if (fileName.indexOf('=') > -1)
                    fileName = fileName.split('=')[1];
                let host = location.hostname;
                let suffix = '.docx'
                await axios({
                    method:'post',
                    url: `${baseUrl}/qjt/file/multi/uploadFromUrl?fileName=${data[0]}&suffix=${suffix}&url=${data[1]}`,
                    headers: { 'Authorization': 'Bearer ' + token},
                    // withCredentials:true,//表明了是否是跨域请求、默认是default
                }).then((res) => {
                    if (res.data.code==200) {
                        let primary = {
                            id:tempId,
                            downloadUrl:decodeURI(res.data.data[0].path)
                        }
                        axios({
                            method:'post',
                            url: `${baseUrl}/qjt/output/template/update`,
                            headers: { 'Authorization': 'Bearer ' + token},
                            data:primary,
                            // withCredentials:true,//表明了是否是跨域请求、默认是default
                        }).then((resp) => {
                            if (resp.data.code==200) {
                                alert("保存成功")
                            }
                        })
                    } else {
                        alert(res.data.msg)
                    }
                })
            }
        };
        let connectEditor = function () {
            let type = 'desktop';
            if (type == "desktop") {
                let app = navigator.appVersion;
                if (app.toLowerCase().indexOf('window') != -1) {
                    type = "desktop";
                } else {
                    type = "mobile";
                }
            };
            // let urlString = "https://cloud.e-iceblue.cn/demo/sample.docx";
            
            let urlString = downloadUrl
            let arrfn = urlString.split(".");
            let strp= arrfn[arrfn.length - 1];
            let documentTypeValue=null;
             switch(strp){
             case "xls":
             case "xlsx":
             case "xlsm":
             case "xlt":
             case "xltx":
             case "xltm":
             case "ods":
             case "fods":
             case "ots":
             case "csv":
             documentTypeValue="spreadsheet";
             break;
             case "pps":
             case "ppsx":
             case "ppsm":
             case "ppt":
             case "pptx":
             case "pptm":
             case "pot":
             case "potx":
             case "potm":
             case "odp":
             case "fodp":
             case "otp":
             documentTypeValue="presentation";
             break;
             default:
             documentTypeValue="document";
             break;
             }
            
            docEditor = new SpireCloudEditor.OpenApi("iframeEditor",
                {
                    fileAttrs: {
                        fileInfo: {
                            name: templateName,
                            ext: '',
                            primary: '',
                            creator: '',
                            createTime: new Date()
                        },
                        sourceUrl: urlString,
                        createUrl: '',
                        callbackUrl: '',////This item can be empty, but only if the 'onSave' callback function must be defined in events. If the callback function is undefined and this item is empty, Cloud Editor will not provide save function.
                        verification: null,//用户文件系统下载文件时若需要验证类似token的数据可以写在这里
                        canEdit: true
                    },
                    user: {
                        primary: '',
                        name: '',
                        canSave: true,
                        customization: {
                            public: {
                                common: {
                                    whiteLabel: false,
                                    defaultZoom: 1,
                                    openReviewChanges: false,
                                    permGroups: ['everyone'],//限制编辑分组
                                    viewVersion: false,
                                    header: {
                                        hideTitle: false,
                                        defaultView: 'full'
                                    }
                                },
                                word: null,//doc定制
                                powerpoint: null,//ppt定制
                                excel: null//xls定制
                            },
                            "private":{
                            "token": null,
                                "appid": null,      
                                "appkey": null          
                            }
                        }
                    },
                    editorAttrs: {//编辑器配置
                        editorWidth: '100%',
                        editorHeight: '100%',
                        editorMode:'edit',
                        editorType: 'document',//编辑器类型，可不配置，程序根据文件类型获取，结果为 document,presentation,spreadsheet
                        platform: 'windows',//编辑器平台类型，可选windows， mobile， embedded
                        viewLanguage: 'zh',//平台界面展示语言可选en/zh
                        canChat: true,//是否可聊天
                        canComment: true,//是否可批注
                        canReview: true,
                        canDownload: true,
                        canForcesave: true,
                        embedded: {
                            saveUrl: '',
                            embedUrl: '',
                            shareUrl: ''
                        },
                        events: {
                        'onReady': onReady,
                        'onDocumentStateChange': onDocumentStateChange,
                        'onError': onError,
                        'onRequestEditRights': onRequestEditRights,
                        'onOutdatedVersion': onOutdatedVersion,
                        'onSave': callbackfn
                        }
                    }
                },
                appid,
				appkey
            );
        };
        if (window.addEventListener) {
            window.addEventListener("load", connectEditor);
        } else if (window.attachEvent) {
            window.attachEvent("load", connectEditor);
        }
    </script>
</head>
<body>
    <form name="form1" method="post" action="./docEditor.aspx?fileID=demo+(2).docx&lang=zh" id="form1">
        <div>
            <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwUKMTkyMjc5MTU4M2RkBQQQBsVcOHQbzTlwYapiES2Trc9Z/U4CC+r9rluZubc=" />
        </div>
        <div>
            <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="C2EAC0DE" />
        </div>
        <div id="iframeEditor"></div>
    </form>
</body>
</html>
