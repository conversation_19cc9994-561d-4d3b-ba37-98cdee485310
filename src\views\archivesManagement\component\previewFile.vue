<!-- 文件预览的组件内容 -->
<template>
  <div>
    <el-dialog
      :title="fileTitle"
      v-model="fjDialog"
      :before-close="handleCloseFj"
      :close-on-click-modal="false"
      :width="fileType === 4 ? '500px' : '90%'"
    >
      <div v-show="fileType === 1" style="width: 100%" :style="{ height: windowHeight }" v-html="vHtml"></div>
      <div v-show="fileType === 2">
        <el-table
          :data="excelData"
          border
          stripe
          style="width: 100%; overflow: auto"
          :style="{ height: windowHeight }"
          :header-cell-style="{ background: '#F5F4F7' }"
        >
          <el-table-column type="index" label="序号" width="60" :resizable="false" align="center" />
          <el-table-column v-for="(value, key, index) in excelData[0]" :key="index" :prop="key" :label="key" />
        </el-table>
      </div>
      <div v-show="fileType === 3" style="width: 100%">
        <iframe :src="fjUrl" frameborder="0" width="100%" :height="windowHeight"></iframe>
      </div>
      <div v-show="fileType === 4" style="width: 100%">
        <auth-img class="img" :authSrc="fjUrl" :width="'100%'" :height="'500px'" :radios="'12px'"></auth-img>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { getToken } from '@/utils/auth';
import axios from 'axios';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import authImg from './authImg.vue';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';

interface PreviewObject {
  name: string;
  url: string;
  [key: string]: any;
}

// 使用import.meta.env替代process.env
const baseUrl = import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/';

// 当前浏览器可视范围内的高度
const windowHeight = ref('600px');
// 预览附件的弹框
const fjDialog = ref(false);
// 附件的url地址
const fjUrl = ref('');
// 1doc 2excel 3pdf或者txt 4图片的预览
const fileType = ref(1);
const vHtml = ref('');
const workbook = ref<XLSX.WorkBook>({} as XLSX.WorkBook);
// excel绑定的数据
const excelData = ref<Record<string, any>[]>([]);
// 对话框的弹框名
const fileTitle = ref('查看');

// 关闭弹窗
const handleCloseFj = () => {
  fjDialog.value = false;
};

// 预览excel附件的时候数据的内容展示
const getTable = (sheetName: string) => {
  const worksheet = workbook.value.Sheets[sheetName];
  excelData.value = XLSX.utils.sheet_to_json(worksheet);
  fjDialog.value = true;
};

// 预览查看附件dialog
const handlePreviewFile = (obj: PreviewObject) => {
  fileTitle.value = obj.name;
  const fileExtension = obj.name.substring(obj.name.lastIndexOf('.') + 1, obj.name.length);

  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  windowHeight.value = window.innerHeight - 150 + 'px';

  if (fileExtension.toLowerCase() === 'txt' || fileExtension.toLowerCase() === 'pdf') {
    axios({
      method: 'get',
      url: `${baseUrl}${obj.url}?att=1`,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Access-Control-Allow-Origin': '*'
      },
      responseType: 'blob'
    }).then((res) => {
      let type = '';
      switch (fileExtension) {
        case 'txt':
          type = 'text/plain';
          break;
        case 'xls':
          type = 'application/vnd.ms-excel';
          break;
        case 'xlsx':
          type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'doc':
          type = 'application/msword';
          break;
        case 'docx':
          type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'pdf':
          type = 'application/pdf';
          break;
        default:
          break;
      }
      loading.close();
      fileType.value = 3;
      const blob = new Blob([res.data], { type: type });
      fjUrl.value = `${window.URL.createObjectURL(blob)}`;
      fjDialog.value = true;
    });
  } else if (fileExtension.toLowerCase() === 'jpg' || fileExtension.toLowerCase() === 'jpeg' || fileExtension.toLowerCase() === 'png') {
    // 设置数据格式
    loading.close();
    fileType.value = 4;
    fjUrl.value = `${baseUrl}${obj.url}`;
    fjDialog.value = true;
  } else {
    const xhr = new XMLHttpRequest();
    xhr.open('get', `${baseUrl}${obj.url}?att=1`, true);
    xhr.responseType = 'arraybuffer';
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = () => {
      if (xhr.status === 200) {
        if (fileExtension.toLowerCase() === 'doc' || fileExtension.toLowerCase() === 'docx') {
          mammoth
            .convertToHtml({ arrayBuffer: xhr.response })
            .then((resultObject) => {
              nextTick(() => {
                fileType.value = 1;
                loading.close();
                fjDialog.value = true;
                vHtml.value = resultObject.value;
              });
            })
            .catch((error) => {
              loading.close();
              ElMessageBox.alert('文件存在异常，无法打开(后缀为.doc 预览可能会发生错误，请更换成后缀为.docx文件，再次尝试)', '错误信息', {
                confirmButtonText: '确定'
              });
            });
        } else {
          fileType.value = 2;
          const data = new Uint8Array(xhr.response);
          try {
            const xWorkbook = XLSX.read(data, { type: 'array' });
            loading.close();
            const sheetNames = xWorkbook.SheetNames; // 工作表名称集合
            workbook.value = xWorkbook;
            getTable(sheetNames[0]);
          } catch (error) {
            loading.close();
            ElMessageBox.alert('文件存在异常，无法打开', '错误信息', {
              confirmButtonText: '确定'
            });
          }
        }
      }
    };
    xhr.send();
  }
};

// 导出需要的内容
defineExpose({
  handlePreviewFile
});
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  width: 100%;
}
:deep(.el-dialog__title) {
  width: calc(100% - 50px);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: block;
}
</style>
