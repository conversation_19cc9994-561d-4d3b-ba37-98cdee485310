<template>
  <div>
    <el-button @click="handleBatchPerson">批量处理个人版</el-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { getCompanyList, updateFromControl } from '@/api/control';

interface CompanyItem {
  companyId: string;
  companyName: string;
  type: number;
  [key: string]: any;
}

interface ChunkData {
  count: number;
  chunkSize: number;
  chunks: CompanyItem[];
}

export default defineComponent({
  name: 'BatchPersonDialog',
  setup() {
    const uploadTotal = ref(0);
    const uploadResultLength = ref(0);
    const successList = ref<{ companyId: string; companyName: string }[]>([]);
    const failList = ref<{ companyId: string; companyName: string }[]>([]);
    let loadingInstance: any = null;

    const handleBatchPerson = async () => {
      const params = {
        pageSize: 10000,
        pageNum: 1,
        companyType: 2
      };

      try {
        const res = await getCompanyList(params);

        if (res.code === 200) {
          const tableList = res.data.rows;
          uploadTotal.value = res.data.total;

          const text = `本次上传的数据共:${uploadTotal.value}条，已上传:${uploadResultLength.value * 10}条`;
          loadingInstance = ElLoading.service({
            lock: true,
            text,
            spinner: 'el-icon-loading',
            background: 'rgba(255, 255, 255, 0.9)'
          });

          const chunksData = await handleChunkData(tableList, 1);
          await handleAllAddByOne(chunksData);
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('批量处理失败:', error);
        ElMessage.error('批量处理失败');
      }
    };

    // 监听上传进度
    watch(
      uploadResultLength,
      (newVal) => {
        if (newVal === uploadTotal.value) {
          setTimeout(() => {
            if (loadingInstance) {
              loadingInstance.close();
            }
            if (failList.value.length > 0) {
              handleDownLoadErrorLog();
            }
            // getData()
          }, 1000);
        } else {
          if (loadingInstance) {
            loadingInstance.setText(`本次上传的数据共:${uploadTotal.value}条，已上传${newVal}条`);
          }
        }
      },
      { deep: true }
    );

    const handleDownLoadErrorLog = () => {
      // 下载txt文件
      const element = document.createElement('a');
      const endContent = `本次更新:成功了${successList.value.length}条，失败了${
        failList.value.length
      }条：\n ${failList.value.map((item) => `${item.companyName}(${item.companyId})`).join('\n')}`;

      element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
      element.setAttribute('download', '更新失败数据.txt');
      element.style.display = 'none';
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element); // 下载完成移除元素
    };

    const handleChunkData = async (list: CompanyItem[], num: number): Promise<ChunkData> => {
      const count = list.length;
      const chunkSize = num || 100;

      return {
        count,
        chunkSize,
        chunks: list
      };
    };

    const handleAllAddByOne = async (chunksData: ChunkData) => {
      for (let index = 0; index < chunksData.count; index++) {
        try {
          const type = chunksData.chunks[index].type;
          const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize, type);

          if (resultNum === uploadTotal.value) {
            // 当数据完成后关闭弹框
            // this.$emit("closeExcelAsset");
          }
        } catch (error) {
          console.error('处理异常:', error);
          ElMessage.error(error as string);
          continue;
        }
      }
    };

    const verificationOnece = async (item: CompanyItem, num: number, count: number, chunkSize: number, type: number): Promise<number> => {
      return new Promise((resolve) => {
        const params = { ...item };

        updateFromControl(params).then((res) => {
          if (res && res.code === 200) {
            setTimeout(() => {
              uploadResultLength.value = num * chunkSize > count ? count : num * chunkSize;
              uploadTotal.value = count;
              successList.value.push({
                companyId: item.companyId,
                companyName: item.companyName
              });
              resolve(uploadResultLength.value);
            }, 100);
          } else {
            uploadResultLength.value = num * chunkSize > count ? count : num * chunkSize;
            uploadTotal.value = count;
            failList.value.push({
              companyId: item.companyId,
              companyName: item.companyName
            });
            resolve(uploadResultLength.value);
          }
        });
      });
    };

    return {
      handleBatchPerson,
      uploadTotal,
      uploadResultLength,
      successList,
      failList
    };
  }
});
</script>

<style lang="scss" scoped>
// Add your styles here
</style>
