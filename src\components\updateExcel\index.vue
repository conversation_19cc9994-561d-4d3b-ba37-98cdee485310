<!-- 用excel更新数据 -->
<template>
  <div class="updateExcel-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      title="更新项目"
      v-model="uploadExcelDialogCopy"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="dialog-box" :style="{ height: dialogHeight }">
        <!-- 第一行 -->
        <div class="flex-row">
          <div class="flex-item">
            <div class="title-label">1、是否新增根数据</div>
            <el-radio-group v-model="isRootNode">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </div>
          <!-- <div class="flex-item" v-show="isRootNode == 2">
            <div class="title-label">2、操作方式</div>
            <el-radio-group v-model="operaType">
              <el-tooltip class="item" effect="dark" content="针对更新普通属性组" placement="top">
                <el-radio :label="1">一对一</el-radio>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="针对更新特殊（多采如【权利人】）属性组" placement="top">
                <el-radio :label="2">一对多</el-radio>
              </el-tooltip>
            </el-radio-group>
          </div> -->
          <div class="flex-item">
            <div class="title-label">2、上传excel</div>
            <el-upload
              class="upload-demo"
              :action="`${base}${sortUrl}`"
              :headers="headers"
              :show-file-list="false"
              :auto-upload="false"
              :multiple="false"
              :on-success="uploadSuccess"
              accept=".xlsx,.xls"
              :file-list="fileList"
              ref="uploadTemp"
              :on-change="handleChangeFile"
            >
              <el-button size="small" v-show="!isUpload">点击上传</el-button>
              <template #tip>
                <div v-show="!isUpload" class="el-upload__tip" style="color: rgba(148, 155, 164, 1)">
                  下载模板并完善信息后，可直接进行上传,支持格式：.xls、.xlsx
                </div>
              </template>
              <div v-if="isUpload" style="margin-bottom: 12px">{{ fileMsg.name }} ({{ filterSize(fileMsg.size) }}kb)</div>
              <el-button size="small" v-if="isUpload">重新选择</el-button>
            </el-upload>
          </div>
          <div class="flex-item">
            <div class="title-label">3、更新方式</div>
            <el-radio-group v-model="type">
              <el-radio :label="item.value" v-for="(item, index) in typeList" :key="index" :disabled="item.disable">{{ item.label }}</el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="flex-row">
          <div class="flex-item">
            <div class="title-label">4、选择（更新/新增）节点</div>
            <el-cascader
              clearable
              style="width: 100%"
              v-model="nodeId"
              :options="ruleTree"
              placeholder="请选择节点"
              :props="{ checkStrictly: true, children: 'list', value: 'id', label: 'typeName' }"
              @change="handleChange"
            ></el-cascader>
          </div>
          <div class="flex-item" v-show="isRootNode == 1">
            <div class="title-label">5、{{ chooseNode.typeName }}名称</div>
            <el-select v-model="currentParcelName" style="width: 100%" filterable clearable placeholder="请选择当前节点名称">
              <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <div class="title-label">{{ isRootNode == 2 ? '5、' : '6、' }}选择（更新/新增）属性组</div>
            <el-select v-model="groupId" placeholder="请选择" @change="changeGroup" style="width: 100%">
              <el-option
                v-for="item in fieldGroupModelList"
                :disabled="[5, 6].includes(item.linkType)"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="flex-row" v-show="isRootNode == 2">
          <div class="flex-item">
            <div class="title-label">6、关联字段（先选属性组，再选择字段）</div>
            <div class="flex-row">
              <el-select v-model="ysChooseGroupId" placeholder="请选择关联属性组" style="width: 100%" @change="chooseYSGroup">
                <el-option
                  v-for="item in fieldGroupModelList"
                  :key="item.id"
                  :disabled="[5, 6].includes(item.linkType)"
                  :label="item.typeName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-select v-model="ysChooseField" placeholder="请选择关联字段" clearable filterable style="margin-left: 10px">
                <el-option v-for="item in ysChooseFieldList" :key="item.fieldName" :label="item.fieldCn" :value="item.fieldName"> </el-option>
              </el-select>
            </div>
          </div>
        </div>

        <!-- 字段映射表格 -->
        <div class="title-label">7、映射字段</div>
        <el-table :data="localfields" style="width: 100%; overflow: auto" :row-style="{ height: '49px' }" height="275" border>
          <el-table-column label="属性组字段">
            <template #default="scope">
              <span v-if="scope.row.attribution.expression">【表达式字段】</span>
              <span :style="getLableColor(scope.row)">{{ scope.row.fieldCn }}</span>
              <span v-if="scope.row.fieldType == 'String'">(文本)</span>
              <span v-if="scope.row.fieldType == 'Long'">(整数)</span>
              <span v-if="scope.row.fieldType == 'Date'">(日期)</span>
              <span v-if="scope.row.fieldType == 'Double'">(小数)</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              <span>SHP字段</span>
              <el-link type="primary" @click="changeYS" style="margin-left: 10px">清除所有映射</el-link>
            </template>
            <template #default="scope">
              <el-select
                :disabled="scope.row.attribution.expression"
                v-model="scope.row.yz"
                placeholder="请选择"
                clearable
                filterable
                @change="changeShpField(scope.row)"
              >
                <el-option v-for="item in shpFields" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 数据上传进度 -->
    <el-dialog
      title="数据导入中"
      v-model="uploadLodingDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="300px"
    >
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="uploadProgress"></el-progress>
        <!-- <div style="margin-top: 10px">{{ uploadMsg }}</div> -->
      </div>
    </el-dialog>
    <!-- 上传前验证弹窗 -->
    <el-dialog
      title="上传前校验"
      :append-to-body="true"
      :modal-append-to-body="false"
      v-model="verificationDialog"
      :close-on-click-modal="false"
      @closed="closeVerification"
      width="650px"
    >
      <div class="dialog-row">校验进度：</div>
      <el-progress :text-inside="true" :stroke-width="26" :percentage="verificationPlan"></el-progress>
      <template v-if="verificationPlan >= 100">
        <div class="dialog-row" style="margin-top: 10px">总数据量：{{ excleList.length }} 条</div>
        <div class="dialog-row">校验成功：{{ verSuccNum }} 条</div>
        <div class="dialog-row">校验失败：{{ verErrorNum }} 条</div>
        <div class="dialog-row" v-show="uploadShpError.length != 0">
          <el-link type="primary" @click="downLoadErrorLog">下载错误日志</el-link>
          <span style="color: red" v-show="verErrorNum != 0">（请修正数据再上传！！！）</span>
        </div>
        <div class="error-div" v-show="uploadShpError.length != 0">
          <div class="item" v-for="(item, index) in uploadShpError" :key="index">
            {{ item }}
          </div>
        </div>
      </template>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="closeVerification">取 消</el-button>
          <el-button type="primary" @click="nextSubmit" :disabled="uploadShpNum != verSuccNum">继续上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import * as xlsx from 'xlsx';
import { getToken } from '@/utils/auth';
import { selectRules } from '@/api/modal';
import { updateInstance, updateInstanceCheck, saveSimple, findAsyncMsgUp as findAsyncMsgApi, updateInstanceAsync } from '@/api/project';
import { excelDateToJsDate } from '@/utils/validate';
import { useUserStore } from '@/store/modules/user';

// ---Props---
interface Props {
  // 打开弹框
  uploadExcelDialog: boolean;
  // 模块id
  moduleId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  uploadExcelDialog: false,
  moduleId: ''
});
const userStore = useUserStore();

const uploadExcelDialogCopy = computed(() => props.uploadExcelDialog);

watch(
  uploadExcelDialogCopy,
  (val) => {
    if (val) {
      getTree();
    }
  },
  { deep: true }
);

// ---定义变量---
const headers = { Authorization: 'Bearer ' + getToken() }; //请求头
const base = import.meta.env.VITE_APP_BASE_API; //基础路径
const fileList = ref([]);
const sortUrl = '/project/fund/excel/upload'; //上传接口
const isUpload = ref(false); //是否已经上传文件
const fileMsg: any = ref('');
const fullscreenLoading = ref(false);
const ruleTree = ref([]); //树节点
const nodeId = ref(''); //选择的节点id
const chooseNode: any = ref({}); //当前选择的节点
const fieldGroupModelList = ref([]); //节点下的属性组
const chooseGroup: any = ref({}); //选择的属性组
const groupId = ref(''); //选择属性组的id
const operaType = ref(1); //操作方式
const localfields = ref([]); //本地字段
const shpFields: any = ref([]); //表格表头
const excleList = ref([]); //excel数据
const ysChooseGroupId = ref(''); //映射属性组选择的id
const ysChooseGroup: any = ref({}); //映射的属性组
const ysChooseFieldList = ref([]); //映射选择的属性组对应的字段列表
const ysChooseField = ref(''); //映射的字段
const uploadLodingDialog = ref(false); //数据上传进度弹窗
const uploadProgress = ref(0); //上传进度
const uploadMsg = ref(''); //上传成功条数信息
const uploadShpError = ref([]); //上传失败的数据
const verificationDialog = ref(false); //上传前验证数据弹窗
const nextEndList = ref([]); //最终提交的数据列表
const verificationPlan = ref(0); //校验进度
const verSuccNum = ref(0); //验证成功条数
const verErrorNum = ref(0); //验证失败条数
const type = ref(1); // 1 表示增量；3表示覆盖
const typeList = ref([
  { label: '增加', value: 1, disable: false },
  { label: '修改', value: 20, disable: false },
  { label: '覆盖', value: 3, disable: false }
]);
const dialogHeight = ref(window.innerHeight - 300 + 'px'); //弹窗高度
const groupType = ref(1); //选择属性组的类型 1为普通属性组 2为多采属性组 普通属性组新增的话传2
const isRootNode = ref(2); //是否新增根节点
const currentParcelName = ref(''); //根节点新增 的时候节点名称
const errorMsg = ref(''); //错误信息
const uploadShpNum = ref(0); //上传的shp数量
const uploadStatus = ref();
const batchId = ref(''); //批次id
const uploadTemp = ref(); //上传组件实例
// 定义emit
const emit = defineEmits<{
  (e: 'closeExcelUpload'): void;
}>();

// ---定义方法---

const filterSize = (val: number) => {
  if (val) {
    return (val / 1024).toFixed(2);
  }
  return '';
};

const handleClose = () => {
  ruleTree.value = []; //树节点
  nodeId.value = ''; //选择的节点id
  chooseNode.value = {}; //当前选择的节点
  fieldGroupModelList.value = []; //节点下的属性组
  chooseGroup.value = {}; //选择的属性组
  groupId.value = ''; //选择属性组的id
  localfields.value = []; //本地字段
  shpFields.value = []; //表格表头
  excleList.value = []; //excel数据
  currentParcelName.value = '';

  // 清除选择的文件
  uploadTemp.value.clearFiles();
  fileList.value = [];
  fileMsg.value = null;
  type.value = 1;
  isUpload.value = false;
  emit('closeExcelUpload');
};

const uploadSuccess = (response, file, fileList) => {
  if (response.code == 200) {
    ElMessage.success('上传成功!');
  } else if (response.code == 500) {
    let errorStr = '';
    response.data.forEach((item) => {
      errorStr += `<strong>${item}</strong><br/>`;
    });
    ElMessage({
      dangerouslyUseHTMLString: true,
      message: errorStr,
      type: 'error'
    });
  } else {
    ElMessage.error(response.msg);
  }
};

const handleChangeFile = (file, fileList) => {
  fileMsg.value = file;
  if (fileList.length > 1) {
    fileList.splice(0, 1);
  }
  fileList.value = [file];
  isUpload.value = true;
  fullscreenLoading.value = true;
  readExcel({ 0: file.raw });
};

const readExcel = (files) => {
  if (files.length <= 0) {
    return false;
  } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
    ElMessage.error('上传格式不正确，请上传xls或者xlsx格式');
    fullscreenLoading.value = false;
    return false;
  }
  const fileReader = new FileReader();
  fileReader.onload = (files) => {
    try {
      const data = files.target.result;
      const workbook = xlsx.read(data, {
        type: 'binary'
      });
      const wsname = workbook.SheetNames[0]; // 取第一张表
      // 获取第一个工作表
      const worksheet = workbook.Sheets[wsname];
      // 将工作表转换为JSON对象
      const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
      // 获取表头
      const headers = jsonData[0];
      const ws = xlsx.utils.sheet_to_json(workbook.Sheets[wsname]); // 生成json表格内容
      fullscreenLoading.value = false;
      shpFields.value = headers;
      excleList.value = ws;
    } catch (e) {
      return false;
    }
  };
  fileReader.readAsBinaryString(files[0]);
};

const getTree = () => {
  selectRules({ moduleId: props.moduleId }).then((res) => {
    if (res.code == 200) {
      ruleTree.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleChange = (val) => {
  getChooseNode(ruleTree.value, val[val.length - 1]);
};

// 通过id迭代得到节点信息
const getChooseNode = (list, id) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      chooseNode.value = JSON.parse(JSON.stringify(list[i]));
      fieldGroupModelList.value = list[i].fieldGroupModelList;
      initDataTree([chooseNode.value]);
      break;
    }
    if (list[i].list.length != 0) {
      getChooseNode(list[i].list, id);
    }
  }
};

/**
 * 整理规则树结构为数据树结构
 * @param list 规则树结构
 */
const initDataTree = (list) => {
  list.forEach((v) => {
    v.ruleId = v.id;
    v.ruleName = v.parcelName;
    v.dataState = 0;
    v.appType = 2;
    v.parcelName = v.typeName;
    if (isRootNode.value == 1) {
      delete v.id;
    }
    // 根据app的情况，现在只需要上传有效数据 如果新增宗地就不需要同时上传空的房产等数据 2025.7.16 11：18
    v.list = [];
    // if (v.graphicalMaxNum == v.graphicalMinNum) {
    //   for (let index = 0; index < v.graphicalMinNum - 1; index++) {
    //     const item = JSON.parse(JSON.stringify(v));
    //     list.push(item);
    //   }
    // }
    // if (v.list && v.list.length != 0) {
    //   initDataTree(v.list);
    // }
  });
};

/**
 * 改变属性组
 * @param val 属性组id
 */
const changeGroup = async (val) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      chooseGroup.value = fieldGroupModelList.value[i];
      localfields.value = [];
      const SFZSBOptions = [
        { label: 0, text: '姓名', fieldType: 'String' },
        { label: 1, text: '性别', fieldType: 'String' },
        { label: 2, text: '民族', fieldType: 'String' },
        { label: 3, text: '出生日期', fieldType: 'Date' },
        { label: 4, text: '住址', fieldType: 'String' },
        { label: 5, text: '身份证号', fieldType: 'String' },
        { label: 6, text: '签发机关', fieldType: 'String' },
        { label: 7, text: '有效期限', fieldType: 'String' },
        { label: 8, text: '身份证正面', fieldType: 'Pic' },
        { label: 9, text: '身份证反面', fieldType: 'Pic' }
      ];
      fieldGroupModelList.value[i].fieldModelList.forEach((v) => {
        if (v.valueMethod == 'idCardScan') {
          if (v.attribution.expendList) {
            //新版
            v.attribution.list.forEach((k, kdx) => {
              localfields.value.push({
                fieldCn: v.attribution.expendList[kdx].cnName,
                fieldName: `${v.fieldName}_${k}`,
                valueMethod: v.attribution.expendList[kdx].valueMethod,
                attribution: {
                  options: v.attribution.expendList[kdx].options
                }
              });
            });
          } else {
            //老版
            v.attribution.list.forEach((k, kdx) => {
              localfields.value.push({
                fieldCn: SFZSBOptions[k].text,
                fieldName: `${v.fieldName}_${k}`,
                valueMethod: SFZSBOptions[k].fieldType
              });
            });
          }
        } else if (v.valueMethod == 'xtBankCard') {
          // 银行卡识别
          if (v.attribution.expendList) {
            v.attribution.list.forEach((k, kdx) => {
              localfields.value.push({
                fieldCn: v.attribution.expendList[kdx].cnName,
                fieldName: `${v.fieldName}_${k}`,
                valueMethod: v.attribution.expendList[kdx].valueMethod,
                attribution: {
                  options: v.attribution.expendList[kdx].options
                }
              });
            });
          }
        } else {
          localfields.value.push(v);
        }
      });
      await getLocalFild();
      break;
    }
  }
};

/**
 * 获取本地字段
 */
const getLocalFild = () => {
  localfields.value.forEach((f) => {
    for (let index = 0; index < shpFields.value.length; index++) {
      if (!f.attribution.expression && f.fieldName.toUpperCase() == shpFields.value[index].toUpperCase()) {
        f.yz = shpFields.value[index];
        break;
      }
    }
  });
};

/**
 * shp字段改变
 * @param val 字段
 */
const changeShpField = (val) => {};

/**
 * 选择映射关联字段属性组
 * @param val 属性组id
 */
const chooseYSGroup = (val) => {
  for (let i = 0; i < fieldGroupModelList.value.length; i++) {
    if (fieldGroupModelList.value[i].id == val) {
      ysChooseGroup.value = fieldGroupModelList.value[i];
      ysChooseFieldList.value = fieldGroupModelList.value[i].fieldModelList;
      break;
    }
  }
  // 手动增加id和parentId选项
  ysChooseFieldList.value.unshift({ fieldName: 'parentId', fieldCn: 'parentId(务必保证excel里存在parentId列)' });
  ysChooseFieldList.value.unshift({ fieldName: 'id', fieldCn: 'id(务必保证excel里存在id列)' });
};

/**
 * 提交
 */
const submit = async () => {
  batchId.value = `${userStore.user.userId}_${new Date().getTime()}`;
  if (!isUpload.value) {
    ElMessage.error('请选择excle！！！');
    return;
  }
  if (!nodeId.value) {
    ElMessage.error('请选择节点！！！');
    return;
  }
  if (!groupId.value) {
    ElMessage.error('请选择属性组！！！');
    return;
  }
  const params = [];
  let num = 1; //排序用
  // 需要判断是否是新增根数据 不是根数据的话调用的是修改属性组的接口 根数据的话调用的是shp更新接口
  if (isRootNode.value == 2) {
    //不是根
    excleList.value.forEach((v) => {
      const item = {
        appId: 0,
        attribution: {},
        groupId: chooseGroup.value.id,
        linkId: chooseGroup.value.linkId,
        ruleAttribution: chooseGroup.value.ruleAttribution,
        groupModel: {
          id: ysChooseGroup.value.id,
          linkId: ysChooseGroup.value.linkId,
          fieldModelList: [
            {
              fieldName: ysChooseField.value
            }
          ],
          ruleId: chooseNode.value.id
        },
        shpId: num,
        qdAttribution: {}
      };
      item.qdAttribution[ysChooseField.value] = v[ysChooseField.value] ? v[ysChooseField.value].trim() : v[ysChooseField.value];
      // 如果选择的关联字段是id或者parentId 则不需要linkid 和id 属性组
      if (ysChooseField.value === 'id' || ysChooseField.value === 'parentId') {
        item.groupModel.linkId = 0;
        item.groupModel.id = 0;
      }
      localfields.value.forEach((k) => {
        if (k.valueMethod == 'date') {
          //excel导入的日期需要处理
          item.attribution[k.fieldName] = excelDateToJsDate(v[k.yz]) || undefined;
        } else if (k.valueMethod == 'select' || k.valueMethod == 'radio') {
          // excel表里面可能是label也可能存的value 都要兼容
          let value = v[k.yz];
          for (let i = 0; i < k.attribution.options.length; i++) {
            if (k.attribution.options[i].label == v[k.yz]) {
              value = k.attribution.options[i].value;
              break;
            }
          }
          item.attribution[k.fieldName] = value;
        } else if (k.valueMethod == 'xtfj' || k.valueMethod == 'upload') {
          item.attribution[k.fieldName] = undefined;
        } else {
          item.attribution[k.fieldName] = v[k.yz] ? v[k.yz].trim() : v[k.yz];
        }
      });
      params.push(item);
      num++;
    });
  } else if (isRootNode.value == 1) {
    //新增根
    excleList.value.forEach((v) => {
      const item = { ...chooseNode.value };
      // 手动生成一个排序
      item.shpId = num;
      item.taskId = 0;
      const groupItem = {
        appId: 0,
        attribution: {},
        groupId: chooseGroup.value.id,
        groupName: chooseGroup.value.typeName,
        linkId: chooseGroup.value.linkId,
        ruleAttribution: chooseGroup.value.ruleAttribution
      };
      localfields.value.forEach((k) => {
        if (k.valueMethod == 'date') {
          //excel导入的日期需要处理
          groupItem.attribution[k.fieldName] = excelDateToJsDate(v[k.yz]);
        } else if (k.valueMethod == 'select' || k.valueMethod == 'radio') {
          // excel表里面可能是label也可能存的value 都要兼容
          let value = v[k.yz];
          for (let i = 0; i < k.attribution.options.length; i++) {
            if (k.attribution.options[i].label == v[k.yz]) {
              value = k.attribution.options[i].value;
              break;
            }
          }
          groupItem.attribution[k.fieldName] = value;
        } else if (k.valueMethod == 'xtfj' || k.valueMethod == 'upload') {
          groupItem.attribution[k.fieldName] = null;
        } else {
          groupItem.attribution[k.fieldName] = v[k.yz] ? v[k.yz].trim() : v[k.yz];
        }
      });
      // 配置名字
      if (currentParcelName.value) {
        item.parcelName = v[currentParcelName.value];
      }
      item.fieldInstanceModels = [groupItem];
      params.push(item);
      num++;
    });
  }
  uploadShpNum.value = params.length;
  // 初始化最终上传列表
  nextEndList.value = [];
  uploadShpError.value = [];
  verErrorNum.value = 0;
  verSuccNum.value = 0;

  if (isRootNode.value == 2) {
    //不是根需要验证
    const chunksData: any = await handleChunkData(params, 10);
    // 这里需要上传前验证 为了实现整批数据要么一起上传要么都不上传
    verificationDialog.value = true;
    verificationPlan.value = 0;
    // 依次上传子数组校验
    let isError = false; //是否验证错误
    for (let index = 0; index < chunksData.chunks.length; index++) {
      try {
        const resultNum = await verificationOnece(chunksData.chunks[index], index + 1, chunksData.count, chunksData.chunkSize);
        if (resultNum == 100) {
          // 当数据完成后关闭弹框
          // this.publicPlanDialog = false
          // this.handleOpenTipResult(chunksData.chunks)
        }
      } catch (error) {
        isError = true;
        ElMessage.error(error);
        // 处理错误，比如跳过当前子数组或中断整个上传过程
        continue;
      }
    }
    if (isError) {
      verificationDialog.value = false;
      ElMessageBox.alert('请检查您的excel里面的数据是否在数据库存在或者您是否映射正确！！！', '错误提示', {
        confirmButtonText: '确定',
        callback: (action) => {}
      });
    }
  } else if (isRootNode.value == 1) {
    //根 不需要验证
    nextEndList.value = params;
    nextSubmitOld();
  }
};

/**
 * 单次校验一批数据
 * @param list 数据
 * @param num 当前批次
 * @param count 总数
 * @param chunkSize 每批次大小
 */
const verificationOnece = (list, num, count, chunkSize) => {
  return new Promise((resolve, reject) => {
    let moreFlag = 0;
    if (operaType.value == 2) {
      //一对多 同时更新多条
      moreFlag = 1;
    }
    updateInstanceCheck(list, moreFlag, batchId.value, chunkSize).then((res) => {
      if (res.code == 200) {
        setTimeout(() => {
          if (res.data.length == 0) {
            //代表该批次数据都可以成功导入
            verSuccNum.value = verSuccNum.value + list.length;
            nextEndList.value.push(...list);
          } else {
            //代表失败一部分或者全部失败
            verErrorNum.value = verErrorNum.value + res.data.length;
            if (res.data.length < list.length) {
              //这个时候需要把失败的批次里面成功的数据提取出来
              // shpId
              const errNums = [];
              res.data.forEach((v) => {
                errNums.push(parseInt(v.substring(1, v.indexOf('行'))));
              });
              list.forEach((v) => {
                if (!errNums.some((obj) => obj == v.shpId)) {
                  nextEndList.value.push(v);
                  verSuccNum.value++;
                }
              });
            }
            uploadShpError.value.push(...res.data);
          }
          verificationPlan.value = Number(
            Number((((num * chunkSize) / count) * 100).toFixed(2)) > 100 ? 100 : (((num * chunkSize) / count) * 100).toFixed(2)
          );
          resolve(verificationPlan.value);
        }, 500);
      } else {
        reject(res.msg);
      }
    });
  });
};

const nextSubmit = () => {
  verificationDialog.value = false;
  // 初始化上传进度弹窗
  uploadStatus.value = '';
  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  uploadShpError.value = [];
  // 需要用batchId 来触发异步更新
  updateInstanceAsync(batchId.value, type.value, true).then((res) => {
    if (res.code === 200) {
      findAsyncMsg(res.data.id);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 查询异步消息
 */
const findAsyncMsg = (id: string) => {
  const parmas = {
    id: id
  };
  findAsyncMsgApi(parmas).then((res) => {
    if (res.code == 200) {
      if (!res.data) {
        ElMessageBox.alert(res.msg, {
          confirmButtonText: '确定',
          callback: (action: any) => {
            uploadLodingDialog.value = false;
          }
        });
        return;
      }
      if (parseFloat(res.data.progress) != 1) {
        uploadProgress.value = Number((res.data.progress * 100).toFixed(2));
        if (res.data.status == -1) {
          ElMessage.error(res.data.result);
          uploadLodingDialog.value = false;
        } else {
          setTimeout(() => {
            findAsyncMsg(id);
          }, 1000);
        }
      } else if (parseFloat(res.data.progress) == 1) {
        uploadProgress.value = Number((res.data.progress * 100).toFixed(2));
        setTimeout(() => {
          uploadLodingDialog.value = false;
          let num = 0;
          if (res.data.result) {
            num = res.data.result.data;
          }
          const str = `成功${type.value == 1 ? '新增' : '更新'}${num}条数据`;
          ElMessageBox.alert(str, `${type.value == 1 ? '新增' : '更新'}成功`, {
            confirmButtonText: '确定',
            callback: (action) => {
              // 在这添加是否切换公司的标识。
              // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
              // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
              sessionStorage.setItem('qiehuan_company', 'false');
              location.reload();
            }
          });
        }, 1000);
      }
    } else {
      ElMessage.error(res.msg);
      uploadLodingDialog.value = false;
    }
  });
};

/**
 * 老方法上传 用于根节点上传
 */
const nextSubmitOld = async () => {
  if (nextEndList.value.length == 0) {
    ElMessage.error('校验成功的数据为0，请修改后重新提交！！！');
    return;
  }
  verificationDialog.value = false;
  const chunkSize = 10;
  const chunks = [];

  // 拆分数组
  for (let i = 0; i < nextEndList.value.length; i += chunkSize) {
    chunks.push(nextEndList.value.slice(i, i + chunkSize));
  }

  // 初始化上传进度弹窗
  uploadStatus.value = '';
  uploadMsg.value = '已成功导入0条';
  uploadLodingDialog.value = true;
  uploadShpError.value = [];
  // 依次上传子数组
  for (let index = 0; index < chunks.length; index++) {
    try {
      await subsectionSubmit(chunks[index], index + 1);
    } catch (error) {
      console.error('上传失败:', error);
      // this.$message.error(error)
      // 处理错误，比如跳过当前子数组或中断整个上传过程
      continue;
    }
  }
  uploadStatus.value = 'success';
  uploadLodingDialog.value = false;
  handleClose();
  let str = `成功${operaType.value == 1 ? '新增' : '更新'}${nextEndList.value.length - uploadShpError.value.length}条数据`;
  if (errorMsg.value) {
    str = `更新失败，具体错误如下：<br/>${errorMsg.value}`;
  }
  operaType.value = null;
  ElMessageBox.alert(str, `${operaType.value == 1 ? '新增' : '更新'}成功`, {
    confirmButtonText: '确定',
    callback: (action) => {
      // 在这添加是否切换公司的标识。
      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
      sessionStorage.setItem('qiehuan_company', 'false');
      location.reload();
    }
  });
};

/**
 * excel导入分段提交
 * @param list 数据
 * @param num 当前批次
 */
const subsectionSubmit = (list, num) => {
  return new Promise((resolve, reject) => {
    if (isRootNode.value == 2) {
      //不是根
      let moreFlag = 0;
      if (operaType.value == 2) {
        //一对多 同时更新多条
        moreFlag = 1;
      }
      updateInstance(list, moreFlag, type.value.toString()).then((res) => {
        if (res.code == 200) {
          uploadProgress.value =
            Number((((num * 10) / uploadShpNum.value) * 100).toFixed(2)) > 100 ? 100 : Number((((num * 10) / uploadShpNum.value) * 100).toFixed(2));
          uploadMsg.value = `已成功导入${(num - 1) * 10 + list.length}条`;
          resolve(null);
        } else {
          uploadShpError.value.push(...list);
          errorMsg.value = res.msg;
          reject(res.msg);
        }
      });
    } else if (isRootNode.value == 1) {
      //根节点新增
      saveSimple(list).then((res) => {
        if (res.code == 200) {
          uploadProgress.value =
            Number((((num * 10) / uploadShpNum.value) * 100).toFixed(2)) > 100 ? 100 : Number((((num * 10) / uploadShpNum.value) * 100).toFixed(2));
          uploadMsg.value = `已成功导入${(num - 1) * 10 + list.length}条`;
          resolve(null);
        } else {
          const errorList = [];
          list.forEach((v) => {
            errorList.push(`第${v.shpId}条数据错误`);
          });
          uploadShpError.value.push(...errorList);
          reject(res.msg);
        }
      });
    }
  });
};

/**
 * 公共方法抽取数据 分段截取数据
 * @param list 数据
 * @param num 每批次大小
 */
const handleChunkData = async (list, num) => {
  const count = list.length;
  const chunkSize = num || 10;
  const chunks = [];
  // 拆分数组
  for (let i = 0; i < list.length; i += chunkSize) {
    // 这里创建二维数组内容，分 10 个 截成一个二维数组
    chunks.push(list.slice(i, i + chunkSize));
  }
  const resultItem = {
    count: count,
    chunkSize: chunkSize,
    chunks: chunks
  };
  return new Promise((resolve) => {
    resolve(resultItem);
  });
};

const closeVerification = () => {
  verificationDialog.value = false;
};

/**
 * 下载错误日志
 */
const downLoadErrorLog = () => {
  //下载txt文件
  const element = document.createElement('a');
  const endContent = uploadShpError.value.join('\n');
  element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(endContent));
  element.setAttribute('download', 'shp上传错误日志');
  element.style.display = 'none';
  element.click();
  document.body.removeChild(element); //下载完成移除元素
};

const changeYS = () => {
  ElMessageBox.confirm('确定要清除所有映射吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      localfields.value.forEach((v) => {
        v.yz = '';
      });
    })
    .catch(() => {});
};

/**
 * 通过是否映射返回相应的颜色，如果没有映射返回红色
 * @param row 当前行的数据
 * @returns {string} 返回颜色
 */
const getLableColor = (row: any) => {
  let color = '#606266';
  if (row.yz === '' || row.yz === undefined) {
    //代表没有映射
    color = 'red';
  }
  return {
    color: color
  };
};
</script>
<style lang="scss" scoped>
.dialog-row {
  margin-bottom: 10px;
}
.error-div {
  height: 400px;
  overflow: auto;
  .item {
    margin-bottom: 5px;
  }
}
.error-content {
  .flex-row {
    margin-bottom: 10px;
  }
  .error-div {
    height: 400px;
    overflow: auto;
    .item {
      margin-bottom: 5px;
    }
  }
}
.updateExcel-main {
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.dialog-box {
  overflow: auto;
  .title-label {
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 15px;

    .flex-item {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
