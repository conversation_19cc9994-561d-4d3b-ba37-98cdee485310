import nodeConfig from './config.ts';

interface Node {
  nodeId: string;
  prevId?: string;
  type: string;
  childNode?: Node;
  conditionNodes?: Node[];
  properties?: {
    priority?: number;
    initiator?: any[];
    conditions?: any[];
    approvals?: any[];
    actionRuleType?: string;
  };
  isdefault?: boolean;
  content?: string;
}

const isEmpty = (data: any): boolean => data === null || data === undefined || data === '';

const isEmptyArray = (data: any): boolean => (Array.isArray(data) ? data.length === 0 : true);

export class NodeUtils {
  static globalID = 10000;

  /**
   * 获取最大的节点ID 转换成10进制
   * @param data - 整个流程数据
   */
  static getMaxNodeId(data: Node): number {
    let max = data.nodeId;
    const loop = (node: Node) => {
      if (!node) return;
      max < node.nodeId && (max = node.nodeId);
      node.childNode && loop(node.childNode);
      Array.isArray(node.conditionNodes) && node.conditionNodes.forEach((c) => loop(c));
    };
    loop(data);
    const chars = '0123456789ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz';
    const len = chars.length;
    return max.split('').reduce((sum, c, i) => {
      return sum + chars.indexOf(c) * Math.pow(len, i);
    }, 0);
  }

  /**
   * 根据自增数生成64进制id
   * @returns 64进制id字符串
   */
  static idGenerator(): string {
    let qutient = ++this.globalID;
    const chars = '0123456789ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz';
    const charArr = chars.split('');
    const radix = chars.length;
    const res: string[] = [];
    do {
      const mod = qutient % radix;
      qutient = (qutient - mod) / radix;
      res.push(charArr[mod]);
    } while (qutient);
    return res.join('');
  }

  /**
   * 判断节点类型
   * @param node - 节点数据
   * @returns Boolean
   */
  static isConditionNode(node: Node): boolean {
    return node && node.type === 'condition';
  }

  static isCopyNode(node: Node): boolean {
    return node && node.type === 'notifier';
  }

  static isAuditNode(node: Node): boolean {
    return node && node.type === 'audit';
  }

  static isStartNode(node: Node): boolean {
    return node && node.type === 'start';
  }

  static isApproverNode(node: Node): boolean {
    return node && node.type === 'approver';
  }

  static isRouteNode(node: Node): boolean {
    return node && node.type === 'route';
  }

  /**
   * 生成随机的uuid
   * @param len - 生成的长度
   * @param radix - 基数
   */
  static uuid(len?: number, radix?: number): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid: string[] = [];
    let i: number;
    radix = radix || chars.length;

    if (len) {
      for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
    } else {
      let r: number;
      uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
      uuid[14] = '4';

      for (i = 0; i < 36; i++) {
        if (!uuid[i]) {
          r = 0 | (Math.random() * 16);
          uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
        }
      }
    }
    return uuid.join('');
  }

  /**
   * 创建指定节点
   * @param type - 节点类型
   * @param previousNodeId - 父节点id
   * @returns 节点数据
   */
  static createNode(type: string, previousNodeId?: string): Node {
    const res = JSON.parse(JSON.stringify(nodeConfig[type]));
    res.nodeId = this.uuid(16, 16);
    res.prevId = previousNodeId;
    return res;
  }

  /**
   * 获取指定节点的父节点（前一个节点）
   * @param prevId - 父节点id
   * @param processData - 流程图全部数据
   * @returns 父节点
   */
  static getPreviousNode(prevId: string, processData: Node): Node | undefined {
    if (processData.nodeId === prevId) return processData;
    if (processData.childNode) {
      const r1 = this.getPreviousNode(prevId, processData.childNode);
      if (r1) {
        return r1;
      }
    }
    if (processData.conditionNodes) {
      for (const c of processData.conditionNodes) {
        const r2 = this.getPreviousNode(prevId, c);
        if (r2) {
          return r2;
        }
      }
    }
  }

  /**
   * 删除节点
   * @param nodeData - 被删除节点的数据
   * @param processData - 流程图的所有节点数据
   * @param checkEmpty - 是否检查空节点
   */
  static deleteNode(nodeData: Node, processData: Node, checkEmpty = true): void {
    const prevNode = this.getPreviousNode(nodeData.prevId!, processData);
    if (!prevNode) return;

    if (checkEmpty && prevNode.type === 'empty') {
      if (this.isConditionNode(nodeData)) {
        this.deleteNode(prevNode, processData);
      } else {
        if (isEmptyArray(prevNode.conditionNodes)) {
          this.deleteNode(prevNode, processData);
        }
        this.deleteNode(nodeData, processData, false);
      }
      return;
    }

    const concatChild = (prev: Node, delNode: Node) => {
      prev.childNode = delNode.childNode;
      if (isEmptyArray(prev.conditionNodes)) {
        prev.conditionNodes = delNode.conditionNodes;
      }
      if (isEmptyArray(prev.conditions)) {
        prev.conditions = delNode.conditions;
      }
      if (prev.childNode) {
        prev.childNode.prevId = prev.nodeId;
      }
      if (prev.conditionNodes) {
        prev.conditionNodes.forEach((c) => (c.prevId = prev.nodeId));
      }
    };

    if (this.isConditionNode(nodeData)) {
      const cons = prevNode.conditionNodes!;
      const index = cons.findIndex((c) => c.nodeId === nodeData.nodeId);

      if (cons.length > 2) {
        cons.splice(index, 1);
      } else {
        const anotherCon = index >= 0 ? cons[index] : cons[0];
        delete prevNode.conditionNodes;

        if (prevNode.childNode) {
          let endNode = anotherCon;
          while (endNode.childNode) {
            endNode = endNode.childNode;
          }
          endNode.childNode = prevNode.childNode;
          endNode.childNode!.prevId = endNode.nodeId;
        }
        concatChild(prevNode, anotherCon);
      }

      cons.forEach((c, i) => (c.properties!.priority = i));
      return;
    }

    concatChild(prevNode, nodeData);
  }

  /**
   * 添加节点（普通节点 approver）
   * @param data - 目标节点数据，在该数据节点之后添加审计节点
   * @param isBranchAction - 目标节点数据，是否是条件分支
   * @param newChildNode - 传入的新的节点 用户操作均为空  删除操作/添加抄送人 会传入该参数 以模拟添加节点
   */
  static addApprovalNode(data: Node, isBranchAction: boolean, newChildNode?: Node): void {
    const oldChildNode = data.childNode;
    newChildNode = newChildNode || this.createNode('approver', data.nodeId);
    data.childNode = newChildNode;
    if (oldChildNode) {
      newChildNode.childNode = oldChildNode;
      oldChildNode.prevId = newChildNode.nodeId;
    }
    const conditionNodes = data.conditionNodes;
    if (Array.isArray(conditionNodes) && !isBranchAction && conditionNodes.length) {
      newChildNode.conditionNodes = conditionNodes.map((c) => {
        c.prevId = newChildNode!.nodeId;
        return c;
      });
      delete data.conditionNodes;
    }
    if (oldChildNode && (oldChildNode.type === 'empty' || oldChildNode.type === 'route')) {
      this.deleteNode(oldChildNode, data);
    }
  }

  /**
   * 添加空节点
   * @param data - 空节点的父级节点
   * @return emptyNode - 空节点数据
   */
  static addEmptyNode(data: Node): Node {
    const emptyNode = this.createNode('empty', data.nodeId);
    this.addApprovalNode(data, true, emptyNode);
    return emptyNode;
  }

  static addCopyNode(data: Node, isBranchAction: boolean): void {
    this.addApprovalNode(data, isBranchAction, this.createNode('notifier', data.nodeId));
  }

  static addAuditNode(data: Node, isBranchAction: boolean): void {
    this.addApprovalNode(data, isBranchAction, this.createNode('audit', data.nodeId));
  }

  /**
   * 创建一个条件分支的节点内容
   */
  static addConditionNode(data: Node, isBranchAction: boolean): Node {
    const routeNode = this.createNode('route', data.nodeId);
    this.addApprovalNode(data, true, routeNode);
    return routeNode;
  }

  /**
   * 添加条件节点 condition 通过点击添加条件进入该操作
   * @param data - 目标节点所在分支数据，在该分支最后添加条件节点
   */
  static appendConditionNode(data: Node): void {
    const conditions = data.conditionNodes!;
    const node = this.createNode('condition', data.nodeId);
    const defaultNodeIndex = conditions.findIndex((ite) => ite.isdefault);
    node.properties!.priority = conditions.length;
    if (defaultNodeIndex > -1) {
      conditions.splice(-1, 0, node);
      node.properties!.priority = conditions.length - 2;
      conditions[conditions.length - 1].properties!.priority = conditions.length - 1;
    } else {
      conditions.push(node);
    }
  }

  /**
   * 添加条件分支 branch
   * @param data - 目标节点所在节点数据，在该节点最后添加分支节点
   * @param isBottomBtnOfBranch - 是否是目标分支的节点
   */
  static appendBranch(data: Node, isBottomBtnOfBranch: boolean): void {
    let nodeData = data;
    if (Array.isArray(data.conditionNodes) && data.conditionNodes.length > 0) {
      if (isBottomBtnOfBranch) {
        nodeData = this.addConditionNode(nodeData, true);
      } else {
        const emptyNode = this.addConditionNode(nodeData, true);
        emptyNode.conditionNodes = nodeData.conditionNodes;
        emptyNode.conditionNodes!.forEach((n) => {
          n.prevId = emptyNode.nodeId;
        });
      }
    } else {
      nodeData = this.addConditionNode(data, true);
    }
    const conditionNodes = [this.createNode('condition', nodeData.nodeId), this.createNode('defaultRoute', nodeData.nodeId)].map((c, i) => {
      if (c.properties) {
        c.properties.priority = i;
      }
      return c;
    });

    nodeData.conditionNodes = conditionNodes;
  }

  /**
   * 重设节点优先级（条件节点）
   * @param cnode - 当前节点
   * @param oldPriority - 替换前的优先级（在数组中的顺序）
   * @param processData - 整个流程图节点数据
   */
  static resortPrioByCNode(cnode: Node, oldPriority: number, processData: Node): void {
    if (cnode.isdefault) {
      cnode.properties!.priority = oldPriority;
      return;
    }
    const prevNode = this.getPreviousNode(cnode.prevId!, processData);
    if (!prevNode) return;
    const newPriority = cnode.properties!.priority!;
    if (prevNode.conditionNodes![newPriority].isdefault) {
      cnode.properties!.priority = oldPriority;
      return;
    }
    const delNode = prevNode.conditionNodes!.splice(newPriority, 1, cnode)[0];
    delNode.properties!.priority = oldPriority;
    prevNode.conditionNodes![oldPriority] = delNode;
  }

  /**
   * 提升条件节点优先级——排序在前
   * @param data - 目标节点数据
   * @param processData - 流程图的所有节点数据
   */
  static increasePriority(data: Node, processData: Node): void {
    if (data.isdefault) {
      return;
    }
    const prevNode = this.getPreviousNode(data.prevId!, processData);
    if (!prevNode) return;
    const branchData = prevNode.conditionNodes!;
    const index = branchData.findIndex((c) => c === data);
    if (index) {
      branchData[index - 1].properties!.priority = index;
      branchData[index].properties!.priority = index - 1;
      branchData[index - 1] = branchData.splice(index, 1, branchData[index - 1])[0];
    }
  }

  /**
   * 降低条件节点优先级——排序在后
   * @param data - 目标节点数据
   * @param processData - 流程图的所有节点数据
   */
  static decreasePriority(data: Node, processData: Node): void {
    const prevNode = this.getPreviousNode(data.prevId!, processData);
    if (!prevNode) return;
    const branchData = prevNode.conditionNodes!;
    const index = branchData.findIndex((c) => c.nodeId === data.nodeId);
    if (index < branchData.length - 1) {
      const lastNode = branchData[index + 1];
      if (lastNode.isdefault) {
        return;
      }
      lastNode.properties!.priority = index;
      branchData[index].properties!.priority = index + 1;
      branchData[index + 1] = branchData.splice(index, 1, branchData[index + 1])[0];
    }
  }

  /**
   * 当有其他条件节点设置条件后 检查并设置最后一个节点为默认节点
   * @param cnode - 当前节点
   * @param processData - 整个流程图节点数据或父级节点数据
   */
  static setDefaultCondition(cnode: Node, processData: Node): void {
    const DEFAULT_TEXT = '其他情况进入此流程';
    const conditions = this.getPreviousNode(cnode.prevId!, processData)!.conditionNodes!;
    const hasCondition = (node: Node) => node.properties && (node.properties.initiator || !isEmptyArray(node.properties.conditions));
    const clearDefault = (node: Node) => {
      node.isdefault = false;
      node.content === DEFAULT_TEXT && (node.content = '请设置条件');
    };
    const setDefault = (node: Node) => {
      node.isdefault = true;
      node.content = DEFAULT_TEXT;
    };
    let count = 0;
    conditions.slice(0, -1).forEach((node) => {
      hasCondition(node) && count++;
      clearDefault(node);
    });
    const lastNode = conditions[conditions.length - 1];
    count > 0 && !hasCondition(lastNode) ? setDefault(lastNode) : clearDefault(lastNode);
  }

  /**
   * 校验单个节点必填项完整性
   * @param node - 节点数据
   */
  static checkNode(node: Node, parent?: Node): boolean {
    let valid = true;
    const props = node.properties || {};
    if (this.isStartNode(node) && !props.initiator) {
      valid = false;
    }
    if (this.isRouteNode(node)) {
      valid = true;
    }
    if (this.isConditionNode(node)) {
      if (node.isdefault) {
        valid = true;
      } else if (!node.isdefault && isEmptyArray(props.conditions)) {
        valid = false;
      }
    }

    const customSettings = ['target_select', 'target_approval'];
    if (this.isApproverNode(node)) {
      if (props.actionRuleType == 'target_originator') {
        valid = true;
      } else if ('target_approval' === props.actionRuleType) {
        if (isEmptyArray(props.approvals)) {
          valid = false;
        } else {
          valid = true;
        }
      } else {
        valid = true;
      }
    }

    return valid;
  }

  /**
   * 判断所有节点是否信息完整
   * @param processData - 整个流程图数据
   * @returns Boolean
   */
  static checkAllNode(processData: Node): boolean {
    let valid = true;
    const loop = (node: Node, callback: () => void, parent?: Node) => {
      !this.checkNode(node, parent) && callback();
      if (node.childNode) loop(node.childNode, callback, parent);
      if (!isEmptyArray(node.conditionNodes)) {
        node.conditionNodes!.forEach((n) => loop(n, callback, node));
      }
    };
    loop(processData, () => (valid = false));
    return valid;
  }
}

/**
 * 获取模拟数据
 */
export function getMockData(): Node {
  const startNode = NodeUtils.createNode('start');
  startNode.childNode = NodeUtils.createNode('approver', startNode.nodeId);

  return startNode;
}
