const dataText = JSON.stringify([
  //生成毕节各个区县的数据
  { name: '毕节市', lng: 105.28, lat: 27.27, status: '已完成' },
  { name: '七星关区', lng: 104.51, lat: 27.03, status: '未完成' },
  { name: '大方县', lng: 105.47, lat: 27.15, status: '已完成' },
  { name: '黔西县', lng: 106.03, lat: 27.02, status: '未完成' },
  { name: '金沙县', lng: 106.22, lat: 27.46, status: '未完成' },
  { name: '织金县', lng: 105.7, lat: 26.65, status: '已完成' },
  { name: '赫章县', lng: 104.72, lat: 27.12, status: '未完成' },
  { name: '威宁彝族回族苗族自治县', lng: 104.28, lat: 26.85, status: '未完成' }
  //七星关区各个乡镇数据
]);

export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: dataText
  },
  attribute: {
    // 标题配置
    titleText: '全国数据分布散点图',
    subtext: '数据可视化',
    titleLeft: 'center',
    titleTop: 10,
    titleFontSize: 18,
    titleColor: '#333',
    subTitleColor: '#666',
    subTitleFontSize: 12,

    // 地图配置
    map: 'china', // 地图类型
    roam: true, // 允许缩放和平移
    zoom: 0.2, // 初始缩放比例
    center: [104, 35], // 地图中心点 [经度, 纬度]

    // 地图样式
    showMapLabel: true, // 显示地图标签
    mapLabelSize: 12,
    mapLabelColor: '#333',
    mapAreaColor: '#f0f0f0', // 地图区域颜色
    mapBorderColor: '#999', // 地图边界颜色
    mapBorderWidth: 1,

    // 地图高亮样式
    showMapEmphasisLabel: true,
    mapEmphasisLabelColor: '#000',
    mapEmphasisAreaColor: '#e0e0e0',
    mapEmphasisBorderColor: '#666',

    // 散点配置
    seriesName: '数据点',
    scatterType: 'scatter', // 'scatter' 或 'effectScatter'
    scatterSymbol: 'circle', // 散点符号
    scatterBaseSize: 8, // 基础大小
    scatterSizeMultiplier: 0.5, // 大小倍数
    scatterColor: '#ff6b6b', // 散点颜色
    scatterOpacity: 0.8, // 散点透明度
    scatterBorderWidth: 1,
    scatterBorderColor: '#fff',

    // 自定义符号配置
    customImageUrl: '', // 自定义图片URL
    customImageSize: 30, // 自定义图片大小
    customPath: 'M0,0 L10,10 L20,0 Z', // 自定义SVG路径
    customPathSize: 20, // 自定义路径大小
    customPathColor: '#ff6b6b', // 自定义路径颜色

    // 双色状态配置
    enableStatusColors: false, // 启用状态颜色
    statusField: 'status', // 状态字段名
    completedColor: '#52c41a', // 已完成颜色
    uncompletedColor: '#ff4d4f', // 未完成颜色
    completedLabel: '已完成', // 已完成标签
    uncompletedLabel: '未完成', // 未完成标签
    showLegend: true, // 显示图例
    legendTextColor: '#333', // 图例文字颜色
    legendTextSize: 12, // 图例文字大小

    // 状态符号配置
    enableStatusSymbols: false, // 启用状态符号
    completedSymbol: 'circle', // 已完成符号
    uncompletedSymbol: 'triangle', // 未完成符号

    // 已完成自定义符号
    completedCustomImageUrl: '', // 已完成自定义图片URL
    completedCustomImageSize: 30, // 已完成自定义图片大小
    completedCustomPath: 'M0,0 L10,10 L20,0 Z', // 已完成自定义SVG路径
    completedCustomPathSize: 20, // 已完成自定义路径大小
    completedCustomPathColor: '#52c41a', // 已完成自定义路径颜色

    // 未完成自定义符号
    uncompletedCustomImageUrl: '', // 未完成自定义图片URL
    uncompletedCustomImageSize: 30, // 未完成自定义图片大小
    uncompletedCustomPath: 'M10,0 L20,10 L10,20 L0,10 Z', // 未完成自定义SVG路径
    uncompletedCustomPathSize: 20, // 未完成自定义路径大小
    uncompletedCustomPathColor: '#ff4d4f', // 未完成自定义路径颜色

    // 散点高亮样式
    scatterEmphasisColor: '#ff4757',
    scatterEmphasisShadowBlur: 10,
    scatterEmphasisShadowColor: '#ff4757',

    // 散点标签
    showScatterLabel: true,
    scatterLabelPosition: 'top',
    scatterLabelColor: '#333',
    scatterLabelSize: 12,
    scatterLabelFormat: 'name', // 'name', 'value', 'both'

    // 涟漪效果（仅当 scatterType 为 'effectScatter' 时生效）
    rippleBrushType: 'stroke', // 'stroke' 或 'fill'
    rippleScale: 2.5,
    ripplePeriod: 4,

    // 视觉映射
    showVisualMap: false,
    visualMapMin: 0,
    visualMapMax: 200,
    visualMapLeft: 'left',
    visualMapTop: 'bottom',
    visualMapMaxText: '高',
    visualMapMinText: '低',
    visualMapTextColor: '#333',
    visualMapMinColor: '#50a3ba',
    visualMapMaxColor: '#eac736',

    // 区域颜色配置
    regionColors: {}, // 存储每个区域的自定义颜色
    regionValues: {} // 存储每个区域的数值（可选）
  }
};
