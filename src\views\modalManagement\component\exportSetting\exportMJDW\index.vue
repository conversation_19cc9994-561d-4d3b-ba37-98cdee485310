<!-- 导出面积单位 -->
<template>
  <div class="exportMJDW-main">
    <div class="title">导出面积单位</div>
    <div class="normal-title">该项是否显示</div>
    <el-select v-model="mjdw.disable" placeholder="请选择" style="width: 100%">
      <el-option label="显示" :value="0"></el-option>
      <el-option label="不显示" :value="1"></el-option>
    </el-select>
    <div class="normal-title">提示文字</div>
    <el-input v-model="mjdw.placeholder" placeholder="请输入提示文字" maxlength="10"></el-input>
    <div class="normal-title">选项</div>
    <div v-for="(item,index) in option" :key="index" class="option-row">
      <div class="left">{{item.label}}</div>
      <div class="right">
        <img src="@/assets/images/openeye_ico.png" class="ico" v-show="item.delFlag == 0" @click="changeShow(item)">
        <img src="@/assets/images/closeeye_ico.png" class="ico" v-show="item.delFlag == 1" @click="changeShow(item)">
        <div class="radio-box">
          <div class="radio-checked" v-if="item.checked" @click="changeDef(item)"></div>
          <div class="radio-no" v-else @click="changeDef(item)"></div>
        </div>
      </div>
    </div>
    <div class="normal-title">是否必填</div>
    <el-select v-model="mjdw.must" placeholder="请选择" style="width: 100%">
      <el-option label="必填" :value="1"></el-option>
      <el-option label="不必填" :value="0"></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  data () {
    return {
      option:[
        {label:'平方米',value:1,checked:false,delFlag:0},
        {label:'平方千米',value:2,checked:false,delFlag:0},
        {label:'公顷',value:3,checked:false,delFlag:0},
        {label:'亩',value:4,checked:false,delFlag:0},
      ]
    };
  },
  props:['mjdw'],

  components: {},

  computed: {},

  mounted() {
    if (this.mjdw.defaultValue == 1) {
      this.option[0].checked = true
    } else if (this.mjdw.defaultValue == 2) {
      this.option[1].checked = true
    } else if (this.mjdw.defaultValue == 3) {
      this.option[2].checked = true
    } else if (this.mjdw.defaultValue == 4) {
      this.option[3].checked = true
    }
  },

  methods: {
    changeShow(item){
      if (item.delFlag == 0) {
        item.delFlag = 1
      } else {
        item.delFlag = 0
      }
    },
    // 改变默认
    changeDef(item){
      this.option.forEach(v=>{
        v.checked = false
      })
      item.checked = true
    }
  }
}

</script>
<style lang='scss' scoped>
.exportMJDW-main{
  width: 100%;
  height: 100%;
  padding: 20px 16px;
  color: #161D26;
  .title{
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .normal-title{
    font-size: 14px;
    margin: 16px 0px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-btn{
      color: var(--current-color);
      cursor: pointer;
    }
  }
  .option-row{
    height: 40px;
    margin-bottom: 4px;
    border: 1px solid rgba(219, 231, 238, 1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left{
      margin-left: 16px;
    }
    .right{
      display: flex;
      align-items: center;
      margin-right: 16px;
      .ico{
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      .radio-box{
        margin-left: 8px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .radio-checked{
          background: var(--current-color);
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
        .radio-no{
          width: 10px;
          height: 10px;
          border-radius: 50%;
          border: 1px solid rgba(130, 145, 169, 1);
        }
      }
    }
  }
}
</style>