<template>
  <!-- 生成分享 -->
  <div>
    <el-dialog
      title="分享码"
      width="400px"
      height="400px"
      v-model="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="handleClose"
      @opened="handleCreateQRCode"
    >
      <div class="qr-code">
        <div class="qr-code-contianer">
          <div class="qr-code-main" id="qrcodeDom" v-if="qrCodeUrl">
            <img :src="qrCodeUrl" alt="生成的二维码" style="margin-top: 20px; border: 1px solid #eee" />
          </div>
        </div>
        <div style="margin-top: 16px">
          <!-- <el-button type="primary" size="small" plain @click="copyImg">截图</el-button> -->
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue';
// @ts-ignore
import QRCode from 'qrcode';
// @ts-ignore
import html2canvas from 'html2canvas';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';

// 用户数据接口
interface UserData {
  userId?: string | number;
  [key: string]: any;
}

const props = defineProps({
  visibleDialog: {
    required: true,
    type: Boolean,
    default: false
  }
});
const text = ref('https://vuejs.org/'); // 默认内容
const emit = defineEmits(['closeCode']);

// 创建响应式变量
const qrCodeRef = ref<HTMLElement | null>(null);
const qrCodeUrl = ref<string>('');
const qrcodeImg = ref('');
const dialogVisible = computed({
  get: () => props.visibleDialog,
  set: () => {
    handleClose();
  }
});
// 定义类型
interface QRCodeOptions {
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  margin?: number;
  scale?: number;
  color?: {
    dark?: string;
    light?: string;
  };
}
// 获取用户信息
const userStore = useUserStore();
const user = computed<UserData>(() => userStore.user || {});

// 生命周期钩子
onMounted(() => {
  handleCreateQRCode();
});

// 创建二维码方法
const handleCreateQRCode = async () => {
  const userId = user.value.userId;
  const url = `https://qjt.smgis.com/download?userId=${userId}`;
  // const url = `http://192.168.31.250:88/download?userId=${userId}`;
  // if (url.trim()) return;

  const options: QRCodeOptions = {
    errorCorrectionLevel: 'H', // 容错级别
    margin: 2, // 边距
    scale: 8, // 缩放比例
    color: {
      dark: '#000000', // 二维码颜色
      light: '#ffffff' // 背景色
    }
  };

  try {
    // 生成DataURL格式的二维码图片
    qrCodeUrl.value = await QRCode.toDataURL(url, options);
  } catch (err) {
    qrCodeUrl.value = '';
  }
};
// 再关闭之前要先清除之前生成二维码
const handleClose = () => {
  if (qrCodeRef.value) {
    qrCodeRef.value.innerHTML = '';
  }
  emit('closeCode');
};

// 复制图片
const copyImg = async () => {
  const domElement = document.getElementById('qrcodeDom');
  if (!domElement) {
    ElMessage.error('获取DOM元素失败');
    return;
  }

  const canvas = await html2canvas(domElement);
  qrcodeImg.value = canvas.toDataURL();
  const data = await fetch(qrcodeImg.value);
  const blob = await data.blob();

  // ClipboardItem API may not be available in all browsers
  try {
    // @ts-ignore
    await navigator.clipboard.write([
      new ClipboardItem({
        [blob.type]: blob
      })
    ]);
    ElMessage.success('截图成功');
  } catch (error) {
    ElMessage.error('复制失败，您的浏览器可能不支持此功能');
  }
};
</script>

<style lang="scss" scoped>
.qr-code {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .qr-code-contianer {
    .qr-code-main {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
