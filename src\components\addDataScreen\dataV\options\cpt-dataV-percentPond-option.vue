<template>
  <el-form label-width="100px">
    <el-form-item label="边框粗细">
      <el-input-number :min="1" :max="100" v-model="attributeCopy.borderWidth" />
    </el-form-item>
    <el-form-item label="边框圆角">
      <el-input-number :min="0" :max="100" v-model="attributeCopy.borderRadius" />
    </el-form-item>
    <el-form-item label="内边距">
      <el-input-number :min="0" :max="100" v-model="attributeCopy.borderGap" />
    </el-form-item>
    <el-form-item label="局部渐变">
      <el-switch v-model="attributeCopy.localGradient" />
    </el-form-item>
    <el-form-item label="线条粗细">
      <el-input-number :min="0" :max="50" v-model="attributeCopy.lineWidth" />
    </el-form-item>
    <el-form-item label="线条间隔">
      <el-input-number :min="0" :max="20" v-model="attributeCopy.lineSpace" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-dataV-percentPond-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();
const attributeCopy = computed(() => props.attribute);
</script>

<style scoped></style>
