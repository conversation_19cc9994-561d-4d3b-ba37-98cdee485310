<!-- 论坛审核列表----仅仅管理员端可见  不能删除 -->
<template>
  <container-card>
    <el-select v-model="searchCheck" placeholder="请选择" @change="handleChangeChecked" style="width: 200px; margin-bottom: 10px">
      <el-option v-for="item in checkOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
    </el-select>
    <el-row>
      <el-table :data="forumCheckList || []" style="width: 100%" v-loading="loading" align="center">
        <el-table-column prop="createTime" label="评论时间" width="150" align="center">
          <template v-slot="{ row }">
            {{ row.createTime ? formatDateYmdhm(row.createTime) : '无' }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="评论人" align="center">
          <template v-slot="{ row }">
            {{ row.createBy || '无' }}
          </template>
        </el-table-column>
        <el-table-column prop="contents" label="评论内容" width="150" align="center">
          <template v-slot="{ row }">
            <span :title="row.contents || '无'">{{ row.contents ? spanRule(row.contents, 6) : '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="quoteName" label="引用人" align="center">
          <template v-slot="{ row }">
            {{ row.quoteName || '无' }}
          </template>
        </el-table-column>
        <el-table-column prop="quoteContents" label="引用信息" width="150" align="center">
          <template v-slot="{ row }">
            <span :title="row.quoteContents || '无'">{{ row.quoteContents ? spanRule(row.quoteContents, 8) : '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="titleInfo" label="主题" width="150" align="center">
          <template v-slot="{ row }">
            <span :title="row.titleInfo || '无'">{{ row.titleInfo ? spanRule(row.titleInfo, 8) : '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="seq" label="是否主题" align="center">
          <template v-slot="{ row }">
            {{ row.seq == 0 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="{ row }">
            <el-button link type="primary" v-if="searchCheck == 0" @click="handleEditForum(row, 1)">同意</el-button>
            <el-button link type="info" v-if="searchCheck == 0" @click="handleEditForum(row, 2)">拒绝</el-button>
            <el-button link type="danger" v-if="searchCheck !== 2" @click="handleEditForum(row, 'delFlag')">删除</el-button>
            <el-button link type="text" v-if="searchCheck == 2" @click="handleEditForum(row, 1)">同意</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="query.pageNum" v-model:limit="query.pageSize" @pagination="getForumTableList" />
    </el-row>
    <el-dialog title="邀请客户" v-model="clientDialogFormVisible" :close-on-click-modal="false">
      <el-form :model="addClientForm">
        <el-form-item label="客户姓名" label-width="120px">
          <el-input v-model="addClientForm.custName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="客户手机号" label-width="120px">
          <el-input v-model="addClientForm.custPhone" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button @click="clientDialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitClientForm">确 定</el-button>
      </template>
    </el-dialog>
  </container-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { getForumCheckList, editForum } from '@/api/forum';
import { ElMessageBox } from 'element-plus';

interface ForumCheck {
  id: number;
  titleId: number;
  createTime: string;
  createBy: string;
  contents: string;
  quoteName: string;
  quoteContents: string;
  titleInfo: string;
  seq: number;
}

interface ClientForm {
  custName: string;
  custPhone: string;
}

const forumCheckList = ref<ForumCheck[]>([]);
const clientDialogFormVisible = ref(false);
const addClientForm = reactive<ClientForm>({
  custName: '',
  custPhone: ''
});
const currentId = ref(0);
const searchCheck = ref(0);
const checkOptions = ref([
  { label: '已审核', value: 1 },
  { label: '未审核', value: 0 },
  { label: '已拒绝', value: 2 }
]);
const total = ref(0);
const loading = ref(false);
const query = reactive({
  pageSize: 10,
  pageNum: 1
});

onBeforeUnmount(() => {
  forumCheckList.value = [];
  total.value = 0;
});

onMounted(() => {
  getForumTableList();
});
/**
 * 筛选
 * @param val 筛选值
 */
const handleChangeChecked = (val: number): void => {
  const params = {
    checkStatus: val
  };
  getForumCheckList(params)
    .then((res) => {
      if (res.code == 200) {
        if (res.data && res.data.rows) {
          forumCheckList.value = res.data.rows;
          total.value = res.data.total || 0;
        } else if (res.rows) {
          forumCheckList.value = res.rows;
          total.value = res.total || 0;
        } else {
          forumCheckList.value = [];
          total.value = 0;
        }
      } else {
        console.error('API 返回错误：', res.msg);
        forumCheckList.value = [];
        total.value = 0;
      }
    })
    .catch((error) => {
      console.error('API 请求失败：', error);
      forumCheckList.value = [];
      total.value = 0;
    });
};

const getForumTableList = (): void => {
  loading.value = true;
  const params = {
    checkStatus: searchCheck.value,
    pageNum: query.pageNum,
    pageSize: query.pageSize
  };
  getForumCheckList(params)
    .then((res) => {
      if (res.code == 200) {
        if (res.data && res.data.rows) {
          forumCheckList.value = res.data.rows;
          total.value = res.data.total || 0;
        } else if (res.rows) {
          forumCheckList.value = res.rows;
          total.value = res.total || 0;
        } else {
          forumCheckList.value = [];
          total.value = 0;
        }
      } else {
        console.error(res.msg);
        forumCheckList.value = [];
        total.value = 0;
      }
    })
    .catch((error) => {
      console.error('获取论坛列表失败:', error);
      forumCheckList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};
/**
 * 编辑论坛
 * @param row 论坛数据
 * @param num 操作类型
 */
const handleEditForum = (row: ForumCheck, num: number | string): void => {
  let str = '';
  let params: any = {
    id: row.id,
    titleId: row.titleId,
    checkStatus: num,
    seq: row.seq
  };
  if (num === 1) {
    str = '确定同意这条论坛评论吗?';
  } else if (num === 2) {
    str = '确定拒绝这条论坛评论吗?';
  } else {
    str = '确定删除这条论坛评论吗?';
    params = {
      id: row.id,
      titleId: row.titleId,
      delFlag: 1,
      seq: row.seq
    };
  }

  ElMessageBox.confirm(str, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      editForum(params).then((res) => {
        if (res.code == 200) {
          getForumTableList();
        } else {
          console.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

const handleSubmitClientForm = (): void => {
  const params = {
    custName: addClientForm.custName,
    custPhone: addClientForm.custPhone,
    direction: 2,
    status: 0
  };
  if (currentId.value) {
    // Modify existing client
    updateClient(params).then((res: any) => {
      if (res.code == 200) {
        forumCheckList.value = res.data;
      } else {
        console.error(res.msg);
      }
    });
  } else {
    // Add new client
    addClient(params).then((res:any) => {
      if (res.code == 200) {
        forumCheckList.value = res.data;
      } else {
        console.error(res.msg);
      }
    });
  }
};

const formatDateYmdhm = (date: string): string => {
  if (!date) return '';
  try {
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (e) {
    console.error('日期格式化错误:', e);
    return date;
  }
};

const spanRule = (text: string, length: number): string => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};
</script>

<style lang="scss" scoped></style>
