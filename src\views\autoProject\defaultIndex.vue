<!-- 林业调查 -->
<template>
  <div class="app-container" v-loading.fullscreen.lock="fullscreenLoading" :class="{ 'big-class': isIfrem, 'iframe-class': isAZF }">
    <div class="container-box" id="box">
      <!-- 左侧列表的位置 -->
      <div class="column left" id="left">
        <div class="w-full h-full" v-show="nodeType == 1">
          <zdList
            class="project-zongdi-list"
            :parcel-list="parcelList"
            :query-params="queryParams"
            :total="total"
            @getParceListByPage="getParceListByCode"
            @getParceItem="getParcelInfo"
            :nowCheckedZD="nowCheckedZD"
            @getParceListByUpAndDown="getParceListByUpAndDown"
            :pageCount="pageCount"
            :title="title"
            ref="zdListRef"
            @noChangeList="noChangeList"
            :ruleTree="ruleTree"
            @getParmas="getParmas"
            @drawLabel="drawLabel"
            :mainHeight="mainHeight"
            :isShowJZD="isShowJZD"
            @changeIsShowJZD="changeIsShowJZD"
            @removerLabelLayer="removerLabelLayer"
            :firstNodes="firstNodes"
            :isKJ="isKJ"
            :toolType="toolType"
            :isAZFI="isAZF"
            :ruleIds="ruleIds"
            @labelCQMsg="labelCQMsg"
            @initChangeCenterToAZF="initChangeCenterToAZF"
            @initAzfCenter="initAzfCenter"
            @changeGraph="changeGraph"
            @clearCQLabel="clearCQLabel"
            @initShowGraphs="initShowGraphs"
            @getLinyeData="getLinyeData"
            @dwarChildNodeToSub="dwarChildNodeToSub"
            @toggleGraphicsVisibility="handleToggleGraphicsVisibility"
            @sendRuleId="handleRuleIdChange"
          />
        </div>
        <div class="w-full h-full" v-show="nodeType == 2">
          <tcList
            :tcData="tcData"
            :query-params="queryParams"
            :total="total"
            :pageCount="pageCount"
            :title="title"
            :tcName="tcName"
            @selectTc="handelSelectTc"
            :mainHeight="mainHeight"
            class="project-tuceng-list"
            @changeGraph="changeGraph"
          ></tcList>
        </div>
      </div>
      <div class="resizer left-resizer" id="leftResize">⋮</div>
      <!-- 中间的地图位置 -->
      <div class="column middle" id="middle">
        <dataMap
          :parcel-list="parcelList"
          :nowCheckedZD="nowCheckedZD"
          @changeNowCheckedZD="changeNowCheckedZD"
          ref="gisMapRef"
          @openInfo="openInfo"
          :changeParcelList="changeParcelList"
          :defaultWMS="defaultWMS"
          :ruleTree="ruleTree"
          :ruleIds="ruleIds"
          @noInitCenter="noInitCenter"
          v-show="!azfCenter"
          @checkedOneParcelId="checkedOneParcelId"
          @getLinyeData="getLinyeData"
          @drawLabel="drawLabel"
          @removerLabelLayer="removerLabelLayer"
          :isShowJZD="isShowJZD"
          :firstNodes="firstNodes"
          :isKJ="isKJ"
          :toolType="toolType"
          :kjExportUrl="kjExportUrl"
          :isAZFI="isAZF"
          @toggleGraphicsVisibility="handleToggleGraphicsVisibility"
          :current-rule-id="currentRuleId"
        />
        <azf ref="azfRef" v-show="azfCenter" :isIfrem="isIfrem" :mainParcelName="mainParcelName"></azf>
      </div>
      <div class="resizer right-resizer" id="RightResize" v-show="isLinyeInfo">⋮</div>
      <!-- 右侧的属性组的位置 -->
      <div class="column right" id="right" v-show="isLinyeInfo">
        <msgInfo
          ref="msgInfoRef"
          class="project-zongdi-detial-info"
          @closeLinyeInfo="handleCloseLinyeInfo"
          :checkedLinye="checkedLinye"
          :attrbutionList="attrbutionList"
          :isMain="isMain"
          :mainId="mainId"
          :ruleTree="ruleTree"
          @clearDeg="clearDeg"
          @speHightGrc="speHightGrc"
          @clearSpeHightGrc="clearSpeHightGrc"
          :mainHeight="mainHeight"
          :checkedNodeId="checkedNodeId"
          @initDeg="initDeg"
          :isIfrem="isIfrem"
          @changeGraph="changeGraph"
          :showTopologyCheck="showTopologyCheck"
        ></msgInfo>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

// 组件部分
import zdList from './components/zdList.vue';
import dataMap from './components/dataMap.vue';
import msgInfo from './components/msgInfo.vue';
import tcList from './components/tcList.vue';
import azf from './components/azf.vue';

import { selectRules, getPlaceList, getPlaceDetail, selectFields, selectParcelOne, examineList } from '@/api/modal';
import { initAttr } from '@/utils/validate';
import axios from 'axios';
import { findAsyncFileTemp } from '@/api/project';

import { useProjectStore } from '@/store/modules/project';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const projectStore = useProjectStore();
const route = useRoute();

// 变量声明部分
const parcelList = ref([]); //宗地列表
const total = ref(0);
const nowCheckedZD = ref(0);
const checkedLinye = ref({}); //选中的林业
const isLinyeInfo = ref(false); //是否展示详情
const currentRuleId = ref(''); // 新增用于存储ruleId
let queryParams: any = reactive({
  // 页码和页面参数 林业查询条件
  areaCode: '',
  pageNum: 1,
  pageSize: 20,
  parcelName: '',
  moduleId: 0,
  ifCheck: false,
  ruleIds: [],
  levelNum: undefined,
  downLoadId: undefined,
  paraceName: undefined,
  ifPage: true
});
const moduleId = ref(0);
const fullscreenLoading = ref(false);
const pageCount = ref(0);
const title = ref(''); //树形图第一个名字 如宗地
const changeParcelList = ref(true); // 宗地列表变化需要初始化地图
const ruleTree = ref([]); //规则树结构
const attrbutionList = ref([]); //显示的属性列表
const nodeForFieldGroupModelList = ref([]); //当前选中的节点对应的规则树的属性组
const checkedChildMsg = ref({}); //迭代找到的子要素对象
const isMain = ref(false); //是不是第一级
const mainId = ref(0); //第一级id
const nodeType = ref(null); //节点类型 1 要素节点 2 图层节点
const tcData = ref([]); //图层数据
const layerBase = ref(import.meta.env.VITE_APP_BASE_API); //图层字段的名字
const tcName = ref(''); //图层字段的名字
const base = ref(import.meta.env.VITE_APP_BASE_API); //图层字段的名字
const defaultWMS = ref('');
const modalType = ref(0); //模块类型 1地图类 2问卷类
const mainHeight = ref(window.innerHeight);
const isInitCenter = ref(true); //是否需要重置位置 地图中心位置
const isShowJZD = ref(false); //是否显示界址点 高亮的时候 默认不显示
const checkedNodeId = ref(''); //当前选择的节点对应的id
const firstNodes = ref([]); //树的第一层集合
const tcNodes = ref([]); //图层数据
const isKJ = ref(false); //结构树是否有勘界类型
const idList = ref([]);
const allExpression = ref([]);
const azfCenter = ref(false); //安置房展示方式
const mainParcelName = ref(''); //根节点名字
const showGraphs = ref([]); //需要显示图形的节点id(对于数据来说是ruleId)
const gisMapRef = ref(null); //地图组件的ref
const zdListRef = ref(null); //宗地列表组件的ref
const msgInfoRef = ref(null); //属性组组件的ref
const azfRef = ref(null); //安置房组件的ref
const allNodeStyle = ref([]); //所有节点的样式
const leftMiniWidth = ref(200); //左侧最小宽度
const rightMiniWidth = ref(280); //右侧最小宽度
const showTopologyCheck = ref(false); //是否展示拓扑检查
const onlyOne = ref(true); //是否只执行一次

const props = defineProps({
  toolType: {
    type: Number,
    default: 0
  },
  isIfrem: {
    type: Boolean,
    default: false
  },
  ruleIds: {
    type: Array,
    default: undefined
  },
  isAZF: {
    type: Boolean,
    default: false
  },
  kjExportUrl: {
    type: String,
    default: ''
  }
});

//---------------------------------------方法部分---------------------------------------

/**
 * 绘制方位角
 * @param val
 * @param deg
 * @param src
 */
const initDeg = (val, deg, src) => {
  gisMapRef.value.initDeg(val, deg, src);
};

/**
 * 绘制图层
 * @param item
 */
const handelSelectTc = (item) => {
  gisMapRef.value.drawWMSItem(item);
};

/**
 * 获取节点及属性组对应的id linkId 迁移用
 * @param list
 */
const getALLId = (list) => {
  list.forEach((v) => {
    const item = `${v.typeName},,,${v.id},`;
    idList.value.push(item);
    v.fieldGroupModelList.forEach((k) => {
      const ite = `${k.typeName},${k.id},${k.linkId},,`;
      idList.value.push(ite);
    });
    if (v.list.length != 0) {
      getALLId(v.list);
    }
  });
};

/**
 * 获取节点及属性组对应的id linkId 迁移用
 * @param list
 */
const initAllExpress = (list) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      k.fieldModelList.forEach((q) => {
        if (q.attribution.expression) {
          allExpression.value.push(
            `【节点：${v.typeName}】->【属性组：${k.typeName}】->【字段：${q.fieldCn}】->【表达式：${q.attribution.expression}】`
          );
        }
      });
    });
    if (v.list.length) {
      initAllExpress(v.list);
    }
  });
};

/**
 * 获取规则树的所有节点样式
 * @param list
 */
const getAllNodeStyle = (list) => {
  list.forEach((v) => {
    allNodeStyle.value.push({
      id: v.id,
      style: v.styleAttribution
    });
    if (v.list.length) {
      getAllNodeStyle(v.list);
    }
  });
};

/**
 * 获取树形图
 * @param moduleId
 */
const getData = (moduleId) => {
  selectRules({ moduleId: moduleId }).then((res) => {
    if (res.code == 200) {
      isKJ.value = false;
      ruleTree.value = res.data;
      //整理规则树的所有样式，实例数据不会返回样式了
      allNodeStyle.value = [];
      getAllNodeStyle(res.data);
      projectStore.setNodeStyles(allNodeStyle.value);
      if (res.data && res.data.length != 0) {
        title.value = res.data[0].typeName;
        if (res.data[0].layerFlag == 1) {
          //图层节点
          nodeType.value = 2;
        } else {
          nodeType.value = 1;
        }
        let tcysNodeNum = 0; //图层要素节点数量
        firstNodes.value = [];
        tcNodes.value = [];
        res.data.forEach((v) => {
          firstNodes.value.push({
            container: v.container,
            ruleId: v.id,
            typeName: v.typeName,
            disable: false
          });
          if (v.container !== 0) {
            tcysNodeNum++;
            tcNodes.value.push({
              id: v.id,
              typeName: v.typeName,
              children: [],
              container: v.container,
              isMain: true
            });
          }
          if (v.tools == 'kanjie') {
            isKJ.value = true;
          }
        });
        gisMapRef.value.init();
        if (tcysNodeNum > 0) {
          //有多个图层要素节点就需要改变
          zdListRef.value.setIsShowSpe();
        }
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取图层数据
 * @param val
 */
const getTcData = (val) => {
  const num = val.indexOf(':');
  const title = val.substring(num + 1, val.length);
  const url = `${layerBase.value}${val.substring(
    0,
    num
  )}/${title}/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${val}&maxFeatures=10000&outputFormat=application%2Fjson`;
  axios.get(url).then((res) => {
    if (res.data.features) {
      if (res.data.features.length != 0) {
        tcData.value = res.data.features;
      }
    }
  });
};

/**
 * 获取林业列表数据
 * @param flg
 */
const getLinyeData = (flg?: boolean) => {
  try {
    //flg true 第一次请求，只需要加载所有平台节点树 不需要加载图层节点
    // if (flg) {
    //   const ruleIds = [];
    //   ruleTree.value.forEach((v) => {
    //     if (v.container == 0) {
    //       ruleIds.push(v.id);
    //     }
    //   });
    //   queryParams.ruleIds = ruleIds;
    // }
    fullscreenLoading.value = true;
    if (props.ruleIds) {
      //房源销控专用 多个模块相同节点的数据查询 需要把moduleId删除
      delete queryParams.moduleId;
      queryParams.ruleIds = props.ruleIds;
    }
    if (route.query.iscq) {
      //如果是拆迁，需要把安置房数据刨除掉
      const ruleIds = [];
      ruleTree.value.forEach((v) => {
        if (!v.typeName.includes('安置房小区名称')) {
          ruleIds.push(v.id);
        }
      });
      queryParams.ruleIds = ruleIds;
    }
    queryParams.levelNum = 1;

    getPlaceList(queryParams)
      .then((res) => {
        fullscreenLoading.value = false;
        if (res.code == 200) {
          if (nodeType.value == 2) {
            //图层
            tcName.value = res.data.list[0].layerField;
            getTcData(res.data.list[0].layerUrl);
            //调用地图 直接显示相应的图层
            const index = res.data.list[0].layerUrl.indexOf(':');
            const title = res.data.list[0].layerUrl.substring(index + 1, res.data.list[0].layerUrl.length);
            const url = `${base.value}${res.data.list[0].layerUrl.substring(0, index)}/${title}/wms`;
            gisMapRef.value.initWMS(url, title);
            defaultWMS.value = res.data.list[0].layerUrl;
          } else {
            // 需要重新绘制地图
            changeParcelList.value = true;
            // 需要判断是否是图层要素
            // parcelList.value.forEach((v) => {
            //   if (v.container == 1) {
            //     //图层要素
            //     let typeName = '';
            //     for (let i = 0; i < tcNodes.value.length; i++) {
            //       if (tcNodes.value[i].id == v.ruleId) {
            //         typeName = tcNodes.value[i].typeName;
            //         break;
            //       }
            //     }
            //     v.typeName = typeName;
            //   }
            // });
            //图层要素需要整合成一个图层
            if (tcNodes.value.length !== 0) {
              parcelList.value = [];
              //先初始化 tcNodes.value[].children = []
              tcNodes.value.forEach((v) => {
                v.children = [];
              });
              // 先把图层默认节点放入
              res.data.list.forEach((v: any) => {
                if (v.container !== 0) {
                  for (let i = 0; i < tcNodes.value.length; i++) {
                    if (v.ruleId === tcNodes.value[i].id) {
                      tcNodes.value[i].children.push(v);
                    }
                  }
                } else {
                  parcelList.value.push(v);
                }
              });
              //把图层内容数据放在最上面
              tcNodes.value.forEach((v: any) => {
                parcelList.value.unshift(v);
              });
            } else {
              parcelList.value = res.data.list;
            }
            // 直接调用地图组件 绘制宗地（最外层要素）
            gisMapRef.value.initLinye(res.data.list, isInitCenter.value);
            total.value = res.data.total;
            let num = parseInt(String(res.data.total / queryParams.pageSize));
            if (res.data.total % queryParams.pageSize > 0) {
              num++;
            }
            pageCount.value = num;
            if (nowCheckedZD.value && nowCheckedZD.value != 0) {
              //如果之前有选中某一条 需要判断列表有没有选择的id 然后再请求详情
              for (let index = 0; index < parcelList.value.length; index++) {
                if (parcelList.value[index].id == nowCheckedZD.value) {
                  getParcelInfo(nowCheckedZD.value);
                  break;
                }
              }
            }
          }
          // 请求结束 看queryParams里面的downLoadId是否有值 有值代表是需要下载未匹配到的数据
          if (queryParams.downLoadId) {
            downloadNoMappingData(queryParams.downLoadId);
          }
          // 如果选择了显示的图形节点，就需要重新让地图绘制图形
          if (showGraphs.value.length != 0) {
            gisMapRef.value.reloadGraphs(parcelList.value, showGraphs.value);
          }
          // 需要查看标注信息
          if (localStorage.getItem('labelMsg')) {
            const labelMsg = JSON.parse(localStorage.getItem('labelMsg'));
            // 判断moduleId是否和当前的moduleId一致
            if (labelMsg.moduleId == moduleId.value) {
              // 把要绘制的标注传给地图
              gisMapRef.value.drawLabel(labelMsg.labelList);
            }
          }
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((error) => {
        fullscreenLoading.value = false;
        console.log('获取林业数据失败:', error);
        ElMessage.error('获取林业数据失败，请稍后重试');
      });
  } catch (error) {
    fullscreenLoading.value = false;
    console.error('林业数据处理错误:', error);
    ElMessage.error('数据处理出错，请稍后重试');
  }
};

// 导出未匹配的数据
const downloadNoMappingData = (id) => {
  const params = {
    id: id,
    suffix: 'txt',
    fileName: '未匹配id',
    del: 1
  };
  findAsyncFileTemp(params).then((res) => {
    if (res.status == 200 && res.data.size !== 0) {
      if (res.data.type == 'application/json') {
        //导出异常
        const read = new FileReader();
        read.readAsText(res.data, 'utf-8');
        let bugMsg = '';
        read.onload = (data) => {
          bugMsg = JSON.parse(data.currentTarget['result']).msg;
          ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
          if (!bugMsg) {
            bugMsg = '未知异常';
          }
        };
      } else {
        //导出正常
        const name = decodeURI(res.headers['content-disposition']);
        const index = name.indexOf('=');
        const endFileName = name.substring(index + 1, name.length) || '未匹配数据.txt';
        const blob = new Blob([res.data], { type: 'text/plain' });

        // 针对ie浏览器
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, endFileName);
        } else {
          //非ie浏览器
          const downloadElement = document.createElement('a');
          const href = window.URL.createObjectURL(blob); //常见下载的链接
          downloadElement.href = href;
          downloadElement.download = endFileName; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放blob对象
        }
      }
    }
  });
};
import { ElLoading } from 'element-plus';
import { string } from 'vue-types';
/**
 * 获取具体数据详情
 * @param id
 */
const getParcelInfo = (id: any) => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '数据加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    nowCheckedZD.value = id;
    selectParcelOne(id)
      .then((res) => {
        loading.close();
        changeParcelList.value = false;
        if (res.code == 200) {
          mainParcelName.value = res.data.parcelName;
          checkedLinye.value = res.data;
          isMain.value = true;
          mainId.value = res.data.id;
          checkedNodeId.value = res.data.id;
          changeGraph(res.data);
          zdListRef.value.getDetail(res.data, 1);
          gisMapRef.value.initData(res.data);
        } else {
          ElMessage.error(res.msg || '获取详情失败');
        }
      })
      .catch((error) => {
        loading.close();
        console.error('获取宗地详情失败:', error);
        ElMessage.error('获取宗地详情失败，请稍后重试');
      });
  } catch (error) {
    console.error('宗地详情处理错误:', error);
    ElMessage.error('数据处理出错，请稍后重试');
  }
};

//带分页的请求
const getParceListByCode = (pageNum, pageSize, paraceName) => {
  queryParams = {
    pageNum: pageNum,
    pageSize: pageSize,
    paraceName: paraceName,
    moduleId: moduleId.value
  }; //林业查询条件
  getLinyeData();
};

//改变当前选中的图形
const changeNowCheckedZD = async (val) => {
  if (nowCheckedZD.value != val) {
    // 先用地图传过来的id判断是否是第一级宗地
    let fristFlg = false;
    for (let index = 0; index < parcelList.value.length; index++) {
      if (parcelList.value[index].id == val) {
        fristFlg = true;
        break;
      }
    }
    // 如果是第一级 说明要重新请求详情 并绘制子要素
    if (fristFlg) {
      getParcelInfo(val);
    } else {
      await getNowNodeForId([checkedLinye.value], val);
      changeGraph(checkedChildMsg.value, true);
    }
  } else {
    //代表又选中第一节点
    changeGraph(checkedLinye.value, true);
  }
};

// 迭代根据id找要素内容
const getNowNodeForId = (list, val) => {
  for (let index = 0; index < list.length; index++) {
    if (list[index].id == val) {
      checkedChildMsg.value = list[index];
      break;
    } else if (list[index].list.length != 0) {
      getNowNodeForId(list[index].list, val);
    }
  }
};

/**
 * 信息弹窗打开
 */
const openInfo = () => {
  isLinyeInfo.value = true;
};

/**
 * 关闭林业信息弹窗
 */
const handleCloseLinyeInfo = () => {
  isLinyeInfo.value = false;
};

/**
 * 改变当前选中的图形
 * @param data
 * @param isChangeList 是true时代表是点击地图的子要素过来，要在列表把子要素高亮
 */
const changeGraph = async (data: any, isChangeList?: any) => {
  checkedNodeId.value = data.id;
  let isMainTemp = false; //是不是第一级
  for (let index = 0; index < parcelList.value.length; index++) {
    if (parcelList.value[index].id == data.id) {
      isMainTemp = true;
      break;
    }
  }
  isMain.value = isMainTemp;

  if (data.geomArcgis) {
    // 给地图高亮
    // gisMapRef.value.endChangHighLight(data);
    gisMapRef.value.drawFirstGeometry(data, data.ruleId, data.graphicalType, data.id);
  } else {
    //去掉原来的高亮和标注 拆除信息标注
    gisMapRef.value.initLableCQ();
  }
  // 需要通过规则树获取当前属性组的规则数据
  const ruleGroups = searchInTreeList(ruleTree.value, data.ruleId);

  // 现需要改为展示所有属性组,不管有没有内容 包括属性组里面的字段
  const allDetailAttr = await getAllDetailAttr(ruleGroups, data.fieldInstanceModels);

  if (allDetailAttr.length == 0) {
    msgInfoRef.value.setEmpty();
  }
  // 得到当前选中的节点对应的规则树属性组
  nodeForFieldGroupModelList.value = [];
  getNodeForFieldGroupModelList(ruleTree.value, data.ruleId);
  // 组装属性组给信息模块
  const attrbutionList = await initAttr(JSON.parse(JSON.stringify(allDetailAttr)), nodeForFieldGroupModelList.value);
  // 初始化赋值
  isLinyeInfo.value = true;
  msgInfoRef.value.setSelectTitleList(attrbutionList);

  if (isChangeList) {
    //列表对应子要素高亮
    zdListRef.value.heightNodeForId(data.id);
  }
};

/**
 * 得到当前选中的节点对应的规则树属性组 方法
 * @param targetId 目标节点的id
 * @param nodes 规则树
 * @returns 属性组
 */
const searchInTreeList = (nodes: any[], targetId: number): any[] | null => {
  for (const node of nodes) {
    if (node.id === targetId) {
      return node.fieldGroupModelList;
    }
    if (node.list && node.list.length > 0) {
      const found = searchInTreeList(node.list, targetId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

/**
 * 让地图绘制拆迁信息
 * @param msg
 * @param id
 * @param name
 * @param isMainNode
 */
const labelCQMsg = (msg, id, name, isMainNode) => {
  gisMapRef.value.drawCQLabel(msg, id, name, isMainNode);
};

/**
 * 取消地图显示拆迁信息弹窗
 */
const clearCQLabel = () => {
  gisMapRef.value.clearCQLabel();
};

/**
 * 组装所有属性组 有数据没数据都有
 * @param fieldGroupModels
 * @param fieldInstanceModels
 * @returns
 */
const getAllDetailAttr = async (fieldGroupModels: any, fieldInstanceModels: any) => {
  // 需要按顺序返回
  const list = [];
  fieldGroupModels.forEach((v) => {
    if (fieldInstanceModels.some((item) => item.linkId == v.linkId)) {
      for (let i = 0; i < fieldInstanceModels.length; i++) {
        if (fieldInstanceModels[i].linkId === v.linkId) {
          fieldInstanceModels[i].seq = v.seq;
          list.push(fieldInstanceModels[i]);
        }
      }
    } else {
      list.push({
        attribution: {},
        groupId: v.id,
        groupName: v.typeName,
        linkId: v.linkId,
        ruleAttribution: v.ruleAttribution,
        seq: v.seq
      });
    }
  });
  return list;
};

/**
 * 给子组件点击上一个下一个翻页情况 1 上一个 并切换上一页最后一个 2 下一个 并切换到下一页第一个
 * @param type
 * @param params
 */
const getParceListByUpAndDown = (type, params) => {
  if (type == 1) {
    //列表切换为上一页，并选中最后一个
    queryParams.pageNum--;
  } else if (type == 2) {
    //列表切换为下一页，并选中第一个
    queryParams.pageNum++;
    changeParcelList.value = true;
  }
  fullscreenLoading.value = true;
  queryParams.levelNum = 1;
  getPlaceList(queryParams).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      // 需要重新绘制地图
      parcelList.value = res.data.list;
      // 直接调用地图组件 绘制宗地（最外层要素）
      gisMapRef.value.initLinye(res.data.list);
      total.value = res.data.total;
      let num = parseInt(String(res.data.total / queryParams.pageSize));
      if (res.data.total % queryParams.pageSize > 0) {
        num++;
      }
      pageCount.value = num;
      if (type == 1) {
        //上一个
        nowCheckedZD.value = parcelList.value[parcelList.value.length - 1].id;
        getParcelInfo(nowCheckedZD.value);
      } else {
        //下一个
        nowCheckedZD.value = parcelList.value[0].id;
        getParcelInfo(nowCheckedZD.value);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 只是改变了宗地列表收起展开
 */
const noChangeList = () => {
  changeParcelList.value = false;
};

/**
 * 得到当前选中的节点对应的规则树属性组 方法
 * @param list
 * @param id
 */
const getNodeForFieldGroupModelList = (list, id) => {
  for (let index = 0; index < list.length; index++) {
    if (list[index].id == id) {
      nodeForFieldGroupModelList.value = list[index].fieldGroupModelList;
      return;
    }
    if (list[index].list instanceof Array) {
      getNodeForFieldGroupModelList(list[index].list, id);
    }
  }
};

/**
 * 列表筛选参数
 * @param parmas
 */
const getParmas = (parmas) => {
  if (parmas.pageSize <= 50) {
    parmas.ifCheck = false;
  }
  // 重新组装筛选条件
  const conditionFields = [];
  const itemParmas = JSON.parse(JSON.stringify(parmas));
  if (itemParmas.conditionFields && itemParmas.conditionFields.length != 0) {
    itemParmas.conditionFields.forEach((v, idx) => {
      if (v.value.length !== 0) {
        conditionFields.push(v);
        // 条件取下一个的条件 最后一个不进入/
        if (idx != itemParmas.conditionFields.length - 1) {
          const obj = { type: 2, value: itemParmas.conditionFields[idx + 1].relation };
          conditionFields.push(obj);
        }
      }
    });
    if (conditionFields.length != 0 && conditionFields[conditionFields.length - 1].type == 2) {
      //如果最后一个是条件 删除
      conditionFields.pop();
    }
    itemParmas.conditionFields = conditionFields;
  }
  queryParams = itemParmas;
  getLinyeData();
};

/**
 * 清除图片方位
 */
const clearDeg = () => {
  gisMapRef.value.clearDeg();
};

/**
 * 父组件调用子组件方法 传给地图
 * @param url
 */
const speHightGrc = (url) => {
  gisMapRef.value.heightSpeGrc(url);
};

/**
 * 父组件调用子组件清除 高亮的方位角
 */
const clearSpeHightGrc = () => {
  gisMapRef.value.clearSpeHightGrc();
};

/**
 * 父组件调用子组件不初始化中心
 */
const noInitCenter = () => {
  isInitCenter.value = false;
};

/**
 * 子组件传过来的绘制标注
 * @param fieldName
 * @param linkId
 */
const drawLabel = (fieldName, linkId) => {
  const parcels = [];
  parcelList.value.forEach((v) => {
    parcels.push(v.id);
  });
  if (parcels.length != 0) {
    selectFields(fieldName, linkId, parcels).then((res) => {
      if (res.code == 200) {
        const labelList = [];
        res.data.forEach((v) => {
          labelList.push({
            id: v.id,
            geomArcgis: v.geomArcgis,
            linkIdValue: v.linkIdValue
          });
        });
        // 需求是需要记住当前的标注内容，记在缓存里面。TODO
        const labelMsg = {
          moduleId: moduleId.value,
          labelList: labelList
        };
        localStorage.setItem('labelMsg', JSON.stringify(labelMsg));
        // 把要绘制的标注传给地图
        gisMapRef.value.drawLabel(labelList);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

/**
 * 显示隐藏高亮的界址点
 */
const changeIsShowJZD = () => {
  isShowJZD.value = !isShowJZD.value;
  gisMapRef.value.changeJZDVisbale(isShowJZD.value);
};

/**
 * 调用地图组件移除标注
 */
const removerLabelLayer = () => {
  gisMapRef.value.removerLabelLayer();
};

/**
 * 默认选中某条数据
 */
const checkedOneParcelId = (parcelId) => {
  queryParams.parcelName = parcelId;
  getLinyeData();
  getParcelInfo(parcelId);
};

/**
 * 监听中间可以左右拖动
 */
const handleDragControllerLeftDiv = () => {
  const resize = document.getElementById('leftResize');
  const left = document.getElementById('left');
  const middle = document.getElementById('middle');
  const right = document.getElementById('right');
  const box = document.getElementById('box');
  resize.onmousedown = (e) => {
    //  坐标的起始位置
    const startX = e.clientX;
    //  左侧位置的起始宽度
    resize['left'] = left.offsetWidth;
    // 获取右侧的初始的宽度
    const rightWidth = right.offsetWidth;
    // 监听鼠标的拖动事件
    document.onmousemove = (e) => {
      // 鼠标拖动的终止位置
      const endX = e.clientX;
      // 移动的距离  = 终止位置 endX -开始位置 startX
      // 左侧和右侧分别应该有一个固定的区域
      //  左侧的最小的宽度
      let leftWidth = resize['left'] + (endX - startX);

      // 限制左边区域的最小宽度为30px
      if (leftWidth < leftMiniWidth.value) leftWidth = leftMiniWidth.value;
      // 中间宽度  = box的宽度 - (计算之后的左侧宽度 + 初始化的右侧跨度)
      // 设置左边区域的宽度
      left.style.width = leftWidth + 'px';
      // 设置中间区域的宽度
      const centWidth = box.clientWidth - (leftWidth + rightWidth);
      middle.style.width = box.clientWidth - (leftWidth + rightWidth) + 'px';
      if (centWidth < 440) {
        //中间地图宽度小于440px的时候 头部操作栏要改为竖着排列 TODO
        gisMapRef.value.changeHandleTopType('vertical');
      } else {
        gisMapRef.value.changeHandleTopType('across');
      }
      //  设置右侧区域的宽度
      // right.style.width = rightWidth + "px";
    };
    // 鼠标松开事件
    document.onmouseup = (evt) => {
      document.onmousemove = null;
      document.onmouseup = null;
      resize['releaseCapture'] && resize['releaseCapture'](); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
    };
    resize['setCapture'] && resize['setCapture'](); //该函数在属于当前线程的指定窗口里设置鼠标捕获
    return false;
  };
};

/**
 * 监听中间可以左右拖动
 */
const handleDragControllerRightDiv = () => {
  const resize = document.getElementById('RightResize');
  const left = document.getElementById('left');
  const middle = document.getElementById('middle');
  const right = document.getElementById('right');
  const box = document.getElementById('box');

  resize.onmousedown = (e) => {
    //  坐标的起始位置
    const startX = e.clientX;
    //  左侧位置的起始宽度
    resize['left'] = right.offsetWidth;
    // 获取左侧的初始宽度
    const leftWidth = left.offsetWidth;
    // 监听鼠标的拖动事件
    document.onmousemove = (e) => {
      // 鼠标拖动的终止位置
      const endX = e.clientX;
      // 移动的距离  = 开始位置 startX - 终止位置 endX （初始位置大，不管是向左侧拖动 还是向右侧拖动 都是试用初始位置减 最后结束的位置）
      // 左侧和右侧分别应该有一个固定的区域
      // 右侧侧的最小的宽度
      let rightWidth = resize['left'] + (startX - endX);
      // 限制左边区域的最小宽度为30px
      if (rightWidth < rightMiniWidth.value) rightWidth = rightMiniWidth.value;
      //  设置右侧区域的宽度
      right.style.width = rightWidth + 'px';
      //  设置中间区域的宽度 = box的宽度 - (计算之后的右侧宽度 + 初始化的左侧宽度)
      const centWidth = box.clientWidth - (rightWidth + leftWidth);
      middle.style.width = box.clientWidth - (rightWidth + leftWidth) + 'px';
      if (centWidth < 440) {
        //中间地图宽度小于440px的时候 头部操作栏要改为竖着排列 TODO
        gisMapRef.value.changeHandleTopType('vertical');
      } else {
        gisMapRef.value.changeHandleTopType('across');
      }
      // 设置左侧区域的宽度
      // left.style.width = leftWidth + "px";
      // 如果右侧属性宽度小于400px的时候 需要把右侧显示的菜单模式改为下拉
      if (rightWidth < 400) {
        msgInfoRef.value.changeMenuType('dropdown');
      } else {
        msgInfoRef.value.changeMenuType('normal');
      }
    };
    // 鼠标松开事件
    document.onmouseup = (evt) => {
      document.onmousemove = null;
      document.onmouseup = null;
      resize['releaseCapture'] && resize['releaseCapture'](); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
    };
    resize['setCapture'] && resize['setCapture'](); //该函数在属于当前线程的指定窗口里设置鼠标捕获
    return false;
  };
};

/**
 * 给安置房专用的
 * @param list
 */
const initChangeCenterToAZF = (list) => {
  azfCenter.value = true;
  azfRef.value.initData(list);
};

/**
 * 点击其他节点要切换回地图
 */
const initAzfCenter = () => {
  azfCenter.value = false;
};

/**
 * 点击其他节点要切换回地图
 * @param ite
 */
const centerToRightAttr = (ite) => {
  changeGraph(ite);
};

/**
 * 如果选择显示图形的树变化需要查询该列表是否查询了树 以及初始化图形数据
 * @param list
 * @param ruleIds
 */
const initShowGraphs = (list, ruleIds) => {
  if (list.length != 0) {
    showGraphs.value = list;
    // 如果查询的列表数据包含了 完整数据 只需要重新根据需求绘制图形即可
    if (queryParams.ifTree) {
      // 如果选择了显示的图形节点，就需要重新让地图绘制图形
      gisMapRef.value.reloadGraphs(parcelList.value, showGraphs.value);
    } else {
      //需要重新加入完整数据请求并请求needData=false
      queryParams.ifTree = true;
      queryParams.childData = false;
    }
    queryParams.ruleIds = ruleIds;
    getLinyeData();
  } else {
    showGraphs.value = [];
    delete queryParams.ifTree;
    delete queryParams.childData;
    queryParams.ruleIds = [];
    getLinyeData();
    gisMapRef.value.reloadGraphs(parcelList.value, showGraphs.value);
  }
};

/**
 * 初始化ruleIds
 * @param list
 */
const initRuleIds = (list) => {
  queryParams.ruleIds = list;
  getLinyeData();
};

/**
 * 平级列表组件调用父组件方法 转传输给地图组件用以绘制子要素
 * @param list 子要素信息
 */
const dwarChildNodeToSub = (list: any) => {
  gisMapRef.value?.drawChildNodeToSub(list);
};

/**
 * 处理图形的显示/隐藏
 * @param id 图形ID
 * @param visible 是否显示
 */
const handleToggleGraphicsVisibility = (id: number, visible: boolean) => {
  if (gisMapRef.value) {
    gisMapRef.value.handleGraphicsVisibility(id, visible);
  }
};

const handleRuleIdChange = (ruleId) => {
  currentRuleId.value = ruleId;
};

const getExamineList = async (moduleId: number) => {
  examineList({ moduleId: moduleId }).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      // 有拓扑检查配置，显示拓扑检查按钮
      showTopologyCheck.value = true;
    }
  });
};

// 生命周期 onMounted
onMounted(() => {
  // 拖动左侧的div 数据
  handleDragControllerLeftDiv();
  // 拖动右侧的div 数据
  handleDragControllerRightDiv();
  moduleId.value = projectStore.proModuleId;
  queryParams.moduleId = moduleId.value;
  if (moduleId.value) {
    getData(moduleId.value);
    //查询是否有拓扑检查配置
    getExamineList(moduleId.value);
  }
  // window.onresize:浏览器尺寸变化响应事件
  window.onresize = () => {
    return (() => {
      // window.innerHeight:浏览器的可用高度
      window['screenHeight'] = window.innerHeight;
      mainHeight.value = window['screenHeight'];
    })();
  };
});
</script>

<style lang="scss" scoped>
.iframe-class {
  width: calc(100% - 20px) !important;
  height: calc(100% - 20px) !important;
}
:deep(.el-pagination) {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
}
.app-container {
  overflow: hidden;
  height: 100%;
  padding: 0;
  width: 100%;

  .container-box {
    display: flex;
    height: 100%;
    width: 100%;
    align-items: center;
    .column {
      min-width: 180px; /* 最小宽度 */
      height: 100%;
    }
    .left {
      min-width: 200px; /* 最小宽度 */
      width: 250px;
    }
    .right {
      min-width: 280px;
      width: 650px;
    }
    .middle {
      width: 60%;
      flex-grow: 2;
      border-right: 1px solid #ededed;
    }
    .resizer {
      cursor: col-resize;
      float: left;
      position: relative;
      background-color: #d6d6d6;
      border-radius: 5px;
      margin-top: -10px;
      width: 10px;
      height: 40px;
      background-size: cover;
      background-position: center;
      font-size: 32px;
      color: white;
    }
    /*拖拽区鼠标悬停样式*/
    .resizer:hover {
      color: #409eff;
    }
  }
}
.big-class {
  height: calc(100% - 40px);
}
</style>
