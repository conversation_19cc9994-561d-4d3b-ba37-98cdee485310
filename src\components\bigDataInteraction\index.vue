<!-- 数据搭配交互 -->
<template>
  <div class="bigDataInteraction-main">
    <el-dialog
      :title="interaction.title"
      v-model="dialogVisible"
      width="850px"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <div class="inter-row">
        <div class="inter-item" v-for="(item, index) in interaction.contentList" :key="index">
          <div class="top-label">{{ item.label }}</div>
          <div>{{ item.value }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
interface InteractionItem {
  label: string;
  value: string | number;
}

interface Interaction {
  title: string;
  contentList: InteractionItem[];
}

const props = defineProps<{
  bigDataDialog: boolean;
}>();

const emit = defineEmits<{
  (e: 'handleCloseBigDataInteraction'): void;
}>();

const dialogVisible = ref(props.bigDataDialog);
const interaction = ref<Interaction>({
  title: '',
  contentList: []
});

// 初始化
const init = (obj: Interaction) => {
  interaction.value = obj;
};

const handleClose = () => {
  emit('handleCloseBigDataInteraction');
};

// 暴露方法给父组件
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.bigDataInteraction-main {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.inter-row {
  display: grid; /* 使用网格布局 */
  grid-template-columns: 1fr 1fr; /* 两列，每列平分空间 */
  .inter-item {
    width: 50%;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    .top-label {
      margin-bottom: 6px;
      font-weight: bold;
    }
  }
}
</style>
