const dataText = JSON.stringify([
  ['张三', 24, '2001-10-20'],
  ['李四', 14, '2003-12-20'],
  ['李四2', 14, '2013-03-12'],
  ['李四3', 19, '2013-03-11'],
  ['李四5', 19, '2011-02-21'],
  ['李四6', 29, '2021-01-11'],
  ['李四8', 29, '2004-05-16']
]);

export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: dataText,
    moduleId: ''
  },
  attribute: {
    columns: JSON.stringify([
      { title: '姓名', width: 80 },
      { title: '年龄', width: 80 },
      { title: '生日', width: null }
    ]), //表头数据
    rowNum: 4, //显示行数
    headerBGC: '#00BAFF', //表头背景色
    oddRowBGC: '#003B51', //奇数行背景色
    evenRowBGC: '#0A2732', //偶数行背景色
    waitTime: 2000, //轮播时间间隔(ms)
    headerHeight: 35, //表头高度
    indexHeader: '序号', //行号表头
    carousel: 'single', //轮播方式'single'|'page'
    hoverPause: true, //悬浮暂停轮播
    index: true //开启序号
  }
};
