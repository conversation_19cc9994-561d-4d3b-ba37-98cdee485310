<!-- 用户填写设计好的表单内容 -->
<template>
  <div class="main">
    <el-card class="card">
      <div class="apply-header">
        <div class="left">
          <el-button text @click="handleGoBack">
            <el-icon><ArrowLeftBold /></el-icon>返回
          </el-button>
        </div>
        <div class="center">
          {{ processName }}
        </div>
        <div class="right">
          <el-button type="primary" size="small" @click="handleSaveForm">保存</el-button>
        </div>
      </div>
      <div class="apply-content">
        <div class="content-left">
          <div class="left-title">表单</div>
          <div class="left-form">
            <!-- 这里是表单 -->
            <el-form :model="formInputItem" ref="ruleForm" :rules="formInputItemRules" label-width="100px" label-position="right">
              <div v-for="ite in formFieldList" :key="ite.fieldSort">
                <el-form-item :label="ite.fieldCn" :prop="ite.fieldName">
                  <div style="color: red" v-if="ite.tag == 'idCardIcon' && isIdCardError">请输入正确的身份证号码</div>
                  <!-- 选择内容-->
                  <el-select
                    v-model="formInputItem[ite.fieldName]"
                    placeholder="请选择活动区域"
                    v-if="ite.tag === 'select'"
                    style="width: 100%"
                    @change="handleChangeCondition($event, ite)"
                  >
                    <el-option :label="op.label" :value="op.value" v-for="(op, opIndex) in ite.options" :key="opIndex"></el-option>
                  </el-select>
                  <!-- 日期内容 -->
                  <el-date-picker
                    type="date"
                    :placeholder="ite.placeholder"
                    v-model="formInputItem[ite.fieldName]"
                    style="width: 100%"
                    v-else-if="ite.tag === 'date'"
                    value-format="timestamp"
                    @change="handleChangeCondition($event, ite)"
                  ></el-date-picker>
                  <!-- 时间  时间选择 和时间区间都在这里 -->
                  <el-time-picker
                    :is-range="ite.tag === 'time-range' ? true : false"
                    v-model="formInputItem[ite.fieldName]"
                    :range-separator="ite.tag === 'time-range' ? '至' : ''"
                    :start-placeholder="ite.tag === 'time-range' ? '开始时间' : ''"
                    :end-placeholder="ite.tag === 'time-range' ? '结束时间' : ''"
                    :placeholder="ite.placeholder"
                    v-else-if="['time', 'time-range'].includes(ite.tag)"
                    value-format="timestamp"
                    style="width: 100%"
                    @change="handleChangeCondition($event, ite)"
                  >
                  </el-time-picker>
                  <!-- 单选内容 -->
                  <el-radio-group
                    v-model="formInputItem[ite.fieldName]"
                    v-else-if="ite.tag === 'radio'"
                    style="width: 100%"
                    @change="handleChangeCondition($event, ite)"
                  >
                    <el-radio :value="ra.value" v-for="(ra, oindex) in ite.options" :key="oindex">{{ ra.label }}</el-radio>
                  </el-radio-group>
                  <!-- 多选 checkBox -->
                  <el-checkbox-group
                    v-model="formInputItem[ite.fieldName]"
                    v-else-if="ite.tag === 'checkbox'"
                    style="width: 100%"
                    @change="handleChangeCondition($event, ite)"
                  >
                    <el-checkbox :value="elc.value" v-for="(elc, elcIndex) in ite.options" :key="elcIndex">{{ elc.label }}</el-checkbox>
                  </el-checkbox-group>
                  <!-- 数字输入框 -->
                  <el-input-number
                    v-model="formInputItem[ite.fieldName]"
                    :precision="ite.precision"
                    v-else-if="ite.tag === 'number'"
                    style="width: 100%"
                    @change="handleChangeCondition($event, ite)"
                  ></el-input-number>
                  <!-- 附件  这里先不考虑 -->
                  <the-one-upload v-else-if="ite.tag === 'upload'" @updateFileList="handleUpdateFileList($event, ite)"></the-one-upload>
                  <!-- 身份证 oninput="value=value.replace(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/g,'')"-->
                  <!-- oninput="value=value.replace(/(^\d{18}$)|(^\d{17}(\d|X|x)$)/g,'')" -->
                  <el-input
                    :placeholder="ite.placeholder"
                    v-model="formInputItem[ite.fieldName]"
                    style="width: 100%"
                    @blur="handleValueIdCard"
                    maxlength="18"
                    show-word-limit
                    v-else-if="ite.tag === 'idCardIcon'"
                    @change="handleChangeCondition($event, ite)"
                  ></el-input>
                  <!-- 手机号 -->
                  <el-input
                    :placeholder="ite.placeholder"
                    oninput="value=value.replace(/[^\d]/g,'')"
                    style="width: 100%"
                    maxlength="11"
                    show-word-limit
                    v-model="formInputItem[ite.fieldName]"
                    v-else-if="ite.tag === 'phoneIcon'"
                    @change="handleChangeCondition($event, ite)"
                  ></el-input>
                  <!-- 正常的单行和多行文本输入框 -->
                  <el-input
                    :type="ite.tag === 'textarea' ? 'textarea' : ''"
                    :placeholder="ite.placeholder"
                    style="width: 100%"
                    v-model="formInputItem[ite.fieldName]"
                    @change="handleChangeCondition($event, ite)"
                    v-else
                  ></el-input>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="content-middle"></div>
        <div class="content-right">
          <div class="right-title">流程</div>
          <div class="content">
            <el-steps direction="vertical">
              <el-step v-for="activity in processConfigList" :key="activity.nodeId">
                <template v-slot:icon>
                  <div>
                    <svg-icon class-name="svg-icon-item" :icon-class="activity.icon"></svg-icon>
                  </div>
                </template>
                <template v-slot:description>
                  <div
                    class="desc-main"
                    :class="{
                      'desc-err': processErrorTip.nodeId == activity.nodeId
                    }"
                  >
                    <div style="display: flex; justify-content: space-between">
                      <div class="desc-title">
                        <div>
                          <span>{{ activity.properties.title }}</span>
                          <span
                            v-if="activity.properties.title == '条件分支' && activity.type == 'route'"
                            style="padding-left: 4px; color: #f8634f; font-size: 12px; font-weight: 400"
                            >必填信息填写后，流程将自动更新展示</span
                          >
                          <span v-if="processErrorTip.nodeId == activity.nodeId" style="padding-left: 4px; color: red">{{
                            processErrorTip.message
                          }}</span>
                        </div>
                        <div
                          v-if="
                            ['approver', 'audit'].includes(activity.type) && activity.properties.approvals && activity.properties.approvals.length > 0
                          "
                          style="font-size: 12px; font-weight: 400"
                        >
                          <span v-if="activity.properties.actType == 'or'">（或签）</span>
                          <span v-else-if="activity.properties.actType == 'and'">（会签）</span>
                          <!-- <span v-else>（默认）</span> -->
                        </div>
                      </div>
                    </div>
                    <div class="desc-content">
                      <div
                        class="desc-add-people"
                        v-if="isShowSelect(activity)"
                        :title="`添加${activity.properties.title}`"
                        @click="handleSelectPeople(activity)"
                      >
                        <el-icon><Plus /></el-icon>
                      </div>
                      <div class="desc-user" v-if="activity.properties && activity.properties.approvals && activity.properties.approvals.length > 0">
                        <div v-for="(app, appIndex) in activity.properties.approvals" :key="appIndex" class="desc-item">
                          <div class="user-name-tag">
                            <el-tag
                              :closable="isShowSelect(activity)"
                              :type="getUserTagType(activity, app)"
                              @close="handleCloseUser(activity, app)"
                              >{{ app.userName }}</el-tag
                            >
                          </div>
                        </div>
                      </div>
                      <!-- 展示当前是发起人自选的情况 -->
                      <div
                        class="desc-user"
                        v-else-if="
                          activity.properties &&
                          activity.properties.actionRuleType === 'target_originator' &&
                          activity.properties.approvals &&
                          activity.properties.approvals.length === 0
                        "
                      >
                        发起人自己
                      </div>
                    </div>
                  </div>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
      </div>
      <!-- 选择抽屉抄送人 -->
      <el-drawer :title="selectPeopleTitle" v-model="isShowSelectPeople" direction="rtl" :before-close="handleCloseShowSelectPeople">
        <div class="select-people-drawer__content">
          <fc-org-select
            v-if="currentActivity.properties !== null && currentActivity.properties !== undefined"
            :ref="`${currentActivity.type}-org`"
            v-model="currentActivity.properties.approvals"
            :type="'user'"
            buttonType="button"
            :title="currentActivity.properties.title ? currentActivity.properties.title : ''"
            :maxNum="maxNum"
          />
          <div class="option-box" v-if="isShowCounterSign">
            <p>多人审批时采用的审批方式</p>
            <el-radio v-model="currentActivity.properties.actType" :value="'or'" class="radio-item">或签（一名审批人同意或拒绝即可）</el-radio>
            <br />
            <el-radio v-model="currentActivity.properties.actType" :value="'and'" class="radio-item">会签（须所有审批人同意）</el-radio>
            <br />
            <!-- <el-radio  v-model="currentActivity.properties.actType"  :label="'normal'" class="radio-item">常规（按顺序同意或者拒绝）</el-radio> -->
          </div>
          <div class="select-people-drawer__footer">
            <el-button type="info" style="margin: 8px" @click="handleCloseShowSelectPeople">取消</el-button>
            <el-button type="primary" style="margin: 8px" @click="handleSaveSelectPeople" plain>确定</el-button>
          </div>
        </div>
      </el-drawer>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { addForm, getPdefinitionDetial, addProcess, addInstance, getInstanceDetial } from '@/api/process';
import { useUserStore } from '@/store/modules/user';
// import TheOneUpload from '@/components/TheOneUpload/index.vue';
// import SvgIcon from '@/components/SvgIcon/index.vue';
import FcOrgSelect from '../FormControls/OrgSelect/index.vue';
import type { FormInstance } from 'element-plus';
import { config } from 'process';

// 类型定义
interface Condition {
  type: string;
  paramKey: string;
  matchType?: string;
  paramValues?: any[];
  conditionNumValue?: number | number[];
}

interface ProcessNode {
  type: string;
  content: string;
  properties: {
    title: string;
    actType?: string;
    approvals?: any[];
    actionRuleType?: string;
    optionalMultiUser?: boolean;
    conditions?: Condition[][];
    priority?: number;
    userOptional?: boolean;
  };
  nodeId: string;
  childNode?: ProcessNode;
  conditionNodes?: ProcessNode[];
  icon?: string;
  prevId?: string;
}

interface FormField {
  fieldName: string;
  fieldCn: string;
  tag: string;
  required: boolean;
  placeholder?: string;
  options?: any[];
  defaults?: any;
  precision?: number;
  fieldSort?: number;
}

interface ProcessErrorTip {
  nodeId?: string;
  message?: string;
}

interface UserInfo {
  userId: string;
  nickName: string;
  userName?: string;
}

interface ProcessInstanceResponse {
  code: number;
  msg: string;
  data?: {
    processFormValue?: Record<string, any>[];
  };
}

interface UploadFile {
  status: string;
  response?: {
    data: Array<{
      name: string;
      path: string;
    }>;
  };
}

interface FileItem {
  name: string;
  originalFilename: string;
  fileUrl: string;
  url: string;
}

// 路由相关
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
// 表单验证
const ruleForm = ref<FormInstance>();

interface UserInfo {
  userId: string | number;
  [key: string]: any;
}

const user = computed<UserInfo>(() => userStore.user as UserInfo);
// 响应式数据
const formFieldList = ref<FormField[]>([]);
const formInputItem = ref<Record<string, any>>({});
const formInputItemRules = ref<Record<string, any>>({});
const processConfig = ref<ProcessNode>({} as ProcessNode);
const isShowSelectPeople = ref(false);
const processName = ref('');
// 当前激活的节点
const currentActivity = ref<ProcessNode>({
  type: '',
  content: '',
  properties: {
    title: '',
    actType: 'normal',
    approvals: [],
    actionRuleType: '',
    optionalMultiUser: false
  },
  nodeId: ''
});
// 选择人的标题
const selectPeopleTitle = ref('选人');
// 流程审核报错的提示
const processErrorTip = ref<ProcessErrorTip>({
  nodeId: undefined,
  message: undefined
});
// 流程节点是否通过
const isValidateProcess = ref(true);
const formValue = ref(undefined);
// 最后的流程节点的数据
const resultNodeList = ref<ProcessNode[]>([]);
// 第一次进入的时候的流程数组
const fristProcessConfigList = ref<ProcessNode[]>([]);
// 当前用户的id
const currentUserId = ref(undefined);
// 表单输入身份证号码的校验
const isIdCardError = ref(false);

/**
 * 当前节点是否能在发起的时候选中节点类型
 */
const isShowSelect = computed(() => {
  return (item: ProcessNode) => {
    let valid = false;
    const prop = item.properties;
    if (item.type === 'approver') {
      if (prop.actionRuleType === 'target_select') {
        valid = true;
      }
    } else if (['audit', 'notifier'].includes(item.type)) {
      valid = prop.userOptional || false;
    }
    return valid;
  };
});
/**
 * 不同（审批/抄送/办理）节点选择的人 展示样式颜色不同
 */
const getUserTagType = computed(() => {
  return (activitiy: ProcessNode, userItem: any) => {
    if (activitiy.type === 'start') {
      return 'info';
    } else if (activitiy.type === 'approver') {
      return 'warning';
    } else if (activitiy.type === 'audit') {
      return 'danger';
    } else if (activitiy.type === 'notifier') {
      return 'primary';
    }
    return 'info';
  };
});
/**
 * 是否出现或签/会签的选项
 */
const isShowCounterSign = computed(() => {
  const typeList = ['approver', 'audit'];
  if (typeList.includes(currentActivity.value.type)) {
    if (currentActivity.value?.properties?.approvals?.length > 1) {
      currentActivity.value.properties.actType = 'or';
    }
    return currentActivity.value?.properties?.approvals?.length > 1;
  }
  return false;
});

/**
 * 计算右侧流程节点的审核详情
 */
const processConfigList = computed(() => {
  if (resultNodeList.value.length > 0) {
    const list = resultNodeList.value.filter((node) => node.type !== 'route' && node.type !== 'condition');
    return list || [];
  } else {
    return fristProcessConfigList.value;
  }
});

/**
 * 计算当前用户数可选的最大数量
 */
const maxNum = computed(() => {
  let number = 99;
  const isTargetSelect = currentActivity.value.properties.actionRuleType === 'target_select';
  const isMoreSelectPeopele = currentActivity.value.properties.optionalMultiUser === false;
  if (isTargetSelect && isMoreSelectPeopele) {
    number = 1;
    return number;
  }
  return number;
});

/**
 * 返回离开这个页面
 */
const handleGoBack = () => {
  ElMessageBox.confirm('离开此页面填写的数据不会保存?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      router.push('/bmp/processAudit');
    })
    .catch(() => {});
};
/**
 * 提交保存之前，对流程节点进行校验主要判断审核人/抄送人/办理人是否有为空节点，如果有空节点提示校验
 * @param configItem  当前判断的流程节点的数据
 */
const handleValidateProcessConfig = async (configItem: ProcessNode): Promise<void> => {
  return new Promise((resolve) => {
    const typeList = ['approver', 'audit', 'notifier'];
    const actTypeList = ['and', 'or'];
    const error = handleTypeStr(configItem.type);
    if (typeList.includes(configItem.type)) {
      const prop = configItem.properties || {};
      const approvals = (prop as any).approvals;
      (prop as any).approvals = approvals;

      if (configItem.type === 'approver' && (prop as any).actionRuleType === 'target_select' && approvals.length === 0) {
        processErrorTip.value = {
          nodeId: configItem.nodeId,
          message: `请选择${error}`
        };
        isValidateProcess.value = false;
        resolve();
        return;
      } else if ((prop as any).actionRuleType !== 'target_originator' && (prop as any).approvals.length === 0) {
        processErrorTip.value = {
          nodeId: configItem.nodeId,
          message: `请选择${error}`
        };
        isValidateProcess.value = false;
        resolve();
        return;
      } else if ((prop as any).actionRuleType === 'target_originator') {
        const params = {
          userId: user.value.userId,
          userName: user.value.nickName,
          label: user.value.nickName,
          nodeId: user.value.userId,
          memberType: 'user'
        };
        (prop as any).approvals.push(params);
      } else if ((prop as any).approvals.length > 1 && !actTypeList.includes((prop as any).actType || '')) {
        processErrorTip.value = {
          nodeId: configItem.nodeId,
          message: `请选择${error}`
        };
        isValidateProcess.value = false;
        resolve();
        return;
      } else {
        isValidateProcess.value = true;
        resolve();
        return;
      }
    }

    if (configItem.childNode && Object.keys(configItem.childNode).length > 0) {
      handleValidateProcessConfig(configItem.childNode).then(resolve);
    } else {
      isValidateProcess.value = true;
      resolve();
    }
  });
};
/**
 * 提交保存表单和流程配置的方法
 */
const handleSaveForm = async () => {
  const processId = route.query.id;
  const keys = Object.keys(formInputItem.value);
  const list = keys.map((key) => ({ [key]: formInputItem.value[key] }));
  const data = {
    processConfig: processConfig.value,
    processForm: formFieldList.value,
    processFormValue: list,
    processDefinitionId: processId,
    title: processName.value
  };

  let formValidate = false;
  await ruleForm.value?.validate((valid) => {
    formValidate = valid;
  });

  await handleValidateProcessConfig(processConfig.value);

  if (formValidate && isValidateProcess.value) {
    addInstance(data).then((res) => {
      if (res.code === 200) {
        ElMessage.success('添加成功！');
        router.push('/bmp/processAudit');
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 生命周期钩子
onMounted(() => {
  handleProcessDetial();
});

// 监听路由参数变化
watch(
  () => route.query.id,
  (newId) => {
    if (newId) {
      handleProcessDetial();
    }
  }
);

/**
 * 获取流程实例详情
 */
const handleProcessDetial = async () => {
  // 这里的id 是字符串不是数字
  const id = route.query.id;
  if (id !== '0') {
    try {
      const res = await getPdefinitionDetial({ id: id });
      if (res.code === 200) {
        processConfig.value = res.data.processConfig;
        processName.value = res.data.processName;
        fristProcessConfigList.value = handleFristProcessConfigList(res.data.processConfig);
        if (res.data.processForm !== null && res.data.processForm !== undefined) {
          handleTransformObjects(res.data.processForm);
          formFieldList.value = res.data.processForm;
          formFieldList.value.forEach((item) => {
            handleFormRules(item);
          });
        }
        const instanceId = route.query.instanceId;
        if (instanceId !== '0') {
          handleProcessInstanceDetial();
        }
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {}
  }
};

/**
 * 处理流程配置的方法
 * @param configItem 当前节点
 * @param depth  深度
 * @param result 处理之后的返回的结果值
 */
const handleFristProcessConfigList = (configItem: ProcessNode, depth = 0, result: ProcessNode[] = []): ProcessNode[] => {
  // 当前节点信息
  const currentNode: ProcessNode = {
    type: configItem.type,
    content: configItem.content,
    properties: configItem.properties,
    nodeId: configItem.nodeId,
    childNode: undefined
  };

  if (currentNode.type === 'route') {
    currentNode.properties = {
      title: '条件分支'
    };
  }

  // 根据类型和审批状态更新图标
  handleUpdateIcon(currentNode);

  // 如果有 prevId 属性，则添加到当前节点
  if ('prevId' in configItem) {
    currentNode.prevId = configItem.prevId;
  }

  // 添加当前节点到结果数组的正确位置
  const insertIndex = depth * 2;
  result.splice(insertIndex, 0, currentNode);

  // 如果存在 childNode 并且 childNode 是一个对象
  if (configItem.childNode && typeof configItem.childNode === 'object') {
    // 递归处理 childNode
    handleFristProcessConfigList(configItem.childNode, depth + 1, result);
  }

  return result;
};

//
/**
 * 处理更新状态图标 起点、终点、审批人、办理人、终点、抄送人、条件节点 每个节点展示的icon 不一样
 * @param node 当前流程的展示节点
 */
const handleUpdateIcon = (node: ProcessNode): void => {
  if (node.type === 'start') {
    node.icon = 'applyStart';
  } else if (node.type === 'approver') {
    node.icon = 'applyApprove';
  } else if (node.type === 'audit') {
    node.icon = 'applyAudit';
  } else if (node.type === 'notifier') {
    node.icon = 'applyNotify';
  } else if (node.type === 'route') {
    node.icon = 'applyQuestion';
  } else if (node.type === 'end') {
    node.icon = 'applyEnd';
  }
};

/**
 * 处理类型字符串
 * @param str  当前节点的类型
 */
const handleTypeStr = (str: string): string => {
  switch (str) {
    case 'approver':
      return '审核人';
    case 'audit':
      return '办理人';
    case 'notifier':
      return '抄送人';
    default:
      return '';
  }
};

/**
 * 如果这个指端设置了必填项，但是用户又没有填写这个值，触发校验
 * @param item 当前表单填写的字段
 */
const handleFormRules = (item: FormField): void => {
  const selectList = ['select', 'radio', 'checkbox', 'time', 'time-range', 'date', 'date-range', 'upload'];
  if (item.required) {
    const message = selectList.includes(item.tag) ? `请选择${item.fieldCn}` : `请输入${item.fieldCn}`;

    formInputItemRules.value[item.fieldName] = [
      {
        required: item.required,
        message,
        trigger: ['blur', 'change']
      }
    ];
  }
};

/**
 * 获取流程实例详情
 */
const handleProcessInstanceDetial = async (): Promise<void> => {
  const instanceId = route.query.instanceId;
  if (!instanceId) return;

  try {
    const res = (await getInstanceDetial({ id: instanceId })) as ProcessInstanceResponse;

    if (res.code === 200 && res.data?.processFormValue) {
      formInputItem.value = res.data.processFormValue.reduce(
        (acc, curr) => ({
          ...acc,
          ...curr
        }),
        {}
      );
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    ElMessage.error('获取流程实例详情失败');
  }
};

// 处理表单数据转换
/**
 * 进入页面的时候需要初始化页面种的值，如果有默认值先展示默认值，如果没有默认值 出多选框checkbox 初始化称数组[],其他情况初始化称null /undefined
 * @param arrayOfItems 页面进入的时候左侧表单类型的值
 */
const handleTransformObjects = (arrayOfItems: FormField[]): void => {
  arrayOfItems.forEach((item) => {
    if (typeof item === 'object' && item !== null && item.fieldName) {
      const fieldName = item.fieldName;

      if (item.tag === 'checkbox') {
        try {
          const defaults = item.defaults ? JSON.parse(JSON.stringify(item.defaults)) : null;
          formInputItem.value[fieldName] = defaults && defaults !== '' && defaults !== null ? defaults : [];
        } catch (error) {
          formInputItem.value[fieldName] = [];
        }
      } else {
        const defaults = item.defaults;
        formInputItem.value[fieldName] = defaults && defaults !== '' && defaults !== null ? defaults : undefined;
      }
    }
  });
};

// 处理表单值变化
/**
 * 左侧填写的表单值发生变化的时候，右侧展示对应的流程内容
 * @param val 原本控件绑定的值
 * @param item 当前控件的配置信息 item
 */
const handleChangeCondition = (val: any, item: FormField): void => {
  resultNodeList.value = [];
  handleProcessConfigList(processConfig.value, val);
};

// 处理流程配置列表
/**
 * 处理流程配置列表，如果当前是条件分支的内容，需要根据输入的值的内容来动态判断审核的节点是哪一个
 * @param processConfig 流程配置，最初接口中返回的流程审核步骤的数据
 * @param processFormValue 用户填写的表单值
 */
const handleProcessConfigList = (processConfig: ProcessNode, processFormValue: any): void => {
  const currentNode: ProcessNode = {
    type: processConfig.type,
    content: processConfig.content,
    properties: processConfig.properties,
    nodeId: processConfig.nodeId,
    childNode: {
      type: '',
      content: '',
      properties: {},
      nodeId: ''
    },
    conditionNodes: [],
    icon: 'approvewaiting',
    prevId: processConfig.prevId
  };

  if (processConfig.type === 'condition') {
    const conditions = processConfig.properties.conditions;
    const isConsitionFlag = handleContions(conditions, processFormValue);
    if (isConsitionFlag) {
      handleAddNodeToResultNodeList(currentNode);
      handleCommonConditionNodesAndChildNode(currentNode, processConfig, processFormValue);
    } else {
      return;
    }
  } else {
    handleAddNodeToResultNodeList(currentNode);
    handleCommonConditionNodesAndChildNode(currentNode, processConfig, processFormValue);
  }
  handleUpdateIcon(currentNode);
};

/**
 * 添加节点到结果列表
 * @param currentNode 当前要添加到节点中的当前节点
 */
const handleAddNodeToResultNodeList = (currentNode: ProcessNode): void => {
  const ids = resultNodeList.value.map((item) => item.nodeId);
  if (!ids.includes(currentNode.nodeId)) {
    resultNodeList.value.push(currentNode);
  }
};

/**
 * 处理条件节点
 * @param currentNode  当前节点
 * @param conditionNodes 当前条件
 * @param processFormValue 当前用户输入的值
 */
const handleConditionNodes = (currentNode: ProcessNode, conditionNodes: ProcessNode[], processFormValue: any): void => {
  const sortedConditionNode = handleConditionSort(conditionNodes);

  let isConditionSelect = false;

  sortedConditionNode.forEach((conditonSortItem) => {
    const conditions = conditonSortItem?.properties?.conditions;
    const isConsitionFlag = handleContions(conditions, processFormValue);
    if (isConsitionFlag) {
      handleAddNodeToResultNodeList(currentNode);
      handleCommonConditionNodesAndChildNode(currentNode, conditonSortItem, processFormValue);
      isConditionSelect = true;
    } else {
      return;
    }
  });

  if (!isConditionSelect) {
    const defaultConditionNode = sortedConditionNode[sortedConditionNode.length - 1];
    handleAddNodeToResultNodeList(defaultConditionNode);
    handleCommonConditionNodesAndChildNode(currentNode, defaultConditionNode, processFormValue);
  }
};

/**
 *	根据当前的输入的 processFormValue值 判断是否进入这个节点，只有内层节点为是且的关系 && ,外层节点是或的关系 ||
 *  设计的表单中只有 4 种类型可以添加到流程审核种作为条件判断的依据：
 *  发起人类型  "condition_starter";
 *  数字类型  "condition_range";
 * 单选类型  "condition_value"; 这里的 el-radio 和el-select 都属于这里
 * 多选类型 "condition_multi_range";
 * @param conditions 当前条件节点的子项，这是一个由二维数组组成的数据
 * @param processFormValue 用户输入的条件节点
 */
const handleContions = (conditions: Condition[][], processFormValue: any): boolean => {
  let outFlagOfOr = false;

  for (const outConditionItem of conditions) {
    let innerFlagOfAnd = true;

    for (const innerConditionItem of outConditionItem) {
      const innerType = innerConditionItem.type;
      const innerParamKey = innerConditionItem.paramKey;
      const paramKeyValue = processFormValue;
      let valid = false;
      if (innerType === 'condition_range') {
        const innerParamType = innerConditionItem.matchType;
        const innerParmaValue = innerConditionItem.conditionNumValue;
        valid = handelEvaluateExpression(innerParamType, paramKeyValue, innerParmaValue);
      } else if (innerType === 'condition_value') {
        const innerParmaValue = JSON.parse(JSON.stringify(innerConditionItem.paramValues));
        const paramKeyValue = Number(processFormValue);
        valid = innerParmaValue.includes(paramKeyValue);
      } else if (innerType === 'condition_multi_range') {
        const innerParmaValues = JSON.parse(JSON.stringify(innerConditionItem.paramValues));
        const formValues = JSON.parse(JSON.stringify(processFormValue));
        const type = innerConditionItem.matchType;
        if (type === 'any' && Array.isArray(formValues)) {
          valid = innerParmaValues.some((item: any) => formValues.includes(item));
        } else if (type === 'all' && Array.isArray(formValues)) {
          valid = innerParmaValues.some((item: any) => formValues.includes(item));
        }
      } else if (innerType === 'condition_starter') {
        const list = innerConditionItem.paramValues;
        const userId = user.value.userId;
        const innerParmaValues1 = JSON.parse(JSON.stringify(list));
        valid = innerParmaValues1.includes(userId);
      }

      if (!valid) {
        innerFlagOfAnd = false;
        break;
      }
    }

    if (innerFlagOfAnd) {
      outFlagOfOr = true;
      break;
    }
  }

  return outFlagOfOr;
};

// 处理表达式求值
/**
 * 针对是数字输入框的时候，需要比较 输入框和本身值的大小
 * @param type 比较类型
 * @param formValue 用户数输入的值
 * @param targetValue 当前比较的值
 */
const handelEvaluateExpression = (type: string, formValue: number, targetValue: number | number[]): boolean => {
  switch (type) {
    case 'gt':
      return formValue > (targetValue as number);
    case 'lt':
      return formValue < (targetValue as number);
    case 'gte':
      return formValue >= (targetValue as number);
    case 'lte':
      return formValue <= (targetValue as number);
    case 'eq':
      return formValue === targetValue;
    case 'bet':
      return (targetValue as number[])[0] < formValue && formValue < (targetValue as number[])[3];
    case 'ebet':
      return (targetValue as number[])[0] <= formValue && formValue < (targetValue as number[])[3];
    case 'bete':
      return (targetValue as number[])[0] < formValue && formValue <= (targetValue as number[])[3];
    case 'ebete':
      return (targetValue as number[])[0] <= formValue && formValue <= (targetValue as number[])[3];
    default:
      return false;
  }
};

/**
 * 针对条件节点的排序，需要把默认条件排在最后，其他节点根据优先值排序
 * @param conditionNodes  当前流程节点的所有条件  这是一个二维数组，我们只需要排[1] 里面的自己节点的数据
 */
const handleConditionSort = (conditionNodes: ProcessNode[]): ProcessNode[] => {
  return conditionNodes.sort((a, b) => {
    const priorityA = a.properties?.priority ?? Infinity;
    const priorityB = b.properties?.priority ?? Infinity;
    return priorityA - priorityB;
  });
};

//
/**
 * 处理通用条件节点和子节点
 * @param currentNode 当前处理节点
 * @param configItem  当前已经处理完成的节点，如果是第一个子节点，currentNode 和 configItem 相等
 * @param processFormValue 当前用户填写的数据值
 */
const handleCommonConditionNodesAndChildNode = (currentNode: ProcessNode, configItem: ProcessNode, processFormValue: any): void => {
  if (configItem.conditionNodes?.length) {
    handleConditionNodes(currentNode, configItem.conditionNodes, processFormValue);
  }
  if (configItem.childNode && typeof configItem.childNode === 'object') {
    handleProcessConfigList(configItem.childNode, processFormValue);
  }
};

/**
 * 处理文件上传列表更新
 * @param list 上传更新的所有数据
 * @param ite 当前节点的itemitem
 */
const handleUpdateFileList = (list: UploadFile[], ite: FormField): void => {
  const files: FileItem[] = [];

  if (list.length !== 0 && list.every((item) => item.status === 'success')) {
    list.forEach((item) => {
      if (item.response) {
        const obj: FileItem = {
          name: item.response.data[0].name,
          originalFilename: item.response.data[0].name,
          fileUrl: item.response.data[0].path,
          url: item.response.data[0].path
        };
        files.push(obj);
      }
    });
  }

  const fileUrls = files.map((item) => item.fileUrl) || [];
  formInputItem.value[ite.fieldName] = fileUrls.length > 0 ? fileUrls.join(',') : '';
};

/**
 * 处理身份证号验证
 * @param event  当前控件绑定的原生事件
 */
const handleValueIdCard = (event: Event): void => {
  const input = event.target as HTMLInputElement;
  const value = input.value;
  const idCardReg = /(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  const isValid = idCardReg.test(value);

  isIdCardError.value = !isValid;
};

/**
 * 打开选择人（审批人、抄送人、办理人）的弹框
 * @param item  当前选中流程节点的 node
 */
const handleSelectPeople = (item: ProcessNode): void => {
  currentActivity.value = item;
  selectPeopleTitle.value = item.properties.title;
  processErrorTip.value = {
    nodeId: undefined,
    message: undefined
  };
  isShowSelectPeople.value = true;
};

/**
 * 关闭选择审批人办理人的弹框
 */
const handleCloseShowSelectPeople = (): void => {
  isShowSelectPeople.value = false;
};
/**
 * 删除当前用户为审批人/办理人/抄送人的方法
 * @param activity 当前流程审核的系欸但
 * @param userItem 删除的用户数据
 */
const handleCloseUser = (activity: ProcessNode, userItem: any): void => {
  let activityStr = '审批人';
  if (activity.type === 'approver') {
    activityStr = '审批人';
  } else if (activity.type === 'audit') {
    activityStr = '办理人';
  } else if (activity.type === 'notifier') {
    activityStr = '抄送人';
  }

  ElMessageBox.confirm(`确认移除${activityStr}:${userItem.userName}吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const index = activity.properties.approvals.findIndex((item: any) => item.userId === userItem.userId);
      activity.properties.approvals.splice(index, 1);

      if (activity.properties.approvals.length <= 1) {
        activity.properties.actType = 'normal';
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

/**
 * 保存选择的人员信息
 */
const handleSaveSelectPeople = (): void => {
  if (currentActivity.value.properties.approvals?.length <= 1) {
    currentActivity.value.properties.actType = 'normal';
  }

  if (currentActivity.value.type === 'notifier') {
    if (currentActivity.value.properties.approvals?.length > 1) {
      currentActivity.value.properties.actType = 'and';
    } else {
      currentActivity.value.properties.actType = 'normal';
    }
  }

  isShowSelectPeople.value = false;
};
</script>
<style>
.svg-icon-item {
  width: 30px !important;
  height: 30px !important;
}
</style>
<style lang="scss" scoped>
.select-people-drawer__content {
  height: 100%;
  width: 100%;
  position: relative;
  padding-left: 16px;
  .option-box {
    height: 80px;
    .radio-item {
      width: 110px;
      padding: 6px;
    }
  }
  .select-people-drawer__footer {
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0;
    height: 60px;
    border-top: 1px solid #ededed;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: flex-end;
  }
}
.main {
  padding: 20px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .card {
    height: 100%;
    width: 100%;
  }
  .apply-header {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: #ededed solid 1px;
    width: 100%;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      .big-title {
        margin-left: 24px;
        font-size: 16px;
        font-weight: 600;
        color: var(--current-color);
      }
    }
    .center {
      flex: 2;
      color: #111112;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
    }
    .right {
      flex: 1;
      min-width: 200px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .apply-content {
    height: calc(100vh - 220px);
    width: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    .content-left {
      width: 50%;
      height: 100%;
      overflow: hidden;
      .left-title {
        font-size: 16px;
        font-weight: 600;
        color: #111112;
        height: 30px;
        line-height: 30px;
        background-color: #f8f8f8;
        width: 100%;
        text-align: center;
      }
      .left-form {
        height: calc(100% - 30px);
        overflow: hidden;
        margin-top: 10px;
        .el-form {
          height: 100%;
          overflow: auto;
        }
      }
    }
    .content-middle {
      width: 1px;
      height: 100%;
      border-right: 1px solid #8291a9;
      margin-left: 10px;
    }
    .content-right {
      width: 50%;
      height: 100%;
      overflow: hidden;
      .right-title {
        font-size: 16px;
        font-weight: 600;
        color: #111112;
        height: 30px;
        line-height: 30px;
        background-color: #f8f8f8;
        width: 100%;
        text-align: center;
      }
      .content {
        overflow: auto;
        height: calc(100% - 30px);
        .el-steps {
          padding-left: 16px;
          padding-top: 12px;
          height: 100%;
          :deep(.el-step) {
            min-height: 90px;
            .el-step__icon.is-text {
              border-radius: 0%;
              border: none;
              .svg-icon-item {
                width: 40px !important;
                height: 40px !important;
              }
            }
            .el-step__line {
              width: 2px;
              top: 30px;
              bottom: 8px;
              left: 11px;
            }
            .el-step__description {
              padding-right: 0%;
              margin-top: -5px;
              font-size: 12px;
              line-height: 30px;
              font-weight: 400;
              .desc-main {
                width: 100%;
                .desc-title {
                  color: #111112;
                  font-size: 14px;
                  font-weight: 600;
                  height: 30px;
                  line-height: 30px;
                  display: flex;
                }
                .desc-content {
                  display: flex;
                  flex-direction: row;
                  justify-content: flex-start;
                  width: 100%;
                  height: auto;
                  flex-wrap: wrap;
                  .desc-add-people {
                    width: 30px;
                    height: 28px;
                    line-height: 28px;
                    color: #409eff !important;
                    background: #ecf5ff !important;
                    border-radius: 4px;
                    text-align: center;
                    // border: 1px solid #b3d8ff;
                    i {
                      font-size: 16px;
                      font-weight: 600;
                      width: 30px;
                      height: 28px;
                    }
                  }
                  .desc-user {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    .desc-item {
                      margin: 0 4px;
                      .user-name-tag {
                        width: tag;
                      }
                    }
                  }
                }
              }
              .desc-err {
                //  当数据出错的时候显示的样式内容
                border: 1px solid red;
                border-radius: 8px;
                padding: 0 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
