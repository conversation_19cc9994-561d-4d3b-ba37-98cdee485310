<!-- 数据大屏 -->
<template>
  <container-card>
    <div class="dataScreen-main" v-loading.fullscreen.lock="fullscreenLoading">
      <div class="hanle-div">
        <el-button type="primary" @click="addBigScreen">新建大屏</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%; margin-top: 20px" border>
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="大屏名称" prop="title"></el-table-column>
        <el-table-column label="创建时间" prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-link type="primary" @click="checkScreen(scope.row)">查看</el-link>
            <el-link type="primary" @click="deitScreen(scope.row)" style="margin-left: 10px">编辑</el-link>
            <el-link
              v-show="scope.row.isDefault == 0"
              type="success"
              @click="settingDefault(scope.row)"
              style="margin-left: 10px"
              :disabled="getDisable(scope.row)"
              >设置为默认大屏</el-link
            >
            <el-link type="warning" v-show="scope.row.isDefault == 1" @click="cancalDefault(scope.row)" style="margin-left: 10px">取消默认</el-link>
            <el-link type="danger" @click="delScreen(scope.row)" style="margin-left: 10px">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { getScreenList, saveScreen, delScreen as delScreenApi } from '@/api/dataScreen';
// --- 定义变量 ---
const tableData = ref([]);
const fullscreenLoading = ref(false);
const searchMsg = reactive({
  pageNo: 1,
  pageSize: 10,
  groupName: '',
  moduleId: 0
});
const total = ref(0);
const moduleId = ref(0);
// --- 定义方法 ---
const addBigScreen = () => {
  window.open(`/addDataScreen/${moduleId.value}/0`);
};

const getData = () => {
  getScreenList(searchMsg).then((res) => {
    if (res.code == 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
// 编辑大屏
const deitScreen = (obj) => {
  window.open(`/addDataScreen/${moduleId.value}/${obj.id}`);
};

// 删除大屏
const delScreen = (obj) => {
  ElMessageBox.confirm('确定要删除该数据大屏吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delScreenApi({ id: obj.id }).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

// 修改大屏状态
const editStatus = (obj, num) => {
  const item = JSON.parse(JSON.stringify(obj));
  item.state = num;
  let message = '';
  if (num == 0) {
    //启用
    message = `确定要启用【${item.title}】吗`;
  } else {
    //停用
    message = `确定要停用【${item.title}】吗`;
  }
  ElMessageBox.confirm(message, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      fullscreenLoading.value = true;
      saveScreen(item).then((res) => {
        fullscreenLoading.value = false;
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};
/**
 * 设置默认大屏
 * @param row
 */
const settingDefault = (row: any) => {
  row.isDefault = 1;
  saveScreen(row).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      });
      getData();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 得到是否禁用
 * @param row
 */
const getDisable = (row: any) => {
  let flg = false;
  let num = 0;
  tableData.value.forEach((v) => {
    if (v.isDefault == 1) {
      num++;
    }
  });
  if (num != 0) {
    flg = true;
  }
  return flg;
};

/**
 * 取消默认
 * @param row
 */
const cancalDefault = (row: any) => {
  ElMessageBox.confirm('确定要取消该大屏为默认大屏吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      row.isDefault = 0;
      saveScreen(row).then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          });
          getData();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {});
};

// 查看大屏
const checkScreen = (obj) => {
  window.open(`/preview/${moduleId.value}/${obj.id}`);
};

onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
.dataScreen-main {
  overflow: auto;
  .hanle-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
