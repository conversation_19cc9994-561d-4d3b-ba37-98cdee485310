<template>
  <el-form label-width="100px" size="small">
    <el-form-item label="地图">
      <el-select v-model="attributeCopy.map">
        <el-option label="全国" value="areaChina" />
        <el-option label="贵州" value="areaGuizhou" />
        <el-option label="贵阳" value="areaGuiYang" />
        <el-option label="云岩区" value="areaYunYanQu" />
        <el-option label="陕西省延安市延川县" value="areaYanChuan" />
        <el-option label="毕节" value="areaBiJie" />
      </el-select>
    </el-form-item>
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="地图高度">
      <el-input v-model="attributeCopy.mapHeight" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="地图缩放">
      <el-input v-model="attributeCopy.distance" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="上下倾斜度">
      <el-input v-model="attributeCopy.alpha" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="左右倾斜度">
      <el-input v-model="attributeCopy.beta" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="是否显示名字">
      <el-switch v-model="attributeCopy.isShowLabel" active-text="是" inactive-text="否" />
    </el-form-item>
    <el-form-item label="字体颜色">
      <el-color-picker v-model="attributeCopy.labelColor" show-alpha />
    </el-form-item>
    <el-form-item label="文字大小">
      <el-input v-model="attributeCopy.fontSize" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="地图背景颜色">
      <el-color-picker v-model="attributeCopy.mapBackgroundColor" show-alpha />
    </el-form-item>
    <el-form-item label="分界线宽度">
      <el-input v-model="attributeCopy.borderWidth" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="分界线颜色">
      <el-color-picker v-model="attributeCopy.borderColor" show-alpha />
    </el-form-item>
    <el-form-item label="是否显示高亮">
      <el-switch v-model="attributeCopy.isShowLed" active-text="是" inactive-text="否" />
    </el-form-item>
    <el-form-item label="高亮文字颜色">
      <el-color-picker v-model="attributeCopy.ledColor" show-alpha />
    </el-form-item>
    <el-form-item label="地图高亮颜色">
      <el-color-picker v-model="attributeCopy.mapLedColor" show-alpha />
    </el-form-item>
    <el-form-item label="地图高亮分界线宽度">
      <el-input v-model="attributeCopy.mapLedBorderWidth" type="number" @keydown="handleInputKeydown"></el-input>
    </el-form-item>
    <el-form-item label="地图高亮分界线颜色">
      <el-color-picker v-model="attributeCopy.mapLedBorderColor" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-3dmap-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = computed(() => props.attribute);
const handleInputKeydown = (event: Event | KeyboardEvent) => {
  const e = event as KeyboardEvent;
  if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
    e.stopPropagation();
    e.preventDefault();
  }
};
</script>

<style scoped></style>
