<!--城市树的组件 -->
<template>
  <div class="city-area-continer">
    <el-cascader
      v-model="selectCityName"
      ref="cascaderHandle"
      :options="areaCodeListOptions"
      :props="cascaderProps"
      @change="handleChangeCity"
      class="city-area-cascader"
      :show-all-levels="false"
    ></el-cascader>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getAreaCode } from '@/api/project';
import { ElMessage } from 'element-plus';
import { useProjectStore } from '@/store/modules/project';
import type { CascaderProps, CascaderOption } from 'element-plus';

interface AreaNode extends CascaderOption {
  areaCode: string;
  areaName: string;
  children?: AreaNode[];
  parentCode?: string;
  [key: string]: any;
}

const store = useProjectStore();
const cascaderHandle = ref();
const areaCodeListOptions = ref<AreaNode[]>([]);
const selectCityName = ref<string[]>([]);

const loadNode = async (node: any, resolve: (data: AreaNode[]) => void) => {
  try {
    const areaCode = node.level === 0 ? '86' : node.data.areaCode;
    const res = await getAreaCode(areaCode);
    if (res.code === 200) {
      resolve(res.data);
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('加载地区数据失败:', error);
    ElMessage.error('加载地区数据失败');
  }
};

const cascaderProps: CascaderProps = {
  lazy: true,
  lazyLoad: loadNode,
  value: 'areaCode',
  label: 'areaName',
  expandTrigger: 'click' as const,
  children: 'children',
  checkStrictly: true
};

/**
 * 获取地区列表
 * @param areaCode 地区编码
 */
const getAreaCodeList = async (areaCode: number) => {
  try {
    const res = await getAreaCode(areaCode.toString());
    if (res.code === 200) {
      areaCodeListOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取地区列表失败:', error);
    ElMessage.error('获取地区列表失败');
  }
};

/**
 * 改变城市
 * @param val 值
 */
const handleChangeCity = (val: any) => {
  const areaCode = val[val.length - 1];
  sessionStorage.setItem('areaCode', areaCode);
  store.setCityCode(val);
  const pageSize = 10;
  const pageNum = 1;
  if (cascaderHandle.value) {
    cascaderHandle.value.dropDownVisible = false;
  }
  emit('getParaceList', areaCode, pageNum, pageSize);
};

const emit = defineEmits<{
  (e: 'getParaceList', areaCode: string, pageNum: number, pageSize: number): void;
}>();

onMounted(() => {
  getAreaCodeList(86);
});
</script>

<style lang="scss" scoped>
.city-area-continer {
  width: 355px;
  height: 40px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;

  .el-cascader {
    .el-input {
      :deep(.el-input__inner) {
        width: 200px;
        background-color: transparent !important;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
      }
    }
  }
}

:deep(.el-cascader .el-input .el-input__inner) {
  background-color: transparent !important;
  width: 345px;
  border: none;
  color: #ffffff;
}
</style>
