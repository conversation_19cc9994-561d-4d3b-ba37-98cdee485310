import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  ProjectParams,
  LogParams,
  DelProjectParams,
  CombineProjectParams,
  ProjectQueryParams,
  ProjectStatusParams,
  ProjectIdParams,
  ProjectUserParams,
  TaskParams,
  TaskStatusParams,
  ProjectSHPParams,
  ProjectNameParams,
  ZJParams
} from '@/api/projectData/types';

/**
 * 查询下级用户级项目--只列出项目信息不列出宗地、房屋等信息
 * @returns {AxiosPromise}
 */
export function getUserwithpts(): AxiosPromise<any> {
  return request({
    url: '/sm/pt/sub/userwithpts',
    method: 'get'
  });
}

/**
 * 查询多个项目详情
 * @param params 请求参数
 * @returns {AxiosPromise}
 */
export function getMoreDetail(params: ProjectParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/pts/detail',
    method: 'post',
    data: params
  });
}

/**
 * 获取宗地日志
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getLog(params: LogParams): AxiosPromise<any> {
  return request({
    url: '/sm/log/get/record',
    method: 'get',
    params: params
  });
}

/**
 * 删除项目
 * @param params 删除参数
 * @returns {AxiosPromise}
 */
export function delProject(params: DelProjectParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/delet/pts',
    method: 'post',
    data: params
  });
}

/**
 * 合并项目
 * @param params 合并参数
 * @returns {AxiosPromise}
 */
export function combineProject(params: CombineProjectParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/hb/copy',
    method: 'post',
    data: params
  });
}

/**
 * 获取可以修改新项目的采集员
 * @returns {AxiosPromise}
 */
export function getCJY(): AxiosPromise<any> {
  return request({
    url: '/sys/user/list/cusers',
    method: 'get'
  });
}

/**
 * 根据项目名字模糊查询项目
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getProjectForKey(params: ProjectQueryParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/select/name/like',
    method: 'get',
    params: params
  });
}

/**
 * 结束项目
 * @param params 项目状态参数
 * @returns {AxiosPromise}
 */
export function endProjectForId(params: ProjectStatusParams): AxiosPromise<any> {
  return request({
    url: `/sm/pt/start/end/${params.type}/${params.projectId}`,
    method: 'post'
  });
}

/**
 * 根据项目id返回项目信息
 * @param params 项目ID参数
 * @returns {AxiosPromise}
 */
export function getProjectForId(params: ProjectIdParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/user/detail/' + params.pid,
    method: 'get'
  });
}

/**
 * 根据项目id获取已有的可以修改人的信息
 * @param params 项目ID参数
 * @returns {AxiosPromise}
 */
export function getUsersForProject(params: ProjectIdParams): AxiosPromise<any> {
  return request({
    url: '/sys/user/newpt/modifier/' + params.pid,
    method: 'get'
  });
}

/**
 * 更新项目的修改人--管理端
 * @param params 项目用户参数
 * @returns {AxiosPromise}
 */
export function modifierProjectUser(params: ProjectUserParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/hb/modifier',
    method: 'post',
    data: params
  });
}

/**
 * 任务列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getOrder(params: TaskParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/task/list',
    method: 'get',
    params: params
  });
}

/**
 * 新建任务
 * @param params 任务参数
 * @returns {AxiosPromise}
 */
export function addOrder(params: TaskParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/task/add',
    method: 'post',
    data: params
  });
}

/**
 * 改变任务状态
 * @param params 任务状态参数
 * @returns {AxiosPromise}
 */
export function changeOrderStatus(params: TaskStatusParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/task/update',
    method: 'post',
    data: params
  });
}

/**
 * shp更新项目
 * @param params 项目SHP参数
 * @returns {AxiosPromise}
 */
export function modifyProjectForShp(params: ProjectSHPParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/modify/data',
    method: 'post',
    data: params
  });
}

/**
 * 导出报告
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoad(params: ProjectParams): AxiosPromise<Blob> {
  return request({
    url: '/project/export/word/djdcb',
    method: 'post',
    data: params,
    responseType: 'blob', // 将文件流转成blob对象
    headers: {
      noErrorMsg: true
    }
  });
}

/**
 * 更新项目名称
 * @param params 项目名称参数
 * @returns {AxiosPromise}
 */
export function modifyProjectName(params: ProjectNameParams): AxiosPromise<any> {
  return request({
    url: 'sm/pt/modify/project/title',
    method: 'post',
    data: params
  });
}

/**
 * 指界检查
 * @param params 指界参数
 * @returns {AxiosPromise}
 */
export function zjReferee(params: ZJParams): AxiosPromise<any> {
  return request({
    url: '/sm/pt/check/referee',
    method: 'post',
    data: params
  });
}

/**
 * 导出shp
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadSHP(params: ProjectSHPParams): AxiosPromise<any> {
  return request({
    url: '/qjt/project/shp/shp/export',
    method: 'post',
    data: params
  });
}

/**
 * 导出勘界shp
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadKJSHP(params: ProjectSHPParams): AxiosPromise<any> {
  return request({
    url: '/qjt/kj/project/shp',
    method: 'post',
    data: params
  });
}

/**
 * 导出gdb
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadGDB(params: ProjectSHPParams): AxiosPromise<any> {
  return request({
    url: '/qjt/project/gdb/remote/export',
    method: 'post',
    data: params
  });
}

/**
 * 宗地房产图上传
 * @param params 上传参数
 * @returns {AxiosPromise}
 */
export function zdfcUpload(params: ProjectParams): AxiosPromise<any> {
  return request({
    url: '/object/opt/update/zdfctu',
    method: 'post',
    data: params
  });
}

/**
 * 上传到文件服务器
 * @param params 上传参数
 * @returns {AxiosPromise}
 */
export function zdfcUploadFile(params: ProjectParams): AxiosPromise<any> {
  return request({
    url: '/file/service/upload',
    method: 'post',
    data: params
  });
}

/**
 * 导出shp征地
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadSHPZD(params: ProjectSHPParams): AxiosPromise<any> {
  return request({
    url: '/qjt/land/shp/export',
    method: 'post',
    data: params
  });
}
