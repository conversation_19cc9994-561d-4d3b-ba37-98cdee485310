<template>
  <dv-scroll-ranking-board :config="config" :style="{ width: width + 'px', height: height + 'px' }" />
</template>

<script setup lang="ts">
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-dataV-scrollList'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const config = ref<any>({});
const uuid = ref(null);

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadData(newObj);
  },
  { deep: true } //深度监听
);
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  const url = window.location.href;
  const list = url.split('/');
  const moduleId = list[list.length - 2];

  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      taskId: taskId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        const list = [];
        res.data.listList.forEach((v) => {
          list.push({
            name: v[0],
            value: v[1]
          });
        });
        config.value = JSON.parse(JSON.stringify(props.option.attribute));
        config.value.data = list;
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      config.value = JSON.parse(JSON.stringify(props.option.attribute));
      config.value.data = res;
    });
  }
};
// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});
</script>

<style scoped></style>
