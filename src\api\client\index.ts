import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ClientData, ClientQuery } from '@/api/client/types';

/**
 * 获取邀请客户的列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getClientList(params: ClientQuery): AxiosPromise<any> {
  return request({
    url: '/system/custInvitation/list',
    method: 'get',
    params: params
  });
}

/**
 * 邀请客户的提交按钮---提交
 * @param params 客户数据
 * @returns {AxiosPromise}
 */
export function addClient(params: ClientData): AxiosPromise<any> {
  return request({
    url: '/system/custInvitation/add',
    method: 'post',
    params: params
  });
}

/**
 * 邀请客户的提交按钮---修改
 * @param params 客户数据
 * @returns {AxiosPromise}
 */
export function updateClient(params: ClientData): AxiosPromise<any> {
  return request({
    url: '/system/custInvitation/update',
    method: 'post',
    params: params
  });
}
