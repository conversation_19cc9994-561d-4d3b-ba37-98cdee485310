<!-- 控件 -->
<template>
  <div
    class="NewDraggableItem-main"
    :class="[
      field.status === 0 ? 'inactive-item' : activeId === field.formId ? 'active-item' : '',
      activeId !== field.formId && isShowHandle && field.tag !== 'el-table' ? 'show-item' : '',
      field.tag === 'el-table' ? 'default-item' : ''
    ]"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- TODO 这里执行删除的方法 -->
    <div class="del-ico" v-show="isShowHandle && (field.status === 0 || !field.id)" @click="delItem">
      <el-icon><Close /></el-icon>
    </div>
    <div class="label">
      <span class="red" v-show="field.required">*</span>
      {{ field.label }}
      <el-icon v-if="field.expression" style="color: red; margin-left: 10px"><InfoFilled /></el-icon>
    </div>
    <!-- 表格控件 -->
    <template v-if="field.tag === 'el-table'">
      <draggable class="drawing-board" :list="field.children" :animation="200" group="componentsGroup">
        <template #item="{ element, index }">
          <div
            class="child-row"
            :class="{ 'show-child': element.isShowHandle, 'active-child': activeId === element.formId }"
            @click.stop="handleClickChild(element)"
            @mouseenter="handleMouseEnterChild(element)"
            @mouseleave="handleMouseLeaveChild(element)"
          >
            <div class="del-ico-child" v-show="element.isShowHandle" @click="delItemChild(index, field)">
              <el-icon><Close /></el-icon>
            </div>
            <div class="child-label">{{ element.label }}</div>
            <draggableTemp :fieldProp="element"></draggableTemp>
          </div>
        </template>
      </draggable>
    </template>
    <!-- 常规控件 -->
    <template v-else>
      <draggableTemp :fieldProp="field"></draggableTemp>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElDatePicker } from 'element-plus';
import draggableTemp from './draggableTemp.vue';
import draggable from 'vuedraggable';
interface field {
  tag: string;
  label: string;
  formId: number;
  content: string;
  placeholder: string;
  tagIcon: string;
  style: {
    width: string;
  };
  required: boolean;
  type: string;
  children: field[];
  isShowHandle: boolean;
}
// props 传入
const props = defineProps<{
  element: field;
  index: number;
  activeId: number;
}>();
// emit
const emit = defineEmits(['activeChild', 'deleteItem']);
// 双向绑定数据
// const field = ref<field>(props.element);
const field = computed({
  get() {
    return props.element;
  },
  set(val) {}
});
const isShowHandle = ref<boolean>(false);
// 方法
const handleMouseEnter = () => {
  isShowHandle.value = true;
};
const handleMouseLeave = () => {
  isShowHandle.value = false;
};
const handleMouseEnterChild = (item: field) => {
  item.isShowHandle = true;
};
const handleMouseLeaveChild = (item: field) => {
  item.isShowHandle = false;
};
const activeIdChild = ref<number>(0);
/**
 * 点击表格字段的子项内容
 * @param item 子项
 */
const handleClickChild = (item: field) => {
  emit('activeChild', item);
};
/**
 * 删除字段
 */
const delItem = () => {
  ElMessageBox.confirm('确定要删除该字段吗？如果确定删除,则会删除所有已采集的数据！！！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('deleteItem', props.index, false);
    })
    .catch(() => {});
};
/**
 * 删除表格字段子项
 * @param index 子项下标
 * @param field 父项
 */
const delItemChild = (index: number, field: any) => {
  ElMessageBox.confirm('确定要删除该字段吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('deleteItem', index, true, field);
    })
    .catch(() => {});
};
onMounted(() => {
  //   if (field.value.tag === 'el-date-picker' && field.value.type === 'daterange') {
  //     field.value.content = [];
  //   }
});
</script>
<style lang="css" scoped>
.NewDraggableItem-main {
  position: relative;
  width: 100%;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  cursor: move;
  border: solid 1px #fff;
  .label {
    margin-bottom: 10px;
    font-weight: bold;
    color: rgb(0, 0, 0, 0.5);
    .red {
      color: red;
      margin-right: 5px;
    }
  }
  .del-ico {
    position: absolute;
    top: 0px;
    right: 5px;
    width: 18px;
    height: 18px;
    background: #38adff;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
  }
  .child-row {
    padding: 10px;
    cursor: move;
    border: solid 1px #fff;
    border-radius: 4px;
    margin-bottom: 5px;
    position: relative;
    .child-label {
      margin-bottom: 5px;
    }
    .del-ico-child {
      position: absolute;
      top: 0px;
      right: 5px;
      width: 18px;
      height: 18px;
      background: #38adff;
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
  .show-child {
    border: 1px dashed #38adff;
    background: #f6f7ff;
  }
  .active-child {
    border: 1px solid #38adff;
    background: #f6f7ff;
  }
}
.active-item {
  border: 1px solid #38adff !important;
  background: #f6f7ff;
}
.inactive-item {
  border: 1px solid #d3d3d3 !important;
  background: #f6f7f8;
}
.show-item {
  border: 1px dashed #38adff;
  background: #f6f7ff;
}
.default-item {
  border: 1px dashed #d3d3d3;
  background: #fff !important;
}
</style>
