// 定义组件配置接口
interface ComponentConfig {
  label: string;
  tag: string;
  tagIcon: string;
  icon: string;
  placeholder?: string;
  defaultValue?: any;
  span?: number;
  labelWidth?: number | null;
  style?: Record<string, any>;
  clearable?: boolean;
  prepend?: string;
  append?: string;
  'prefix-icon'?: string;
  'suffix-icon'?: string;
  maxlength?: number | null;
  'show-word-limit'?: boolean;
  readonly?: boolean;
  disabled?: boolean;
  required?: boolean;
  regList?: { pattern: string; message: string } | any[];
  changeTag?: boolean;
  proCondition?: boolean;
  asSummary?: boolean;
  content?: string;
  fieldType?: string;
  type?: string;
  autosize?: {
    minRows: number;
    maxRows: number;
  };
  min?: number;
  max?: number;
  step?: number;
  'step-strictly'?: boolean;
  precision?: number;
  'controls-position'?: string;
  filterable?: boolean;
  multiple?: boolean;
  options?: Array<{
    label: string;
    value: number;
  }>;
  'picker-options'?: {
    selectableRange: string;
  };
  format?: string;
  'value-format'?: string;
  'is-range'?: boolean;
  'range-separator'?: string;
  'start-placeholder'?: string;
  'end-placeholder'?: string;
  action?: string;
  accept?: string;
  name?: string;
  'auto-upload'?: boolean;
  showTip?: boolean;
  buttonText?: string;
  fileSize?: number;
  sizeUnit?: string;
  'list-type'?: string;
  layout?: string;
  vModel?: string;
  isPlaceholder?: boolean;
  defalutSelect?: number;
  delFlag?: number;
  expression?: any;
  isHide?: boolean;
}

// 定义表单配置接口
interface FormConfig {
  formRef: string;
  size: 'default' | 'small' | 'large';
  labelPosition: 'left' | 'right' | 'top';
  labelWidth: number;
  gutter: number;
  disabled: boolean;
  span: number;
  formBtns: boolean;
}

// 定义触发器配置接口
interface TriggerConfig {
  [key: string]: string;
}

// 导出表单配置
export const formConf: FormConfig = {
  formRef: 'elForm',
  size: 'small',
  labelPosition: 'right',
  labelWidth: 100,
  gutter: 15,
  disabled: false,
  span: 24,
  formBtns: true
};

// 导出输入型组件配置
export const inputComponents: ComponentConfig[] = [
  {
    label: '单行输入框',
    tag: 'el-input',
    tagIcon: 'Cpu',
    icon: 'input',
    placeholder: '请输入',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: undefined,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: undefined,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    fieldType: 'String'
  },
  {
    label: '多行输入框',
    tag: 'el-input',
    tagIcon: 'Cpu',
    icon: 'textarea',
    type: 'textarea',
    placeholder: '请输入',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: undefined,
    span: 24,
    labelWidth: null,
    autosize: {
      minRows: 4,
      maxRows: 4
    },
    style: { width: '100%' },
    maxlength: undefined,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    fieldType: 'String'
  },
  {
    label: '数字输入框',
    tag: 'el-input-number',
    tagIcon: 'Cpu',
    icon: 'number',
    placeholder: '请输入',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: undefined,
    style: { width: null },
    span: 24,
    labelWidth: null,
    min: undefined,
    max: undefined,
    step: undefined,
    'step-strictly': false,
    precision: undefined,
    'controls-position': 'right',
    disabled: false,
    required: false,
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    fieldType: 'Double'
  }
];

// 导出选择型组件配置
export const selectComponents: ComponentConfig[] = [
  {
    label: '下拉选择',
    tag: 'el-select',
    tagIcon: 'Cpu',
    icon: 'select',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    style: { width: '100%' },
    defaultValue: undefined,
    span: 24,
    labelWidth: null,
    clearable: true,
    disabled: false,
    required: false,
    filterable: false,
    multiple: false,
    options: [
      {
        label: '选项1',
        value: 1
      },
      {
        label: '选项2',
        value: 2
      }
    ],
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    fieldType: 'String'
  },
  {
    label: '单选框组',
    tag: 'el-radio-group',
    tagIcon: 'Cpu',
    icon: 'radio',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: undefined,
    span: 24,
    labelWidth: null,
    style: {},
    optionType: 'default',
    border: false,
    size: 'medium',
    disabled: false,
    required: false,
    options: [
      {
        label: '选项1',
        value: 1
      },
      {
        label: '选项2',
        value: 2
      }
    ],
    regList: [],
    changeTag: true,
    proCondition: true,
    content: '',
    fieldType: 'String'
  },
  {
    label: '多选框组',
    tag: 'el-checkbox-group',
    tagIcon: 'Cpu',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    icon: 'checkbox',
    defaultValue: [],
    span: 24,
    labelWidth: null,
    style: {},
    optionType: 'default',
    border: false,
    size: 'medium',
    disabled: false,
    required: false,
    options: [
      {
        label: '选项1',
        value: 1
      },
      {
        label: '选项2',
        value: 2
      }
    ],
    regList: [],
    changeTag: true,
    proCondition: true,
    asSummary: false,
    content: '',
    fieldType: 'String'
  },
  {
    label: '时间选择',
    tag: 'el-time-picker',
    tagIcon: 'Cpu',
    icon: 'time',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: null,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    'picker-options': {
      selectableRange: '00:00:00-23:59:59'
    },
    format: 'HH:mm:ss',
    'value-format': 'timestamp',
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    fieldType: 'Date'
  },
  {
    label: '时间范围',
    tag: 'el-time-picker',
    showDuration: false,
    tagIcon: 'Cpu',
    icon: 'time-range',
    defaultValue: null,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    'is-range': true,
    'range-separator': '至',
    'start-placeholder': '开始时间',
    'end-placeholder': '结束时间',
    format: 'HH:mm:ss',
    'value-format': 'timestamp',
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    isHide: true,
    fieldType: 'Date'
  },
  {
    label: '日期选择',
    tag: 'el-date-picker',
    tagIcon: 'Cpu',
    icon: 'date',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: null,
    type: 'date',
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    required: false,
    format: 'yyyy-MM-dd',
    'value-format': 'timestamp',
    readonly: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    fieldType: 'Date'
  },
  {
    label: '日期范围',
    tag: 'el-date-picker',
    showDuration: false,
    tagIcon: 'Cpu',
    placeholder: '请选择',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    icon: 'date-range',
    defaultValue: null,
    span: 24,
    labelWidth: null,
    style: { width: '100%' },
    type: 'daterange',
    'range-separator': '至',
    'start-placeholder': '开始日期',
    'end-placeholder': '结束日期',
    disabled: false,
    clearable: true,
    required: false,
    format: 'yyyy-MM-dd',
    'value-format': 'timestamp',
    readonly: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    isHide: true,
    fieldType: 'Date'
  },
  {
    label: '附件',
    tag: 'el-upload',
    tagIcon: 'Cpu',
    icon: 'upload',
    action: 'https://jsonplaceholder.typicode.com/posts/',
    defaultValue: [],
    labelWidth: null,
    disabled: false,
    required: false,
    accept: 'image',
    name: 'file',
    'auto-upload': true,
    showTip: false,
    buttonText: '点击上传附件',
    fileSize: 20,
    sizeUnit: 'MB',
    'list-type': 'text',
    multiple: false,
    regList: [],
    changeTag: true,
    proCondition: false,
    asSummary: false,
    content: '',
    fieldType: 'String'
  },
  {
    layout: 'colFormItem',
    tagIcon: 'Cpu',
    icon: 'phoneIcon',
    label: '手机号',
    vModel: 'mobile',
    tag: 'el-input',
    placeholder: '',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: '',
    defalutSelect: 1,
    delFlag: 0,
    expression: undefined,
    span: 24,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': 'el-icon-mobile',
    'suffix-icon': '',
    maxlength: 11,
    'show-word-limit': true,
    readonly: false,
    disabled: false,
    required: false,
    changeTag: true,
    proCondition: false,
    regList: {
      pattern: '/1[2-9]\\d{9}/',
      message: '手机号格式错误'
    },
    fieldType: 'String'
  },
  {
    layout: 'colFormItem',
    tagIcon: 'Cpu',
    label: '身份证号码',
    vModel: 'idCrad',
    icon: 'idCardIcon',
    tag: 'el-input',
    placeholder: '',
    // 设置编辑框是否允许编辑
    isPlaceholder: false,
    defaultValue: '',
    defalutSelect: 1,
    delFlag: 0,
    expression: undefined,
    span: 24,
    style: { width: '100%' },
    clearable: true,
    prepend: '',
    append: '',
    'prefix-icon': 'el-icon-user-solid',
    'suffix-icon': '',
    maxlength: 18,
    'show-word-limit': true,
    readonly: false,
    disabled: false,
    required: false,
    changeTag: true,
    proCondition: false,
    regList: [
      {
        pattern: /\d{17}[0-9Xx]|\d{15}/,
        message: '身份证输入错误'
      }
    ],
    fieldType: 'String'
  }
];

// 导出通用组件配置
export const commonComponents: ComponentConfig[] = [...inputComponents, ...selectComponents].map((t) => Object.assign({ cmpType: 'common' }, t));

// 获取配置函数
export const getConfigByTag = (targetList: ComponentConfig[], tag: string): ComponentConfig | undefined => {
  return targetList.find((t) => t.tag === tag);
};

// 复制配置函数
export const copyConfigAsCustom = (rowConf: any, childrenConf: any[]): void => {
  const clone = (target: ComponentConfig, conf = {}): ComponentConfig => {
    const template = JSON.parse(JSON.stringify(target));
    return Object.assign({}, template, { cmpType: 'custom' }, conf);
  };
};

// 导出触发器配置
export const trigger: TriggerConfig = {
  'el-input': 'blur',
  'el-input-number': 'blur',
  'el-select': 'change',
  'el-radio-group': 'change',
  'el-checkbox-group': 'change',
  'el-cascader': 'change',
  'el-time-picker': 'change',
  'el-date-picker': 'change',
  'el-rate': 'change',
  'fc-amount': 'change',
  'fc-time-duration': 'change',
  'fc-date-duration': 'change',
  'fc-org-select': 'input'
};
