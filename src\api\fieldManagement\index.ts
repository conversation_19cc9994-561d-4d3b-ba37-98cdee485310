import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FieldData, FieldQuery } from '@/api/fieldManagement/types';

/**
 * 获取字段列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getfieldList(params: FieldQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/field/search',
    method: 'post',
    data: params
  });
}

/**
 * 新增字段
 * @param params 字段数据
 * @returns {AxiosPromise}
 */
export function addField(params: FieldData): AxiosPromise<any> {
  return request({
    url: '/qjt/field/add',
    method: 'post',
    data: params
  });
}

/**
 * 修改字段
 * @param params 字段数据
 * @returns {AxiosPromise}
 */
export function updateField(params: FieldData): AxiosPromise<any> {
  return request({
    url: '/qjt/field/modify',
    method: 'post',
    data: params
  });
}

/**
 * 禁用、启用字段
 * @param params 字段数据
 * @param type 操作类型
 * @returns {AxiosPromise}
 */
export function disableField(params: FieldData, type: string): AxiosPromise<any> {
  return request({
    url: `/qjt/field/opt/${type}`,
    method: 'post',
    data: params
  });
}

/**
 * 通过模块查询字段表达式排序
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function insertFieldOrderList(params: FieldData): AxiosPromise<any> {
  return request({
    url: `/tool/field/insertFieldOrder`,
    method: 'post',
    data: params
  });
}

/**
 * 刷新表达式字段获取排序的接口
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function fieldOrderList(params: FieldQuery): AxiosPromise<any> {
  return request({
    url: `/tool/field/selectFieldOrder`,
    method: 'post',
    params: params
  });
}

/**
 * 表达式自动刷新
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function saveFieldQuote(params: FieldQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/field/saveFieldQuote`,
    method: 'post',
    params: params
  });
}
