<template>
  <div>
    <div class="user-info-head" @click="editCropper()">
      <img :src="options.img" title="点击上传头像" class="img-circle img-lg" />
    </div>
    <el-dialog v-model="open" :title="title" width="800px" append-to-body @opened="modalOpened" @close="closeDialog" :close-on-click-modal="false">
      <el-row>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <vue-cropper
            ref="cropperRef"
            :img="options.img"
            :info="true"
            :auto-crop="options.autoCrop"
            :auto-crop-width="options.autoCropWidth"
            :auto-crop-height="options.autoCropHeight"
            :fixed-box="options.fixedBox"
            :output-type="options.outputType"
            @real-time="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <div class="avatar-upload-preview">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload action="#" :http-request="requestUpload" :show-file-list="false" :before-upload="beforeUpload">
            <el-button size="small">
              选择
              <el-icon class="el-icon--right"><Upload /></el-icon>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{ span: 1, offset: 2 }" :sm="2" :xs="2">
          <el-button size="small" @click="changeScale(1)">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button size="small" @click="changeScale(-1)">
            <el-icon><Minus /></el-icon>
          </el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button size="small" @click="rotateLeft()">
            <el-icon><RefreshLeft /></el-icon>
          </el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :sm="2" :xs="2">
          <el-button size="small" @click="rotateRight()">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </el-col>
        <el-col :lg="{ span: 2, offset: 6 }" :sm="2" :xs="2">
          <el-button type="primary" size="small" @click="uploadImg()">提 交</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onBeforeUnmount, computed, watch } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { VueCropper } from 'vue-cropper';
import 'vue-cropper/dist/index.css';
import { uploadAvatar } from '@/api/system/user';
import { debounce } from '@/utils';
import { ElMessage } from 'element-plus';
import { Plus, Minus, RefreshLeft, RefreshRight, Upload } from '@element-plus/icons-vue';
import type { UploadRequestOptions } from 'element-plus';

// 定义props
interface UserProps {
  user?: Record<string, any>;
}

// 获取store实例
const userStore = useUserStore();

// refs
const cropperRef = ref();

// 响应式数据
const open = ref(false);
const visible = ref(false);
const title = ref('修改头像');
const previews = ref<Record<string, any>>({});
const resizeHandler = ref<(() => void) | null>(null);
const base = ref(import.meta.env.VITE_APP_BASE_API);

// 裁剪选项
const currentImg = ref(userStore.avatar);
const options = reactive({
  img: currentImg.value
    ? currentImg.value.startsWith('http')
      ? currentImg.value
      : import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/' + currentImg.value
    : '', // 裁剪图片的地址
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 200, // 默认生成截图框宽度
  autoCropHeight: 200, // 默认生成截图框高度
  fixedBox: true, // 固定截图框大小 不允许改变
  outputType: 'png', // 默认生成截图为PNG格式
  name: '' // 图片的名字
});

// 监听 avatar 变化
watch(
  () => userStore.avatar,
  (newVal) => {
    currentImg.value = newVal;
    if (newVal) {
      options.img = newVal.startsWith('http') ? newVal : import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/' + newVal;
    }
  }
);

// 编辑头像
const editCropper = () => {
  open.value = true;
};

// 打开弹出层结束时的回调
const modalOpened = () => {
  visible.value = true;
  if (!resizeHandler.value) {
    resizeHandler.value = debounce(
      () => {
        refresh();
      },
      100,
      false
    );
  }
  window.addEventListener('resize', resizeHandler.value);
};

// 刷新组件
const refresh = () => {
  cropperRef.value?.refresh();
};

// 覆盖默认的上传行为
const requestUpload = (options: UploadRequestOptions): XMLHttpRequest => {
  // 不做任何事，仅为阻止默认上传
  return new XMLHttpRequest();
};

// 向左旋转
const rotateLeft = () => {
  cropperRef.value?.rotateLeft();
};

// 向右旋转
const rotateRight = () => {
  cropperRef.value?.rotateRight();
};

// 图片缩放
const changeScale = (num: number) => {
  num = num || 1;
  cropperRef.value?.changeScale(num);
};

// 上传预处理
const beforeUpload = (file: File) => {
  if (file.type.indexOf('image/') === -1) {
    ElMessage.error('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。');
  } else {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      options.img = reader.result as string;
      options.name = file.name;
    };
  }
};

// 上传图片
const uploadImg = () => {
  cropperRef.value?.getCropBlob((data: Blob) => {
    const formData = new FormData();
    formData.append('avatarfile', data, options.name);
    uploadAvatar(formData).then((response: any) => {
      open.value = false;
      // 更新图片地址
      currentImg.value = response.imgUrl;
      // 更新到store
      userStore.setAvatar(response.imgUrl);
      ElMessage.success('修改成功');
      visible.value = false;
    });
  });
};

// 实时预览
const realTime = (data: Record<string, any>) => {
  previews.value = data;
};

// 关闭窗口
const closeDialog = () => {
  currentImg.value = userStore.avatar;
  visible.value = false;
  if (resizeHandler.value) {
    window.removeEventListener('resize', resizeHandler.value);
  }
};

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  if (resizeHandler.value) {
    window.removeEventListener('resize', resizeHandler.value);
  }
});

// 暴露组件方法供父组件调用
defineExpose({
  editCropper
});
</script>

<style scoped lang="scss">
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  border-radius: 50%;
}
</style>
