<!-- 根据部门选择人员 -->
<template>
  <div class="chooseUser-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      class="container-box"
      title="人员选择"
      v-model="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="handleClose"
      @open="handleOpen"
      width="600px"
      :before-close="handleClose"
    >
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">部门</span></div>
          <div class="content">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              ref="treeRef"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            />
          </div>
        </div>
        <div class="right">
          <div class="title-div">
            <span class="normal-sapn">成员({{ userList.length }})</span>
          </div>
          <div class="content-handle">
            <el-input placeholder="请输入用户名筛选" clearable v-model="queryParams.custName" @keyup.enter="getList" />
            <el-button style="margin-left: 10px; margin-right: 5px" type="primary" @click="getList">搜索</el-button>
          </div>
          <div class="content-handle" v-show="userList.length != 0">
            <el-checkbox v-model="checkedAll" @change="checkAll">全选</el-checkbox>
          </div>
          <div class="content" style="height: calc(100% - 120px)">
            <div class="flex-row" v-for="(item, index) in userList" :key="index">
              <div class="label">{{ item.custName }}</div>
              <el-checkbox v-model="item.checked" style="margin-right: 10px" :disabled="item.disable" @change="changeUser(item)" />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submit">确 认</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { deptTreeSelect, listUser } from '@/api/system/user';
import type { ElTree } from 'element-plus';

// 类型定义
interface User {
  userId: string | number;
  deptId: number;
  userName: string;
  nickName: string;
  custName: string;
  deptName: string;
  checked?: boolean;
  disable?: boolean;
  deptIds?: number[];
  deptNames?: string;
}

interface DeptNode {
  id: string | number;
  label: string;
  children?: DeptNode[];
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  custName?: string;
  phonenumber?: string;
  status?: string;
  deptId?: number;
  userId?: number;
}

interface Props {
  userDialog: boolean;
  users: User[];
  flowList: any[];
  isShowDisable: boolean;
}

// Props
const props = withDefaults(defineProps<Props>(), {
  userDialog: false,
  users: () => [],
  flowList: () => [],
  isShowDisable: false
});

// Emits
const emit = defineEmits(['chageUserDialog', 'getChooseUser']);

// Refs
const treeRef = ref<InstanceType<typeof ElTree>>();
const fullscreenLoading = ref(false);
const dialogVisible = ref(false);
const checkedAll = ref(false);
const deptOptions = ref<DeptNode[]>([]);
const userList = ref<User[]>([]);
const chooseList = ref<User[]>([]);
const nowDeptName = ref('');

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 1000000,
  custName: undefined,
  phonenumber: undefined,
  status: undefined,
  deptId: undefined,
  userId: undefined
});

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'label'
};

/**
 * 监听用户列表
 */
watch(
  () => props.users,
  (val) => {
    chooseList.value = JSON.parse(JSON.stringify(val));
  },
  { deep: true }
);

watch(
  () => props.userDialog,
  (val) => {
    dialogVisible.value = val;
  }
);

/**
 * 打开用户选择并获取部门树
 */
const handleOpen = () => {
  getDeptTree();
};
/**
 * 关闭用户选择
 */
const handleClose = () => {
  emit('chageUserDialog', false);
};

/**
 * 获取部门树
 */
const getDeptTree = async () => {
  try {
    const response = await deptTreeSelect();
    deptOptions.value = response.data;
    nowDeptName.value = response.data[0].label;
    await treeRef.value?.setCurrentKey(Number(response.data[0].id));
    handleNodeClick(response.data[0]);
  } catch (error) {
    console.error('获取部门树失败:', error);
  }
};

/**
 * 处理节点点击
 * @param data 部门节点
 */
const handleNodeClick = (data: DeptNode) => {
  nowDeptName.value = data.label;
  queryParams.deptId = Number(data.id);
  handleQuery();
};

/**
 * 处理查询
 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/**
 * 获取用户列表
 */
const getList = async () => {
  fullscreenLoading.value = true;
  try {
    const response = await listUser(queryParams);
    userList.value = response.rows.map((user) => ({
      ...user,
      custName: user.custName || user.nickName || user.userName,
      deptName: user.deptName || '',
      checked: false
    }));

    if (props.isShowDisable) {
      userList.value.forEach((user) => {
        setUserDisable(props.flowList, user);
      });
    }

    // 反显已选择的用户
    if (chooseList.value.length > 0) {
      userList.value.forEach((user) => {
        if (chooseList.value.some((selected) => selected.userId === user.userId)) {
          user.checked = true;
        }
      });
    }

    isCheckedAll();
  } catch (error) {
    console.error('获取用户列表失败:', error);
  } finally {
    fullscreenLoading.value = false;
  }
};

/**
 * 设置用户禁用
 * @param list 列表
 * @param user 用户
 */
const setUserDisable = (list: any[], user: User) => {
  for (const item of list) {
    if (item.users?.some((u: User) => u.userId === user.userId) && !props.users.some((u) => u.userId === user.userId)) {
      user.disable = true;
      break;
    }
    if (item.list?.length > 0) {
      setUserDisable(item.list, user);
    }
  }
};

/**
 * 改变用户
 * @param user 用户
 */
const changeUser = (user: User) => {
  if (user.checked) {
    chooseList.value.push({
      userId: user.userId,
      deptId: user.deptIds?.[0] || 0,
      userName: user.userName,
      nickName: user.nickName,
      custName: user.custName,
      deptName: user.deptName || ''
    });
  } else {
    const index = chooseList.value.findIndex((item) => item.userId === user.userId);
    if (index > -1) {
      chooseList.value.splice(index, 1);
    }
  }
  isCheckedAll();
};

/**
 * 提交
 */
const submit = () => {
  if (chooseList.value.length === 0) {
    ElMessage.error('您未选择人员');
    return;
  }
  emit('getChooseUser', chooseList.value);
};

/**
 * 全选
 * @param val 值
 */
const checkAll = (val: boolean | string | number) => {
  const currentDeptUsers = userList.value;
  const selectedUsers = chooseList.value.filter((x) => currentDeptUsers.some((y) => y.userId === x.userId));

  if (val) {
    const newUsers = currentDeptUsers.filter((x) => !selectedUsers.some((y) => y.userId === x.userId) && !x.disable);

    newUsers.forEach((user) => {
      chooseList.value.push({
        userId: user.userId,
        deptId: user.deptIds?.[0] || 0,
        userName: user.userName,
        nickName: user.nickName,
        custName: user.custName,
        deptName: user.deptName || ''
      });
    });

    currentDeptUsers.forEach((user) => {
      if (!user.disable) {
        user.checked = true;
      }
    });
  } else {
    const remainingUsers = chooseList.value.filter((x) => !currentDeptUsers.some((y) => y.userId === x.userId));

    chooseList.value = remainingUsers;
    currentDeptUsers.forEach((user) => {
      user.checked = false;
    });
  }
};

/**
 * 获取不同数组
 * @param allArr 所有数组
 * @param partArr 部分数组
 * @param field 字段
 * @returns 不同数组
 */
const getDifferentArr = (allArr: any[], partArr: any[], field: string) => {
  return allArr.filter((item) => !partArr.some((part) => part[field] === item[field]));
};
/**
 * 是否全选
 */
const isCheckedAll = () => {
  const checkedCount = userList.value.filter((user) => user.checked).length;
  checkedAll.value = checkedCount === userList.value.length;
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body) {
  overflow: hidden;
}

.dialog-box {
  height: 400px;
  border: 1px solid rgba(219, 231, 238, 1);
  border-radius: 6px;
  display: flex;
  flex-direction: row;

  .left {
    flex: 2;
  }

  .right {
    flex: 3;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }

  .title-div {
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;

    .normal-sapn {
      margin-left: 20px;
    }
  }

  .content-handle {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    padding-left: 20px;
  }

  .content {
    height: calc(100% - 67px);
    padding: 0px 8px;
    width: calc(100% - 16px);
    margin-left: 8px;
    overflow: auto;

    .el-tree-node__content {
      height: 32px;
      font-size: 12px;
    }

    .flex-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .label {
        font-size: 12px;
        padding-left: 12px;
      }
    }

    .flex-row:hover {
      background-color: #f5f7fa;
    }
  }

  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }

  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }

  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
</style>
