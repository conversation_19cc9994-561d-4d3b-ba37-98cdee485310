<!-- 林业 -->
<template>
  <div class="zongdi-detail-info-contianer">
    <div class="title-div">
      {{ checkedLinye.title }}
      <span class="icon" @click="handleColseZongDiInfo">
        <i class="el-icon-circle-close"></i>
      </span>
    </div>
    <div class="tab-div">
      <div class="tab-item" v-for="item in selectTitleList" :key="item.value" :class="{ active: item.isCkeck }" @click="changeTab(item)">
        {{ item.label }}
      </div>
    </div>
    <!-- 图形信息 -->
    <template v-if="currentValue === 1">
      <el-tree :data="graphicalList" node-key="id" :props="defaultProps" :expand-on-click-node="true" default-expand-all @node-click="checkedNode">
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span v-if="data.level === 1" class="zd-icon">
              <!-- 宗地图标 -->
              <el-image :src="zdIcon"></el-image>
            </span>
            <span v-if="data.level === 2" class="zd-icon">
              <!-- 样方 图标 -->
              <el-image :src="yangf"></el-image>
            </span>
            <span class="zd-name">{{ node.label }}</span>
            <span class="zd-leng" v-if="data.children && data.children.length > 0">({{ data.children.length }})</span>
            <template v-if="data.children">
              <span class="zd-arrow" v-if="!data.isExpand && data.level !== 4 && data.type !== 'sz'"><i class="el-icon-arrow-right"></i></span>
              <span class="zd-arrow" v-if="data.isExpand && data.level !== 4 && data.type !== 'sz'"><i class="el-icon-arrow-down"></i></span>
            </template>
          </span>
        </template>
      </el-tree>
    </template>
    <!-- 家庭成员 -->
    <template v-if="currentValue === 2">
      <div class="content">
        <div class="family-item" v-for="(item, index) in checkedLinye.obligeeModelList" :key="index">
          <div class="family-title">权利人{{ index + 1 }}</div>
          <div class="family-row">
            <div class="family-ite">
              <div class="label">姓名：</div>
              {{ item.qlrxm }}
            </div>
            <div class="family-ite">
              <div class="label">手机号:</div>
              {{ item.phone }}
            </div>
          </div>
          <div class="family-row">
            <div class="family-ite">
              <div class="label">证件号码：</div>
              {{ item.zjhm }}
            </div>
          </div>
          <div class="obligee-img">
            <authImg :authSrc="`${baseUrl}/${item.idCardFront}?att=1`" :width="'100%'" :height="'auto'" :radios="'4px'" v-if="item.idCardFront">
            </authImg>
            <authImg :authSrc="`${baseUrl}/${item.idCardBack}?att=1`" :width="'100%'" :height="'auto'" :radios="'4px'" v-if="item.idCardBack">
            </authImg>
            <authImg :authSrc="`${baseUrl}/${item.signature}?att=1`" :width="'100%'" :height="'auto'" :radios="'4px'" v-if="item.signature">
            </authImg>
            <authImg :authSrc="`${baseUrl}/${item.fingerprint}?att=1`" :width="'100%'" :height="'auto'" :radios="'4px'" v-if="item.fingerprint">
            </authImg>
          </div>
        </div>
      </div>
    </template>
    <!-- 动物 -->
    <template v-if="currentValue === 3"></template>
    <!-- 植物 -->
    <template v-if="currentValue === 4"></template>
    <!-- 现场照片 -->
    <template v-if="currentValue === 5">
      <div class="content img-box">
        <div class="photo-img" v-for="(img, imdx) in imgList" :key="img.id">
          <div class="loding-photo" v-if="isLoading">
            <el-image :src="lodingImg"></el-image>
          </div>
          <authImg
            class="img"
            :key="imdx"
            :authSrc="`${baseUrl}/${img.netUrl}?att=1`"
            :width="'156px'"
            :height="'156px'"
            :radios="'6px'"
            style="margin-right: 10px"
            v-else
          >
          </authImg>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import zdIcon from '@/assets/images/zdIcon.png';
import yangf from '@/assets/images/yangf.png';
import authImg from '../../../components/authImg/index.vue';
import lodingImg from '@/assets/images/lodingImg.png';

interface TabItem {
  label: string;
  value: number;
  isCkeck: boolean;
}

interface TreeNode {
  level: number;
  isExpand: boolean;
  id: string | number;
  label: string;
  children?: TreeNode[];
  type?: string;
}

interface ObligeeModel {
  qlrxm: string;
  phone: string;
  zjhm: string;
  idCardFront?: string;
  idCardBack?: string;
  signature?: string;
  fingerprint?: string;
}

interface Picture {
  id: string | number;
  netUrl: string;
}

interface Props {
  checkedLinye: {
    title: string;
    id: string | number;
    quadratList?: Array<{
      id: string | number;
      title: string;
    }>;
    obligeeModelList?: ObligeeModel[];
    pictureList?: Picture[];
  };
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'closeLinyeInfo'): void;
}>();

const currentValue = ref(1);
const isLoading = ref(true);
const baseUrl = import.meta.env.VUE_APP_BASE_API + '/qjt/file/downloadone';

const selectTitleList = ref<TabItem[]>([
  { label: '图形信息', value: 1, isCkeck: true },
  { label: '家庭成员', value: 2, isCkeck: false },
  { label: '动物', value: 3, isCkeck: false },
  { label: '植物', value: 4, isCkeck: false },
  { label: '现场照片', value: 5, isCkeck: false }
]);

const defaultProps = {
  children: 'children',
  label: 'label'
};

/**
 * 图形信息列表
 * @returns 图形信息列表
 */
const graphicalList = computed(() => {
  const list: TreeNode[] = [];
  if (props.checkedLinye) {
    const obj: TreeNode = {
      level: 1,
      isExpand: true,
      children: [],
      id: props.checkedLinye.id,
      label: props.checkedLinye.title
    };
    if (props.checkedLinye.quadratList?.length) {
      props.checkedLinye.quadratList.forEach((v) => {
        const item: TreeNode = {
          level: 2,
          isExpand: true,
          id: v.id,
          label: v.title
        };
        obj.children?.push(item);
      });
    }
    list.push(obj);
  }
  return list;
});

/**
 * 图片列表
 * @returns 图片列表
 */
const imgList = computed(() => {
  const list: Picture[] = [];
  props.checkedLinye.pictureList?.forEach((pic) => {
    list.push(pic);
  });
  return list;
});

/**
 * 图片列表
 * @returns 图片列表
 */
watch(
  imgList,
  (newList) => {
    if (newList.length > 0) {
      isLoading.value = false;
    }
  },
  { immediate: true }
);

const handleColseZongDiInfo = () => {
  emit('closeLinyeInfo');
};

/**
 * 改变标签
 * @param item 标签
 */
const changeTab = (item: TabItem) => {
  selectTitleList.value.forEach((v) => {
    v.isCkeck = false;
  });
  item.isCkeck = true;
  currentValue.value = item.value;
};

const instance = getCurrentInstance();

/**
 * 检查节点
 * @param node 节点
 */
const checkedNode = (node: TreeNode) => {
  if (instance?.parent?.exposed?.changeGraph) {
    instance.parent.exposed.changeGraph(node);
  }
  if (node.id === node.id) {
    node.isExpand = !node.isExpand;
  }
};

defineExpose({
  changeGraph: (node: TreeNode) => {
    // 父组件可以调用的方法
  }
});
</script>

<style lang="scss" scoped>
.el-tree {
  background: transparent;
  padding: 0px 10px 10px 10px;
  width: 100%;
  height: 545px;
  .el-icon svg {
    //原有的箭头 去掉
    display: none !important;
    height: 0;
    width: 0;
  }
  :deep(.el-tree-node__content) {
    width: 512;
    height: 44px;
    background: rgba(0, 0, 0, 0);
    border-radius: 0px 0px 0px 0px;
    .custom-tree-node {
      margin-left: 12px;
      display: flex;
      align-items: center;
      position: relative;
      width: 100%;
      height: 44px;
      border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
      .zd-icon {
        width: 20px;
        .el-image {
          :deep(.el-image__inner) {
            width: 20px;
            height: 20px;
            margin: 12px;
            vertical-align: bottom;
          }
        }
      }
      .zd-name {
        height: 20px;
        width: 300px;
        font-size: 14px;
        font-family:
          PingFang SC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        margin-left: 12px;
      }
      .zd-leng {
        position: absolute;
        right: 40px;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.5);
        line-height: 20px;
      }
      .zd-arrow {
        position: absolute;
        right: 10px;
        height: 20px;
        font-size: 14px;
        font-family:
          PingFangSC-328080,
          PingFang SC;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.5);
        line-height: 20px;
      }
    }
    &:hover {
      width: 512;
      height: 44px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
    }
  }
}
.zongdi-detail-info-contianer {
  height: auto;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  backdrop-filter: blur(5px);
  width: 507px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title-div {
    width: 100%;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 0px 0px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    .icon {
      width: 14px;
      height: 14px;
      position: absolute;
      right: 12px;
      color: #ffffff;
      cursor: pointer;
    }
  }
  .tab-div {
    padding: 16px 10px 12px 10px;
    width: 100%;
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: row;
    .tab-item {
      height: 28px;
      border-radius: 16px 16px 16px 16px;
      padding: 0px 16px;
      line-height: 28px;
      cursor: pointer;
      color: #ffffff;
    }
    .active {
      background: var(--current-color);
    }
  }
  .content ::-webkit-scrollbar {
    width: 1px;
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
  .content {
    height: 545px;
    width: calc(100% - 20px);
    overflow: auto;
    .family-item {
      width: 100%;
      height: auto;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      margin-bottom: 12px;
      padding: 12px;
      .family-title {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 12px;
      }
      .family-row {
        display: flex;
        margin-bottom: 12px;
        flex-direction: row;
        align-items: center;
        .family-ite {
          flex: 1;
          color: #ffffff;
          font-size: 14px;
          display: flex;
          flex-direction: row;
          .label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
      .obligee-img {
        display: grid;
        grid-gap: 10px;
        grid-template-columns: auto auto auto auto;
        margin-top: 20px;
        margin-left: 12px;
        .autoImage-main {
          // margin-left: 4px;
          // margin-right: 4px;
        }
      }
    }
  }
  .img-box {
    // display: grid;
    // grid-gap: 10px;
    // grid-template-columns: auto auto auto;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .loding-photo {
      width: 156px;
      height: 156px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px 6px 6px 6px;
      border: 1px dashed #ffffff;
      display: flex;
      justify-items: center;
      .el-image {
        :deep(.el-image__inner) {
          width: 48px;
          height: 48px;
          margin: 54px;
        }
      }
    }
  }
}
</style>
