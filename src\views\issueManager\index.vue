<!-- 图层管理 发布geoserver图层以及获取图层信息 -->
<template>
  <div class="issueManager-main" v-loading.fullscreen.lock="fullscreenLoading">
    <container-card>
      <div class="handle">
        <el-button type="primary" size="small" @click="handleAddIssue">发布图层</el-button>
      </div>
      <div class="table">
        <el-table :data="layerList" style="width: 100%" border>
          <el-table-column label="图层名称">
            <template #default="scope">
              {{ scope.row.mapAlias }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-link type="danger" @click="del(scope.row)">删除</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-dialog title="发布图层" v-model="dialogVisible" width="800px" @close="handleClose">
        <el-form :model="layerMsg" :rules="layerMsgRules" ref="layerMsgRef" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="数据源" prop="fileList">
                <el-upload
                  ref="uploadRef"
                  class="upload-demo"
                  :limit="1"
                  :multiple="false"
                  :action="`${baseUrl}/qjt/geoserver/shp/publish/zip/upload`"
                  :headers="headers"
                  :auto-upload="false"
                  :on-change="handleChangeUpload"
                  :data="{
                    chunkNumber: chunkNumber,
                    totalChunks: totalChunks,
                    uploadId: uploadId
                  }"
                  :on-success="handleAvatarSuccess"
                  accept=".zip"
                  :file-list="fileList"
                >
                  <el-button size="small" type="primary">选择SHP</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图层别名" prop="mapAlias">
                <el-input v-model="layerMsg.mapAlias" placeholder="请输入图层别名(1~20)"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="父级图层">
                <el-cascader
                  style="width: 100%"
                  v-model="layerMsg.parentIds"
                  :props="attrProps"
                  :options="parentAttr"
                  @change="handleChangeCascader"
                  clearable
                ></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="图层描述" prop="description">
                <el-input
                  v-model="layerMsg.description"
                  type="textarea"
                  placeholder="请输入图层描述"
                  :autosize="{ minRows: 2, maxRows: 20 }"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="样式模式" prop="type">
                <el-radio-group v-model="layerMsg.type" @change="handleRadioType">
                  <el-radio v-for="item in typeList" :key="item.id" :value="item.id">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="layerMsg.type === 1">
              <el-form-item label="样式名称" prop="styleName2">
                <el-input v-model="layerMsg.styleName2" placeholder="请输入样式" @blur="handleBlurStyleName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-else>
              <el-form-item label="样式名称" prop="styleName">
                <el-select v-model="layerMsg.styleName" placeholder="请选择样式" style="width: 100%">
                  <el-option v-for="item in styleList" :key="item" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 以下是自定义样式 -->
          <div v-if="layerMsg.type == 1">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="图层序号" prop="seq">
                  <el-input-number
                    v-model="layerMsg.seq"
                    :min="1"
                    :value-on-clear="1"
                    :max="1000"
                    placeholder="请输入图层序号"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="图层透明度" prop="fillOpacity">
                  <el-input-number
                    v-model="layerMsg.fillOpacity"
                    :min="0.0"
                    :max="1.0"
                    :precision="1"
                    :step="0.1"
                    :value-on-clear="0.5"
                    placeholder="图层透明度(取值范围为0.0~1.0)"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="填充颜色" prop="fillColor">
                  <el-color-picker
                    style="vertical-align: middle"
                    :value-on-clear="1"
                    v-model="layerMsg.fillColor"
                    :show-alpha="false"
                    :predefine="predefineColors"
										@change="handleFillColorChange"
                  >
                  </el-color-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="线条颜色" prop="strokeColor">
                  <el-color-picker style="vertical-align: middle" v-model="layerMsg.strokeColor" :show-alpha="false" :predefine="predefineColors" @change="handleColorChange">
                  </el-color-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="线条宽度" prop="strokeWidth">
                  <el-input-number
                    v-model="layerMsg.strokeWidth"
                    :min="1"
                    :max="10"
                    placeholder="线条宽度(取值范围为1-10)"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标注字段" prop="propertyName">
                  <el-input v-model="layerMsg.propertyName" placeholder="请输入标注字段"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="字体加粗" prop="fontWeight">
                  <el-select v-model="layerMsg.fontWeight" placeholder="请选择样式" style="width: 100%">
                    <el-option v-for="item in fontWeightList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字号大小" prop="fontSize">
                  <el-input-number
                    v-model="layerMsg.fontSize"
                    :min="10"
                    :max="20"
                    :value-on-clear="12"
                    placeholder="字号大小(范围在10~20)"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div class="shp-span" style="font-size: 14px">注意事项：</div>
        <div class="shp-span">1、只能上传zip文件,且不超过300M</div>
        <div class="shp-span">2、为了保证app和网页能正常使用请使用 "投影坐标系:3857(WGS_1984_Web_Mercator_Auxiliary_Sphere)"</div>
        <div class="shp-span">3、压缩包和shp文件名均不可以包含中文空格等字符，只能以字母开头，可以包含数字（长度2-50个字符）</div>
        <div class="shp-span">4、为保证图层唯一，发布的shp名和zip名必须全局唯一，否则发布失败</div>
        <div class="shp-span">5、压缩包不可以包含非shp文件</div>
        <div class="shp-span">6、压缩包结构：直接把shp等相关数据压缩成zip格式，中间不要加文件夹</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="submitUpload">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </container-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import type { FormInstance, UploadInstance, FormRules, UploadFile, UploadUserFile } from 'element-plus';
import { getTCList, getStyle, delTC, getShpTree, delTC1, publishInit, publishUpload, publishMerge, publishProgress } from '@/api/issueManager';
import { getToken } from '@/utils/auth';
import sparkMD5 from 'spark-md5';

// 接口定义
interface StyleItem {
  id: number;
  label: string;
}

interface FontWeightItem {
  label: string;
  value: string;
}

interface CascaderOption {
  value: string;
  label: string;
  children?: CascaderOption[];
}

interface LayerForm {
  parentIds: string[];
  parentId: string;
  file: UploadFile | null;
  styleName: string;
  styleName2: string;
  seq: number;
  type: number;
  mapAlias: string;
  description: string;
  fillOpacity: number;
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
  propertyName: string;
  fontWeight: string;
  fontSize: number;
}

// 状态定义
const fullscreenLoading = ref(false);
const layerList = ref<any[]>([]);
const dialogVisible = ref(false);
const styleList = ref<string[]>([]);
let uploadLoading: any = null;

// ref引用
const layerMsgRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();

// 图层数据
const layerMsg = reactive<LayerForm>({
  parentIds: [],
  parentId: '',
  file: null,
  styleName: '',
  styleName2: '',
  seq: 1,
  type: 2,
  mapAlias: '',
  description: '',
  fillOpacity: 0.5,
  fillColor: '',
  strokeColor: '',
  strokeWidth: 1,
  propertyName: '',
  fontWeight: 'normal',
  fontSize: 12
});

// 验证方法
const validateFile = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请上传文件'));
  } else {
    callback();
  }
};

const validateFileList = (rule: any, value: any, callback: any) => {
  if (fileList.value.length === 0) {
    callback(new Error('请上传SHP文件'));
  } else {
    callback();
  }
};

// 表单验证规则
const layerMsgRules = reactive<FormRules>({
  fileList: [{ required: true, validator: validateFileList, trigger: 'change' }],
  file: [{ required: true, validator: validateFile, trigger: 'change' }],
  styleName: [{ required: true, message: '请选择样式名称', trigger: 'change' }],
  styleName2: [
    { required: true, message: '请输入样式名称', trigger: 'change' },
    {
      message: '只能输入英文和数字',
      pattern: /^[a-zA-Z0-9]*$/,
      trigger: ['blur', 'change']
    }
  ],
  type: [{ required: true, message: '请选择样式模式', trigger: 'change' }],
  mapAlias: [{ required: true, message: '请输入图层别名', trigger: 'blur' }]
});

// 环境变量和配置
const baseUrl = import.meta.env.VITE_APP_BASE_API || process.env.VUE_APP_BASE_API || '';
const headers = {
  Authorization: 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
};

// 样式相关
const typeList = ref<StyleItem[]>([
  { label: '默认样式', id: 2 },
  { label: '自定义样式', id: 1 }
]);

const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
]);

const fontWeightList = ref<FontWeightItem[]>([
  { label: '加粗', value: 'bolder' },
  { label: '粗体', value: 'blod' },
  { label: '正常', value: 'normal' },
  { label: '最小', value: 'lighter' }
]);

// 级联选择器配置
const attrProps = {
  value: 'id',
  label: 'label',
  children: 'children',
  checkStrictly: true
};

// 上传相关
const parentAttr = ref<CascaderOption[]>([]);
const fileList = ref<UploadUserFile[]>([]);
const chunkSize = 10 * 1024 * 1024; // 10MB 分片大小
const uploadId = ref('');
const chunkNumber = ref(0);
const totalChunks = ref(0);
const uploadingText = ref('上传进度为：0%');

// 监听上传进度文本
watch(uploadingText, (val) => {
  if (uploadLoading) {
    uploadLoading.setText(val);
  }
});

// 生命周期钩子
onMounted(() => {
  getTCListData();
});

// 方法定义
// 获取图层列表
const getTCListData = async () => {
  fullscreenLoading.value = true;
  try {
    const res = await getTCList();
    if (res.code === 200) {
      layerList.value = res.data;
      getShpTreeData();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取图层数据失败');
  } finally {
    fullscreenLoading.value = false;
  }
};

// 获取图层树结构
const getShpTreeData = async () => {
  try {
    const res = await getShpTree();
    if (res.code === 200) {
      parentAttr.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取图层树结构失败');
  }
};

// 处理样式模式变更
const handleRadioType = (val: number) => {
  if (val === 2) {
    layerMsg.fillColor = '';
    layerMsg.strokeColor = '';
  } else {
    layerMsg.fillColor = '#ff9999';
    layerMsg.strokeColor = '#ff0000';
  }
};

// 处理样式名称失焦
const handleBlurStyleName = (event: Event) => {
  const target = event.target as HTMLInputElement;
  layerMsg.styleName = target.value;
};

// 处理级联选择器变更
const handleChangeCascader = (value: string[]) => {
  layerMsg.parentId = value[value.length - 1];
};

// 添加图层
const handleAddIssue = async () => {
  try {
    const res = await getStyle();
    if (res.code === 200) {
      styleList.value = res.data;
      dialogVisible.value = true;
    } else {
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取样式列表失败');
  }
};

// 关闭对话框
const handleClose = () => {
  // 重置表单
  Object.assign(layerMsg, {
    parentIds: [],
    parentId: '',
    file: null,
    styleName: '',
    styleName2: '',
    seq: 1,
    type: 2,
    mapAlias: '',
    description: '',
    fillOpacity: 0.5,
    fillColor: '',
    strokeColor: '',
    strokeWidth: 1,
    propertyName: '',
    fontWeight: 'normal',
    fontSize: 12
  });
  fileList.value = [];
  layerMsgRef.value?.resetFields();
  dialogVisible.value = false;
};

// 删除图层
const del = (row: any) => {
  ElMessageBox.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const res = await delTC1(row.shpFileName);
        if (res.code === 200) {
          ElMessage.success('删除成功!');
          getTCListData();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error(error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

/***
 * 上传的图层文件改变的时候
 * @param file 当前上传的文件
 * @param fileListNew 当前上传的文件列表
 */
const handleChangeUpload = async (file: UploadFile, fileListNew: UploadUserFile[]) => {
  const isLt300M = file.size / 1024 / 1024 > 300;
  if (isLt300M) {
    layerMsg.file = null;
    ElMessage.error('只能上传不超过300M的压缩包');
    const index = fileListNew.findIndex((item) => item.name == file.name && item.uid == file.uid);
    if (index !== -1) {
      fileListNew.splice(index, 1);
    }
    return;
  }

  try {
    // 初始化上传，获取分片ID
    const res = await publishInit();
    if (res.code === 200) {
      layerMsgRef.value?.clearValidate('fileList');
      uploadId.value = res.data;
      layerMsg.file = file;
      fileList.value = fileListNew;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('初始化上传失败');
  }
};

// 上传成功回调
const handleAvatarSuccess = (res: any) => {
  if (res.code === 200) {
    ElMessage.success('操作成功');
    getTCListData();
  } else {
    ElMessage.error(res.msg);
  }
  handleClose();
};

// 提交表单
const submit = () => {
  layerMsgRef.value?.validate((valid) => {
    if (valid) {
      uploadRef.value?.submit();
    }
  });
};

// 处理文件上传
const handleUpload = async (options: { file: UploadFile }) => {
  const file = options.file.raw;
  if (!file) return;

  const totalChunksVal = Math.ceil(file.size / chunkSize);
  totalChunks.value = totalChunksVal;

  uploadLoading = ElLoading.service({
    lock: true,
    text: '文件解析中.....',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    for (let chunkIndex = 0; chunkIndex < totalChunksVal; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);
      chunkNumber.value = chunkIndex;

      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('chunkNumber', chunkIndex.toString());
      formData.append('totalChunks', totalChunksVal.toString());
      formData.append('uploadId', uploadId.value);

      const res = await publishUpload(formData);
      if (res.code === 200) {
        const percent = ((chunkNumber.value / totalChunks.value) * 100).toFixed(2);
        uploadingText.value = `上传进度为：${percent}%`;
      } else {
        ElMessage.error(res.msg);
      }

      // 最后一个分片，调用合并请求
      if (chunkIndex === totalChunksVal - 1) {
        await handleUploadMerge();
      }
    }
  } catch (error) {
    uploadLoading.close();
    console.error(error);
    ElMessage.error('上传失败');
  }
};
/**
 * 处理填充色选择器变更
 * @param newColor 新颜色
 */
const handleFillColorChange = (newColor: string) => {
  if (!newColor) {
    // 恢复之前的颜色
    layerMsg.fillColor = layerMsg.fillColor || '#ff9999';
  }
};
/**
 * 处理边框颜色选择器变更
 * @param newColor 新颜色
 */
const handleColorChange = (newColor: string) => {
  if (!newColor) {
    // 恢复之前的颜色
    layerMsg.strokeColor = layerMsg.strokeColor || '#ff0000';
  }
};

// 提交上传
const submitUpload = () => {
  layerMsgRef.value?.validate((valid) => {
    if (valid) {
      handleUpload({ file: layerMsg.file as UploadFile });
    }
  });
};

// 合并上传的分片
const handleUploadMerge = async () => {
  const data = {
    styleName: layerMsg.styleName,
    parentId: layerMsg.parentId,
    seq: layerMsg.seq,
    type: layerMsg.type,
    mapAlias: layerMsg.mapAlias,
    description: layerMsg.description,
    fillOpacity: layerMsg.fillOpacity,
    fillColor: layerMsg.fillColor,
    strokeColor: layerMsg.strokeColor,
    strokeWidth: layerMsg.strokeWidth,
    propertyName: layerMsg.propertyName,
    fontWeight: layerMsg.fontWeight,
    fontSize: layerMsg.fontSize,
    uploadId: uploadId.value
  };

  try {
    const res = await publishMerge(data);
    if (res.code === 200) {
      handlePublishProgress();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('合并上传失败');
    uploadLoading?.close();
  }
};

// 获取发布进度
const handlePublishProgress = () => {
  const fetchProgress = async () => {
    try {
      const res = await publishProgress(uploadId.value);
      if (res.code === 200) {
        // status 状态 1运行中 0结束 -1异常
        if (res.data.status === '1') {
          const progress = res.data.progress;
          const remark = res.data.remark;
          uploadingText.value = `${remark},进度为${progress}%`;
          setTimeout(fetchProgress, 1000); // 隔一秒后再次调用
        } else if (res.data.status === '-1') {
          handleDealStatus(res.data);
        } else {
          const progress = res.data.progress;
          const remark = res.data.remark;
          uploadingText.value = `${remark},进度为${progress}%`;
          setTimeout(() => {
            // 等于 0 的情况 直接关闭弹框
            uploadLoading?.close();
            dialogVisible.value = false;
            getTCListData();
          }, 1000);
        }
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('获取发布进度失败');
      uploadLoading?.close();
    }
  };
  fetchProgress(); // 初始调用
};

// 处理状态异常
const handleDealStatus = (item: { remark: string }) => {
  // 先关闭loading 然后再次提示
  uploadLoading?.close();
  const remark = item.remark;
  ElMessageBox.alert(remark, '提示', {
    confirmButtonText: '确定',
    callback: () => {
      dialogVisible.value = false;
      getTCListData();
    }
  });
};
</script>

<style lang="scss" scoped>
.issueManager-main {
  height: calc(100% - 1px);
  width: calc(100% - 1px);
  // position: absolute;
  // // top: 20px;
  // left: 20px;
  // bottom: 20px;
  // padding: 20px;
  // border-radius: 8px;
  // opacity: 1;
  // background: #ffffff;
  // overflow: hidden;

  .handle {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
  }

  .table {
    height: calc(100% - 80px);
    overflow: auto;
    width: 100%;
    // 滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
}

.shp-span {
  height: 25px;
  color: red;
  font-size: 12px;
}

.demo-ruleForm {
  width: 100%;
  height: calc(100% - 160px);

  .el-form-item {
    margin-bottom: 16px !important;
  }

  :deep(.el-input) {
    .el-input__inner {
      min-width: 150px;
      width: 100%;
      flex: 1;
      height: 30px;

      /* 谷歌 */
      &::-webkit-input-placeholder {
        font-size: 12px;
      }

      /* 火狐 */
      &::-moz-placeholder {
        font-size: 12px;
      }

      /* IE */
      &::-ms-input-placeholder {
        font-size: 12px;
      }
    }

    .el-input__suffix {
      .el-select__caret {
        height: 30px;
        line-height: 30px;
      }
    }

    .el-input__prefix {
      height: 30px;
      width: 16px;
      line-height: 30px;

      .el-input__icon {
        height: 30px;
        width: 16px;
        line-height: 30px;
      }
    }
  }

  :deep(.el-input-number) {
    .el-input-number__increase,
    .el-input-number__decrease {
      height: 28px !important;
      top: 3px !important;
    }
  }

  .el-select {
    :deep(.el-input) {
      .el-input__inner {
        height: 30px;
      }

      .el-input__prefix {
        height: 30px;
        width: 16px;
        line-height: 30px;
      }

      .el-input__suffix {
        .el-select__caret {
          height: 30px;
          line-height: 30px;
          font-weight: bold;
        }
      }
    }
  }
}

:deep(.el-color-picker) {
  width: 100px;
  height: 23px;
  margin-right: 8px;
  border: 6px;
  z-index: 99;

  .el-color-picker__trigger {
    width: 100px;
    height: 23px;
    padding: 0;
    border: 6px;

    .el-color-picker__icon {
      display: none;
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .issueManager-main {
    width: calc(100% - 20px);
    left: 10px;
    padding: 10px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
  }
}
</style>
