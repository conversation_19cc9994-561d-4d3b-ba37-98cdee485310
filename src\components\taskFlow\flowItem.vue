<!-- 单个流程 -->
<template>
  <div>
    <div class="flow-item" v-for="(item, index) in flowList" :key="index">
      <template v-if="item.stepType == 1 && item.delFlag != 1">
        <div class="flow-main" @mouseenter="hoverStep(item, 1)" @mouseleave="hoverStep(item, 2)">
          <div class="title" @click.stop>
            <div style="width: 100%" @click.stop="showEditTitle(item)" v-show="!item.changeTitle">
              {{ item.typeName }}
            </div>
            <el-input v-model="item.typeName" v-show="item.changeTitle" @keyup.enter="item.changeTitle = false"></el-input>
            <div class="close-btn" v-show="item.checked && index != 0" @click="delOneStep(item, index)"><i class="el-icon-close"></i></div>
          </div>
          <div class="flow-content" @click="openDetail(item)">
            <div class="con-title">
              <span v-show="item.receiverNames">{{ item.receiverNames }}</span>
              <span v-show="!item.receiverNames" style="color: #bfbfbf">请设置步骤内容</span>
              <i class="el-icon-arrow-right" style="margin-left: 1px"></i>
            </div>
          </div>
        </div>
        <div class="hr"></div>
      </template>
      <!-- 分支 -->
      <template v-if="item.stepType == 3 && item.delFlag != 1">
        <div class="flow-main" @mouseenter="hoverStep(item, 1)" @mouseleave="hoverStep(item, 2)">
          <div class="title" @click.stop>
            <div style="width: 100%" @click.stop="showEditTitle(item)" v-show="!item.changeTitle">
              {{ item.typeName }}
            </div>
            <el-input v-model="item.typeName" v-show="item.changeTitle" @keyup.enter="item.changeTitle = false" size="default"></el-input>
            <div class="close-btn" v-show="item.checked && index != 0" @click="delOneChild(item, index)">
              <el-icon><Close /></el-icon>
            </div>
          </div>
          <div class="flow-content">
            <el-link type="primary" @click="addBranch(item)">添加分支</el-link>
          </div>
        </div>
        <div class="hr"></div>
        <div class="child-box">
          <div class="big-hr-box">
            <div class="big-hr"></div>
          </div>
          <div class="child-content">
            <div v-for="(ite, idx) in item.list" :key="idx">
              <div class="flow-item" :class="{ 'margin-flow-item': idx != item.list.length - 1 }" v-show="ite.delFlag != 1">
                <div class="up-hr"></div>
                <div class="flow-main" @mouseenter="hoverStep(ite, 1)" @mouseleave="hoverStep(ite, 2)">
                  <div class="title" @click.stop>
                    <div style="width: 100%" @click.stop="showEditTitle(ite)" v-show="!ite.changeTitle">
                      {{ ite.typeName }}
                    </div>
                    <el-input v-model="ite.typeName" v-show="ite.changeTitle" @keyup.enter="ite.changeTitle = false" size="default"></el-input>
                    <div class="close-btn" v-show="ite.checked && index != 0" @click="delOneStepChild(item, idx)">
                      <el-icon><Close /></el-icon>
                    </div>
                  </div>
                  <div class="flow-content" @click="openDetailChild(ite)">
                    <div class="con-title">
                      <span v-show="ite.receiverNames">{{ ite.receiverNames }}</span>
                      <span v-show="!ite.receiverNames" style="color: #bfbfbf">请设置步骤内容</span>
                      <i class="el-icon-arrow-right" style="margin-left: 1px"></i>
                    </div>
                  </div>
                </div>
                <div class="hr"></div>
                <div class="arrowhead"></div>
              </div>
            </div>
          </div>
          <div class="big-hr-box">
            <div class="big-hr"></div>
          </div>
          <div style="display: flex; justify-content: center">
            <div class="hr"></div>
          </div>
        </div>
      </template>
    </div>
    <div class="end-box">
      <div class="add-node-btn" @click.stop="addClick($event)">
        <i class="el-icon-plus"></i>
      </div>
      <div class="arrowhead"></div>
      <div class="end"></div>
    </div>
    <div class="flow-end">流程结束</div>
    <div class="add-node-content" v-show="visible" :style="{ top: addNodeTop, left: addNodeLeft }">
      <div class="item" @click="chooseOneBtn(1)">
        <img src="../../assets/images/caiji.png" alt="" class="ico" />
        采集
      </div>
      <div class="item" @click="chooseOneBtn(3)">
        <img src="../../assets/images/fenzhi.png" alt="" class="ico" />
        分支
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

interface FlowItem {
  id?: number;
  typeName: string;
  stepLevel: number;
  users: any[];
  receiverNames: string;
  stepType: number;
  changeTitle: boolean;
  treeNodes: any[];
  charges: any[];
  chargesName: string;
  list: FlowItem[];
  delFlag?: number;
  checked?: boolean;
}

const props = defineProps<{
  flowList: FlowItem[];
}>();

const emit = defineEmits<{
  (e: 'handleDelOneStep', item: FlowItem, index: number): void;
  (e: 'handleChooseOneBtn', type: number): void;
  (e: 'handleOpenDetail', item: FlowItem): void;
  (e: 'handleDelOneChild', index: number): void;
}>();

const addNodeTop = ref('0px');
const addNodeLeft = ref('0px');
const visible = ref(false);

const closeVisible = () => {
  visible.value = false;
};

const hoverStep = (item: FlowItem, type: number) => {
  if (type == 1) {
    item.checked = true;
  } else {
    item.checked = false;
  }
};

const delOneStep = (item: FlowItem, index: number) => {
  emit('handleDelOneStep', item, index);
};

const delOneStepChild = (item: FlowItem, idx: number) => {
  ElMessageBox.confirm('确定要删除该流程吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      if (item.id) {
        item.list[idx].delFlag = 1;
      } else {
        item.list.splice(idx, 1);
      }
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {
      // 取消删除
    });
};

const addClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const rect = target.getBoundingClientRect();

  addNodeTop.value = rect.top + window.scrollY + 'px';
  addNodeLeft.value = rect.left + window.scrollX + 'px';
  visible.value = true;
};

const chooseOneBtn = (type: number) => {
  emit('handleChooseOneBtn', type);
};

const showEditTitle = (item: FlowItem) => {
  props.flowList.forEach((v) => {
    v.changeTitle = false;
  });
  item.changeTitle = true;
  item.checked = false;
};

const openDetail = (item: FlowItem) => {
  emit('handleOpenDetail', item);
};

const openDetailChild = (ite: FlowItem) => {
  emit('handleOpenDetail', ite);
};

const addBranch = (item: FlowItem) => {
  item.list.push({
    typeName: '步骤',
    stepLevel: item.stepLevel,
    users: [],
    receiverNames: '',
    stepType: 1,
    changeTitle: false,
    treeNodes: [],
    charges: [],
    chargesName: '',
    list: []
  });
};

const delOneChild = (item: FlowItem, index: number) => {
  ElMessageBox.confirm('删除该分支会把分支下的内容都删除，确定要删除该分支吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('handleDelOneChild', index);
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {
      // 取消删除
    });
};
</script>

<style lang="scss" scoped>
.margin-flow-item {
  margin-right: 40px;
}
.flow-item {
  width: 220px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .up-hr {
    height: 40px;
    width: 3px;
    background: #cacaca;
  }
  .flow-main:hover {
    border: #0089ff solid 1px;
  }
  .flow-main {
    width: 100%;
    cursor: pointer;
    border: solid 1px #fff;
    border-radius: 4px;
    .title {
      height: 35px;
      padding: 0px 16px;
      background: #0089ff;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      color: #fff;
      display: flex;
      align-items: center;
      cursor: pointer;
      width: 100%;
      box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
      position: relative;
      .close-btn {
        color: #fff;
        font-size: 18px;
        position: absolute;
        right: 0;
      }
    }
    .title:hover {
      text-decoration: underline; /* 鼠标悬停时显示下划线 */
    }
    .flow-content {
      padding: 24px;
      background: #fff;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      width: 100%;
      box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
      .con-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
      }
    }
  }
  .child-box {
    .big-hr-box {
      width: 100%;
      display: flex;
      justify-content: center;
      .big-hr {
        background: #cacaca;
        width: calc(100% - 216px);
        height: 3px;
      }
    }
    .child-content {
      display: flex;
      flex-direction: row;
    }
  }
  .hr {
    width: 3px;
    height: 30px;
    background: #cacaca;
  }
  .add-node-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    background: #0089ff;
    cursor: pointer;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .add-node-btn:hover {
    transform: scale(1.3);
    box-shadow: 0 13px 27px 0 rgba(0, 0, 0, 0.1);
  }
  .arrowhead {
    height: 30px;
    width: 3px;
    background: #cacaca;
  }
  .triangle {
    width: 0px;
    height: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 15px #cacaca solid;
    margin-bottom: 5px;
  }
  .end {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #d3d3d3;
    margin-bottom: 5px;
  }
}
.end-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .end {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #d3d3d3;
    margin-bottom: 5px;
  }
  .add-node-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    background: #0089ff;
    cursor: pointer;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .add-node-btn:hover {
    transform: scale(1.3);
    box-shadow: 0 13px 27px 0 rgba(0, 0, 0, 0.1);
  }
  .arrowhead {
    height: 30px;
    width: 3px;
    background: #cacaca;
  }
  .triangle {
    width: 0px;
    height: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 15px #cacaca solid;
    margin-bottom: 5px;
  }
}
.flow-end {
  padding: 12px 24px;
  border-radius: 25px;
  background: rgba(23, 26, 29, 0.03);
  color: rgba(25, 31, 37, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-node-content {
  position: absolute;
  display: flex;
  padding: 16px 16px 0px 16px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  .item {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    background: rgba(17, 31, 44, 0.02);
    border-radius: 4px;
    padding: 12px;
    cursor: pointer;
    .ico {
      width: 32px;
      height: 32px;
      margin-right: 20px;
    }
  }
  .item:hover {
    background: #ffffff;
    border: 1px solid #ecedef;
    box-shadow: 0 2px 8px 0 rgba(17, 31, 44, 0.08);
  }
}
</style>
