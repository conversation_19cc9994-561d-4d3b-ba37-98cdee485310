<template>
  <div>
    <el-row class="mb8">
      <el-col :span="20">
        <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="订单状态">
            <el-select v-model="queryParams.orderState" placeholder="请选择">
              <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery"
              ><el-icon><Search /></el-icon>搜索</el-button
            >
            <el-button @click="resetQuery"
              ><el-icon><RefreshRight /></el-icon>重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="4">
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getOrdersList" :columns="columns"></right-toolbar>
      </el-col>
    </el-row>
    <div class="table-main">
      <el-table v-loading="loading" :data="orderList">
        <el-table-column type="index" width="55" align="center" label="序号" />
        <el-table-column label="订单编号" align="center" prop="orderNum" width="200" v-if="columns[0].visible" />
        <el-table-column label="支付金额" align="center" prop="payMoney" v-if="columns[3].visible">
          <template #default="{ row }">
            <span>{{ row.payMoney / 100 }}</span>
            <span style="padding-left: 4px">元</span>
          </template>
        </el-table-column>
        <el-table-column label="订单备注" align="center" prop="orderDesc" width="400" v-if="columns[1].visible" />
        <el-table-column label="订单状态" align="center" prop="orderState" v-if="columns[2].visible">
          <template #default="{ row }">
            <el-tag type="success" v-if="row.orderState == 4">{{ formatOrderStatus(row.orderState) }}</el-tag>
            <el-tag type="danger" v-else-if="row.orderState == 3">{{ formatOrderStatus(row.orderState) }}</el-tag>
            <el-tag type="info" v-else>{{ formatOrderStatus(row.orderState) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" align="center" prop="payType" v-if="columns[4].visible">
          <template #default="{ row }">
            <span v-if="row.payType == 2">支付宝</span>
            <span v-else>微信</span>
          </template>
        </el-table-column>
        <el-table-column label="支付渠道" align="center" prop="payChannel" v-if="columns[5].visible">
          <template #default="{ row }">
            <span v-if="row.payChannel == 1">APP</span>
            <span v-else-if="row.payChannel == 3">线下</span>
            <span v-else>网页</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付时间" align="center" prop="payTime" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.payTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer">
        <div class="footer-text">
          <span>当页订单汇总金额</span>
          <span style="color: #0088ff">{{ amountTotal.allTotal }}</span>
          <span>元，支付成功</span>
          <span style="color: #0088ff">{{ amountTotal.successTotal }}</span>
          <span>元</span>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :pageSizes="pageSizes"
          :layout="layout"
          @pagination="getOrdersList"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { getOreders } from '@/api/pay';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';

// 类型定义
interface OrderStatusOption {
  value: number;
  label: string;
}

interface ColumnItem {
  key: string;
  label: string;
  visible: boolean;
}

interface OrderItem {
  orderNum: string;
  payMoney: number;
  orderDesc: string;
  orderState: number;
  payType: number;
  payChannel: number;
  createTime: string;
  payTime: string;
  [key: string]: any;
}

interface QueryParams {
  orderState: number;
  pageSize: number;
  pageNum: number;
}

interface AmountTotal {
  allTotal: string;
  successTotal: string;
}

// 表单引用
const queryFormRef = ref<FormInstance>();

// 遮罩层
const loading = ref(true);
// 订单列表
const orderList = ref<OrderItem[]>([]);
// 总条数
const total = ref(0);
// 显示搜索条件
const showSearch = ref(true);

// 查询参数
const queryParams = ref<QueryParams>({
  orderState: 0,
  pageSize: 10,
  pageNum: 1
});

// 订单状态选项
const orderStatusOptions = ref<OrderStatusOption[]>([
  { value: 0, label: '全部' },
  { value: 1, label: '未支付' },
  { value: 2, label: '已取消' },
  { value: 3, label: '支付失败' },
  { value: 4, label: '支付成功' },
  { value: 5, label: '退款中' },
  { value: 6, label: '已退款' }
]);

// 列信息
const columns = ref<ColumnItem[]>([
  { key: '0', label: '订单编号', visible: true },
  { key: '1', label: '订单备注', visible: true },
  { key: '2', label: '订单状态', visible: true },
  { key: '3', label: '支付金额', visible: true },
  { key: '4', label: '支付方式', visible: true },
  { key: '5', label: '支付渠道', visible: true }
]);

// 分页大小选项
const pageSizes = ref<number[]>([10, 50, 100, 500, 1000]);
// 分页布局
const layout = ref('total, sizes, prev, pager, next');

// 格式化订单状态
const formatOrderStatus = (val: number): string => {
  switch (val) {
    case 1:
      return '未支付';
    case 2:
      return '已取消';
    case 3:
      return '支付失败';
    case 4:
      return '支付成功';
    case 5:
      return '退款中';
    case 6:
      return '已退款';
    default:
      return '--';
  }
};

// 格式化日期时间
const formatDateTime = (time: string): string => {
  if (!time) return '--';
  const date = new Date(time);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 订单汇总金额和支付成功的金额
const amountTotal = computed<AmountTotal>(() => {
  let allTotal = 0;
  let successTotal = 0;

  orderList.value.forEach((item) => {
    if (item.payMoney > 0) {
      const tempMoney = item.payMoney / 100;
      allTotal = allTotal + tempMoney;
    }
    if (item.payMoney > 0 && item.orderState == 4) {
      const tempMoney = item.payMoney / 100;
      successTotal = successTotal + tempMoney;
    }
  });

  return {
    allTotal: allTotal.toFixed(2),
    successTotal: successTotal.toFixed(2)
  };
});

// 获取我的订单
const getOrdersList = async () => {
  const params = {
    orderState: queryParams.value.orderState,
    pageSize: queryParams.value.pageSize,
    pageNum: queryParams.value.pageNum
  };

  try {
    loading.value = true;
    const res = await getOreders(params);
    if (res.code === 200) {
      orderList.value = res.data.list;
      total.value = res.data.total;
      queryParams.value.pageSize = res.data.pageSize;
      queryParams.value.pageNum = res.data.pageNum;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取订单列表失败', error);
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索按钮
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getOrdersList();
};

// 重置按钮
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value = {
    orderState: 0,
    pageSize: 10,
    pageNum: 1
  };
  handleQuery();
};

// 页面加载时获取列表数据
onMounted(() => {
  getOrdersList();
});
</script>

<style lang="scss" scoped>
.table-main {
  width: 100%;
  height: calc(100vh - 300px);
  // min-height: calc(100vh - 200px);
  overflow-y: auto;
}
.footer {
  display: flex;
  justify-content: space-between;
  flex-direction: row;

  .footer-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    height: 40px;
    line-height: 40px;
    margin-top: 8px;
    padding-top: 2px;
    letter-spacing: 2px;
  }
  .pagination-container {
    padding: 0 !important;
    flex: 1;
  }
}
</style>
