<!-- 3d地图地区 -->
<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import 'echarts-gl';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-3dmap'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();

// --- 定义变量 ---
const uuid = ref(uuidv1());
let chart: any = null;
const cptData = ref();
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.map) {
          const names = Object.keys(res.data.map);
          const list = [];
          names.forEach((v, idx) => {
            list.push({
              name: v,
              value: res.data.map[v]
            });
          });
          cptData.value = list;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

const loadChart = (attribute) => {
  const chartOption = {
    tooltip: {
      // 自定义代码
    },
    series: [
      {
        type: 'map3D',
        name: attribute.titleText,
        // 相对于父容器比例
        center: ['50%', '50%'],
        selectedMode: 'single', // 地图高亮单选
        regionHeight: attribute.mapHeight, // 地图高度
        map: attribute.map,
        viewControl: {
          // 缩放大小，数值越大，地图越小
          distance: attribute.distance,

          // 上下倾斜角度
          alpha: attribute.alpha,
          // rotateSensitivity: [1, 1],
          // 左右倾斜角度
          beta: attribute.beta
        },
        label: {
          show: attribute.isShowLabel, // 是否显示名字
          color: attribute.labelColor, // 文字颜色
          fontSize: attribute.fontSize, // 文字大小
          fontWeight: 'bold' // 文字大小
        },
        itemStyle: {
          color: attribute.mapBackgroundColor, // 地图背景颜色
          borderWidth: attribute.borderWidth, // 分界线wdith
          borderColor: attribute.borderColor, // 分界线颜色
          opacity: 0.92
        },
        emphasis: {
          label: {
            show: attribute.isShowLed, // 是否显示高亮
            textStyle: {
              color: attribute.ledColor // 高亮文字颜色
            }
          },
          itemStyle: {
            color: attribute.mapLedColor, // 地图高亮颜色
            borderWidth: attribute.mapLedBorderWidth, // 分界线wdith
            borderColor: attribute.mapLedBorderColor // 分界线颜色
          }
        },
        light: {
          main: {
            color: '#fff',
            intensity: 1,
            shadow: true,
            shadowQuality: 'high',
            alpha: 25, //
            beta: 2
          },
          ambient: {
            color: '#fff',
            intensity: 0.6
          }
        },
        data: cptData.value
      }
    ]
  };
  chart?.setOption(chartOption);
};

// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>
<style lang="scss" scoped></style>
