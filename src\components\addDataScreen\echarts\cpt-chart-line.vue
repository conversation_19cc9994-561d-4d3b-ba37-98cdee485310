<template>
  <div style="width: 100%; height: 100%">
    <el-date-picker
      v-show="option.attribute.openYear"
      v-model="yearValue"
      type="year"
      placeholder="选择年"
      size="small"
      style="width: 120px; margin-left: 10px"
      value-format="yyyy"
      @change="changeYear"
    >
    </el-date-picker>
    <div :id="uuid" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-chart-line'
});

const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData = ref();
const yearValue = ref('2025');

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);
// --- 定义方法 ---
const changeYear = () => {
  refreshCptData();
};
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  if (props.option.cptDataForm.dataSource == 2) {
    // 表达式必填
    if (!props.option.cptDataForm.apiUrl) {
      ElMessage.warning('表达式不能为空');
      return;
    }
    // 需要手动替换 expression 里面的年份数据
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl.replace(/"2020"/g, `"${yearValue.value}"`),
      moduleId: props.option.cptDataForm.moduleId,
      taskId: taskId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {

      if (res.code == 200) {
        if (res.data.xdata && res.data.ydata) {
          const obj = {
            xData: res.data.xdata.join(','),
            yData: res.data.ydata.join(',')
          };
          cptData.value = obj;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};
const loadChart = (attribute) => {
  chartOption.value = {
    color: attribute.lineColor,
    title: {
      text: attribute.title,
      subtext: attribute.subtext,
      left: attribute.titleLeft,
      top: attribute.titleTop,
      textStyle: {
        color: attribute.titleTextColor
      },
      subtextStyle: { fontSize: 12, color: attribute.subtextColor }
    },
    grid: {
      x: 10,
      y: 30,
      x2: 10,
      y2: 10,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: getEData(cptData.value.xData),
      axisLabel: {
        show: attribute.xLabelShow,
        color: attribute.xLabelColor
      },
      axisLine: {
        show: attribute.xLineShow,
        lineStyle: {
          color: attribute.xLineColor
        }
      },
      axisTick: {
        //x轴刻度线
        show: attribute.xTickShow
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: attribute.yLabelShow,
        color: attribute.yLabelColor
      },
      axisLine: {
        show: attribute.yLineShow,
        lineStyle: {
          color: attribute.yLineColor
        }
      },
      axisTick: {
        //y轴刻度线
        show: attribute.yTickShow
      },
      splitLine: {
        //网格线
        show: attribute.yGridLineShow
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    series: [
      {
        data: getEData(cptData.value.yData),
        type: 'line',
        smooth: attribute.smooth,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: attribute.areaColor1
            },
            {
              offset: 0.34,
              color: attribute.areaColor2
            },
            {
              offset: 1,
              color: attribute.areaColor3
            }
          ])
        }
      }
    ]
  };
  chart?.setOption(chartOption.value);
};
// 返回数据
const getEData = (data) => {
  if (data) {
    return data.split(',');
  }
  return [];
};
// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));
  refreshCptData();
});
</script>

<style scoped></style>
