<template>
  <div class="flow-container">
    <template v-if="data">
      <component :is="() => nodeFactory(data)" />
    </template>
    <section class="end-node">流程结束</section>
  </div>
</template>

<script setup lang="ts">
import { NodeUtils } from './util';
import { h, VNode } from 'vue';
import { ElPopover, ElIcon } from 'element-plus';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { Plus, ArrowRight, ArrowLeft, Position, Pointer, Close, Connection, Promotion } from '@element-plus/icons-vue';

const props = defineProps<{
  data: any;
  verifyMode?: boolean;
}>();

const emit = defineEmits(['emits']);

const isCondition = (data: any) => data.type === 'condition';
const notEmptyArray = (arr: any[]) => Array.isArray(arr) && arr.length > 0;
const hasBranch = (data: any) => notEmptyArray(data.conditionNodes);
const stopPro = (ev: Event) => ev.stopPropagation();
// 创建条件分支的node  节点
let branchNode: any = null;
/**
 * 创建审批、分支、抄送、办理人的节点
 * @param ctx 上下文
 * @param conf 当前传递进来的节点数据
 * @returns
 */
const createNormalCard = (ctx: any, conf: any) => {
  const classList = ['flow-path-card'];
  const afterTrue = (isTrue: boolean, name: string) => (isTrue && classList.push(name), isTrue);
  const isStartNode = afterTrue(NodeUtils.isStartNode(conf), 'start-node');
  const isApprNode = afterTrue(NodeUtils.isApproverNode(conf), 'approver');
  const isCopyNode = afterTrue(NodeUtils.isCopyNode(conf), 'notifier');
  const isAuditNode = afterTrue(NodeUtils.isAuditNode(conf), 'audit');
  // 判断当前节点是不是条件节点的外层节点
  const isRouteNode = afterTrue(NodeUtils.isRouteNode(conf), 'route');
  return h(
    'section',
    {
      class: classList,
      style: {
        width: '220px',
        minHeight: '82px',
        fontSize: '12px',
        borderRadius: '4px',
        textAlign: 'left',
        cursor: 'pointer',
        overflow: 'hidden',
        position: 'relative',
        boxSizing: 'border-box',
        boxShadow: '0 0 6px 0 rgba(0, 0, 0, 0.3)',
        background: '#fff'
      },
      onClick: (e: Event) => {
        e.stopPropagation();
        eventLancher('edit', conf);
      }
    },
    [
      h(
        'header',
        {
          class: 'header',
          style: {
            paddingLeft: '16px',
            paddingRight: '30px',
            width: '100%',
            height: '24px',
            lineHeight: '24px',
            color: 'white',
            position: 'relative',
            boxSizing: 'border-box',
            backgroundColor: isStartNode ? '#576a95' : isApprNode ? '#ff9431' : isCopyNode ? '#3296fa' : isAuditNode ? '#fb603d' : '#576a95'
          }
        },
        [
          h(
            'div',
            {
              class: 'title-box',
              style: {
                height: '100%',
                width: '190px',
                position: 'relative',
                display: 'inline-block'
              }
            },
            [
              isApprNode &&
                !isRouteNode &&
                h(SvgIcon, {
                  iconClass: 'shenpi',
                  style: {
                    fontSize: '12px',
                    color: 'white',
                    marginRight: '4px',
                    marginTop: '4px',
                    float: 'none'
                  },
                  onClick: stopPro
                }),
              isCopyNode &&
                !isRouteNode &&
                h(
                  ElIcon,
                  {
                    style: {
                      fontSize: '12px',
                      color: 'white',
                      marginRight: '4px'
                    },
                    onClick: stopPro
                  },
                  () => h(Promotion)
                ),
              isAuditNode &&
                !isRouteNode &&
                h(
                  ElIcon,
                  {
                    style: {
                      fontSize: '12px',
                      color: 'white',
                      marginRight: '4px'
                    },
                    onClick: stopPro
                  },
                  () => h(Pointer)
                ),
              h(
                'span',
                {
                  class: 'title-text',
                  style: {
                    verticalAlign: 'middle'
                  }
                },
                conf?.properties?.title || `条件${isRouteNode}`
              ),
              conf?.properties?.actType === 'and' && h('span', { class: 'title-text' }, '（会签）'),
              conf?.properties?.actType === 'or' && h('span', { class: 'title-text' }, '（或签）'),
              !isStartNode &&
                !isRouteNode &&
                h('input', {
                  class: 'title-input',
                  style: {
                    position: 'absolute',
                    left: '0',
                    border: 'none',
                    background: 'inherit',
                    color: 'inherit',
                    opacity: '0',
                    marginTop: '3px'
                  },
                  onClick: stopPro,
                  modelValue: conf?.properties?.title,
                  'onUpdate:modelValue': (val: string) => (conf.properties.title = val)
                })
            ]
          ),
          h(
            'div',
            {
              class: 'actions',
              style: {
                // position: 'absolute',
                // right: '0',
                // top: '0',
                // visibility: 'hidden',
                marginRight: '4px'
              }
            },
            [
              h(
                ElIcon,
                {
                  class: 'icon',
                  onClick: (e: Event) => {
                    e.stopPropagation();
                    eventLancher('deleteNode', conf, props.data);
                  }
                },
                () => h(Close)
              )
            ]
          )
        ]
      ),
      h(
        'div',
        {
          class: 'body',
          style: {
            position: 'relative',
            padding: '12px',
            paddingRight: '30px',
            boxSizing: 'border-box'
          }
        },
        [
          h(
            'span',
            {
              class: 'text',
              style: {
                margin: '0'
              }
            },
            conf.content
          ),
          h(
            'div',
            {
              class: 'icon-wrapper right',
              style: {
                position: 'absolute',
                top: '0',
                right: '0',
                height: '100%',
                width: '14px',
                boxSizing: 'border-box'
              }
            },
            [
              h(
                ElIcon,
                {
                  class: 'icon right-arrow',
                  style: {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)'
                  }
                },
                () => h(ArrowRight)
              )
            ]
          )
        ]
      )
    ]
  );
};

/**
 * 创建条件节点
 * @param ctx 上下文
 * @param conf 当前传递进来的节点数据
 * @returns
 */
const createConditionNode = (ctx: any, conf: any) => {
  if (conf.isdefault && conf.type === 'condition') {
    return h('section', { class: 'flow-path-card condition' }, [
      h('header', { class: 'header' }, [
        h('div', { class: 'title-box', style: 'height: 20px;width:160px;' }, [
          h('span', { class: 'title-text' }, conf.properties.title),
          h('input', {
            class: 'title-input',
            style: 'margin-top:1px;',
            onClick: stopPro,
            modelValue: conf.properties.title,
            'onUpdate:modelValue': (val: string) => (conf.properties.title = val)
          })
        ]),
        h('span', { class: 'priority' }, `优先级${conf.properties.priority + 1}`)
      ]),
      h('div', { class: 'body' }, [h('pre', { class: 'text' }, conf.content)])
    ]);
  }

  return h(
    'section',
    {
      class: 'flow-path-card condition',
      onClick: (e: Event) => {
        e.stopPropagation();
        eventLancher('edit', conf);
      }
    },
    [
      h('header', { class: 'header' }, [
        h('div', { class: 'title-box', style: 'height: 20px;width:160px;' }, [
          h('span', { class: 'title-text' }, conf.properties.title),
          h('input', {
            class: 'title-input',
            style: 'margin-top:1px;',
            onClick: stopPro,
            modelValue: conf.properties.title,
            'onUpdate:modelValue': (val: string) => (conf.properties.title = val)
          })
        ]),
        h('span', { class: 'priority' }, `优先级${conf.properties.priority + 1}`),
        h('div', { class: 'actions' }, [
          h(
            ElIcon,
            {
              class: 'icon',
              onClick: (e: Event) => {
                e.stopPropagation();
                eventLancher('deleteNode', conf, props.data);
              }
            },
            () => h(Close)
          )
        ])
      ]),
      h('div', { class: 'body' }, [
        h(
          'pre',
          {
            class: 'text'
          },
          conf.content
        )
      ]),
      h(
        'div',
        {
          class: 'icon-wrapper left',
          onClick: (e: Event) => {
            e.stopPropagation();
            eventLancher('increasePriority', conf, props.data);
          }
        },
        [
          h(
            ElIcon,
            {
              class: 'icon left-arrow',
              style: {
                fontSize: '14px',
                color: '#666'
              }
            },
            () => h(ArrowLeft)
          )
        ]
      ),
      h(
        'div',
        {
          class: 'icon-wrapper right',
          onClick: (e: Event) => {
            e.stopPropagation();
            eventLancher('decreasePriority', conf, props.data);
          }
        },
        [
          h(
            ElIcon,
            {
              class: 'icon right-arrow',
              style: {
                fontSize: '14px',
                color: '#666'
              }
            },
            () => h(ArrowRight)
          )
        ]
      )
    ]
  );
};
/**
 * 创建添加节点按钮
 * @param ctx 上下文
 * @param data 当前传递进来的节点数据
 * @param isBranch 是否是分支节点
 * @returns
 */
const addNodeButton = (ctx: any, data: any, isBranch = false) => {
  const couldAddBranch = !hasBranch(data) || isBranch;
  // 只有非条件节点和条件分支树下面的那个按钮 才能添加新分支树
  // if (!couldAddBranch) return null;
  const isEmpty = data.type === 'empty' || data.type === 'route';
  if (isEmpty && !isBranch) return null;
  // 如果是条件分支节点，不显示添加按钮
  // if (data.type === 'condition') return null;
  return h(
    'div',
    {
      class: 'add-node-btn-box flex justify-center',
      style: {
        padding: '20px 0 32px',
        position: 'relative'
      }
    },
    [
      h(
        'div',
        {
          class: 'add-node-btn',
          style: {
            width: '30px',
            height: '30px',
            background: '#fff',
            borderRadius: '50%',
            border: '1px solid #cacaca',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            boxShadow: '0 2px 5px 0 rgba(0, 0, 0, 0.1)',
            '&:hover': {
              background: '#3296fa',
              borderColor: '#3296fa',
              color: '#fff'
            }
          }
        },
        [
          h(
            ElPopover,
            {
              placement: 'right',
              trigger: 'click',
              width: '400',
              popperClass: 'add-node-popover'
            },
            {
              default: () =>
                h(
                  'div',
                  {
                    class: 'condition-box',
                    style: {
                      padding: '20px'
                    }
                  },
                  [
                    h(
                      'div',
                      {
                        style: {
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '20px',
                          width: '100%'
                        }
                      },
                      [
                        h(
                          'div',
                          {
                            class: 'condition-svg',
                            style: {
                              textAlign: 'center',
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column'
                            },
                            onClick: () => eventLancher('addApprovalNode', data, isBranch)
                          },
                          [
                            h(SvgIcon, {
                              class: 'node-icon',
                              iconClass: 'shenpi',
                              style: {
                                width: '32px',
                                height: '32px',
                                display: 'block'
                              }
                            }),
                            [
                              h(
                                'span',
                                {
                                  style: {
                                    color: '#333',
                                    height: '6px'
                                  }
                                },
                                '审批人'
                              )
                            ]
                          ]
                        ),
                        h(
                          'div',
                          {
                            class: 'condition-icon icon-hover',
                            onClick: () => eventLancher('addCopyNode', data, isBranch)
                          },
                          [
                            h(
                              ElIcon,
                              {
                                style: {
                                  width: '32px',
                                  height: '32px',
                                  display: 'block',
                                  margin: '32px auto 16px ',
                                  fontSize: '32px'
                                }
                              },
                              () => h(Promotion)
                            ),
                            [
                              h(
                                'span',
                                {
                                  style: {
                                    color: '#333'
                                  }
                                },
                                '抄送人'
                              )
                            ]
                          ]
                        ),
                        h(
                          'div',
                          {
                            class: 'condition-svg',
                            onClick: () => eventLancher('appendBranch', data, isBranch)
                          },
                          [
                            h(SvgIcon, {
                              class: 'node-icon',
                              iconClass: 'fenzhi',
                              style: {
                                width: '30px',
                                height: '30px',
                                display: 'block'
                              }
                            }),
                            [
                              h(
                                'span',
                                {
                                  style: {
                                    color: '#333',
                                    height: '6px'
                                  }
                                },
                                '条件分支'
                              )
                            ]
                          ]
                        ),
                        h(
                          'div',
                          {
                            class: 'condition-icon icon-hover',
                            onClick: () => eventLancher('addAuditNode', data, isBranch)
                          },
                          [
                            h(
                              ElIcon,
                              {
                                class: 'approvel-hover',
                                style: {
                                  width: '32px',
                                  height: '32px',
                                  display: 'block',
                                  margin: '32px auto 16px',
                                  fontSize: '32px'
                                }
                              },
                              () => h(Pointer)
                            ),
                            [
                              h(
                                'span',
                                {
                                  style: {
                                    color: '#333'
                                  }
                                },
                                '办理人'
                              )
                            ]
                          ]
                        )
                      ]
                    )
                  ]
                ),
              reference: () =>
                h(
                  'button',
                  {
                    class: 'btn',
                    type: 'button',
                    style: {
                      border: 'none',
                      background: 'none',
                      cursor: 'pointer',
                      outline: 'none'
                    }
                  },
                  [
                    h(
                      ElIcon,
                      {
                        style: {
                          fontSize: '16px',
                          color: '#666'
                        }
                      },
                      () => h(Plus)
                    )
                  ]
                )
            }
          )
        ]
      )
    ]
  );
};
/**
 * 创建节点
 * @param data 当前传递进来的节点数据
 * @returns
 */
const nodeFactory = (data: any): VNode | null => {
  if (!data) return null;
  if (data.type === 'route' && !hasBranch(data)) return null;
  const showErrorTip = props.verifyMode && NodeUtils.checkNode(data) === false;
  const isRoute = data.type === 'route';
  // const isCondition = data.type === 'condition';
  const selfNode = h('div', { class: 'node-wrap' }, [
    !isRoute &&
      h(
        'div',
        {
          class: `node-wrap-box ${data.type} ${showErrorTip ? 'error' : ''}`
        },
        [
          showErrorTip &&
            h(
              'el-tooltip',
              {
                content: '未设置条件',
                placement: 'top',
                effect: 'dark'
              },
              {
                default: () =>
                  h(
                    'div',
                    {
                      class: 'error-tip',
                      onClick: (e: Event) => {
                        e.stopPropagation();
                        //  这几个事件 edit  就是打开右侧弹框的
                        eventLancher('edit', data);
                      }
                    },
                    '!'
                  )
              }
            ),
          data.type === 'condition' ? createConditionNode(null, data) : createNormalCard(null, data),
          addNodeButton(null, data)
        ]
      )
  ]);

  if (hasBranch(data)) {
    branchNode = h('div', { class: 'branch-wrap' }, [
      h('div', { class: 'branch-box-wrap' }, [
        h('div', { class: 'branch-box flex justify-center relative' }, [
          h(
            'button',
            {
              class: 'btn',
              onClick: (e: Event) => {
                e.stopPropagation();
                eventLancher('appendConditionNode', data);
              }
            },
            '添加条件'
          ),
          ...data.conditionNodes.map((d: any) => nodeFactory(d))
        ])
      ]),
      addNodeButton(null, data, true)
    ]);

    return h(
      'div',
      {
        class: 'col-box',
        onClick: (e: Event) => {
          e.stopPropagation();
          eventLancher('edit', data);
        }
      },
      [selfNode, branchNode, nodeFactory(data.childNode)]
    );
  }
  if (isCondition(data)) {
    return h('div', { class: 'col-box' }, [
      h('div', { class: 'center-line' }),
      h('div', { class: 'top-cover-line' }),
      h('div', { class: 'bottom-cover-line' }),
      selfNode,
      // branchNode,
      data.childNode ? nodeFactory(data.childNode) : null
    ]);
  }
  return h('div', { class: 'col-box' }, [selfNode, nodeFactory(data.childNode)]);
};
/**
 * 事件发射器
 * @param event 事件名
 * @param args 事件参数
 */
const eventLancher = (event: string, ...args: any[]) => {
  emit('emits', { event, args });
};
</script>

<script lang="ts">
export default {
  name: 'FlowCard'
};
</script>

<style lang="scss">
@import './index.scss';
</style>
