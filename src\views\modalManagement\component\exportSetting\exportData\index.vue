<!-- 数据列表 -->
<template>
  <div class="exportData-main">
    <div class="title">数据列表</div>
    <div class="normal-title">提示文字</div>
    <el-input v-model="placeholder" placeholder="请输入提示文字"></el-input>
    <div class="normal-title">单次导出数据数量</div>
    <div class="flex-row">
      <div class="flex-item">
        <div class="label">最少</div>
        <el-input v-model="minNum" class="content"></el-input>
      </div>
      <div class="flex-item">
        <div class="label">最多</div>
        <el-input v-model="maxNum" class="content" placeholder="无限制"></el-input>
      </div>
    </div>
    <div class="normal-title">是否必填</div>
    <el-select v-model="must" placeholder="请选择" style="width: 100%">
      <el-option label="必填" value="1"></el-option>
      <el-option label="不必填" value="0"></el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

// 定义 props
const props = defineProps<{
  sjList: {
    placeholder: string;
    minNum: string;
    maxNum: string;
    must: string;
  };
}>();

// 定义响应式数据
const placeholder = ref<string>('请选择数据');
const minNum = ref<string>('1');
const maxNum = ref<string>('');
const must = ref<string>('1');

// 监听 props
watch(
  () => props.sjList,
  (val) => {
    if (val) {
      placeholder.value = val.placeholder;
      minNum.value = val.minNum;
      maxNum.value = val.maxNum;
      must.value = val.must;
    }
  },
  { deep: true }
);

// 定义提交方法
const sumbit = () => {
  const obj = {
    minNum: minNum.value,
    placeholder: placeholder.value,
    maxNum: maxNum.value,
    must: must.value
  };
  return obj;
};
defineExpose({ sumbit });
</script>
<style lang="scss" scoped>
.exportData-main {
  width: 100%;
  height: 100%;
  padding: 20px 16px;
  color: #161d26;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .normal-title {
    font-size: 14px;
    margin: 16px 0px;
    font-weight: bold;
    display: flex;
    align-items: center;
    .right-btn {
      color: var(--current-color);
      cursor: pointer;
    }
  }
  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    .flex-item {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      .label {
        margin-right: 8px;
        color: #8291a9;
        width: 40px;
        text-align: right;
      }
      .content {
        flex: 1;
      }
    }
  }
}
</style>
