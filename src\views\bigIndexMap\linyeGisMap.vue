<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" class="gisMap-main">
    <div id="viewDiv" ref="gisMap" class="map"></div>
    <div class="search-div">
      <div class="search-item">
        <div class="search-hr"></div>
        <div class="big-span">小班数据</div>
      </div>
      <div class="search-item" style="margin-right: 24px; justify-content: flex-end">
        <el-select
          v-model="lyTitleId"
          placeholder="请选择"
          size="small"
          style="margin-left: 14px"
          value-key="id"
          v-loadmore="getMoreLinyeData"
          @change="getCurrentParcel"
        >
          <el-option v-for="item in lyOptions" :key="item.id" :label="item.title" :value="item"> </el-option>
        </el-select>
      </div>
    </div>
    <div class="right-handle-div">
      <!-- <div class="min-handle-item" @click="zoomMap">
        <svg-icon icon-class="zoom" />
      </div> -->
      <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
        <svg-icon icon-class="map" />
      </div>
    </div>
    <div class="map-change" v-show="showChangeMap">
      <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">地图</div>
      </div>
      <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">影像</div>
      </div>
    </div>
    <div class="map-copyRight">
      <img src="https://api.tianditu.gov.cn/v4.0/image/logo.png" alt="" class="copy-ico" />
      GS（2024）0568号 - 甲测资字1100471
    </div>
    <router-link target="_blank" :to="{ path: '/bigLinyeMap', query: { lyTitleId: JSON.stringify(lyTitleId) } }">
      <div class="right-bottom-zoom">
        <svg-icon icon-class="zoom" />
      </div>
    </router-link>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getLinyeList, getLinyeDetail } from '@/api/project';
import { getToken } from '@/utils/auth';
import { loadModules } from 'esri-loader';
import { ElMessage } from 'element-plus';

interface Props {
  lyOptionList?: any[];
  nowCheckedZD?: number;
  appType?: number;
}

const props = withDefaults(defineProps<Props>(), {
  lyOptionList: () => [],
  nowCheckedZD: 0,
  appType: 2
});

const emit = defineEmits(['openInfo', 'changeNowCheckedZD']);

const route = useRoute();

const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

const tiandituBaseUrl = 'http://{subDomain}.tianditu.gov.cn';
const token = 'f250cc0a2b1fe177a3a5b8ce821a6c8d';

let fontLayer: any = null;
let graphicsLayer: any = null;
let housesLayer: any = null;
let tiledLayer: any = null;
const tiledLayerAnno: any = null;
const normalLayer: any = null;
const normalAnno: any = null;

const gisMap = ref<HTMLElement | null>(null);
const map = ref<any>(null);
const view = ref<any>(null);
const fullscreenLoading = ref(false);
const showChangeMap = ref(false);
const checkedMap = ref('image');
const lyTitleId = ref<any>(undefined);
const lyOptions = ref<any[]>([]);
const currentSelectZd = ref<any>({});
const oldHighlight = ref<any>(null);
const allHouses = ref<any[]>([]);
const allZD = ref<any[]>([]);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10
});

const userPages = ref(0);

// 监听当前选中的宗地id变化
watch(
  () => props.nowCheckedZD,
  async (val) => {
    if (val) {
      try {
        const res = await getLinyeDetail(val);
        if (res.code === 200) {
          currentSelectZd.value = res.data;
          highlightZD();
          housesLayer.removeAll();
          allHouses.value = res.data.quadratList;
          initHouses();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('获取林业详情失败:', error);
      }
    }
  }
);

// 初始化地图
const init = () => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar'
    ],
    config
  ).then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar]) => {
    // 初始化地图图层
    tiledLayer = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + token,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({ wkid: 102100 }),
      getTileUrl: function (level: number, row: number, col: number) {
        if (level <= 18) {
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=img_w/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + token;
        }
        return '';
      }
    });

    // 其他图层初始化代码...
    // 这里省略了其他图层的初始化代码，保持原有逻辑不变

    map.value = new Map({
      basemap: {
        baseLayers: [tiledLayer, tiledLayerAnno, normalLayer, normalAnno]
      },
      logo: false,
      spatialReference: { wkid: 102100 }
    });

    view.value = new MapView({
      container: gisMap.value,
      map: map.value,
      center: [116.39126, 39.90763],
      zoom: 6,
      ui: { components: [] },
      spatialReference: new SpatialReference({ wkid: 102100 })
    });

    // 设置地图约束
    view.value.constraints = {
      minZoom: 2,
      maxZoom: 22
    };

    // 添加控件
    const compassWidget = new Compass({ view: view.value });
    view.value.ui.add(compassWidget, 'bottom-right');
    const zoom = new Zoom({ view: view.value });
    view.value.ui.add(zoom, 'bottom-right');
    view.value.ui.remove('attribution');
    const scaleBar = new ScaleBar({
      view: view.value,
      unit: 'metric',
      style: 'line'
    });
    view.value.ui.add(scaleBar, { position: 'bottom-right' });

    // 添加图层
    fontLayer = new GraphicsLayer({ id: '234' });
    map.value.add(fontLayer, 9999);
    graphicsLayer = new GraphicsLayer({ id: '123' });
    map.value.add(graphicsLayer);
    housesLayer = new GraphicsLayer({ id: '456' });
    map.value.add(housesLayer);

    // 地图点击事件
    view.value.on('click', function (event: any) {
      view.value.hitTest(event).then((res: any) => {
        const id = res.results[0].graphic.id;
        if (id === props.nowCheckedZD) {
          graphicsLayer.graphics.items.forEach((v: any) => {
            if (v.id === id) {
              if (oldHighlight.value) {
                oldHighlight.value.remove();
              }
              view.value.whenLayerView(v.layer).then(function (layerView: any) {
                oldHighlight.value = layerView.highlight(v);
              });
              emit('openInfo');
            }
          });
        } else {
          if (res.results[0].layer.id === '123') {
            emit('changeNowCheckedZD', id);
          }
        }
      });
    });
  });
};

// 获取林业列表数据
const getLinyeData = async () => {
  try {
    const res = await getLinyeList(queryParams.value);
    if (res.code === 200) {
      lyOptions.value = res.data.rows;
      const pages = Math.ceil(res.data.total / 10);
      userPages.value = pages;
      return lyOptions.value;
    } else {
      ElMessage.error(res.msg);
      return [];
    }
  } catch (error) {
    console.error('获取林业列表失败:', error);
    return [];
  }
};

// 触底获取更多信息
const getMoreLinyeData = async () => {
  if (userPages.value > queryParams.value.pageNum) {
    queryParams.value.pageNum += 1;
    try {
      const res = await getLinyeList(queryParams.value);
      if (res.code === 200) {
        lyOptions.value.push(...res.data.rows);
      } else {
        ElMessage.error(res.msg);
      }
    } catch (error) {
      console.error('获取更多林业数据失败:', error);
    }
  }
};

// 切换底图
const changeMap = (type: number) => {
  if (type === 1) {
    checkedMap.value = 'normal';
    tiledLayer.visible = false;
    tiledLayerAnno.visible = false;
    normalLayer.visible = true;
    normalAnno.visible = true;
  } else if (type === 2) {
    checkedMap.value = 'image';
    tiledLayer.visible = true;
    tiledLayerAnno.visible = true;
    normalLayer.visible = false;
    normalAnno.visible = false;
  }
};

// 点击时高亮当前的宗地
const getCurrentParcel = (item: any) => {
  currentSelectZd.value = item;
  highlightZD();
  if (housesLayer) {
    housesLayer.removeAll();
  }
  if (item.houseList && item.houseList.length !== 0) {
    allHouses.value = item.houseList;
  }
  initHouses();
};

// 初始化林业
const initLinye = (val: any[]) => {
  if (graphicsLayer) {
    graphicsLayer.removeAll();
    housesLayer.removeAll();
    fontLayer.removeAll();
  }
  allZD.value = val;
  drawZD();
};

onMounted(() => {
  init();
  getLinyeData().then((res) => {
    let item = null;
    if (route.query.lyTitleId !== undefined) {
      item = JSON.parse(route.query.lyTitleId as string);
    }
    res.forEach((e: any) => {
      if (item && e.id === item.id) {
        lyTitleId.value = item;
        getCurrentParcel(item);
      } else {
        lyTitleId.value = res[0];
      }
    });
    initLinye(res);
  });
});

onBeforeUnmount(() => {
  if (view.value) {
    view.value.container = null;
  }
});
</script>

<style scoped lang="scss">
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table) th.el-table__cell {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top):after {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top):before {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-scale-bar).esri-widget {
  position: absolute;
  top: 175px;
}
:deep(.esri-zoom) .esri-widget--button {
  background-color: transparent;
  color: #fff;
}
:deep(.esri-zoom) .esri-widget--button:hover {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 70px;
}
:deep(.esri-view) .esri-view-surface {
  border-radius: 6px;
}
.gisMap-main {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 0;
  .map {
    width: 100%;
    height: 100%;
  }
  .search-div {
    width: 100%;
    height: 60px;
    background: #fff;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    top: 0px;
    border-radius: 6px 6px 0px 0px;
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      .big-span {
        font-size: 16px;
        color: #000000;
        font-weight: 600;
        margin-left: 18px;
      }
    }
    .search-hr {
      width: 4px;
      height: 24px;
      background: linear-gradient(135deg, #0081ff 0%, #22cce2 100%);
      border-radius: 0px 2px 2px 0px;
    }
  }
  .rght-handle-div {
    position: absolute;
    width: 20px;
    height: 36px;
    background: #f1efef;
    border-radius: 4px 0px 0px 4px;
    right: 0px;
    top: 45%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #999;
    z-index: 99;

    .zankai {
      width: 16px;
      height: 16px;
    }
  }

  .rght-handle-div :hover {
    color: #1b9af7;
  }

  .project-detail {
    position: absolute;
    width: 500px;
    height: calc(100% - 30px);
    background: #ffffff;
    right: 0px;
    top: 0px;
    padding: 15px;
    // opacity: .8;
    .head-div {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .tab-div {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      overflow-x: auto;

      .tab-row {
        height: 30px;
        min-width: 110px;
        font-size: 14px;
        color: #999999;
        cursor: pointer;
        background-color: #f6f7f8;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tab-active {
        background: #e7f5ff;
        color: #333333;
      }
      .error-tab {
        border: #ff5555 solid 1px;
      }
    }
    .detail-content {
      height: calc(100% - 123px);
      overflow: auto;
      .flex-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        .item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .label {
            color: #333333;
            font-size: 14px;
            margin-bottom: 15px;
          }
        }
        .margin {
          margin-left: 20px;
        }
      }
      .flex-box {
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px;
        margin-bottom: 20px;
        .flex-row {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .label {
              color: #333333;
              font-size: 14px;
              margin-bottom: 15px;
            }
            .img-box {
              width: 100%;
              height: 140px;
            }
          }
          .margin {
            margin-left: 20px;
          }
        }
        .right-handle {
          display: flex;
          flex-basis: row;
          justify-content: flex-end;

          .span {
            color: #999999;
            cursor: pointer;
          }
        }
      }

      .grid-container {
        display: grid;
        grid-gap: 20px 20px;
        grid-template-columns: repeat(2, 45%);
        width: 100%;

        .grid-item {
          height: 300px;
        }
      }
    }
  }

  .more-right {
    right: 500px;
  }

  .dialog-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    .label {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .content {
    }
  }

  .zdfclabel {
    display: inline-block;
    width: 20%;
  }

  .zdfccontent {
    display: inline-block;
    width: 70%;
  }
  .uploadimgspan {
    display: inline-block;
    font-size: 3pt;
    font-style: italic;
  }

  .dialog-box-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .dialog-row-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // justify-content: space-between;
    margin-bottom: 20px;

    .checkbox-item {
      width: 20%;
      margin-bottom: 5px;
    }
  }

  .dialog-label {
    margin-bottom: 10px;
  }

  .dialog-row-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .red-span {
    color: #ff5555;
    margin-bottom: 10px;
  }

  .zhipai {
    position: absolute;
    top: 70px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }

  .gengxin {
    position: absolute;
    top: 110px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }
  .right-handle-div {
    position: absolute;
    right: 16px;
    bottom: 140px;
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #fff;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
      position: absolute;
      right: 0px;
      top: 60px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .map-change {
    position: absolute;
    bottom: 180px;
    right: 60px;
    display: flex;
    flex-direction: row;
    .map-item {
      width: 96px;
      height: 72px;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: flex-end;
      .map-footer {
        width: 100%;
        height: 22px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
        border-radius: 0px 0px 8px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #fff;
      }
      .map-footer-active {
        color: #1b9af7;
      }
    }
    .map-left {
      background-image: url('../../assets/images/normal-map.png');
      background-size: cover;
    }
    .map-right {
      background-image: url('../../assets/images/image-map.png');
      background-size: cover;
      margin-left: 8px;
    }
    .map-active {
      border: #1b9af7 solid 1px;
    }
  }
  .map-copyRight {
    position: absolute;
    bottom: 5px;
    left: 10px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    .copy-ico {
      width: 53px;
      height: 22px;
      margin-right: 10px;
    }
  }
  .right-bottom-zoom {
    position: absolute;
    bottom: 200px;
    right: 16px;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 8px 8px;
    cursor: pointer;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .right-bottom-zoom:hover {
    background: #fff;
    color: #000;
  }
}
</style>

<style>
.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 110px;
  height: 110px;
}
.upload-demo .el-upload-list--picture-card img {
  width: 110px;
  height: 110px;
}
.uoloadSty .el-upload--picture-card {
  width: 110px;
  height: 110px;
  line-height: 110px;
}

.disUoloadSty .el-upload--picture-card {
  display: none;
  /* 上传按钮隐藏 */
}
</style>
