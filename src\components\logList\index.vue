<!-- 日志 -->
<template>
  <div class="logList-main">
    <el-timeline>
      <el-timeline-item :timestamp="filterTime(item.operTime)" placement="top" v-for="(item, index) in list" :key="index">
        <el-card>
          <div class="card-body">
            <div class="flex-row"><span class="label">操作者：</span>{{ item.operName }}</div>
            <div class="flex-row">
              <span class="label">操作平台：</span>
              <span class="end-label">
                {{ item.appType }}
              </span>
            </div>
            <div class="flex-row">
              <span class="label">要素名称：</span>
              <span class="end-label">
                {{ item.ruleName }}
              </span>
            </div>
            <div class="flex-row">
              <span class="label">图形名称：</span>
              <span class="end-label">
                {{ item.parcelName }}
              </span>
            </div>
            <div class="flex-row">
              <span class="label">操作内容：</span>
              <span class="end-label">
                {{ item.operParam }}
              </span>
            </div>
            <el-button
              style="width: 50px"
              type="text"
              size="small"
              @click="dataComparison(item)"
              v-show="item.logDetailList && item.logDetailList.length != 0"
            >
              操作详情
            </el-button>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    <div v-if="!list || list.length == 0">
      <el-empty description="暂无日志"></el-empty>
    </div>
    <!-- 数据比对 -->
    <el-dialog title="数据比对" v-model="compareDialog" width="875px" :close-on-click-modal="false" v-dialogDrag :before-close="handleCloseCompare">
      <div class="compare-box">
        <div class="notice">
          <div class="notice-item">
            <div class="empty color-box"></div>
            <span>当前版本已删除</span>
          </div>
          <div class="notice-item">
            <div class="diff color-box"></div>
            <span>当前版本和上一个版本不一致</span>
          </div>
          <div class="notice-item">
            <div class="new color-box"></div>
            <span>当前版本新增字段</span>
          </div>
          <div class="notice-item">
            <div class="gray color-box"></div>
            <span>无改变</span>
          </div>
        </div>
        <el-select size="mini" v-model="activeName" placeholder="请选择" style="margin-top: 10px" @change="changeSel">
          <el-option v-for="item in logDetailList" :key="item.linkId" :label="item.groupName" :value="item.linkId"> </el-option>
        </el-select>
        <div class="compare-main">
          <div class="item">
            <div class="title">
              上一个版本
              <span style="margin-left: 10px; font-size: 12px">{{ formatDateYmdhm(lastTime) }}</span>
              <span style="margin-left: 10px; font-size: 12px">操作人：{{ lastOperName }}</span>
            </div>
            <div class="content" ref="leftContent" @scroll="syncScroll('left')">
              <!-- 普通属性组 -->
              <template v-if="[1, 7].includes(linkType)">
                <div class="flex-row" v-for="(item, index) in lastList" :key="index" :class="getColor(item)">
                  <span class="label">{{ item.fieldCn }}：</span>
                  <span class="end-label">
                    <template v-if="item.valueMethod == 'upload'">
                      <el-image
                        v-for="(item, index) in item.fieldValue"
                        :key="index"
                        style="width: 40px; height: 40px"
                        :src="`${baseUrl}${item.url}?token=${token}`"
                        :preview-src-list="srcList"
                      >
                      </el-image>
                    </template>
                    <template v-else-if="['select'].includes(item.valueMethod)">
                      {{ filterOption(item.fieldValue, item.attribution.options) }}
                    </template>
                    <template v-else>
                      {{ item.fieldValue }}
                    </template>
                  </span>
                </div>
              </template>
              <!-- 多采 -->
              <template v-else>
                <div class="child-box" v-for="(obj, odx) in lastList" :key="odx">
                  <div class="title">{{ obj.title }}</div>
                  <div class="flex-row" v-for="(item, index) in obj.fieldModelList" :key="index" :class="getColor(item)">
                    <span class="label">{{ item.fieldCn }}：</span>
                    <span class="end-label">
                      <template v-if="item.valueMethod == 'upload'">
                        <el-image
                          v-for="(item, index) in item.fieldValue"
                          :key="index"
                          style="width: 40px; height: 40px"
                          :src="`${baseUrl}${item.url}?token=${token}`"
                          :preview-src-list="srcList"
                        >
                        </el-image>
                      </template>
                      <!-- 身份证正反面 -->
                      <template v-else-if="['idCardBitmap', 'idCardBitmap'].includes(item.valueMethod)">
                        <el-image
                          v-if="item.fieldValue"
                          style="width: 40px; height: 40px"
                          :src="`${baseUrl}${item.fieldValue}?token=${token}`"
                          :preview-src-list="[`${baseUrl}${item.fieldValue}?token=${token}`]"
                        >
                        </el-image>
                      </template>
                      <template v-else-if="['select'].includes(item.valueMethod)">
                        {{ filterOption(item.fieldValue, item.attribution.options) }}
                      </template>
                      <template v-else>
                        {{ item.fieldValue }}
                      </template>
                    </span>
                  </div>
                </div>
              </template>
              <el-empty description="暂无版本信息" v-show="lastList.length == 0"></el-empty>
            </div>
          </div>
          <div class="item" style="margin-left: 10px">
            <div class="title">
              当前版本
              <span style="margin-left: 10px; font-size: 12px">{{ formatDateYmdhm(nowTime) }}</span>
              <span style="margin-left: 10px; font-size: 12px">操作人：{{ nowOperName }}</span>
            </div>
            <div class="content" ref="rightContent" @scroll="syncScroll('right')">
              <!-- 普通属性组 -->
              <template v-if="[1, 7].includes(linkType)">
                <div class="flex-row" v-for="(item, index) in nowList" :key="index" :class="getColor(item)">
                  <span class="label">{{ item.fieldCn }}：</span>
                  <span class="end-label">
                    <template v-if="item.valueMethod == 'upload'">
                      <el-image
                        v-for="(item, index) in item.fieldValue"
                        :key="index"
                        style="width: 40px; height: 40px"
                        :src="`${baseUrl}${item.url}?token=${token}`"
                        :preview-src-list="[`${baseUrl}${item.url}?token=${token}`]"
                      >
                      </el-image>
                    </template>
                    <!-- 身份证正反面 -->
                    <template v-else-if="['idCardBitmap', 'idCardBitmap'].includes(item.valueMethod)">
                      <el-image
                        v-if="item.fieldValue"
                        style="width: 40px; height: 40px"
                        :src="`${baseUrl}${item.fieldValue}?token=${token}`"
                        :preview-src-list="[`${baseUrl}${item.fieldValue}?token=${token}`]"
                      >
                      </el-image>
                    </template>
                    <template v-else-if="['select'].includes(item.valueMethod)">
                      {{ filterOption(item.fieldValue, item.attribution.options) }}
                    </template>
                    <template v-else>
                      {{ item.fieldValue }}
                    </template>
                  </span>
                </div>
              </template>
              <!-- 多采 -->
              <template v-else>
                <div class="child-box" v-for="(obj, odx) in nowList" :key="odx">
                  <div class="title">{{ obj.title }}</div>
                  <div class="flex-row" v-for="(item, index) in obj.fieldModelList" :key="index" :class="getColor(item)">
                    <span class="label">{{ item.fieldCn }}：</span>
                    <span class="end-label">
                      <template v-if="item.valueMethod == 'upload'">
                        <el-image
                          v-for="(item, index) in item.fieldValue"
                          :key="index"
                          style="width: 40px; height: 40px"
                          :src="`${baseUrl}${item.url}?token=${token}`"
                          :preview-src-list="srcList"
                        >
                        </el-image>
                      </template>
                      <!-- 身份证正反面 -->
                      <template v-else-if="['idCardBitmap', 'idCardBitmap'].includes(item.valueMethod)">
                        <el-image
                          v-if="item.fieldValue"
                          style="width: 40px; height: 40px"
                          :src="`${baseUrl}${item.fieldValue}?token=${token}`"
                          :preview-src-list="[`${baseUrl}${item.fieldValue}?token=${token}`]"
                        >
                        </el-image>
                      </template>
                      <template v-else-if="['select'].includes(item.valueMethod)">
                        {{ filterOption(item.fieldValue, item.attribution.options) }}
                      </template>
                      <template v-else>
                        {{ item.fieldValue }}
                      </template>
                    </span>
                  </div>
                </div>
              </template>
              <el-empty description="暂无版本信息" v-show="nowList.length == 0"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getLog as getLogApi, selectFieldCompare } from '@/api/project';
import { getToken } from '@/utils/auth';
import { filterOption } from '../../utils/filters';
import { formatDateYmdhm } from '@/utils/filters';
const list = ref([]); //日志
const compareDialog = ref(false); //数据比对
const logDetailList = ref([]); //操作的属性组内容
const activeName = ref(''); //当前对比的属性组
const lastList = ref([]); //老的
const nowList = ref([]); //新的
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const token = getToken();
const lastTime = ref(null); //上一次的时间
const nowTime = ref(null); //当前的时间
const nowOperName = ref(null); //当前操作人
const lastOperName = ref(null); //上一次操作人
const parcelId = ref(null); //当前操作数据id
const linkType = ref(0); //当前比对的属性组的类型 2/3/4 点线面共享 5/6 线、点子要素 7 无图形 1普通属性组
const nowGroup = ref(null); //当前比对的属性组
const leftContent = ref<any>(null); //左
const rightContent = ref<any>(null); //右

// 定义 props
const props = defineProps<{
  ruleTree: any; // 规则树
}>();

/**
 * 获取宗地日志
 * @param val
 */
const getLog = (val: any) => {
  getLogApi(val).then((res) => {
    if (res.code == 200) {
      list.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 同步滚动
 * @param source
 */
const syncScroll = (source: any) => {
  nextTick(() => {
    const left = leftContent.value;
    const right = rightContent.value;
    if (source === 'left' && left) {
      right.scrollTop = left.scrollTop;
    } else if (source === 'right' && right) {
      left.scrollTop = right.scrollTop;
    }
  });
};

/**
 * 关闭比较
 */
const handleCloseCompare = () => {
  compareDialog.value = false;
  if (leftContent.value) {
    leftContent.value.removeEventListener('scroll', syncScroll);
  }
  if (rightContent.value) {
    rightContent.value.removeEventListener('scroll', syncScroll);
  }
};

/**
 * 选择属性组 比较的操作日志
 * @param item 日志数据
 */
const dataComparison = (item: any) => {
  parcelId.value = item.parcelId;
  nowOperName.value = item.operName;
  logDetailList.value = item.logDetailList;
  // 默认执行比对第一条
  activeName.value = logDetailList.value[0].linkId;
  linkType.value = logDetailList.value[0].linkType;
  getDetailItem(logDetailList.value[0]);
  compareDialog.value = true;
};

/**
 * 迭代获取对应的属性组
 * @param {string} linkId 需要查找的 linkId
 * @returns {object|null} 找到的属性组对象，如果未找到则返回 null
 */
const getNowGroup = (list: any[], linkId: number) => {
  for (let i = 0; i < list.length; i++) {
    for (let j = 0; j < list[i].fieldGroupModelList.length; j++) {
      if (list[i].fieldGroupModelList[j].linkId == linkId) {
        nowGroup.value = list[i].fieldGroupModelList[j];
        break;
      }
    }
    if (list[i].list && list[i].list.length > 0) {
      getNowGroup(list[i].list, linkId);
    }
  }
};
/**
 * 比对某个属性组
 * @param{object} item 选择的属性组
 * @returns {void}
 */
const getDetailItem = (item: any) => {
  const params = {
    batch: item.batchId,
    linkId: item.linkId,
    parcelId: parcelId.value
  };

  selectFieldCompare(params).then((res) => {
    if (res.code == 200) {
      //根据 item.linkid 在this.ruleTree 中迭代找到对应的属性组
      // }
      getNowGroup(props.ruleTree, item.linkId);
      // // 先判断是否有上一个版本信息
      // if (!res.data.last || (res.data.last && res.data.last.length==0)) {
      //     return;
      // }
      let lastAttr = null;
      if (res.data.last && res.data.last.length != 0) {
        lastAttr = res.data.last[0].attribution;
      }
      let nowAttr = null;
      if (res.data.now && res.data.now.length != 0) {
        nowAttr = res.data.now[0].attribution;
      }
      if (res.data.lastInfo) {
        lastTime.value = res.data.lastInfo.operTime;
      }
      nowTime.value = res.data.now[0].createTime;
      // 需要判断类型 通过linkType 判断
      // 1 普通属性组 2/3/4 点线面共享 5/6 线、点子要素 7 无图形
      if (linkType.value == 1) {
        //普通属性组
        getNormalAttr(lastAttr, nowAttr, nowGroup.value);
      } else if ([5, 6].includes(linkType.value)) {
        //子要素
        getChildAttr(lastAttr, nowAttr, nowGroup.value);
      } else if ([2, 3, 4].includes(linkType.value)) {
        //多采
        getMutiAttr(res.data.last, res.data.now, nowGroup.value);
      }
      if (res.data.lastInfo) {
        lastOperName.value = res.data.lastInfo.operName;
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 组装普通属性组
 * @param lastAttr 上一个
 * @param nowAttr 当前
 * @param nowGroup 当前
 */
const getNormalAttr = (lastAttr: any, nowAttr: any, nowGroup: any) => {
  lastList.value = [];
  nowList.value = [];
  nowGroup.fieldModelList.forEach((item: any) => {
    const old_obj = {
      fieldCn: item.fieldCn,
      fieldName: item.fieldName,
      fieldValue: lastAttr?.[item.fieldName],
      valueMethod: item.valueMethod,
      attribution: item.attribution
    };
    const new_obj = {
      fieldCn: item.fieldCn,
      fieldName: item.fieldName,
      fieldValue: nowAttr?.[item.fieldName],
      valueMethod: item.valueMethod,
      attribution: item.attribution
    };
    // 对比 如果老的值存在，新的值不存在 则新的设置为红色 值不一样两个值设置为淡红色
    // 如果新的值存在，老的值不存在 则新的设置为绿色
    if (lastAttr?.[item.fieldName] && !nowAttr?.[item.fieldName]) {
      new_obj.isEmpty = true;
    } else if (!lastAttr?.[item.fieldName] && nowAttr?.[item.fieldName]) {
      new_obj.isNew = true;
    } else if (lastAttr?.[item.fieldName] && nowAttr?.[item.fieldName]) {
      if (transitionDataToDiff(lastAttr?.[item.fieldName]) != transitionDataToDiff(nowAttr?.[item.fieldName])) {
        new_obj.isDiff = true;
        old_obj.isDiff = true;
      }
    } else {
      //未改变
      new_obj.gray = true;
      old_obj.gray = true;
    }
    if (lastAttr) {
      lastList.value.push(old_obj);
    }
    if (nowAttr) {
      nowList.value.push(new_obj);
    }
  });
};
/**
 * 组装子要素
 * @param lastAttr 上一个
 * @param nowAttr 当前
 * @param nowGroup 当前
 */
const getChildAttr = (lastAttr: any, nowAttr: any, nowGroup: any) => {
  lastList.value = [];
  nowList.value = [];
  // 组装老的数据
  lastAttr.list.forEach((item: any, index: number) => {
    const old_obj = {
      title: `${nowGroup.typeName}${index + 1}`,
      fieldModelList: []
    };
    nowGroup.fieldModelList.forEach((item2: any) => {
      const obj_filed = {
        fieldCn: item2.fieldCn,
        fieldName: item2.fieldName,
        fieldValue: item[item2.fieldName],
        valueMethod: item2.valueMethod,
        attribution: item2.attribution
      };
      // 对比新的数据 对比的时候需要先判断新的数据是否存在该下标数据
      if (nowAttr.list && nowAttr.list[index]) {
        // 老的数据只需要比对值改变的
        if (transitionDataToDiff(item[item2.fieldName]) != transitionDataToDiff(nowAttr.list[index][item2.fieldName])) {
          obj_filed.isDiff = true;
        } else {
          obj_filed.gray = true;
        }
      }
      old_obj.fieldModelList.push(obj_filed);
    });
    lastList.value.push(old_obj);
  });
  // 组装新的数据
  nowAttr.list.forEach((item: any, index: number) => {
    const old_obj = {
      title: `${nowGroup.typeName}${index + 1}`,
      fieldModelList: []
    };
    nowGroup.fieldModelList.forEach((item2: any) => {
      const obj_filed = {
        fieldCn: item2.fieldCn,
        fieldName: item2.fieldName,
        fieldValue: item[item2.fieldName],
        valueMethod: item2.valueMethod,
        attribution: item2.attribution
      };
      // 对比老的数据 对比的时候需要先判断老的数据是否存在该下标数据
      if (lastAttr.list && lastAttr.list[index]) {
        // 新的数据需要对比值是否为空 是否新增 是否改变
        if (
          lastAttr.list[index][item2.fieldName] &&
          transitionDataToDiff(item[item2.fieldName]) != transitionDataToDiff(lastAttr.list[index][item2.fieldName])
        ) {
          obj_filed.isDiff = true;
        } else if (item[item2.fieldName] && !lastAttr.list[index][item2.fieldName]) {
          obj_filed.isNew = true;
        } else if (!item[item2.fieldName] && lastAttr.list[index][item2.fieldName]) {
          obj_filed.isEmpty = true;
        } else {
          //未改变
          obj_filed.gray = true;
        }
      }
      old_obj.fieldModelList.push(obj_filed);
    });
    nowList.value.push(old_obj);
  });
};
/**
 * 组装多采
 * @param lastAttr 上一个
 * @param nowAttr 当前
 * @param nowGroup 当前
 */
const getMutiAttr = (lastAttr: any, nowAttr: any, nowGroup: any) => {
  lastList.value = [];
  nowList.value = [];
  // 组装老的数据
  if (lastAttr && lastAttr.length != 0) {
    lastAttr.forEach((item: any, index: number) => {
      const old_obj = {
        title: `${nowGroup.typeName}${index + 1}`,
        fieldModelList: []
      };
      nowGroup.fieldModelList.forEach((item2: any) => {
        // 需要判断是否是特殊字段 比如身份证识别
        if (item2.valueMethod == 'idCardScan') {
          item2.attribution.expendList.forEach((item3: any) => {
            const obj_filed = {
              fieldCn: item3.cnName,
              fieldName: item3.enName,
              fieldValue: item.attribution[`${item2.fieldName}_${item3.label}`],
              valueMethod: item3.valueMethod,
              attribution: item3.attribution
            };
            // 对比新的数据 对比的时候需要先判断新的数据是否存在该下标数据
            if (nowAttr && nowAttr[index]) {
              // 老的数据只需要比对值改变的
              if (
                transitionDataToDiff(item.attribution[`${item2.fieldName}_${item3.label}`]) !=
                transitionDataToDiff(nowAttr[index].attribution[`${item2.fieldName}_${item3.label}`])
              ) {
                obj_filed.isDiff = true;
              } else {
                obj_filed.gray = true;
              }
            }
            old_obj.fieldModelList.push(obj_filed);
          });
        } else {
          //普通字段
          const obj_filed = {
            fieldCn: item2.fieldCn,
            fieldName: item2.fieldName,
            fieldValue: item.attribution[item2.fieldName],
            valueMethod: item2.valueMethod,
            attribution: item2.attribution
          };
          // 对比新的数据 对比的时候需要先判断新的数据是否存在该下标数据
          if (nowAttr && nowAttr[index]) {
            // 老的数据只需要比对值改变的
            if (transitionDataToDiff(item[item2.fieldName]) != transitionDataToDiff(nowAttr[index][item2.fieldName])) {
              obj_filed.isDiff = true;
            } else {
              obj_filed.gray = true;
            }
          }
          old_obj.fieldModelList.push(obj_filed);
        }
      });
      lastList.value.push(old_obj);
    });
  }
  if (nowAttr && nowAttr.length != 0) {
    // 组装新的数据
    nowAttr.forEach((item: any, index: number) => {
      const old_obj = {
        title: `${nowGroup.typeName}${index + 1}`,
        fieldModelList: []
      };
      nowGroup.fieldModelList.forEach((item2: any) => {
        // 需要判断是否是特殊字段 比如身份证识别
        if (item2.valueMethod == 'idCardScan') {
          item2.attribution.expendList.forEach((item3: any) => {
            const obj_filed = {
              fieldCn: item3.cnName,
              fieldName: item3.enName,
              fieldValue: item.attribution[`${item2.fieldName}_${item3.label}`],
              valueMethod: item3.valueMethod,
              attribution: item3.attribution
            };
            // 对比老的的数据 对比的时候需要先判断新的数据是否存在该下标数据
            if (lastAttr && lastAttr[index]) {
              // 老的数据只需要比对值改变的
              if (
                lastAttr[index].attribution[`${item2.fieldName}_${item3.label}`] &&
                transitionDataToDiff(item.attribution[`${item2.fieldName}_${item3.label}`]) !=
                  transitionDataToDiff(lastAttr[index].attribution[`${item2.fieldName}_${item3.label}`])
              ) {
                obj_filed.isDiff = true;
              } else if (item.attribution[`${item2.fieldName}_${item3.label}`] && !lastAttr[index].attribution[`${item2.fieldName}_${item3.label}`]) {
                obj_filed.isNew = true;
              } else if (!item.attribution[`${item2.fieldName}_${item3.label}`] && lastAttr[index].attribution[`${item2.fieldName}_${item3.label}`]) {
                obj_filed.isEmpty = true;
              } else {
                //未改变
                obj_filed.gray = true;
              }
            } else {
              //代表老的都没有数据
              if (item.attribution[`${item2.fieldName}_${item3.label}`]) {
                obj_filed.isNew = true;
              }
            }
            old_obj.fieldModelList.push(obj_filed);
          });
        } else {
          //普通字段
          const obj_filed = {
            fieldCn: item2.fieldCn,
            fieldName: item2.fieldName,
            fieldValue: item.attribution[item2.fieldName],
            valueMethod: item2.valueMethod,
            attribution: item2.attribution
          };
          // 对比老的数据 对比的时候需要先判断老的数据是否存在该下标数据
          if (lastAttr && lastAttr[index]) {
            // 新的数据需要对比值是否为空 是否新增 是否改变
            if (
              lastAttr[index].attribution[item2.fieldName] &&
              transitionDataToDiff(item.attribution[item2.fieldName]) != transitionDataToDiff(lastAttr[index].attribution[item2.fieldName])
            ) {
              obj_filed.isDiff = true;
            } else if (item.attribution[item2.fieldName] && !lastAttr[index].attribution[item2.fieldName]) {
              obj_filed.isNew = true;
            } else if (!item.attribution[item2.fieldName] && lastAttr[index].attribution[item2.fieldName]) {
              obj_filed.isEmpty = true;
            } else {
              //未改变
              obj_filed.gray = true;
            }
          } else {
            //代表老的都没有数据
            if (item.attribution[item2.fieldName]) {
              obj_filed.isNew = true;
            }
          }
          old_obj.fieldModelList.push(obj_filed);
        }
      });
      nowList.value.push(old_obj);
    });
  }
};

/**
 * 返回颜色样式
 * @param item
 * @returns {string}
 *
 */
const getColor = (item: any) => {
  if (item.isEmpty) {
    return 'red-color';
  } else if (item.isNew) {
    return 'green-color';
  } else if (item.isDiff) {
    return 'diff-color';
  } else {
    return 'gray';
  }
};
/**
 * 切换属性组
 * @param item 选择的属性组
 * @returns {string}
 */
const changeSel = (e: any) => {
  activeName.value = e;
  const item = logDetailList.value.find((item) => item.linkId == e);
  linkType.value = item.linkType;
  // 初始化
  lastList.value = [];
  nowList.value = [];
  getDetailItem(item, item.parcelId);
};

/**
 * 给数据对比进行数据转换 如果是字符串不需要转换直接返回该字符串 如果是对象需要转换为字符串用于对比
 * @param data 需要转换的数据
 * @returns {string} 转换后的字符串
 */
const transitionDataToDiff = (data: any) => {
  if (typeof data == 'string') {
    return data;
  } else {
    return JSON.stringify(data);
  }
};

defineExpose({
  getLog
});

const filterTime = (value: string) => {
  const date = new Date(Number(value));
  const year = date.getFullYear();
  let month: number | string = date.getMonth() + 1;
  let day: number | string = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  let hh: number | string = date.getHours();
  let mm: number | string = date.getMinutes();
  let ss: number | string = date.getSeconds();
  hh = hh < 10 ? '0' + hh : hh;
  mm = mm < 10 ? '0' + mm : mm;
  ss = ss < 10 ? '0' + ss : ss;
  return year + '-' + month + '-' + day + ' ' + hh + ':' + mm + ':' + ss;
};
</script>
<style lang="scss" scoped>
.compare-box {
  overflow: hidden;
  .notice {
    display: flex;
    .notice-item {
      margin-right: 16px;
      display: flex;
      align-items: center;
      .color-box {
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
      }
      .empty {
        background: #ff0000;
      }
      .diff {
        background: rgba(255, 0, 0, 0.5);
      }
      .new {
        background: green;
      }
      .gray {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
  .compare-main {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    .item {
      height: 100%;
      // flex: 1;
      width: calc(50% - 5px);
      border: #d3d3d3 solid 1px;
      padding: 16px 16px 6px 16px;
      .content {
        max-height: calc(100vh - 280px);
        min-height: 350px;
        overflow-y: auto;
        .flex-row {
          display: flex;
          margin-bottom: 10px;
          align-items: center;
          .label {
            font-size: 12px;
            margin-right: 10px;
            width: 120px;
            text-align: right;
            font-weight: bold;
          }
          .end-label {
            // flex: 1;
            width: calc(100% - 130px);
            font-size: 12px;
          }
        }
        .red-color {
          color: #ff0000;
        }
        .diff-color {
          color: rgba(255, 0, 0, 0.5);
        }
        .green-color {
          color: green;
        }
        .gray {
          color: rgba(0, 0, 0, 0.5);
        }
        .child-box {
          margin-bottom: 10px;
          border-bottom: #d3d3d3 solid 1px;
          .title {
            font-size: 12px;
            margin-bottom: 10px;
            font-weight: bold;
          }
        }
      }
      .title {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
        font-weight: bold;
      }
    }
  }
}
:deep(.el-timeline-item__content) {
  margin-right: 10px;
}
:deep(.el-timeline-item__tail) {
}
.logList-main {
  width: 100%;
  height: calc(100% - 10px);
  margin-top: 10px;
  padding-left: 10px;
  .card-body {
    width: calc(100% - 20px);
    min-height: 116px;
    // background: rgba(0,0,0,0.8);
    border-radius: 8px 8px 8px 8px;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .flex-row {
      display: flex;
      // color: #fff;
      font-size: 14px;
      margin-bottom: 5px;
      .label {
        // color: rgba(255,255,255,0.8);
        font-size: 14px;
      }
      .end-label {
        flex: 1;
      }
    }
  }
}
</style>
