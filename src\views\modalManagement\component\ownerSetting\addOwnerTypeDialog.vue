<template>
  <!-- 新增属性的弹框 -->
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="800" @close="handleClose" @open="handleOpen">
      <el-form :model="addTypeForm" :rules="addTypeFormRules" ref="addTypeFormRef" label-width="120px" :label-position="`right`">
        <el-form-item label="属性名称" prop="typeName">
          <el-input v-model="addTypeForm.typeName" @input="handleChangeTypeName" placeholder="请输入属性名称" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="属性图标" prop="iconUrl">
          <div class="item-content" @click="handleOpenSettingIconDialog">
            <div class="choose-img-div">
              <div class="choose-img">
                <div v-if="addTypeForm.iconUrl && addTypeForm.iconUrl.substring(addTypeForm.iconUrl.lastIndexOf('_') + 1) === 'blob'">
                  <!-- <authImg :authSrc="`${baseUrl}${addTypeForm.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" /> -->
                  <el-image style="width: 20px; height: 20px" :src="`${baseUrl}${addTypeForm.iconUrl}?token=${token}`" :fit="fit" />
                </div>
                <div v-else>
                  <svg-icon class-name="svg-item" :icon-class="addTypeForm.iconUrl" />
                </div>
              </div>
              <div class="choose-btn" @click="handleOpenSettingIconDialog">
                <el-icon><Setting /></el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="属性说明" prop="remark">
          <el-input v-model="addTypeForm.remark" @input="handleChangeRemark" placeholder="请输入属性说明"></el-input>
        </el-form-item>
        <el-form-item label="属性固定别名" prop="displayName">
          <el-input v-model="addTypeForm.displayName" @input="handleChangeDisplayName" placeholder="选填"></el-input>
        </el-form-item>
        <el-form-item label="引用属性组" prop="groupScope">
          <el-select
            v-model="addTypeForm.linkGroupScope"
            clearable
            placeholder="请选择"
            style="width: 100%"
            @clear="clearLinkGroup"
            @change="changeLinkGroup"
          >
            <el-option v-for="item in groupList" :key="item.id" :label="item.typeName" :value="item.id"></el-option>
          </el-select>
          <el-table
            v-show="oneSelfFields.length !== 0 && addTypeForm.linkGroupScope"
            :data="oneSelfFields"
            style="width: 100%; margin-top: 10px"
            ref="multipleTableRef"
            :height="300"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column type="index" label="序号" width="55"></el-table-column>
            <el-table-column label="字段名称" prop="fieldName">
              <template #default="scope">
                <span v-if="scope.row.valueMethod === 'idCardScan' || scope.row.valueMethod == 'xtBankCard'">{{
                  scope.row.attribution.expendList[0].enName
                }}</span>
                <span v-else>{{ scope.row.fieldName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="字段别名" prop="fieldCn">
              <template #default="scope">
                <span v-if="scope.row.valueMethod === 'idCardScan' || scope.row.valueMethod == 'xtBankCard'">{{
                  scope.row.attribution.expendList[0].cnName
                }}</span>
                <span v-else>{{ scope.row.fieldCn }}</span>
              </template>
            </el-table-column>
            <el-table-column label="映射字段">
              <template #default="scope">
                <el-select v-model="scope.row.quoteField" placeholder="请选择映射字段" clearable>
                  <el-option v-for="(item, index) in concatFieldList" :key="index" :label="getLabel(item)" :value="getValue(item)"></el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="跨节点引用" prop="groupScope" v-if="addTypeForm.linkGroupScope">
          <el-checkbox v-model="addTypeForm.isMoreNode">是否跨节点引用</el-checkbox>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmitOwnerSetting">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 设置属性图标的组件 -->
    <setting-icon
      :iconVisible="iconVisible"
      :defaultList="groupIconList"
      :iconSelected="addTypeForm.iconUrl"
      :isShow="isShow"
      :iconName="iconName"
      @closeSettingIcon="handleCloseSettingIcon"
      @submitIcon="handleSubmitSettingIcon"
    ></setting-icon>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import settingIcon from '../../SettingIcon/index.vue';
import authImg from '@/components/authImg/index.vue';
import customImg from '../../SettingIcon/customImg.vue';
import svgIcon from '../../svgIcon/index.vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { saveFieldGroup, selectFieldFactorByScopeGroup, getOwnerListByModuleId } from '@/api/modal/index';
import groupIconList from '../../SettingIcon/groupIcon.json';
import { ElMessage } from 'element-plus';
import { Setting } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const fit = 'cover'; // 图片填充方式，'contain' 或 'cover'
const token = useUserStore().token; // 从用户信息中获取 token

// 定义 props
const props = defineProps<{
  title?: string;
  ownerVisible?: boolean;
  currentItem?: Record<string, any>;
}>();

// 定义 emits
const emit = defineEmits(['closeOwner', 'openFieldForm', 'updateOwner']);

const dialogVisible = computed({
  get() {
    return props.ownerVisible;
  },
  set(value) {
    // 触发关闭事件
    // emit('closeOrder');
  }
});

const userStore = useUserStore();
const modalStore = useModalStore();

const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/otherDownload/');

// 表单引用
const addTypeFormRef = ref<{ validate: (callback: (valid: boolean) => void) => void } | null>(null);
const multipleTableRef = ref<{ toggleRowSelection: (row: any) => void; clearSelection: () => void } | null>(null);

// 新增字段绑定的对象
const addTypeForm = ref<{
  typeName: string;
  iconUrl: string;
  remark: string;
  linkGroupScope: number | undefined;
  isMoreNode: boolean | undefined;
  displayName: string;
  id?: number;
}>({
  typeName: '',
  iconUrl: '',
  remark: '',
  linkGroupScope: undefined,
  isMoreNode: undefined,
  displayName: ''
});

// 校验规则
const validateIcon = (rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    return callback(new Error('请选择属性图标'));
  } else {
    callback();
  }
};

const addTypeFormRules = ref({
  typeName: [{ required: true, message: '属性名称不能为空', trigger: 'blur' }],
  iconUrl: [{ required: true, validator: validateIcon, trigger: ['blur', 'change'] }],
  remark: [{ required: true, message: '属性说明不能为空', trigger: 'blur' }]
});

// 选择属性图标的组件
const iconVisible = ref(false);
const isShow = ref(true);
const iconName = ref('属性组自定义照片');
const concatFieldList = ref<any[]>([]); // 关联属性组的字段
const multipleSelection = ref<any[]>([]);
const oneSelfFields = ref<any[]>([]); // 当前属性组的字段
const groupScopeOptions = ref<any[]>([]);

const moduleId = computed(() => modalStore.moduleId);

const groupList = computed(() => {
  return modalStore.speGroups;
});

// 监听 addTypeForm.iconUrl 的变化
watch(
  () => addTypeForm.value.iconUrl,
  (val) => {
    if (val && addTypeFormRef.value) {
      addTypeFormRef.value.clearValidate('iconUrl');
    }
  }
);

// 打开弹框时  回显数据
const handleOpen = () => {
  // 修改时的回显数据
  if (props.currentItem && props.title === '修改属性') {
    addTypeForm.value = {
      ...addTypeForm.value,
      typeName: props.currentItem.typeName,
      iconUrl: props.currentItem.iconUrl,
      id: props.currentItem.id,
      remark: props.currentItem.remark,
      linkGroupScope: props.currentItem.attribution?.linkGroupScope,
      isMoreNode: props.currentItem.attribution?.isMoreNode,
      displayName: props.currentItem.displayName
    };
  }
  // 查询关联属性组的数据
  getOwnerGroupList();
};

// 关闭对话框
const handleClose = () => {
  emit('closeOwner');
  addTypeForm.value = {
    typeName: '',
    iconUrl: '',
    remark: '',
    linkGroupScope: undefined,
    isMoreNode: undefined,
    displayName: ''
  };
  // 判断是权属人设置要点及字段 还是采集要素设置字段
  modalStore.setIsHasAcquition(false);
  // 设置当前树节点下的某一个节点的内容
  modalStore.setCheckedNodeItem({});
};

// 打开属性图标的组件 dialog
const handleOpenSettingIconDialog = () => {
  iconVisible.value = true;
};

// 关闭设置属性图标的 dialog 弹框
const handleCloseSettingIcon = () => {
  iconVisible.value = false;
};

// 确定选中的属性图标，子组件中的传值
const handleSubmitSettingIcon = (url: string) => {
  addTypeForm.value.iconUrl = url;
};

// 提交权属人设置
const handleSubmitOwnerSetting = async () => {
  const params = {
    // 提交的全数组属于范围 1 模块 2 规则 可以采用复制方式到另一个模块即可实现公司共享
    groupScope: 1,
    iconUrl: addTypeForm.value.iconUrl,
    moduleId: moduleId.value,
    operaType: 1, // 操作属性 1 新增  2 删除
    typeName: addTypeForm.value.typeName,
    wordName: '',
    linkType: 1,
    remark: addTypeForm.value.remark,
    displayName: addTypeForm.value.displayName,
    attribution: {
      // 引用属性组
      linkGroupScope: addTypeForm.value.linkGroupScope,
      //  是否跨节点
      isMoreNode: addTypeForm.value.isMoreNode,
      formData: props.currentItem?.attribution?.formData || null
    }
  };

  if (props.title === '修改属性') {
    params.id = addTypeForm.value.id;
  }

  // 提交前需要验证选择的内容是否有没有映射的
  let flg = true;
  for (let i = 0; i < multipleSelection.value.length; i++) {
    if (!multipleSelection.value[i].quoteField) {
      flg = false;
      break;
    }
  }

  if (!flg) {
    ElMessage.error('您有勾选的字段未选择映射，请检查后提交！！！');
    return;
  }
  // 关联的字段
  const chooseField: any[] = [];
  const groupedByCategory = multipleSelection.value.reduce((groups: any, item: any) => {
    if (!groups[item.fieldName]) {
      groups[item.fieldName] = [];
    }
    groups[item.fieldName].push(item);
    return groups;
  }, {});
  if (Object.keys(groupedByCategory).length !== 0) {
    Object.keys(groupedByCategory).forEach((v) => {
      if (groupedByCategory[v].length > 0) {
        groupedByCategory[v].forEach((ite: any) => {
          if (ite.valueMethod === 'idCardScan' || ite.valueMethod === 'xtBankCard') {
            const spe_field = JSON.parse(JSON.stringify(ite));
            if (spe_field.attribution && spe_field.attribution.expendList.length != 0) {
              spe_field.attribution.expendList[0].quoteField = spe_field.quoteField;
            }
            chooseField.push(spe_field);
          } else {
            chooseField.push(groupedByCategory[v][0]);
          }
        });
      }
    });
    params.attribution.chooseField = chooseField;
  }
  try {
    await getOwnerList();
    if (addTypeFormRef.value) {
      await addTypeFormRef.value.validate((valid: boolean) => {
        if (valid) {
          saveFieldGroup(params).then((res) => {
            if (res.code === 200) {
              ElMessage.success('保存成功');
              modalStore.setCurrentGroupItem(res.data);
              modalStore.setGroupId(res.data.id);
              if (!addTypeForm.value.id) {
                emit('openFieldForm', res.data.id);
              }
              emit('updateOwner');
              // 提交成功之后关闭弹框
              // handleClose();
              emit('closeOwner');
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
      });
    }
  } catch (err) {
    console.error(err);
  }
};

// 根据 ModuleId 获取权属属性列表
const getOwnerList = () => {
  return new Promise((resolve, reject) => {
    const params = {
      moduleId: moduleId.value,
      groupScope: 0
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      params.companyId = companyId;
    }
    const isName = modalStore.speGroups.some((item: any) => {
      return !addTypeForm.value.id && item.typeName === addTypeForm.value.typeName;
    });
    if (isName) {
      ElMessage.error('组名称不能重复');
      reject();
      return;
    } else {
      resolve(true);
    }
  });
};

// 修改创建的属性的名称
const handleChangeTypeName = (val: string) => {
  addTypeForm.value.typeName = val;
};

const handleChangeRemark = (val: string) => {
  addTypeForm.value.remark = val;
};

const handleChangeDisplayName = (val: string) => {
  addTypeForm.value.displayName = val;
};

// 查询当前权属人下面建的组的数据
const getOwnerGroupList = () => {
  const params = {
    moduleId: moduleId.value,
    groupScope: 1
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  // 查询属性组
  groupScopeOptions.value = modalStore.speGroups;
  // 得到当前属性组的所有字段
  oneSelfFields.value = [];
  if (props.currentItem?.fieldModelList) {
    props.currentItem.fieldModelList.forEach((v: any) => {
      if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
        if (!v.attribution && !v.attribution.expendList && v.attribution.expendList.length === 0) return;
        v.attribution.expendList.forEach((k: any, kdx: number) => {
          const ite_obj = JSON.parse(JSON.stringify(v));
          ite_obj.attribution.expendList = [k];
          ite_obj.attribution.list = [k.label];
          ite_obj.quoteField = '';
          ite_obj.attribution.expendList[0].quoteField = '';
          oneSelfFields.value.push(ite_obj);
        });
      } else {
        v.quoteField = '';
        oneSelfFields.value.push(v);
      }
    });
  }

  concatFieldList.value = [];
  // 反显该引用属性组所有字段
  for (let i = 0; i < modalStore.speGroups.length; i++) {
    if (props.currentItem?.attribution?.linkGroupScope === modalStore.speGroups[i].id) {
      modalStore.speGroups[i].fieldModelList.forEach((v: any) => {
        if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
          v.attribution.expendList.forEach((k: any, kdx: number) => {
            const ite = JSON.parse(JSON.stringify(v));
            ite.attribution.expendList = [k];
            ite.attribution.list = [k.label];
            concatFieldList.value.push(ite);
          });
        } else {
          concatFieldList.value.push(v);
        }
      });
      break;
    }
  }

  if (props.currentItem?.attribution?.chooseField) {
    // 反显当前选择的字段
    const menuList: any[] = [];
    props.currentItem.attribution.chooseField.forEach((v: any) => {
      if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
        v.attribution.expendList.forEach((k: any, kdx: number) => {
          const ite = JSON.parse(JSON.stringify(v));
          ite.attribution.expendList = [k];
          ite.attribution.list = [k.label];
          menuList.push(ite);
        });
      } else {
        menuList.push(v);
      }
    });
    nextTick(() => {
      oneSelfFields.value.forEach((v: any) => {
        if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
          for (let i = 0; i < menuList.length; i++) {
            if (
              menuList[i].fieldName === v.fieldName &&
              (menuList[i].valueMethod === 'idCardScan' || menuList[i].valueMethod == 'xtBankCard') &&
              menuList[i].attribution.expendList[0].enName === v.attribution.expendList[0].enName
            ) {
              v.attribution.expendList[0].quoteField = menuList[i].attribution.expendList[0].quoteField;
              v.quoteField = menuList[i].attribution.expendList[0].quoteField;
              if (multipleTableRef.value) {
                multipleTableRef.value.toggleRowSelection(v);
              }
              break;
            }
          }
        } else {
          for (let i = 0; i < menuList.length; i++) {
            if (menuList[i].fieldName === v.fieldName) {
              v.quoteField = menuList[i].quoteField;
              if (multipleTableRef.value) {
                multipleTableRef.value.toggleRowSelection(v);
              }
              break;
            }
          }
        }
      });
    });
  }
};

// 引用属性组增加选择字段
const changeLinkGroup = (e: number) => {
  if (e) {
    for (let i = 0; i < groupList.value.length; i++) {
      if (groupList.value[i].id === e) {
        concatFieldList.value = [];
        groupList.value[i].fieldModelList.forEach((v: any) => {
          if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
            v.attribution.expendList.forEach((k: any, kdx: number) => {
              const ite = JSON.parse(JSON.stringify(v));
              ite.attribution.expendList = [k];
              ite.attribution.list = [k.label];
              concatFieldList.value.push(ite);
            });
          } else {
            concatFieldList.value.push(v);
          }
        });
        break;
      }
    }
  }
};

const clearLinkGroup = () => {
  if (multipleTableRef.value) {
    multipleTableRef.value.clearSelection();
  }
  // 清除当前属性组字段映射关系
  oneSelfFields.value.forEach((v: any) => {
    if (v.valueMethod === 'idCardScan' || v.valueMethod == 'xtBankCard') {
      v.attribution.expendList[0].quoteField = '';
      v.quoteField = '';
    } else {
      v.quoteField = '';
    }
  });
  addTypeForm.value.linkGroupScope = null;
};

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

// 返回字段名
const getLabel = (item: any) => {
  let result = '';
  if (item.valueMethod === 'idCardScan' || item.valueMethod == 'xtBankCard') {
    result = `${item.fieldCn}-${item.attribution.expendList[0].cnName}`;
  } else {
    result = item.fieldCn;
  }
  return result;
};

// 返回字段值
const getValue = (item: any) => {
  let result = '';
  if (item.valueMethod === 'idCardScan' || item.valueMethod == 'xtBankCard') {
    result = `${item.fieldName}_${item.attribution.list[0]}`;
  } else {
    result = item.fieldName;
  }
  return result;
};
</script>

<style lang="scss" scoped>
.item-content {
  flex: 1;
  .choose-img-div {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    .choose-img {
      width: 20px;
      height: 20px;
      margin-left: 12px;
      margin-top: -2px;
      .svg-item {
        width: 20px;
        height: 20px;
        color: #333;
      }
    }
    .choose-btn {
      width: 52px;
      height: 100%;
      border-left: 1px solid #e6e9ee;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      background: #edf4fb;
      border-radius: 0px 4px 4px 0px;
    }
  }
}
:deep(.el-form-item__label) {
  color: #161d26;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-size: 14px;
}
</style>
