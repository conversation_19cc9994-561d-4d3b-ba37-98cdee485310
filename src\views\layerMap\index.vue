<!--  -->
<template>
  <div class="layerMap-main" v-loading.fullscreen.lock="fullscreenLoading" id="layerMapRef">
    <div id="viewDiv" ref="gisMapRef" class="map"></div>
    <div class="right-handle-div">
      <div class="min-handle-item" @click="zoomMap">
        <svg-icon icon-class="zoom" />
      </div>
      <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
        <svg-icon icon-class="map" />
      </div>
    </div>
    <div class="map-change" v-show="showChangeMap">
      <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">地图</div>
      </div>
      <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">影像</div>
      </div>
    </div>
    <div class="map-copyRight">
      <img src="https://api.tianditu.gov.cn/v4.0/image/logo.png" alt="" class="copy-ico" />
      GS（2024）0568号 - 甲测资字1100471
    </div>
    <div class="layer-list" :class="{ 'layer-list-active': showLayer }" @click="showLayer = !showLayer">
      <svg-icon icon-class="coverage" />
    </div>
    <div class="layer-show" v-show="showLayer">
      <div style="margin-left: 8px; font-size: 12px">
        <span>总数:{{ treeCount }}</span>
        <span style="padding-left: 8px">勾选:{{ defaultCheckList.length }}</span>
      </div>
      <el-tree
        ref="treeRef"
        :data="layerList"
        show-checkbox
        node-key="id"
        highlight-current
        @check-change="handleNodeClick"
        :default-checked-keys="defaultCheckList"
        :check-strictly="true"
        :default-expand-all="true"
      >
        <template #default="{ data }">
          <div style="height: 20px; line-height: 20px; display: flex; align-items: center; justify-content: space-between">
            <div>{{ data.label }}</div>
            <div style="padding-left: 16px" @click="handleMapDetial(data)">
              <el-icon style="height: 20px; line-height: 20px; font-size: 14px"><Position /></el-icon>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <div class="attr-box" v-show="showAttr" :style="{ 'top': top, 'left': left }">
      <div class="attr-div">
        <div class="attr-close" @click="showAttr = false">
          <el-icon><CircleClose /></el-icon>
        </div>
        <div class="attr-row title">属性111</div>
        <div class="attr-row" v-for="(item, index) in properties" :key="index" :class="{ 'two': index % 2 != 0 }">
          <div class="left">{{ item.label }}</div>
          <div class="right">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <!-- 查看图层属性设置 -->
    <el-dialog
      title="图层描述"
      v-model="mapDescVisible"
      :modal-append-to-body="false"
      :modal="false"
      class="detial-dialog"
      style="margin-top: 15vh !important"
    >
      <div>
        {{ mapDescContent }}
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { loadModules, esriLoader } from 'esri-loader';
import { getTCList, getShpTree } from '@/api/issueManager';
import axios from 'axios';
import { getToken } from '@/utils/auth';
import { useTdtInstanceComponent } from './hook/useTdtInstanceComponent';


// 类型定义
interface Feature {
  properties: Record<string, any>;
}

interface PropertyItem {
  label: string;
  value: any;
}

interface LayerItem {
  id: string;
  label: string;
  mapName: string;
  mapAlias?: string;
  description?: string;
  children?: LayerItem[];
  [key: string]: any;
}

interface TreeNode {
  id: string;
  label: string;
  mapName: string;
  description?: string;
  children?: TreeNode[];
  [key: string]: any;
}

// 配置
const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};

import { TDT_BASE_URL, TDT_TOKEN } from '@/constants';

// 地图服务相关
const tiandituBaseUrl = TDT_BASE_URL; // 天地图服务地址
const token = TDT_TOKEN; // 天地图管网申请token
let tiledLayer: any = null; // 影像
let tiledLayerAnno: any = null; // 影像标记
let normalLayer: any = null; // 矢量底图
let normalAnno: any = null; // 矢量标记
const xiangLayer: any = null;

// 空间参考
const outSpatialReference = {
  wkid: 4523
};

// 偏差值
const tolerance = 3;

// 响应式状态
let map = null; // 地图容器
let view = null; // mapview
const fullscreenLoading = ref(false);
const showChangeMap = ref(false);
const checkedMap = ref('image'); // 显示的底图 默认影像
const base = ref(import.meta.env.VITE_APP_LAYER_BASE || process.env.VUE_APP_LAYER_BASE);
const layerList = ref<LayerItem[]>([]);
const showLayer = ref(false);
const checkList = ref<string[]>([]);
const properties = ref<PropertyItem[]>([]); // 点击的要素数据
const showAttr = ref(false); // 是否展示要素属性
const top = ref('0px');
const left = ref('0px');
const isGetFeatures = ref(false);
const treeCount = ref(0);
const defaultCheckList = ref<string[]>([]);
const mapDescVisible = ref(false);
const mapDescContent = ref('');
const nowLayerList = ref<any[]>([]);

// DOM引用
const gisMapRef = ref(null);
const treeRef = ref<any>(null);

// 树结构配置
const defaultProps = {
  children: 'children',
  label: 'label',
  id: 'id'
};

// 生命周期钩子
onMounted(() => {
  init();
});

// 初始化地图
const init = async () => {
  try {
    const [Map, MapView, WebTileLayer, SpatialReference, Compass, Zoom, ScaleBar] = await loadModules(
      [
        'esri/Map',
        'esri/views/MapView',
        'esri/layers/WebTileLayer',
        'esri/geometry/SpatialReference',
        'esri/widgets/Compass',
        'esri/widgets/Zoom',
        'esri/widgets/ScaleBar'
      ],
      config
    );

    let { tiledLayerIns, tiledLayerAnnoIns, normalLayerIns, normalAnnoIns } = useTdtInstanceComponent(WebTileLayer, SpatialReference);
    tiledLayer = tiledLayerIns;
    tiledLayerAnno = tiledLayerAnnoIns;
    normalLayer = normalLayerIns;
    normalAnno = normalAnnoIns;



    map = new Map({
      basemap: {
        baseLayers: [tiledLayer, tiledLayerAnno, normalLayer, normalAnno]
      },
      ground: 'world-elevation',
      logo: false
    });

    view = new MapView({
      container: gisMapRef.value,
      map: map,
      center: [116.39126, 39.90763],
      zoom: 15,
      ui: {
        components: []
      },
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    // 设置最大缩放等级
    view.constraints = {
      minZoom: 2,
      maxZoom: 22
    };

    // 添加指南针组件
    const compassWidget = new Compass({ view: view });
    view.ui.add(compassWidget, 'bottom-right');

    // 添加缩放组件
    const zoomWidget = new Zoom({
      view: view
    });
    view.ui.add(zoomWidget, 'bottom-right');
    view.ui.remove('attribution');

    // 添加比例尺
    const scaleBar = new ScaleBar({
      view: view,
      unit: 'metric',
      style: 'line'
    });
    view.ui.add(scaleBar, {
      position: 'bottom-right'
    });

    // 获取图层树结构
    getShpTreeData();

    view.when(function () {
      // 初始化是否获取属性 & 绑定点击事件
      isGetFeatures.value = true;
      view.on('click', executeIdentify);
    });
  } catch (error) {
    console.error('地图初始化失败', error);
    ElMessage.error('地图初始化失败');
  }
};

// 加载WMS图层
const initWMS = async (url: string, title: string) => {
  try {
    const [WMSLayer, SpatialReference] = await loadModules(['esri/layers/WMSLayer', 'esri/geometry/SpatialReference'], config);

    // 乡镇界线
    const oneLayer = new WMSLayer({
      url: url,
      visible: true,
      id: title, // 添加图层ID
      // WMSLayer 自定义参数，由于后台geoserver增加了权限
      customParameters: {
        token: getToken()
      }
    });

    map.add(oneLayer);

    // 图层加载完成后居中显示
    oneLayer.on('layerview-create', function (event: any) {
      view.center = [oneLayer.fullExtent.center.longitude, oneLayer.fullExtent.center.latitude];
    });
  } catch (error) {
    console.error('WMS图层加载失败', error);
    ElMessage.error('图层加载失败');
  }
};

// 获取图层树结构
const getShpTreeData = async () => {
  try {
    const res = await getShpTree();
    if (res.code === 200) {
      layerList.value = res.data;
      treeCount.value = countTotalNodes(res.data);

      // 默认勾选第一个图层
      nowLayerList.value = []
      if (layerList.value.length > 0) {
        defaultCheckList.value.push(layerList.value[0].id);
        nowLayerList.value.push(layerList.value[0].mapName);
        changeLayer([layerList.value[0].mapName]);
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取图层树结构失败', error);
    ElMessage.error('获取图层树结构失败');
  }
};

// 计算节点总数
const countTotalNodes = (nodes: TreeNode[]): number => {
  let count = nodes.length; // 当前层级的节点数
  nodes.forEach((node) => {
    if (node.children && node.children.length > 0) {
      count += countTotalNodes(node.children); // 递归计算子节点数量并累加
    }
  });
  return count;
};

// 图层属性识别
const executeIdentify = async (event: any) => {
  try {
    const [projection] = await loadModules(['esri/geometry/projection'], config);

    const element = document.getElementById('layerMapRef');
    if (!element) return;

    const offsetWidth = element.offsetWidth;
    const offsetHeight = element.offsetHeight;

    // 设置属性框位置
    top.value = event.y + 'px';
    left.value = event.x + 'px';

    // 确保属性框不超出地图范围
    if (event.x + 300 >= offsetWidth) {
      // 超出宽度 移到左边
      left.value = event.x - 300 + 'px';
    }
    if (event.y + 400 >= offsetHeight) {
      // 超出高度 移到上面
      top.value = event.y - 400 + 'px';
    }

    await projection.load();

    let hasFeatures = false; // 标记是否找到要素

    // 遍历所有选中的图层
    for (let index = 0; index < nowLayerList.value.length; index++) {
      const BBOX = `${event.mapPoint.x},${event.mapPoint.y},${event.mapPoint.x + tolerance},${event.mapPoint.y + tolerance}`;
      const layerName = nowLayerList.value[index];
      const num = layerName.indexOf(':');
      const title = layerName.substring(num + 1, layerName.length);
      //
      const url = `${base.value}${layerName.substring(0, num)}/${title}/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=image%2Fpng&TRANSPARENT=true&QUERY_LAYERS=${layerName.substring(0, num)}%3A${title}&STYLES&LAYERS=${layerName.substring(0, num)}%3A${title}&exceptions=application%2Fvnd.ogc.se_inimage&INFO_FORMAT=application%2Fjson&FEATURE_COUNT=50&X=50&Y=50&SRS=EPSG%3A3857&WIDTH=101&HEIGHT=101&BBOX=${BBOX}&token=${getToken()}`;
      const res = await axios.get(url);
      if (res.data.features && res.data.features.length > 0) {
        hasFeatures = true;
        const featProps = res.data.features[0].properties;
        properties.value = [];

        // 将属性转换为数组格式
        Object.keys(featProps).forEach((key) => {
          properties.value.push({
            label: key,
            value: featProps[key]
          });
        });

        showAttr.value = true;
        break; // 找到要素后退出循环
      }
    }

    // 如果没有找到任何要素，关闭属性框
    if (!hasFeatures) {
      showAttr.value = false;
    }
  } catch (error) {
    console.error('属性识别失败', error);
    showAttr.value = false; // 发生错误时也关闭属性框
  }
};

// 树节点选中处理
const handleNodeClick = (data: LayerItem, checked: boolean) => {
  if (checked) {
    // 加载WMS图层
    const index = data.mapName.indexOf(':');
    const title = data.mapName.substring(index + 1, data.mapName.length);
    const url = `${base.value}${data.mapName.substring(0, index)}/${title}/wms?token=${getToken()}`;
    initWMS(url, title);
  } else {
    // 取消的时候需要移除图层

    const layerIndex = data.mapName.indexOf(':');
    const layerTitle = data.mapName.substring(layerIndex + 1, data.mapName.length);
    // 从地图中移除对应的图层
    removeWMS(layerTitle);
  }
};
// 移除wmsLayer
const removeWMS = (title: string) => {
  if (map) {
    const layers = map.layers.toArray();
    const layerToRemove = layers.find((layer) => layer.id === title);
    if (layerToRemove) {
      map.remove(layerToRemove);
    }
  }
};
// 查看图层描述
const handleMapDetial = (data: LayerItem) => {
  mapDescVisible.value = true;

  if (data.description && data.description !== '' && data.description !== null) {
    mapDescContent.value = data.description;
  } else {
    mapDescContent.value = '暂无图层描述';
  }
};

// 切换底图
const changeMap = (type: number) => {
  if (type === 1) {
    // 地图
    checkedMap.value = 'normal';
    tiledLayer.visible = false;
    tiledLayerAnno.visible = false;
    normalLayer.visible = true;
    normalAnno.visible = true;
  } else if (type === 2) {
    // 影像图
    checkedMap.value = 'image';
    tiledLayer.visible = true;
    tiledLayerAnno.visible = true;
    normalLayer.visible = false;
    normalAnno.visible = false;
  }
};

// F11全屏
const zoomMap = () => {
  document.documentElement.requestFullscreen();
};

// 获取所有图层
const getTCListData = async () => {
  try {
    const res = await getTCList();

    if (res.code === 200) {
      const tempList: LayerItem[] = [];

      res.data.forEach((v: any) => {
        const item: LayerItem = {
          id: v.id || v.mapName,
          label: v.mapAlias,
          layerName: v.mapName,
          mapAlias: v.mapAlias
        };
        tempList.push(item);
      });

      layerList.value = tempList;

      if (tempList.length > 0) {
        checkList.value.push(tempList[0].layerName);
        changeLayer([tempList[0].layerName]);
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取图层数据失败', error);
    ElMessage.error('获取图层数据失败');
  }
};

// 图层切换
const changeLayer = (val: string[]) => {
  showAttr.value = false;
  // 移除所有图层
  if (map) {
    map.removeAll();
  }

  // 加载选中的图层
  val.forEach((v) => {
    const index = v.indexOf(':');
    const title = v.substring(index + 1, v.length);
    const url = `${base.value}${v.substring(0, index)}/${title}/wms?token=${getToken()}`;

    initWMS(url, title);
  });

  // 更新选中图层列表
  checkList.value = val;
};
</script>

<style lang="scss" scoped>
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom) .esri-widget--button {
  background-color: transparent;
  color: #fff;
}
:deep(.esri-zoom) .esri-widget--button:hover {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 80px;
}
.layerMap-main {
  height: calc(100% - 0px);
  width: calc(100% - 0px);
  position: absolute;
  opacity: 1;
  background: #ffffff;
  overflow: hidden;
  .map {
    width: 100%;
    height: 100%;
  }
  .right-handle-div {
    position: absolute;
    right: 16px;
    bottom: 240px;
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #fff;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .map-change {
    position: absolute;
    bottom: 180px;
    right: 60px;
    display: flex;
    flex-direction: row;
    .map-item {
      width: 96px;
      height: 72px;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: flex-end;
      .map-footer {
        width: 100%;
        height: 22px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
        border-radius: 0px 0px 8px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #fff;
      }
      .map-footer-active {
        color: #1b9af7;
      }
    }
    .map-left {
      background-image: url('../../assets/images/normal-map.png');
      background-size: cover;
    }
    .map-right {
      background-image: url('../../assets/images/image-map.png');
      background-size: cover;
      margin-left: 8px;
    }
    .map-active {
      border: #1b9af7 solid 1px;
    }
  }
  .map-copyRight {
    position: absolute;
    bottom: 90px;
    left: 16px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    .copy-ico {
      width: 53px;
      height: 22px;
      margin-right: 10px;
    }
  }
  .layer-list {
    position: absolute;
    left: 20px;
    top: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: #fff;
    cursor: pointer;
  }
  .layer-list:hover {
    color: #000;
    background: #fff;
  }
  .layer-list-active {
    color: #000;
    background: #fff;
  }
  .layer-show {
    position: absolute;
    left: 72px;
    top: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    padding: 10px;
    color: #fff;
    max-height: 400px;
    overflow: auto;
  }
  .layer-show ::-webkit-scrollbar {
    width: 1px;
  }
  /*滚动条样式*/
  .layer-show::-webkit-scrollbar {
    width: 4px;
  }
  .layer-show::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .layer-show::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
:deep(.el-checkbox__label) {
  color: #fff;
}
:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
}
.attr-box {
  position: absolute;
  width: 300px;
  height: 400px;
  z-index: 1;
  .attr-div {
    width: 100%;
    height: 100%;
    background: rgb(0, 0, 0, 0.5);
    border-radius: 8px;
    position: relative;
    overflow: auto;
    .attr-close {
      position: absolute;
      top: 8px;
      right: 5px;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
    }
    .attr-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #fff;
      padding: 0px 10px;
      flex-wrap: wrap;
      font-size: 12px;
      .left {
        width: 100px;
      }
      .right {
        flex: 1;
      }
    }
    .two {
      background: rgba(0, 0, 0, 0.5);
    }
    .title {
      font-weight: bold;
      display: flex;
      justify-content: center;
      background: rgba(0, 0, 0, 0.5);
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
    }
  }
  /*滚动条样式*/
  .attr-div::-webkit-scrollbar {
    width: 4px;
  }
  .attr-div::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .attr-div::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}

:deep(.el-tree) {
  background-color: transparent;
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background-color: rgba(248, 248, 248, 0.2);
  }
}
:deep(.el-tree--highlight-current) .el-tree-node.is-current > .el-tree-node__content {
  background-color: rgba(0, 0, 0, 0.5) !important; /* 自定义颜色 */
}
</style>
