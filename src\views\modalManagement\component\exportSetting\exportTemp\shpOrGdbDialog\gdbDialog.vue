<!-- 添加或修改gdb -->
<template>
  <div class="main">
    <el-dialog title="新建GDB" draggable v-model="editGdbDialog" width="875px" :close-on-click-modal="false" @close="handleClose">
      <div class="label"><span style="color: red">*</span> GDB文件名称</div>
      <div class="flex-row">
        <el-input v-model="gdbFileName" placeholder="请输入GDB文件名称" maxlength="20"></el-input>
      </div>
      <el-link type="primary" @click="addGdbContent">添加GDB内容</el-link>
      <div class="content">
        <gdbTemp
          v-for="(item, index) in newGdbMsg"
          :ref="`GDBTempRef${index}`"
          :tempIndex="index"
          :key="index"
          :newMsgProps="item"
          :ysTreeProps="ysTree"
          @delOne="delOne"
          @showOne="showOne"
          @packUp="packUp"
          class="content-item"
        ></gdbTemp>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import gdbTemp from './gdbTemp.vue';
import { getCurrentInstance } from 'vue';

// 定义 props
const props = defineProps<{
  editGdbDialogProp: boolean;
  newGdbMsgProp: any[];
  ysTreeProp: any[];
}>();

// 定义 emits
const emit = defineEmits(['closeGdbDialog', 'submitGdb', 'addGDBContent']);

// 定义响应式数据
const gdbFileName = ref('');
const tempYsTree = ref<any[]>([]);
// const gdbTempRefs = ref<Array<InstanceType<typeof gdbTemp> | null>>([]);
const editGdbDialog = computed(() => props.editGdbDialogProp);
const newGdbMsg = computed(() => props.newGdbMsgProp);
const ysTree = computed(() => props.ysTreeProp);
const proxy = getCurrentInstance()?.proxy;

// 监听 editGdbDialog 的变化
watch(
  () => props.editGdbDialogProp,
  (val) => {
    if (val) {
      tempYsTree.value = [...props.ysTreeProp];
      newGdbMsg.value.forEach((v) => {
        v.show = false;
      });
      if (newGdbMsg.value.length !== 0) {
        newGdbMsg.value[0].show = true;
      }
    }
  },
  { deep: true }
);

// 初始化名称
const initName = (name: string) => {
  gdbFileName.value = name;
};

// 关闭对话框
const handleClose = () => {
  emit('closeGdbDialog');
};

// 提交数据
const submit = () => {
  if (!gdbFileName.value) {
    ElMessage.error('请输入GDB文件名称');
    return;
  }
  // 验证每个内容是否填写完整
  let flg = true;
  const list = Object.keys(proxy.$refs);
  
  list.forEach((v) => {
    if (proxy.$refs[v].length != 0) {
      //ref存在的时候再执行 删除的时候dom没有同时删除
      const flg_one = proxy.$refs[v][0].getInfo();
      if (!flg_one) {
        flg = false;
      }
    }
  });
  if (!flg) {
    ElMessage.error('请填写完整内容');
    return;
  }
  emit('submitGdb', newGdbMsg.value, gdbFileName.value);
};

// 添加gdb内容
const addGdbContent = () => {
  emit('addGDBContent');
};

// 删除某条gdb内容
const delOne = (index: number) => {
  
  newGdbMsg.value.splice(index, 1);
};

// 展示某条
const showOne = (index: number) => {
  
  newGdbMsg.value.forEach((v) => {
    v.show = false;
  });
  newGdbMsg.value[index].show = true;
};

// 收起某条
const packUp = (index: number) => {
  newGdbMsg.value[index].show = false;
};
defineExpose({
  initName
});
</script>
<style lang="scss" scoped>
.main {
}
.label {
  line-height: 36px;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}
.flex-row {
  margin: 5px 0px 10px 0px;
}
.order-div {
  padding: 10px 10px 10px 10px;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  .order-head {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .order-item {
    display: flex;
    margin-top: 8px;
    align-items: center;
    .order-label {
    }
    .order-content {
      flex: 1;
    }
  }
}
.content {
  height: 500px;
  overflow: auto;
  margin-top: 10px;
  .content-item {
    margin-bottom: 10px;
  }
}
/*滚动条样式*/
.content::-webkit-scrollbar {
  width: 4px;
}
.content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
