<!-- 通过选择部门树得到成员并选择 -->
<template>
  <div class="chooseUserForDept-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      :title="props.chooseUserTitle"
      v-model="dialogVisible"
      width="600px"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="handleClose"
    >
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">部门</span></div>
          <div class="content">
            <el-tree
              ref="treeRef"
              :data="deptOptions"
              :props="defaultProps"
              highlight-current
              default-expand-all
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
              class="tree-div"
            >
              <template #default="{ data }">
                <div class="tree-row">
                  {{ data.label }}
                </div>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="center">
          <div class="title-div">
            <span class="normal-sapn">成员({{ total }})</span>
          </div>
          <div class="search-div">
            <el-input clearable v-model="queryParams.custName" size="small" placeholder="请输入用户名筛选" @keyup.enter="getUser(1)"></el-input>
            <el-button size="small" style="margin-left: 10px" type="primary" @click="getUser(1)">搜索</el-button>
          </div>
          <div class="content">
            <div v-if="userList.length == 0" class="empty-span">暂无数据</div>
            <template v-else>
              <div class="flex-row" v-for="(item, index) in userList" :class="{ 'flex-active': item.checked }" :key="index" @click="changeUser(item)">
                <div class="label">{{ item.custName }}</div>
                <div class="ico" v-show="item.checked">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitUser">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, reactive } from 'vue';
import { Check } from '@element-plus/icons-vue';
import { listUser, deptTreeSelect } from '@/api/system/user';

interface Props {
  chooseUserDialog: boolean;
  chooseUserId: string | number;
  title?: string;
  chooseUserTitle?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'closeChooseUser'): void;
  (e: 'submitChooseUser', user: any): void;
}>();

// 对话框可见性（由父组件props控制，但需要在本地维护副本以实现双向绑定）
const dialogVisible = computed(() => props.chooseUserDialog);

// 监听props变化
watch(
  dialogVisible,
  (val) => {
    if (val) {
      checkedUser.value = {};
      getDeptTree();
    }
  },
  { deep: true }
);

const deptOptions = ref([]);
const treeRef = ref();
const defaultProps = reactive({
  children: 'children',
  label: 'label'
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10000,
  custName: undefined,
  phonenumber: undefined,
  status: undefined,
  deptId: undefined,
  userId: undefined
});

const userList = ref<any[]>([]);
const total = ref(0);
const fullscreenLoading = ref(false);
const checkedUser = ref<any>({}); //选中的成员

const handleClose = () => {
  emit('closeChooseUser');
};

/** 查询部门下拉树结构 */
const getDeptTree = () => {
  fullscreenLoading.value = true;
  deptTreeSelect().then((response) => {
    fullscreenLoading.value = false;
    deptOptions.value = response.data;
    nextTick(() => {
      treeRef.value.setCurrentKey(response.data[0].id);
    });
    handleNodeClick(response.data[0]);
  });
};

const handleNodeClick = (data: any) => {
  queryParams.deptId = data.id;
  getUser();
};

const getUser = (type?: number) => {
  if (type == 1) {
    queryParams.deptId = undefined;
  }
  fullscreenLoading.value = true;
  listUser(queryParams).then((response) => {
    fullscreenLoading.value = false;
    userList.value = response.rows;
    total.value = response.total;
    userList.value.forEach((v) => {
      v.checked = v.userId == props.chooseUserId || (checkedUser.value && v.userId == checkedUser.value.userId);
    });
  });
};

// 选择的成员
const changeUser = (obj: any) => {
  userList.value.forEach((v) => {
    v.checked = false;
  });
  obj.checked = true;
  checkedUser.value = obj;
};

// 提交选择的成员
const submitUser = () => {
  emit('submitChooseUser', checkedUser.value);
};
</script>

<style lang="scss" scoped>
.chooseUserForDept-main {
  width: 100%;
  height: 100%;
}
.dialog-box {
  height: 300px;
  border: 1px solid rgba(219, 231, 238, 1);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  .left {
    flex: 2;
  }
  .center {
    flex: 2;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .right {
    flex: 3;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .title-div {
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    .normal-sapn {
      margin-left: 20px;
    }
  }
  .search-div {
    display: flex;
    flex-direction: row;
    padding: 0px 10px;
    margin-bottom: 5px;
  }
  .content {
    height: calc(100% - 75px);
    padding: 0px 8px;
    width: calc(100% - 16px);
    margin-left: 8px;
    overflow: auto;
    .el-tree-node__content {
      height: 32px;
      font-size: 12px;
    }
    .empty-span {
      color: #909399;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .flex-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      .label {
        font-size: 12px;
        padding-left: 12px;
        width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .label-field {
        font-size: 12px;
        padding-left: 12px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }
      .ico {
        padding-right: 8px;
      }
    }
    .flex-row:hover {
      background-color: #f5f7fa;
    }
    .flex-active {
      background: #edf4fb;
      color: var(--current-color);
    }
    .no-span {
      color: #d3d3d3 !important;
      cursor: not-allowed;
    }
    .flex-row-spe {
      height: auto;
      .spe-title {
        color: #d3d3d3;
        padding-left: 12px;
        cursor: not-allowed;
      }
      .spe-item {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        font-size: 12px;
        padding-left: 24px;
      }
      .spe-item:hover {
        background: #edf4fb;
      }
      .spe-item-active {
        background: #edf4fb;
      }
    }
    .tree-div {
      // height: calc(100% - 49px);
      overflow: auto;
      width: 100%;
      padding: 0px;
      .tree-row {
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        width: 100%;
        padding: 0px 16px;
        .tree-row-left {
          display: flex;
          flex-direction: row;
          align-items: center;
          .tree-img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background: #fff;
          }
          .svg-item {
            width: 16px;
            height: 16px;
            // margin-right: 8px;
            color: #333;
            vertical-align: middle;
          }
        }
        .tree-row-handle {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2px;
        }
        .tree-row-handle:hover {
          background: var(--current-color);
          color: #fff;
        }
      }
      .active-tree {
        background: var(--current-color);
        color: #fff;
      }
    }
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}
</style>
