<!-- 图形信息 -->
<template>
  <div class="graphicalInfo-info-container">
    <el-tree :data="graphicalList" node-key="id" :props="defaultProps" :expand-on-click-node="true" default-expand-all @node-click="checkedNode">
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span v-if="data.level === 1" class="zd-icon">
            <!-- 宗地图标 -->
            <el-image :src="zdIcon" />
          </span>
          <span v-if="data.level === 2 && data.type === 'z'" class="zd-icon">
            <!-- 幢 图标 -->
            <el-image :src="zIcon" />
          </span>
          <span v-if="data.level === 2 && data.type === 'hx'" class="zd-icon">
            <!-- 红线 图标 -->
            <el-image :src="hxIcon" />
          </span>
          <span v-if="data.level === 3" class="zd-icon">
            <!-- 楼层图标 层 -->
            <el-image :src="cIcon" />
          </span>
          <span v-if="data.level === 4 && data.label === '阳台'" class="zd-icon">
            <!-- 阳台 图标 -->
            <el-image :src="ytIcon" />
          </span>
          <span v-if="data.level === 4 && data.label === '楼梯'" class="zd-icon">
            <!-- 楼梯 图标 -->
            <el-image :src="ltIcon" />
          </span>
          <span v-if="data.level === 4 && data.label === '滴水线'" class="zd-icon">
            <!-- 滴水线 图标 -->
            <el-image :src="dsxIcon" />
          </span>
          <span v-if="data.level === 2 && data.type === 'sz'" class="zd-icon">
            <!-- 四至 图标 -->
            <el-image :src="szIcon" v-if="data.name === '北'" />
            <el-image :src="szIconN" v-if="data.name === '南'" />
            <el-image :src="szIconX" v-if="data.name === '西'" />
            <el-image :src="szIconD" v-if="data.name === '东'" />
          </span>
          <span class="zd-name">{{ node.label }}</span>
          <span class="zd-leng" v-if="data.children && data.children.length > 0">({{ data.children.length }})</span>

          <template v-if="data.children">
            <span class="zd-arrow" v-if="!data.isExpand && data.level !== 4 && data.type !== 'sz'"><i class="el-icon-arrow-right"></i></span>
            <span class="zd-arrow" v-if="data.isExpand && data.level !== 4 && data.type !== 'sz'"><i class="el-icon-arrow-down"></i></span>
          </template>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import zdIcon from '@/assets/images/zdIcon.png';
import zIcon from '@/assets/images/zIcon.png';
import cIcon from '@/assets/images/cIcon.png';
import szIcon from '@/assets/images/szIcon.png';
import ytIcon from '@/assets/images/ytIcon.png';
import dsxIcon from '@/assets/images/dsxIcon.png';
import ltIcon from '@/assets/images/ltIcon.png';
import hxIcon from '@/assets/images/hxIco.png';
import szIconD from '@/assets/images/szIconD.png';
import szIconN from '@/assets/images/szIconN.png';
import szIconX from '@/assets/images/szIconX.png';

interface TreeNode {
  label: string;
  id: string | number;
  level: number;
  type?: string;
  isExpand?: boolean;
  children?: TreeNode[];
  pointAttribution?: any;
  pieceAttribution?: any;
  desc?: string;
  name?: string;
}

const store = useProjectStore();
const currentParceItem = ref<Record<string, any>>({});

const defaultProps = {
  children: 'children',
  label: 'label'
};

const graphicalList = computed(() => {
  const val = store.parcelInfo;
  const graphicalList: TreeNode[] = [];
  const obj: TreeNode = {
    label: JSON.parse(JSON.stringify(val.parcelName)),
    id: JSON.parse(JSON.stringify(val.id)),
    level: 1,
    type: 'zd',
    isExpand: true,
    children: []
  };

  // 幢
  val.houseList.forEach((h: any) => {
    // 第二层的对象
    const secondObjH: TreeNode = {
      label: h.tag,
      id: h.id,
      level: 2,
      type: 'z',
      isExpand: true,
      pointAttribution: h.pointAttribution,
      children: []
    };

    // 楼层
    h.floorList.forEach((floor: any) => {
      // 第三层对象
      const thirdObj: TreeNode = {
        label: floor.tag,
        id: floor.id,
        level: 3,
        isExpand: true,
        children: []
      };

      floor.ancillaryList.forEach((an: any) => {
        // 第四层对象
        const fourthObj: TreeNode = {
          label: an.type === '4' ? '阳台' : an.type === '5' ? '楼梯' : '滴水线',
          id: an.id,
          level: 4,
          pieceAttribution: an.pieceAttribution
        };
        thirdObj.children?.push(fourthObj);
      });

      secondObjH.children?.push(thirdObj);
    });

    obj.children?.push(secondObjH);
  });
  /**
   * 四至
   * @param fourItem 四至
   */
  val.fourBoundaryList.forEach((fourItem: any) => {
    const secondObjf: TreeNode = {
      label: `${fourItem.name}(${fourItem.desc})`,
      id: fourItem.id,
      desc: fourItem.desc,
      pointAttribution: fourItem.pointAttribution,
      level: 2,
      type: 'sz',
      name: fourItem.name
    };
    obj.children?.push(secondObjf);
  });

  // 红线
  val.redLineList.forEach((redLin: any) => {
    const redLinobj: TreeNode = {
      label: '红线',
      id: redLin.id,
      level: 2,
      type: 'hx'
    };
    obj.children?.push(redLinobj);
  });

  graphicalList.push(JSON.parse(JSON.stringify(obj)));
  return graphicalList;
});
/**
 * 选中节点
 * @param node 节点
 * @param data 数据
 */
const checkedNode = (node: any, data: any) => {
  emit('centerFun', node);
  if (data.data.id === node.id) {
    node.isExpand = data.expanded;
  }
};

const emit = defineEmits(['centerFun']);
</script>

<style lang="scss" scoped>
.graphicalInfo-info-container {
  height: calc(100vh - 300px);
  overflow-y: auto;
  .el-tree {
    background: transparent;
    .el-icon svg {
      //原有的箭头 去掉
      display: none !important;
      height: 0;
      width: 0;
    }
    :deep(.el-tree-node__content) {
      width: 512;
      height: 44px;
      background: rgba(0, 0, 0, 0);
      border-radius: 0px 0px 0px 0px;
      .custom-tree-node {
        margin-left: 12px;
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;
        height: 44px;
        border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
        .zd-icon {
          width: 20px;
          .el-image {
            :deep(.el-image__inner) {
              width: 20px;
              height: 20px;
              margin: 12px;
              vertical-align: bottom;
            }
          }
        }
        .zd-name {
          height: 20px;
          width: 300px;
          font-size: 14px;
          font-family:
            PingFang SC-Medium,
            PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          margin-left: 12px;
        }
        .zd-leng {
          position: absolute;
          right: 40px;
          height: 20px;
          font-size: 14px;
          font-family:
            PingFangSC-328080,
            PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.5);
          line-height: 20px;
        }
        .zd-arrow {
          position: absolute;
          right: 10px;
          height: 20px;
          font-size: 14px;
          font-family:
            PingFangSC-328080,
            PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.5);
          line-height: 20px;
        }
      }
      &:hover {
        width: 512;
        height: 44px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
      }
    }
  }
}
:deep(.el-tree-node__content > .el-tree-node__expand-icon) {
  display: none;
  margin: 12px;
}
</style>
