<template>
  <dv-percent-pond :key="refreshFlagKey" :config="pondConfig" style="width: 100%; height: 100%" />
</template>

<script setup lang="ts">
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute } from 'vue-router';
const route = useRoute();
defineOptions({
  name: 'cpt-dataV-percentPond'
});
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const uuid = ref(null);
const pondConfig = ref({});
const refreshFlagKey = ref(null);
// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadData();
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
watch(
  () => props.height,
  () => {
    refreshFlagKey.value = uuidv1();
  }
);
onMounted(() => {
  uuid.value = uuidv1();
  refreshFlagKey.value = uuidv1();
  refreshCptData();
});
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId?: any) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      taskId: taskId,
      code: props.option.cptDataForm.code
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        const tempConfig = JSON.parse(JSON.stringify(props.option.attribute));
        tempConfig.value = res.data || 0;
        tempConfig.lineDash = [tempConfig.lineWidth, tempConfig.lineSpace];
        pondConfig.value = tempConfig;
        refreshFlagKey.value = uuidv1(); //强制刷新视图 报错为dataV组件内部bug
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      const tempConfig = JSON.parse(JSON.stringify(props.option.attribute));
      tempConfig.value = res.value;
      tempConfig.lineDash = [tempConfig.lineWidth, tempConfig.lineSpace];
      pondConfig.value = tempConfig;
      refreshFlagKey.value = uuidv1(); //强制刷新视图 报错为dataV组件内部bug
    });
  }
};
</script>

<style scoped></style>
