<template>
  <container-card>
    <div class="add-modal-contianer">
      <div class="add-modal-main">
        <div @click="handleBack">
          <div style="cursor: pointer; width: 45px; margin-left: 10px; display: flex; align-items: center">
            <el-icon><ArrowLeft /></el-icon>返回
          </div>
        </div>
        <div class="add-modal-step">
          <!-- :disabled="isDisableOwnerInfo"  -->
          <el-link :underline="false" @click="handleCurrentStep(1)" class="step" :style="{ color: isTextColor(1) }">
            <div class="step-mian">
              <div :class="activeSteps === 1 ? 'finish' : activeStepsList.includes(1) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(1) && activeSteps !== 1"><Check /></span>
                <span v-else>1</span>
              </div>
              <div>模块设置</div>
            </div>
          </el-link>
          <div class="step-arroaw">——></div>
          <el-link
            :underline="false"
            :disabled="!activeStepsList.includes(2)"
            @click="handleCurrentStep(2)"
            class="step"
            :style="{ color: isTextColor(2) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 2 ? 'finish' : activeStepsList.includes(2) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(2) && activeSteps !== 2"><Check /></span>
                <span v-else>2</span>
              </div>
              <div>特殊属性设置</div>
            </div>
          </el-link>
          <div class="step-arroaw">——></div>
          <el-link
            :underline="false"
            :disabled="!activeStepsList.includes(3)"
            @click="handleCurrentStep(3)"
            class="step"
            :style="{ color: isTextColor(3) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 3 ? 'finish' : activeStepsList.includes(3) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(3) && activeSteps !== 3"><Check /></span>
                <span v-else>3</span>
              </div>
              <div>采集设置</div>
            </div>
          </el-link>
          <!-- <div  class="step-arroaw">——></div>
        <el-link :underline="false" :disabled="!activeStepsList.value.includes(4)" @click="handleCurrentStep(4)" class="step"  :style="{color:isTextColor(4)}">
          <div class="step-mian">
            <div :class="activeSteps.value === 4?'finish': activeStepsList.value.includes(4)?'success':'unFinish'">
              <span v-if="activeStepsList.value.includes(4) && activeSteps.value !== 4"><Check /></span>
              <span v-else>4</span>
            </div>
            <div>图层设置</div>
          </div>
        </el-link> -->
          <div class="step-arroaw" v-show="vipType == 3">——></div>
          <el-link
            v-show="vipType == 3"
            :underline="false"
            :disabled="!activeStepsList.includes(4)"
            @click="handleCurrentStep(4)"
            class="step"
            :style="{ color: isTextColor(4) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 4 ? 'finish' : activeStepsList.includes(4) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(4) && activeSteps !== 4"><Check /></span>
                <span v-else>4</span>
              </div>
              <div>数据大屏</div>
            </div>
          </el-link>
          <div class="step-arroaw" v-show="vipType == 3">——></div>
          <el-link
            v-show="vipType == 3"
            :underline="false"
            :disabled="!activeStepsList.includes(5)"
            @click="handleCurrentStep(5)"
            class="step"
            :style="{ color: isTextColor(5) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 5 ? 'finish' : activeStepsList.includes(5) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(5) && activeSteps !== 5"><Check /></span>
                <span v-else>5</span>
              </div>
              <div>报告设置</div>
            </div>
          </el-link>
          <div class="step-arroaw" v-show="vipType == 3">——></div>
          <el-link
            v-show="vipType == 3"
            :underline="false"
            :disabled="!activeStepsList.includes(6)"
            @click="handleCurrentStep(6)"
            class="step"
            :style="{ color: isTextColor(6) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 6 ? 'finish' : activeStepsList.includes(6) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(6) && activeSteps !== 6"><Check /></span>
                <span v-else>6</span>
              </div>
              <div>导出设置</div>
            </div>
          </el-link>
          <div class="step-arroaw" v-show="vipType == 3">——></div>
          <el-link
            v-show="vipType == 3"
            :underline="false"
            :disabled="!activeStepsList.includes(7)"
            @click="handleCurrentStep(7)"
            class="step"
            :style="{ color: isTextColor(7) }"
          >
            <div class="step-mian">
              <div :class="activeSteps === 7 ? 'finish' : activeStepsList.includes(7) ? 'success' : 'unFinish'">
                <span v-if="activeStepsList.includes(7) && activeSteps !== 7"><Check /></span>
                <span v-else>7</span>
              </div>
              <div>拓扑检查设置</div>
            </div>
          </el-link>
        </div>
      </div>
      <!-- 第1步 基础设置 -->
      <div class="active-step-one" v-show="activeSteps === 1">
        <!-- @updateBaseInfo="handleUpdateBaseInfoFrom" -->
        <base-setting ref="baseSettingRef" @nextStep="handleCurrentStep"></base-setting>
      </div>
      <!-- 第2步 权属人设置 -->
      <div class="active-step-two" v-if="activeSteps === 2">
        <owner-setting
          :ownerList="ownerList"
          :ownerTypeList="ownerTypeList"
          ref="ownerSettingRef"
          @nextStep="handleCurrentStep"
          @updateOwnerList="getOwnerList"
        ></owner-setting>
      </div>
      <!-- 第3步 采集设置 -->
      <div class="active-step-third" v-show="activeSteps === 3">
        <acquisition-setting ref="acquisitionSettingRef" :moduleId="moduleId" @nextStep="handleCurrentStep"></acquisition-setting>
      </div>
      <!-- 第4步 图层设置 -->
      <!-- 2024/02/26 决定针对勘界功能是app 和后端来约定好做，不需要web设置 -->
      <!-- <div class="active-step-tc" v-show="activeSteps.value === 4">
        <tc-setting
          @nextStep="handleCurrentStep"
        ></tc-setting>
      </div> -->
      <!-- 第5步 数据大屏 -->
      <div class="active-step-third" v-show="activeSteps === 4">
        <dataScreen ref="dataScreenRef" :moduleId="moduleId" @nextStep="handleCurrentStep"></dataScreen>
      </div>
      <!-- 第6步 导出报告 -->
      <div class="active-step-four" v-show="activeSteps === 5">
        <settingTemp @nextStep="handleCurrentStep" ref="settingTempRef" :isFinish="false"></settingTemp>
      </div>
      <!-- 第7步 -->
      <div class="active-step-five" v-show="activeSteps === 6">
        <export-setting ref="exportSettingRef" @nextStep="handleCurrentStep"></export-setting>
      </div>
      <!-- 拓扑检查设置 -->
      <div class="active-step-five" v-show="activeSteps === 7">
        <examineSetting ref="examineSettingRef" :ruleTreeProp="ruleTree"></examineSetting>
      </div>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import baseSetting from './baseSetting/index.vue';
import acquisitionSetting from './acquisitionSetting/index.vue';
import exportSetting from './exportSetting/index.vue';
import ownerSetting from './ownerSetting/index.vue';
import settingTemp from './settingTemp/index.vue';
import { getOwnerListByModuleId, selectRules, addModule, modifyModule } from '@/api/modal/index';
import dataScreen from './dataScreen/index.vue';
import tcSetting from './tcSetting/index.vue';
import examineSetting from './acquisitionSetting/examineSetting.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { Check, ArrowLeft } from '@element-plus/icons-vue';
import { selectModuleById } from '@/api/modal/index';

const modalStore = useModalStore();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 定义响应式数据
const activeSteps = ref<number>(1);
const activeStepsList = ref<number[]>([1]);
const ownerList = ref<any[]>([]);
const ownerTypeList = ref<any[]>([]);
const ruleTree = ref<any[]>([]);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);
const vipType = computed(() => userStore.vipType);

// 定义 ref
const baseSettingRef = ref<any>(null);
const ownerSettingRef = ref<any>(null);
const acquisitionSettingRef = ref<any>(null);
const dataScreenRef = ref<any>(null);
const settingTempRef = ref<any>(null);
const exportSettingRef = ref<any>(null);
const examineSettingRef = ref<any>(null);

// 处理返回按钮
const handleBack = async () => {
  const flag = baseSettingRef.value.handleValidateBack();
  if (flag) {
    ElMessageBox.confirm('是否保存当前数据', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        if (activeSteps.value === 1) {
          handleValidateModalForm();
          modalStore.setNodeTree([]);
          router.push({
            path: '/modal',
            query: {
              companyId: route.query.companyId
            }
          });
        } else if (activeSteps.value === 5) {
          modalStore.setNodeTree([]);
          exportSettingRef.value.strongSubmit();
          router.push({
            path: '/modal',
            query: {
              companyId: route.query.companyId
            }
          });
        } else if (activeStepsList.value.includes(3)) {
          acquisitionSettingRef.value.handleNextBack();
          modalStore.setNodeTree([]);
          router.push({
            path: '/modal',
            query: {
              companyId: route.query.companyId
            }
          });
        } else {
          modalStore.setNodeTree([]);
          router.push({
            path: '/modal',
            query: {
              companyId: route.query.companyId
            }
          });
        }
      })
      .catch(() => {
        modalStore.setNodeTree([]);
        router.push({
          path: '/modal',
          query: {
            companyId: route.query.companyId
          }
        });
      });
  } else {
    modalStore.setNodeTree([]);
    router.push({
      path: '/modal',
      query: {
        companyId: route.query.companyId
      }
    });
  }
};

// 保存第一步的模块设置
const handleValidateModalForm = () => {
  const modalForm = baseSettingRef.value?.modalForm;
  if (moduleId.value === 0) {
    const data = {
      moduleName: modalForm.name,
      iconUrl: modalForm.icon,
      remark: modalForm.desc,
      areaType: modalForm.areaType,
      status: 0,
      levelNum: 1,
      type: 1,
      attribution: {
        roleIds: modalForm.roleIds
      }
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      data.companyId = companyId;
    }
    addModule(data).then((res) => {
      if (res.code === 200) {
        modalStore.setModuleId(Number(res.data.id));
        // 这里假设 $emit 可以通过其他方式处理，比如自定义事件
        // this.$emit('nextStep', 2);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    const data = {
      moduleName: modalForm.name,
      iconUrl: modalForm.icon,
      remark: modalForm.desc,
      areaType: modalForm.areaType,
      id: moduleId.value,
      delFlag: modalForm.delFlag,
      list: modalForm.list,
      createUserId: modalForm.createUserId,
      createTime: modalForm.createTime,
      type: 1,
      attribution: {
        roleIds: modalForm.roleIds
      }
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      data.companyId = companyId;
    }
    const params = {
      moduleId: moduleId.value
    };
    modifyModule(data, params).then((res) => {
      if (res.code === 200) {
        // 这里假设 $emit 可以通过其他方式处理，比如自定义事件
        // this.$emit('nextStep', 2);
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 当前点击的某一项
const handleCurrentStep = (num: number) => {
  activeSteps.value = num;
  activeStepsList.value = activeStepsList.value.filter((item) => item !== num);
  activeStepsList.value.push(num);
  if (num === 1) {
    saveTreeRule();
  }
  if (num === 2 && moduleId.value && moduleId.value !== 0) {
    getOwnerList(moduleId.value);
    saveTreeRule();
    if (ownerSettingRef.value) {
      ownerSettingRef.value.isFieldForm = false;
    }
  }
  if (num === 3) {
    if (acquisitionSettingRef.value) {
      acquisitionSettingRef.value.getModuleDetial();
      acquisitionSettingRef.value.$refs.ownerRef.getOwnerList();
      acquisitionSettingRef.value.isFieldForm = false;
    }
  }
  if (num === 4) {
    if (dataScreenRef.value) {
      dataScreenRef.value.getData();
    }
  }
  if (num === 5) {
    if (settingTempRef.value) {
      settingTempRef.value.getData();
    }
  }
  if (num === 6) {
    if (exportSettingRef.value) {
      exportSettingRef.value.getData();
    }
  }
  if (num === 7) {
    if (examineSettingRef.value) {
      examineSettingRef.value.getData();
    }
  }
};

// 主动触发
const saveTreeRule = () => {
  if (activeStepsList.value.includes(3)) {
    acquisitionSettingRef.value?.changeTabSaveRule();
  }
};

// 计算文本颜色
const isTextColor = (num: number): string => {
  let color = '#e0e4ea';
  if (activeStepsList.value.includes(num)) {
    if (activeSteps.value === num) {
      color = 'var(--current-color)';
    } else {
      color = '#09b667';
    }
  } else {
    color = '#8291a9';
  }
  return color;
};

// 用于获取步骤  可能只保存了基础设置
const getActiveSteps = async () => {
  if (moduleId.value) {
    const params = {
      moduleId: moduleId.value,
      groupScope: 1
    };
    let secondStep = false;
    let three = false;
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      params.companyId = companyId;
    }
    let speList = [];
    await getOwnerListByModuleId(params).then((res) => {
      if (res.code === 200) {
        if (res.data.length !== 0) {
          speList = res.data;
          secondStep = true;
        }
        modalStore.setSpeGroups(speList);
      } else {
        ElMessage.error(res.msg);
      }
    });
    const pitem = {
      moduleId: moduleId.value,
      ignoreStatus: true
    };
    // 设置公司私有模块的数据 需要传递公司id
    // const companyId = this.$route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      pitem.companyId = companyId;
    }
    await selectRules(pitem).then((res) => {
      ruleTree.value = res.data;
      localStorage.setItem('ruleTree', JSON.stringify(res.data));
      modalStore.setNodeTree(res.data);
      if (res.code === 200) {
        if (res.data.length !== 0) {
          three = true;
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
    activeStepsList.value = [1];
    if (secondStep) {
      activeStepsList.value.push(2);
    }
    if (three) {
      activeStepsList.value.push(2);
      activeStepsList.value.push(3);
      activeStepsList.value.push(4);
      activeStepsList.value.push(5);
      activeStepsList.value.push(6);
      activeStepsList.value.push(7);
    }
    //获取模块详情
    selectModuleById({ id: moduleId.value }).then((res) => {
      console.log('模块详情--', res);
      if (res.code === 200) {
        if (res.data.toolType === 1) {
          //代表是勘界 可以配置导出url
          baseSettingRef.value?.initKjSetting(res.data.url);
        }
      }
    });
  }
};

// 获取权属人信息
const getOwnerList = async (moduleId: number) => {
  const params = {
    moduleId: moduleId,
    groupScope: 1
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  await getOwnerListByModuleId(params).then((res) => {
    if (res.code == 200) {
      ownerList.value = res.data;
      if (res.data && res.data.length > 0) {
        ownerTypeList.value = [];
        res.data.forEach((item: any) => {
          ownerTypeList.value.push({ typeName: item.typeName, id: item.id, linkGroup: item.linkGroup });
        });
      }
      modalStore.setSpeGroups(res.data);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

onMounted(async () => {
  if (router.currentRoute.value.query.id) {
    modalStore.setModuleId(Number(router.currentRoute.value.query.id));
    await getActiveSteps();
  } else {
    // 新增模块
    modalStore.setSpeGroups([]);
  }

  const routeActiveSteps = router.currentRoute.value.query.activeSteps;
  if (routeActiveSteps) {
    activeSteps.value = Number(routeActiveSteps);
  }
});
defineExpose({
  handleCurrentStep,
  handleBack,
  activeSteps,
  activeStepsList,
  ownerList,
  ownerTypeList,
  ruleTree
});
</script>

<style lang="scss" scoped>
// 样式部分保持不变
.add-modal-contianer {
  height: 100%;
  // width: calc(100% - 40px);
  // position: absolute;
  // // top: 20px;
  // left: 20px;
  // bottom: 20px;
  // border-radius: 8px;
  // opacity: 1;
  background: #f6f7f8;
  // // overflow-y: auto;
  // // overflow-x: hidden;
  display: flex;
  // justify-content: space-between;
  flex-direction: column;
  overflow: hidden;
  .add-modal-main {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 70px;
    // padding: 0 16px;
    // line-height: 70px;
    background-color: #ffffff;
    color: #101010;
    font-size: 14px;
    text-align: center;
    font-family: PingFangSC-regular;
    border-bottom: 1px solid rgba(219, 231, 238, 1);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    .add-modal-step {
      display: flex;
      justify-content: center;
      align-items: center;
      // margin-left: 11%;
      flex-wrap: wrap;
      flex: 1;
      .step {
        // width: 100px;
        color: rgba(22, 29, 38, 1);
        font-size: 14px;
        text-align: left;
        font-family: PingFangSC-regular;
        font-weight: 400;
        .step-mian {
          display: flex;
          justify-content: center;
          align-items: center;
          .finish {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            background-color: var(--current-color);
            color: #fff;
            margin-right: 2px;
          }
          .success {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            background-color: #09b66d;
            color: #fff;
            margin-right: 2px;
          }
          .unFinish {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            background-color: #8291a9;
            color: #fff;
            margin-right: 2px;
          }
        }

        // margin: 0 16px;
        // cursor: pointer;
      }
      .step-arroaw {
        color: #8291a9;
        margin: 0 8px;
        font-weight: 400;
      }
    }
  }
  .active-step-one {
    display: flex;
    justify-content: center;
    align-self: center;
    background-color: #fff;
    width: 30%;
    min-width: 400px;
    height: calc(100% - 80px);
    overflow: auto;
    margin: 16px 0 24px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px 0px rgba(130, 145, 169, 0.12);
  }
  .active-step-two {
    // justify-self: center;
    // background-color: #fff;
    // width: 70%;
    // min-width: 400px;
    // height:calc(100% - 80px) ;
    // overflow: auto;
    // margin:16px 15% 24px;
    // border-radius: 8px;
    // box-shadow:  0px 4px 10px 0px rgba(130, 145, 169, 0.12);
  }
  .active-step-third {
    display: flex;
    flex-direction: row;
    height: 100%;
    width: 100%;
    overflow: auto;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  .active-step-five {
    height: calc(100% - 80px);
    overflow: auto;
    display: flex;
    flex-direction: row;
    height: 100%;
    width: 100%;
  }
  .active-step-tc {
    height: calc(100% - 80px);
    overflow: auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 100%;
    width: 100%;
  }
  .active-step-four {
    height: calc(100% - 70px);
    display: flex;
    flex-direction: row;
    width: 100%;
    border-radius: 8px;
    overflow: auto;
  }
}
</style>
