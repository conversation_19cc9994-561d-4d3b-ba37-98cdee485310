import { App } from 'vue';
// element
import cpt_text from '@/components/addDataScreen/element/cpt-text.vue';
import cpt_select from '@/components/addDataScreen/element/cpt-select.vue';
import cpt_module from '@/components/addDataScreen/element/cpt-module.vue';
import cpt_carousel from '@/components/addDataScreen/element/cpt-carousel.vue';
import cpt_button from '@/components/addDataScreen/element/cpt-button.vue';
import cpt_image from '@/components/addDataScreen/element/cpt-image.vue';
import cpt_iframe from '@/components/addDataScreen/element/cpt-iframe.vue';
import cpt_num from '@/components/addDataScreen/element/cpt-num.vue';
import cpt_rect_num from '@/components/addDataScreen/element/cpt-rect-num.vue';
import cpt_scroll_table from '@/components/addDataScreen/element/cpt-scroll-table.vue';
import cpt_organization from '@/components/addDataScreen/element/cpt-organization.vue';
// echarts
import cpt_chart_column from '@/components/addDataScreen/echarts/cpt-chart-column.vue';
import cpt_chart_pie from '@/components/addDataScreen/echarts/cpt-chart-pie.vue';
import cpt_chart_clock from '@/components/addDataScreen/echarts/cpt-chart-clock.vue';
import cpt_chart_map_gc from '@/components/addDataScreen/echarts/cpt-chart-map-gc.vue';
import cpt_chart_map_migrate from '@/components/addDataScreen/echarts/cpt-chart-map-migrate.vue';
import cpt_chart_line from '@/components/addDataScreen/echarts/cpt-chart-line.vue';
import cpt_chart_td_column from '@/components/addDataScreen/echarts/cpt-chart-td-column.vue';
import cpt_chart_gauge from '@/components/addDataScreen/echarts/cpt-chart-gauge.vue';
import cpt_chart_3dmap from '@/components/addDataScreen/echarts/cpt-chart-3dmap.vue';
import cpt_chart_radar from '@/components/addDataScreen/echarts/cpt-chart-radar.vue';
import cpt_chart_gl_bar3d from '@/components/addDataScreen/echarts/cpt-chart-gl-bar3d.vue';
import cpt_chart_geo_scatter from '@/components/addDataScreen/echarts/cpt-chart-geo-scatter.vue';

// dataV
import cpt_dataV_border from '@/components/addDataScreen/dataV/cpt-dataV-border.vue';
import cpt_dataV_scrollTable from '@/components/addDataScreen/dataV/cpt-dataV-scrollTable.vue';
import cpt_dataV_scrollList from '@/components/addDataScreen/dataV/cpt-dataV-scrollList.vue';
import cpt_dataV_waterLevel from '@/components/addDataScreen/dataV/cpt-dataV-waterLevel.vue';
import cpt_dataV_decoration from '@/components/addDataScreen/dataV/cpt-dataV-decoration.vue';
import cpt_dataV_digitalFlop from '@/components/addDataScreen/dataV/cpt-dataV-digitalFlop.vue';
import cpt_dataV_percentPond from '@/components/addDataScreen/dataV/cpt-dataV-percentPond.vue';
import cpt_dataV_activeRing from '@/components/addDataScreen/dataV/cpt-dataV-activeRing.vue';

const cptList = [
  cpt_dataV_border,
  cpt_text,
  cpt_select,
  cpt_module,
  cpt_carousel,
  cpt_button,
  cpt_image,
  cpt_chart_column,
  cpt_chart_td_column,
  cpt_chart_pie,
  cpt_chart_clock,
  cpt_dataV_scrollTable,
  cpt_scroll_table,
  cpt_dataV_scrollList,
  cpt_chart_map_gc,
  cpt_chart_map_migrate,
  cpt_dataV_waterLevel,
  cpt_dataV_decoration,
  cpt_chart_line,
  cpt_dataV_digitalFlop,
  cpt_dataV_percentPond,
  cpt_iframe,
  cpt_dataV_activeRing,
  cpt_chart_gauge,
  cpt_num,
  cpt_rect_num,
  cpt_chart_3dmap,
  cpt_chart_radar,
  cpt_chart_gl_bar3d,
  cpt_chart_geo_scatter,
  cpt_organization
];

export default {
  install(app: App): void {
    cptList.forEach((component) => {
      app.component(component.name, component);
    });
  }
};
