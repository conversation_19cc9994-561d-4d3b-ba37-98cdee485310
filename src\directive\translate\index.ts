import { slugify } from 'transliteration';
import type { Directive, DirectiveBinding } from 'vue';

interface TranslateDirectiveElement extends HTMLElement {
  _sourceElement?: HTMLInputElement;
  _targetElement?: HTMLInputElement;
  _isFirstInput?: boolean;
  _keyupHandler?: (event: Event) => void;
}

const vTranslate: Directive = {
  mounted(el: TranslateDirectiveElement, binding: DirectiveBinding, vnode: any) {
    // Find the source and target input elements
    const formEl = el.closest('form');
    if (!formEl) return;

    // Get source (roleName) and target (roleKey) input elements
    const sourceInputEl = formEl.querySelector('input[placeholder="请输入角色名称"]') as HTMLInputElement;
    const targetInputEl = formEl.querySelector('input[placeholder="请输入权限字符"]') as HTMLInputElement;

    if (!sourceInputEl || !targetInputEl) return;

    let isFirstInput = true;

    // Store references for cleanup
    el._sourceElement = sourceInputEl;
    el._targetElement = targetInputEl;
    el._isFirstInput = isFirstInput;

    const keyupHandler = () => {
      // Check if target is empty or not first input
      const isEmpty = !targetInputEl.value;
      if (isEmpty || !isFirstInput) {
        // Add a delay to make it less noticeable
        setTimeout(() => {
          const transValue = slugify(sourceInputEl.value, { separator: '' });

          const inputEvt = new InputEvent('input', {
            inputType: 'insertText',
            data: transValue,
            dataTransfer: null,
            isComposing: false
          });

          targetInputEl.value = transValue;
          targetInputEl.dispatchEvent(inputEvt);
          isFirstInput = false;
        }, 500);
      }
    };

    sourceInputEl.addEventListener('keyup', keyupHandler);
    el._keyupHandler = keyupHandler;
  },

  unmounted(el: TranslateDirectiveElement) {
    // Clean up event listener
    if (el._sourceElement && el._keyupHandler) {
      el._sourceElement.removeEventListener('keyup', el._keyupHandler);
    }
  }
};

export default vTranslate;

// Helper function from the original code
export function truncate(q: string): string {
  const length = q.length;
  if (length <= 20) return q;
  return q.substring(0, 10) + length + q.substring(length - 10, length);
}
