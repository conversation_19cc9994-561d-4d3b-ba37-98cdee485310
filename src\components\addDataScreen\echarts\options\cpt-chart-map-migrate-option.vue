<template>
  <el-form label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="attributeCopy.titleText" />
    </el-form-item>
    <el-form-item label="标题颜色">
      <el-color-picker v-model="attributeCopy.titleColor" show-alpha />
    </el-form-item>
    <el-form-item label="标题大小">
      <el-input-number v-model="attributeCopy.titleFontSize" :min="4" :max="100" />
    </el-form-item>
    <el-form-item label="副标题">
      <el-input v-model="attributeCopy.subtext" />
    </el-form-item>
    <el-form-item label="副标题颜色">
      <el-color-picker v-model="attributeCopy.subTitleColor" show-alpha />
    </el-form-item>
    <el-form-item label="副标题大小">
      <el-input-number v-model="attributeCopy.subTitleFontSize" :min="4" :max="100" />
    </el-form-item>
    <el-form-item label="标题位置(左)">
      <el-input v-model="attributeCopy.titleLeft" />
    </el-form-item>
    <el-form-item label="标题位置(上)">
      <el-input v-model="attributeCopy.titleTop" />
    </el-form-item>
    <el-form-item label="说明">
      <el-input v-model="attributeCopy.seriesName" />
    </el-form-item>
    <el-form-item label="缩放">
      <el-switch v-model="attributeCopy.roam" active-text="开" inactive-text="关" />
    </el-form-item>

    <el-form-item label="路线颜色">
      <el-color-picker v-model="attributeCopy.seriesColor" show-alpha />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-chart-map-migrate-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);
</script>

<style scoped></style>
