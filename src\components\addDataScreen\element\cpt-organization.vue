<template>
  <div style="width: 100%; height: 100%" class="module-main" @click="redirect">
    <div class="module-title">{{ companyName }}</div>
    <div class="flex-row">成员数量：{{ userNum }}</div>
    <div class="flex-row-two">模块数量：{{ moduleNum }}</div>
    <div class="table-div">
      <dv-scroll-ranking-board :config="config" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
// --- props ---
const props = defineProps<{
  option: Record<string, any>;
}>();
//  ---定义emit---
const emit = defineEmits<{
  (e: 'reload'): void;
  (e: 'changeTaskId', taskId: string): void;
}>();

// --- 定义变量 ---
const cptData = ref({
  option: []
});
const uuid = ref(null);
const taskId = ref('');
const config: any = ref({});
const companyName = ref('');
const userNum = ref(0);
const moduleNum = ref(0);
// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});

const loadData = (id) => {
  const url = window.location.href;
  const list = url.split('/');
  const moduleId = list[list.length - 2];
  if (id != '' || id == 0) {
    taskId.value = id;
  }
  if (props.option.cptDataForm.dataSource == 2) {
    // 该组件为 模块组件，只需要用户绑定对应的模块，然后自动内置模块需要的表达式得到数据
    //该组件需要直接获取表达式内容 内置
    //组织名称
    const parmas: any = {
      expression: '$CompanyName'
    };
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        companyName.value = res.data;
      }
    });
    // 模块列表
    const parmas1: any = {
      expression: 'getModuleList("moduleName,moduleNum")'
    };
    // 设置公司私有模块的数据 需要传递公司id
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas1.companyId = companyId;
    }
    getDataForFormula(parmas1).then((res) => {
      if (res.code == 200) {
        const list = [];
        res.data.listList.forEach((v) => {
          list.push({
            name: v[0],
            value: v[1]
          });
        });
        config.value = JSON.parse(JSON.stringify(props.option.attribute));
        config.value.data = list;
      }
    });
    // 模块数量
    const parmas2: any = {
      expression: 'getModuleList("moduleName,moduleNum").getSize()'
    };
    // 设置公司私有模块的数据 需要传递公司id
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas2.companyId = companyId;
    }
    getDataForFormula(parmas2).then((res) => {
      if (res.code == 200) {
        moduleNum.value = res.data;
      }
    });
    // 获取公司成员数量
    const parmas3: any = {
      expression: 'getManageInfo("userId","公司","count").getSize()'
    };
    // 设置公司私有模块的数据 需要传递公司id
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas3.companyId = companyId;
    }
    getDataForFormula(parmas3).then((res) => {
      if (res.code == 200) {
        userNum.value = res.data;
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    cptData.value.option = JSON.parse(props.option.cptDataForm.dataText);
    getDataJson(props.option.cptDataForm).then((res) => {
      config.value = JSON.parse(JSON.stringify(props.option.attribute));
      config.value.data = res;
    });
  }
};
const redirect = () => {
  if (props.option.attribute.url) {
    if (props.option.attribute.url.startsWith('view')) {
      router.push(props.option.attribute.url);
      emit('reload');
    } else {
      window.open(props.option.attribute.url);
    }
  }
};

// 切换任务
const changeTask = () => {
  for (let i = 0; i < cptData.value.option.length; i++) {
    if (cptData.value.option[i].value == taskId.value) {
      document.title = cptData.value.option[i].label + '任务数据大屏';
      break;
    }
  }
  emit('changeTaskId', taskId.value);
};

// onMounted
onMounted(() => {
  uuid.value = uuidv1();
  refreshCptData();
});
defineOptions({
  name: 'cpt-organization'
});
</script>

<style lang="scss" scoped>
select:focus-visible {
  outline: none;
}
option {
  font-size: 14px;
  color: #333;
  text-align: left;
}
.module-main {
  display: flex;
  flex-direction: column;
  position: relative;
  .module-title {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border: #1890ff solid 1px;
    border-radius: 4px;
    position: absolute;
    top: 0;
    width: 100%;
  }
  .flex-row {
    padding: 10px 0px;
    color: #fff;
    position: absolute;
    top: 60px;
  }
  .flex-row-two {
    padding: 10px 0px;
    color: #fff;
    position: absolute;
    top: 90px;
  }
  .table-div {
    position: absolute;
    height: calc(100% - 112px);
    top: 112px;
    width: 100%;
  }
}
</style>
