<!-- 代码编辑器组件 -->
<!-- 
  请不要删除！！！
  [/(max)|(min)|(sum)|(subtract)|(multiply)|floor|round|(divide)|(substring)|(concat)|(toUpperCase)|(toLowerCase)|(replace)/,'keyword'],
  [/toString|toList|getLength|getGeometry|getParts|getArea|getBeginPoint|getEndPoint/,'keyword'],
  [/size|getChild|getPictureList|getFields|getField|getFactorList|getFactor|getX|getY|getGroupsByLine/,'keyword'], 
  [/getMainGroups|getLineGroups|getLines|getPointGroups|getPoints|toStr|toPicture|special20230921|toDateStr|mergeList/,'keyword'],
  [/getGroupsByPoint|getNode|getInstances|getGroups|createTable|getAttr|get/,'keyword'], -->
<template>
  <div>
    <!--  :style="{maxWidth:'400px',height: scrollerEditorHeight,overflowY:'auto'}" -->
    <MonacoEditor
      class="monaco-editor"
      language="myLanguage"
      theme="myTheme"
      :editorMounted="onEditorMounted"
      :options="optionSetting"
      @change="onChange"
      id="containerRef"
    ></MonacoEditor>
  </div>
</template>
<script>
// import MonacoEditor from 'monaco-editor-vue';
// import * as monaco from 'monaco-editor'
// import 'monaco-editor/esm/vs/basic-languages/html/html.contribution'
// import { conf as htmlConf } from 'monaco-editor/esm/vs/basic-languages/html/html.js'
// import { language as mysqlLanguage } from 'monaco-editor/esm/vs/basic-languages/mysql/mysql.js'
// 格式化代码
const beautify = require('js-beautify');
const beautify_js = beautify.js;
export default {
  name: 'TheMonacoEditor',
  data() {
    return {
      optionSetting: {
        value: '', // 编辑器初始显示文字
        automaticLayout: true, // 自动布局
        overviewRulerBorder: false, // 不要滚动条的边框
        foldingStrategy: 'indentation', // 代码可分小段折叠
        tabSize: 0, // tab 缩进长度
        autoClosingBrackets: 'always', // 是否自动添加结束括号(包括中括号) "always" | "languageDefined" | "beforeWhitespace" | "never"
        // autoClosingDelete: 'always', // 是否自动删除结束括号(包括中括号) "always" | "never" | "auto"
        // autoClosingQuotes: 'always', // 是否自动添加结束的单引号 双引号 "always" | "languageDefined" | "beforeWhitespace" | "never"
        autoIndent: 'None', // 控制编辑器在用户键入、粘贴、移动或缩进行时是否应自动调整缩进
        autoClosingBrackets: true,
        comments: {
          ignoreEmptyLines: true, // 插入行注释时忽略空行。默认为真。
          insertSpace: true // 在行注释标记之后和块注释标记内插入一个空格。默认为真。
        }, // 注释配置
        cursorBlinking: 'Solid', // 光标动画样式
        cursorSmoothCaretAnimation: true, // 是否启用光标平滑插入动画  当你在快速输入文字的时候 光标是直接平滑的移动还是直接"闪现"到当前文字所处位置
        cursorSurroundingLines: 0, // 光标环绕行数 当文字输入超过屏幕时 可以看见右侧滚动条中光标所处位置是在滚动条中间还是顶部还是底部 即光标环绕行数 环绕行数越大 光标在滚动条中位置越居中
        cursorSurroundingLinesStyle: 'all', // "default" | "all" 光标环绕样式
        cursorWidth: 2, // <=25 光标宽度
        diagnostics: true,
        minimap: {
          // 关闭代码缩略图
          enabled: false // 是否启用预览图
        },
        overviewRulerBorder: false, // 是否应围绕概览标尺绘制边框
        wordWrap: 'on', // 文本自动换行
        folding: true, // 是否启用代码折叠
        scrollBeyondLastLine: false, // 设置编辑器是否可以滚动到最后一行之后
        renderLineHighlight: 'all', // 当前行突出显示方式  "all" | "line" | "none" | "gutter"
        theme: 'vs', // 官方自带三种主题vs, hc-black, or vs-dark
        formatOnPaste: true, //是否粘贴自动格式化
        renderValidationDecorations: 'on',
        hover: {
          enabled: true,
          delay: 500
        }
      },
      // 编辑器
      monacoEditor: null,
      // 编辑器的内容
      monacoObject: null,
      // 编辑器的指针注入
      monacoProject: null,
      monacoProject1: null
    };
  },
  computed: {
    scrollerEditorHeight() {
      return window.innerHeight - 500 + 'px';
    }
  },
  props: {
    // 手动敲代码的时候，提示的信息
    allHintList: {
      type: Array,
      require: true,
      default: () => []
    },
    // 属性组代码提示
    groupHintList: {
      type: Array,
      require: true,
      default: () => []
    },
    // 属性组下的代码提示
    fieldHintList: {
      type: Array,
      require: true,
      default: () => []
    },
    // 短暂的存取字符串的内容，在编辑的时候点击的累加值
    tempStr: {
      type: String,
      default: ''
    },
    expressionStr: {
      type: String,
      default: ''
    }
  },
  watch: {
    tempStr: {
      handler(val) {
        this.monacoEditor.setValue(val);
      },
      deep: true
    }
  },
  components: { MonacoEditor },
  created() {},
  mounted() {
    this.setCustomHint();
    this.setMonacoColor();
    this.setInputHint();
    this.setHoverCodeHint();
    this.handleAutoBacket();
    this.handleSetParamHint();
    this.handleRegistureCommand();
    this.handleDeleteAll();
    // this.handleQuicklyFixError()
  },
  beforeUnmount() {
    // 在离开的时候销毁数据,不然会有多份重复的数据提示
    window.provider?.dispose();
    window.provider1?.dispose();
    window.hoverCode?.dispose();
    window.signatureText?.dispose();
  },
  methods: {
    onChange(value) {
      if (!value || value == '') {
        this.$emit('updateExperssion', '');
      } else {
        // 处理字符场中的 空格,换行符
        const expressionStr = value;
        this.$emit('updateExperssion', expressionStr);
      }
    },
    onEditorMounted(editor, monaco) {
      window.editor = editor;
      window.monaco = monaco;
      this.monacoEditor = editor;
      this.monacoObject = monaco;
      // 检测当内容发生改变的时候
      editor.onDidChangeModelContent(this.changeModelChange);
      // 处理用户选择的动作
      // editor.onDidChangeCursorPosition(event => {
      //     const position = event.position;
      //     // 无效回调函数
      //     // editor.setSelection(position);
      //     // const codeActions = monaco.languages.getCodeActionsAtPosition('myLanguage', editor.getModel().getOffsetAt(position));
      //     // if (codeActions) {
      //     //   const selectedAction = codeActions[0];
      //     //   // 执行用户选择的动作
      //     //   if (selectedAction) {
      //     //     selectedAction.run();
      //     //   }
      //     // }
      // });
    },
    // 设置默认值的颜色和样式
    setMonacoColor() {
      const _this = this;
      // 设置方法的颜色,从数据库中取出方法
      const funcList = _this.allHintList.filter((i) => {
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        if (list.includes(i.type)) {
          i.kind = monaco.languages.CompletionItemKind.Function;
          return list.includes(i.type);
        }
      });
      let reg = '/\\b(';
      funcList.forEach((item, index) => {
        if (index == funcList.length - 1) {
          reg += `${item.name}`;
        } else {
          reg += `${item.name}|`;
        }
      });
      reg += ')\\b/i';
      // 上面拼凑完成是字符串 要转换成正则表达式
      const formationsfunc = eval(reg);
      // 设置变量的颜色
      let str = '/\\b(';
      const varList = _this.allHintList.filter((val) => {
        const list = [98, 99];
        if (list.includes(val.type)) {
          val.kind = monaco.languages.CompletionItemKind.Variable;
          return list.includes(val.type);
        }
      });
      varList.forEach((item, index) => {
        if (index === varList.length - 1) {
          str += `${item.name.substring(1)}`;
        } else {
          str += `${item.name.substring(1)}|`;
        }
      });
      str += ')\\b/i';
      const formationsValible = eval(str);
      monaco.languages.register({ id: 'myLanguage' });
      monaco.languages.setMonarchTokensProvider('myLanguage', {
        ignoreCase: false, //忽略大小写
        tokenizer: {
          root: [
            // 内置的颜色
            [/CHILDPOINT1|CHILDPOINT2/, 'custom-number'],
            // 变量的颜色
            [/[$]/, 'custom-number'],
            [formationsValible, { token: 'custom-number' }],
            //  方法的颜色
            [formationsfunc, { token: 'keyword' }],
            // 操作符的颜色
            [/[+]|[-]|[*]|[/]|[%]|[>]|[<]|[=]|[!]|[:]|[&&][||]/, { token: 'custom-oper' }],
            // 括号的颜色
            [/[(]|[)]/, { token: 'custom-let' }]
          ]
        }
      });
      monaco.editor.defineTheme('myTheme', {
        base: 'vs', // vs、vs-dark、hc-black
        inherit: true,
        rules: [
          { token: 'custom-number', foreground: '#7944F8' },
          { token: 'custom-string', foreground: '#0081ff' },
          { token: 'custom-sys', foreground: '#13ce66' },
          { token: 'custom-let', foreground: '#8ec2f2' },
          { token: 'custom-oper', foreground: '#f95e13' }
        ],
        colors: {
          'editor.background': '#e6ebf5', //编辑器背景颜色
          'editorLineNumber.foreground': '#0081ff', //行号颜色
          'editorLineNumber.activeForeground': '#7944F8', //当前行号颜色
          'editor.lineHighlightBackground': '#e6ebff', // 当前行背景色
          'editorGutter.background': '#e6ebf5' //行号背景色
        }
      });
      monaco.editor.setTheme('myTheme');
    },
    // 自定义代码提示
    setCustomHint() {
      // 取 data 中的数据需要 用_this 的内容
      const _this = this;
      const commonUse = [];
      // const myLanguage =[]
      // 因为注册语言注册了2次，所以在自定义提示（输入$ 的时候，会有重复的代码提示）的时候回显2次代码
      // 解决办法如下 判第一个语言注册是否已经注册过了，如果注册过了就销毁，为了解决代码重复提示的问题
      //  window.provider1?.dispose();  this.monacoProject?.dispose();
      window.provider = monaco.languages.registerCompletionItemProvider('myLanguage', {
        provideCompletionItems: function (model, position) {
          // 判第一个语言注册是否已经注册过了，如果注册过了就销毁，为了解决代码重复提示的问题
          // window.provider1?.dispose();
          // this.monacoProject1?.dispose();
          const textUnitPosition = model.getValueInRange({
            startLineNumber: position.lineNumber,
            startColumn: 1,
            endLineNumber: position.lineNumber,
            endColumn: position.column
          });

          let match = textUnitPosition.match(/(\S+)$/);
          if (!match) return [];
          match = match[0].toUpperCase();

          const suggestions = [];
          const handleSuggestions = (arr, type, detail) => {
            arr.forEach(async (item) => {
              let insertText = `${item.name}`;
              // 判断对象有多少个属性，并依次把他推进数组中 list 是需要和后端配合需要参数的,自动加（） noList 是前端写死，不需要参数,也不需要括号
              // TODO 98和99 是否需要参数的待定
              const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
              const noList = [98, 99];
              if (list.includes(item.type)) {
                insertText = `${item.name}()`;
              }
              suggestions.push({
                label: item.name, // 提示文本
                kind: item.kind, //提示类型
                // insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                sortText: commonUse.includes(item.name) ? '0' : '1', //排序，可以将常用提示放在前面
                detail: item.remark, // 提示解释文本
                insertText: insertText //插入内容，此处可以使用 $数值 来确定插入后光标位置，例如：'<if test="$0">\n\t\n</if>'
              });
            });
          };
          handleSuggestions(_this.allHintList, 'Field', '库表信息');
          handleSuggestions(_this.groupHintList, 'Field', '属性组');
          handleSuggestions(_this.fieldHintList, 'Field', '属性组字段');

          return {
            incomplete: true,
            suggestions: suggestions
          };
        }
      });
    },
    // 配置自动补全括号
    handleAutoBacket() {
      const config = {
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '<', close: '>' },
          { open: "'", close: "'" },
          { open: '"', close: '"' }
        ],
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: "'", close: "'", notIn: ['string', 'comment'] },
          { open: '"', close: '"', notIn: ['string', 'comment'] }
        ]
      };
      monaco.languages.setLanguageConfiguration('myLanguage', config);
    },
    //  获取内容改变
    changeModelChange(e) {
      // 获取当前光标的位置
      const currentPos = e.changes[0].rangeOffset;
      const currentText = this.monacoEditor.getValue();
      const model = monaco.editor.getModels()[0];
      // 取当前最新输入的内容
      const latestContent = e.changes[e.changes.length - 1].text;
      // 找到最新输入的内容的位置
      const openIndex = latestContent.indexOf('(');
      const closeIndex = latestContent.indexOf(')');
      // 获取当前编辑器中光标的位置
      const pos = this.monacoEditor.getPosition();
      // 获取当前光标所在位置的行号
      const currentLine = pos.lineNumber;
      // 获取当前行的编辑内容
      const lineText = model.getLineContent(currentLine);
      // 在这里拼凑正则表达式，要完全匹配
      let pattern = '/';
      let label = '';
      if (openIndex != -1 && closeIndex != -1) {
        label = latestContent.substring(0, openIndex);
        for (let i = 0; i < latestContent.length; i++) {
          if (latestContent[i] == ')' || latestContent[i] == '(') {
            pattern += `\\${latestContent[i]}`;
          } else if (i == latestContent.length - 1) {
            pattern += '/g';
          } else {
            pattern += latestContent[i];
          }
        }
      }
      pattern += '/g';
      let posArr = [];
      // 如果没有输入函数，则对应的正则表达式是//g,所以应该排除出去
      if (pattern != '//g') {
        // 当前输入的内容在当前行的位置，返回匹配到对应内容的起始位置和结束位置
        posArr = this.handleContentFindPosition(lineText, pattern);
      }
      // 返回值大于0，代表匹配到了正确函数，则设置光标的位置在（）中
      if (posArr.length > 0) {
        const col = posArr[1];
        const row = pos.lineNumber;
        const newPosition = {
          lineNumber: row,
          column: col
        };
        this.$nextTick(() => {
          this.monacoEditor.setPosition(newPosition);
          this.monacoEditor.focus();
          // 触发签名，调用内置函数  参数提示
          editor.trigger('keyboard', 'editor.action.triggerParameterHints');
        });
      }
    },
    // 设置值能否回退
    setEditorValue(val) {
      this.monacoEditor.pushUndoStop();
      this.monacoEditor.executeEdits('', [
        {
          range: this.monacoEditor.getModel().getFullModelRange(), // full range
          text: val // target value here
        }
      ]);
      this.monacoEditor.pushUndoStop();
    },
    // 手动输入的时候调用这里
    setInputHint() {
      // let _this = this
      window.provider1 = monaco.languages.registerCompletionItemProvider('myLanguage', {
        triggerCharacters: ['.', '$', '('], //输入$和.时触发
        provideCompletionItems: (model, position) => {
          // 判第一个语言注册是否已经注册过了，如果注册过了就销毁，为了解决代码重复提示的问题
          // window.provider?.dispose();
          // this.monacoProject?.dispose();
          const line = position.lineNumber;
          // 获取当前列数
          const column = position.column;
          // 获取当前行的所有内容
          const content = model.getLineContent(line);
          // 通过下标来取当前输入行的内容，刚刚输入的内容
          const sys = content[column - 2];
          // 当前光标所在位置的行的全部内容
          const codePre = model.getValueInRange({
            startLineNumber: position.lineNumber,
            startColumn: 1,
            endLineNumber: position.lineNumber,
            endColumn: position.column
          });
          const word = model.getWordUntilPosition(position);
          const suggestions = [];
          if (sys == '$') {
            // 这里不该写死，应该调用接口，取出类型等于 type = 98、99 的数据在这里渲染
            const arr = this.allHintList.filter((val) => {
              const list = [98, 99];
              if (list.includes(val.type)) {
                val.kind = monaco.languages.CompletionItemKind.Variable;
                return list.includes(val.type);
              }
            });
            arr.forEach((item) => {
              suggestions.push({
                label: item.name,
                // kind 代码提示的默认类型  代码提示前面的icon
                kind: item.kind,
                insertText: item.name.substring(1), //  因为输入了$才提示，所以再取值的时候不应该再取$要截去
                // insertTextRules: monaco.languages.CompletionItemInsertTextRule.None,
                detial: item.remark,
                range: {
                  startLineNumber: position.lineNumber,
                  endLineNumber: position.lineNumber,
                  startColumn: word.startColumn,
                  endColumn: word.endColumn
                }
              });
            });
          } else if (sys == '.') {
            // 当输入.的时候的提示的方法
            const pointList = this.handleInputPointToHint();
            pointList.forEach((item) => {
              let insertText = `${item.name}`;
              // 判断对象有多少个属性，并依次把他推进数组中 list 是需要和后端配合需要参数的,自动加（） noList 是前端写死，不需要参数,也不需要括号
              // TODO 98和99 是否需要参数的待定
              const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
              if (list.includes(item.type)) {
                insertText = `${item.name}()`;
              }
              suggestions.push({
                label: item.name,
                // kind 代码提示的默认类型  代码提示前面的icon
                kind: item.kind,
                insertText: insertText,
                // insertTextRules: monaco.languages.CompletionItemInsertTextRule.None,
                detial: item.remark,
                range: {
                  startLineNumber: position.lineNumber,
                  endLineNumber: position.lineNumber,
                  startColumn: word.startColumn,
                  endColumn: word.endColumn
                }
              });
            });
          } else {
            // 有其他默认提示
            suggestions.push[
              {
                label: word.word,
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: word.word,
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: {
                  startLineNumber: position.lineNumber,
                  endLineNumber: position.lineNumber,
                  startColumn: word.startColumn,
                  endColumn: word.endColumn
                }
              }
            ];
          }
          return {
            suggestions: suggestions
          };
        }
      });
    },
    // 鼠标悬浮再某个单词或者方法上面的时候 提示的内容
    setHoverCodeHint(e) {
      const _this = this;
      window.hoverCode = monaco.languages.registerHoverProvider('myLanguage', {
        provideHover: function (model, position) {
          const line = position.lineNumber;
          // 获取当前行的所有内容
          const content = model.getLineContent(line);
          // 当前光标所在位置的行的全部内容
          const codePre = model.getValueInRange({
            startLineNumber: position.lineNumber,
            startColumn: 1,
            endLineNumber: position.lineNumber,
            endColumn: position.column
          });
          let contents = [];
          let obj = {};
          let valueStr = '';
          let valueDesc = '';
          let objDesc = {};
          const handleSuggestions = (arr, type, detail) => {
            contents = [];
            const list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
            arr.forEach(async (item) => {
              if (list.includes(item.type) && content.substring(0, content.length - 2) == item.name) {
                //  取出参数
                if (item.param != null || item.param != undefined) {
                  obj = {};
                  valueStr = '';
                  const pKeys = Object.keys(item.param).sort();
                  pKeys.forEach((p, index) => {
                    if (index == pKeys.length - 1) {
                      valueStr += `参数${index + 1}:***${item.param[p]}*** `;
                    } else {
                      valueStr += `参数${index + 1}:***${item.param[p]}*** | `;
                    }
                    obj = { value: valueStr };
                  });
                  contents.push(obj);
                }
                // 取出参数描述
                if (item.paramDesc != null || item.paramDesc != undefined) {
                  valueDesc = '';
                  objDesc = {};
                  const dKeys = Object.keys(item.paramDesc).sort();
                  dKeys.forEach((d, index) => {
                    if (index == dKeys.length - 1) {
                      valueDesc += `参数${index + 1}:***${item.paramDesc[d]}*** `;
                    } else {
                      valueDesc += `参数${index + 1}:***${item.paramDesc[d]}*** | `;
                    }
                    objDesc = { value: valueDesc };
                  });
                  contents.push(objDesc);
                }
              }
            });
          };
          handleSuggestions(_this.allHintList, 'Field', '库表信息');
          return {
            range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
            contents: contents
          };
        }
      });
    },
    // 当前最新输入的内容在整个字符串中的位置,且括号中没有内容
    handleContentFindPosition(content, pattern) {
      // 传进来的pattern  是字符串，使用eval 函数生成正则表达式
      const pat = eval(pattern);
      const matches = content.match(pat);
      const arr = [];
      if (matches) {
        for (let i = 0; i < matches.length; i++) {
          const start_index = content.indexOf(matches[i]);
          const end_index = start_index + matches[i].length;
          arr.push(start_index);
          arr.push(end_index);
        }
      } else {
      }
      return arr;
    },
    // 当输入点. 的时候计算当前表达式返回的值，然后提供某些方法函数
    handleInputPointToHint() {
      // TODO  怎么只获取当前编辑行的内容 进行代码提示
      // TODO 如果当换行.之后代码提示就会不准确
      const text = this.monacoEditor.getValue();
      const textList = text.split('.');
      // 最后输入的内容
      // 当前最后输入的内容 应该-2  因为点值后会有一个空数据
      const lastContent = textList[textList.length - 2];
      // 判断是否包括括号
      const Sindex = lastContent.indexOf('(');
      const eIndex = lastContent.indexOf(')');
      // 和所有数据要比对的字符
      let str = '';
      if (Sindex != -1 && eIndex != -1) {
        str = lastContent.substring(0, Sindex);
      } else {
        str = lastContent;
      }
      let returnVal = '';
      let pointList = [];
      this.allHintList.forEach((item) => {
        if (item.name == str) {
          returnVal = item.returnVal;
        }
      });

      if (returnVal != null || returnVal != '') {
        pointList = this.allHintList.filter((item) => item.parentVal == returnVal);
      } else {
        // 如果返回值和相对应的调用值没有对应的，则提示所有数据
        pointList = this.allHintList;
      }
      return pointList;
    },
    //  设置参数提示
    handleSetParamHint() {
      window.signatureText = this.monacoObject.languages.registerSignatureHelpProvider('myLanguage', {
        // 重要说明 如果不使用特定字符来激活函数，可以使用editor.trigger('keyboard', 'editor.action.triggerParameterHints');来激活
        // keyboard表示触发来源为键盘事件，editor.action.triggerParameterHints是一个内置命令，用于显示参数提示和签名帮助信息
        signatureHelpTriggerCharacters: ['(', ',', '()', ')'],
        // signatureHelpTriggerCharacters:[],
        provideSignatureHelp: (model, position, token) => {
          const line = position.lineNumber;
          // 获取当前行的所有内容
          const content = model.getLineContent(line);
          // 找字符串中最后一个.
          const p = /\b(\w+)\s*\(/;
          //  取表达中最后一个点和（ 之间的内容，如果没有 .则只取 （ 前的内容
          const pointPos = content.lastIndexOf('.');
          const leftBacketPos = content.lastIndexOf('(');
          const rightBacketPos = content.lastIndexOf(')');
          let newLastContent = '';
          // pointPos < leftBacketPos 点的位置要永远小于（ 的位置，才能准确计算出参数
          // max(1.0,3.9) substring("8888",4,6)  这种情况时 截取的数据又会发生错误9) substring  计算了小数之前的.
          if (pointPos != -1 && pointPos < leftBacketPos) {
            newLastContent = content.substring(pointPos + 1, leftBacketPos);
          } else {
            newLastContent = content.substring(0, leftBacketPos);
          }
          let signatures = [];
          // 取出参数提示
          let parameters = [];
          const currentItem = this.allHintList.find((item) => item.name === newLastContent);
          if (currentItem && currentItem.param && currentItem.paramDesc) {
            // 取出当前数据的参数
            const pkeys = Object.keys(currentItem.param).sort();
            const paramList = [];
            pkeys.map((k, index) => {
              // paramList.push(currentItem.param[k])
              // 后面拼凑index 是为了解决 参数相同的时候，直接在最后一个画线
              // 因为 Monaco editor将会将这些参数视为同一个参数，并且只在签名帮助信息中显示一次
              paramList.push(`${currentItem.param[k]} $param${index + 1}`);
            });
            const paramStr = paramList.join(',');

            const dKeys = Object.keys(currentItem.paramDesc).sort();
            parameters = pkeys.map((param, index) => {
              return {
                label: `${currentItem.param[param]} $param${index + 1}`,
                documentation: currentItem.paramDesc[dKeys[index]]
              };
            });
            signatures = [
              {
                label: `${currentItem.name}(${paramStr})`,
                parameters: parameters
              }
            ];
          }
          // 动态设置参数位置
          // 确定参数的位置--- 比较参数括号和括号中的逗号 douhaoNum == parameterNum -1
          // 但数字函数中输入. 截取就会不准确-- 只截取( 之前的.  括号中的. 不截
          let paramContent = '';
          if (leftBacketPos != -1 && rightBacketPos != -1) {
            paramContent = content.substring(leftBacketPos + 1, rightBacketPos);
          }
          let activeParameter = 0;
          const activeSignature = 0;
          const count = paramContent.split(',').length;
          if (count > 0 && count <= parameters.length) {
            activeParameter = count - 1;
            // monaco.editor.getModelMarkers({owner: "owner"}).forEach(marker => {
            //   if (marker.severity === monaco.MarkerSeverity.Error || marker.severity === monaco.MarkerSeverity.Warning) {
            //     monaco.editor.setModelMarkers(model, "owner", [])
            //   }
            // })
          } else {
            // 当参数输入超过本来的参数 提示错误
            // monaco.editor.setModelMarkers(editor.getModel(), 'owner', [{
            //     startLineNumber: position.lineNumber,
            //     startColumn: 1,
            //     endLineNumber: position.lineNumber,
            //     endColumn: position.column,
            //     message:'参数数量过多', // 提示文案
            //     severity: monaco.MarkerSeverity.Error, // 提示的类型
            //   },
            // ])
          }
          return {
            dispose: () => {},
            value: {
              activeParameter: activeParameter,
              activeSignature: 0,
              signatures: signatures
            }
          };
        }
      });
    },
    //  注册快捷命令
    handleRegistureCommand() {
      // 在某个地方触发执行命令
      // this.editor.getAction(['editor.action.formateCodeForce' ])._run()
      editor.addAction({
        id: 'formateCodeForce',
        label: '强制格式化',
        keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyMod.Alt | monaco.KeyCode.KeyF],
        contextMenuGroupId: '9_cutcopypaste', // 2_customCommand  自定义指令
        run(ed, opt) {
          const a = editor.getValue();
          const b = beautify_js(a);
          // editor.setValue(b)
          editor.executeEdits('', [
            {
              range: new monaco.Range(1, 1, editor.getModel().getLineCount() + 1, 1),
              text: b
            }
          ]);
        }
      });
    },
    // 一键清除全部的代码
    handleDeleteAll() {
      editor.addAction({
        id: 'deleteAllText',
        label: '一键清空',
        keybindings: [monaco.KeyMod.Alt | monaco.KeyCode.Delete],
        contextMenuGroupId: '9_cutcopypaste', //navigation：该组是右键的第一栏，1_modification：修改组，9_cutcopypaste：剪切复制粘贴组
        run(ed, opt) {
          editor.executeEdits('', [
            {
              range: new monaco.Range(1, 1, editor.getModel().getLineCount() + 1, 1),
              text: ''
            }
          ]);
        }
      });
      // editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.Delete, () => { /* 清除代码操作 */
      //   editor.setValue('')
      // }, condition);
      // var condition = editor.createContextKey('condition ',false);
    },
    // 当标记了错误/画波浪线之后，快捷修复
    handleQuicklyFixError() {
      monaco.languages.registerCodeActionProvider('myLanguage', {
        provideCodeActions: function (model, token, context) {
          // model 是包含代码的 Monaco 模型
          // token 是用于 API 的令牌
          // context 是包含代码 action 上下文的对象
          const fixes = [
            {
              title: '修复波浪线',
              fix: function (editor, languageService) {
                // 这是一个修复函数，如果需要的话
                // 在这里提供修复逻辑
                // editor 是 Monaco 编辑器实例
                // languageService 是语言服务实例
                // 例如，你可以使用 languageService.getCompletionsAtPosition(model, token) 来获取可能的补全
              },
              edit: {
                range: new monaco.Range(1, 1, model.getLineCount() + 1, 1),
                text: 'max(1,2)'
              }
            }
          ];
          return {
            actions: fixes,
            dispose: () => {}
          };
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.monaco-editor .view-line {
  letter-spacing: 0.5px !important;
  font-family: inherit !important;
}
</style>
