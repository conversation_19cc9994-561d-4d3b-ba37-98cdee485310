<!-- 现场照片，户口本 -->
<template>
  <div class="zongdi-photo-container">
    <div v-if="imgList.length === 0" style="color: #fff">暂无数据...</div>
    <div class="photo-img" v-for="(img, imdx) in imgList" :key="img.id">
      <div class="loding-photo" v-if="isLoading">
        <el-image :src="lodingImg"></el-image>
      </div>
      <authImg
        class="img"
        :key="imdx"
        :authSrc="`${baseUrl}/${img.netUrl}?att=1`"
        :width="'156px'"
        :height="'156px'"
        :radios="'6px'"
        :isList="true"
        v-else
      >
      </authImg>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import lodingImg from '@/assets/images/lodingImg.png';
import authImg from '@/components/authImg/index.vue';
import { getToken } from '@/utils/auth';
import Axios from 'axios';
import { useProjectStore } from '@/store/modules/project';

interface Picture {
  id: string | number;
  netUrl: string;
  type: number;
}

interface Props {
  parceInfoItem: Record<string, any>;
  imgType: number;
}

const props = withDefaults(defineProps<Props>(), {
  parceInfoItem: () => ({}),
  imgType: 6
});

const baseUrl = import.meta.env.VUE_APP_BASE_API + '/qjt/file/downloadone';
const isLoading = ref(true);
const currentParceItem = ref<Record<string, any>>({});

watch(
  () => props.parceInfoItem,
  (val) => {
    currentParceItem.value = val;
  },
  { deep: true, immediate: true }
);

const imgList = computed<Picture[]>(() => {
  const list: Picture[] = [];
  if (props.parceInfoItem && Array.isArray(props.parceInfoItem.pictureList)) {
    props.parceInfoItem.pictureList.forEach((pic: Picture) => {
      if (pic.type === props.imgType) {
        list.push(pic);
      }
    });
  }
  return list;
});

watch(
  imgList,
  (newList) => {
    if (newList.length > 0) {
      const srcList: string[] = [];
      newList.forEach((v) => {
        Axios({
          method: 'get',
          url: `${baseUrl}/${v.netUrl}`,
          headers: { Authorization: 'Bearer ' + getToken(), 'Access-Control-Allow-Origin': '*' },
          responseType: 'blob'
        }).then((res) => {
          const blob = res.data;
          const reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onload = function () {
            srcList.push(reader.result as string);
          };
        });
      });
    }
    isLoading.value = false;
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.zongdi-photo-container {
  margin: 12px;
  display: grid;
  grid-gap: 10px;
  grid-template-columns: auto auto auto;
  overflow: auto;
  .photo-img {
    width: 100%;
    height: auto;
    .loding-photo {
      width: 156px;
      height: 156px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px 6px 6px 6px;
      border: 1px dashed #ffffff;
      display: flex;
      justify-items: center;
      .el-image {
        :deep(.el-image__inner) {
          width: 48px;
          height: 48px;
          margin: 54px;
        }
      }
    }
  }
}
</style>
