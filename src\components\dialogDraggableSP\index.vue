<!-- 审批设置 -->
<template>
  <div>
    <el-dialog title="审批设置" v-model="dialogVisible" width="30%" @close="handleClose">
      <div class="dialog-box">
        <div class="item" v-for="(item, index) in tableData" :key="index" @click="handleSubmitSPSetting(item)">
          <img :src="getIcon(item)" alt="" class="ico" />
          <div class="label">{{ item.processName }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
/// <reference types="vite/client" />
import { ref, watch, defineProps, defineEmits } from 'vue';
import { getPdefinitionList } from '@/api/process';
import { ElMessage } from 'element-plus';

interface ProcessItem {
  iconUrl: number;
  processName: string;
  [key: string]: any;
}

interface IconItem {
  src: string;
  id: number;
}

// 定义props
const props = defineProps<{
  draggableDialog: boolean;
}>();

// 定义emit
const emit = defineEmits<{
  (e: 'closeDraggableDailog'): void;
  (e: 'submitSPSetting', item: ProcessItem): void;
}>();

// 响应式数据
const height = ref(window.innerHeight - 300 + 'px');
const active = ref(1);
const tableData = ref<ProcessItem[]>([]);
const dialogVisible = ref(false);

// 获取图标列表 - 使用 import.meta.glob 替代 require.context
const iconModules = import.meta.glob('@/assets/images/approverIcon/*.png', { eager: true });
const iconList: IconItem[] = Object.entries(iconModules).map(([path, module], idx) => ({
  src: (module as { default: string }).default,
  id: idx
}));

// 监听props变化
watch(
  () => props.draggableDialog,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      handleProcessList();
    }
  },
  { immediate: true }
);

// 获取图标
const getIcon = (obj: ProcessItem): string => {
  const iconItem = iconList.find((item) => item.id === obj.iconUrl);
  if (iconItem && iconItem.id !== undefined && iconItem.src) {
    return iconItem.src;
  } else {
    return iconList[0].src;
  }
};

// 获取审批流程列表
const handleProcessList = () => {
  getPdefinitionList({ page: 1, size: 1000 }).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 关闭对话框
const handleClose = () => {
  emit('closeDraggableDailog');
};

// 提交审批设置
const handleSubmitSPSetting = (item: ProcessItem) => {
  emit('submitSPSetting', item);
};
</script>

<style lang="scss" scoped>
.dialog-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  max-height: 400px;
  overflow: auto;
  .item {
    padding: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    .ico {
      width: 44px;
      height: 44px;
    }
    .label {
      margin-left: 10px;
    }
  }
  .item:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #409eff;
  }
}
</style>
