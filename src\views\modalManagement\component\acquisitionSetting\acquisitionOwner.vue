<template>
  <div class="acquisition-owner-main">
    <div class="item-title">
      <div class="handle-title">
        <span class="text">特殊属性选择</span>
      </div>
    </div>
    <div class="attribute-main" v-if="ownerTypeList.length > 0">
      <div class="attribite-list">
        <el-row class="attribite-item" v-for="item in ownerTypeList" :key="item.id">
          <el-col :span="7" style="display: flex; align-items: center">
            <div
              v-if="item.iconUrl && item.iconUrl.substring(item.iconUrl.lastIndexOf('_') + 1) == 'blob'"
              style="display: flex; align-items: center"
            >
              <!-- <authImg :authSrc="`${baseUrl}${item.iconUrl}?att=1`" :width="'20px'" :height="'20px'" style="margin-right: 8px" /> -->
              <el-image style="width: 20px; height: 20px; margin-right: 10px" :src="`${baseUrl}${item.iconUrl}?token=${token}`" :fit="fit" />
            </div>
            <div v-else style="margin-left: 4px; margin-top: 2px">
              <svg-icon class-name="svg-item" :icon-class="item.iconUrl + ''" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }" />
            </div>
            <div class="item-type-name" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }">{{ item.typeName }}</div>
          </el-col>
          <el-col :span="8">
            <div class="item-remark" :style="{ color: idList.includes(item.id) ? '#161d26' : '#8291a9' }">{{ item.remark }}</div>
          </el-col>
          <el-col :span="9" style="display: flex; flex-wrap: nowrap; justify-content: flex-end">
            <el-checkbox-group v-model="idList">
              <el-checkbox :value="item.id" @change="handleCheckedOwnerType"></el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="attribute-main" v-else>
      <div class="attribite-list">
        <el-row class="attribite-item-no">
          <div class="no-text">暂无特殊属性</div>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import authImg from '@/components/authImg/index.vue';
import { getOwnerListByModuleId } from '@/api/modal/index';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const modalStore = useModalStore();
const userStore = useUserStore();

const token = useUserStore().token;

const fit = 'cover';

// 定义 props 类型
interface Props {
  checkedLevel: number;
  checkedNodeId: number;
  checkedTreeMsg: Record<string, any>;
}

const props = defineProps<Props>();

// 定义响应式数据
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const ownerTypeList = ref<
  Array<{
    id: number;
    iconUrl: string;
    typeName: string;
    remark: string;
    linkId?: any;
    ruleAttribution?: {
      type: string;
    };
  }>
>([]);
const idList = ref<number[]>([]);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);

// 根据 ModuleId 获取权属类型列表
const getOwnerList = async () => {
  const params = {
    moduleId: moduleId.value,
    groupScope: 1
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  try {
    const res = await getOwnerListByModuleId(params);
    ownerTypeList.value = res.data;
    if (props.checkedTreeMsg?.fieldGroupModelList?.length > 0) {
      props.checkedTreeMsg.fieldGroupModelList.forEach((v: any) => {
        ownerTypeList.value.forEach((k: any) => {
          if (k.id === v.id) {
            k.linkId = v.linkId;
          }
        });
      });
    }
  } catch (error) {
    console.error('获取权属类型列表失败', error);
  }
};

// 监听 checkedTreeMsg 变化
watch(
  () => props.checkedTreeMsg,
  (val) => {
    idList.value = [];
    if (val.graphicalType === 1 && val.fieldGroupModelList.length > 0) {
      // 点类型数据回显
      val.fieldGroupModelList.forEach((item: any) => {
        if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint') {
          idList.value.push(item.id);
        }
      });
    } else if (val.graphicalType === 2 && val.fieldGroupModelList.length > 0) {
      // 线类型数据回显
      val.fieldGroupModelList.forEach((item: any) => {
        if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') {
          idList.value.push(item.id);
        }
      });
    } else if (val.graphicalType === 4 && val.fieldGroupModelList.length > 0) {
      // 无图行数据回显
      val.fieldGroupModelList.forEach((item: any) => {
        if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalNull') {
          idList.value.push(item.id);
        }
      });
    } else {
      if (val.fieldGroupModelList) {
        val.fieldGroupModelList.forEach((item: any) => {
          // 正常情况
          if (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalArea') {
            idList.value.push(item.id);
          }
        });
      }
    }
  },
  { deep: true }
);

// 判断当前选择属性组
const handleCheckedOwnerType = () => {
  const old_fieldGroupModelList = JSON.parse(JSON.stringify(props.checkedTreeMsg.fieldGroupModelList));
  if (props.checkedTreeMsg.graphicalType === 1 || props.checkedTreeMsg.graphicalType === 4) {
    // 点要素 需要去掉老的属性组的线和面特殊属性组 避免 linkid 赋值错误
    for (let i = old_fieldGroupModelList.length - 1; i >= 0; i--) {
      if (
        old_fieldGroupModelList[i].ruleAttribution &&
        (old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalLine' || old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalArea')
      ) {
        old_fieldGroupModelList.splice(i, 1);
      }
    }
    let type = 'graphicalPoint';
    // 处理了点的内容和无图行内容
    if (props.checkedTreeMsg.graphicalType === 1) {
      type = 'graphicalPoint';
    } else if (props.checkedTreeMsg.graphicalType === 4) {
      type = 'graphicalNull';
    }
    props.checkedTreeMsg.fieldGroupModelList = props.checkedTreeMsg.fieldGroupModelList.filter((item: any) => {
      return item.groupScope === 2;
    });
    ownerTypeList.value.forEach((item: any) => {
      idList.value.forEach((id: number) => {
        if (item.id === id) {
          item.ruleAttribution = {
            // 相当于是面选中数据
            type: type
          };
          item.linkId = null;
          // 这里需要把 linkId 赋值回去
          for (let i = 0; i < old_fieldGroupModelList.length; i++) {
            if (old_fieldGroupModelList[i].id === id) {
              item.linkId = old_fieldGroupModelList[i].linkId;
              break;
            }
          }
          props.checkedTreeMsg.fieldGroupModelList.push(item);
        }
      });
    });
  } else if (props.checkedTreeMsg.graphicalType === 2) {
    // 线要素 需要去掉老的属性组的点和面特殊属性组 避免 linkid 赋值错误
    for (let i = old_fieldGroupModelList.length - 1; i >= 0; i--) {
      if (
        old_fieldGroupModelList[i].ruleAttribution &&
        (old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalPoint' || old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalArea')
      ) {
        old_fieldGroupModelList.splice(i, 1);
      }
    }
    // 处理了线内容
    props.checkedTreeMsg.fieldGroupModelList = props.checkedTreeMsg.fieldGroupModelList.filter((item: any) => {
      return item.groupScope === 2 || (item.groupScope === 1 && item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint');
    });
    ownerTypeList.value.forEach((item: any) => {
      idList.value.forEach((id: number) => {
        if (item.id === id) {
          item.ruleAttribution = {
            // 相当于是面选中数据
            type: 'graphicalLine'
          };
          item.linkId = null;
          // 这里需要把 linkId 赋值回去
          for (let i = 0; i < old_fieldGroupModelList.length; i++) {
            if (old_fieldGroupModelList[i].id === id) {
              item.linkId = old_fieldGroupModelList[i].linkId;
              break;
            }
          }
          props.checkedTreeMsg.fieldGroupModelList.push(item);
        }
      });
    });
  } else {
    // 正常情况
    // 面要素 需要去掉老的属性组的点和线特殊属性组 避免 linkid 赋值错误
    for (let i = old_fieldGroupModelList.length - 1; i >= 0; i--) {
      if (
        old_fieldGroupModelList[i].ruleAttribution &&
        (old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalPoint' || old_fieldGroupModelList[i].ruleAttribution.type === 'graphicalLine')
      ) {
        old_fieldGroupModelList.splice(i, 1);
      }
    }
    props.checkedTreeMsg.fieldGroupModelList = props.checkedTreeMsg.fieldGroupModelList.filter((item: any) => {
      return (
        item.groupScope === 2 ||
        (item.groupScope === 1 &&
          ((item.ruleAttribution && item.ruleAttribution.type === 'graphicalLine') ||
            (item.ruleAttribution && item.ruleAttribution.type === 'graphicalPoint')))
      );
    });
    ownerTypeList.value.forEach((item: any) => {
      idList.value.forEach((id: number) => {
        if (item.id === id) {
          item.ruleAttribution = {
            // 相当于是面选中数据
            type: 'graphicalArea'
          };
          item.linkId = null;
          // 这里需要把 linkId 赋值回去
          for (let i = 0; i < old_fieldGroupModelList.length; i++) {
            if (old_fieldGroupModelList[i].id === id) {
              item.linkId = old_fieldGroupModelList[i].linkId;
              break;
            }
          }
          props.checkedTreeMsg.fieldGroupModelList.push(item);
        }
      });
    });
  }
  emit('saveRule', 'owner');
};

const emit = defineEmits(['saveRule']);

onMounted(() => {
  // getOwnerList();
});
defineExpose({
  getOwnerList
});
</script>

<style lang="scss" scoped>
:deep(.el-checkbox__label) {
  display: none;
}
// 样式部分保持不变
.acquisition-owner-main {
  background-color: #fff;
  border: 1px solid #dbe7ee;
  border-radius: 12px;
  margin: 24px 16px 32px;
  position: relative;
  min-height: 80px;
  .item-title {
    position: absolute;
    top: -12px;
    left: 24px;
    right: 32px;
    display: flex;
    justify-content: space-between;

    .handle-title {
      // width: 80px;
      height: 16px;
      color: #333333;
      background-color: #fff;
      .text {
        color: #161d26;
        font-size: 14px;
        text-align: left;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        padding: 8px;
        font-weight: 600;
        vertical-align: top;
        &:hover {
          color: var(--current-color);
        }
      }
    }
  }

  &:hover {
    border: 1px solid var(--current-color);
    // color: #0081ff;
  }
  &:hover .handle-title .text {
    color: var(--current-color);
  }
  .attribute-main {
    // margin: 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    // margin: 24px 0 16px;
    margin-top: 12px;
    .attribite-list {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      // align-content: center;
      // align-items: center;
      margin: 0px 16px 12px;
      flex: 1;
      .attribite-item-no {
        margin: 12px 0;
        .no-text {
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-align: center;
          color: #8291a9;
        }
      }
      .attribite-item {
        margin: 12px 0;
        // padding: 8px 12px;
        padding: 0px 12px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        background-color: rgba(246, 247, 248, 1);
        // text-align: center;
        display: flex;
        align-items: center;

        .svg-item {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          color: #333;
        }
        .item-type-name {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
        }
        .item-remark {
          height: 40px;
          color: #161d26;
          font-size: 14px;
          text-align: left;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .text-btn {
          margin: 0 8px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          .svg-item {
            width: 16px;
            height: 16px;
            margin: auto;
            color: #8291a9;
            &:hover {
              color: var(--current-color);
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
          &:hover .svg-item {
            width: 16px;
            height: 16px;
            background-color: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
          }
        }
      }
    }
  }
}

.el-checkbox-group {
  .el-checkbox {
    :deep(&) {
      .el-checkbox__label {
        display: none;
      }
    }
  }
}
</style>
