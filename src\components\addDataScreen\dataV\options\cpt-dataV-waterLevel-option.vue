<template>
  <div style="position: relative">
    <el-form label-width="100px">
      <el-form-item label="颜色1">
        <el-color-picker v-model="tempColor1" show-alpha @change="changeColor" />
      </el-form-item>
      <el-form-item label="颜色2">
        <el-color-picker v-model="tempColor2" show-alpha @change="changeColor" />
      </el-form-item>
      <el-form-item label="样式">
        <el-select v-model="attributeCopy.shape" placeholder="请选择">
          <el-option label="矩形" value="rect" />
          <el-option label="圆角矩形" value="roundRect" />
          <el-option label="圆形" value="round" />
        </el-select>
      </el-form-item>
      <el-form-item label="波浪数量">
        <el-input-number v-model="attributeCopy.waveNum" :min="1" />
      </el-form-item>
      <el-form-item label="波浪高度">
        <el-input-number v-model="attributeCopy.waveHeight" :min="1" />
      </el-form-item>
      <el-form-item label="波浪透明度">
        <el-input-number v-model="attributeCopy.waveOpacity" :min="0" :max="2" />
      </el-form-item>
      <el-form-item label="信息格式化">
        <el-input type="textarea" v-model="attributeCopy.formatter" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'cpt-dataV-waterLevel-option'
});

const props = defineProps<{
  attribute: Record<string, any>;
}>();
const attributeCopy = computed(() => props.attribute);
// --- 定义变量 ---
const tempColor1 = ref('#00BAFF');
const tempColor2 = ref('#3DE7C9');

const changeColor = () => {
  attributeCopy.value.colors = [tempColor1.value, tempColor2.value];
  attributeCopy.value.refresh = !attributeCopy.value.refresh;
};
</script>

<style scoped></style>
