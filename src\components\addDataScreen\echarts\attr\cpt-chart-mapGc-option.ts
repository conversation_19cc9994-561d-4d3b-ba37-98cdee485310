const dataText = JSON.stringify([
  { 'name': '南海诸岛', 'value': 0 },
  { 'name': '北京市', 'value': 54 },
  { 'name': '天津市', 'value': 13 },
  { 'name': '上海市', 'value': 40 },
  { 'name': '重庆市', 'value': 75 },
  { 'name': '河北省', 'value': 13 },
  { 'name': '河南省', 'value': 83 },
  { 'name': '云南省', 'value': 11 },
  { 'name': '辽宁省', 'value': 19 },
  { 'name': '黑龙江省', 'value': 15 },
  { 'name': '湖南省', 'value': 69 },
  { 'name': '安徽省', 'value': 60 },
  { 'name': '山东省', 'value': 39 },
  { 'name': '新疆维吾尔自治区', 'value': 4 },
  { 'name': '江苏省', 'value': 31 },
  { 'name': '浙江省', 'value': 104 },
  { 'name': '江西省', 'value': 36 },
  { 'name': '湖北省', 'value': 1052 },
  { 'name': '广西壮族自治区', 'value': 33 },
  { 'name': '甘肃省', 'value': 7 },
  { 'name': '山西省', 'value': 9 },
  { 'name': '内蒙古自治区', 'value': 7 },
  { 'name': '陕西省', 'value': 22 },
  { 'name': '吉林省', 'value': 4 },
  { 'name': '福建省', 'value': 18 },
  { 'name': '贵州省', 'value': 5 },
  { 'name': '广东省', 'value': 98 },
  { 'name': '青海省', 'value': 1 },
  { 'name': '西藏自治区', 'value': 0 },
  { 'name': '四川省', 'value': 44 },
  { 'name': '宁夏回族自治区', 'value': 4 },
  { 'name': '海南省', 'value': 22 },
  { 'name': '台湾', 'value': 3 },
  { 'name': '香港', 'value': 5 },
  { 'name': '澳门', 'value': 5 }
]);
export default {
  cptDataForm: {
    dataSource: 1,
    pollTime: 0,
    dataText: dataText
  },
  attribute: {
    map: 'china',
    roam: false,
    titleText: '肺炎地图',
    titleLeft: 'center',
    titleTop: 10,
    subtext: '数据纯属虚构',
    titleFontSize: 20,
    titleColor: '#ddd',
    subTitleColor: '#aaa',
    subTitleFontSize: 13,
    seriesName: '确诊病例',
    geoLabelColor: '#555',
    geoLabelSize: 14,
    piecesLabel1: '> 100 人',
    piecesColor1: '#7f1100',
    piecesLabel2: '10 - 100 人',
    piecesColor2: '#ff5428',
    piecesLabel3: '1 - 9 人',
    piecesColor3: '#ff8c71',
    piecesLabel4: '疑似',
    piecesColor4: '#ffd768',
    piecesLabel5: '无',
    piecesColor5: '#ffffff',
    piecesMin: 0,
    piecesMax: 10,
    piecesMinLabel: '低',
    piecesMaxLabel: '高',
    piecesMinColor: 'lightskyblue',
    piecesCenterColor: 'yellow',
    piecesMaxColor: 'orangered'
  }
};
