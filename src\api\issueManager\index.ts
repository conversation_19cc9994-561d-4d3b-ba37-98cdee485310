import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LayerData, LayerQuery, UploadData } from '@/api/issueManager/types';

/**
 * 获取图层列表
 * @returns {AxiosPromise}
 */
export function getTCList(): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/layer/list/web',
    method: 'get'
  });
}

/**
 * 获取图层样式
 * @returns {AxiosPromise}
 */
export function getStyle(): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/get/styles',
    method: 'get'
  });
}

/**
 * 发布图层
 * @param params 图层数据
 * @returns {AxiosPromise}
 */
export function addTC(params: LayerData): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/publish/zip',
    method: 'post',
    data: params
  });
}

/**
 * 删除图层
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function delTC(params: LayerQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/publish/postgis/delete',
    method: 'delete',
    params: params
  });
}

/**
 * 删除图层(通过ID)
 * @param id 图层ID
 * @returns {AxiosPromise}
 */
export function delTC1(id: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/geoserver/shp/remove/layer/${id}`,
    method: 'delete'
  });
}

/**
 * 获取图层树形结构
 * @returns {AxiosPromise}
 */
export function getShpTree(): AxiosPromise<any> {
  return request({
    // url: '/qjt/geoserver/shp/get/tree/{platForm}',
    url: '/qjt/geoserver/shp/get/tree/web',
    method: 'get'
  });
}

/**
 * 初始化分片上传
 * @param params 上传参数
 * @returns {AxiosPromise}
 */
export function publishInit(params: LayerData): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/publish/zip/init',
    method: 'post',
    data: params
  });
}

/**
 * 上传展示内容
 * @param formData 上传数据
 * @returns {AxiosPromise}
 */
export function publishUpload(formData: UploadData): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/publish/zip/upload',
    method: 'post',
    data: formData
  });
}

/**
 * 合并分片
 * @param params 合并参数
 * @returns {AxiosPromise}
 */
export function publishMerge(params: LayerData): AxiosPromise<any> {
  return request({
    url: '/qjt/geoserver/shp/publish/zip/merge',
    method: 'post',
    data: params
  });
}

/**
 * 获取当前发布图层的进度
 * @param uploadId 上传ID
 * @returns {AxiosPromise}
 */
export function publishProgress(uploadId: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/geoserver/shp/publish/zip/progress/${uploadId}`,
    method: 'get'
  });
} 