<!-- 实例详情 -->
<template>
  <el-drawer
    @close="handleClose"
    :model-value="isShowInstanceDetial"
    @update:model-value="$emit('update:isShowInstanceDetial', $event)"
    direction="rtl"
    custom-class="demo-drawer"
    ref="drawer"
    :wrapperClosable="false"
    :destroy-on-close="true"
  >
    <template #header>详情</template>

    <div class="instance-main">
      <div class="content">
        <div class="instance-item-header">
          <div class="title" :title="instanceTitle">{{ instanceTitle }}</div>
          <el-link type="primary" @click="jumpDataDetail()" v-show="parcelId" style="margin: 0 0px 16px 16px">查看数据详情</el-link>
          <div class="text" v-if="instanceStatus === 0 && currentApproveNode.properties">等待{{ dealUserName }}处理</div>
          <div class="icon" v-else-if="instanceStatus === 100">
            <svg-icon class-name="svg-icon" icon-class="approveTG"></svg-icon>
          </div>
          <div class="icon" v-else-if="instanceStatus === 50">
            <svg-icon class-name="svg-icon" icon-class="approveJJ"></svg-icon>
          </div>
          <div class="icon" v-else-if="instanceStatus === 40">
            <svg-icon class-name="svg-icon" icon-class="approveCX"></svg-icon>
          </div>
          <div class="icon" v-else-if="instanceStatus === 70">
            <svg-icon class-name="svg-icon" icon-class="approveYC"></svg-icon>
          </div>
        </div>
        <div class="instance-item-form" v-if="processForm !== null && JSON.stringify(processForm) != '{}'">
          <div class="form-title">表单</div>
          <div v-for="(item, index) in processForm" :key="index" class="flex-item">
            <div class="title">{{ item.fieldCn }}:</div>
            <div class="text" v-if="item.tag == 'date'">
              {{ formatDateType(processFormValue[item.fieldName]) }}
            </div>
            <div class="text" v-else-if="item.tag == 'time'">
              {{ formatTime(processFormValue[item.fieldName]) }}
            </div>
            <div class="text" v-else-if="item.tag == 'checkbox'">
              {{ processFormValue[item.fieldName].join(',') }}
            </div>
            <div class="img-mian" v-else-if="item.tag == 'upload'">
              <div class="img-item" v-for="(pic, pIndex) in imgList" :key="pIndex">
                <!-- <el-image
                  style="width: 50px; height: 50px; border-radius: 12px"
                  :src="`${pic}`"
                  :preview-src-list="imgList"
                >
                </el-image> -->
                <authImg :authSrc="`${pic}?att=1`" :width="'50px'" :height="'50px'" :radios="'12px'" />
              </div>
            </div>
            <div class="text" v-else>
              {{ processFormValue[item.fieldName] }}
            </div>
          </div>
        </div>
        <div class="instance-item-line">
          <div class="title">流程</div>
          <el-steps direction="vertical">
            <el-step v-for="activity in processConfigList" :key="activity.nodeId">
              <template #icon>
                <svg-icon
                  class-name="svg-icon"
                  icon-class="approveend1"
                  v-if="[0, 40, 50, 70].includes(instanceStatus) && activity.icon == 'approveend'"
                ></svg-icon>
                <svg-icon class-name="svg-icon" :icon-class="activity.icon" v-else></svg-icon>
              </template>
              <template #description>
                <div class="desc-main" v-if="activity.type !== 'route'">
                  <div style="display: flex; justify-content: space-between">
                    <div
                      class="desc-title"
                      :style="{
                        color:
                          instanceStatus === 0 && activity.icon == 'approving'
                            ? '#f08706'
                            : ['approveend', 'approvewaiting'].includes(activity.icon)
                              ? '#595959'
                              : ''
                      }"
                    >
                      <div>{{ activity.properties.title }}</div>
                      <div v-if="['approver', 'audit'].includes(activity.type)" style="font-size: 12px; font-weight: 400">
                        <span v-if="activity.properties.actType == 'or'">（或签）</span>
                        <span v-else-if="activity.properties.actType == 'and'">（会签）</span>
                        <!-- <span v-else>（默认）</span> -->
                      </div>
                    </div>
                    <div class="text" v-if="activity.time != '' && activity.time">
                      {{ formatDateAndTimeType(activity.time) }}
                    </div>
                  </div>
                  <div class="desc-content">
                    <!-- 这里是处理节点 -->
                    <div v-if="instanceStatus === 0 && activity.icon == 'approving'">
                      等待<span style="color: #f08706; padding: 0 4px">{{ dealUserName }}</span
                      >处理
                    </div>
                    <!-- 这里是其他的节点 -->
                    <div v-else>
                      <div v-for="(app, appIndex) in activity.approve" :key="appIndex" class="desc-item">
                        <div v-if="activity.type === 'notifier'" class="notify-text">
                          <div class="text-name">
                            {{ app.approveUserNickName }}
                          </div>
                        </div>
                        <div class="text" v-else>
                          <div v-if="activity.icon !== 'approving'" style="font-size: 12px">
                            {{ app.approveUserNickName }}
                            <span v-if="activity.type !== 'start' && activity.type !== 'end'">
                              <span v-if="app.approveResult === 1">(同意)</span>
                              <span v-if="app.approveResult === 0">(拒绝)</span>
                            </span>
                          </div>
                        </div>
                        <div
                          class="desc-reason"
                          v-if="activity.type !== 'notifier' && activity.type !== 'end' && activity.approve.length > 0 && app.approveReason"
                        >
                          <div>{{ app.approveReason }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
      <div class="instance-bottom">
        <div style="margin-left: 8px">
          <el-button type="info" size="small" @click="handleClose" icon="CircleCloseFilled">关闭</el-button>
          <el-button
            type="warning"
            v-if="isShowWithdraw && instanceStatus === 0 && createUserId == currentUserId"
            size="small"
            @click="handleWithdraw"
            >撤销</el-button
          >
        </div>
        <div v-if="currentApproveNode.type !== 'audit' && instanceStatus === 0 && dealUserId.includes(currentUserId)" style="margin-right: 8px">
          <el-button type="danger" size="small" @click="handleSubmitApprove(0, 'approve')" plain>拒绝</el-button>
          <el-button type="primary" size="small" @click="handleSubmitApprove(1, 'approve')" plain>同意</el-button>
        </div>
        <div v-if="currentTag !== 2 && currentApproveNode.type == 'audit' && instanceStatus === 0 && dealUserId.includes(currentUserId)">
          <el-button type="primary" size="small" @click="handleSubmitApprove(1, 'adiut')" plain>提交</el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { getInstanceDetial, submitApprove, withdrawProcess } from '@/api/process/index';
import { getUserProfile } from '@/api/system/user/index';
import { useRouter } from 'vue-router';

// Interfaces
interface ApprovalUser {
  userId: string | number;
  userName: string;
}

interface NodeProperties {
  title: string;
  actType?: 'or' | 'and';
  approvals: ApprovalUser[];
  conditions?: any[];
  priority?: number;
}

interface ApproveItem {
  approveUserId: string | number;
  approveUserNickName: string;
  approveResult: number;
  approveReason?: string;
  approveTime?: string;
}

interface ProcessNode {
  nodeId: string;
  type: string;
  content?: string;
  properties: NodeProperties;
  childNode?: ProcessNode;
  conditionNodes?: ProcessNode[];
  approve: ApproveItem[];
  icon: string;
  time: string;
  prevId?: string;
}

interface FormField {
  fieldName: string;
  fieldCn: string;
  tag: string;
  options?: {
    label: string;
    value: string | number;
  }[];
}

interface CurrentItem {
  taskId: string;
  processInstanceId: string;
}

// Props
const props = defineProps({
  isShowInstanceDetial: {
    type: Boolean,
    default: false
  },
  instanceId: {
    type: String,
    default: '',
    required: true
  },
  currentItem: {
    type: Object as PropType<CurrentItem>,
    default: () => ({}),
    required: true
  },
  isShowApprove: {
    type: Boolean,
    default: false
  },
  isShowWithdraw: {
    type: Boolean,
    default: false
  },
  currentTag: {
    type: Number,
    default: 1
  }
});

// Emits
const emit = defineEmits(['closeInstance', 'update:isShowInstanceDetial']);

// State
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API || '');
const processForm = ref<FormField[]>([]);
const processFormValue = ref<Record<string, any>>({});
const imgList = ref<string[]>([]);
const currentApproveNode = ref<ProcessNode>({} as ProcessNode);
const instanceTitle = ref('');
const instanceStatus = ref(0);
const currentUserId = ref(-1);
const createUserId = ref(-1);
const processConfigResult = ref<Record<string, any>>({});
const resultNodeList = ref<ProcessNode[]>([]);
const parcelId = ref<string | null>(null);
const moduleId = ref<string | null>(null);
const router = useRouter();

// Computed properties
const dealUserName = computed(() => {
  let str = '';
  if (JSON.stringify(currentApproveNode.value) !== '{}') {
    const name = currentApproveNode.value.properties.approvals
      .map((item) => {
        return item.userName;
      })
      .join(',');
    str = `${name}`;
  }
  return str;
});

const dealUserId = computed(() => {
  const currentNodeCandidateUserId: (string | number)[] = [];
  let currentNodeApproveUserIds: (string | number)[] = [];

  // 从 currentApproveNode 找到候选审批人的id
  currentApproveNode.value?.properties?.approvals.forEach((item) => currentNodeCandidateUserId.push(item.userId));

  // 当前节点的nodeid
  const currentNodeId = currentApproveNode.value?.nodeId;

  // 获取节点processConfigResult结果中的的节点key值
  const currentKeys = Object.keys(processConfigResult.value);
  if (currentKeys.includes(currentNodeId)) {
    currentNodeApproveUserIds = processConfigResult.value[currentNodeId].approve?.map((item: ApproveItem) => item.approveUserId);
  }

  // 过滤出不同的用户ID
  const differentList = new Set(currentNodeCandidateUserId.filter((userId) => !new Set(currentNodeApproveUserIds).has(userId)));

  return Array.from(differentList);
});

const processConfigList = computed(() => {
  const list = resultNodeList.value.filter((node) => node.type !== 'route' && node.type !== 'condition');
  return list || [];
});

/**
 * 过滤当前的日期
 * @param value 当的日期
 */
const formatDateType = (value: string) => {
  // Implement date formatting
  if (!value) return '';
  // Simple date format implementation
  // In a real app, use a proper date formatting library
  return new Date(value).toLocaleDateString();
};

const formatTime = (value: string) => {
  // Implement time formatting
  if (!value) return '';
  // Simple time format implementation
  // In a real app, use a proper date formatting library
  return new Date(value).toLocaleTimeString();
};

const formatDateAndTimeType = (value: string) => {
  // Implement date and time formatting
  if (!value) return '';
  // Simple date and time format implementation
  // In a real app, use a proper date formatting library
  return new Date(value).toLocaleString();
};

/**
 * 关闭详情弹框
 */
const handleClose = () => {
  emit('closeInstance');
  emit('update:isShowInstanceDetial', false);
  // 关闭之后需要清空值，因为使用计算属性，不然页面进入之后不会流程列表
  resultNodeList.value = [];
  processConfigResult.value = {};
  processForm.value = [];
  processFormValue.value = {};
};

/**
 * 查询实例的详情
 * @param instanceId 实例id
 */
const handleInstanceDetial = (instanceId: string) => {
  getInstanceDetial({ id: instanceId }).then((res) => {
    if (res.code === 200) {
      handleResponseSuccess(res);
    } else {
      ElMessage.error(res.msg);
    }
  });
};
/**
 * 从接口中获取的请求到的数据
 */
const handleResponseSuccess = (res: any) => {
  currentApproveNode.value = res.data.currentNode;
  instanceTitle.value = res.data.title;
  instanceStatus.value = res.data.status;
  createUserId.value = res.data.createUserId;
  processForm.value = res.data.processForm;
  processConfigResult.value = res.data.processApproveResult;
  parcelId.value = res.data.parcelId;
  moduleId.value = res.data.moduleId;

  if (res.data.processForm && res.data.processForm !== null && JSON.stringify(res.data.processForm) != '{}') {
    processFormValue.value = reduceProcessFormValues(res.data.processFormValue, res.data.processForm);
    // 处理上传附件查看大图
    handleUploadImages(res.data.processForm, processFormValue.value);
  }

  const processApproveResultCopy = res.data.processApproveResult;
  handleProcessConfigList(res.data.processConfig, processApproveResultCopy, res.data.currentNode, processFormValue.value);
};

/**
 * 解析用户填写的值
 * @param values 当前表单中填写的只，流程表到
 * @param processForm
 */
const reduceProcessFormValues = (values: any[], processForm: FormField[]) => {
  const formValue = values.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  //  这里需要特殊处理单选，多选，下拉的值，存的时value  需要展示处理的值时label
  const formValueKeys = Object.keys(formValue);
  processForm.forEach((item) => {
    if (['select', 'radio', 'checkbox'].includes(item.tag) && formValueKeys.includes(item.fieldName)) {
      const options = JSON.parse(JSON.stringify(item.options || []));
      //  多选框选中的值一个数组，需要单独处理  下拉和单选可以一起处理
      if (item.tag === 'checkbox') {
        const fieldName = JSON.parse(JSON.stringify(item.fieldName));
        const resultList = options.filter((opt: any) => formValue[fieldName].includes(opt.value)).map((opt: any) => opt.label);
        formValue[fieldName] = resultList;
      } else {
        const fieldName = JSON.parse(JSON.stringify(item.fieldName));
        const resultItem = options.find((opt: any) => opt.value === formValue[fieldName]);
        if (resultItem) {
          formValue[fieldName] = resultItem.label;
        }
      }
    }
  });
  return formValue;
};

//
/**
 * 处理上传的附件内容解析，可以查看大图
 * @param formFields 表单字段
 * @param formValues 表单值
 */
const handleUploadImages = (formFields: FormField[], formValues: Record<string, any>) => {
  imgList.value = [];
  const uploadField = formFields.find((item) => item.tag === 'upload');
  if (uploadField && formValues[uploadField.fieldName]) {
    const filePathList = formValues[uploadField.fieldName].split(',');
    filePathList.forEach((file) => {
      const path = `${baseUrl.value}/qjt/file/downloadone/${file}`;
      imgList.value.push(path);
    });
  }
};

/**
 * 处理用户流程的设置
 * @param processConfig 当前流程数据 节点
 * @param processApproveResultCopy 节点处理的结果
 * @param currentNodeItem 当前执行到的节点
 * @param processFormValues 用户输入的原始值，非转换后的值
 */
const handleProcessConfigList = (
  processConfig: ProcessNode,
  processApproveResultCopy: Record<string, any>,
  currentNodeItem: ProcessNode,
  processFormValues: Record<string, any>
) => {
  // 这里定义最后要在页面上展示的当前的节点Node
  const currentNode: ProcessNode = {
    type: processConfig.type,
    content: processConfig.content,
    properties: processConfig.properties,
    nodeId: processConfig.nodeId,
    childNode: processConfig.childNode,
    conditionNodes: processConfig.conditionNodes,
    approve: [],
    icon: 'approvewaiting', // 默认值
    time: '',
    prevId: processConfig.prevId
  };

  const resultKeys = Object.keys(processApproveResultCopy);
  // 取nodeId 和 NodeType
  const nodeId = processConfig.nodeId;
  const nodeType = processConfig.type;

  if (resultKeys.includes(nodeId)) {
    // 当前节点在审批节点中，把当前审核的结果approve添加到当前节点中，需要在页面中展示
    currentNode.approve = processApproveResultCopy[nodeId].approve;
    currentNode.time = processApproveResultCopy[nodeId].approve[0].approveTime;
    // 将当前节点添加到数组最后的节点中
    handleAddNodeToResultNodeList(currentNode);
    // 再次遍历 conditionNodes 和 childNode
    handleCommonConditionNodesAndChildNode(currentNode, processConfig, processApproveResultCopy, currentNodeItem, processFormValues);
  } else {
    // 节点不在审批结果中：流程可能会经过节点，也可能不经过节点，需要判断
    if (nodeType == 'condition') {
      const conditions = processConfig.properties.conditions;
      // 判断当前流程中是否经过这个节点
      const isConsitionFlag = handleContions(conditions, processFormValues);

      if (isConsitionFlag) {
        // 这里代表进入此节点，需要将此节点放到对应的节点中
        // 将当前节点添加到数组最后的节点中
        handleAddNodeToResultNodeList(currentNode);
        // 再次遍历 conditionNodes 和 childNode
        handleCommonConditionNodesAndChildNode(currentNode, processConfig, processApproveResultCopy, currentNodeItem, processFormValues);
      } else {
        return;
      }
    } else {
      // 当前条件的节点类型不是条件condition分支，而是route audit approval notifiy的情况
      // 将当前节点添加到数组最后的节点中
      handleAddNodeToResultNodeList(currentNode);
      // 再次遍历 conditionNodes 和 childNode
      handleCommonConditionNodesAndChildNode(currentNode, processConfig, processApproveResultCopy, currentNodeItem, processFormValues);
    }
  }

  handleUpdateIcon();

  // 检查是否是当前正在处理的节点，如果是当前正在处理的节点需改其中的样式图标内容
  if (currentNodeItem.nodeId === processConfig.nodeId) {
    currentNode.icon = 'approving';
  }
};

/**
 * 把当前符合条件的节点添加到的结果数组中
 * @param currentNode 当前节点
 */
const handleAddNodeToResultNodeList = (currentNode: ProcessNode) => {
  // 因为节点可能重复添加，所以做一个判断，数组中当前节点的nodeId的时候才添加
  const ids = resultNodeList.value.map((item) => item.nodeId);
  if (!ids.includes(currentNode.nodeId)) {
    resultNodeList.value.push(currentNode);
  }
};

// 处理conditionNodes的数据
/**
 * 处理条件节点
 * @param currentNode  当前节点
 * @param conditionNodes 当前条件
 * @param processApproveResult 审批流程的节点
 * @param currentNodeItem 当前审核到达的节点
 * @param processFormValue 当前用户输入的值
 */
const handleConditionNodes = (
  currentNode: ProcessNode,
  conditionNodes: ProcessNode[],
  processApproveResult: Record<string, any>,
  currentNodeItem: ProcessNode,
  processFormValues: Record<string, any>
) => {
  const resultKeys = Object.keys(processApproveResult);
  const sortedConditionNode = handleConditionSort(conditionNodes);

  sortedConditionNode.forEach((conditonSortItem) => {
    const conditionNodeId = conditonSortItem.nodeId;

    if (resultKeys.includes(conditionNodeId)) {
      // 条件分支的节点在审核结果中 processApproveResult
      currentNode.approve = processApproveResult[conditionNodeId].approve;
      // 将当前节点添加到数组最后的节点中
      handleAddNodeToResultNodeList(currentNode);
      // 再次遍历 conditionNodes 和 childNode
      handleCommonConditionNodesAndChildNode(currentNode, conditonSortItem, processApproveResult, currentNodeItem, processFormValues);
    } else {
      const conditions = conditonSortItem?.properties?.conditions;
      const isConsitionFlag = handleContions(conditions, processFormValues);

      if (isConsitionFlag) {
        // 这里代表进入此节点，需要将此节点放到对应的节点中
        // 将当前节点添加到数组最后的节点中
        handleAddNodeToResultNodeList(currentNode);
        // 再次遍历 conditionNodes 和 childNode
        handleCommonConditionNodesAndChildNode(currentNode, conditonSortItem, processApproveResult, currentNodeItem, processFormValues);
      } else {
        return;
      }
    }
  });
};

// 判断 conditionNode 的 conditions, 目的是判断程序是否经过这个节点
const handleContions = (conditions: any[], processFormValues: Record<string, any>) => {
  let valid = false;
  // 外层循环是否继续
  let outFlagOfOr = false;
  for (let i = 0; i < conditions.length; i++) {
    // 获取内层的条件数组，外层条件是或（or）的关系
    // 遍历内层的条件数组，内层条件是且（and）的关系
    // 内层循环中，只要有一个的结果为真，则外层的结果就为真，无需继续循环外层
    // 只有当内层的结果一直是false的时候，才需要继续循环外层：因为外层是or的关系，内层是and的关系
    const outConditionItem = conditions[i];
    let innerFlagOfAnd = true;
    for (let j = 0; j < outConditionItem.length; j++) {
      const innerConditionItem = outConditionItem[j];
      // 获取类型来判断
      const innerType = innerConditionItem.type;
      // 获取当前的字段 id
      const innerParamKey = innerConditionItem.paramKey;
      // 获取当前的比较符号的类型
      const innerParamType = innerConditionItem.matchType;
      // 获取当前的比较整个的结果值，介于的情况是一个数组，需要两个值
      const innerParmaValue = innerConditionItem.conditionNumValue;
      // 获取当前用户输入的值
      const paramKeyValue = processFormValues[innerParamKey];

      if (innerType === 'condition_range') {
        // 数字类型判断
        valid = handelEvaluateExpression(innerParamType, paramKeyValue, innerParmaValue);

        if (!valid) {
          // 内层条件有一个为false，则内层的最终结果都是false(因为内层是且【and】的关系)
          innerFlagOfAnd = false;
          break;
        }
      } else if (innerType === 'condition_value') {
        // 单选的情况
        const innerParmaValue = JSON.parse(JSON.stringify(innerConditionItem.paramValues));
        // 获取当前用户输入的值
        const paramKeyValue = Number(processFormValues[innerParamKey]);

        if (!innerParmaValue.includes(paramKeyValue)) {
          // 内层选中的值包括页面的值，则还继续判断一个内层的条件
          // 如果内层选中的值不包括页面中的值，则当前内层循环就没有满足条件的，就跳出循环
          innerFlagOfAnd = false;
          break;
        }
      } else if (innerType === 'condition_multi_range') {
        // checkBox 多选框进入的情况
        const innerParmaValues = JSON.parse(JSON.stringify(innerConditionItem.paramValues));
        const formValues = JSON.parse(JSON.stringify(processFormValues[innerParamKey]));

        // 获取当前匹配的类型
        const type = innerConditionItem.matchType;

        if (type === 'any' && Array.isArray(formValues)) {
          // 任意匹配的方法
          valid = innerParmaValues.some((item: any) => formValues.includes(item));

          if (!valid) {
            // 返回false就终止内层循环去执行外层循环
            innerFlagOfAnd = false;
            break;
          }
        } else if (type === 'all' && Array.isArray(formValues)) {
          // 完全匹配的方法
          valid = innerParmaValues.some((item: any) => formValues.includes(item));

          if (!valid) {
            innerFlagOfAnd = false;
            break;
          }
        }
      } else if (innerType === 'condition_starter') {
        const innerParmaValues1 = JSON.parse(JSON.stringify(innerConditionItem.paramValues));
        valid = innerParmaValues1.includes(currentUserId.value);

        if (!valid) {
          innerFlagOfAnd = false;
          break;
        }
      }
    }

    if (innerFlagOfAnd) {
      // 内层结果为有一组为true，则整个结果都为true，无需继续往下，结束整个循环
      outFlagOfOr = true;
      break;
    }
  }

  return outFlagOfOr;
};

// 处理大于小于等于的情况 formValue是用户输入的值 targetValue流程设计时候的条件值
const handelEvaluateExpression = (type: string, formValue: any, targetValue: any) => {
  let result = false;

  switch (type) {
    case 'gt': // 大于
      result = formValue > targetValue;
      break;
    case 'lt': // 小于
      result = formValue < targetValue;
      break;
    case 'gte': // 大于等于
      result = formValue >= targetValue;
      break;
    case 'lte': // 小于等于
      result = formValue <= targetValue;
      break;
    case 'eq': // 等于
      result = formValue === targetValue;
      break;
    case 'bet': // 介于两者之间
      result = targetValue[0] < formValue && formValue < targetValue[3];
      break;
    case 'ebet': // 介于两者之间
      result = targetValue[0] <= formValue && formValue < targetValue[3];
      break;
    case 'bete': // 介于两者之间
      result = targetValue[0] < formValue && formValue <= targetValue[3];
      break;
    case 'ebete': // 介于两者之间
      result = targetValue[0] <= formValue && formValue <= targetValue[3];
      break;
    default:
      throw new Error(`Unsupported type: ${type}`);
  }

  return result;
};

// 根据condtionNodes排序
const handleConditionSort = (conditionNodes: ProcessNode[]) => {
  return conditionNodes.sort((a, b) => {
    // 获取两个对象中 properties 下的 priority 值
    const priorityA = a.properties?.priority ?? Infinity;
    const priorityB = b.properties?.priority ?? Infinity;
    // 比较 priority 值，返回值决定排序顺序
    // priorityA < priorityB 时 a 应该在 b 前面
    return priorityA - priorityB;
  });
};

// 在每一个分支和每一个条件中都需要创建先判断conditionNodes在判断childNode
// 在这里抽成一个公共的方法
const handleCommonConditionNodesAndChildNode = (
  currentNode: ProcessNode,
  configItem: ProcessNode,
  processApproveResult: Record<string, any>,
  currentNodeItem: ProcessNode,
  processFormValues: Record<string, any>
) => {
  if (configItem.conditionNodes && configItem.conditionNodes.length > 0) {
    handleConditionNodes(currentNode, configItem.conditionNodes, processApproveResult, currentNodeItem, processFormValues);
  }

  if (configItem.childNode && typeof configItem.childNode === 'object') {
    // 代表conditionNodes中的一项还有子节点childNode继续回调
    handleProcessConfigList(configItem.childNode, processApproveResult, currentNodeItem, processFormValues);
  }
};

/**	更新前面的所有节点值之后再执行刷新图标的方法，根据类型和审批状态更新图标
 * @param currentNode 当前审核的流程节点
 */
const handleUpdateIcon = (node: ProcessNode) => {
  if (node.type === 'start') {
    node.icon = 'approvestart';
  } else if (node.type === 'end') {
    node.icon = 'approveend';
  } else if (node.approve.length > 0) {
    const flag = node.approve.every((ap) => ap.approveResult === 1);
    const flagCustom = node.approve.every((ap) => ap.approveResult === -2);

    if (flag) {
      node.icon = 'approvefinish';
    } else if (flagCustom) {
      node.icon = 'approvewaiting';
    } else {
      node.icon = 'approverefuse';
    }
  } else {
    // 默认情况设置图标为灰色
    node.icon = 'approvewaiting';
  }
};

// 同意或者拒绝申请
const handleSubmitApprove = (num: number, type: 'approve' | 'adiut') => {
  const str = type === 'approve' ? '请输入审批意见' : '请输入办理意见';
  const title = type === 'approve' ? '审批意见' : '办理意见';

  ElMessageBox.prompt(title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: str
  })
    .then(({ value }) => {
      const data = {
        taskId: props.currentItem.taskId,
        approveReason: value,
        approveResult: num
      };
      submitApprove(data).then((res) => {
        if (res.code == 200) {
          ElMessage.success(res.msg);
          handleInstanceDetial(props.instanceId);
          emit('closeInstance', 'undo');
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      });
    });
};

// 撤回申请
const handleWithdraw = () => {
  ElMessageBox.confirm('确定撤回申请吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      withdrawProcess(props.currentItem.processInstanceId).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '撤销成功!'
          });
          emit('closeInstance', 'launch');
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 获取用户信息
const getUser = () => {
  getUserProfile().then((response) => {
    currentUserId.value = response.data.userId;
  });
};

// 跳转查看地块详情
const jumpDataDetail = () => {
  const routeUrl = router.resolve({
    path: `/map/autoProject@${moduleId.value}?parcelId=${parcelId.value}`
  });
  window.open(routeUrl.href, '_blank');
};

// Lifecycle hooks and watchers
onMounted(() => {
  getUser();
});

// 监听instanceId的变化
watch(
  () => props.instanceId,
  (val) => {
    if (val && val != '' && val != null) {
      resultNodeList.value = [];
      processConfigResult.value = {};
      processForm.value = [];
      processFormValue.value = {};
      handleInstanceDetial(val);
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.instance-main {
  height: 100%;
  width: 100%;
  position: relative;
  background-color: #ededed;
  overflow: hidden;
  .content {
    height: calc(100% - 60px);
    width: 100%;
    overflow: auto;
    .instance-item-header {
      background-color: #fff;
      position: relative;
      .title {
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        padding-left: 16px;
        width: calc(100% - 2px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .text {
        font-size: 12px;
        color: #f08706;
        position: absolute;
        bottom: 8px;
        right: 16px;
      }
      .icon {
        width: 100px;
        height: 100px;
        position: absolute;
        bottom: 0px;
        right: 12px;
        top: 0px;
        z-index: 999;
        .svg-icon {
          width: 100px;
          height: 100px;
        }
      }
    }
    .instance-item-form {
      margin-top: 12px;
      background-color: #fff;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      justify-items: center;
      padding-right: 12px;
      .form-title {
        color: #5e5e70;
        font-size: 14px;
        font-weight: 600;
        height: 30px;
        line-height: 30px;
        padding-left: 16px;
        margin-top: 8px;
      }
      .flex-item {
        display: flex;
        align-content: center;
        // align-items: center;
        font-size: 14px;
        padding-left: 16px;
        line-height: 30px;
        .title {
          color: #111112;
          font-weight: 600;
          width: auto;
          text-align: left;
          white-space: nowrap;
          height: 30px;
        }
        .text {
          color: #5e5e70;
          width: calc(100% - 70px);
          padding-left: 4px;
          margin-left: 8px;
        }
        .img-mian {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          .img-item {
            margin: 4px;
            border: 8px;
          }
        }
      }
    }
    .instance-item-line {
      margin-top: 12px;
      background-color: #fff;
      padding-right: 12px;
      .title {
        color: #5e5e70;
        font-size: 14px;
        font-weight: 600;
        height: 30px;
        line-height: 30px;
        padding-left: 16px;
        margin-top: 8px;
      }
      .el-steps {
        padding-left: 16px;
        padding-top: 12px;
        :deep(.el-step) {
          min-height: 70px;
          .el-step__icon.is-text {
            border-radius: 0%;
            border: none;
            .svg-icon {
              width: 24px;
              height: 24px;
            }
          }
          .el-step__description {
            padding-right: 0%;
            margin-top: -5px;
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
            .desc-main {
              width: 100%;
              .desc-title {
                color: #111112;
                font-size: 14px;
                font-weight: 600;
                height: 30px;
                line-height: 30px;
                display: flex;
              }
              .desc-content {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: 100%;
                height: auto;
                .desc-item {
                  width: 100%;
                  display: flex;
                  flex-direction: column;
                  .text {
                    color: #7f7f90;
                    font-size: 14px;
                    font-weight: 500;
                    height: 30px;
                    line-height: 30px;
                  }
                  .notify-text {
                    display: flex;
                    justify-content: flex-start;
                    width: 100%;
                    flex-direction: row;
                    flex-wrap: wrap;

                    .text-name {
                      min-width: 50px;
                      color: #7f7f90;
                      font-size: 14px;
                      font-weight: 500;
                      &:not(:last-child)::after {
                        content: '、';
                      }
                    }
                  }
                  .desc-reason {
                    display: flex;
                    flex-direction: row;
                    background-color: #f4f4f4;
                    width: 100%;
                    border-radius: 4px;
                    padding: 4px 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .instance-bottom {
    position: -webkit-sticky; /* Safari */
    position: sticky;
    bottom: 0px;
    left: 0px;
    height: 60px;
    width: 100%;
    border-top: 1px solid #ededed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
  }
}
</style>
