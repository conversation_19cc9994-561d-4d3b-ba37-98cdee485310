<!-- 邀请列表 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="组织名称">
        <el-input v-model="queryParams.orgName" placeholder="请输入组织名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="邀请时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-circle-check" size="small" :disabled="single" @click="handleCancleInvate(1)">同意</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-circle-close" size="small" :disabled="single" @click="handleCancleInvate(2)">拒绝</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getClientTableList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="clientList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="受邀人" align="center" prop="custName" />
      <el-table-column label="手机号码" align="center" prop="custPhone" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="邀请时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ formatDateTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button link @click="handleDealInvited(row, 1)" icon="el-icon-circle-check">同意</el-button>
          <el-button style="color: #ff3d57" link icon="el-icon-circle-close" @click="handleDealInvited(row, 2)">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getClientTableList"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getClientList, updateClient } from '@/api/client';
import type { FormInstance } from 'element-plus';

// 定义客户数据接口
interface ClientItem {
  id: string | number;
  companyId: string | number;
  companyName: string;
  custName: string;
  custPhone: string;
  remark: string;
  createTime: string;
  [key: string]: any;
}

interface QueryParams {
  orgName: string;
  dateRange: string[];
  pageSize: number;
  pageNum: number;
}

// 遮罩层
const loading = ref(true);
// 选中数组
const ids = ref<ClientItem[]>([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// table 的数组
const clientList = ref<ClientItem[]>([]);
// 邀请客户
const clientDialogFormVisible = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 当前编辑项的id
const currentId = ref(0);
// 表单引用
const queryFormRef = ref<FormInstance>();

// 查询参数
const queryParams = ref<QueryParams>({
  orgName: '',
  dateRange: [],
  pageSize: 10,
  pageNum: 1
});

// 计算总条数
const total = computed(() => {
  return clientList.value.length;
});

// 格式化日期时间
const formatDateTime = (time: string): string => {
  if (!time) return '';
  const date = new Date(time);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取受邀列表
const getClientTableList = async () => {
  const params = {
    direction: 1,
    status: 0,
    type: 1,
    companyName: queryParams.value.orgName
  };

  try {
    loading.value = true;
    const res = await getClientList(params);
    if (res.code === 200) {
      clientList.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取列表失败', error);
    ElMessage.error('获取列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理邀请
const handleDealInvited = (row: ClientItem, status: number) => {
  const str = status === 1 ? `是否确认同意【${row.companyName}】的邀请?` : `是否确认拒绝【${row.companyName}】的邀请?`;

  ElMessageBox.confirm(str, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const params = {
        id: row.id,
        companyId: row.companyId,
        custPhone: row.custPhone,
        status: status, //  取消-1 同意是1 拒绝2
        type: 1
      };

      try {
        const res = await updateClient(params);
        if (res.code === 200) {
          ElMessage.success(res.msg);
          getClientTableList();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('处理邀请失败', error);
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 多选框选中数据
const handleSelectionChange = (selection: ClientItem[]) => {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 批量邀请
const handleCancleInvate = (status: number) => {
  if (ids.value.length === 0) {
    ElMessage.warning('请至少选择一条记录');
    return;
  }

  const selectedItem = ids.value[0];
  const { id, companyId, custPhone, companyName } = selectedItem;

  const str = status === 1 ? `是否确认同意【${companyName}】的邀请?` : `是否确认拒绝【${companyName}】的邀请?`;

  ElMessageBox.confirm(str, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const params = {
        id,
        companyId,
        custPhone,
        status, // 取消-1 同意是1 拒绝2
        type: 1
      };

      try {
        const res = await updateClient(params);
        if (res.code === 200) {
          ElMessage.success(res.msg);
          getClientTableList();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (error) {
        console.error('批量处理邀请失败', error);
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      });
    });
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getClientTableList();
};

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value = {
    orgName: '',
    dateRange: [],
    pageSize: 10,
    pageNum: 1
  };
  handleQuery();
};

// 页面加载时获取列表数据
onMounted(() => {
  getClientTableList();
});
</script>

<style lang="scss" scoped></style>
