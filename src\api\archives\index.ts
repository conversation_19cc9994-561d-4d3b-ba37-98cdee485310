import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ArchivesData, ArchivesQuery } from '@/api/archives/types';

/**
 * 操作档案
 * @param data 档案操作数据
 * @returns {AxiosPromise}
 */
export function saveFilesOpera(data: ArchivesData): AxiosPromise<any> {
  return request({
    url: '/qjt/files/opera',
    method: 'post',
    data: data
  });
}

/**
 * 查询档案对应的列表
 * @param data 查询数据
 * @returns {AxiosPromise}
 */
export function getFilesList(data: ArchivesData): AxiosPromise<any> {
  return request({
    url: '/qjt/files/selectList',
    method: 'post',
    data: data
  });
}

/**
 * 查询档案详情 /qjt/files/selectId?id=10
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getFilesById(params: ArchivesQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/files/selectId',
    method: 'post',
    params: params
  });
}

/**
 * 上传文件
 * @param data 上传数据
 * @returns {AxiosPromise}
 */
export function uploadFile(data: FormData): AxiosPromise<any> {
  return request({
    url: '/qjt/file/multi/upload',
    method: 'post',
    data: data
  });
}

/**
 * 每个列表上面的查询 4个统计
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getselectMap(params: ArchivesQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/files/selectMap',
    method: 'post',
    params: params
  });
}

/**
 * 查询折线图的数据
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getselectZx(params: ArchivesQuery): AxiosPromise<any> {
  return request({
    url: '/qjt/files/selectZx',
    method: 'post',
    params: params
  });
}
