<!-- 数据筛选 -->
<template>
  <div class="dataSearch-main">
    <el-dialog
      title="数据筛选"
      v-model="localDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="874px"
      :before-close="handleClose"
    >
      <div v-dialogDrag class="dialog-content">
        <el-form ref="dialogSearch" :model="dialogSearchCopy" label-width="120px" height="100%">
          <el-row :gutter="20">
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="任务">
                <el-select v-model="dialogSearchCopy.taskId" placeholder="请选择" style="width: 100%" clearable filterable>
                  <el-option v-for="item in taskList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="采集人">
                <div class="close-ico" v-show="dialogSearchCopy.createUserId" @click="clearUser(1)">
                  <el-icon><CircleClose /></el-icon>
                </div>
                <el-input v-model="dialogSearchCopy.createUserName" readonly placeholder="请选择采集人" @click="showChooseUser(1)"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="数据名称">
                <el-input v-model="dialogSearchCopy.parcelName" placeholder="请输入数据名称" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="数据id">
                <el-tooltip class="item" effect="dark" content="改值可以输入多个id，用英文,分割，可以更快速的查询到数据!!!" placement="top-start">
                  <div style="position: absolute; top: 0px; left: -90px">
                    <el-icon><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 6 }"
                  placeholder="请输入id,多个用英文逗号,分割"
                  v-model="dialogSearchCopy.ids"
                  onkeyup="value=value.replace(/[^\d,]/g,'')"
                  @input="changeIds"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dialogSearchCopy.createDate"
                  type="daterange"
                  value-format="x"
                  range-separator="至"
                  start-placeholder="开始日期"
                  :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
                  end-placeholder="结束日期"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="最后修改时间">
                <el-date-picker
                  v-model="dialogSearchCopy.updateDate"
                  type="daterange"
                  value-format="x"
                  range-separator="至"
                  start-placeholder="开始日期"
                  :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
                  end-placeholder="结束日期"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20"> </el-row>

          <el-row :gutter="20">
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="最后修改人">
                <div class="close-ico" v-show="dialogSearchCopy.optUserId" @click="clearUser(2)">
                  <el-icon><CircleClose /></el-icon>
                </div>
                <el-input v-model="dialogSearchCopy.optUserName" readonly placeholder="请选择最后修改人" @click="showChooseUser(2)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="拓扑检查">
                <el-select v-model="dialogSearchCopy.checkes" multiple placeholder="请选择拓扑规则" style="width: 100%">
                  <el-option v-for="item in ruleCheckList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12" v-show="!isQuestion">
              <el-form-item label="宗地标识">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="该值是数据新增的时候默认生成的(如果该数据是5.0迁入6.0代表是5.0的宗地id)不允许修改，为了快速搜索"
                  placement="top-start"
                >
                  <div style="position: absolute; top: 0px; left: -90px">
                    <el-icon><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
                <el-input
                  v-model="dialogSearchCopy.parcelCode"
                  @input="changeParcelCode"
                  type="textarea"
                  placeholder="请输入匹配值,多个用英文逗号,分割"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" v-if="moduleRuleList.length != 0">
              <el-form-item label="选择节点树">
                <el-checkbox-group v-model="dialogSearchCopy.ruleIds">
                  <el-checkbox :value="item.id" v-for="(item, index) in moduleRuleList" :key="index">{{ item.typeName }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="导出未匹配数据">
                <el-radio-group v-model="isMapping">
                  <el-radio :value="1" :disabled="disableMapping">是</el-radio>
                  <el-radio :value="2" :disabled="disableMapping">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="表达式异常数据">
                <el-tooltip class="item" effect="dark" content="该选项是为了用户查询有表达式的模块表达式字段异常的数据！！！" placement="top-start">
                  <div style="position: absolute; top: 0px; left: 20px">
                    <el-icon><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
                <el-checkbox v-model="dialogSearchCopy.express"></el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="condition-item" v-for="(ite, idx) in dialogSearchCopy.conditionFields" :key="idx">
            <div class="item1" v-show="idx == 0">当</div>
            <div class="item1" v-show="idx != 0">
              <el-select v-model="ite.relation" placeholder="请选择">
                <el-option label="且" value="and"></el-option>
              </el-select>
            </div>
            <div class="item2">
              <chooseField
                :isQuestion="isQuestion"
                :ref="`conditionFiled${idx}`"
                :num="idx"
                :defaultField="ite.name"
                :treeList="treeList"
                @editField="editField"
              ></chooseField>
            </div>
            <div class="item3">
              <el-select v-model="ite.operator" placeholder="请选择">
                <el-option label="等于" value="="></el-option>
                <el-option label="模糊查询" value="like"></el-option>
              </el-select>
            </div>
            <div class="item4">
              <el-input
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 6 }"
                placeholder="请输入匹配值,多个用英文逗号,’分割"
                v-model="ite.value"
                @input="clearValidateIds(ite, idx)"
              >
              </el-input>
            </div>
            <div class="item5" @click="delCondition(idx)">
              <el-icon><Delete /></el-icon>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <div class="add-condition" @click="addCondition">
            <el-icon><Plus /></el-icon><el-link type="primary">新增筛选条件</el-link>
          </div>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="submitSearch">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 选择人员组件 -->
    <chooseUserForDept
      :chooseUserDialog="chooseUserDialog"
      :chooseUserId="chooseUserId"
      :chooseUserTitle="chooseUserTitle"
      @submitChooseUser="submitChooseUser"
      @closeChooseUser="closeChooseUser"
    ></chooseUserForDept>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { QuestionFilled, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
import chooseUserForDept from '@/components/chooseUserForDept/index.vue';
import areaCodeTemp from '@/components/areaCodeTemp/index.vue';
import chooseField from './chooseField.vue';
import { selectRules, examineList } from '@/api/modal';
import { useProjectStore } from '@/store/modules/project';
import { isArray } from '@/utils/validate';

interface Props {
  shaixuanDialog: boolean;
  dialogSearch: any;
  taskList: any[];
  isQuestion?: boolean;
  moduleId?: string;
  isShowSpe?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isQuestion: false,
  isShowSpe: false
});

const dialogSearchCopy = computed(() => props.dialogSearch);

const emit = defineEmits(['handleCloseShaixuan', 'clearUser', 'submitSearch', 'handleResetSearch', 'editCondition']);

const projectStore = useProjectStore();

// 状态定义
const chooseUserType = ref(1); //选择人员的类型 1采集人 2最后修改人
const chooseUserDialog = ref(false); //选择人员弹窗
const chooseUserId = ref(0); //当前需要反显的人员id
const chooseUserTitle = ref(''); //选择人员的title
const treeList = ref<any[]>([]); //树结构
const tableHeight = ref(window.innerHeight - 400);
const ruleCheckList = ref<any[]>([]); //拓扑检查列表
const moduleRuleList = ref<any[]>([]); //模块树
const isMapping = ref(2); //否
const areaCodeRef = ref();
const localDialog = ref(false);
const disableMapping = ref(true); //是否禁止选择 导出未匹配数据

// 监听 props 变化，同步更新本地数据
watch(
  () => props.shaixuanDialog,
  (newVal) => {
    localDialog.value = newVal;
    if (newVal) {
      isMapping.value = 2;
      if (isArray(dialogSearchCopy.value.ids)) {
        dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.join(',');
      }
      getTree();
    }
  },
  { immediate: true }
);

watch(
  () => dialogSearchCopy.value.conditionFields,

  (newVal: any) => {
    if (newVal.length == 0 && !dialogSearchCopy.value.ids) {
      disableMapping.value = true;
    } else {
      disableMapping.value = false;
    }
  },
  { deep: true }
);

watch(
  () => dialogSearchCopy.value.ids,
  (newVal: any) => {
    if (newVal && !Array.isArray(newVal)) {
      dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.replace(/[^0-9,]/g, '');
    }
    if (newVal || dialogSearchCopy.value.conditionFields.length != 0) {
      disableMapping.value = false;
    } else {
      disableMapping.value = true;
      isMapping.value = 2;
    }
  }
);

// 监听 props 变化，同步更新本地数据
// watch(
//   () => props.dialogSearch,
//   (newVal: any) => {
//     dialogSearchCopy.value = JSON.parse(JSON.stringify(newVal));
//   },
//   { deep: true }
// );

// 方法定义
const getTree = () => {
  let moduleId = projectStore.proModuleId;
  if (props.moduleId) {
    moduleId = Number(props.moduleId);
  }
  selectRules({ moduleId: moduleId }).then((res) => {
    if (res.code == 200) {
      moduleRuleList.value = res.data;
      treeList.value = [];
      ruleCheckList.value = [];
      examineList({ moduleId: moduleId }).then((resp) => {
        if (resp.code == 200) {
          resp.data.forEach((v: any) => {
            const item = {
              label: getLable(res.data, v),
              value: v.id
            };
            ruleCheckList.value.push(item);
          });
        } else {
          ElMessage.error(resp.msg);
        }
      });
      initTree(res.data);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const initTree = (list: any[]) => {
  list.forEach((v) => {
    v.label = v.typeName;
    v.value = '';
    v.children = v.fieldGroupModelList;
    treeList.value.push(v);
    if (v.children.length != 0) {
      initAttrGroup(v.children);
    }
    if (v.list.length > 0) {
      initTree(v.list);
    }
  });
};

const initAttrGroup = (list: any[]) => {
  list.forEach((v) => {
    v.label = v.typeName;
    if (v.ruleAttribution && (v.ruleAttribution.type == 'graphicalPoint' || v.ruleAttribution.type == 'commonPoint')) {
      v.label = `${v.typeName}(点)`;
    } else if (v.ruleAttribution && (v.ruleAttribution.type == 'graphicalLine' || v.ruleAttribution.type == 'commonLine')) {
      v.label = `${v.typeName}(线)`;
    }
    v.value = v.linkId;
    v.children = v.fieldModelList;
    if (v.children.length != 0) {
      initField(v.children);
    }
  });
};

const initField = (list: any[]) => {
  list.forEach((v) => {
    v.label = v.fieldCn;
    v.value = JSON.stringify(v);
    if (v.valueMethod == 'idCardScan') {
      const children = [
        { label: '姓名', value: 0 },
        { label: '性别', value: 1 },
        { label: '民族', value: 2 },
        { label: '出生日期', value: 3 },
        { label: '住址', value: 4 },
        { label: '身份证号码', value: 5 },
        { label: '签发机关', value: 6 },
        { label: '有效期限', value: 7 },
        { label: '身份证正面', value: 8 },
        { label: '身份证反面', value: 9 }
      ];
      v.children = children;
    } else if (v.valueMethod == 'xtdwsb' || v.valueMethod == 'xtzwsb') {
      const children = [
        { label: '名称', value: 0 },
        { label: '图片', value: 1 },
        { label: '描述', value: 3 }
      ];
      v.children = children;
    } else if (v.valueMethod == 'xttable') {
      const children = v.attribution.children;
      children.forEach((k: any) => {
        k.label = k.fieldCn;
        k.value = JSON.stringify(k);
      });
      v.children = children;
    }
  });
};

const handleClose = () => {
  emit('handleCloseShaixuan');
};

const clearUser = (type: any) => {
  emit('clearUser', type);
};

const showChooseUser = (type: any) => {
  chooseUserType.value = type;
  if (type == 1) {
    chooseUserTitle.value = '采集人选择';
    chooseUserId.value = props.dialogSearch.createUserId;
  } else {
    chooseUserTitle.value = '最后修改人选择';
    chooseUserId.value = props.dialogSearch.optUserId;
  }
  chooseUserDialog.value = true;
};

const submitChooseUser = (obj: any) => {
  if (chooseUserType.value == 1) {
    dialogSearchCopy.value.createUserId = obj.userId;
    dialogSearchCopy.value.createUserName = obj.custName;
  } else if (chooseUserType.value == 2) {
    dialogSearchCopy.value.optUserId = obj.userId;
    dialogSearchCopy.value.optUserName = obj.custName;
  }
  chooseUserDialog.value = false;
};

const closeChooseUser = () => {
  chooseUserDialog.value = false;
};

const changeCityCode = (code: any) => {
  if (localDialog.value) {
    dialogSearchCopy.value.areaCode = code;
  } else {
    if (code) {
      dialogSearchCopy.value.areaCode = code;
    } else {
      dialogSearchCopy.value.areaCode = '';
    }
  }
};

const submitSearch = () => {
  if (dialogSearchCopy.value.ids && !Array.isArray(dialogSearchCopy.value.ids)) {
    // 去掉不合法字符，只保留数字和英文逗号
    // dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.replace(/[^0-9,]/g, '');
    // 去掉最后一个逗号
    if (dialogSearchCopy.value.ids.endsWith(',')) {
      dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.slice(0, -1);
    }
    dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.split(',');
    // 去掉空字符串的数据
    dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.filter((item) => item !== '');
  } else {
    dialogSearchCopy.value.ids = null;
  }
  if (dialogSearchCopy.value.parcelCode) {
    let parcelCodeStr = String(dialogSearchCopy.value.parcelCode); // 强制转换为字符串
    if (parcelCodeStr.endsWith(',')) {
      parcelCodeStr = parcelCodeStr.slice(0, -1);
    }
    dialogSearchCopy.value.parcelCode = parcelCodeStr;
  } else {
    dialogSearchCopy.value.parcelCode = null;
  }
  let flg = true;
  if (dialogSearchCopy.value.ids && dialogSearchCopy.value.ids.length > 0) {
    for (let i = 0; i < dialogSearchCopy.value.ids.length; i++) {
      if (!dialogSearchCopy.value.ids[i]) {
        flg = false;
        break;
      } else if (dialogSearchCopy.value.ids[i].length > 18) {
        //id长度超过18位
        flg = false;
        break;
      }
    }
  }
  if (!flg) {
    dialogSearchCopy.value.ids = dialogSearchCopy.value.ids.join(',');
    ElMessageBox.alert('您输入的数据id有问题，请检查！！！', '错误提示', {
      confirmButtonText: '确定',
      callback: (action) => {}
    });
    return;
  }
  let errorInput = 0;
  if (dialogSearchCopy.value.conditionFields) {
    dialogSearchCopy.value.conditionFields.forEach((v: any, idx: any) => {
      if (v.value) {
        v.value = (v.value + '').split(',');
        if (!v.value || v.value.length == 0) {
          errorInput++;
        }
        if (!v.name) {
          errorInput++;
        }
      } else {
        errorInput++;
      }
    });
  }
  if (errorInput > 0) {
    ElMessage.error('您有筛选条件未填写内容');
    return;
  }
  emit('submitSearch', isMapping.value);
};

const changeCodeGetName = (name: any) => {
  dialogSearchCopy.value.areaCodeName = name;
};

const reset = () => {
  // areaCodeRef.value.clearAreaCode();
  emit('handleResetSearch');
  //执行确定
  submitSearch();
};

const addCondition = () => {
  emit('editCondition', 1);
  // 等待 DOM 更新后执行滚动
  nextTick(() => {
    const formEl = document.querySelector('.el-form');
    if (formEl) {
      formEl.scrollTop = formEl.scrollHeight;
    }
  });
};

const delCondition = (idx: any) => {
  emit('editCondition', 2, idx);
  // 如果条件和ids都不存在，需要初始化是否导出未匹配数据
  if (dialogSearchCopy.value.conditionFields.length == 0 && !dialogSearchCopy.value.ids) {
    isMapping.value = 2;
  }
};

const editField = (item: any, num: any, idx: any, linkId: any) => {
  dialogSearchCopy.value.conditionFields[num].name = JSON.parse(item).fieldName;
  dialogSearchCopy.value.conditionFields[num].index = idx;
  dialogSearchCopy.value.conditionFields[num].linkId = linkId;
};

const clearValidateIds = (obj: any, idx: any) => {
  const item = obj.value.replace(/\s|\n/g, '');
  dialogSearchCopy.value.conditionFields[idx].value = item;
};

const changeParcelCode = (val: any) => {
  const item = val.replace(/\s|\n/g, '');
  dialogSearchCopy.value.parcelCode = item || '';
};

const getYSName = (tree: any[], id: any): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id == id) {
      return tree[i].typeName;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getYSName(tree[i].list, id);
      if (result) {
        return result;
      }
    }
  }
  return undefined;
};

const getLable = (tree: any[], item: any): string => {
  let label = '';
  if (item.sourceType == 1) {
    label = `【${getYSName(tree, item.sourceRuleId) || '没有找到节点或者节点已经被删除'}】`;
    if (!item.targetRuleId && !item.attribution) {
      label = label + '与外部同级节点';
    }
    if (item.attribution && item.attribution.GeometryGroup) {
      label = label + '的' + item.attribution.GeometryGroup.fieldGroupModelList.fieldModelList[0].fieldCn + '字段值相同的兄弟节点';
    }
    if (item.checkType == 1) {
      label = label + '不相交';
    } else if (item.checkType == 2) {
      label = label + '相交';
    } else if (item.checkType == 3) {
      label = label + '相邻';
    } else if (item.checkType == 4) {
      label = label + '不包含';
    } else if (item.checkType == 5) {
      label = label + '包含';
    }
    if (!item.attribution || (item.attribution && !item.attribution.GeometryGroup)) {
      if (item.targetRuleId && item.attribution && item.attribution.direct) {
        label = label + '直属';
      }
      if (item.targetRuleId && (!item.attribution || (item.attribution && !item.attribution.direct))) {
        label = label + '所有';
      }
    }
    if (item.targetRuleId) {
      label = label + `【${getYSName(tree, item.targetRuleId) || '没有找到节点或者节点已经被删除'}】`;
    }
    if (item.insideFlag == 1) {
      label = label + '的【内部检查】规则';
    } else {
      label = label + '的【外部检查】规则';
    }
  } else {
    label = `当【${getYSName(tree, item.sourceRuleId) || '没有找到节点或者节点已经被删除'}】的【${getAttrName(tree, item.sourceRuleId, item.attribution.attrGroup)}】`;
    if (item.attribution.condition == 1) {
      label = label + '等于';
    } else {
      label = label + '不等于';
    }
    label = label + `【${item.attribution.value}】时【${getJdName(tree, item.sourceRuleId, item.attribution.targetIds)}】节点不用创建`;
  }
  return label;
};

const getAttrName = (tree: any[], ruleId: any, attrGroup: any): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id == ruleId) {
      let group, fieldCn;
      for (let j = 0; j < tree[i].fieldGroupModelList.length; j++) {
        if (tree[i].fieldGroupModelList[j].linkId == attrGroup[0]) {
          group = tree[i].fieldGroupModelList[j];
          break;
        }
      }
      if (group) {
        for (let k = 0; k < group.fieldModelList.length; k++) {
          if (group.fieldModelList[k].fieldName == attrGroup[1]) {
            fieldCn = group.fieldModelList[k].fieldCn;
            break;
          }
        }
      }
      return `【${group.typeName}】属性组的【${fieldCn}】字段`;
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getAttrName(tree[i].list, ruleId, attrGroup);
      if (result) {
        return result;
      }
    }
  }
  return undefined;
};

const getJdName = (tree: any[], ruleId: any, ids: any[]): string | undefined => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id == ruleId) {
      const names: string[] = [];
      tree[i].list.forEach((v: any) => {
        if (ids.includes(v.id)) {
          names.push(v.typeName);
        }
      });
      return names.join(',');
    }
    if (tree[i].list && tree[i].list.length > 0) {
      const result = getJdName(tree[i].list, ruleId, ids);
      if (result) {
        return result;
      }
    }
  }
  return undefined;
};
</script>

<style lang="scss" scoped>
.close-ico {
  position: absolute;
  right: 10px;
  top: 0px;
  z-index: 1;
  cursor: pointer;
  font-size: 16px;
}
.add-condition {
  cursor: pointer;
  color: var(--current-color);
  display: flex;
  align-items: center;
  position: absolute;
  top: -16px;
  left: 14px;
}
.condition-item {
  height: 40px;
  display: flex;
  align-items: center;
  .item1 {
    width: 110px;
    margin-right: 10px;
    text-align: right;
  }
  .item2 {
    flex: 2;
    margin-right: 10px;
  }
  .item3 {
    flex: 1;
    margin-right: 10px;
  }
  .item4 {
    flex: 3;
    margin-right: 10px;
  }
  .item5 {
    width: 20px;
  }
  .item5:hover {
    color: #f56c6c;
    cursor: pointer;
  }
}
.dialog-footer {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.dialog-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.dataSearch-main {
  .close-ico {
    position: absolute;
    z-index: 1;
    right: 30px;
    top: 10px;
  }
}
</style>
