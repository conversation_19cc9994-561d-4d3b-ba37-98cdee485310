<!-- 基础设置 -->
<template>
  <div class="base-setting-main">
    <el-form ref="modalFormRef" :model="modalForm" :rules="modalFormRules" :label-position="`top`">
      <el-row>
        <el-col :span="24">
          <el-form-item label="模块图标" prop="icon" class="item-error">
            <div class="modal-icon-main">
              <div v-if="modalForm.icon">
                <div v-if="modalForm.icon && modalForm.icon.substring(modalForm.icon.lastIndexOf('_') + 1) === 'blob'">
                  <custom-img
                    :authSrc="`${baseUrl}${modalForm.icon}?att=1`"
                    :width="'72px'"
                    :height="'72px'"
                    :radios="'4px'"
                    style="margin: 0"
                    @editIcon="handleOpenSettingIcon"
                    :isEdit="isEdit"
                  />
                </div>
                <div v-else>
                  <svg-icon class-name="svg-item" :icon-class="modalForm.icon" />
                  <div class="edit" @click="handleOpenSettingIcon">
                    <span class="edit-text">修改</span>
                  </div>
                </div>
              </div>
              <div class="modal-icon-contianer" @click="handleOpenSettingIcon" v-else>
                <el-icon><Plus /></el-icon>
                <div class="text">点击设置</div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模块名称" prop="name">
            <el-input v-model="modalForm.name" maxlength="50" placeholder="请输入模块名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="面积计算方式" prop="areaType">
            <el-select v-model="modalForm.areaType" placeholder="请选择面积计算方式" style="width: 100%">
              <el-option label="投影坐标系" :value="1"></el-option>
              <el-option label="大地坐标系" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="modal-desc">
          <el-form-item label="模块说明" prop="desc">
            <el-input type="textarea" v-model="modalForm.desc" placeholder="请输入模块说明" autosize maxlength="1000" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="modal-desc">
          <el-form-item label="app权限设置">
            <el-select v-model="modalForm.roleIds" placeholder="请选择" clearable collapse-tags multiple style="width: 100%">
              <el-option v-for="item in roleList" :key="item.roleId" :label="item.roleName" :value="item.roleId"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="modal-desc" v-show="isKJUrl">
          <el-form-item label="勘界导出url（不用首尾位置加/）">
            <el-input v-model="kjExportUrl" placeholder="请输入导出url" @input="kjExportUrl = $event.replace(/[^a-zA-Z0-9\/]/g, '')"></el-input>
            <!-- <el-select v-model="modalForm.roleIds" placeholder="请选择" clearable collapse-tags multiple style="width: 100%">
              <el-option v-for="item in roleList" :key="item.roleId" :label="item.roleName" :value="item.roleId"> </el-option>
            </el-select> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-next-step">
      <el-button type="primary" @click="handleValidateModalForm" size="small">
        下一步<el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <!-- 设置图标的dialog -->
    <setting-icon
      :iconVisible="iconVisible"
      :defaultList="modalIconList"
      :iconSelected="modalForm.icon"
      :isShow="isShowIcon"
      :iconName="iconName"
      @closeSettingIcon="handleCloseSettingIcon"
      @submitIcon="handleSubmitSettingIcon"
    ></setting-icon>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import authImg from '@/components/authImg/index.vue';
import settingIcon from '../../SettingIcon/modalIndex.vue';
import CustomImg from '../../SettingIcon/customImg.vue';
import { selectRules, addModule, modifyModule, uploadCopperImg } from '@/api/modal/index';
import modalIconList from '../../SettingIcon/modalIcon.json';
import { getRoleList } from '@/api/system/user/index';
import { ArrowRight } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const userStore = useUserStore();
const router = useRouter();
const modalStore = useModalStore();

// 定义 props
const props = defineProps<{
  type?: number;
}>();

// 回显图片的路径
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API + '/qjt/file/downloadone/');

// 定义表单数据
const modalForm = reactive<{
  name: string | undefined;
  icon: string | undefined;
  desc: string | undefined;
  areaType: number;
  roleIds: number[];
}>({
  name: undefined,
  icon: undefined,
  desc: undefined,
  areaType: 1,
  roleIds: []
});

// 定义表单校验规则
const modalFormRules = {
  name: [
    { required: true, message: '模块名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: ['blur', 'change'] }
  ],
  icon: [{ required: true, message: '模块图标不能为空', trigger: 'blur' }],
  desc: [
    { required: true, message: '模块说明不能为空', trigger: 'blur' },
    { min: 2, max: 1000, message: '长度在 2 到 1000 个字符', trigger: ['blur', 'change'] }
  ],
  // 原代码此处可能有误，推测应该是 areaType 而不是 modalForm
  areaType: [{ required: true, message: '请选择模块面积计算方式', trigger: 'change' }]
};

// 定义其他响应式数据
const iconVisible = ref(false);
const isEdit = ref(true);
const isShow = ref(false);
const isShowIcon = ref(false);
const iconName = ref('模块自定义照片');
const roleList = ref<{ roleId: number; roleName: string }[]>([]);
const isKJUrl = ref(false); //是否是勘界模块
const kjExportUrl = ref(''); //勘界导出url

// 定义表单引用
const modalFormRef = ref<FormInstance>();

// 计算属性获取 moduleId
const moduleId = computed(() => modalStore.moduleId);

// 监听 modalForm 的变化
watch(
  () => modalForm,
  (val) => {
    if (val.icon !== '' && val.icon !== null) {
      if (modalFormRef.value) {
        modalFormRef.value.validateField('icon');
      }
    }
  },
  { deep: true }
);

// 获取角色列表
const getRolesList = async () => {
  try {
    const res = await getRoleList();
    if (res.code === 200) {
      roleList.value = res.rows;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('获取角色列表失败', error);
  }
};

// 打开设置图标的 dialog
const handleOpenSettingIcon = () => {
  iconVisible.value = true;
};

// 关闭设置图标的 dialog 弹框
const handleCloseSettingIcon = () => {
  iconVisible.value = false;
};

// 确定选中的图标，子组件中的传值
const handleSubmitSettingIcon = (url: string) => {
  modalForm.icon = url;
};

// 表单校验必填项
const handleValidateModalForm = () => {
  if (modalFormRef.value) {
    modalFormRef.value.validate(async (valid) => {
      if (valid) {
        if (moduleId.value === 0) {
          const data = {
            moduleName: modalForm.name,
            iconUrl: modalForm.icon,
            remark: modalForm.desc,
            areaType: modalForm.areaType,
            status: 0,
            levelNum: 1,
            type: props.type,
            attribution: {
              roleIds: modalForm.roleIds
            }
          };
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            data.companyId = companyId;
          }
          try {
            const res = await addModule(data);
            if (res.code === 200) {
              modalStore.setModuleId(res.data.id);
              emit('nextStep', 2);
            } else {
              ElMessage.error(res.msg);
            }
          } catch (error) {
            console.error('新增模块失败', error);
          }
        } else {
          const data = {
            moduleName: modalForm.name,
            iconUrl: modalForm.icon,
            remark: modalForm.desc,
            areaType: modalForm.areaType,
            id: moduleId.value,
            // status: modalForm.status,
            delFlag: modalForm.delFlag,
            list: modalForm.list || [],
            createUserId: modalForm.createUserId,
            createTime: modalForm.createTime,
            type: props.type,
            attribution: {
              roleIds: modalForm.roleIds
            }
          };
          if (kjExportUrl.value) {
            data.url = kjExportUrl.value;
          }
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            data.companyId = companyId;
          }
          try {
            const res = await modifyModule(data);
            if (res.code === 200) {
              emit('nextStep', 2);
            } else {
              ElMessage.error(res.msg);
            }
          } catch (error) {
            console.error('修改模块失败', error);
          }
        }
      } else {
        return false;
      }
    });
  }
};

// 返回的表单校验
const handleValidateBack = async () => {
  let isValidFlag = false;
  if (modalFormRef.value) {
    await modalFormRef.value.validate((valid) => {
      if (valid) {
        isValidFlag = valid;
      } else {
        modalFormRef.value?.resetFields();
        isValidFlag = false;
      }
    });
  }
  return isValidFlag;
};

onMounted(() => {
  const id = router.currentRoute.value.query.id as string | undefined;
  const flag = router.currentRoute.value.query && router.currentRoute.value.query.roleIds;
  let roleIds: number[] = [];
  if (flag) {
    if (Array.isArray(router.currentRoute.value.query.roleIds)) {
      roleIds = router.currentRoute.value.query.roleIds.map((item) => parseInt(item as string));
    } else {
      roleIds = [parseInt(router.currentRoute.value.query.roleIds as string)];
    }
  }
  if (id) {
    const item = {
      icon: router.currentRoute.value.query.icon as string | undefined,
      name: router.currentRoute.value.query.name as string | undefined,
      desc: router.currentRoute.value.query.desc as string | undefined,
      status: router.currentRoute.value.query.status as number | undefined,
      delFlag: router.currentRoute.value.query.delFlag as number | undefined,
      list: router.currentRoute.value.query.list as any | undefined,
      createUserId: router.currentRoute.value.query.createUserId as number | undefined,
      createTime: router.currentRoute.value.query.createTime as string | undefined,
      areaType: parseInt(router.currentRoute.value.query.areaType as string),
      roleIds
    };
    Object.assign(modalForm, item);
    modalStore.setModuleId(Number(id));
  }
  getRolesList();
});

// 定义 emits
const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();

/**
 * 勘界特殊配置导出url
 * @param url:勘界导出url
 */
const initKjSetting = (url: string) => {
  isKJUrl.value = true;
  kjExportUrl.value = url || '';
};

defineExpose({
  handleValidateBack,
  initKjSetting
});
</script>

<style lang="scss" scoped>
.base-setting-main {
  // display: flex;
  // flex-direction: column;
  position: relative;
  .el-form {
    margin: 24px;
    .modal-icon-main {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      // margin-top: 40px;
      position: relative;
      margin-bottom: -12px;
      .svg-item {
        width: 72px;
        height: 72px;
      }
      .edit {
        width: 44px;
        height: 24px;
        background: #edf4fb;
        border-radius: 4px 4px 4px 4px;
        text-align: center;
        line-height: 24px;
        cursor: pointer;
        margin-bottom: 10px;
        position: absolute;
        left: 80px;
        bottom: 10px;
        .edit-text {
          height: 20px;
          font-size: 14px;
          // font-family: PingFang SC-Regular, PingFang SC;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
          font-weight: 400;
          color: var(--current-color);
          line-height: 20px;
        }
      }

      .modal-icon-contianer {
        margin-right: 8px;
        width: 72px;
        height: 72px;
        line-height: 20px;
        opacity: 0.25;
        border-radius: 6px;
        background-color: #edf4fb;
        font-size: 14px;
        text-align: center;
        color: #161d26;
        font-family:
          Helvetica Neue,
          Helvetica,
          PingFang SC,
          Hiragino Sans GB,
          Microsoft YaHei,
          Arial,
          sans-serif;
        border: 1px solid #8291a9;
        padding-top: 16px;
        i {
          width: 20px;
          height: 20px;
          color: #8291a9;
          font-size: 20px;
        }
        .text {
          color: #8291a9;
          font-size: 12px;
          font-weight: 600;
          font-family:
            Helvetica Neue,
            Helvetica,
            PingFang SC,
            Hiragino Sans GB,
            Microsoft YaHei,
            Arial,
            sans-serif;
        }
      }
    }
    .modal-desc {
      // margin-top: 80px;
      .el-textarea {
        min-height: 60px !important;
        :deep(&) {
          .el-textarea__inner {
            width: 100%;
            min-height: 60px !important;
          }
        }
      }
    }
  }
  .btn-next-step {
    // position: absolute;
    // right: 16px;
    // // bottom: 32px;
    // top: calc(100vh - 300px);
    display: flex;
    justify-content: flex-end;
    margin-right: 20px;
    padding-bottom: 20px;
  }
}
.item-error {
  .modal-icon-contianer {
    border: 1px solid #eb0f0f;
  }
  :deep(&) {
    .el-form-item__error {
      // margin-top: 75px;
    }
  }
}
:deep(.el-form-item__label) {
  color: #161d26;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  font-size: 14px;
}
</style>
