<template>
  <div class="main">
    <el-card class="card">
      <div class="page-header">
        <div class="flex-left">
          <el-button size="small" @click="handleGoBack">
            <el-icon><ArrowLeft /></el-icon>返回
          </el-button>
        </div>
        <div class="flex-center">
          <div
            v-for="(item, index) in steps"
            :key="index"
            class="center-item"
            :class="[activeStep === item.key ? 'active' : '']"
            @click="changeSteps(item)"
          >
            <span class="step-index">{{ index + 1 }}</span>
            {{ item.label }}
          </div>
        </div>
        <div class="flex-right">
          <el-button type="primary" size="small" @click="handlePublish">保存</el-button>
        </div>
      </div>
      <section class="page-content" v-if="mockData">
        <BasicSetting
          ref="basicSetting"
          :conf="mockData.basicSetting"
          v-show="activeStep === 'basicSetting'"
          tabName="basicSetting"
          @initiatorChange="onInitiatorChange"
        />
        <DynamicFormBmp ref="formDesign" :conf="mockData.formData" v-show="activeStep === 'formDesign'" tabName="formDesign"></DynamicFormBmp>
        <Process
          ref="processDesign"
          :conf="mockData.processData"
          :isOnlineForm="isOnlineForm"
          tabName="processDesign"
          v-show="activeStep === 'processDesign'"
          @startNodeChange="onStartChange"
        />
      </section>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, Component, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import Process from './Process/index.vue';
import DynamicFormBmp from './DynamicFormBmp/index.vue';
import BasicSetting from './BasicSetting/index.vue';
import { addForm, getFormDetial, addProcess, getPdefinitionDetial, getDpts, modifyProcess } from '@/api/process';
import mockDataImport from './mockData';
import {
  inputComponents,
  selectComponents,
  layoutComponents,
  customMadeComponents,
  commonComponents,
  formConf
} from './DynamicFormBmp/components/generator/config';

// Define types
interface Step {
  label: string;
  key: string;
}

interface FormData {
  [key: string]: any;
}

interface ProcessConfig {
  type?: string;
  content?: string;
  properties?: any;
  nodeId?: string;
  icon?: string;
  time?: string;
  childNode?: ProcessConfig;
  [key: string]: any;
}

// Router and route
const router = useRouter();
const route = useRoute();

// Refs for child components
const basicSetting = ref<InstanceType<typeof BasicSetting> | null>(null);
const formDesign = ref<InstanceType<typeof DynamicFormBmp> | null>(null);
const processDesign = ref<InstanceType<typeof Process> | null>(null);

// Component state
const id = ref(route.query.id);
const title = ref('空白模板');
const procdefData = ref(null);
const mockData = ref(mockDataImport);
const activeStep = ref('basicSetting'); // 激活的步骤面板
const isOnlineForm = ref(false);

// Component data
const steps = reactive<Step[]>([
  { label: '基础设置', key: 'basicSetting' },
  { label: '表单设计', key: 'formDesign' },
  { label: '流程设计', key: 'processDesign' }
  // { label: '高级设置', key: 'advancedSetting' }
]);

// Methods
// 切换步骤
const changeSteps = (item: Step) => {
  activeStep.value = item.key;
};

// 获取流程详情
const handleProcessDetial = () => {
  const currentId = route.query.id;
  getPdefinitionDetial({ id: currentId }).then((res) => {
    if (res.code === 200 && res.data) {
      // 回显第一部的数据
      mockData.value.basicSetting = {
        flowName: res.data.processName,
        flowGroup: res.data.processGroupId,
        flowImg: res.data.iconUrl,
        flowRemark: res.data.processRemark || ''
      };
      // 回显第三步骤的数据
      //  这里因为手动添加了结束end节点，导致回显的时候有误，所以应该在回显的时候删除end节点
      mockData.value.processData = handleDeleteEndNode(JSON.parse(JSON.stringify(res.data.processConfig)));

      // 回显第二步的数据
      if (res.data.processForm && res.data.processForm.length > 0) {
        handleFormDesignData(res.data.processForm);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 处理删除最后一个节点的内容
const handleDeleteEndNode = (data: any): any => {
  // 检查是否是对象（数组或标准对象）
  if (typeof data === 'object' && data !== null) {
    // 如果当前节点的 type 是 end，则直接返回 undefined 表示要移除此节点
    if (data.type === 'end') {
      return undefined;
    }
    // 使用 Object.keys 来迭代对象的键
    Object.keys(data).forEach((key) => {
      // 如果当前属性值是对象，递归调用 removeEndNodes
      if (typeof data[key] === 'object' && data[key] !== null) {
        data[key] = handleDeleteEndNode(data[key]);
      }
    });
  }
  // 返回处理后的数据
  return data;
};

// 拼凑第二步骤需要的反显数据
const handleFormDesignData = (list: any[]) => {
  const resultList: any[] = [];
  const matchedCommon = new Map();
  //去字段中的所有数据作为映射值
  commonComponents.forEach((match) => {
    matchedCommon.set(match.icon, match);
  });
  list.forEach((item) => {
    // 这里创建一个obj是为了防止下方写入修改值
    const obj: any = {};
    if (matchedCommon.has(item.tag)) {
      const config = matchedCommon.get(item.tag);
      // 将一个对象拷贝到另一个对象中
      Object.assign(obj, config);
      obj.label = item.fieldCn;
      obj.vModel = item.fieldName;
      obj.required = item.required;
      obj.placeholder = item.placeholder;
      obj.layout = 'colFormItem';
      obj.formId = item.fieldSort;
      obj.defaults = item.defaultValue;
      obj.maxlength = item.maxlength;
      //  单选多选都需要重新赋值
      if (['select', 'radio', 'checkbox'].includes(item.tag)) {
        obj.options = item.options;
      } else if (item.tag === 'number') {
        obj.precision = item.precision;
      }
    }
    resultList.push(obj);
  });
  mockData.value.formData = {
    fields: resultList
  };
};

// 刷新表单类型
const refresheFormType = () => {
  const basicSettingKey = 'basicSetting';
  if (!mockData.value || !basicSetting.value) {
    isOnlineForm.value = false;
  } else {
    basicSetting.value
      .getDataNoValidate()
      .then((res: any) => {
        const formData = res.formData;
        isOnlineForm.value = formData.formType === 0;
      })
      .catch((e: any) => {
        console.error('获取表单类型失败:', e);
      });
  }
};

// 循环遍历判断是否有childNode存在于当前字段中，如果没有则手动添加一个字段，如果有则继续遍历
// 因为条件分支中创建的是conditionNodes中，所以不用担心条件分支的中childNode中的节点，值判断外城的节点内容
const handleFindEndNode = (currentNode: ProcessConfig): ProcessConfig => {
  // 如果有 childNode 属性，则添加到当前节点
  if ('childNode' in currentNode && typeof currentNode.childNode === 'object') {
    // configItem.childNode = currentNode.childNode
    handleFindEndNode(currentNode.childNode);
  } else {
    currentNode.childNode = createEndNode();
  }
  return currentNode;
};

// 创建最后一个节点
const createEndNode = (): ProcessConfig => {
  return {
    type: 'end',
    content: '结束',
    properties: { title: '结束' },
    nodeId: 'END',
    icon: 'applyEnd',
    time: ''
  };
};

// 提交表单数据的时候处理第二步后端需要的参数内容
const handleSecondForm = (formItem: any) => {
  const formFiledList: any[] = [];
  formItem.fields.forEach((item: any, index: number) => {
    const obj = {
      fieldName: item.vModel,
      fieldCn: item.label,
      fieldType: item.fieldType,
      required: item.required,
      tag: item.icon,
      placeholder: item.placeholder,
      options: item.options,
      precision: item.precision,
      fieldSort: index + 1,
      layout: item.layout,
      defaults: item.defaultValue,
      maxlength: item.maxlength
    };
    formFiledList.push(obj);
  });
  return formFiledList;
};
const getCmpData = (name: string) => {
  switch (name) {
    case 'basicSetting':
      return basicSetting.value?.getData();
    case 'formDesign':
      return formDesign.value?.getData();
    case 'processDesign':
      return processDesign.value?.getData();
    default:
      return Promise.reject(new Error('Unknown component'));
  }
};

// 保存/发布
const handlePublish = () => {
  const p0 = getCmpData('basicSetting');
  const p1 = getCmpData('formDesign');
  const p2 = getCmpData('processDesign');
  if (!p0 || !p1 || !p2) {
    ElMessage.warning('请检查流程中是否有必填项未填写');
    return;
  }
  Promise.all([p0, p1, p2])
    .then((resPromise) => {
      const resPromise2 = JSON.parse(JSON.stringify(resPromise[2].formData));
      const processConfig = handleFindEndNode(resPromise2);
      const data: any = {
        processName: resPromise[0].formData.flowName,
        processGroupId: resPromise[0].formData.flowGroup,
        iconUrl: resPromise[0].formData.flowImg,
        // processForm: processForm,
        processConfig: processConfig
      };

      if (JSON.stringify(resPromise[1].formData) !== '{}') {
        data.processForm = handleSecondForm(resPromise[1].formData);
      }

      const currentId = route.query.id as string;
      if (currentId && currentId != '0') {
        data.processId = currentId;
        modifyProcess(data).then((res) => {
          if (res.code == 200) {
            ElMessage.success('修改成功');
            router.push('/bmp/processAudit');
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        addProcess(data).then((res) => {
          if (res.code == 200) {
            ElMessage.success('添加成功');
            router.push('/bmp/processAudit');
          } else {
            ElMessage.error(res.msg);
          }
        });
      }
    })
    .catch((err) => {
      if (err.target) {
        activeStep.value = err.target;
      }
      if (err.msg) {
        ElMessage.warning(err.msg);
      }
    });
};

// 返回
const handleGoBack = () => {
  ElMessageBox.confirm('离开此页面您得修改将会丢失, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      router.push('/bmp/processAudit');
      activeStep.value = 'basicSetting';
      id.value = undefined;
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    });
};

// 同步基础设置发起人和流程节点发起人
const onInitiatorChange = (val: any, labels: string) => {
  const processCmp = processDesign.value;
  if (!processCmp) return;

  const startNode = processCmp.data;
  startNode.properties.initiator = val;
  startNode.content = labels || '所有人';
  processCmp.forceUpdate();
};

// 监听流程节点发起人改变并同步到基础设置发起人数据
const onStartChange = (node: any) => {
  const basicSettingCmp = basicSetting.value;
  if (!basicSettingCmp) return;

  basicSettingCmp.formData.initiator = node.properties.initiator;
};

// 初始化和生命周期方法
onMounted(() => {
  localStorage.removeItem('drawingItems');
  id.value = route.query.id;

  if (id.value && id.value !== '0') {
    refresheFormType();
    handleProcessDetial();
  } else {
    mockData.value = {};
  }
});
// 监听路由参数变化
watch(
  () => route.query.id,
  (newId) => {
    id.value = newId;
    if (newId && newId !== '0') {
      handleProcessDetial();
    } else {
      // 重置所有子组件的表单
      basicSetting.value?.resetForm();
      formDesign.value?.resetForm();
      processDesign.value?.resetForm();
      // 重置 mockData
      mockData.value = {
        basicSetting: {
          flowName: '',
          flowImg: '',
          flowGroup: '',
          flowRemark: ''
        },
        processData: {
          type: 'start',
          content: '所有人',
          properties: {
            title: '发起人',
            initiator: 'all'
          },
          nodeId: 'START',
          icon: 'applyStart',
          time: ''
        },
        formData: {
          fields: []
        }
      };
    }
  }
);
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  .card {
    height: 100%;
    width: 100%;
    .page-header {
      width: calc(100% - 30px);
      margin-top: 10px;
      border-radius: 4px;
      height: 54px;
      min-height: 54px;
      background-color: #fff;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #ededed;
      .flex-left {
        flex: 1;
        margin-left: 10px;
      }
      .flex-center {
        flex: 2;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        .center-item {
          cursor: pointer;
          margin-right: 10px;
        }
        .active {
          color: var(--current-color);
        }
      }
      .flex-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        margin-right: 10px;
      }
    }
    .page-content {
      height: calc(100% - 60px);
      margin-top: 10px;
      width: calc(100% - 30px);
      display: flex;
      overflow: hidden;
      max-height: calc(100vh - 230px);
    }
  }
}
</style>
