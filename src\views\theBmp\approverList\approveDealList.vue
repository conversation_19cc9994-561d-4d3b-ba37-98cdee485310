<!-- 这里是审批中心的数据:待处理、已处理、已发起、我收到的 -->
<template>
  <div class="approvel-deal-list-main">
    <div class="handle-div">
      <div class="tab-div" v-for="item in dealList" :key="item.value">
        <el-badge :value="badgeNum" class="badge-item" :hidden="item.value !== 1" :max="99" :min="1">
          <div class="tab-item" :class="{ 'tab-active': item.checked }" @click="changeTab(item)">
            {{ item.label }}
          </div>
        </el-badge>
      </div>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%; margin-top: 16px"
      :height="tableHeight"
      ref="refTable"
      @row-click="clickTable"
      :row-key="getRowKeys"
      :expand-row-keys="expands"
      @expand-change="expandColumn"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" type="index" width="55"></el-table-column>
      <el-table-column label="流程id" prop="processInstanceId" v-if="[3, 4].includes(currentTag)">
        <template #default="scope">
          <el-tooltip popper-class="table-tool-tip" effect="dark" :content="scope.row.processInstanceId" placement="top-start">
            <div class="overflow-text">{{ scope.row.processInstanceId }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="任务id" prop="taskId" v-if="[1, 2].includes(currentTag)"></el-table-column>
      <el-table-column label="标题" prop="title">
        <template #default="scope">
          <el-tooltip popper-class="table-tool-tip" effect="dark" :content="scope.row.title" placement="top-start">
            <div class="overflow-text">{{ scope.row.title }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="摘要" prop="desc">
        <template #default="scope">
          <div class="overflow-text" v-for="de in scope.row.desc" :key="de.key">
            <span style="color: #111112">{{ de.title }}:</span
            ><span>{{ de.value }}</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="发起人" prop="createUserNickName">
        <template #default="scope">
          <el-tooltip popper-class="table-tool-tip" effect="dark" :content="scope.row.createUserNickName" placement="top-start">
            <div class="overflow-text">{{ scope.row.createUserNickName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="发起时间" prop="startTime">
        <template #default="scope">
          <el-tooltip popper-class="table-tool-tip" effect="dark" :content="formatDateYmdhm(scope.row.startTime)" placement="top-start">
            <div class="overflow-text">
              {{ formatDateYmdhm(scope.row.startTime) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" prop="endTime">
        <template #default="scope">
          <el-tooltip popper-class="table-tool-tip" effect="dark" :content="formatDateYmdhm(scope.row.endTime)" placement="top-start">
            <div class="overflow-text">
              {{ formatDateYmdhm(scope.row.endTime) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="流程状态" prop="status">
        <template #default="scope">
          <div class="overflow-text" :style="getType(scope.row.status)" style="cursor: pointer">
            <span>{{ scope.row.statustext }}</span>
            <span>{{ filterOption(scope.row.status, publicJson.statusList) }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="paginition-right"
      v-model:current-page="search.page"
      v-model:page-size="search.size"
      :page-sizes="[10, 50, 100, 200]"
      :layout="'total,sizes, prev, pager, next'"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      v-show="total > 0"
    />
    <!-- 查看实例详情 -->
    <instance-detial
      :isShowInstanceDetial="isShowInstanceDetial"
      :instanceId="processInstanceId"
      :currentItem="currentItem"
      :isShowApprove="isShowApprove"
      :isShowWithdraw="isShowWithdraw"
      :currentTag="currentTag"
      @closeInstance="handleCloseInstance"
    ></instance-detial>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, ElTooltip } from 'element-plus';
import { getUndoList, getDoneList, getLaunchList, getNotifyList } from '@/api/process';
import InstanceDetial from '../components/instanceDetial.vue';
import publicJson from '../utils/publick.json';
import { formatDateYmdhm } from '@/utils/filters';

// 类型定义
interface TabItem {
  label: string;
  value: number;
  checked: boolean;
}

interface SearchParams {
  page: number;
  size: number;
}

interface TableItem {
  processInstanceId?: string;
  taskId?: string;
  title?: string;
  createUserNickName?: string;
  startTime?: string;
  endTime?: string;
  status?: number;
  statustext?: string;
  currentNode?: any;
  [key: string]: any;
}

// 组件数据
const refTable = ref();
const tableData = ref<TableItem[]>([]);
const tableHeight = ref(window.innerHeight - 310 + 'px');
const search = ref<SearchParams>({
  page: 1,
  size: 50
});
const total = ref(0);
const expands = ref<string[]>([]);
const currentTag = ref(1);
const dealList = reactive<TabItem[]>([
  { label: '待处理', value: 1, checked: true },
  { label: '已处理', value: 2, checked: false },
  { label: '已发起', value: 3, checked: false },
  { label: '我收到', value: 4, checked: false }
]);
const isShowInstanceDetial = ref(false);
const processInstanceId = ref<string | undefined>(undefined);
const currentItem = ref<TableItem>({
  taskId: '',
  processInstanceId: '',
  title: '',
  createUserNickName: '',
  startTime: '',
  endTime: '',
  status: 0,
  statustext: '',
  currentNode: null
});
const badgeNum = ref(0);
const isShowApprove = ref(false);
const isShowWithdraw = ref(false);

// 计算属性和监听器
watch(
  dealList,
  (val) => {
    val.forEach((ite) => {
      if (ite.checked && ite.value === 1) {
        isShowApprove.value = true;
      }
    });
  },
  { deep: true, immediate: true }
);

// 获取流程状态文字颜色
const getType = (type: number) => {
  if (type === 10) {
    return { color: '#111112' };
  } else if (type === 100) {
    return { color: '#1CCB12' };
  } else if (type === 0) {
    return { color: '#0079FF' };
  } else if (type === 40) {
    return { color: '#FF9219' };
  } else if (type === 50) {
    return { color: '#Ff3434' };
  } else if (type === 6) {
    return { color: '#5ACFF0' };
  } else {
    return { color: '#F07B5A' };
  }
};

// 处理状态文本
const handleStatusText = (currentNode: any) => {
  if (!currentNode) return '';
  return currentNode.content || '';
};

/**
 * 判断流程节点是哪个状态
 * @param value 当前值
 * @param options 选项值
 */
const filterOption = (value: number, options: any[]) => {
  const item = options.find((item) => item.value === value);
  return item ? item.label : '';
};

/**
 * 获取当前行标识
 * @param row 当前的表格中一行的数据
 */
const getRowKeys = (row: TableItem) => {
  return row.processInstanceId || row.taskId || '';
};

/**
 * 获取待办列表
 */
const handleUndoList = async () => {
  try {
    tableData.value = [];
    if (currentTag.value === 1) {
      // 待处理
      const res = await getUndoList(search.value);
      if (res.code === 200) {
        res.data.list.forEach((item: TableItem) => {
          item.statustext = handleStatusText(item.currentNode);
          tableData.value.push(item);
        });
        total.value = res.data.total;
        badgeNum.value = res.data.total;
        search.value.page = res.data.pageNum;
        search.value.size = res.data.pageSize;
      } else {
        ElMessage.error(res.msg);
      }
    } else if (currentTag.value === 2) {
      // 已处理
      const res = await getDoneList(search.value);
      if (res.code === 200) {
        res.data.list.forEach((item: TableItem) => {
          item.statustext = handleStatusText(item.currentNode);
          tableData.value.push(item);
        });
        total.value = res.data.total;
        search.value.page = res.data.pageNum;
        search.value.size = res.data.pageSize;
      } else {
        ElMessage.error(res.msg);
      }
    } else if (currentTag.value === 3) {
      // 已发起
      const res = await getLaunchList(search.value);
      if (res.code === 200) {
        res.data.list.forEach((item: TableItem) => {
          item.statustext = handleStatusText(item.currentNode);
          tableData.value.push(item);
        });
        total.value = res.data.total;
        search.value.page = res.data.pageNum;
        search.value.size = res.data.pageSize;
      } else {
        ElMessage.error(res.msg);
      }
    } else if (currentTag.value === 4) {
      // 我收到
      const res = await getNotifyList(search.value);
      if (res.code === 200) {
        res.data.list.forEach((item: TableItem) => {
          item.statustext = handleStatusText(item.currentNode);
          tableData.value.push(item);
        });
        total.value = res.data.total;
        search.value.page = res.data.pageNum;
        search.value.size = res.data.pageSize;
      } else {
        ElMessage.error(res.msg);
      }
    }
  } catch (error) {
    console.error('获取列表失败', error);
    ElMessage.error('获取列表失败');
  }
};

/**
 * 分页处理
 * /
const handleSizeChange = (size: number) => {
  search.value.size = size;
  handleUndoList();
};
/**
 * 分页处理
 */
const handleCurrentChange = (page: number) => {
  search.value.page = page;
  handleUndoList();
};

/**
 * 切换标签页
 * @param item 当前点击的切换标签的数据
 */
const changeTab = (item: TabItem) => {
  dealList.forEach((ite) => {
    ite.checked = false;
  });
  item.checked = true;
  currentTag.value = item.value;
  search.value.page = 1;
  search.value.size = 50;
  handleUndoList();
};

/**
 * 点击表格行
 * @param row 当前行数据
 */
const clickTable = (row: TableItem) => {
  isShowInstanceDetial.value = true;
  processInstanceId.value = row.processInstanceId;
  currentItem.value = row;
  if (currentTag.value === 1) {
    isShowApprove.value = true;
    isShowWithdraw.value = false;
  } else if (currentTag.value === 3) {
    isShowApprove.value = false;
    isShowWithdraw.value = true;
  } else {
    isShowApprove.value = false;
    isShowWithdraw.value = false;
  }
};

/**
 * 展开列
 * @param row 当前行数据
 * @param expandedRows 展开列的书
 */
const expandColumn = (row: TableItem, expandedRows: TableItem[]) => {
  if (expandedRows.length) {
    expands.value = expandedRows.map((item) => item.processInstanceId || item.taskId || '');
  } else {
    expands.value = [];
  }
};

/**
 * 关闭查看表格详情的数据
 */
const handleCloseInstance = () => {
  isShowInstanceDetial.value = false;
};

// 生命周期
onMounted(() => {
  handleUndoList();
});
defineExpose({
  handleUndoList
});
</script>

<style lang="scss" scoped>
.approvel-deal-list-main {
  height: 100%;
  width: 100%;
  .handle-div {
    display: flex;
    align-items: center;
  }
  .tab-div {
    width: 60px;
    height: 28px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #d8dfe6;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 16px;
    margin-top: 12px;
    .badge-item {
      :deep(.el-badge__content.is-fixed) {
        position: absolute;
        top: 0px;
        right: 16px;
      }
      .tab-item {
        width: 60px;
        height: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #606266;
        background-color: #ededed;
      }
      .tab-active {
        background: var(--current-background-color);
        color: var(--current-color);
        border-radius: 4px;
      }
    }
  }
  .overflow-text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 14px;
    font-weight: 400;
    color: #606266;
  }
  .paginition-right {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
