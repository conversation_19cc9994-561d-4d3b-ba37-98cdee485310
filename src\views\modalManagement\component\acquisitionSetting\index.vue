<template>
  <!-- 采集设置 -->
  <div class="acquisitionSetting-main">
    <div class="acquire-title">
      层级结构设置
      <el-link type="primary" @click="handleOpenAddNodeDialog"
        ><el-icon style="margin-right: 5px"><Plus /></el-icon>新增</el-link
      >
      <!-- <el-dropdown @command="handleCommandNode">
        <span class="el-dropdown-link">
          <el-icon><Plus /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="1">要素节点</el-dropdown-item>
            <el-dropdown-item command="2" :disabled="Boolean(nodeType)">WMS节点</el-dropdown-item>
            <el-dropdown-item command="3">图层节点</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
    </div>
    <div class="left" v-show="!isFieldForm">
      <!-- 要素树 -->
      <el-tree
        v-show="nodeType === 1"
        :data="treeList"
        node-key="id"
        ref="treeRef"
        default-expand-all
        :props="defaultProps"
        :expand-on-click-node="false"
        :highlight-current="true"
        :current-node-key="checkedDataId"
        class="tree-div"
      >
        <template v-slot="{ node, data }">
          <div class="tree-row" @click="chooseTree(node, data)">
            <div class="tree-row-left">
              <div v-if="data.iconUrl && data.iconUrl.substring(data.iconUrl.lastIndexOf('_') + 1) == 'blob'">
                <el-image style="width: 20px; height: 20px; margin-right: 8px" :src="`${baseUrl}${data.iconUrl}?token=${token}`" :fit="'cover'" />
              </div>
              <div v-else>
                <svg-icon class-name="svg-item" :icon-class="data.iconUrl + ''" />
              </div>
              <span style="margin-left: 4px">{{ data.typeName }}</span>
            </div>
            <div class="tree-row-right">
              <div class="tree-row-handle">
                <svg-icon class-name="svg-item" icon-class="more_dian" />
              </div>
              <div class="menu-list">
                <!-- 图层节点不允许有子节点 -->
                <div class="opera-item" @click="handleCommand(node, data, '1')" v-if="data.container !== 1 && data.container !== 2">添加子级</div>
                <div class="opera-item" @click="handleCommand(node, data, '2')" v-if="setShowDel(data)">删除</div>
              </div>
            </div>
          </div>
        </template>
      </el-tree>
      <!-- 图层树 -->
      <div v-show="nodeType === 2" class="tc-div">
        {{ tcMsg.typeName }}
      </div>
      <!-- 无数据时候 -->
      <div v-show="!nodeType">
        <el-empty description="暂无层级"></el-empty>
        <div style="text-align: center; margin-top: -20px; display: flex; align-items: center; justify-content: center">
          请添加<el-link type="primary" @click="addNode(1)">要素节点</el-link> <span style="margin: 0px 1px">或</span>
          <el-link type="primary" @click="addNode(2)">图层节点</el-link>
        </div>
      </div>
    </div>
    <div class="right" v-show="!isFieldForm && nodeType === 1">
      <!-- 基本信息 -->
      <base-setting-info
        ref="baseSettingInfoRef"
        :checkedLevel="checkedLevel"
        :checkedNodeId="checkedNodeId"
        :checkedTreeMsg="checkedTreeMsg"
        :moduleId="moduleId"
      />
      <!-- 采集要素 -->
      <!--     @updateElement="handleUpdateTreeElement"  -->
      <!-- 组节点不显示采集要素设置 -->
      <acquisition-element
        v-show="checkedTreeMsg.container !== 3"
        ref="acquisitionElementRef"
        :checkedLevel="checkedLevel"
        :checkedNodeId="checkedNodeId"
        :checkedTreeMsg="checkedTreeMsg"
        :groupObj="groupObj"
        @openFieldForm="handleOpenFieldForm"
        @showField="showField"
        @saveRule="handleSaveRule"
        @lineHeightTree="handleLineHeightTree"
      />
      <!-- 属性组-->
      <attribute-group
        ref="attribteGroupRef"
        :moduleId="moduleId"
        :checkedLevel="checkedLevel"
        :checkedNodeId="checkedNodeId"
        :checkedTreeMsg="checkedTreeMsg"
        @openFieldForm="handleOpenFieldForm"
        @showField="showField"
        @saveRule="handleSaveRule"
      />
      <!-- 采集权属人 -->
      <acquisition-owner
        ref="ownerRef"
        :moduleId="moduleId"
        :checkedLevel="checkedLevel"
        :checkedNodeId="checkedNodeId"
        :checkedTreeMsg="checkedTreeMsg"
        @saveRule="handleSaveRule"
      />
      <!-- 勘界设置 -->
      <kjSetting
        ref="kjSettingRef"
        :checkedTreeMsg="checkedTreeMsg"
        v-show="checkedTreeMsg.levelNum === 1 && checkedTreeMsg.tools === 'kanjie'"
      ></kjSetting>
      <!-- 拓扑检查 -->
      <!-- <examineSetting></examineSetting> -->
      <div class="btn-next-step">
        <el-button type="primary" @click="handleOpenOrderGroup" size="small" :icon="Operation">属性组排序</el-button>
        <el-button type="primary" @click="handleNext" size="small" v-if="vipType === 3"
          >下一步<el-icon><ArrowRight /></el-icon>
        </el-button>
        <!-- 最后一步是完成 -->
        <el-button type="primary" @click="handleNextBack" size="small" v-else>完成</el-button>
      </div>
    </div>
    <!-- 图层属性 -->
    <div class="right tc-right" v-show="nodeType === 2">
      <el-form :model="tcMsg" :rules="tcMsgRules" ref="tcMsgRef" label-width="100px" class="demo-ruleForm" label-position="top">
        <el-form-item label="图层标题" prop="typeName">
          <el-input v-model="tcMsg.typeName" placeholder="请输入图层标题" maxlength="10"></el-input>
        </el-form-item>
        <el-form-item label="选择图层" prop="layerUrl">
          <el-select v-model="tcMsg.layerUrl" placeholder="请选择图层" style="width: 100%" @change="changeTc">
            <el-option v-for="item in layerList" :key="item.layerName" :label="item.title" :value="item.layerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图层字段" prop="layerField">
          <el-select v-model="tcMsg.layerField" placeholder="请选择图层" style="width: 100%">
            <el-option v-for="item in properties" :key="item" :label="item" :value="item"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="btn-next-step">
        <el-button type="primary" @click="handleNext" size="small" v-if="vipType === 3">
          下一步<el-icon><ArrowRight /></el-icon>
        </el-button>
        <!-- 最后一步是完成 -->
        <el-button type="primary" @click="handleNextBack" size="small" v-else>完成</el-button>
      </div>
    </div>

    <!-- 新增表单字段 -->
    <dynamic-from
      v-show="isFieldForm"
      @goBack="handleGoBack"
      @submitField="handleSubmitField"
      :confProp="formData"
      :isFieldFormProp="isFieldForm"
      @updateGroup="handleUpdateGroupField"
    ></dynamic-from>
    <!-- 属性组排序 -->
    <order-by-group :orderVisible="orderVisible" :checkedTreeMsg="checkedTreeMsg" @closeOrder="handleCloseOrder"></order-by-group>
    <!-- 新增节点弹窗 -->
    <el-dialog draggable v-model="addNewNodeDialog" title="添加采集节点" width="875" :before-close="handleCloseNewNodeDialog">
      <div class="addNode-content">
        <div class="left-clom">
          <div
            class="flex-item"
            :class="{ 'active': item.active }"
            :style="{ cursor: addTypeFrom === 2 && item.type === '2' ? 'not-allowed' : 'pointer' }"
            v-for="(item, index) in addNodes"
            :key="index"
            @click="handleChangeNode(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="right-clom">{{ nowRemark }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseNewNodeDialog">取消</el-button>
          <el-button type="primary" @click="submitAddNodeDialog"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import axios from 'axios';
import authImg from '@/components/authImg/index.vue';
import baseSettingInfo from './baseSetting.vue';
import acquisitionElement from './acquisitionElement.vue';
import { addRule, selectRules, getPlaceList } from '@/api/modal';
import { operaParcel } from '@/api/project';
import attributeGroup from './attributeGroup.vue';
import AcquisitionOwner from './acquisitionOwner.vue';
import DynamicFrom from '@/components/DynamicForm/index.vue';
import SvgIcon from '../../svgIcon/index.vue';
import { getTCList } from '@/api/issueManager';
import examineSetting from './examineSetting.vue';
import kjSetting from './kjSetting.vue';
import orderByGroup from './orderByGroup.vue';
import { Plus, ArrowRight, Operation } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { getToken } from '@/utils/auth';

const userStore = useUserStore();
const modalStore = useModalStore();
const router = useRouter();
const route = useRoute();
const token = getToken();

// 定义响应式数据
const formData = ref({});
const isFieldForm = ref(false);
const baseUrl = `${import.meta.env.VITE_APP_BASE_API}/qjt/file/otherDownload/`;
const treeList = ref<any[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const checkedTreeMsg = ref<any>({});
const aliasName = ref('');
const defaultProps = ref({
  children: 'list'
});
const checkedLevel = ref(1);
const checkedNodeId = ref(0);
const checkedDataId = ref(-1);
const checkedNode = ref<any>({});
const repetition = ref('2');
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
]);
const groupObj = ref({});
const parentNode = ref<any>({});
const nodeType = ref<number | null>(null);
const tcMsg = ref({
  layerUrl: '',
  layerField: '',
  layerFlag: 1, //图层标识;1,图层;2非图层;
  graphicalType: 0,
  typeName: '图层标题'
});
const tcMsgRules = ref({
  layerUrl: [{ required: true, message: '请选择图层', trigger: 'change' }],
  typeName: [{ required: true, message: '请输入图层标题', trigger: 'blur' }],
  layerField: [{ required: true, message: '请选择图层字段', trigger: 'change' }]
});
const layerList = ref<any[]>([]);
const layerBase = ref(import.meta.env.VUE_APP_LAYER_BASE);
const properties = ref<any[]>([]);
const orderVisible = ref(false);

// 定义组件引用
const treeRef = ref<any>(null);
const baseSettingInfoRef = ref<any>(null);
const kjSettingRef = ref<any>(null);
const acquisitionElementRef = ref<any>(null);
const attribteGroupRef = ref<any>(null);
const ownerRef = ref<any>(null);
const tcMsgRef = ref<any>(null);

// 计算属性
const moduleId = computed(() => modalStore.moduleId);
const ruleId = computed(() => modalStore.ruleId);
const vipType = computed(() => userStore.vipType);
const modal_state = Number(route.query.status);
const pinaRuleTree = computed(() => modalStore.nodeTree);
const addNewNodeDialog = ref(false); //新版新增节点弹窗
const addNodes = ref([
  {
    name: '要素节点',
    type: '1',
    active: true,
    remark: '单个采集要素，在app和网页上显示为单个要素，要素类型可以是点，线或者面。要素节点之下可以添加另外的采集节点。'
  },
  {
    name: '图层节点',
    type: '3',
    active: false,
    remark: '多个采集要素构成一个图层，类型可以是点，线或者面，图层节点之下不能添加别的采集节点。'
  },
  {
    name: '组节点',
    type: '4',
    active: false,
    remark: '用于归类，分组显示采集节点。组节点不具有几何要素类型。'
  },
  {
    name: '四至节点',
    type: '5',
    active: false,
    remark: '专门用于采集四至点要素，需要选择所属的面要素用于自动生成四个四至节点，属性默认具有东至，西至，南至和北至，以及可选的坐标等等属性。'
  },
  {
    name: 'WMS节点',
    type: '2',
    active: false,
    remark: '用于显示用户发布的在线要素图层，此图层只能用作根节点，不能作为其他采集节点的子节点或者父节点，通常用于底图。'
  }
]);
const nowRemark = ref('');
const addTypeFrom = ref(1); //1新增根 2新增子节点
// 监听 moduleId 变化
watch(
  moduleId,
  (val) => {
    if (val !== 0) {
      checkedTreeMsg.value.moduleId = val;
    }
  },
  { immediate: true, deep: true }
);

// 生命周期钩子
onMounted(async () => {
  const id = Number(router.currentRoute.value.query.id);
  if (id && id !== 0) {
    modalStore.setModuleId(id);
    await getModuleDetial();
  } else {
    // this.handleSelectnan()
  }
  if (vipType.value !== 3) {
    nodeType.value = 1;
  } else {
    await getTc();
  }
});

// 方法定义
const handleLineHeightTree = () => {
  nextTick(() => {
    setTimeout(() => {
      const id = treeRef.value?.getCurrentKey();
      treeRef.value?.setCurrentKey(id);
    }, 200);
  });
};

const chooseTree = (node: any, data: any) => {
  node.checked = true;
  checkedLevel.value = node.level;
  checkedNodeId.value = node.id;
  if (node && node.data) {
    if (!node.data.attribution || !('uniq' in node.data.attribution)) {
      if (node.data.attribution) {
        node.data.attribution = {
          ...node.data.attribution,
          uniq: null
        };
      } else {
        node.data.attribution = {
          uniq: null
        };
      }
    }
  }
  checkedTreeMsg.value = node.data;
  initChildCom(node.data);
  sessionStorage.setItem('checkedTreeMsg', JSON.stringify(node.data));
  checkedNode.value = node;
  if (node.level !== 1) {
    repetition.value = '2';
  }
  checkedDataId.value = data.id;
  treeRef.value?.setCurrentKey(data.id);
  modalStore.setRuleId(node.data.id);
};

const beforeHandleCommand = (node: any, data: any, command: string) => {
  return {
    'node': node,
    'data': data,
    'command': command
  };
};

const editItmeDel = (list: any[]) => {
  list.forEach((v) => {
    v.delFlag = 1;
    v.status = 0;
    if (v.fieldGroupModelList && v.fieldGroupModelList.length !== 0) {
      v.fieldGroupModelList.forEach((item: any) => {
        if (item.groupScope !== 1) {
          item.delFlag = 1;
          item.status = 0;
        }
      });
    }
    if (v.list && v.list.length !== 0) {
      editItmeDel(v.list);
    }
  });
};

const handleCommand = (node: any, data: any, str: string) => {
  parentNode.value = data;
  if (str === '1') {
    /**
     * 需要判断
     * 1如果父节点是要素节点 那么不允许添加WMS节点
     * 2如果父节点是图层节点，那么子节点不允许条件WMS节点
     * 3如果父节点是组节点，那么子节点不允许添加WMS节点
     * 4如果父节点是四至节点 那么什么子节点都不允许添加
     */
    addTypeFrom.value = 2;
    nowRemark.value = addNodes.value[0].remark;
    addNewNodeDialog.value = true;
  } else if (str === '2') {
    ElMessageBox.confirm('确定要删除该层级及下级吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        const parent = node.parent;
        const children = parent.data.list || parent.data;
        const index = children.findIndex((d: any) => d.id === data.id);
        children.splice(index, 1);
        nextTick(() => {
          treeRef.value?.setCurrentKey(parent.data.id);
        });
        chooseTree(parent, data);
        data.delFlag = 1;
        data.status = 0;
        const item = data;
        editItmeDel([item]);
        if (item.id) {
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            item.companyId = companyId;
          }
          await addRule([item]).then((res) => {
            modalStore.setNodeTree(res.data);
            if (treeList.value.length === 0) {
              nodeType.value = null;
            }
          });
        }
      })
      .catch(() => {});
  }
};

const handleSubmitSettingIcon = (url: string) => {
  checkedTreeMsg.value.iconUrl = url;
};

const handleGoBack = (item: any) => {
  formData.value = {};
  groupObj.value = item;
  isFieldForm.value = false;
  nextTick(() => {
    const id = treeRef.value?.getCurrentKey();
    treeRef.value?.setCurrentKey(id);
  });
};

const handleSubmitField = (item: any) => {
  nextTick(() => {
    setTimeout(() => {
      const id = treeRef.value?.getCurrentKey();
      treeRef.value?.setCurrentKey(id);
    }, 100);
  });
  groupObj.value = item;
  isFieldForm.value = false;
};

const getModuleDetial = async () => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  if (vipType.value !== 3) {
    nodeType.value = 1;
    if (pinaRuleTree.value.length === 0) {
      treeList.value = [
        {
          typeName: '第1级',
          id: 0,
          iconUrl: 'diagram',
          color: '#FF0000',
          aliasName: '',
          wordName: 'di1ji',
          companyId: route.query.companyId,
          aliasNameCn: '',
          graphicalType: 3,
          fieldGroupModelList: [],
          list: [],
          moduleId: moduleId.value || 0,
          levelNum: 1,
          graphicalNum: 10,
          styleAttribution: {
            polylineType: 'solid',
            polylineWidth: 1,
            polylineColor: '#FF0000',
            pointType: 'Circle',
            pointSize: 4,
            pointColor: '#FF0000',
            polygonFillColor: '#FF000033',
            polygonType: 'Solid'
          },
          attribution: {
            uniq: null
          },
          ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
        }
      ];
      checkedTreeMsg.value = treeList.value[0];
      initChildCom(treeList.value[0]);
    }
  } else {
    nodeType.value = null;
  }
  if (pinaRuleTree.value.length !== 0) {
    const layerFlag = pinaRuleTree.value[0].layerFlag;
    if (layerFlag === 1) {
      nodeType.value = 2;
      tcMsg.value.typeName = pinaRuleTree.value[0].typeName;
      tcMsg.value.layerFlag = pinaRuleTree.value[0].layerFlag;
      tcMsg.value.ruleId = pinaRuleTree.value[0].id;
      const parmas = {
        pageNum: 1,
        pageSize: 10,
        moduleId: moduleId.value
      };
      // 设置公司私有模块的数据 需要传递公司id
      const companyId = route.query.companyId;
      if (companyId && companyId !== undefined && companyId !== null) {
        params.companyId = companyId;
      }
      parmas.levelNum = 1;
      getPlaceList(parmas).then((resp) => {
        if (resp.code === 200) {
          tcMsg.value.layerField = resp.data.list[0].layerField;
          tcMsg.value.layerUrl = resp.data.list[0].layerUrl;
          tcMsg.value.id = resp.data.list[0].id;
        } else {
          ElMessage.error(resp.msg);
        }
      });
    } else if (layerFlag === 2) {
      nodeType.value = 1;
      const ite_list = pinaRuleTree.value.filter((v: any) => v.display !== 0);
      treeList.value = ite_list;
      checkedTreeMsg.value = treeList.value[0];
      initChildCom(treeList.value[0]);
      if (treeList.value[0].attribution === null) {
        checkedTreeMsg.value.attribution = {
          uniq: null
        };
      }
      nextTick(() => {
        treeRef.value?.setCurrentKey(treeList.value[0].id);
        checkedDataId.value = treeList.value[0].id;
      });
      const ruleId = pinaRuleTree.value[0].id;
      modalStore.setRuleId(ruleId);
    }
  } else {
    treeList.value = [];
  }
};

/**
 * 用于给子组件初始化数据
 * @param item 选中的节点
 * @returns void
 */
const initChildCom = (item: any) => {
  if (item.attribution && item.attribution.kjSetting) {
    kjSettingRef.value.initData(item);
  }
  baseSettingInfoRef.value.initData(item);
  acquisitionElementRef.value.initData(item);
};

const handleUpdateGroupField = async (params: any) => {
  // 设置公司私有模块的数据 需要传递公司id
  // const companyId = route.query.companyId;
  // if (companyId && companyId !== undefined && companyId !== null) {
  //   params.companyId = companyId;
  // }
  updateFieldGroupModeList(params, treeList.value);
  await addRule(treeList.value).then((res) => {
    if (res.code === 200) {
      modalStore.setNodeTree(res.data);
      treeList.value = res.data;
      getNowNode(treeList.value);
    } else {
      ElMessage.error(res.msg);
    }
  });
  modalStore.setElementType('');
};

const updateFieldGroupModeList = (params: any, list: any[]) => {
  list.forEach((v) => {
    if (v.id === checkedTreeMsg.value.id) {
      let flag = true;
      v.fieldGroupModelList.forEach((k: any) => {
        if (k.id === params.id) {
          flag = false;
          Object.assign(k, params);
        }
      });
      if (flag) {
        v.fieldGroupModelList.push(params);
      }
      if (!flag) {
        v.fieldGroupModelList.forEach((k: any) => {
          if (k.id === params.id) {
            const fields = k.attribution.formData.fields;
            const endFields = params.fieldModelList;
            fields.forEach((o: any) => {
              endFields.forEach((x: any) => {
                if (!o.vModel) {
                  const fieldName = `${o.children[0].children.vModel}${o.componentName.substring(2)}`;
                  if (fieldName === x.fieldName) {
                    o.id = x.id;
                  }
                } else {
                  if (o.vModel === x.fieldName) {
                    o.id = x.id;
                  }
                }
              });
            });
          }
        });
      }
    } else if (v.list.length !== 0) {
      updateFieldGroupModeList(params, v.list);
    }
  });
};

const handleOpenFieldForm = (groupId: any, data: any) => {
  formData.value = {};
  isFieldForm.value = true;
};

const showField = (formDataParam: any) => {
  formData.value = formDataParam;
  isFieldForm.value = true;
};

const handleOpenOrderGroup = () => {
  orderVisible.value = true;
};

/**
 * 关闭属性组排序弹窗并更新视图
 * @param data 排序后的结果
 */
const handleCloseOrder = (data: any) => {
  orderVisible.value = false;
  if (data) {
    // 还需要更新 最新的结果
    treeList.value = data;
    const matchedNode = findNodeById(data, checkedTreeMsg.value.id);
    if (matchedNode) {
      checkedTreeMsg.value = { ...matchedNode };
    }
  }
};

const findNodeById = (nodes: any[], id: string): any => {
  for (const node of nodes) {
    if (node.id === id) return node;
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
};

const handleNext = async () => {
  if (nodeType.value === 1) {
    const isBaseInfo = await baseSettingInfoRef.value.handleValidateForm();
    const isAcqireInfo = await acquisitionElementRef.value.handleValidateForm();
    if (isBaseInfo && isAcqireInfo) {
      checkedTreeMsg.value.moduleId = moduleId.value;
      await addRule(treeList.value).then((res) => {
        if (res.code === 200) {
          modalStore.setNodeTree(res.data);
          ElMessage.success('保存成功');
          emit('nextStep', 4);
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  } else if (nodeType.value === 2) {
    tcMsgRef.value?.validate((valid: boolean) => {
      if (valid) {
        if (tcMsg.value.ruleId) {
          const parmas = {
            ruleId: tcMsg.value.ruleId,
            parcelName: tcMsg.value.typeName,
            layerUrl: tcMsg.value.layerUrl,
            layerField: tcMsg.value.layerField,
            taskId: 0,
            id: tcMsg.value.id
          };
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            parmas.companyId = companyId;
          }
          operaParcel([parmas]).then((resp) => {
            if (resp.code === 200) {
              emit('nextStep', 4);
            } else {
              ElMessage.error(resp.msg);
            }
          });
        } else {
          const ruleMsg = {
            layerFlag: 1,
            typeName: tcMsg.value.typeName,
            moduleId: moduleId.value
          };
          // 设置公司私有模块的数据 需要传递公司id
          const companyId = route.query.companyId;
          if (companyId && companyId !== undefined && companyId !== null) {
            ruleMsg.companyId = companyId;
          }
          addRule([ruleMsg]).then((res) => {
            if (res.code === 200) {
              const parmas = {
                ruleId: res.data[0].id,
                parcelName: tcMsg.value.typeName,
                layerUrl: tcMsg.value.layerUrl,
                layerField: tcMsg.value.layerField,
                taskId: 0
              };
              // 设置公司私有模块的数据 需要传递公司id
              const companyId = route.query.companyId;
              if (companyId && companyId !== undefined && companyId !== null) {
                parmas.companyId = companyId;
              }
              operaParcel([parmas]).then((resp) => {
                if (resp.code === 200) {
                  emit('nextStep', 4);
                } else {
                  ElMessage.error(resp.msg);
                }
              });
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
      } else {
        return false;
      }
    });
  }
};

const handleNextBack = async () => {
  if (nodeType.value === 1) {
    const isBaseInfo = await baseSettingInfoRef.value.handleValidateForm();
    let isAcqireInfo = false;
    if (!acquisitionElementRef.value) {
      isAcqireInfo = true;
    } else {
      isAcqireInfo = await acquisitionElementRef.value?.handleValidateForm();
    }
    if (isBaseInfo && isAcqireInfo) {
      checkedTreeMsg.value.moduleId = moduleId.value;
      await addRule(treeList.value).then((res) => {
        if (res.code === 200) {
          ElMessage.success('保存成功');
          modalStore.setNodeTree([]);
          router.push({
            path: '/modal',
            query: {
              companyId: route.query.companyId
            }
          });
        } else {
          ElMessage.error(res.msg);
        }
      });
    } else {
      ElMessage.info('请检查采集设置中数据是否填写完整!');
    }
  } else if (nodeType.value === 2) {
    // 原代码此处无逻辑
  }
};

const handleSaveRule = async (flag: string) => {
  const isBaseInfo = await baseSettingInfoRef.value.handleValidateForm();
  const isAcqireInfo = await acquisitionElementRef.value.handleValidateForm();
  // TODO
  if (isBaseInfo && isAcqireInfo) {
    checkedTreeMsg.value.moduleId = moduleId.value;
    console.log('需要保存树---', treeList.value);

    await addRule(treeList.value).then((res) => {
      if (res.code === 200) {
        modalStore.setNodeTree(res.data);
        if (flag === 'element') {
          acquisitionElementRef.value.funCES();
        } else if (flag === 'group') {
          attribteGroupRef.value.groupCenterFun();
        } else if (flag === 'owner') {
        }
        treeList.value = res.data;
        getNowNode(treeList.value);
        nextTick(() => {
          treeRef.value?.setCurrentKey(checkedTreeMsg.value.id);
        });
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    ElMessage.error('请先填写基本信息,在添加分组');
  }
};

const getNowNode = (list: any[]) => {
  list.forEach((v) => {
    if (v.typeName === checkedTreeMsg.value.typeName && v.wordName === checkedTreeMsg.value.wordName) {
      checkedTreeMsg.value = v;
    } else {
      if (v.list.length !== 0) {
        getNowNode(v.list);
      }
    }
  });
};

const addNode = (type: number) => {
  if (type === 1) {
    nodeType.value = 1;
    changeYsNode();
  } else if (type === 2) {
    nodeType.value = 2;
  }
};

/**
 * 新增生成唯一id 默认生成一个负的唯一id
 */
const getNewId = () => {
  return -Math.floor(Math.random() * 90000000 + 10000000);
};

const changeYsNode = () => {
  nodeType.value = 1;
  let typeName = '第1级';
  let wordName = 'di1ji';
  let levelNum = 1;
  if (addTypeFrom.value === 2) {
    //表示是子节点
    levelNum = checkedLevel.value + 1;
    typeName = `第${levelNum}级`;
    wordName = `di${levelNum}ji`;
  }
  const newTree = {
    typeName: typeName,
    id: getNewId(),
    iconUrl: 'diagram',
    color: '#FF0000',
    aliasName: '',
    companyId: route.query.companyId,
    wordName: wordName,
    aliasNameCn: '',
    graphicalType: 3,
    fieldGroupModelList: [],
    list: [],
    moduleId: moduleId.value || 0,
    levelNum: levelNum,
    graphicalNum: 10,
    styleAttribution: {
      polylineType: 'solid',
      polylineWidth: 1,
      polylineColor: '#FF0000',
      pointType: 'Circle',
      pointSize: 4,
      pointColor: '#FF0000',
      polygonFillColor: '#FF000033',
      polygonType: 'Solid'
    },
    attribution: {
      uniq: null
    },
    ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
  };
  if (addTypeFrom.value === 2) {
    //子要素
    parentNode.value.list.push(newTree);
  } else {
    treeList.value.push(newTree);
  }
  if (treeList.value.length === 1) {
    checkedTreeMsg.value = treeList.value[0];
    initChildCom(treeList.value[0]);
  }
  tcMsg.value = {
    layerUrl: '',
    layerField: '',
    layerFlag: 1,
    graphicalType: 0,
    typeName: '图层标题',
    id: null,
    ruleId: null
  };
};

const getTc = async () => {
  await getTCList().then((res) => {
    if (res.code === 200) {
      res.data.forEach((v: any) => {
        const index = v.mapName.indexOf(':');
        const title = v.mapName.substring(index + 1, v.length);
        const ite = {
          layerName: v,
          title: title
        };
        layerList.value.push(ite);
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const changeTc = (val: string) => {
  tcMsg.value.layerField = '';
  properties.value = [];
  const num = val.indexOf(':');
  const title = val.substring(num + 1, val.length);
  const url = `${layerBase.value}${val.substring(0, num)}/${title}/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${val}&maxFeatures=10&outputFormat=application%2Fjson`;
  axios.get(url).then((res) => {
    if (res.data.features) {
      if (res.data.features.length !== 0) {
        properties.value = Object.keys(res.data.features[0].properties);
      }
    }
  });
};

const changeTabSaveRule = async () => {
  if (treeList.value.length !== 0) {
    await addRule(treeList.value).then((res) => {
      if (res.code === 200) {
        modalStore.setNodeTree(res.data);
      } else {
        ElMessage.error(res.msg);
        treeList.value = [];
      }
    });
  }
};

const handlSubmit = () => {
  router.push({
    path: '/modal',
    query: {
      companyId: route.query.companyId
    }
  });
};

/**
 * 是否显示删除 只有 modal_state 不为-1,1时 或者 data.id 没有时才显示删除
 * @param data 当前选中的节点
 * @returns boolean
 */
const setShowDel = (data: any) => {
  if (data.id && [-1, 1].includes(modal_state)) {
    return false;
  }
  return true;
};

/**
 * 弹出新增节点
 */
const handleOpenAddNodeDialog = () => {
  addTypeFrom.value = 1;
  parentNode.value = null;
  nowRemark.value = addNodes.value[0].remark;
  addNewNodeDialog.value = true;
};

/**
 * 新增节点弹窗关闭
 */
const handleCloseNewNodeDialog = () => {
  addNewNodeDialog.value = false;
};

/**
 * 提交新增节点 弹窗
 */
const submitAddNodeDialog = () => {
  let command = '';
  for (let i = 0; i < addNodes.value.length; i++) {
    if (addNodes.value[i].active) {
      command = addNodes.value[i].type;
      break;
    }
  }
  let levelNum = 1;
  if (addTypeFrom.value === 2) {
    //表示是子节点
    levelNum = checkedLevel.value + 1;
  }
  if (command === '1') {
    if (nodeType.value === 2) {
      ElMessageBox.confirm('确定要切换为要素节点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          if (tcMsg.value.ruleId) {
            const parms = {
              delFlag: 1,
              status: 0,
              id: tcMsg.value.ruleId
            };
            // 设置公司私有模块的数据 需要传递公司id
            const companyId = route.query.companyId;
            if (companyId && companyId !== undefined && companyId !== null) {
              parms.companyId = companyId;
            }
            await addRule([parms]).then((res) => {
              if (res.code === 200) {
                modalStore.setNodeTree(res.data);
                changeYsNode();
              } else {
                ElMessage.error(res.msg);
              }
            });
          } else {
            changeYsNode();
          }
        })
        .catch(() => {});
    } else {
      changeYsNode();
    }
  } else if (command === '2') {
    nodeType.value = 2;
  } else if (command === '3') {
    nodeType.value = 1;

    const newTree = {
      typeName: '面图层节点',
      id: getNewId(),
      iconUrl: 'diagram',
      color: '#FF0000',
      aliasName: '',
      wordName: 'tuceng',
      aliasNameCn: '',
      graphicalType: 3,
      fieldGroupModelList: [],
      list: [],
      moduleId: moduleId.value || 0,
      levelNum: levelNum,
      graphicalNum: 10,
      container: 1, // 1为图层节点 2为四至节点 3为组节点
      styleAttribution: {
        polylineType: 'solid',
        polylineWidth: 1,
        polylineColor: '#FF0000',
        pointType: 'Circle',
        pointSize: 4,
        pointColor: '#FF0000',
        polygonFillColor: '#FF000033',
        polygonType: 'Solid'
      },
      attribution: {
        uniq: null
      },
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
    };
    if (addTypeFrom.value === 2) {
      //子节点
      parentNode.value.list.push(newTree);
    } else {
      //根节点
      treeList.value.push(newTree);
    }
    if (treeList.value.length === 1) {
      checkedTreeMsg.value = treeList.value[0];
      initChildCom(treeList.value[0]);
    }
  } else if (command === '4') {
    //组节点
    nodeType.value = 1;
    const newTree = {
      typeName: '组节点',
      id: getNewId(),
      iconUrl: 'diagram',
      color: '#FF0000',
      aliasName: '',
      wordName: 'tuceng',
      aliasNameCn: '',
      graphicalType: 4, //无图形
      fieldGroupModelList: [],
      list: [],
      moduleId: moduleId.value || 0,
      levelNum: levelNum,
      graphicalNum: 10,
      container: 3, // 1为图层节点 2为四至节点 3为组节点
      styleAttribution: {
        polylineType: 'solid',
        polylineWidth: 1,
        polylineColor: '#FF0000',
        pointType: 'Circle',
        pointSize: 4,
        pointColor: '#FF0000',
        polygonFillColor: '#FF000033',
        polygonType: 'Solid'
      },
      attribution: {
        uniq: null
      },
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
    };
    if (addTypeFrom.value === 2) {
      //子节点
      parentNode.value.list.push(newTree);
    } else {
      //根节点
      treeList.value.push(newTree);
    }
    if (treeList.value.length === 1) {
      checkedTreeMsg.value = treeList.value[0];
      initChildCom(treeList.value[0]);
    }
  } else if (command === '5') {
    //四至节点
    nodeType.value = 1;
    const newTree = {
      typeName: '四至节点',
      id: getNewId(),
      iconUrl: 'diagram',
      color: '#FF0000',
      aliasName: '',
      wordName: 'tuceng',
      aliasNameCn: '',
      graphicalType: 1, //无图形
      fieldGroupModelList: [],
      list: [],
      moduleId: moduleId.value || 0,
      levelNum: levelNum,
      graphicalNum: 10,
      container: 2, // 1为图层节点 2为四至节点 3为组节点
      graphicalMaxNum: 4,
      graphicalMinNum: 4,
      styleAttribution: {
        polylineType: 'solid',
        polylineWidth: 1,
        polylineColor: '#FF0000',
        pointType: 'Circle',
        pointSize: 4,
        pointColor: '#FF0000',
        polygonFillColor: '#FF000033',
        polygonType: 'Solid'
      },
      attribution: {
        uniq: null,
        eastTo: '东至',
        southTo: '南至',
        westTo: '西至',
        northTo: '北至'
      },
      ruleId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8)
    };
    if (addTypeFrom.value === 2) {
      //子节点
      parentNode.value.list.push(newTree);
    } else {
      //根节点
      treeList.value.push(newTree);
    }
    if (treeList.value.length === 1) {
      checkedTreeMsg.value = treeList.value[0];
      initChildCom(treeList.value[0]);
    }
  }
  addNewNodeDialog.value = false;
};

/**
 * 切换选择节点类型
 * @param item 选中某个节点类型
 */
const handleChangeNode = (item: any) => {
  if (addTypeFrom.value === 2 && item.type === '2') {
    ElMessage.warning('WMS节点只能单独存在');
    return;
  }
  addNodes.value.forEach((v) => {
    v.active = false;
  });
  item.active = true;
  nowRemark.value = item.remark;
};

// 组件注册
defineExpose({
  // 可根据需要暴露方法或数据
  getModuleDetial,
  isFieldForm,
  changeTabSaveRule
});

defineProps<{
  // 若有 props 可在此定义
}>();

const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();
</script>

<style lang="scss" scoped>
:deep(.el-image) {
  background: transparent !important;
}
:deep(.el-tree-node__content) {
  height: 44px;
  // padding-left: 106px !important;
}
:deep(.el-image) {
  margin: 0px;
}
:deep(.el-tree--highlight-current) .el-tree-node.is-current > .el-tree-node__content {
  color: var(--current-color); // 节点的字体颜色
  font-weight: 500; // 字体加粗
}
// :deep(.tree-dropDown-item):hover{
//   color: red;
// }
// :deep(.tree-dropDown-item):hover
//  ~ .drop-down-all
//   ~ .tree-row-right {
//   display: block;
//   color: #a200ff;
// }
.acquisitionSetting-main {
  font-size: 14px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  // border-top: 1px solid rgba(219, 231, 238, 1);
  position: relative;
  .acquire-title {
    height: 48px;
    position: absolute;
    top: 0;
    width: 370px;
    line-height: 36px;
    padding-left: 16px;
    padding-right: 16px;
    font-size: 14px;
    cursor: pointer;
    font-family:
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;
    font-weight: 600;
    color: #161d26;
    background: #fff;
    border-right: 1px solid rgba(219, 231, 238, 1);
    padding-top: 8px;
    border-bottom: 1px solid #dbe7ee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-ico {
      margin-right: 10px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
    }
  }
  .acquire-title:hover + .text-no {
    display: block;
    color: red;
    cursor: pointer;
  }
  .left {
    width: 370px;
    height: 100%;
    border-right: 1px solid rgba(219, 231, 238, 1);
    background: #fff;
    overflow: hidden;
    margin-top: 48px;
    .top-handle {
      width: 100%;
      height: 49px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0px 16px;
    }
    .tc-div {
      height: 44px;
      width: 100%;
      color: var(--current-color);
      background-color: #edf6ff;
      display: flex;
      align-items: center;
      padding-left: 16px;
    }
    .tree-div {
      height: calc(100% - 49px);
      overflow: auto;
      width: 100%;
      color: #161d26;
      :deep(&) {
        //有子节点 且未展开 小三角
        .el-icon-caret-right::before {
          color: #8291a9;
          font-weight: 600;
          font-size: 16px;
          padding: 8px;
        }
        //有子节点 且已展开 小三角
        .el-tree-node__expand-icon.is-leaf {
          padding-left: 22px;
        }
        .el-tree-node__expand-icon.is-leaf::before {
          content: '';
          display: block;
          font-weight: 600;
          font-size: 16px;
          padding: 8px;
        }
        //没有子节点 小三角
        .el-tree-node__expand-icon.expanded.el-icon-caret-right::before {
          color: #8291a9;
          font-weight: 600;
          font-size: 16px;
          padding: 8px;
        }
      }
      .tree-row {
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        width: 100%;
        // padding: 0px 16px;
        padding-right: 16px;
        .tree-row-left {
          display: flex;
          flex-direction: row;
          align-items: center;
          .tree-img {
            width: 20px;
            height: 20px;
            // margin-right: 8px;
            background: #fff;
          }
          .svg-item {
            width: 20px;
            height: 20px;
            vertical-align: middle;
          }
        }
        .tree-row-right {
          display: none;
          .tree-row-handle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            margin-right: 8px;
            .svg-item {
              width: 18px;
              height: 18px;
              color: #666;
              vertical-align: middle;
              &:hover {
                color: var(--current-color);
              }
            }
          }
          .tree-row-handle:hover {
            background: rgba(0, 129, 255, 0.1);
            color: var(--current-color);
            width: 24px;
            height: 24px;
            border-radius: 6px 6px 6px 6px;
            margin-right: 8px;
          }
          .tree-row-handle:hover + .menu-list {
            display: block;
          }
          .menu-list:hover {
            display: block;
          }
          .menu-list {
            width: 100px;
            border-radius: 4px;
            z-index: 2000;
            color: #606266;
            font-size: 12px;
            cursor: pointer;
            transform: center top;
            margin-top: 1px; //这个别太高
            background-color: #fff;
            box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e6ebf5;
            position: absolute;
            right: -18px;
            margin-right: 24px;
            display: none;
            // &:after{
            //   content: "";
            //   position: absolute;
            //   top: -5px;
            //   right: 24px;
            //   transform: center top;
            //   width: 0;
            //   height: 0;
            //   border-top: 6px solid #edf6ff;
            //   border-left: 6px solid #edf6ff;
            //   border-right: 6px solid #edf6ff;
            //   border-bottom: 6px solid #ffffff;
            // }
            .opera-item {
              margin: 4px 0;
              height: 30px;
              line-height: 30px;
              padding: 0 16px;
              font-size: 14px;
              font-family:
                Helvetica Neue,
                Helvetica,
                PingFang SC,
                Hiragino Sans GB,
                Microsoft YaHei,
                Arial,
                sans-serif;
              font-weight: 400;
              color: #161d26;
              &:hover {
                background-color: #e8f4ff;
                color: #46a6ff;
                line-height: 30px;
                font-size: 14px;
                height: 30px;
              }
            }
          }
        }
        &:hover .tree-row-right {
          display: block;
        }
      }
      .active-tree {
        background: var(--current-color);
        color: #fff;
      }
    }
  }
  .right {
    flex: 1;
    background: #fff;
    height: auto;
    overflow: auto;
    position: relative;
    .right-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      .item-label {
        width: 100px;
        text-align: right;
      }
      .item-content {
        flex: 1;
        margin-left: 10px;
      }
    }
  }
  .tc-right {
    padding: 16px;
  }
  /*滚动条样式*/
  .right::-webkit-scrollbar {
    width: 4px;
  }
  .right::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .right::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
  .btn-next-step {
    display: flex;
    margin-bottom: 60px;
    justify-content: flex-end;
    margin-right: 10px;
    margin-top: -15px;
  }
}
// .dropdown {
//   background-color: #fff; // 背景颜色
//   border: 1px solid #f3f3f3;

//   .el-dropdown-menu__item {
//       color: #fff !important;    // 下拉菜单文字颜色
//   }
//   .popper__arrow::after { //箭头颜色
//     border-bottom-color: #0f93ee !important;
//     border-top-color: #0f93ee !important;
//   }
//   .el-dropdown-menu__item:not(.is-disabled):hover { //鼠标悬停样式
//       background-image: linear-gradient(90deg, rgba(0, 255, 255, 0.791) 0%, rgba(92, 250, 250, 0.169) 100%);
//       color: #fff;
//   }
// }
.addNode-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 325px;
  color: #fff;
  .left-clom {
    width: 280px;
    height: 100%;
    overflow: auto;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .flex-item {
      background: rgb(68, 114, 196);
      height: 55px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
    }
    .flex-item:hover {
      background: #46a6ff;
    }
    .active {
      background: #46a6ff;
    }
  }
  .right-clom {
    flex: 1;
    height: 100%;
    border-radius: 4px;
    background: rgb(68, 114, 196);
    font-size: 18px;
    display: flex;
    align-items: center;
    padding: 0px 10px;
  }
}
</style>
