<template>
  <el-form label-width="80px" size="mini">
    <el-form-item label="url">
      <el-input v-model="attributeCopy.url"></el-input>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'cpt-iframe-option'
});
const props = defineProps<{
  attribute: Record<string, any>;
}>();

const attributeCopy = reactive(props.attribute);
</script>

<style scoped></style>
