<!-- 读取shp转换成字段 -->
<template>
  <div>
    <el-dialog title="上传shp自动生成字段" v-model="dialogVisible" width="80%" :before-close="handleClose">
      <div class="dialog-row flex-row">
        <div class="flex-item">
          <div class="span">1、选择编码方式</div>
          <div class="end">
            <el-radio-group v-model="bmType">
              <el-radio label="utf-8">utf-8</el-radio>
              <el-radio label="gbk">gbk</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="flex-item">
          <div class="span">2、选择文件 <span style="color: red">(请选择.shp,.dbf,.prj,.shx文件)</span></div>
          <div class="end">
            <form enctype="multipart/form-data" method="post" id="uploadForm" ref="uploadForm">
              <div class="file-div">
                <label class="file-upload">
                  <input ref="fileInput" type="file" multiple accept=".shp,.dbf,.prj,.shx" />
                </label>
              </div>
            </form>
          </div>
        </div>
        <div class="flex-item">
          <div class="span">3、读取文件</div>
          <div class="end"><el-button type="primary" @click="submitUploadProject" size="small">读取</el-button></div>
        </div>
      </div>
      <!-- <div class="dialog-row">1、选择编码方式</div>
        <div class="dialog-row">
            <el-radio-group v-model="bmType">
                <el-radio label="utf-8">utf-8</el-radio>
                <el-radio label="gbk">gbk</el-radio>
            </el-radio-group>
        </div>
        <div class="dialog-row">2、选择文件 <span style="color:red">(请选择.shp,.dbf,.prj,.shx文件)</span></div>
        <form
            enctype="multipart/form-data"
            method="post"
            id="uploadForm"
            ref="uploadForm"
        >
            <div class="file-div">
            <label class="file-upload">
                <input ref="file" type="file" multiple accept=".shp,.dbf,.prj,.shx">
            </label>
            </div>
        </form>
        <div class="dialog-row" style="margin-top:10px">3、读取文件</div>
        <div class="dialog-row">
            <el-button type="primary" @click="submitUploadProject" size="mini">读取</el-button>
        </div> -->
      <div class="dialog-row">4、字段预览及修改</div>
      <el-table :data="shpFields" style="width: 100%" border :height="tableHeight" ref="multipleTable" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="shp字段名" prop="shpFieldName"></el-table-column>
        <el-table-column label="字段名">
          <template v-slot="scope">
            <el-input
              v-model="scope.row.vModel"
              placeholder="请输入字段名"
              oninput="value = value.replace(/[_]/g, '')"
              maxlength="10"
              @input="(event) => handleInputName(event, scope.$index)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="字段别名" prop="label">
          <template v-slot="scope">
            <el-input v-model="scope.row.label" placeholder="请输入别名" maxlength="200"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="字段类型">
          <template v-slot="scope">
            <el-select v-model="scope.row.tagIcon" placeholder="请选择">
              <el-option v-for="item in commonComponents" :key="item.tagIcon" :label="item.label" :value="item.tagIcon"> </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import proj4 from 'proj4';
import { read as shapeRead } from 'shapefile';
import {
  inputComponents,
  selectComponents,
  layoutComponents,
  commonComponents as importedCommonComponents,
  formConf,
  customComponents
} from './components/generator/config';

// 定义Props类型
interface Props {
  drawingList: any[];
  fieldShpDialog: boolean;
}
const dialogVisible = computed({
  get() {
    return props.fieldShpDialog;
  },
  set(value) {}
});

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'handlCloseFieldDialog'): void;
  (e: 'submitShpField', list: any[]): void;
}>();

// 响应式数据
const bmType = ref('utf-8'); // 默认编码方式gbk
const shpFields = ref<any[]>([]); // 字段列表
const commonComponents = ref<any[]>([]); // 所有的字段类型
const defaultField = ref<any>({}); // 默认为input字符串输入框
const tableHeight = ref(window.innerHeight - 300);
const multipleSelection = ref<any[]>([]);
const fileInput = ref<HTMLInputElement | null>(null);
const multipleTable = ref<any>();

// 生命周期钩子
onMounted(() => {
  importedCommonComponents.forEach((v: any) => {
    if (!v.vModel) {
      commonComponents.value.push(v);
    }
  });
});

// 方法定义
const handleClose = () => {
  emit('handlCloseFieldDialog');
};

const submitUploadProject = () => {
  const files = fileInput.value?.files;

  if (!files) return;
  parseShapefile(Array.from(files));
};

const parseShapefile = async (files: File[]) => {
  if (files.length === 0) {
    ElMessage.error('请选择文件!!!');
    return;
  }
  if (files.length !== 4) {
    ElMessage.error('请选择正确的文件!!!');
    return;
  }

  const shpFile = files.find((f) => f.name.endsWith('.shp'));
  const dbfFile = files.find((f) => f.name.endsWith('.dbf'));
  const prjFile = files.find((f) => f.name.endsWith('.prj'));

  if (!shpFile || !dbfFile || !prjFile) {
    ElMessage.error('文件类型不完整!!!');
    return;
  }

  const promises = [shpFile, dbfFile].map((i) => readInputFile(i));
  promises.push(readInputFile(prjFile, 'Text'));

  let prjCrs: proj4.Proj;
  try {
    const [shp, dbf, prj] = await Promise.all(promises);
    prjCrs = new proj4.Proj(prj as string);
    const source = await shapeRead(shp as ArrayBuffer, dbf as ArrayBuffer, { encoding: bmType.value });

    if (!source.features || source.features.length === 0) {
      ElMessage.error('没有数据，请重新上传!!!');
      return;
    }

    const fields = Object.keys(source.features[0].properties);
    const inputComponent = commonComponents.value.find((c: any) => c.tagIcon === 'input');
    if (inputComponent) defaultField.value = inputComponent;

    shpFields.value = fields.map((v, index) => ({
      tagIcon: 'input',
      vModel: v,
      label: v,
      placeholder: `请输入${v}`,
      shpFieldName: v,
      layout: 'colFormItem',
      formId: generateUniqueId(index)
    }));

    shpFields.value.forEach((row) => {
      multipleTable.value?.toggleRowSelection(row);
    });
  } catch (error) {
    ElMessage.error('文件解析失败!!!');
  }
};

const readInputFile = (file: File, type: 'ArrayBuffer' | 'Text' = 'ArrayBuffer') => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;

    switch (type) {
      case 'ArrayBuffer':
        reader.readAsArrayBuffer(file);
        break;
      case 'Text':
        reader.readAsText(file);
        break;
    }
  });
};

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

const handleInputName = (e: InputEvent, index: number) => {
  const value = (e.target as HTMLInputElement).value;
  shpFields.value[index].vModel = value.replace(/[^a-zA-Z0-9]/g, '');
  if (shpFields.value[index].vModel.length > 10) {
    shpFields.value[index].vModel = value.slice(0, 10);
  }
};

const submit = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.error('请选择您要生成的字段！！！');
    return;
  }

  const list = multipleSelection.value.map((v) => {
    const component = commonComponents.value.find((c: any) => c.tagIcon === v.tagIcon);
    return {
      ...component,
      vModel: v.vModel,
      label: v.label,
      shpFieldName: v.shpFieldName,
      layout: 'colFormItem',
      formId: v.formId,
      placeholder: v.tagIcon === 'select' ? `请选择${v.label}` : `请输入${v.label}`
    };
  });

  emit('submitShpField', list);
  initDialog();
};

const initDialog = () => {
  multipleSelection.value = [];
  multipleTable.value?.clearSelection();
  shpFields.value = [];
  if (fileInput.value) fileInput.value.value = '';
};

const generateUniqueId = (index: number) => {
  return props.drawingList.length > 0 ? props.drawingList.length + index + 1 : index + 1;
};
</script>
<style lang="scss" scoped>
.dialog-row {
  margin-bottom: 10px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  .flex-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 20px;
    .span {
      margin-right: 10px;
    }
    .end {
      flex: 1;
    }
  }
}
</style>
