<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-tooltip class="item" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top" v-if="search">
        <el-button size="small" circle @click="toggleSearch()">
          <el-icon><Search /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button size="small" circle @click="refresh()">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="columns">
        <el-dropdown trigger="click" :hide-on-click="false">
          <span class="el-dropdown-link">
            <el-button size="small" circle style="margin-left: 8px">
              <el-icon><Menu /></el-icon>
            </el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <div class="text-tip">请选择列表中要展示的信息</div>
              <el-checkbox style="margin: 15px" :indeterminate="isIndeterminate" v-model="isCheckAll" @change="handleCheckAllChange"
                >全选</el-checkbox
              >
              <el-dropdown-item v-for="item in columnsRef" :key="item.key">
                <el-checkbox-group v-model="columnsCheckList" @change="changeColumns">
                  <el-checkbox :value="item.key">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-tooltip>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, PropType } from 'vue';
import { Search, Refresh, Menu } from '@element-plus/icons-vue';

// 定义列接口
interface Column {
  key: string;
  label: string;
  visible: boolean;
}

// Props 定义
const props = defineProps({
  showSearch: {
    type: Boolean,
    default: true
  },
  columns: {
    type: Array as PropType<Column[]>,
    default: () => []
  },
  search: {
    type: Boolean,
    default: true
  },
  gutter: {
    type: Number,
    default: 10
  }
});

// Emits 定义
const emit = defineEmits(['update:showSearch', 'queryTable', 'update:columns']);

// 响应式数据
const columnsRef = ref<Column[]>(props.columns ? [...props.columns] : []);
const columnsCheckList = ref<string[]>([]);
const isCheckAll = ref(false);
const isIndeterminate = ref(true);

// 计算属性
const style = computed(() => {
  const ret: Record<string, string> = {};
  if (props.gutter) {
    ret.marginRight = `${props.gutter / 2}px`;
  }
  return ret;
});

// 监视 props.columns 变化，更新内部 columnsRef
watch(
  () => props.columns,
  (newVal) => {
    if (newVal) {
      columnsRef.value = [...newVal];
    }
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  // 显隐列初始默认隐藏列
  if (columnsRef.value && columnsRef.value.length > 0) {
    columnsRef.value.forEach((item) => {
      if (item.visible === true) {
        columnsCheckList.value.push(item.key);
      }
    });
  }
});

// 搜索
const toggleSearch = () => {
  emit('update:showSearch', !props.showSearch);
};

// 刷新
const refresh = () => {
  emit('queryTable');
};

// 右侧列表元素变化
const dataChange = (data: string[]) => {
  for (let i = 0; i < columnsRef.value.length; i++) {
    const key = columnsRef.value[i].key;
    columnsRef.value[i].visible = !data.includes(key);
  }
};

// 改变列显示
const changeColumns = (val: string[]) => {
  for (let i = 0; i < columnsRef.value.length; i++) {
    const key = columnsRef.value[i].key;
    columnsRef.value[i].visible = val.includes(key);
  }
  const leng = val.length;
  isCheckAll.value = leng === columnsRef.value.length;
  isIndeterminate.value = leng > 0 && leng < columnsRef.value.length;
  // 同步更改回父组件
  emit('update:columns', columnsRef.value);
};

// 全选按钮
const handleCheckAllChange = (val: boolean) => {
  if (val) {
    columnsCheckList.value = [];
    columnsRef.value.forEach((item) => {
      columnsCheckList.value.push(item.key);
    });
    changeColumns(columnsCheckList.value);
  } else {
    columnsCheckList.value = [];
    changeColumns(columnsCheckList.value);
  }
  isIndeterminate.value = false;
};
</script>

<style lang="scss" scoped>
:deep(.el-transfer__button) {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
:deep(.el-transfer__button:first-child) {
  margin-bottom: 10px;
}

.text-tip {
  margin-left: 8px;
  margin-right: 8px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: 0em;

  /* 浅灰色 */
  color: #8291a9;
}
</style>
