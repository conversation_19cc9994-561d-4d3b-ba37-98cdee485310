<!-- 映射数据源弹窗 -->
<template>
  <div class="mapField-main">
    <el-dialog title="选择映射源" v-model="dialogVisible" width="720px" :close-on-click-modal="false" :before-close="handleClose">
      <div class="dialog-box">
        <div class="left">
          <div class="title-div"><span class="normal-sapn">结构树</span></div>
          <div class="content">
            <el-tree
              ref="tree"
              :data="treeList"
              :props="defaultProps"
              highlight-current
              default-expand-all
              node-key="id"
              :current-node-key="defaultExpand"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </div>
        </div>
        <div class="center">
          <div class="title-div"><span class="normal-sapn">属性组</span></div>
          <div class="content">
            <div v-if="attrbutionGroup.length == 0" class="empty-span">暂无属性组数据</div>
            <template v-else>
              <div
                class="flex-row check-item"
                v-for="(item, index) in attrbutionGroup"
                :class="{ 'flex-active': item.checked }"
                :key="index"
                @click="changeAtt(item)"
              >
                <div class="label">
                  {{ item.typeName }}
                  <span v-show="item.ruleAttribution && (item.ruleAttribution.type == 'graphicalPoint' || item.ruleAttribution.type == 'commonPoint')"
                    >(点)</span
                  >
                  <span
                    v-show="item.ruleAttribution && (item.ruleAttribution.type == 'graphicalLine' || item.ruleAttribution.type == 'graphicalLine')"
                    >(线)</span
                  >
                </div>
                <div class="ico">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="right">
          <div class="title-div"><span class="normal-sapn">字段</span></div>
          <div class="content">
            <div v-if="fieldList.length == 0" class="empty-span">暂无字段数据</div>
            <template v-else>
              <div v-for="(item, index) in fieldList" :key="index">
                <!-- 表格 -->
                <!-- 表格字段 -->
                <div class="flex-row-spe" v-if="item.valueMethod == 'xttable'">
                  <div class="spe-title">
                    <!-- 大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                  <div
                    class="spe-item"
                    :class="{ 'spe-item-active': ite.checked, 'no-span': ite.valueMethod != 'upload' && ite.valueMethod != 'xtfj' }"
                    v-for="(ite, idx) in item.attribution.children"
                    :key="idx"
                    @click="changeFieldTable(item, ite)"
                  >
                    <!-- 身份证识别中的某个字段 -->
                    <div>
                      <span>{{ ite.fieldName }}</span
                      ><span style="padding-left: 4px">({{ ite.fieldCn }})</span>
                    </div>
                  </div>
                </div>
                <!-- 身份证识别 -->
                <div v-else-if="item.valueMethod == 'idCardScan'" class="flex-row-spe">
                  <div class="spe-title">
                    <!-- 大标题 -->
                    {{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})
                  </div>
                </div>
                <div
                  v-else
                  class="flex-row check-item"
                  @click="changeField(item)"
                  :class="{ 'no-span': item.valueMethod != 'upload' && item.valueMethod != 'xtfj', 'flex-active': checkedField.id == item.id }"
                >
                  <div class="label">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldCn }})</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitField">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { selectRules } from '@/api/modal/index';
import { ElMessage } from 'element-plus'; // 假设使用的是 Element UI
import { useModalStore } from '@/store/modules/modal';
import { useRouter } from 'vue-router';
import { object } from 'vue-types';

const modalStore = useModalStore();
const router = useRouter();

// 定义 props
const props = defineProps<{
  souseFieldDialog: boolean;
  sourceField: any;
}>();

const dialogVisible = computed({
  get() {
    return props.souseFieldDialog;
  },
  set(value) {
    // 触发关闭事件
    emit('closeSouseFieldDialog');
  }
});

// 定义 emits
const emit = defineEmits(['closeSouseFieldDialog', 'submitSouseField']);

// 定义响应式数据
const treeList = ref<any[]>([]);
const defaultProps = ref({
  children: 'list',
  label: 'typeName'
});
const attrbutionGroup = ref<any[]>([]);
const fieldList = ref<any[]>([]);
const checkedYS = ref<any>(null);
const checkedField = ref<any>({});
const fieldGroupModelList = ref<any>({});
const defaultExpand = ref(0);
const tree = ref<any>(null);

// 计算属性
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = Number(router.currentRoute.value.query.id);
  }
  return moduleId;
});

// 监听 souseFieldDialog
watch(
  () => props.souseFieldDialog,
  (val) => {
    if (val) {
      getTree();
    }
  },
  { deep: true }
);

// 方法
const getTree = () => {
  const params = {
    moduleId: moduleId.value
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = router.currentRoute.value.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  selectRules(params).then((res) => {
    if (res.code === 200) {
      treeList.value = res.data;
      if (props.sourceField && props.sourceField.id) {
        // 代表需要反显
        init();
      } else {
        attrbutionGroup.value = res.data[0].fieldGroupModelList;
        checkedYS.value = res.data[0];
        nextTick(() => {
          // selectId：绑定的 node-key
          if (tree.value) {
            tree.value.setCurrentKey(res.data[0].id);
          }
        });
        defaultExpand.value = res.data[0].id;
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleClose = () => {
  fieldList.value = [];
  attrbutionGroup.value = [];
  emit('closeSouseFieldDialog');
};

const submitField = () => {
  if (Object.keys(checkedField.value).length === 0) {
    ElMessage.error('请选择字段!!!');
    return;
  }
  checkedYS.value.fieldGroupModelList = [];
  fieldGroupModelList.value.fieldModelList = [];
  fieldGroupModelList.value.fieldModelList.push(checkedField.value);
  checkedYS.value.fieldGroupModelList.push(fieldGroupModelList.value);
  const checkedYSClone = JSON.parse(JSON.stringify(checkedYS.value));
  // 表格特殊处理
  if (checkedYSClone.fieldGroupModelList[0].fieldModelList[0].valueMethod === 'xttable') {
    const children = [];
    for (let i = 0; i < checkedYSClone.fieldGroupModelList[0].fieldModelList[0].attribution.children.length; i++) {
      if (checkedYSClone.fieldGroupModelList[0].fieldModelList[0].attribution.children[i].checked) {
        children.push(checkedYSClone.fieldGroupModelList[0].fieldModelList[0].attribution.children[i]);
        break;
      }
    }
    checkedYSClone.fieldGroupModelList[0].fieldModelList[0].attribution.children = children;
  }
  emit('submitSouseField', checkedYSClone, checkedField.value.fieldCn);
  fieldList.value = [];
  attrbutionGroup.value = [];
};

const handleNodeClick = (data: any) => {
  attrbutionGroup.value = data.fieldGroupModelList;
  checkedYS.value = data;
  fieldList.value = [];
};

const changeAtt = (item: any) => {
  fieldList.value = item.fieldModelList;
  attrbutionGroup.value.forEach((v) => {
    v.checked = false;
  });
  item.checked = true;
};

const changeField = (item: any) => {
  if (item.valueMethod === 'upload' || item.valueMethod === 'xtfj') {
    // 只能选择图片
    checkedField.value = item;
    // 选中字段的时候就赋值对应的属性组
    attrbutionGroup.value.forEach((v) => {
      if (v.checked) {
        fieldGroupModelList.value = v;
      }
    });
  }
};

const init = () => {
  const ysID = props.sourceField.id;
  const groupId = props.sourceField.fieldGroupModelList[0].id;
  const fieldId = props.sourceField.fieldGroupModelList[0].fieldModelList[0].id;
  defaultExpand.value = ysID;
  // 默认高亮树
  nextTick(() => {
    if (tree.value) {
      tree.value.setCurrentKey(ysID);
    }
  });
  getGroupAttr(treeList.value, groupId, fieldId);
};

const getGroupAttr = (list: any[], groupId: any, fieldId: any) => {
  for (let index = 0; index < list.length; index++) {
    if (list[index].id === defaultExpand.value) {
      attrbutionGroup.value = list[index].fieldGroupModelList;
      checkedYS.value = list[index];
      attrbutionGroup.value.forEach((v) => {
        v.checked = false;
        if (v.id === groupId) {
          v.checked = true;
          fieldGroupModelList.value = v;
          fieldList.value = v.fieldModelList;
          fieldList.value.forEach((k) => {
            k.checked = false;
            if (k.id === fieldId) {
              k.checked = true;
              checkedField.value = k;
            }
          });
        }
      });
      return;
    }
    if (list[index].list.length !== 0) {
      getGroupAttr(list[index].list, groupId, fieldId);
    }
  }
};

const changeFieldTable = (item: any, ite: any) => {
  if (ite.valueMethod === 'upload' || ite.valueMethod === 'xtfj') {
    ite.checked = true;
    checkedField.value = item;

    attrbutionGroup.value.forEach((v) => {
      if (v.checked) {
        fieldGroupModelList.value = v;
      }
    });
  }
};
</script>
<style lang="scss" scoped>
.mapField-main {
  .dialog-box {
    height: 300px;
    border: 1px solid rgba(219, 231, 238, 1);
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    .left {
      flex: 2;
    }
    .center {
      flex: 2;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .right {
      flex: 3;
      border-left: 1px solid rgba(219, 231, 238, 1);
    }
    .title-div {
      width: 100%;
      height: 37px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: bold;
      .normal-sapn {
        margin-left: 20px;
      }
    }
    .content {
      height: calc(100% - 37px);
      padding: 0px 8px;
      width: calc(100% - 16px);
      margin-left: 8px;
      overflow: auto;
      :deep(&) {
        .el-tree-node__content {
          height: 32px;
          font-size: 12px;
        }
      }
      .empty-span {
        color: #909399;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      .flex-row {
        min-height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        color: rgba(22, 29, 38, 1);
        cursor: pointer;
        .label {
          margin-top: 6px;
          font-size: 12px;
          padding-left: 12px;
        }
        .ico {
          margin-top: 6px;
          padding-right: 8px;
        }
      }
      .flex-row:hover {
        background-color: #f5f7fa;
      }
      .flex-active {
        background: #edf4fb;
      }
      .no-span {
        color: #d3d3d3 !important;
        cursor: not-allowed;
      }
      .check-item {
        height: auto;
        align-items: flex-start;
      }
      .flex-row-spe {
        height: auto;
        .spe-title {
          color: #d3d3d3;
          padding-left: 12px;
          cursor: not-allowed;
        }
        .spe-item {
          height: 32px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          font-size: 12px;
          padding-left: 24px;
        }
        .spe-item:hover {
          background: #edf4fb;
        }
        .spe-item-active {
          background: #edf4fb;
        }
      }
    }
    /*滚动条样式*/
    .content::-webkit-scrollbar {
      width: 4px;
    }
    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(255, 255, 255, 0.5);
    }
    .content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
