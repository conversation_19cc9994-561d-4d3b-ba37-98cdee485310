<!-- 新建数据大屏 -->
<template>
  <div>
    <el-row class="top">
      <el-col :span="6" :offset="1">
        <span>数据大屏设计</span>
      </el-col>
      <el-col :span="16" @click.self="outBlur">
        <div class="flex items-center justify-end">
          <div class="text-14px mx-10px cursor-pointer" @click="importDesign">导入</div>
          <div class="flex items-center mx-10px">
            <el-dropdown @command="exportCommand">
              <span class="el-dropdown-link">
                导出
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="img">图片</el-dropdown-item>
                  <el-dropdown-item command="json">设计文件</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <el-icon class="configBtn mx-10px" @click="clearDesign">
            <Delete />
          </el-icon>
          <el-button @click="submitDesign" class="save-btn">保存</el-button>
          <el-button @click="preview" class="preview-btn">预览</el-button>
        </div>
      </el-col>
    </el-row>
    <div :style="{ height: windowHeight - 45 + 'px', background: 'url(' + portImg + ') repeat' }" @click.self="outBlur">
      <div style="float: left; height: 100%; overflow: hidden" :style="{ width: cptBarWidth + 'px' }">
        <!--左侧组件栏-->
        <component-bar
          @dragStart="dragStart"
          :selectedComponents="cacheComponents"
          :currentCptIndex="currentCptIndex"
          @showConfigBar="showConfigBar"
          @copyCpt="copyCpt"
          @delCpt="delCpt"
        />
      </div>
      <div
        class="content"
        style="float: left; position: relative; overflow: auto; height: 100%"
        :style="{ width: windowWidth - cptBarWidth - configBarWidth + 24 + 'px' }"
        @click.self="outBlur"
      >
        <!--顶部刻度线-->
        <div style="height: 10px; margin-left: 10px" :style="{ width: 1920 * containerScale + 'px' }"><ScaleMarkX /></div>
        <!--左侧刻度线-->
        <div style="position: absolute; width: 10px" :style="{ height: ((1920 * containerScale) / designData.scaleX) * designData.scaleY + 'px' }">
          <ScaleMarkY />
        </div>
        <div
          class="webContainer"
          :style="{
            width: designData.scaleX + 'px',
            height: designData.scaleY + 'px',
            backgroundColor: designData.bgColor,
            backgroundImage: designData.bgImg ? 'url(' + fileUrl + designData.bgImg + '?token=' + token + ')' : 'none',
            transform: 'scale(' + containerScale + ')'
          }"
          @dragover="allowDrop"
          @drop="drop"
          ref="webContainerRef"
          @click.self="outBlur"
        >
          <div
            v-for="(item, index) in cacheComponents"
            :key="index"
            class="cptDiv"
            :style="{
              width: Math.round(item.cptWidth) + 'px',
              height: Math.round(item.cptHeight) + 'px',
              top: Math.round(item.cptY) + 'px',
              left: Math.round(item.cptX) + 'px',
              zIndex: currentCptIndex === index ? 1800 : item.cptZ
            }"
            :ref="'div' + item.cptKey + index"
            @mousedown="showConfigBar($event, item, index)"
            tabindex="0"
          >
            <div v-show="currentCptIndex === index" style="position: fixed; border-top: 1px dashed #8a8a8a; width: 100%; left: 0" />
            <!--顶部辅助线-->
            <div v-show="currentCptIndex === index" style="position: fixed; border-right: 1px dashed #8a8a8a; height: 100%; top: 0" />
            <!--左侧辅助线-->
            <!-- 2021-12-28新增iframe组件，防止焦点聚焦在iframe内部，添加此蒙版 -->
            <div v-resize="'move'" class="activeMask" :style="cacheChoices[item.id] ? { border: '1px solid #B6BFCE' } : {}" />
            <div style="width: 100%; height: 100%">
              <component
                :is="item.cptKey"
                :ref="item.cptKey + index"
                :width="Math.round(item.cptWidth) || 20"
                @changeTaskId="changeTaskId"
                :height="Math.round(item.cptHeight) || 20"
                :option="item.cptOption"
              />
            </div>
            <div class="delTag">
              <el-icon class="el-icon-copy-document" @click.stop="copyCpt(item)">
                <CopyDocument />
              </el-icon>
              <el-icon class="el-icon-delete" @click.stop="delCpt(item, index)">
                <Delete />
              </el-icon>
            </div>
            <div
              v-show="currentCptIndex === index"
              style="top: 0; left: 0; cursor: se-resize; transform: translate(-50%, -50%)"
              class="resizeTag"
              v-resize="'lt'"
            />
            <div
              v-show="currentCptIndex === index"
              style="top: 0; left: 50%; cursor: s-resize; transform: translate(-50%, -50%)"
              class="resizeTag"
              v-resize="'t'"
            />
            <div
              v-show="currentCptIndex === index"
              style="top: 0; right: 0; cursor: ne-resize; transform: translate(50%, -50%)"
              class="resizeTag"
              v-resize="'rt'"
            />
            <div
              v-show="currentCptIndex === index"
              style="top: 50%; right: 0; cursor: w-resize; transform: translate(50%, -50%)"
              class="resizeTag"
              v-resize="'r'"
            />
            <div
              v-show="currentCptIndex === index"
              style="bottom: 0; right: 0; cursor: se-resize; transform: translate(50%, 50%)"
              class="resizeTag"
              v-resize="'rb'"
            />
            <div
              v-show="currentCptIndex === index"
              style="bottom: 0; left: 50%; cursor: s-resize; transform: translate(-50%, 50%)"
              class="resizeTag"
              v-resize="'b'"
            />
            <div
              v-show="currentCptIndex === index"
              style="bottom: 0; left: 0; cursor: ne-resize; transform: translate(-50%, 50%)"
              class="resizeTag"
              v-resize="'lb'"
            />
            <div
              v-show="currentCptIndex === index"
              style="top: 50%; left: 0; cursor: w-resize; transform: translate(-50%, -50%)"
              class="resizeTag"
              v-resize="'l'"
            />
          </div>
        </div>
        <div style="position: fixed; width: 120px; height: 30px; bottom: 40px; left: 220px">
          <el-slider v-model="containerScale" :min="0.3" :max="2" :step="0.01" />
        </div>
      </div>
      <div
        style="float: right; height: calc(100% - 48px); overflow: hidden; position: absolute; right: 0px"
        :style="{ width: configBarWidth - 6 + 'px' }"
      >
        <!--右侧属性栏-->
        <config-bar
          ref="configBarRef"
          :appTypeOptions="appTypeOptions"
          :currentCpt="currentCpt"
          :designData="designData"
          @refreshCptData="refreshCptData"
          :height="windowHeight"
          @initCptConfig="initCptConfig"
        />
      </div>
    </div>
    <input v-show="false" type="file" id="files" ref="fileRef" @change="fileLoad" accept=".cd" />
  </div>
</template>

<script lang="ts" setup>
import { v1 as uuidv1 } from 'uuid';
// 组件部分
import ComponentBar from './componentBar.vue';
import ConfigBar from './configBar.vue';
import ScaleMarkX from './scaleMark/ScaleMarkX.vue';
import ScaleMarkY from './scaleMark/ScaleMarkY.vue';
import html2canvas from 'html2canvas';
import { fileDownload } from '@/utils/FileUtil';
import { clearCptInterval } from '@/utils/refreshCptData';
import { getToken } from '@/utils/auth';
import { saveScreen, getScreenById as getScreenByIdApi } from '@/api/dataScreen';
import { getModuleList } from '@/api/modal';
import { useUserStore } from '@/store/modules/user';
import { useRoute } from 'vue-router';
import { getCurrentInstance } from 'vue';
const route = useRoute();
const userStore = useUserStore();
const version = '0.9.0';
const active = 'dev';
import portImg from '@/assets/images/port.png';

// ---props---
const props = defineProps<{
  moduleId: string | number;
  id: string | number;
}>();

// --- 定义变量 ---
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const windowWidth = ref(0);
const windowHeight = ref(0);
const fileUrl = ref(baseUrl.value + '/qjt/file/otherDownload/');
const cptBarWidth = ref(200);
const configBarWidth = ref(300);
const copyDom: any = ref('');
const proxy = getCurrentInstance()?.proxy as any;
const designData: any = ref({
  id: '',
  title: '我的大屏',
  scaleX: 1920,
  scaleY: 1080,
  version: '',
  taskId: '',
  bgColor: '#2B3340',
  simpleDesc: '',
  bgImg: '',
  viewCode: '',
  components: []
});

const cacheComponents: any = ref([]);
const currentCptIndex = ref(-1);
const currentCpt: any = ref({
  cptOption: {
    interaction: {
      title: '', //交互标题
      dataSource: 1, //1 静态数据 2表达式
      dataText: '',
      expression: '', //表达式
      contentList: [] //显示内容
    }
  }
});
const containerScale = ref(1);
const cacheChoices: any = ref({});
const cacheChoicesFixed: any = ref({}); //记录移动前选中组件的位置 自定义事件内部无法处理，放在了外面。
const appTypeOptions = ref([]);
const token = getToken();
const webContainerRef = ref(null);
const fileRef = ref<any>(null);
const id = ref(props.id);
const configBarRef = ref<any>(null);

// 定义 v-resize 指令
const vResize = {
  mounted(el: any, binding: DirectiveBinding<string>) {
    el.onmousedown = function (e) {
      const scaleClientX = e.clientX / containerScale.value;
      const scaleClientY = e.clientY / containerScale.value;
      const rbX = scaleClientX - el?.parentNode?.offsetWidth;
      const rbY = scaleClientY - el?.parentNode?.offsetHeight;
      const ltX = scaleClientX + el?.parentNode?.offsetWidth;
      const ltY = scaleClientY + el?.parentNode?.offsetHeight;
      const disX = scaleClientX - el?.parentNode?.offsetLeft;
      const disY = scaleClientY - el?.parentNode?.offsetTop;
      let cptWidth: number, cptHeight: number, cptX, cptY;

      document.onmousemove = function (me) {
        const meScaleClientX = me.clientX / containerScale.value;
        const meScaleClientY = me.clientY / containerScale.value;
        if (binding.value === 'move') {
          cptX = meScaleClientX - disX;
          cptY = meScaleClientY - disY;
          Object.keys(cacheChoices.value).forEach((key) => {
            cacheChoices.value[key].cptX = cacheChoicesFixed.value[key].cptX + Math.round(meScaleClientX - scaleClientX);
            cacheChoices.value[key].cptY = cacheChoicesFixed.value[key].cptY + Math.round(meScaleClientY - scaleClientY);
          });
        } else {
          switch (binding.value) {
            case 'lt':
              cptWidth = ltX - meScaleClientX;
              cptHeight = ltY - meScaleClientY;
              cptX = meScaleClientX - disX;
              cptY = meScaleClientY - disY;
              currentCpt.value.cptX = Math.round(cptX);
              currentCpt.value.cptY = Math.round(cptY);
              break;
            case 't':
              cptHeight = ltY - meScaleClientY;
              cptY = meScaleClientY - disY;
              currentCpt.value.cptY = Math.round(cptY);
              break;
            case 'rt':
              cptWidth = meScaleClientX - rbX;
              cptHeight = ltY - meScaleClientY;
              cptY = meScaleClientY - disY;
              currentCpt.value.cptY = Math.round(cptY);
              break;
            case 'r':
              cptWidth = meScaleClientX - rbX;
              break;
            case 'rb':
              cptWidth = meScaleClientX - rbX;
              cptHeight = meScaleClientY - rbY;
              break;
            case 'b':
              cptHeight = meScaleClientY - rbY;
              break;
            case 'lb':
              cptWidth = ltX - meScaleClientX;
              cptHeight = meScaleClientY - rbY;
              cptX = meScaleClientX - disX;
              currentCpt.value.cptX = Math.round(cptX);
              break;
            case 'l':
              cptWidth = ltX - meScaleClientX;
              cptX = meScaleClientX - disX;
              currentCpt.value.cptX = Math.round(cptX);
              break;
          }
          cptWidth = cptWidth < 40 ? 40 : cptWidth; //限制最小缩放
          cptHeight = cptHeight < 20 ? 20 : cptHeight;
          if (cptWidth) currentCpt.value.cptWidth = Math.round(cptWidth);
          if (cptHeight) currentCpt.value.cptHeight = Math.round(cptHeight);
        }
      };
      document.onmouseup = function () {
        document.onmousemove = document.onmouseup = null;
        cacheChoicesFixed.value = JSON.parse(JSON.stringify(cacheChoices.value)); //解决多选移动未松开ctrl键第二次以后拖动定位还原
      };
      return false;
    };
  }
};

// 创建 BroadcastChannel 实例，频道名称要和原窗口一致
const channel = new BroadcastChannel('dataScreenChannel');

// --- 定义方法 ---
const initCptConfig = () => {
  currentCpt.value = {
    cptOption: {
      interaction: {
        title: '', //交互标题
        dataSource: 1, //1 静态数据 2表达式
        dataText: '',
        expression: '', //表达式
        contentList: [] //显示内容
      }
    }
  };
};
/**
 * 获取所有模块列表
 */
const getModelList = () => {
  getModuleList([1, 8]).then((res) => {
    if (res.code == 200) {
      appTypeOptions.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const initContainerSize = () => {
  windowWidth.value = document.documentElement.clientWidth;
  windowHeight.value = document.documentElement.clientHeight;
  const tempWidth = windowWidth.value - cptBarWidth.value - configBarWidth.value;
  containerScale.value = Math.round((tempWidth / designData.value.scaleX) * 100) / 100;
};

const exportCommand = (command: string) => {
  if (command === 'img') {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    html2canvas(webContainerRef.value as any, {
      backgroundColor: '#49586e'
    })
      .then((canvas) => {
        const canvasData = canvas.toDataURL('image/jpeg');
        fileDownload(canvasData, designData.value.title + '.png');
        loading.close();
      })
      .catch(() => {
        loading.close();
      });
  } else if (command === 'json') {
    designData.value.components = cacheComponents.value;
    designData.value.version = version;
    const data = JSON.stringify(designData.value);
    const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data); //encodeURIComponent解决中文乱码
    fileDownload(uri, designData.value.title + '.cd');
  }
};

const importDesign = () => {
  fileRef.value?.dispatchEvent(new MouseEvent('click'));
};

const fileLoad = () => {
  const selectedFile = fileRef.value.files[0];
  const reader = new FileReader();
  reader.readAsText(selectedFile);
  reader.onload = function () {
    const fileJson = JSON.parse(this.result as any); //文件大小、合法性待校验

    if (!fileJson.version || fileJson.version !== version) {
      ElMessage.error('导入失败，与当前版本不一致');
    } else {
      fileJson.id = designData.value.id;
      designData.value = fileJson;
      cacheComponents.value = fileJson.components;
      designData.value.components = [];
      ElMessage.success('导入成功');
      // 导入的时候需要查询当前公司的id 并赋值进去
      designData.value.companyId = userStore.user?.companyId;
    }
  };
  fileRef.value.value = '';
};

const clearDesign = () => {
  ElMessageBox.confirm('此操作将会清空图层，是否继续？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      cacheComponents.value = [];
      designData.value.components = [];
      currentCpt.value = {};
      localStorage.removeItem('designCache');
      clearCptInterval(null, true);
      ElMessage.success('清除成功');
    })
    .catch(() => {});
};

const loadCacheData = () => {
  if (id.value && id.value != 0) {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    getScreenByIdApi(id.value, '0').then((res) => {
      if (res.code == 200) {
        designData.value = res.data;
        cacheComponents.value = JSON.parse(designData.value.components);
        if (!cacheComponents.value) {
          cacheComponents.value = [];
        }
        designData.value.components = [];
        initContainerSize();
        loading.close();
      } else {
        ElMessage.error(res.msg);
      }
    });
    loading.close();
  } else {
    cacheComponents.value = [];
    designData.value.components = [];
    initContainerSize();
  }
};

const copyCpt = (item: { cptX: number; cptY: number }) => {
  const copyItem = JSON.parse(JSON.stringify(item));
  copyItem.cptX = item.cptX + 30; //复制的组件向右下偏移
  copyItem.cptY = item.cptY + 30;
  copyItem.id = uuidv1();
  cacheComponents.value.push(copyItem);
  currentCptIndex.value = cacheComponents.value.length - 1; //聚焦到复制的组件
};

function refreshCptData() {
  const refName = currentCpt.value.cptKey + currentCptIndex.value;

  if (!proxy.$refs[refName][0]?.refreshCptData) {
    ElMessage.warning('当前图层还未实现refreshCptData方法');
  } else {
    proxy.$refs[refName][0].refreshCptData(); //刷新子组件数据，refs为组建名加index
  }
}

const outBlur = () => {
  //取消聚焦组件
  currentCptIndex.value = -1;
  currentCpt.value = {};
  cacheChoices.value = {};
};

const submitDesign = () => {
  if (isCheckSomeComponentNoExpress(cacheComponents.value)) {
    return;
  }

  //保存
  designData.value.moduleId = props.moduleId;
  let flg = true;
  // 如果有导入进来的数据 需要验证code是否正确
  for (let i = 0; i < cacheComponents.value.length; i++) {
    if (cacheComponents.value[i].cptOption.cptDataForm && cacheComponents.value[i].cptOption.cptDataForm.isOk) {
      if (cacheComponents.value[i].cptOption.cptDataForm.isOk == 1) {
        flg = false;
        break;
      }
    }
  }
  if (!flg) {
    ElMessage.error('有组件绑定的模块未找到，请核查清楚再保存！！！');
    return;
  }
  designData.value.components = JSON.stringify(cacheComponents.value);
  if (id.value != 0 && id.value) {
    designData.value.id = id.value;
  }
  const loading = ElLoading.service({
    lock: true,
    text: '保存中',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    designData.value.companyId = companyId;
  }
  saveScreen(designData.value).then((res) => {
    loading.close();
    if (res.code == 200) {
      id.value = res.data.id;
      ElMessage({
        type: 'success',
        message: '保存成功'
      });
      // 发送消息给原窗口
      channel.postMessage({ type: 'DATA_SAVED' });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 检查缓存的组件是否有设置需要表达式，但是没设置表达式的值
 * @param {array} cacheComponents 组件数组
 */
const isCheckSomeComponentNoExpress = (cacheComponents: Array<any>) => {
  let flag = false;
  if (!cacheComponents || cacheComponents.length === 0) {
    ElMessage.warning('最少设置一个组件');
    flag = true;

    return flag;
  }

  const someComponentNoExpress = cacheComponents.some((e: { cptOption: { cptDataForm: any } }) => {
    const cptDataForm = e?.cptOption?.cptDataForm;
    return cptDataForm?.dataSource === 2 && !cptDataForm?.apiUrl;
  });
  if (someComponentNoExpress) {
    ElMessage.warning('有组件未设置表达式，请检查');
    flag = true;
  }
  return flag;
};

/**
 * 预览按钮
 */
const preview = () => {
  if (isCheckSomeComponentNoExpress(cacheComponents.value)) {
    return;
  }
  // 预览先调用下保存
  designData.value.moduleId = props.moduleId;
  designData.value.components = JSON.stringify(cacheComponents.value);
  const loading = ElLoading.service({
    lock: true,
    text: '保存中',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  saveScreen(designData.value).then((res) => {
    loading.close();
    if (res.code == 200) {
      id.value = res.data.id;
      designData.value.components = cacheComponents.value;
      localStorage.setItem('designCache', JSON.stringify(designData.value));
      window.open(`/preview/${props.moduleId}/${id.value}`);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const delCpt = (cpt, index) => {
  ElMessageBox.confirm('删除' + cpt.cptTitle + '组件?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      //记录一个bug，v-for key值重复导致页面渲染数据错乱。在丢下组件时实用uuid作为key解决。
      currentCpt.value = {};
      cacheComponents.value.splice(index, 1);
      const childId = proxy.$refs[cpt.cptKey + index][0]?.uuid; // TODO: 需要优化
      clearCptInterval(childId);
    })
    .catch(() => {});
};

const showConfigBar = (
  e: { ctrlKey?: any },
  item: {
    cptTitle?: any;
    icon?: any;
    cptKey: any;
    cptOptionKey?: any;
    cptOption?: any;
    cptX?: number;
    cptY?: number;
    cptZ?: number;
    cptWidth?: any;
    cptHeight?: any;
    id: any;
  },
  index: number
) => {
  //刷新属性栏数据，页面上拖动的组件执行click事件来更新组件的属性栏
  currentCpt.value = item;
  if (!currentCpt.value.cptOption.interaction) {
    const interaction = {
      title: '', //交互标题
      dataSource: 1, //1 静态数据 2表达式
      dataText: '',
      expression: '', //表达式
      contentList: [] //显示内容
    };
    currentCpt.value.cptOption.interaction = interaction;
  }
  currentCptIndex.value = index;
  const divRef = proxy.$refs['div' + item.cptKey + index];
  if (divRef) {
    divRef[0]?.focus(); //聚焦 用于多选移动
  }
  if (!e.ctrlKey) {
    //未按住ctrl键
    cacheChoices.value = {};
  }
  configBarRef.value.showCptConfig(item);
  cacheChoices.value[item.id] = item;
  cacheChoicesFixed.value[item.id] = JSON.parse(JSON.stringify(item));
};

const dragStart = (copyDomData: any) => {
  copyDom.value = copyDomData;
  //从组件栏拿起组件
  copyDomData.draggable = false;
};

const allowDrop = (e: any) => {
  e.preventDefault();
};

const drop = (e: any) => {
  //从组件栏丢下组件
  const config = JSON.parse(copyDom.value['getAttribute']('config'));
  if (config.option.cptDataForm) {
    //2022-01-24：将静态数据、api、sql用三个字段存储，配置项未填写apiUrl字段和sql字段在此处赋默认值
    if (!config.option.cptDataForm.apiUrl) {
      config.option.cptDataForm.apiUrl = '';
    }
    if (!config.option.cptDataForm.sql) {
      config.option.cptDataForm.sql = '';
    }
  }
  const cpt = {
    cptTitle: config.name,
    icon: config.icon,
    cptKey: config.cptKey,
    cptOptionKey: config.cptOptionKey ? config.cptOptionKey : config.cptKey + '-option',
    cptOption: config.option,
    cptX: Math.round(e.offsetX),
    cptY: Math.round(e.offsetY),
    cptZ: 100,
    cptWidth: config.width ? config.width : 400,
    cptHeight: config.height ? config.height : 300,
    id: uuidv1()
  };
  cacheComponents.value.push(cpt);
  cacheChoices.value = {}; //多选清空
  showConfigBar({}, cpt, cacheComponents.value.length - 1); //丢下组件后刷新组件属性栏
  configBarRef.value.showCptConfig();
};

// 任务下拉列表 改变
const changeTaskId = (id) => {
  // 任务改变的时候主动触发所有组件刷新
  const loadList = [
    'cpt-text',
    'cpt-dataV-percentPond',
    'cpt-rect-num',
    'cpt-dataV-scrollList',
    'cpt-chart-map-gc',
    'cpt-chart-pie',
    'cpt-chart-line',
    'cpt-dataV-scrollTable'
  ]; //需要执行的组件key
  cacheComponents.value.forEach((v: { cptKey: string }, vdx: any) => {
    if (loadList.includes(v.cptKey)) {
      const ref = v.cptKey + vdx;
      const compRef = proxy.$refs[`${ref}`];
      if (compRef && compRef[0]) {
        compRef[0].loadData(id);
      }
    }
  });
};

// onMounted
onMounted(() => {
  document.getElementById('copyFooter').classList.add('remove-footer');
  loadCacheData();
  window.addEventListener('keydown', (event) => {
    if (currentCptIndex.value !== -1) {
      const key = event.key;
      switch (
        key //方向键移动当前组件
      ) {
        case 'ArrowDown':
          currentCpt.value.cptY += 1;
          break;
        case 'ArrowUp':
          currentCpt.value.cptY -= 1;
          break;
        case 'ArrowLeft':
          currentCpt.value.cptX -= 1;
          break;
        case 'ArrowRight':
          currentCpt.value.cptX += 1;
          break;
      }
    }
  });
  window.onresize = () => {
    return (() => {
      initContainerSize();
    })();
  };
  getModelList();
});
</script>

<style scoped>
.top {
  height: 45px;
  box-shadow: 0 2px 5px #2b3340 inset;
  color: #fff;
  overflow: hidden;
  margin: 0;
  font-size: 18px;
  line-height: 45px;
  background: #353f50;
}
.preview-btn {
  margin: 0 10px;
  background: #49586e;
  color: #fff;
}
.save-btn {
  margin: 0 10px;
  background: #d5d9e2;
  color: #333;
}
.webContainer {
  position: relative;
  margin: 0 10px;
  background-size: 100% 100%;
  transform-origin: 0 0;
}
.delTag {
  width: 45px;
  height: 22px;
  background: rgba(43, 51, 64, 0.8);
  border-radius: 2px;
  color: #ccc;
  z-index: 2000;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  display: none;
  cursor: pointer;
}
.activeMask {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1801;
}
.cptDiv {
  position: absolute;
  outline: none;
}
.cptDiv:hover .delTag {
  display: block;
}
.resizeTag {
  width: 8px;
  height: 8px;
  position: absolute;
  background-color: #b6bfce;
  z-index: 2000;
  border-radius: 50%;
}
.configBtn:hover {
  cursor: pointer;
  color: #b6bfce;
}
.el-dropdown-link {
  cursor: pointer;
  color: #ffffff !important;
}
.content {
}
/*滚动条样式*/
.content::-webkit-scrollbar {
  width: 4px;
}
.content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(176, 175, 175, 0.5);
}
.content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(248, 248, 248, 0.1);
}
</style>
