<!-- 超期过渡费汇总 -->
<template>
  <container-card>
    <div class="cqgdfData-main app-container">
      <!-- <div class="handle-title">超期过渡费汇总</div> -->
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="征收户" name="first">
          <cqIndex v-if="activeName === 'first'"></cqIndex>
        </el-tab-pane>
        <el-tab-pane label="安置房房源查询" name="second">
          <azfIndex v-if="activeName === 'second'"></azfIndex>
        </el-tab-pane>
      </el-tabs>
    </div>
  </container-card>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import cqIndex from './components/cqIndex.vue';
import azfIndex from './components/azfIndex.vue';

// 当前激活的选项卡
const activeName = ref('first');

// 选项卡点击事件
const handleClick = (tab: any, event: any) => {
};
</script>

<style lang="scss" scoped></style>
