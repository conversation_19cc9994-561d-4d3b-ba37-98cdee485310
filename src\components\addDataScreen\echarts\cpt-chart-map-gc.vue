<template>
  <div style="width: 100%; height: 100%" :id="uuid"></div>
</template>

<script lang="ts" setup>
import { getDataJson, pollingRefresh } from '@/utils/refreshCptData';
import { getDataForFormula } from '@/api/dataScreen';
import { v1 as uuidv1 } from 'uuid';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
defineOptions({
  name: 'cpt-chart-map-gc'
});
const route = useRoute();
const props = defineProps<{
  width: number;
  height: number;
  option: Record<string, any>;
}>();
// --- 定义变量 ---
const uuid = ref(uuidv1());
const chartOption = ref({});
let chart: any = null;
const cptData = ref([]);
const taskId = ref('');

// --- watch ---
watch(
  () => props.option.attribute,
  (newObj) => {
    loadChart(newObj);
  },
  { deep: true } //深度监听
);
watch(
  () => props.width,
  (newVal) => {
    chart?.resize();
  }
);
watch(
  () => props.height,
  (newVal) => {
    chart?.resize();
  }
);

// --- 定义方法 ---
const refreshCptData = () => {
  pollingRefresh(uuid.value, props.option.cptDataForm, loadData as any);
};
defineExpose({
  refreshCptData
});
const loadData = (taskId) => {
  if (props.option.cptDataForm.dataSource == 2 && props.option.cptDataForm.apiUrl) {
    const parmas: any = {
      expression: props.option.cptDataForm.apiUrl,
      moduleId: props.option.cptDataForm.moduleId,
      code: props.option.cptDataForm.code
    };
    if (taskId != '') {
      parmas.taskId = taskId;
    }
    // 设置公司私有模块的数据 需要传递公司id
    const companyId = route.query.companyId;
    if (companyId && companyId !== undefined && companyId !== null) {
      parmas.companyId = companyId;
    }
    getDataForFormula(parmas).then((res) => {
      if (res.code == 200) {
        if (res.data.maps) {
          cptData.value = res.data.maps;
        }
        loadChart(props.option.attribute);
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else if (props.option.cptDataForm.dataSource == 1) {
    getDataJson(props.option.cptDataForm).then((res) => {
      cptData.value = res;
      loadChart(props.option.attribute);
    });
  }
};

const loadChart = (attribute) => {
  chartOption.value = {
    title: {
      text: attribute.titleText,
      subtext: attribute.subtext,
      left: attribute.titleLeft,
      top: attribute.titleTop,
      textStyle: {
        color: attribute.titleColor,
        fontSize: attribute.titleFontSize
      },
      subtextStyle: {
        color: attribute.subTitleColor,
        fontSize: attribute.subTitleFontSize
      }
    },
    tooltip: {
      formatter: function (e) {
        //e, t, n
        let value = 0;
        if (e.value) {
          value = e.value;
        }
        return e.seriesName + '<br />' + e.name + '：' + value;
      }
    },
    visualMap: {
      min: attribute.piecesMin || 0,
      max: attribute.piecesMax || 10,
      text: [attribute.piecesMaxLabel || '', attribute.piecesMinLabel || ''],
      textStyle: {
        color: '#ddd'
      },
      realtime: false,
      calculable: true,
      inRange: {
        color: [attribute.piecesMinColor || 'lightskyblue', attribute.piecesCenterColor || 'yellow', attribute.piecesMaxColor || 'orangered']
      }
    },

    geo: {
      map: attribute.map,
      roam: attribute.roam, //允许缩放
      zoom: 1.23,
      label: {
        show: true,
        fontSize: attribute.geoLabelSize,
        color: attribute.geoLabelColor
      },
      itemStyle: {
        borderColor: '#777' //边界线颜色
      }
    },
    series: [
      {
        name: attribute.seriesName,
        type: 'map',
        geoIndex: 0,
        data: cptData.value
      }
    ]
  };

  chart?.setOption(chartOption.value);
};
// 初始化
onMounted(() => {
  chart = echarts.init(document.getElementById(uuid.value));

  refreshCptData();
});
</script>

<style scoped></style>
