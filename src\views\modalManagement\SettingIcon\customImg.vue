<template>
  <div class="main" @mouseenter="enterCateItem" @mouseleave="leaveCateItem">
    <el-image ref="imgRef" fit="cover" :src="img" :style="{ width: width, height: height, borderRadius: radios }" :preview-src-list="bigImg">
      <template v-slot:error>
        <div class="image-slot">
          <img src="@/assets/images/img-error.png" alt="" :style="{ width: width, height: height }" />
        </div>
      </template>
    </el-image>
    <div class="edit" @click="handelEdit">
      <span class="edit-text">修改</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { getToken } from '@/utils/auth';
import axios from 'axios';
import { ElImage } from 'element-plus';

// 定义 props
const props = defineProps<{
  authSrc?: string;
  width?: string;
  height?: string;
  radios?: string;
  isList?: boolean;
  isEdit?: boolean;
}>();

// 定义 emit
const emit = defineEmits(['editIcon']);

// 定义响应式数据
const bigImg = ref<string[]>([]);
const img = ref<string>('');
const isShow = ref<boolean>(false);
const imgRef = ref<InstanceType<typeof ElImage> | null>(null);

// 获取图片的函数
const getImg = async () => {
  if (!props.authSrc) return;

  try {
    const res = await axios({
      method: 'get',
      url: props.authSrc,
      headers: { 'Authorization': `Bearer ${getToken()}`, 'Access-Control-Allow-Origin': '*' },
      responseType: 'blob'
    });

    const blob = res.data;
    const reader = new FileReader();

    reader.readAsDataURL(blob);
    reader.onload = () => {
      img.value = reader.result as string;
    };
  } catch (error) {
    console.error('获取图片失败:', error);
  }
};

// 鼠标进入事件处理函数
const enterCateItem = () => {
  isShow.value = true;
};

// 鼠标离开事件处理函数
const leaveCateItem = () => {
  isShow.value = false;
};

// 处理编辑事件
const handelEdit = () => {
  emit('editIcon');
};

// 监听 authSrc 的变化
watch(
  () => props.authSrc,
  (newVal) => {
    if (newVal) {
      getImg();
    }
  }
);

// 组件挂载时获取图片
onMounted(() => {
  getImg();
});
</script>

<style scoped>
.main {
  display: flex;
  align-items: flex-end;
  position: relative;
}

.edit {
  width: 44px;
  height: 24px;
  background: #edf4fb;
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  line-height: 24px;
  cursor: pointer;
  /* margin-bottom:10px ; */
  margin-left: 4px;
  position: absolute;
  left: 75px;
  bottom: 5px;
}
.edit-text {
  height: 20px;
  font-size: 14px;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: var(--current-color);
  line-height: 20px;
}
.img-item {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 300px;
  height: 200px;
}
.samll-img {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 50px;
  height: 40px;
}

:deep(.image-slot) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: #ddd dashed 1px;
  background: rgba(0, 0, 0, 0.1);
}
</style>
