<!-- 图层设置内容 -->
<template>
  <div class="tuceng-main">
    <div class="tuceng-list" v-show="!isFieldForm">
      <div class="tuceng-title">图层功能设置</div>
      <div class="tuceng-checked-main">
        <el-checkbox-group v-model="allCurrentItem.attribution.checkList" style="display: flex; flex-direction: column">
          <el-checkbox label="addShp">新建SHP</el-checkbox>
          <el-checkbox label="exportShp">导入SHP</el-checkbox>
          <el-checkbox label="step"
            >自定义步骤<span style="padding-left: 12px"><i class="el-icon-question"></i></span
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <!-- 自定义步骤的数据 -->
      <div class="custom-step-main" v-if="allCurrentItem.attribution.checkList.includes('step')">
        <div class="step-title" @dblclick="handleShow">
          <el-input v-model="allCurrentItem.name" v-if="isShowInput" placeholder="请输入自定义步骤名称"></el-input>
          <div class="title" v-else>{{ allCurrentItem.name }}</div>
        </div>
        <el-button type="primary" plain icon="el-icon-plus" size="small" style="margin: 16px 16px 0" @click="handleAddStep">添加步骤</el-button>
        <div v-for="(item, index) in allCurrentItem.stepDetailModels" :key="item.name">
          <div class="step-item" v-if="item.delFlag != 1">
            <div style="margin: 0 16px">
              <span style="color: var(--current-color)">步骤{{ index + 1 }}:</span>
              <span style="padding-left: 16px; font-size: 14px; font-weight: 600">{{ item.name }}</span>
            </div>
            <div class="step-btn-main">
              <el-tooltip class="item" effect="dark" content="样式" placement="top">
                <div class="text-btn" @click="handleOpenInfoField(item, index, 'class')">
                  <svg-icon class-name="svg-item" icon-class="more_class" />
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="字段" placement="top">
                <div class="text-btn" @click="handleOpenInfoField(item, index, 'field')">
                  <svg-icon class-name="svg-item" icon-class="more_field" />
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="修改" placement="top">
                <div class="text-btn" @click="handleOwnerSetting('修改步骤', item, index)">
                  <svg-icon class-name="svg-item" icon-class="more_edit" />
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除" placement="top">
                <div class="text-btn" @click="handleOwnerDelete(item)">
                  <svg-icon class-name="svg-item" icon-class="more_delete" />
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div class="btn-next">
        <el-button type="primary" @click="handleNext" size="small">下一步<i class="el-icon-arrow-right el-icon--right"></i></el-button>
      </div>
    </div>
    <!-- 添加步骤弹框 -->
    <el-dialog :title="stepDialogTitle" v-model:visible="stepVisible" @closed="handleStepClose">
      <el-form :model="stepForm" :rules="stepFormRules" ref="stepFormRef">
        <el-form-item label="步骤名称" prop="name">
          <el-input v-model="stepForm.name" autocomplete="off" placeholder="请输入步骤名称"></el-input>
        </el-form-item>
        <el-form-item label="步骤分析" prop="stepKey">
          <el-select v-model="stepForm.stepKey" placeholder="请选择活动区域" style="width: 100%">
            <el-option label="相交分析" value="xjfx"></el-option>
            <el-option label="单地类面积分析" value="mjfx"></el-option>
            <el-option label="追溯分析" value="zsfx"></el-option>
            <el-option label="多年份地类面积分析" value="dnfmjfx"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="步骤说明">
          <el-input v-model="stepForm.remark" autocomplete="off" placeholder="请输入步骤说明"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="handleStepClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmitStep">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增表单字段 -->
    <dynamic-from
      :class="{ 'dynamic-form-class': true }"
      v-show="isFieldForm"
      @goBack="handleGoBack"
      @submitField="handleSubmitField"
      :confProp="formData"
      :isFieldFormProp="isFieldForm"
      :isTypeProp="isType"
    ></dynamic-from>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, defineEmits } from 'vue';
import DynamicFrom from '@/components/DynamicForm/index.vue';
import { saveStep, selectStep } from '@/api/modal';
import type { ElForm } from 'element-ui/types/form';
import { useModalStore } from '@/store/modules/modal';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const modalStore = useModalStore();

// 类型定义
interface AllCurrentItem {
  attribution: {
    checkList: string[];
  };
  stepDetailModels: StepDetailModel[];
  name: string;
  operaType: number;
  moduleId?: number;
}

interface StepForm {
  name: string;
  stepKey: string;
  remark: string;
}

interface StepDetailModel {
  name: string;
  remark: string;
  attribution: {
    classList: any[];
    stepKey: string;
  };
  fieldGroupModel: FieldGroupModel;
  delFlag?: number;
}

interface FieldGroupModel {
  fieldModelList: any[];
  typeName: string;
  remark: string;
  groupScope: number;
  moduleId?: number;
  attribution: {
    formData: any;
  };
}

// 响应式数据
const isFieldForm = ref(false);
const isShowInput = ref(true);
const stepForm = ref<StepForm>({ name: '', stepKey: '', remark: '' });
const stepVisible = ref(false);
const stepDialogTitle = ref('添加步骤');
const isType = ref('');
const stepIndex = ref<number>();
const allCurrentItem = ref<AllCurrentItem>({
  attribution: { checkList: ['addShp', 'exportShp'] },
  stepDetailModels: [],
  name: '',
  operaType: 1,
  moduleId: undefined
});
const formData = ref<any>({});

// 计算属性
const moduleId = computed(() => modalStore.moduleId);

// 事件发射
const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();

// 表单引用
const stepFormRef = ref<InstanceType<typeof ElForm>>();

// 步骤验证规则
const stepFormRules = ref({
  name: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' },
    { min: 2, max: 10, message: '长度在 2到 10 个字符', trigger: 'blur' }
  ],
  stepKey: [{ required: true, message: '请选择步骤分析', trigger: ['change', 'blur'] }]
});

// 生命周期
onMounted(() => {
  handleSelectStep();
});

// 根据modalId查询数据
const handleSelectStep = async () => {
  const params = {
    moduleId: moduleId.value,
    exclude: ''
  };
  //设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    params.companyId = companyId;
  }
  try {
    const res = await selectStep(params);
    if (res.code === 200) {
      allCurrentItem.value = res.data?.[0] || {
        attribution: { checkList: ['addShp', 'exportShp'] },
        stepDetailModels: [],
        name: '',
        operaType: 1,
        moduleId: undefined
      };
    }
  } catch (error) {
    console.error('查询步骤数据失败:', error);
  }
};

// 下一步按钮
const handleNext = async () => {
  allCurrentItem.value.moduleId = moduleId.value;
  //设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    allCurrentItem.value.companyId = companyId;
  }
  try {
    const res = await saveStep([allCurrentItem.value]);
    if (res.code === 200) {
      ElMessage.error('图层设置成功!');
      emit('nextStep', 5);
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error('保存步骤数据失败:', error);
  }
};

// 显示/隐藏名称输入框
const handleShow = () => {
  isShowInput.value = !isShowInput.value;
};

// 添加步骤
const handleAddStep = () => {
  stepVisible.value = true;
  stepDialogTitle.value = '添加步骤';
  stepForm.value = { name: '', stepKey: '', remark: '' };
};

// 新增字段返回
const handleGoBack = () => {
  formData.value = {};
  isFieldForm.value = false;
};

// 提交字段
const handleSubmitField = (list: any[], formOptions: string) => {
  const data = JSON.parse(formOptions);
  const currentStep = allCurrentItem.value.stepDetailModels[stepIndex.value!];
  if (isType.value === 'class') {
    currentStep.attribution.classList = JSON.parse(JSON.stringify(list));
    currentStep.attribution.formData = JSON.parse(JSON.stringify(data.formData));
  } else {
    currentStep.fieldGroupModel.fieldModelList = JSON.parse(JSON.stringify(list));
    currentStep.fieldGroupModel.attribution.formData = JSON.parse(JSON.stringify(data.formData));
  }
  isFieldForm.value = false;
};

// 打开字段/样式设置
const handleOpenInfoField = (item: StepDetailModel, index: number, str: string) => {
  isType.value = str;
  stepIndex.value = index;
  formData.value = str === 'class' ? item.attribution?.formData || {} : item.fieldGroupModel.attribution?.formData || {};
  isFieldForm.value = true;
};

// 修改步骤
const handleOwnerSetting = (str: string, item: StepDetailModel, index: number) => {
  stepDialogTitle.value = str;
  stepForm.value = {
    name: item.name,
    stepKey: item.attribution.stepKey,
    remark: item.remark
  };
  stepIndex.value = index;
  stepVisible.value = true;
};

// 删除步骤
const handleOwnerDelete = (item: StepDetailModel) => {
  item.delFlag = 1;
};

// 关闭步骤弹框
const handleStepClose = () => {
  stepForm.value = { name: '', stepKey: '', remark: '' };
  stepFormRef.value?.resetFields();
  stepVisible.value = false;
};

// 提交步骤
const handleSubmitStep = () => {
  stepFormRef.value?.validate((valid) => {
    if (!valid) return;

    if (stepDialogTitle.value === '修改步骤') {
      const currentStep = allCurrentItem.value.stepDetailModels[stepIndex.value!];
      currentStep.name = stepForm.value.name;
      currentStep.remark = stepForm.value.remark;
      currentStep.attribution.stepKey = stepForm.value.stepKey;
      currentStep.fieldGroupModel.typeName = stepForm.value.name;
      currentStep.fieldGroupModel.remark = stepForm.value.remark;
    } else {
      allCurrentItem.value.stepDetailModels.push({
        name: stepForm.value.name,
        remark: stepForm.value.remark,
        attribution: {
          classList: [],
          stepKey: stepForm.value.stepKey
        },
        fieldGroupModel: {
          fieldModelList: [],
          typeName: stepForm.value.name,
          remark: stepForm.value.remark,
          groupScope: 4,
          moduleId: moduleId.value,
          attribution: { formData: {} }
        }
      });
    }
    stepVisible.value = false;
  });
};
</script>

<style lang="scss" scoped>
:deep(.el-checkbox__input).is-checked + .el-checkbox__label {
  color: #282a2d;
}
.tuceng-main {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-content: center;
  .tuceng-list {
    width: 592px;
    height: calc(100% - 30px);
    background: #fff;
    box-shadow: 0px 4px 10px 0px rgba(130, 145, 169, 0.16);
    border-radius: 12px 12px 12px 12px;
    opacity: 1;
    margin-top: 12px;
    position: relative;
    .tuceng-title {
      width: 84px;
      height: 20px;
      font-size: 14px;
      font-family:
        PingFang SC,
        PingFang SC;
      color: #161d26;
      line-height: 20px;
      margin: 12px 16px;
      font-weight: 600;
    }
    .tuceng-checked-main {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin: 0 16px;
      .el-checkbox {
        height: 36px;
        border-radius: 6px;
      }
    }
    .custom-step-main {
      width: calc(100% - 50px);
      height: calc(100% - 224px);
      opacity: 1;
      border: 1px solid #e6e9ee;
      margin: 0 24px;
      .step-title {
        width: 100%;
        height: 36px;
        background: #ffffff;
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        border: 1px solid #e6e9ee;
        line-height: 36px;
        text-align: center;

        .title {
          height: 36px;
          font-size: 14px;
          font-family:
            PingFang SC,
            PingFang SC;
          color: #1f2935;
          line-height: 36px;
          text-align: center;
          vertical-align: middle;
          font-weight: 600;
        }
        .el-input {
          height: 36px;
          line-height: 36px;
          top: 0;
          :deep(&) {
            .el-input__inner {
              border-radius: 0;
              border: none;
              border-bottom: 1px solid #e6e9ee;
              text-align: center;
              color: #1f2935;
              line-height: 36px;
              text-align: center;
              vertical-align: middle;
              font-weight: 600;
            }
          }
        }
      }
      .step-item {
        width: calc(100% - 32px);
        height: 40px;
        background: #f6f7f8;
        border-radius: 6px 6px 6px 6px;
        display: flex;
        justify-content: space-between;
        align-content: center;
        align-items: center;
        margin: 16px;
        .step-btn-main {
          display: flex;
          .text-btn {
            margin: 0 8px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            .svg-item {
              width: 16px;
              height: 16px;
              margin: auto;
              color: #8291a9;
              &:hover {
                color: var(--current-color);
              }
            }
            &:hover {
              width: 24px;
              height: 24px;
              background-color: rgba(0, 129, 255, 0.1);
              color: var(--current-color);
            }
            &:hover .svg-item {
              width: 16px;
              height: 16px;
              background-color: rgba(0, 129, 255, 0.1);
              color: var(--current-color);
            }
          }
        }
      }
    }
    .btn-next {
      position: absolute;
      bottom: 32px;
      right: 32px;
    }
  }
}

.dynamic-form-class {
  width: 100%;
  background-color: #fff;
}
</style>
