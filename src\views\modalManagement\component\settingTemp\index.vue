<!-- 导出报告 -->
<template>
  <div class="settingTemp-main" v-loading.fullscreen.lock="fullscreenLoading">
    <!-- 列表 -->
    <template v-if="!showEdit">
      <div class="hanle-div">
        <div>
          <el-dropdown @command="changeTempType" style="margin-right: 8px">
            <el-button type="primary" plain size="small"> <i class="el-icon-plus"></i> 新建报告 </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1" style="display: flex; justify-content: center; align-content: center; align-items: center">
                  <el-image :src="wordIcon" style="width: 14px; height: 14px; margin-right: 4px"></el-image>文字文档</el-dropdown-item
                >
                <el-dropdown-item command="2" style="display: flex; justify-content: center; align-content: center; align-items: center">
                  <el-image :src="excelIcon" style="width: 14px; height: 14px; margin-right: 4px"></el-image>表格文档</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <el-button type="primary" size="small" @click="showAddTemp" plain> <i class="el-icon-plus"></i> 新建报告</el-button> -->
          <!-- <el-button type="primary" size="small" @click="showCopy"> <i class="el-icon-plus"></i> 字段复制</el-button> -->
          <!-- v-hasPermi="['template:file:upload']" -->
          <el-button type="primary" size="small" @click="handleOpenTemplateDialog"> <i class="el-icon-upload"></i> 上传</el-button>
        </div>
        <div class="handle">
          <el-link type="primary" @click="showFast">快捷表达式</el-link>
          <el-link type="primary" @click="handleCopy" style="margin-left: 10px">自定义表达式</el-link>
        </div>
      </div>
      <el-table :data="tableData" class="table" border>
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="报告名称" prop="templateName"></el-table-column>
        <el-table-column label="上传时间">
          <template #default="scope">
            {{ formatDateAndTimeType(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="scope">
            <el-button type="primary" @click="edit(scope.row)" plain size="small"><i class="el-icon-edit"></i>编辑</el-button>
            <el-button type="warning" @click="down(scope.row)" style="margin-left: 10px" plain size="small"
              ><i class="el-icon-download"></i>下载</el-button
            >
            <el-button type="danger" @click="del(scope.row)" style="margin-left: 10px" plain size="small"
              ><i class="el-icon-delete"></i>删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next"
          :total="total">
        </el-pagination>
      </div> -->
      <div class="btn-next-step">
        <el-button type="primary" @click="handleNext" size="small" style="margin-right: 32px">
          下一步<el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </template>
    <!-- 新增、编辑 -->
    <template v-else>
      <editTemp :url="temUrl" @toList="toList" :showEdit="showEdit" :templateName="templateName" :tempId="tempId" :moduleId="moduleId"></editTemp>
    </template>
    <!-- 导入报告的弹框 -->
    <el-dialog v-model="templateDialogVisiable" @close="handleClosedTemplate" width="800px" class="colse-dialog" :close-on-click-modal="false">
      <template v-slot:title>
        <div>
          <span>导入报告</span>
        </div>
      </template>
      <el-form :model="templateForm" :rules="templateFormRule" ref="templateRef">
        <el-form-item label="文件" prop="file">
          <!-- :on-change="handleChangeContractFile" -->
          <el-upload
            style="width: 100%"
            class="upload-demo"
            ref="upload"
            :action="`${baseUrl}/output/template/upload`"
            :headers="headers"
            :before-upload="beforeAvatarUpload"
            drag
            multiple
            :limit="9"
            :auto-upload="false"
            :data="tempParams"
            v-model:file-list="templateForm.fileList"
            accept=".doc,.docx,.xls,.xlsx"
            :on-exceed="handleExceed"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> <span style="color: red">单次只允许最多上传9个文件！！！</span></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="templateDialogVisiable = false">取 消</el-button>
          <el-button type="primary" @click="submitTemplate" :disabled="templateForm.fileList.length === 0">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新建报告弹窗 -->
    <el-dialog title="新建报告" v-model="addTempDialog" width="30%" :before-close="handleCloseAddTemp">
      <div class="office-box" v-if="!addTempType">
        <img src="@/assets/images/word.png" alt="" class="item" @click="changeTempType(1)" />
        <img src="@/assets/images/excel.png" alt="" class="item" @click="changeTempType(2)" style="margin-left: 30px" />
      </div>
      <div v-else>
        <div style="padding-bottom: 10px">文件名字：</div>
        <el-input v-model="addTempName" placeholder="请输入文件名字" maxlength="10"></el-input>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseAddTemp">取 消</el-button>
          <el-button type="primary" @click="submitAddTemp">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 打开公式编辑的弹框 -->
    <formula-editing-dialog
      :modelValue="formulaVisible"
      :expression="expression"
      :isCopy="true"
      @closeFormulaEdit="handleCloseFormulation"
      @submitFormulation="handleSubmitFormulation"
      :appType="appType"
    ></formula-editing-dialog>

    <!-- 快捷表达式 -->
    <fastExpression
      :mapFieldDialog="mapFieldDialog"
      @submitFastExp="submitFastExp"
      @handleCloseFast="handleCloseFast"
      :moduleId="moduleId"
    ></fastExpression>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getTempList, uploadTemplates, downLoadTemplate, delTemplate, selectRules } from '@/api/modal';
import { getToken } from '@/utils/auth';
import editTemp from './editTemp.vue';
import Axios from 'axios';
import wordIcon from '@/assets/images/wordIcon.png';
import excelIcon from '@/assets/images/excelIcon.png';
import formulaEditingDialog from '@/components/formulaEditingDialog/index.vue';
import { useUserStore } from '@/store/modules/user';
import { useModalStore } from '@/store/modules/modal';
import { useRouter, useRoute } from 'vue-router';
import { formatDateYmdhm, formatDateAndTimeType } from '@/utils/filters';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowRight } from '@element-plus/icons-vue';

const modalStore = useModalStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

// 定义响应式数据
const appType = ref('#word');
const wordIconRef = ref(wordIcon);
const excelIconRef = ref(excelIcon);
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const templateDialogVisiable = ref(false);
const templateForm = ref({
  fileList: [],
  fileRaw: {},
  file: ''
});
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const headers = ref({
  'Authorization': 'Bearer ' + getToken(),
  'Access-Control-Allow-Origin': '*'
});
const templateFormRule = ref({
  file: [{ required: true, message: '请上传文件', trigger: 'blur' }]
});
const fullscreenLoading = ref(false);
const showEdit = ref(false);
const temUrl = ref('');
const templateName = ref('');
const addTempDialog = ref(false);
const tempId = ref(0);
const addTempType = ref<number>(1);
const addTempName = ref('');
const mapFieldDialog = ref(false);
const formulaVisible = ref(false);
const expression = ref('');
const tempParams = ref({});
// 计算属性
const moduleId = computed(() => {
  let moduleId = modalStore.moduleId;
  if (router.currentRoute.value.query.id) {
    moduleId = parseInt(router.currentRoute.value.query.id as string);
  }
  return moduleId;
});

const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();

// 生命周期钩子
onMounted(() => {
  getData();
});

// 方法定义
const getData = () => {
  const parmas = {
    moduleId: moduleId.value,
    name: ''
  };
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    parmas.companyId = companyId;
  }
  getTempList(parmas).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const handleNext = () => {
  // 这里假设 $emit 可以通过某种方式实现，具体需要根据项目上下文调整
  // 这里简单注释掉，需要根据实际情况修改
  emit('nextStep', 6);
};

const handleSizeChange = (val: number) => {};

const handleCurrentChange = (val: number) => {};

const handleOpenTemplateDialog = () => {
  templateDialogVisiable.value = true;
};

const beforeAvatarUpload = (file: any) => {
  //  文件类型
  const isFileType = file.raw.type;
  //  文件大小
  const isSize = file.size / 1024 / 1024 < 10;
  //可以上传word 和excel
  const whiteList = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  if (whiteList.indexOf(isFileType) === -1) {
    ElMessage.error('只能上传WORD/Excel类型的文件');
    templateForm.value.fileList.splice(0, 1);
    return false;
  }

  if (!isSize) {
    ElMessage.error('上传的文件不能超过10MB');
    templateForm.value.fileList.splice(0, 1);
    return false;
  }
  return true;
};

const handleClosedTemplate = () => {
  templateForm.value = {
    fileList: [],
    fileRaw: {}
  };
  // 这里假设可以通过某种方式访问 templateRef
  // 具体需要根据项目上下文调整
  // (templateRef.value as any)?.resetFields();
  templateDialogVisiable.value = false;
};

const submitTemplate = () => {
  // 这里假设可以通过某种方式访问 templateRef
  // 具体需要根据项目上下文调整
  // (templateRef.value as any)?.validate((valid: boolean) => {
  // if (valid) {
  const formData = new FormData();
  templateForm.value.fileList.forEach((item: any) => {
    formData.append('files', item.raw);
  });
  fullscreenLoading.value = true;
  // 设置公司私有模块的数据 需要传递公司id
  const companyId = route.query.companyId;
  if (companyId && companyId !== undefined && companyId !== null) {
    tempParams.value.companyId = companyId;
  }
  uploadTemplates(formData, moduleId.value, tempParams.value.companyId).then((res) => {
    fullscreenLoading.value = false;
    if (res.code === 200) {
      ElMessage({
        type: 'success',
        message: '导入成功'
      });
      getData();
      templateDialogVisiable.value = false;
    } else {
      ElMessage({
        type: 'error',
        message: res.msg
      });
      getData();
      templateDialogVisiable.value = false;
    }
  });
  // } else {
  //   return false;
  // }
  // });
};

const down = (item: any) => {
  downLoadTemplate(item.downloadUrl).then((res) => {
    const blob = res.data;
    const fileName = item.templateName;
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveOrOpenBlob(blob, fileName);
    } else {
      //非ie浏览器
      const downloadElement = document.createElement('a');
      const href = window.URL.createObjectURL(blob); //常见下载的链接
      downloadElement.href = href;
      downloadElement.download = fileName; //下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); //点击下载
      document.body.removeChild(downloadElement); //下载完成移除元素
      window.URL.revokeObjectURL(href); //释放blob对象
    }
  });
};

const del = (item: any) => {
  ElMessageBox.confirm('是否确认删除【' + item.templateName + '】？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const tids = [item.id];
      delTemplate(tids).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '删除成功'
          });
          getData();
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      });
    })
    .catch(() => {});
};

const edit = (row: any) => {
  const query = {
    url: `${baseUrl.value}/qjt/file/downloadWord/${row.downloadUrl}`,
    templateName: row.templateName,
    tempId: row.id,
    baseUrl: baseUrl.value,
    token: getToken(),
    moduleId: moduleId.value
  };

  const routeData = router.resolve({ path: '/editTemp', query: query });
  window.open(routeData.href, '_blank');
};

const toList = () => {
  showEdit.value = false;
  getData();
};

const showAddTemp = () => {
  addTempDialog.value = true;
};

const handleCloseAddTemp = () => {
  addTempName.value = '';
  addTempType.value = 1;
  addTempDialog.value = false;
};

const changeTempType = (type: number) => {
  addTempType.value = Number(type);
  addTempDialog.value = true;
};

const submitAddTemp = () => {
  if (!addTempName.value) {
    ElMessage.error('请输入文件名字');
    return;
  }
  let fileName = addTempName.value;
  if (addTempType.value === 1) {
    //word
    fileName = fileName + '.docx';
  } else if (addTempType.value === 2) {
    //excel
    fileName = fileName + '.xlsx';
  }
  Axios({
    method: 'post',
    url: `${baseUrl.value}/qjt/file/multi/uploadFromUrl?fileName=${fileName}`,
    headers: { 'Authorization': 'Bearer ' + getToken() }
    // withCredentials:true,//表明了是否是跨域请求、默认是default
  }).then((res) => {
    const parmas = {
      downloadUrl: res.data.data[0].path,
      moduleId: moduleId.value,
      templateName: res.data.data[0].name
    };
    Axios({
      method: 'post',
      url: `${baseUrl.value}/qjt/output/template/add`,
      headers: { 'Authorization': 'Bearer ' + getToken() },
      data: parmas
      // withCredentials:true,//表明了是否是跨域请求、默认是default
    }).then((resp) => {
      if (resp.data.code === 200) {
        addTempName.value = '';
        getData();
        addTempDialog.value = false;
        const query = {
          url: `${baseUrl.value}/qjt/file/otherDownload/${resp.data.data.downloadUrl}?token=${getToken()}`,
          templateName: resp.data.data.templateName,
          tempId: resp.data.data.id,
          baseUrl: baseUrl.value,
          token: getToken(),
          moduleId: moduleId.value
        };
        const routeData = router.resolve({ path: '/editTemp', query: query });
        window.open(routeData.href, '_blank');
      } else {
        ElMessage.error(resp.data.msg);
      }
    });
  });
};

const showFast = () => {
  mapFieldDialog.value = true;
};

const handleCloseFormulation = () => {
  formulaVisible.value = false;
  modalStore.setIsAllGroup(false);
};

const handleSubmitFormulation = (expressionVal: string) => {
  expression.value = expressionVal;
  copyFun(expressionVal);
};

const copyFun = (expressionVal: string) => {
  const variable = expressionVal;
  const tag = document.createElement('textarea'); // create textarea标签，注意：创建input标签则不会换行
  document.body.appendChild(tag); // 添加到body中
  tag.value = variable; // 给textarea设置value属性为需要copy的内容
  tag.select(); // 选中
  document.execCommand('copy', false); // copy已经选中的内容
  ElMessage({
    type: 'success',
    message: '复制成功'
  });
  tag.remove();
};

const handleCopy = () => {
  // 在每次打开自定义表达式的时候，需要把字段数据清除否则会有缓存
  modalStore.setIsHasAcquition(false);
  // 是否需要全部属性组的数据，如果需要添加这个字段
  modalStore.setIsAllGroup(true);
  expression.value = '';
  formulaVisible.value = true;
};

const handleCloseFast = () => {
  mapFieldDialog.value = false;
};

const submitFastExp = (expressionVal: string) => {
  copyFun(expressionVal);
  mapFieldDialog.value = false;
};

/**
 * 超出限制的回调函数
 * @param files 超出限制的文件列表
 * @param uploadFiles 已上传的文件列表
 */
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(`当前限制选择 9 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + uploadFiles.length} 个文件`);
};

defineExpose({
  getData
});
</script>
<style lang="scss" scoped>
.settingTemp-main {
  width: 100%;
  // height: 100%;
  background: #fff;
  padding: 16px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  // overflow: auto;
  .btn-next-step {
    position: fixed;
    bottom: 40px;
    right: 22px;
    z-index: 999;
  }
  .hanle-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .table {
    width: 100%;
    height: calc(100% - 120px);
    overflow: auto;
    margin-top: 12px;
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(176, 175, 175, 0.5);
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
  }
  .page {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }
}
.dialog-box {
  height: 300px;
  border: 1px solid rgba(219, 231, 238, 1);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  .left {
    flex: 2;
  }
  .center {
    flex: 2;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .right {
    flex: 3;
    border-left: 1px solid rgba(219, 231, 238, 1);
  }
  .title-div {
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    .normal-sapn {
      margin-left: 20px;
    }
  }
  .content {
    height: calc(100% - 37px);
    padding: 0px 8px;
    width: calc(100% - 16px);
    margin-left: 8px;
    overflow: auto;
    :deep(&) {
      .el-tree-node__content {
        height: 32px;
        font-size: 12px;
      }
    }
    .empty-span {
      color: #909399;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .flex-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      color: rgba(22, 29, 38, 1);
      cursor: pointer;
      .label {
        font-size: 12px;
        padding-left: 12px;
      }
      .ico {
        padding-right: 8px;
      }
    }
    .flex-row:hover {
      background-color: #f5f7fa;
    }
    .flex-active {
      background: #edf4fb;
    }
    .no-span {
      color: #d3d3d3 !important;
      cursor: not-allowed;
    }
  }
  /*滚动条样式*/
  .content::-webkit-scrollbar {
    width: 4px;
  }
  .content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgb(255, 255, 255, 0.5);
  }
  .content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
.office-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .item {
    width: 110px;
    height: 110px;
    cursor: pointer;
  }
}

.handle {
  display: flex;
  flex-direction: row;
  .item {
    padding: 10px;
    cursor: pointer;
  }
}
</style>
